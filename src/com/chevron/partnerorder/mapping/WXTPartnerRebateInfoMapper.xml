<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.WXTPartnerRebateInfoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.WXTPartnerRebateInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="year_str" property="yearStr" jdbcType="VARCHAR" />
    <result column="month_str" property="monthStr" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="actual_rebate_amount" property="actualRebateAmount" jdbcType="NUMERIC" />
    <result column="actual_deserved_rebate_discount_ratio" property="actualDeservedRebateDiscountRatio" jdbcType="NUMERIC" />
    <result column="actual_deserved_rebate_amount" property="actualDeservedRebateAmount" jdbcType="NUMERIC" />
    <result column="current_month_rebate_amount" property="currentMonthRebateAmount" jdbcType="NUMERIC" />
    <result column="total_rebate_amount" property="totalRebateAmount" jdbcType="NUMERIC" />
    <result column="workshop_is_reach" property="workshopIsReach" jdbcType="VARCHAR" />
    <result column="workshop_reach_ratio" property="workshopReachRatio" jdbcType="NUMERIC" />
    <result column="active_workshop_number" property="activeWorkshopNumber" jdbcType="INTEGER" />
    <result column="workshop_not_reach_discount_amount" property="workshopNotReachDiscountAmount" jdbcType="NUMERIC" />
    <result column="workshop_not_reach_discount_ratio" property="workshopNotReachDiscountRatio" jdbcType="NUMERIC" />
    <result column="sales_is_reach" property="salesIsReach" jdbcType="VARCHAR" />
    <result column="scancode_reach_ratio" property="scancodeReachRatio" jdbcType="NUMERIC" />
    <result column="purchase_liters" property="purchaseLiters" jdbcType="BIGINT" />
    <result column="sales_liters" property="salesLiters" jdbcType="BIGINT" />
    <result column="scan_code_ratio" property="scanCodeRatio" jdbcType="NUMERIC" />
    <result column="scancode_not_reach_discount_amount" property="scancodeNotReachDiscountAmount" jdbcType="NUMERIC" />
    <result column="scancode_not_reach_discount_ratio" property="scancodeNotReachDiscountRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_is_reach" property="fleeingGoodsIsReach" jdbcType="VARCHAR" />
    <result column="fleeing_goods_ratio" property="fleeingGoodsRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_discount_amount" property="fleeingGoodsDiscountAmount" jdbcType="NUMERIC" />
    <result column="fleeing_goods_discount_ratio" property="fleeingGoodsDiscountRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_remark" property="fleeingGoodsRemark" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, year_str, month_str, create_time, update_time, actual_rebate_amount, 
    actual_deserved_rebate_discount_ratio, actual_deserved_rebate_amount, current_month_rebate_amount, 
    total_rebate_amount, workshop_is_reach, workshop_reach_ratio, active_workshop_number, 
    workshop_not_reach_discount_amount, workshop_not_reach_discount_ratio, sales_is_reach, 
    scancode_reach_ratio, purchase_liters, sales_liters, scan_code_ratio, scancode_not_reach_discount_amount, 
    scancode_not_reach_discount_ratio, fleeing_goods_is_reach, fleeing_goods_ratio, fleeing_goods_discount_amount, 
    fleeing_goods_discount_ratio, fleeing_goods_remark, remark,status
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_rebate_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_partner_rebate_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_partner_rebate_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoExample" >
    delete from wx_t_partner_rebate_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfo" >
    insert into wx_t_partner_rebate_info (id, partner_id, year_str, 
      month_str, create_time, update_time, 
      actual_rebate_amount, actual_deserved_rebate_discount_ratio, 
      actual_deserved_rebate_amount, current_month_rebate_amount, 
      total_rebate_amount, workshop_is_reach, workshop_reach_ratio, 
      active_workshop_number, workshop_not_reach_discount_amount, 
      workshop_not_reach_discount_ratio, sales_is_reach, 
      scancode_reach_ratio, purchase_liters, sales_liters, 
      scan_code_ratio, scancode_not_reach_discount_amount, 
      scancode_not_reach_discount_ratio, fleeing_goods_is_reach, 
      fleeing_goods_ratio, fleeing_goods_discount_amount, 
      fleeing_goods_discount_ratio, fleeing_goods_remark, 
      remark,status)
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{yearStr,jdbcType=VARCHAR}, 
      #{monthStr,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{actualRebateAmount,jdbcType=NUMERIC}, #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC}, 
      #{actualDeservedRebateAmount,jdbcType=NUMERIC}, #{currentMonthRebateAmount,jdbcType=NUMERIC}, 
      #{totalRebateAmount,jdbcType=NUMERIC}, #{workshopIsReach,jdbcType=VARCHAR}, #{workshopReachRatio,jdbcType=NUMERIC}, 
      #{activeWorkshopNumber,jdbcType=INTEGER}, #{workshopNotReachDiscountAmount,jdbcType=NUMERIC}, 
      #{workshopNotReachDiscountRatio,jdbcType=NUMERIC}, #{salesIsReach,jdbcType=VARCHAR}, 
      #{scancodeReachRatio,jdbcType=NUMERIC}, #{purchaseLiters,jdbcType=BIGINT}, #{salesLiters,jdbcType=BIGINT}, 
      #{scanCodeRatio,jdbcType=NUMERIC}, #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC}, 
      #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC}, #{fleeingGoodsIsReach,jdbcType=VARCHAR}, 
      #{fleeingGoodsRatio,jdbcType=NUMERIC}, #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC}, 
      #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC}, #{fleeingGoodsRemark,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfo" >
    insert into wx_t_partner_rebate_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="yearStr != null" >
        year_str,
      </if>
      <if test="monthStr != null" >
        month_str,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="actualRebateAmount != null" >
        actual_rebate_amount,
      </if>
      <if test="actualDeservedRebateDiscountRatio != null" >
        actual_deserved_rebate_discount_ratio,
      </if>
      <if test="actualDeservedRebateAmount != null" >
        actual_deserved_rebate_amount,
      </if>
      <if test="currentMonthRebateAmount != null" >
        current_month_rebate_amount,
      </if>
      <if test="totalRebateAmount != null" >
        total_rebate_amount,
      </if>
      <if test="workshopIsReach != null" >
        workshop_is_reach,
      </if>
      <if test="workshopReachRatio != null" >
        workshop_reach_ratio,
      </if>
      <if test="activeWorkshopNumber != null" >
        active_workshop_number,
      </if>
      <if test="workshopNotReachDiscountAmount != null" >
        workshop_not_reach_discount_amount,
      </if>
      <if test="workshopNotReachDiscountRatio != null" >
        workshop_not_reach_discount_ratio,
      </if>
      <if test="salesIsReach != null" >
        sales_is_reach,
      </if>
      <if test="scancodeReachRatio != null" >
        scancode_reach_ratio,
      </if>
      <if test="purchaseLiters != null" >
        purchase_liters,
      </if>
      <if test="salesLiters != null" >
        sales_liters,
      </if>
      <if test="scanCodeRatio != null" >
        scan_code_ratio,
      </if>
      <if test="scancodeNotReachDiscountAmount != null" >
        scancode_not_reach_discount_amount,
      </if>
      <if test="scancodeNotReachDiscountRatio != null" >
        scancode_not_reach_discount_ratio,
      </if>
      <if test="fleeingGoodsIsReach != null" >
        fleeing_goods_is_reach,
      </if>
      <if test="fleeingGoodsRatio != null" >
        fleeing_goods_ratio,
      </if>
      <if test="fleeingGoodsDiscountAmount != null" >
        fleeing_goods_discount_amount,
      </if>
      <if test="fleeingGoodsDiscountRatio != null" >
        fleeing_goods_discount_ratio,
      </if>
      <if test="fleeingGoodsRemark != null" >
        fleeing_goods_remark,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="status != null" >
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="yearStr != null" >
        #{yearStr,jdbcType=VARCHAR},
      </if>
      <if test="monthStr != null" >
        #{monthStr,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualRebateAmount != null" >
        #{actualRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateDiscountRatio != null" >
        #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateAmount != null" >
        #{actualDeservedRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="currentMonthRebateAmount != null" >
        #{currentMonthRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalRebateAmount != null" >
        #{totalRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopIsReach != null" >
        #{workshopIsReach,jdbcType=VARCHAR},
      </if>
      <if test="workshopReachRatio != null" >
        #{workshopReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="activeWorkshopNumber != null" >
        #{activeWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="workshopNotReachDiscountAmount != null" >
        #{workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopNotReachDiscountRatio != null" >
        #{workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="salesIsReach != null" >
        #{salesIsReach,jdbcType=VARCHAR},
      </if>
      <if test="scancodeReachRatio != null" >
        #{scancodeReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="purchaseLiters != null" >
        #{purchaseLiters,jdbcType=BIGINT},
      </if>
      <if test="salesLiters != null" >
        #{salesLiters,jdbcType=BIGINT},
      </if>
      <if test="scanCodeRatio != null" >
        #{scanCodeRatio,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountAmount != null" >
        #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountRatio != null" >
        #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsIsReach != null" >
        #{fleeingGoodsIsReach,jdbcType=VARCHAR},
      </if>
      <if test="fleeingGoodsRatio != null" >
        #{fleeingGoodsRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountAmount != null" >
        #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountRatio != null" >
        #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsRemark != null" >
        #{fleeingGoodsRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_partner_rebate_info
    <set >
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.yearStr != null" >
        year_str = #{record.yearStr,jdbcType=VARCHAR},
      </if>
      <if test="record.monthStr != null" >
        month_str = #{record.monthStr,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.actualRebateAmount != null" >
        actual_rebate_amount = #{record.actualRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.actualDeservedRebateDiscountRatio != null" >
        actual_deserved_rebate_discount_ratio = #{record.actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.actualDeservedRebateAmount != null" >
        actual_deserved_rebate_amount = #{record.actualDeservedRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.currentMonthRebateAmount != null" >
        current_month_rebate_amount = #{record.currentMonthRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.totalRebateAmount != null" >
        total_rebate_amount = #{record.totalRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.workshopIsReach != null" >
        workshop_is_reach = #{record.workshopIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.workshopReachRatio != null" >
        workshop_reach_ratio = #{record.workshopReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.activeWorkshopNumber != null" >
        active_workshop_number = #{record.activeWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="record.workshopNotReachDiscountAmount != null" >
        workshop_not_reach_discount_amount = #{record.workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.workshopNotReachDiscountRatio != null" >
        workshop_not_reach_discount_ratio = #{record.workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.salesIsReach != null" >
        sales_is_reach = #{record.salesIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.scancodeReachRatio != null" >
        scancode_reach_ratio = #{record.scancodeReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.purchaseLiters != null" >
        purchase_liters = #{record.purchaseLiters,jdbcType=BIGINT},
      </if>
      <if test="record.salesLiters != null" >
        sales_liters = #{record.salesLiters,jdbcType=BIGINT},
      </if>
      <if test="record.scanCodeRatio != null" >
        scan_code_ratio = #{record.scanCodeRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.scancodeNotReachDiscountAmount != null" >
        scancode_not_reach_discount_amount = #{record.scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.scancodeNotReachDiscountRatio != null" >
        scancode_not_reach_discount_ratio = #{record.scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsIsReach != null" >
        fleeing_goods_is_reach = #{record.fleeingGoodsIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.fleeingGoodsRatio != null" >
        fleeing_goods_ratio = #{record.fleeingGoodsRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsDiscountAmount != null" >
        fleeing_goods_discount_amount = #{record.fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsDiscountRatio != null" >
        fleeing_goods_discount_ratio = #{record.fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsRemark != null" >
        fleeing_goods_remark = #{record.fleeingGoodsRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_partner_rebate_info
    set
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      year_str = #{record.yearStr,jdbcType=VARCHAR},
      month_str = #{record.monthStr,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      actual_rebate_amount = #{record.actualRebateAmount,jdbcType=NUMERIC},
      actual_deserved_rebate_discount_ratio = #{record.actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      actual_deserved_rebate_amount = #{record.actualDeservedRebateAmount,jdbcType=NUMERIC},
      current_month_rebate_amount = #{record.currentMonthRebateAmount,jdbcType=NUMERIC},
      total_rebate_amount = #{record.totalRebateAmount,jdbcType=NUMERIC},
      workshop_is_reach = #{record.workshopIsReach,jdbcType=VARCHAR},
      workshop_reach_ratio = #{record.workshopReachRatio,jdbcType=NUMERIC},
      active_workshop_number = #{record.activeWorkshopNumber,jdbcType=INTEGER},
      workshop_not_reach_discount_amount = #{record.workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      workshop_not_reach_discount_ratio = #{record.workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      sales_is_reach = #{record.salesIsReach,jdbcType=VARCHAR},
      scancode_reach_ratio = #{record.scancodeReachRatio,jdbcType=NUMERIC},
      purchase_liters = #{record.purchaseLiters,jdbcType=BIGINT},
      sales_liters = #{record.salesLiters,jdbcType=BIGINT},
      scan_code_ratio = #{record.scanCodeRatio,jdbcType=NUMERIC},
      scancode_not_reach_discount_amount = #{record.scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      scancode_not_reach_discount_ratio = #{record.scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_is_reach = #{record.fleeingGoodsIsReach,jdbcType=VARCHAR},
      fleeing_goods_ratio = #{record.fleeingGoodsRatio,jdbcType=NUMERIC},
      fleeing_goods_discount_amount = #{record.fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      fleeing_goods_discount_ratio = #{record.fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_remark = #{record.fleeingGoodsRemark,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfo" >
    update wx_t_partner_rebate_info
    <set >
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="yearStr != null" >
        year_str = #{yearStr,jdbcType=VARCHAR},
      </if>
      <if test="monthStr != null" >
        month_str = #{monthStr,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualRebateAmount != null" >
        actual_rebate_amount = #{actualRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateDiscountRatio != null" >
        actual_deserved_rebate_discount_ratio = #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateAmount != null" >
        actual_deserved_rebate_amount = #{actualDeservedRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="currentMonthRebateAmount != null" >
        current_month_rebate_amount = #{currentMonthRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalRebateAmount != null" >
        total_rebate_amount = #{totalRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopIsReach != null" >
        workshop_is_reach = #{workshopIsReach,jdbcType=VARCHAR},
      </if>
      <if test="workshopReachRatio != null" >
        workshop_reach_ratio = #{workshopReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="activeWorkshopNumber != null" >
        active_workshop_number = #{activeWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="workshopNotReachDiscountAmount != null" >
        workshop_not_reach_discount_amount = #{workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopNotReachDiscountRatio != null" >
        workshop_not_reach_discount_ratio = #{workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="salesIsReach != null" >
        sales_is_reach = #{salesIsReach,jdbcType=VARCHAR},
      </if>
      <if test="scancodeReachRatio != null" >
        scancode_reach_ratio = #{scancodeReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="purchaseLiters != null" >
        purchase_liters = #{purchaseLiters,jdbcType=BIGINT},
      </if>
      <if test="salesLiters != null" >
        sales_liters = #{salesLiters,jdbcType=BIGINT},
      </if>
      <if test="scanCodeRatio != null" >
        scan_code_ratio = #{scanCodeRatio,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountAmount != null" >
        scancode_not_reach_discount_amount = #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountRatio != null" >
        scancode_not_reach_discount_ratio = #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsIsReach != null" >
        fleeing_goods_is_reach = #{fleeingGoodsIsReach,jdbcType=VARCHAR},
      </if>
      <if test="fleeingGoodsRatio != null" >
        fleeing_goods_ratio = #{fleeingGoodsRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountAmount != null" >
        fleeing_goods_discount_amount = #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountRatio != null" >
        fleeing_goods_discount_ratio = #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsRemark != null" >
        fleeing_goods_remark = #{fleeingGoodsRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfo" >
    update wx_t_partner_rebate_info
    set partner_id = #{partnerId,jdbcType=BIGINT},
      year_str = #{yearStr,jdbcType=VARCHAR},
      month_str = #{monthStr,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      actual_rebate_amount = #{actualRebateAmount,jdbcType=NUMERIC},
      actual_deserved_rebate_discount_ratio = #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      actual_deserved_rebate_amount = #{actualDeservedRebateAmount,jdbcType=NUMERIC},
      current_month_rebate_amount = #{currentMonthRebateAmount,jdbcType=NUMERIC},
      total_rebate_amount = #{totalRebateAmount,jdbcType=NUMERIC},
      workshop_is_reach = #{workshopIsReach,jdbcType=VARCHAR},
      workshop_reach_ratio = #{workshopReachRatio,jdbcType=NUMERIC},
      active_workshop_number = #{activeWorkshopNumber,jdbcType=INTEGER},
      workshop_not_reach_discount_amount = #{workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      workshop_not_reach_discount_ratio = #{workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      sales_is_reach = #{salesIsReach,jdbcType=VARCHAR},
      scancode_reach_ratio = #{scancodeReachRatio,jdbcType=NUMERIC},
      purchase_liters = #{purchaseLiters,jdbcType=BIGINT},
      sales_liters = #{salesLiters,jdbcType=BIGINT},
      scan_code_ratio = #{scanCodeRatio,jdbcType=NUMERIC},
      scancode_not_reach_discount_amount = #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      scancode_not_reach_discount_ratio = #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_is_reach = #{fleeingGoodsIsReach,jdbcType=VARCHAR},
      fleeing_goods_ratio = #{fleeingGoodsRatio,jdbcType=NUMERIC},
      fleeing_goods_discount_amount = #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      fleeing_goods_discount_ratio = #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_remark = #{fleeingGoodsRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  
  
  
  <resultMap id="TaskResultMap" type="com.chevron.task.model.WxTaskMainNew">
    <result column="task_main_id" jdbcType="BIGINT" property="taskMainId" />
    <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" /> 
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="task_priority" jdbcType="VARCHAR" property="taskPriority" />
    <result column="in_stock_no" jdbcType="VARCHAR" property="inStockNo" />
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="excute_user_id" property="excuteUserId" jdbcType="BIGINT" />
    <result column="exec_user_name" jdbcType="VARCHAR" property="executeUserName" />
    <result column="is_pass" jdbcType="INTEGER" property="isPass" /> 
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" /> 
    <result column="task_id" jdbcType="BIGINT" property="taskId" /> 
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="organizationname" jdbcType="NVARCHAR" property="organizationName" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workShopName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workShopId" />
    <result column="workshop_addr" jdbcType="VARCHAR" property="workshopAddress" /> 
    <result column="scale" jdbcType="VARCHAR" property="workshopScale" /> 
    <result column="f_workshopstatus" jdbcType="VARCHAR" property="workshopStatus" />
    <result column="access_count" jdbcType="INTEGER" property="accessCount" /> 
    <result column="taskstatus" jdbcType="VARCHAR" property="taskStatusChn" />  
    <result column="IS_TO_LD" jdbcType="NVARCHAR" property="isToLD" />
    <result column="IS_TO_GD" jdbcType="NVARCHAR" property="isToGD" />
    <result column="new_task_status" jdbcType="NVARCHAR" property="taskNewStatus" />
    <result column="task_remark" jdbcType="NVARCHAR" property="taskRemark" />
    <result column="check_count" jdbcType="INTEGER" property="checkCount" />
    <result column="workshop_type" jdbcType="NVARCHAR" property="workshopType" />
    <result column="monthly_oil_sales_volume" jdbcType="BIGINT" property="monthlyOilSalesVolume" /> 
    <result column="monthly_chevron_oil_sales_volume" jdbcType="BIGINT" property="monthlyChevronOilSalesVolume" /> 
    </resultMap>
    <!-- 含有数据权限控制 -->
   <select id="getTaskByType" parameterType="com.chevron.partnerorder.model.PartnerRebateTaskConditions" resultMap="TaskResultMap">
    SELECT 
     *
    FROM
    (
    SELECT 
    *,
    (CASE 
     WHEN IS_TO_LD='0' AND t.task_status='4' AND t.tmb_type_code='TT_2_LD' THEN '11'
     WHEN IS_TO_LD='1' AND t.task_status='4' AND t.tmb_type_code='TT_2_LD' THEN '10' 
     WHEN IS_TO_GD='0' AND t.task_status='4' AND t.tmb_type_code='TT_2_SD' THEN '9' 
     WHEN IS_TO_GD='1' AND t.task_status='4' AND t.f_workshopstatus='0' AND t.tmb_type_code='TT_2_SD' THEN '8' 
     ELSE t.task_status end )AS new_task_status 
     FROM (
    
    SELECT 
    tt_main.task_name as task_name,tt_main.task_start_time,t_sub.xg_sj task_finish_time,tt_main.tmb_type_code,
    tt_main.create_user,tt_main_createu.ch_name create_user_name,tt_main.create_time,tt_main.in_stock_no,
    t_sub.task_status,t_sub.exec_user excute_user_id,tt_user.ch_name exec_user_name ,
    t_sub.check_result,t_sub.is_pass,t_sub.task_remark ,t_sub.check_count ,
    t_sub.task_id,t_sub.task_main_id,t_sub.tenant_id partner_id,tt_org.organization_name organizationname,
    tt_workshop.work_shop_name workshop_name,tt_workshop.scale,tt_workshop.work_shop_address workshop_addr,
    tt_workshop.id workshop_id,tt_workshop.type workshop_type, 
    tt_workshop.monthly_oil_sales_volume, tt_workshop.monthly_chevron_oil_sales_volume,
    tt_main.task_priority,
    tt_workshop.status f_workshopstatus,
    tt_has_gd_workshopid.workshop_id has_gd_workshopid,
    (SELECT
            count(*)
            FROM
            wx_t_task_exec_trace
            WHERE
            work_shop_id = tt_workshop.id
            AND
            sub_task_id = t_sub.task_id) AS access_count,
    (CASE WHEN  tt_has_gd_workshopid.workshop_id IS NOT NULL OR tt_workshop.status='3' 
                THEN
                '0'
                ELSE 
                '1'
              END)AS IS_TO_GD,
    (CASE 
              WHEN t_sub.task_status='4' AND tt_workshop.status='1' AND tt_main.tmb_type_code='TT_2_LD'  
              THEN '1' 
              ELSE '0' END)AS IS_TO_LD
    
    FROM wx_task_sub t_sub 
    LEFT JOIN wx_task_main tt_main ON tt_main.task_main_id = t_sub.task_main_id
    LEFT JOIN wx_t_user tt_user ON tt_user.user_id=t_sub.exec_user 
    LEFT JOIN wx_t_user tt_main_createu ON tt_main.create_user = tt_main_createu.user_id
    LEFT JOIN wx_t_organization tt_org ON tt_user.org_id= tt_org.id 
    LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = t_sub.org_id
    LEFT JOIN wx_task_has_gd_workshop tt_has_gd_workshopid ON tt_has_gd_workshopid.workshop_id = t_sub.org_id  
    WHERE 
        tt_main.tmb_type_code= 'TT_2_SD'
     and
        <if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
                <![CDATA[ t_sub.xg_sj>= #{taskFinishTimeS} and ]]>
        </if>
        <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
                <![CDATA[ t_sub.xg_sj< #{taskFinishTimeE} and ]]>
        </if>
        <if test="partnerId!=null and partnerId!=''">
            t_sub.tenant_id = #{partnerId}  
        AND 
        </if> 
        tt_user.ch_name IS  NOT NULL
     and 
     1 = 1 $Permission_Clause$
    ) t
    ) tt 
    WHERE 1=1 
    AND (tt.new_task_status!='1' and tt.new_task_status!='3' and tt.new_task_status!='6' and tt.new_task_status!='7')
   </select>  
   
   
   
   
   <select id="getPartnerRebateInfoByMap" resultMap="BaseResultMap">
       SELECT * FROM wx_t_partner_rebate_info t_r_info
        WHERE 1=1
        <if test="partnerId!=null">
        and
        t_r_info.partner_id = #{partnerId}
        </if>
        <if test="year!=null">
        and
        t_r_info.year_str = #{year}
        </if>
        <if test="month!=null">
        and
        t_r_info.month_str = #{month}
        </if>
   </select>
   
   
   <select id="getActualRebateAmountByMap" resultType="double" parameterType="map">
        SELECT sum(transaction_amount)  as transaction_amount FROM wx_t_partner_bill_detail 
		WHERE transaction_time >#{queryTime} AND transaction_type = 'IN BILL' 
		AND partner_id = #{partnerId}
   </select>
   
   
   <select id="getPartnerBillTotalAmountByMap" resultType="double" parameterType="map">
		select  total_amount from wx_t_partner_bill
		WHERE partner_id = #{partnerId}
   </select>
   
   
   <select id="getPartnerInStockLitersByMap" resultType="long" parameterType="map">
       SELECT sum(total_liter_count) FROM wx_t_partner_order 
       WHERE partner_id = #{partnerId} 
       <![CDATA[and create_time>= #{queryStartTime}]]>
       <![CDATA[and create_time< #{queryEndTime}]]>
   </select>
   
   <select id="getPartnerOutStockLitersByMap" resultType="long" parameterType="map">
       SELECT sum(t_a.skuLiterCount) FROM (
		SELECT osp.sku, isnull( osp.actual_out_count,0) AS count, product.capacity, convert(FLOAT,product.capacity) * isnull( osp.actual_out_count,0) AS skuLiterCount 
			FROM wx_t_out_stock_product osp 
			LEFT JOIN wx_t_product product ON osp.sku = product.sku
			WHERE osp.stock_out_no IN (
				SELECT stock_out_no FROM wx_t_out_stock WHERE order_no  IN(
					SELECT order_no FROM wx_t_order t_order 
						LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_order.work_shop_id = t_workshop_partner.workshop_id 
						WHERE 1=1 AND (t_order.order_type != 'DA' and t_order.order_type != 'DP' and t_order.order_type != 'SPDA') 
						AND  t_workshop_partner.partner_id = #{partnerId}
					) 	
					<![CDATA[and out_time>= #{queryStartTime}]]> 
					<![CDATA[and out_time< #{queryEndTime}]]>
			)
			AND product.product_property = 'SN Above'
		) t_a
   </select>
   
    <resultMap id="ResponsePartnerRebateInfoMap" type="com.chevron.partnerorder.model.ResponsePartnerRebateInfo" >
	    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
	    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
	    <!-- 应返利 -->
	    <result column="actual_rebate_amount" property="currentRebateAmount" jdbcType="NUMERIC" />
	    <!-- 实际返利 -->
	    <result column="actual_deserved_rebate_amount" property="actualRebateAmount" jdbcType="NUMERIC" />
	    <result column="actual_deserved_rebate_discount_ratio" property="actualDeservedRebateDiscountRatio" jdbcType="NUMERIC" />
	    <result column="workshop_is_reach" property="workshopIsReach" jdbcType="VARCHAR" />
	    <result column="active_workshop_number" property="activeWorkshopNumber" jdbcType="INTEGER" />
	    <result column="workshop_not_reach_discount_ratio" property="workshopNotReachDiscountRatio" jdbcType="NUMERIC" />
	    
	    <result column="sales_is_reach" property="salesIsReach" jdbcType="VARCHAR" />
	    <result column="purchase_liters" property="purchaseLiters" jdbcType="BIGINT" />
	    <result column="sales_liters" property="salesLiters" jdbcType="BIGINT" />
	    <!-- 扫码率 -->
	    <result column="scan_code_ratio" property="scanCodeRatio" jdbcType="NUMERIC" />
	    <result column="scancode_not_reach_discount_ratio" property="scancodeNotReachDiscountRatio" jdbcType="NUMERIC" />
	    
	    <result column="fleeing_goods_is_reach" property="fleeingGoodsIsReach" jdbcType="VARCHAR" />
	    <result column="fleeing_goods_discount_ratio" property="fleeingGoodsDiscountRatio" jdbcType="NUMERIC" />
	    <result column="fleeing_goods_remark" property="fleeingGoodsRemark" jdbcType="VARCHAR" />
	    
	    <result column="workshop_count" property="workshopCount" jdbcType="INTEGER" />
	    <result column="is_workshop_reach" property="isWorkshopReach" jdbcType="VARCHAR" />
	    <result column="sell_in" property="sellIn" jdbcType="BIGINT" />
	    <result column="sell_throuth" property="sellThrouth" jdbcType="BIGINT" />
	    <result column="cdm_rebate" property="cdmRebate" jdbcType="NUMERIC" />
	    <result column="status" property="status" jdbcType="VARCHAR" />
    </resultMap>
    <select id="queryPartnerRebateInfoByMap" resultMap="ResponsePartnerRebateInfoMap" parameterType="map">
       <!-- 合伙人返利详情最新： -->
		SELECT DISTINCT t_partner_workshop_info.*, cast(t_partner_scan_info.sell_in as bigint)sell_in, 
		<!--cast(t_partner_scan_info.sell_throuth as bigint)sell_throuth,--> 
		CAST(isnull(t_sell_throuth.sell_through,0) AS bigint) AS sell_throuth,
		(-1*t_partner_scan_info.cdm_rebate) cdm_rebate,
		t_partner_rebate.actual_rebate_amount,t_partner_rebate.actual_deserved_rebate_discount_ratio,t_partner_rebate.actual_deserved_rebate_amount,t_partner_rebate.current_month_rebate_amount,t_partner_rebate.total_rebate_amount,
		t_partner_rebate.workshop_is_reach,t_partner_rebate.workshop_reach_ratio,t_partner_rebate.active_workshop_number,t_partner_rebate.workshop_not_reach_discount_ratio,
		t_partner_rebate.sales_is_reach,t_partner_rebate.purchase_liters,t_partner_rebate.sales_liters,t_partner_rebate.scan_code_ratio,t_partner_rebate.scancode_not_reach_discount_ratio,
		t_partner_rebate.fleeing_goods_is_reach,t_partner_rebate.fleeing_goods_discount_ratio,t_partner_rebate.fleeing_goods_remark,t_partner_rebate.status
		from (
		<!-- 合伙人门店录入统计 -->
		SELECT t_og.id partner_id ,t_og.organization_name partner_name, 
		isnull(ttt.workshop_count,0) workshop_count, <!-- 门店数量 -->
		(CASE  WHEN isnull(ttt.workshop_count,0) &gt;= #{workshopReachCount}  <!-- 门店变量1 -->
		THEN 'true'
		ELSE 'false'
		END) is_workshop_reach <!-- 是否达标 -->
		FROM wx_t_organization t_og 
		LEFT JOIN 
		(SELECT * FROM 
		(SELECT  count(1) workshop_count,t_main.tenant_id FROM
		  wx_task_main t_main
		INNER  JOIN wx_task_sub t_sub
		ON t_main.task_main_id = t_sub.task_main_id
		WHERE t_sub.xg_sj &gt;= #{queryStartDateStr}   <!-- >='2018-08-01'时间1变量 -->
		AND t_sub.xg_sj  &lt; #{queryEndDateStr}   <!-- <'2018-09-01'时间2变量 -->
		AND t_main.tmb_type_code = 'TT_2_SD'
		AND t_sub.task_status = '4'
		GROUP BY t_main.tenant_id) tt) ttt
		ON ttt.tenant_id = t_og.id
		INNER JOIN wx_t_partner_o2o_enterprise tt_partner
		ON tt_partner.partner_id = t_og.id 
		WHERE t_og.type = '1' AND tt_partner.partner_property = 'NORMAL') t_partner_workshop_info
		INNER  JOIN 
		<!-- 合伙人扫码统计 -->
		(SELECT DISTINCT t_org.id partner_id,t_org.organization_name, 
		isnull(tt.sell_in,0) sell_in, isnull(tt.sell_throuth,0)sell_throuth,isnull(tt.cdm_rebate,0)cdm_rebate  
		FROM wx_t_partner_o2o_enterprise t_p_o
		LEFT JOIN wx_t_organization t_org
		ON t_org.id = t_p_o.partner_id
		LEFT JOIN 
		(SELECT * FROM 
		(SELECT dw_sellin_throuth.customer_name_cn,
		isnull(sum(dw_sellin_throuth.sell_in),0) sell_in,
		isnull(sum(dw_sellin_throuth.sell_through),0)sell_throuth,
		isnull(sum(dw_sellin_throuth.cdm_rebate),0) * 1.16 cdm_rebate
		 FROM dw_cdm_rebate_sell_in_throuth dw_sellin_throuth
		WHERE dw_sellin_throuth.year_month_date = #{dwQueryDateStr} <!-- '2018-09-01' 时间3变量   注意时间与上面时间分开定义变量 -->
		GROUP BY dw_sellin_throuth.customer_name_cn) t) tt
		ON tt.customer_name_cn = t_org.organization_name
		WHERE  t_org.type = '1' AND t_p_o.partner_property = 'NORMAL' and t_org.status=1) t_partner_scan_info
		ON  t_partner_scan_info.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN (
			SELECT partner_id, isNull(sum(sell_through),0) sell_through
	      		FROM
	      		(select t_workshop_partner.partner_id,
	      			(select sum(isnull( osp.actual_out_count,0) * convert(float, p1.capacity))
	      				from wx_t_out_stock_product osp
	      				LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
	     				left join wx_t_product p1 on p1.sku=osp.sku 
	     				WHERE t_outstock.order_no = o.order_no and p1.product_property = 'SN Above'
	     				AND  t_outstock.out_time &gt;= #{queryStartDateStr}   <!-- >='2018-08-01'时间1变量 -->
						AND  t_outstock.out_time &lt; #{queryEndDateStr} 
	     			) sell_through
	     			from wx_t_order o
	      		left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
	      		where o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7')
	      	GROUP BY t_workshop_partner.partner_id,o.order_no
	      ) tt
	      GROUP BY partner_id
		)t_sell_throuth ON t_sell_throuth.partner_id = t_partner_workshop_info.partner_id
		LEFT JOIN wx_t_partner_rebate_info t_partner_rebate
		ON t_partner_rebate.partner_id = t_partner_scan_info.partner_id
		AND  (year_str+'-'+ left('00',2-len(month_str))+month_str) = #{yearMonthStr} <!-- '2018-08'时间变量4 -->
		WHERE 1=1 
		<if test="partnerId!=null">
		AND t_partner_workshop_info.partner_id = #{partnerId}  <!-- 查询单个合伙人的时候，添加上此条件过滤 -->
		</if>
   </select>
   
   <select id="queryPartnerRebateInfoConfirmedByMap" resultMap="ResponsePartnerRebateInfoMap" parameterType="map">
       SELECT t_og.id as partner_id ,t_og.organization_name as partner_name,
        t_partner_rebate.purchase_liters as sell_in,
        t_partner_rebate.sales_liters as sell_throuth,
		t_partner_rebate.actual_rebate_amount,t_partner_rebate.actual_deserved_rebate_discount_ratio,t_partner_rebate.actual_deserved_rebate_amount,t_partner_rebate.current_month_rebate_amount,t_partner_rebate.total_rebate_amount,
		t_partner_rebate.workshop_is_reach,t_partner_rebate.workshop_reach_ratio,t_partner_rebate.active_workshop_number,t_partner_rebate.workshop_not_reach_discount_ratio,
		t_partner_rebate.sales_is_reach,t_partner_rebate.purchase_liters,t_partner_rebate.sales_liters,t_partner_rebate.scan_code_ratio,t_partner_rebate.scancode_not_reach_discount_ratio,
		t_partner_rebate.fleeing_goods_is_reach,t_partner_rebate.fleeing_goods_discount_ratio,t_partner_rebate.fleeing_goods_remark,t_partner_rebate.status
		from wx_t_partner_rebate_info t_partner_rebate	
		LEFT JOIN wx_t_organization t_og on t_og.id = t_partner_rebate.partner_id
		WHERE 1=1 
		AND  (year_str+'-'+ left('00',2-len(month_str))+month_str) = #{yearMonthStr} <!-- '2018-08'时间变量4 -->
		<if test="partnerId!=null">
		AND t_partner_rebate.partner_id = #{partnerId}  <!-- 查询单个合伙人的时候，添加上此条件过滤 -->
		</if>
   </select>
   
    <resultMap id="ResponseTotalRebateAmountMap" type="com.chevron.partnerorder.model.ResponseTotalRebateAmount" >
	    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
	    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
	    <result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
	    <result column="apply_fund" property="applyFund" jdbcType="NUMERIC" />
	    <result column="used_amount" property="usedAmount" jdbcType="NUMERIC" />
    </resultMap>
    <select id="queryTotalRebateAmount" resultMap="ResponseTotalRebateAmountMap" parameterType="map">
        SELECT rebate.*,ISNULL(apply_fund.apply_fund,0) AS apply_fund, ISNULL(used_amount.used_amount,0) AS used_amount FROM (
		SELECT t_rebate_info.partner_id, t_org.organization_name AS partner_name
			, SUM(isnull(t_rebate_info.actual_deserved_rebate_amount, 0)) AS total_amount
		FROM wx_t_partner_rebate_info t_rebate_info
		LEFT JOIN wx_t_organization t_org
				ON t_org.id = t_rebate_info.partner_id
		WHERE 1 = 1
			AND t_org.status = 1
		GROUP BY t_rebate_info.partner_id, t_org.organization_name
		) rebate
		LEFT JOIN (
			SELECT sum(apply_fund) AS apply_fund, organization_id AS partner_id
			FROM wx_cdm_rebate_apply
			WHERE status >= 2
				AND status != 3
			GROUP BY organization_id
		) apply_fund
		ON apply_fund.partner_id = rebate.partner_id
		LEFT JOIN (
			SELECT sum(invoice_amount_total) AS used_amount, organization_id AS partner_id
			FROM wx_cdm_rebate_apply
			WHERE status = 20
			GROUP BY organization_id
		) used_amount
		ON used_amount.partner_id = rebate.partner_id
		LEFT JOIN wx_t_organization t_org ON t_org.id = rebate.partner_id
		<if test="partnerId!=null">
		AND rebate.partner_id = #{partnerId}
		</if>
    </select>
    <select id="queryTotalRebateAmountByYearMonth" resultMap="ResponseTotalRebateAmountMap" parameterType="map">
        SELECT rebate.*,ISNULL(apply_fund.apply_fund,0) AS apply_fund, ISNULL(used_amount.used_amount,0) AS used_amount FROM (
		SELECT t_rebate_info.partner_id, t_org.organization_name AS partner_name
			, SUM(isnull(t_rebate_info.actual_deserved_rebate_amount, 0)) AS total_amount
		FROM wx_t_partner_rebate_info t_rebate_info
		LEFT JOIN wx_t_organization t_org
				ON t_org.id = t_rebate_info.partner_id
		WHERE 1 = 1
			AND t_org.status = 1
		and CONVERT(datetime,month_str+'/1/'+ year_str,101) &lt;= CONVERT(datetime,#{monthStr} +'/1/'+ #{yearStr},101)
		GROUP BY t_rebate_info.partner_id, t_org.organization_name
		) rebate
		LEFT JOIN (
			SELECT sum(apply_fund) AS apply_fund, organization_id AS partner_id
			FROM wx_cdm_rebate_apply
			WHERE status >= 2
				AND status != 3
			GROUP BY organization_id
		) apply_fund
		ON apply_fund.partner_id = rebate.partner_id
		LEFT JOIN (
			SELECT sum(invoice_amount_total) AS used_amount, organization_id AS partner_id
			FROM wx_cdm_rebate_apply
			WHERE status = 20
			GROUP BY organization_id
		) used_amount
		ON used_amount.partner_id = rebate.partner_id
		LEFT JOIN wx_t_organization t_org ON t_org.id = rebate.partner_id
		where 1 = 1
		<if test="partnerId!=null">
		AND rebate.partner_id = #{partnerId}
		</if>
    </select>
    <select id="queryTotalRebateAmountByCondition" resultMap="ResponseTotalRebateAmountMap"
		parameterType="com.chevron.partnerorder.model.ResponseTotalRebateAmountConditions">
		select v.* from
		(
			SELECT rebate.*,ISNULL(apply_fund.apply_fund,0) AS apply_fund, ISNULL(used_amount.used_amount,0) AS used_amount FROM (
			SELECT t_rebate_info.partner_id, t_org.organization_name AS partner_name
				, SUM(isnull(t_rebate_info.actual_deserved_rebate_amount, 0)) AS total_amount
			FROM wx_t_partner_rebate_info t_rebate_info
			LEFT JOIN wx_t_organization t_org
					ON t_org.id = t_rebate_info.partner_id
			WHERE 1 = 1
				AND t_org.status = 1
			GROUP BY t_rebate_info.partner_id, t_org.organization_name
			) rebate
			LEFT JOIN (
				SELECT sum(apply_fund) AS apply_fund, organization_id AS partner_id
				FROM wx_cdm_rebate_apply
				WHERE status >= 2
					AND status != 3
				GROUP BY organization_id
			) apply_fund
			ON apply_fund.partner_id = rebate.partner_id
			LEFT JOIN (
				SELECT sum(invoice_amount_total) AS used_amount, organization_id AS partner_id
				FROM wx_cdm_rebate_apply
				WHERE status = 20
				GROUP BY organization_id
			) used_amount
			ON used_amount.partner_id = rebate.partner_id
			LEFT JOIN wx_t_organization t_org ON t_org.id = rebate.partner_id
			<if test="partnerId!=null">
			AND rebate.partner_id = #{partnerId}
			</if>
		) v where 1 = 1 
	</select>
    <resultMap id="ResponseTotalRebateAmountDetailMap" type="com.chevron.partnerorder.model.ResponseTotalRebateAmountDetail" >
    	<result column="partner_id" property="partnerId" jdbcType="VARCHAR" />
        <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
        <result column="year_str" property="yearStr" jdbcType="VARCHAR" />
        <result column="month_str" property="monthStr" jdbcType="VARCHAR" />
        <result column="trans_amount" property="transAmount" jdbcType="NUMERIC" />
        <result column="trans_time" property="transTime" jdbcType="TIMESTAMP" />
        <result column="trans_remark" property="transRemark" jdbcType="NUMERIC" />
        <result column="status" property="status" jdbcType="NUMERIC" />
    </resultMap>
     <select id="queryTotalRebateAmountDetail" resultMap="ResponseTotalRebateAmountDetailMap" parameterType="map">
        SELECT t_rebate_info.partner_id,org.organization_name as partner_name,t_rebate_info.year_str,t_rebate_info.month_str,
			t_rebate_info.actual_deserved_rebate_amount trans_amount,t_rebate_info.create_time trans_time,
			t_rebate_info.month_str+'月份返利' trans_remark,t_rebate_info.status
			FROM wx_t_partner_rebate_info t_rebate_info
			LEFT JOIN wx_t_organization org ON org.id = t_rebate_info.partner_id
			WHERE 1=1
        	<if test="partnerId!=null">
        		AND t_rebate_info.partner_id = #{partnerId}
        	</if>
    </select>


  <resultMap id="PartnerSalesInfoMap" type="com.chevron.partnerorder.model.PartnerSalesInfo" >
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
    <result column="sell_in" property="sellIn" jdbcType="NUMERIC" />
    <result column="sell_through" property="sellThrough" jdbcType="NUMERIC" />
    <result column="year_str" property="year" jdbcType="INTEGER" />
    <result column="month_str" property="month" jdbcType="INTEGER" />
    <result column="day_str" property="day" jdbcType="INTEGER" />
  </resultMap>
  <select id="getPartnerSellInInfo" resultMap="PartnerSalesInfoMap" parameterType="map">
    <!-- SELECT po.partner_id,po.partner_name,sum(po.total_liter_count)sell_in
    from wx_t_partner_order po
    WHERE  po.status  IN ('3','5','6','7')
    <![CDATA[and po.create_time>=#{sellInStartTime}]]>
    <![CDATA[and po.create_time<#{sellInEndTime}]]>
    AND po.partner_id = #{partnerId}
    GROUP BY po.partner_id,po.partner_name -->
    SELECT t_org.id AS partner_id,t_org.organization_name AS partner_name, dw_sell.sell_in FROM (
		SELECT customer_name_cn, isnull(sum(sell_in),0) AS sell_in FROM dw_cdm_rebate_sell_in_throuth WHERE  year_month_date = #{sellInTime}
		GROUP BY customer_name_cn ) dw_sell
	<!-- LEFT JOIN wx_t_partner_o2o_enterprise poe ON dw_sell.customer_code = poe.sap_code -->
	<!-- LEFT JOIN wx_t_organization t_org ON t_org.id = poe.partner_id  -->
	LEFT JOIN wx_t_organization t_org ON t_org.organization_name = dw_sell.customer_name_cn
	WHERE t_org.id = #{partnerId}

  </select>


  <select id="getPartnerSellThroughInfo" resultMap="PartnerSalesInfoMap" parameterType="map">
    SELECT partner_id, sum(sell_through)sell_through
      FROM
      (select t_workshop_partner.partner_id,
      (select sum(isnull( osp.actual_out_count,0) * convert(float, p1.capacity))
      	from wx_t_out_stock_product osp
      	LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
      	left join wx_t_product p1 on p1.sku=osp.sku 
      	WHERE t_outstock.order_no = o.order_no and p1.product_property = 'SN Above'
      	 <![CDATA[and t_outstock.out_time>=#{startTime}]]>
      	 <![CDATA[and t_outstock.out_time<#{endTime}]]>
      )sell_through
      from wx_t_order o
      left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
      where o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7')
      <if test="partnerId!=null">
      AND t_workshop_partner.partner_id = #{partnerId}
      </if>
      <if test="partnerIds!=null">
        AND t_workshop_partner.partner_id in
        <foreach item="partnerid" index="index" collection="partnerIds" open="(" separator="," close=")">
          #{partnerid}
        </foreach>
      </if>
      GROUP BY t_workshop_partner.partner_id,o.order_no
      ) tt
      GROUP BY partner_id
  </select>


    <select id="getPartnerSellThroughInfoEveryDay" resultMap="PartnerSalesInfoMap" parameterType="map">
        SELECT out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
			, SUM(out_stock_detail.actual_out_count * out_stock_detail.capacity) AS sell_through
		FROM (
			SELECT isnull(osp.actual_out_count, 0) AS actual_out_count
				, convert(float, p1.capacity) AS capacity, t_workshop_partner.partner_id
				, datepart(yy, t_outstock.out_time) AS year_str
				, datepart(mm, t_outstock.out_time) AS month_str
				, datepart(dd, t_outstock.out_time) AS day_str
			FROM wx_t_out_stock_product osp
				LEFT JOIN wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
				LEFT JOIN wx_t_product p1 ON p1.sku = osp.sku
				LEFT JOIN wx_t_order o ON o.order_no = t_outstock.order_no
				LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
			WHERE o.order_type != 'DA'
				AND o.order_type != 'DP'
				AND o.order_type != 'SPDA'
				AND o.status IN ('5', '7')
				AND p1.product_property = 'SN Above'
				<![CDATA[and t_outstock.out_time >= #{startTime}]]>
		        <![CDATA[and t_outstock.out_time < #{endTime}]]>
				AND t_workshop_partner.partner_id = #{partnerId}
		) out_stock_detail
		GROUP BY out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
  </select>



  <resultMap id="PartnerSalesInfoToMessageMap" type="com.chevron.partnerorder.model.PartnerSalesInfoToMessageVo" >
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
    <result column="sell_in" property="sellIn" jdbcType="NUMERIC" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="mobile_tel" property="mobile" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
  </resultMap>
  <select id="getPartnerSellInInfoForMessage" resultMap="PartnerSalesInfoToMessageMap" parameterType="map">
    SELECT * FROM
    (SELECT row_number()
    OVER (partition BY po.partner_id
    ORDER BY  po.partner_id,
    tt_user.user_id DESC) AS group_idx,
    po.partner_id,po.partner_name,tt_user.user_id user_id, tt_user.mobile_tel,tt_user.email, sum(po.total_liter_count)sell_in
    from wx_t_partner_order po
    LEFT  JOIN wx_t_user tt_user
    ON po.partner_id = tt_user.org_id
    inner JOIN wx_t_userrole tt_user_role
    ON tt_user.user_id = tt_user_role.user_id
    inner JOIN wx_t_role tt_role
    ON tt_user_role.role_id = tt_role.role_id
    WHERE  po.status  IN ('3','5','6','7')
    <![CDATA[and po.create_time>=#{sellInStartTime}]]>
    <![CDATA[and po.create_time<#{sellInEndTime}]]>
    AND tt_role.ch_role_name='Service_Partner_Manager'
    AND tt_user.email !=''
    AND tt_user.email IS NOT NULL
    <if test="partnerId!=null">
    AND po.partner_id = #{partnerId}
    </if>
    GROUP BY po.partner_id,po.partner_name,tt_user.mobile_tel,tt_user.email,tt_user.user_id)tt
    WHERE group_idx=1
  </select>
  
  <select id="statisticSellInSellThroughByMonth" resultMap="PartnerSalesInfoMap" parameterType="map">
    SELECT org.id AS partner_id,org.organization_name AS partner_name,  
	isnull(sellin.sell_in,0) AS sell_in , 
	isnull(sellthrough.sell_through, 0) AS sell_through
	FROM
		wx_t_organization org 
		LEFT JOIN wx_t_partner_o2o_enterprise poe ON org.id = poe.partner_id
		LEFT JOIN (
		SELECT dw_sellin.customer_name_cn, SUM(isnull(dw_sellin.sell_in, 0)) AS sell_in
		FROM dw_cdm_rebate_sell_in_throuth dw_sellin
		WHERE dw_sellin.year_month_date = #{yearMonthDate}
		GROUP BY dw_sellin.customer_name_cn
	) sellin ON sellin.customer_name_cn = org.organization_name
		LEFT JOIN (
			SELECT partner_id, isnull(SUM(sell_through), 0) AS sell_through
			FROM (
				SELECT t_workshop_partner.partner_id
					, (
						SELECT SUM(isnull(osp.actual_out_count, 0) * convert(float, p1.capacity))
						FROM wx_t_out_stock_product osp
							LEFT JOIN wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
							LEFT JOIN wx_t_product p1 ON p1.sku = osp.sku
						WHERE t_outstock.order_no = o.order_no
							AND p1.product_property = 'SN Above'
							<![CDATA[AND t_outstock.out_time >= #{startTime}]]>
					) AS sell_through
				FROM wx_t_order o
					LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
				WHERE o.order_type != 'DA'
					AND o.order_type != 'DP'
					AND o.order_type != 'SPDA'
					AND o.status IN ('5', '7')
				GROUP BY t_workshop_partner.partner_id, o.order_no
			) tt
			GROUP BY partner_id
		) sellthrough
		ON sellthrough.partner_id = org.id WHERE org.type = '1' AND poe.partner_property = 'NORMAL'
	</select>
</mapper>