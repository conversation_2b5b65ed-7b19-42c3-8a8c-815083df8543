package com.chevron.promote.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.point.business.PointBizService;
import com.chevron.point.dic.BusinessType;
import com.chevron.point.model.WXTPointBusinessVo;
import com.chevron.point.model.WXTPointValueDetailVo;
import com.chevron.promote.business.ApproveHistoryService;
import com.chevron.promote.business.PromoteBizService;
import com.chevron.promote.dao.PromoteApplicationBatchMapper;
import com.chevron.promote.dao.PromoteDistributionMapper;
import com.chevron.promote.dao.PromotePlanMapper;
import com.chevron.promote.dao.PromoteRegionalPointsLogMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.CountPacksUsageForDistribution;
import com.chevron.promote.model.CountPromoteDistributionPack;
import com.chevron.promote.model.CountPromoteHandleInfo;
import com.chevron.promote.model.CountUsedPacksInfo;
import com.chevron.promote.model.CustomerRegionUser;
import com.chevron.promote.model.PromoteApplicationBatch;
import com.chevron.promote.model.PromoteDistribution;
import com.chevron.promote.model.PromoteDistributionPreData;
import com.chevron.promote.model.PromotePlan;
import com.chevron.promote.model.PromoteRegionalPointsLog;
import com.chevron.promote.model.PromoteStatusEnum;
import com.chevron.promote.model.detail.PromoteAdvertisementDetail;
import com.chevron.promote.model.detail.PromoteAgricultureDetail;
import com.chevron.promote.model.detail.PromoteRoadshowActivityDetail;
import com.chevron.promote.model.detail.PromoteSeminarActivityDetail;
import com.chevron.promote.model.detail.PromoteShopSignsDetail;
import com.chevron.promote.model.detail.PromoteTryOilDetail;
import com.chevron.promote.model.detail.PromoteXDOpenDetail;
import com.chevron.promote.model.response.ResponsePromoteSeminarDetail;
import com.chevron.promote.service.IPromoteApplicationWorkFlowService;
import com.chevron.promote.service.IPromoteDistributionService;
import com.chevron.promote.service.IPromotePlanService;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.EmptyChecker;
@Service
public class PromoteDistributionServiceImpl extends BasePromote implements IPromoteDistributionService{
	private static Logger log = LoggerFactory.getLogger(PromoteDistributionServiceImpl.class);
	@Resource
	PromotePlanMapper planMapper;
	@Resource
	IPromotePlanService planService;
	@Resource
	PromoteDistributionMapper planDistributionMapper;
	@Resource
	PromoteBizService promoteBizService;
	@Resource
	PromoteApplicationBatchMapper promoteApplyBatchMapper;
	@Resource
	ApproveHistoryService approveHisService;
	
	@Resource
	PromoteRegionalPointsLogMapper pointsLogMapper;
	@Resource
	IPromoteApplicationWorkFlowService promoteWorkFlowService;
	@Resource
	private PointBizService pointBizService;
	
	@Override
	public Map<String, Object> countPromotePacksAndPoints() {
		log.info("countPromotePacksAndPoints");
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			// 统计地促分配礼包数量
			CountPromoteDistributionPack countPacks = new CountPromoteDistributionPack();
			// 获取customer用户
			CustomerRegionUser customerRegionUser = planService.getCustomerRegionUserV2();
			// 获取当前用户负责的区域
			String regionName = customerRegionUser.getRegionName();
			// 根据当前时间，查询时间在所属计划时间段中的计划，若存在多条，取最新(最近)的一条  （获取计划id）
			PromotePlan currentMarketingPlan = getMarketingPlanByRegion(regionName);
			
			String caiRoleName = customerRegionUser.getCustomerRoleName(); 
			// 如果是大区经理，那么就按对应的区域或总量即可  （根据计划id）
			log.info("countPromotePacksAndPoints caiRoleName:{}",caiRoleName);
			if(PROMOTE_CHANNELMANAGER.equals(caiRoleName))//大区经理不需要查看。。。。。not use
			{
				log.info("countPromotePacksAndPoints PROMOTE_CHANNELMANAGER");
				if(null!=currentMarketingPlan)
				{
					countPacks.setOpenShopGdPacksTotalCount(currentMarketingPlan.getOpenShopGdPacksCount());
					countPacks.setOpenShopPacksTotalCount(currentMarketingPlan.getOpenShopPacksCount());
					countPacks.setPointsTotalCount(currentMarketingPlan.getIntegralTotalCount());
					countPacks.setSeminarPacksTotalCount(currentMarketingPlan.getSeminarPacksCount());
					countPacks.setRoadShowActiviPacksTotalCount(currentMarketingPlan.getRoadShowActivitiesMaterialsCount());
					countPacks.setRoadShowConsumerPacksTotalCount(currentMarketingPlan.getRoadShowConsumerPacksCount());
					countPacks.setVenueMealTotal(currentMarketingPlan.getVenueMeal()==null?0:currentMarketingPlan.getVenueMeal());
					countPacks.setSeminarPacksHighTotalCount(currentMarketingPlan.getSeminarPacksHighCount()==null?0:currentMarketingPlan.getSeminarPacksHighCount());
					countPacks.setStorePacksTotalCount(currentMarketingPlan.getStorePacksCount()==null?0:currentMarketingPlan.getStorePacksCount());
					countPacks.setAdvertPacksTotalCount(currentMarketingPlan.getAdvertPacksCount()==null?0:currentMarketingPlan.getAdvertPacksCount());
					countPacks.setAgriculturePacksTotalCount(currentMarketingPlan.getAgriculturePacksCount()==null?0:currentMarketingPlan.getAgriculturePacksCount());
					countPacks.setTryPacksTotalCount(currentMarketingPlan.getTryPacksCount()==null?0:currentMarketingPlan.getTryPacksCount());
					countPacks = doHandleEnablePacks(countPacks,customerRegionUser.getUserId(),currentMarketingPlan.getId());
				}
				
			}else
			{
				if(null!=currentMarketingPlan)
				{
					// 如果是小区经理 或者销售，获取接收者是自己总数量即可 （根据计划id）
					//计算总共的
					countPacks = doHandleTotalPacks(countPacks,customerRegionUser.getUserId(),currentMarketingPlan.getId());
					// 计算可用的
					countPacks = doHandleEnablePacks(countPacks,customerRegionUser.getUserId(),currentMarketingPlan.getId());
				}
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("countPacks", countPacks);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("countPromotePacksAndPoints"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
			resultMap.put("countPacks", new CountPromoteDistributionPack());
		}
		return resultMap;
	}
	
	/**
	 * 统计总共的的礼包数量  分配到自己头上的，，即是总数
	 * <AUTHOR> 2018-6-17 下午3:41:29
	 * @param countPacks
	 * @param userId              接收者是自己
	 * @param marketingPlanId     源计划id
	 * @return
	 */
	private CountPromoteDistributionPack doHandleTotalPacks(
			CountPromoteDistributionPack countPacks, Long userId, Long marketingPlanId)throws Exception {
		log.info("doHandleTotalPacks countPacks:{},userId:{},marketingPlanId:{}",countPacks,userId,marketingPlanId);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("acceptUserId", userId);
		reqMap.put("marketinPlanId", marketingPlanId);
		reqMap.putAll(PromoteBizService.getStatus());//add by bo.liu 180702
		List<PromoteDistribution> lst = planDistributionMapper.countHasToatlPacksData(reqMap);
		if(null==lst || lst.isEmpty())//还未有分配到自己的头上
		{
			countPacks.setOpenShopGdPacksTotalCount(0);
			countPacks.setOpenShopPacksTotalCount(0);
			countPacks.setPointsTotalCount(0);
			countPacks.setSeminarPacksTotalCount(0);
			countPacks.setRoadShowActiviPacksTotalCount(0);
			countPacks.setRoadShowConsumerPacksTotalCount(0);
			countPacks.setVenueMealTotal(0.0);
			countPacks.setSeminarPacksHighTotalCount(0);
			countPacks.setStorePacksTotalCount(0);
			countPacks.setAdvertPacksTotalCount(0);
			countPacks.setAgriculturePacksTotalCount(0);
			countPacks.setTryPacksTotalCount(0);
			return countPacks;
		}
		
		/*
		PromoteDistribution tmpPromoteDistribution = lst.get(0);
	    countPacks.setOpenShopGdPacksTotalCount(tmpPromoteDistribution.getOpenShopGdPacksDistributionCount());
		countPacks.setOpenShopPacksTotalCount(tmpPromoteDistribution.getOpenShopPacksDistributionCount());
		countPacks.setPointsTotalCount(tmpPromoteDistribution.getPointsDistributionCount());
		countPacks.setSeminarPacksTotalCount(tmpPromoteDistribution.getSeminarPacksDistributionCount());
		countPacks.setRoadShowActiviPacksTotalCount(tmpPromoteDistribution.getRoadShowActiviPacksDistributionCount());
		countPacks.setRoadShowConsumerPacksTotalCount(tmpPromoteDistribution.getRoadShowConsumerPacksDistributionCount());*/
		int totalGdPacksTotal = 0;
		int totalOpenShopPacks = 0;
		int totalPoints = 0;
		int totalSeminarPacks = 0;
		int totalRoadShowActiviPacks = 0;
		int totalRoadShowConsumerPacks = 0;
		Double taotalVenueMeal = 0.0;
		int totalSeminarHighPacks = 0;
		int totalStorePacks = 0;
		int totalAdvertPacks = 0;
		int totalAgriculturePacks = 0;
		int totalTryPacks = 0;
		for(PromoteDistribution tmpPromoteDistribution:lst)
		{
			totalGdPacksTotal+=(tmpPromoteDistribution.getOpenShopGdPacksDistributionCount()==null?0:
				tmpPromoteDistribution.getOpenShopGdPacksDistributionCount());
			totalOpenShopPacks+=(tmpPromoteDistribution.getOpenShopPacksDistributionCount()==null?0:
				tmpPromoteDistribution.getOpenShopPacksDistributionCount());
			totalPoints+=(tmpPromoteDistribution.getPointsDistributionCount()==null?0:
				tmpPromoteDistribution.getPointsDistributionCount());
			totalSeminarPacks+=(tmpPromoteDistribution.getSeminarPacksDistributionCount()==null?0:
				tmpPromoteDistribution.getSeminarPacksDistributionCount());
			totalRoadShowActiviPacks+=(tmpPromoteDistribution.getRoadShowActiviPacksDistributionCount()==null?0:
				tmpPromoteDistribution.getRoadShowActiviPacksDistributionCount());
			totalRoadShowConsumerPacks+=(tmpPromoteDistribution.getRoadShowConsumerPacksDistributionCount()==null?0:
				tmpPromoteDistribution.getRoadShowConsumerPacksDistributionCount());
			taotalVenueMeal += (tmpPromoteDistribution.getVenueMealDistribution()==null?0:
				tmpPromoteDistribution.getVenueMealDistribution());
			totalSeminarHighPacks += (tmpPromoteDistribution.getSeminarHighPacksDistributionCount()==null?0:tmpPromoteDistribution.getSeminarHighPacksDistributionCount());
			totalStorePacks += (tmpPromoteDistribution.getStorePacksDistributionCount()==null?0:tmpPromoteDistribution.getStorePacksDistributionCount());
			totalAdvertPacks +=(tmpPromoteDistribution.getAdvertPacksDistributionCount()==null?0:tmpPromoteDistribution.getAdvertPacksDistributionCount());
			totalAgriculturePacks += (tmpPromoteDistribution.getAgriculturePacksDistributionCount()==null?0:tmpPromoteDistribution.getAgriculturePacksDistributionCount());
			totalTryPacks += (tmpPromoteDistribution.getTryPacksDistributionCount()==null?0:tmpPromoteDistribution.getTryPacksDistributionCount());
		}
		countPacks.setOpenShopGdPacksTotalCount(totalGdPacksTotal);
		countPacks.setOpenShopPacksTotalCount(totalOpenShopPacks);
		countPacks.setPointsTotalCount(totalPoints);
		countPacks.setSeminarPacksTotalCount(totalSeminarPacks);
		countPacks.setRoadShowActiviPacksTotalCount(totalRoadShowActiviPacks);
		countPacks.setRoadShowConsumerPacksTotalCount(totalRoadShowConsumerPacks);
		countPacks.setVenueMealTotal(taotalVenueMeal);
		countPacks.setSeminarPacksHighTotalCount(totalSeminarHighPacks);
		countPacks.setStorePacksTotalCount(totalStorePacks);
		countPacks.setAdvertPacksTotalCount(totalAdvertPacks);
		countPacks.setAgriculturePacksTotalCount(totalAgriculturePacks);
		countPacks.setTryPacksTotalCount(totalTryPacks);
		return countPacks;
	}

	/**
	 * 统计可用（分配）的礼包数量   分配者是自己的，，可以计算已经分配的总数、、可分配的总数
	 * <AUTHOR> 2018-6-17 下午3:15:05
	 * @param countPacks
	 * @param userId            分配者是自己
	 * @param marketingPlanId   源计划id
	 * @return
	 * @throws Exception
	 */
	private CountPromoteDistributionPack doHandleEnablePacks(CountPromoteDistributionPack countPacks,
			Long userId, Long marketingPlanId)throws Exception {
		log.info("doHandleEnablePacks countPacks:{},userId:{},marketingPlanId:{}",countPacks,userId,marketingPlanId);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("distributId", userId);
		reqMap.put("marketinPlanId", marketingPlanId);
		reqMap.putAll(PromoteBizService.getStatus());//add by bo.liu 180702
		PromoteDistribution returnDistribution = planDistributionMapper.countHasDistributPacksData(reqMap);
		if(null!=returnDistribution)
		{
			countPacks.setOpenShopGdPacksEnableCount(countPacks.getOpenShopGdPacksTotalCount()-returnDistribution.getOpenShopGdPacksDistributionCount());
			countPacks.setOpenShopPacksEnableCount(countPacks.getOpenShopPacksTotalCount()-returnDistribution.getOpenShopPacksDistributionCount());
			countPacks.setPointsEnableCount(countPacks.getPointsTotalCount()-returnDistribution.getPointsDistributionCount());
			countPacks.setSeminarPacksEnableCount(countPacks.getSeminarPacksTotalCount()-returnDistribution.getSeminarPacksDistributionCount());
			countPacks.setRoadShowActiviPacksEnableCount(countPacks.getRoadShowActiviPacksTotalCount()-returnDistribution.getRoadShowActiviPacksDistributionCount());
			countPacks.setRoadShowConsumerPacksEnableCount(countPacks.getRoadShowConsumerPacksTotalCount()-returnDistribution.getRoadShowConsumerPacksDistributionCount());
			countPacks.setVenueMealEnable(countPacks.getVenueMealTotal()-(returnDistribution.getVenueMealDistribution()==null?0:returnDistribution.getVenueMealDistribution()));
			countPacks.setSeminarPacksHighEnableCount(countPacks.getSeminarPacksHighTotalCount()-(returnDistribution.getSeminarHighPacksDistributionCount()==null?0:returnDistribution.getSeminarHighPacksDistributionCount()));
			countPacks.setStorePacksEnableCount(countPacks.getStorePacksTotalCount() - (returnDistribution.getStorePacksDistributionCount()==null?0:returnDistribution.getStorePacksDistributionCount()));
			countPacks.setAdvertPacksEnableCount(countPacks.getAdvertPacksTotalCount() - (returnDistribution.getAdvertPacksDistributionCount()==null?0:returnDistribution.getAdvertPacksDistributionCount()));
			countPacks.setAgriculturePacksEnableCount(countPacks.getAgriculturePacksTotalCount() - (returnDistribution.getAgriculturePacksDistributionCount()==null?0:returnDistribution.getAgriculturePacksDistributionCount()));
			countPacks.setTryPacksEnableCount(countPacks.getTryPacksTotalCount() - (returnDistribution.getTryPacksDistributionCount()==null?0:returnDistribution.getTryPacksDistributionCount()));
			
		}else
		{
			countPacks.setOpenShopGdPacksEnableCount(countPacks.getOpenShopGdPacksTotalCount());//-returnDistribution.getOpenShopGdPacksDistributionCount());
			countPacks.setOpenShopPacksEnableCount(countPacks.getOpenShopPacksTotalCount());//-returnDistribution.getOpenShopPacksDistributionCount());
			countPacks.setPointsEnableCount(countPacks.getPointsTotalCount());//-returnDistribution.getPointsDistributionCount());
			countPacks.setSeminarPacksEnableCount(countPacks.getSeminarPacksTotalCount());//-returnDistribution.getSeminarPacksDistributionCount());
			countPacks.setRoadShowActiviPacksEnableCount(countPacks.getRoadShowActiviPacksTotalCount());//-returnDistribution.getRoadShowActiviPacksDistributionCount());
			countPacks.setRoadShowConsumerPacksEnableCount(countPacks.getRoadShowConsumerPacksTotalCount());//-returnDistribution.getRoadShowConsumerPacksDistributionCount());
			countPacks.setVenueMealEnable(countPacks.getVenueMealTotal());
			countPacks.setSeminarPacksHighEnableCount(countPacks.getSeminarPacksHighTotalCount());
			countPacks.setStorePacksEnableCount(countPacks.getStorePacksTotalCount());
			countPacks.setAdvertPacksEnableCount(countPacks.getAdvertPacksTotalCount());
			countPacks.setAgriculturePacksEnableCount(countPacks.getAgriculturePacksTotalCount());
			countPacks.setTryPacksEnableCount(countPacks.getTryPacksTotalCount());
		}
		return countPacks;
	}


	private PromotePlan getMarketingPlanByRegion(String regionName)throws Exception {
		log.info("getMarketingPlanByRegion regionName：{}",regionName);
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("nowdate", new Date());
		reqMap.put("regionName",regionName);
		List<PromotePlan> lstPlan = planMapper.getPromotePlanByRegion(reqMap);
		if(null==lstPlan || lstPlan.isEmpty())
		{
			//throw new Exception("拉取数据失败,marketing还没有分配计划");
			return null;
		}
		return lstPlan.get(0);
	}


	@Override
	public Map<String, Object> getPromoteDistributionPreDataList(Long applyBatchId) {
		log.info("getPromoteDistributionPreDataList applyBatchId:{}",applyBatchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			if(null!=applyBatchId)
			{
				//如果有批次，证明，直接就是从暂存那里来的 直接查询分配表中对应的暂存数据到页面
				reqMap.put("applyBatchId",applyBatchId);
				List<PromoteDistributionPreData> lstPromotePreData = planDistributionMapper.getPromoteDistributionPreData1(reqMap);
				resultMap.put(RESULT_LST_KEY, lstPromotePreData);
				return resultMap;
				
			}
			//如果没有批次....先获取最新计划数据；
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Long sourcePlanId = customerRegionUser.getSourcePlanId();
			log.info("getPromoteDistributionPreDataList sourcePlanId:{}",sourcePlanId);
			//先判断分配表中是否有分配了的计划（当前登录者（作为分配者）、原计划id）
			reqMap.put("distributionUserId", customerRegionUser.getUserId());
			reqMap.put("sourcePlanId", sourcePlanId);
			/*
			 * delete by bo.liu 180705  不取最近的，每次新建都是从0开始
			 * List<PromoteDistributionPreData> lstPromotePreData2 = planDistributionMapper.getPromoteDistributionPreData1(reqMap);
			if(null!=lstPromotePreData2 &&  !lstPromotePreData2.isEmpty())
			{
				String status = lstPromotePreData2.get(0).getApproveStatus();
				
				// 如果存在，且状态为暂存的话，返回对应的暂存数据到页面
				if(PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS.equals(status) || PROMOTE_DISTRIBUTION_BATCH_UNAPPROVED_STATUS.equals(status))
				{
					resultMap.put(RESULT_LST_KEY, lstPromotePreData2);
					return resultMap;
				}
			}*/
			
			//if(null==lstPromotePreData2 || lstPromotePreData2.isEmpty())  //dele start by bo.liu 180702 
			//{
				List<PromoteDistributionPreData> lstPromotePreData3 = new ArrayList<PromoteDistributionPreData>();
				//大区经理给小区经理分配
				reqMap.put("cai", customerRegionUser.getCai());
				reqMap.put("queryStartDate", DateUtils.getDateStr(DateUtils.getFirstDayOfYear(DateUtils.addYears(DateUtils.getCurrentDate(),-1)), "yyyy-MM-dd"));
				reqMap.put("queryEndDate", DateUtils.getDateStr(DateUtils.getFirstDayOfYear(new Date()), "yyyy-MM-dd"));
				if(PROMOTE_CHANNELMANAGER.equals(customerRegionUser.getCustomerRoleName()))
				{
					lstPromotePreData3 = planDistributionMapper.getPromoteDistributionPreData2(reqMap);
				}else if(PROMOTE_SUPERVISOR.equals(customerRegionUser.getCustomerRoleName()))
				{
					lstPromotePreData3 = planDistributionMapper.getPromoteDistributionPreData3(reqMap);
				}
				
				List<PromoteDistributionPreData> returnLstPromotePreData = new ArrayList<PromoteDistributionPreData>();
				if(null!=lstPromotePreData3 && !lstPromotePreData3.isEmpty())
				{
					for(PromoteDistributionPreData tmp: lstPromotePreData3)
					{
						if(null!=tmp.getAcceptUserId() && tmp.getUserStatus()!=0)
						{
							returnLstPromotePreData.add(tmp);
						}
					}
				}
				resultMap.put(RESULT_LST_KEY, returnLstPromotePreData);
				return resultMap;
			//}
				
				
			//delete by bo.liu 180702	
			/*String status = lstPromotePreData2.get(0).getApproveStatus();
			
			// 如果存在，且状态为暂存的话，返回对应的暂存数据到页面
			if(PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS.equals(status) || PROMOTE_DISTRIBUTION_BATCH_UNAPPROVED_STATUS.equals(status))
			{
				resultMap.put(RESULT_LST_KEY, lstPromotePreData2);
				return resultMap;
			}*/
			
			//如果存在，且状态为已提交的话，直接返回给提示（证明已经提交了，不需要重新分配）
			//if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status) || PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS.equals(status))
			//{
				
				
				//dele start by bo.liu 180702 
				//resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				//resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_HAS_DISTRIBUTION_MSG);
				//resultMap.put("errorCode", "0");
				//dele end
				//return resultMap;
			//}
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPromoteDistributionPreDataList"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	@Override
	public Map<String, Object> getCurrentUserCaiByCustomerReal() {
		// not use  用户表关联dw_customer_region_sales_supervisor_rel这个表，获取当前用户cai类型   是大区经理，小区经理，
		//转 planService.getCustomerRegionUser();
		return null;
	}
	
	
	@Override
	public Map<String, Object> getPromoteDistributionApplyList(
			String queryStatusType,String queryFiled) {
		log.info("getPromoteDistributionApplyList queryStatusType:{},queryFiled:{}",queryStatusType,queryFiled);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			// 获取当前用户信息
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			String customerRoleName = customerRegionUser.getCustomerRoleName(); 
			log.info("getPromoteDistributionApplyList customerRoleName:{},distributionUserId:{}",customerRoleName,customerRegionUser.getUserId());
			//大区经理
			if(PROMOTE_CHANNELMANAGER.equals(customerRoleName))
			{
				
				resultMap = doQueryChannelManagerPromoteDistributionLst(queryStatusType,customerRegionUser.getUserId(),queryFiled);
				return resultMap;
			}
			
			//小区经理
			if(PROMOTE_SUPERVISOR.equals(customerRoleName))
			{
				resultMap = doQuerySupervisorPromoteDistributionLst(queryStatusType,customerRegionUser.getUserId(),queryFiled);
				return resultMap;
			}
			
			//marketing   剩下的都是marketing
			//if(PROMOTE_MARKETING.equals(customerRoleName))
			{
				resultMap = doQueryMarketingPromoteDistributionLst(queryStatusType,queryFiled);
				return resultMap;
			}
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPromoteDistributionApplyList"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	
	private Map<String, Object> doQueryMarketingPromoteDistributionLst(
			String queryStatusType,String queryFiled)throws Exception {
		Map<String,Object> returnMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("queryFiled", queryFiled);
		if(queryStatusType.equals(PROMOTE_DISTRIBUTION_INAPPROVAL_QUERYTYPE))//审核中
		{
			reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
		}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_APPROVED_QUERYTYPE))//待审核
		{
			reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
		}
		List<PromoteApplicationBatch> lst = promoteApplyBatchMapper.getPromoteApplayLstByMap(reqMap);
		returnMap.put(RESULT_LST_KEY, lst);
		returnMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return returnMap;
	}

	private Map<String, Object> doQuerySupervisorPromoteDistributionLst(
			String queryStatusType,Long distributionId,String queryFiled)throws Exception {
		Map<String,Object> returnMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("distruibutionUserId", distributionId);
		reqMap.put("queryFiled", queryFiled);
		if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PENDING_QUERYTYPE))//待处理，状态为暂存，反驳的
		{
			reqMap.put("saveStatus", PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS);
			reqMap.put("unapprovedStatus", PROMOTE_DISTRIBUTION_BATCH_UNAPPROVED_STATUS);
		}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_INAPPROVAL_QUERYTYPE))//审核中
		{
			reqMap.put("approvingStatus", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
			
		}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_APPROVED_QUERYTYPE))//已审核
		{
			reqMap.put("approvedStatus", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
		}
		List<PromoteApplicationBatch> lst = promoteApplyBatchMapper.getPromoteApplayLstForSupervisorByMap(reqMap);
		returnMap.put(RESULT_LST_KEY, lst);
		returnMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return returnMap;
	}

	private Map<String, Object> doQueryChannelManagerPromoteDistributionLst(
			String queryStatusType,Long distributionId,String queryFiled)throws Exception {
		Map<String,Object> returnMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("distruibutionUserId", distributionId);
		reqMap.put("queryFiled", queryFiled);
		if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PENDING_QUERYTYPE))//暂存
		{
			reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS);
		}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PROCESSED_QUERYTYPE))//已提交
		{
			reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS);
		}
		List<PromoteApplicationBatch> lst = promoteApplyBatchMapper.getPromoteApplayLstByMap(reqMap);
		returnMap.put(RESULT_LST_KEY, lst);
		returnMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return returnMap;
	}

	@Override
	public Map<String, Object> getPromoteDistributionDetail(Long applyBatchId,
			Long parentDistributionId,boolean isOpen) {
		log.info("getPromoteDistributionDetail applyBatchId:{},parentDistributionId:{}",applyBatchId,parentDistributionId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			Map<String,Object> reqMap = new HashMap<String,Object>();
			// 获取当前用户信息
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			String customerRoleName = customerRegionUser.getCustomerRoleName(); 
			if(!PROMOTE_MARKETING.equals(customerRoleName))
			{
				//非marketing，，意思是大区经理，小区经理
				reqMap.put("applyBatchId", applyBatchId);
			}else
			{
				//marketing查看.....
				if(!isOpen)
				{
					
					//第一层传递参数的时候，传递parentDistributionId   获取分配id为，，其父级的id
					reqMap.put("parentDistributionId", parentDistributionId);
					
				}else
				{
					//第二层传递   批次applyBatchId
					reqMap.put("applyBatchId", applyBatchId);
				}
			}
			List<PromoteDistribution> lst = planDistributionMapper.getPromoteDistributionDetailLst(reqMap);
			resultMap.put(RESULT_LST_KEY, lst);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPromoteDistributionDetail"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertOrUpdatePromoteDistribution(
			List<PromoteDistribution> lst,Long applyBatchId, String status) {
		log.info("insertOrUpdatePromoteDistribution lst:{},applyBatchId:{},status:{}",lst,applyBatchId,status);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			// 获取当前用户信息
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("applyBatchId", applyBatchId);
			//第一次录入系统，直接插入，，，可能是暂存  也可能是提交
			if(null==applyBatchId)
			{
				// 先插入申请批次
				applyBatchId = doInsertPromoteApplication(customerRegionUser,status);
				doInsertPromoteDistribution(lst,customerRegionUser,applyBatchId);
				resultMap.put("applyBatchId", applyBatchId);
				return resultMap;
			}
			
			//批量更新   也可能是暂存，也可能是提交
			planDistributionMapper.batchUpdatePromoteDistribution(lst);
			//更新申请表中
			PromoteApplicationBatch updateApplicationBatch = new PromoteApplicationBatch();
			updateApplicationBatch.setBatchid(applyBatchId);
			updateApplicationBatch.setApproveStatus(status);
			if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))//提交
			{
				if(PROMOTE_SUPERVISOR.equals(customerRegionUser.getCustomerRoleName()))//小区经理
				{
					//add by bo.liu 180703 start
					//根据批次获取versionFlag 
					PromoteApplicationBatch tmpApplicationBatch = promoteApplyBatchMapper.selectByPrimaryKey(applyBatchId);
					if(null==tmpApplicationBatch)
					{
						throw new Exception("没找到对应的活动的批次");
					}
					String versionFlag = tmpApplicationBatch.getVersionFlag()==null?"0":tmpApplicationBatch.getVersionFlag();
					updateApplicationBatch.setApproveStatus(PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
					updateApplicationBatch.setVersionFlag(""+(Integer.parseInt(versionFlag)+1));//版本
					updateApplicationBatch.setCurrentStep(PROMOTE_MARKETING);//流向到marketing
					promoteBizService.doSndMessageForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_MARKETING_TIP+PROMOTE_DISTRIBUTION_APPROVE);
					promoteBizService.doSndMailForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_MARKETING_TIP+PROMOTE_DISTRIBUTION_APPROVE);
					//录入一条日志到审批表中
					String parm1 = "首次提交";
					String parm2 = ".大区经理首次提交分配计划申请";
					if(!"1".equals(versionFlag))
					{
						parm1 = "再次提交";
						parm2 = ".大区经理再次提交分配计划申请";
					}
					approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, applyBatchId, status, parm1
							,versionFlag+parm2);
					//add by bo.liu 180703 end
					updateApplicationBatch.setApproveStatus(PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);//审批中
					updateApplicationBatch.setRemark("大区经理最近更新:"+DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_TIME_PATTERN));
				}
			}
			promoteApplyBatchMapper.updateByPrimaryKeySelective(updateApplicationBatch);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("insertOrUpdatePromoteDistribution"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}

	private void doInsertPromoteDistribution(List<PromoteDistribution> lst,
			CustomerRegionUser customerRegionUser, Long applyBatchId)throws Exception {
		log.info("doInsertPromoteDistribution lst:{},applyBatchId:{},customerRegionUser:{}",lst,applyBatchId,customerRegionUser);
		//根据接收者，原计划id 查询分配的id
		String customerRoleName = customerRegionUser.getCustomerRoleName(); 
		Long parentDistributionId = null;
		
		//delete by bo.liu 180702 适应多个分配
		/*if(!PROMOTE_CHANNELMANAGER.equals(customerRoleName))//非大区经理
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("acceptId", customerRegionUser.getUserId());//接收者
			reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
			parentDistributionId = planDistributionMapper.getParentDistributionId(reqMap); //TODO...隐患，，存在多条，，，
		}*/
		for(PromoteDistribution promoteDistribution : lst)
		{
			promoteDistribution.setDistributionUserName(customerRegionUser.getChName());//或者loginName
			promoteDistribution.setDistributionUserId(customerRegionUser.getUserId());
			promoteDistribution.setCreateTime(new Date());
			promoteDistribution.setReleaseTime(new Date());
			promoteDistribution.setApplyBatchId(applyBatchId);
			promoteDistribution.setSourcePlanId(customerRegionUser.getSourcePlanId());
			//delete by bo.liu 180702 适应多个分配
			/*if(!PROMOTE_CHANNELMANAGER.equals(customerRoleName))//非大区经理
			{
				promoteDistribution.setParentDistributionId(parentDistributionId);
			}*/
			
		}
		//批量录入
		planDistributionMapper.insertBatchPromoteDistribution(lst);
	}

	private Long doInsertPromoteApplication(
			CustomerRegionUser customerRegionUser,String status)throws Exception {
		log.info("doInsertPromoteApplication status:{},customerRegionUser:{}",status,customerRegionUser);
		String customerRoleName = customerRegionUser.getCustomerRoleName();
		PromoteApplicationBatch applyPromoteDistribution = new PromoteApplicationBatch();
		applyPromoteDistribution.setApproveStatus(status);//可能是暂存或提交
		if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))//提交
		{
			if(PROMOTE_SUPERVISOR.equals(customerRoleName))//小区经理
			{
				applyPromoteDistribution.setApproveStatus(PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);//审批中
				
				//ADD BY BO.LIU 180703 start
				applyPromoteDistribution.setVersionFlag("2");//版本2
				applyPromoteDistribution.setCurrentStep(PROMOTE_MARKETING);//流向到marketing
				promoteBizService.doSndMessageForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_MARKETING_TIP+PROMOTE_DISTRIBUTION_APPROVE);
				promoteBizService.doSndMailForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, PROMOTE_MARKETING_TIP+PROMOTE_DISTRIBUTION_APPROVE);
				//ADD BY BO.LIU 180703 end
			}
			
		}
		
		//ADD BY BO.LIU 180703 start
		else if(PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS.equals(status))
		{
			if(PROMOTE_SUPERVISOR.equals(customerRoleName))//小区经理
			{
				applyPromoteDistribution.setVersionFlag("1");//版本1
				applyPromoteDistribution.setCurrentStep(PROMOTE_SUPERVISOR);//暂存流向小区经理  提交
			}
		}
		//ADD BY BO.LIU 180703 end
		
		String title = "";
		if(PROMOTE_SUPERVISOR.equals(customerRoleName))//小区经理
		{
			
			title = "大区经理"+customerRegionUser.getChName()+"给销售分配计划";
			
		}else
		{
			title = "渠道经理"+customerRegionUser.getChName()+"给大区经理分配计划";
		}
		applyPromoteDistribution.setBatchTitle(DateUtils.getDateStr(new Date())+title+PROMOTE_DISTRIBUTION_TITLE);
		applyPromoteDistribution.setCreateTime(new Date());
		applyPromoteDistribution.setProkey(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
		applyPromoteDistribution.setVersionFlag("1");//初始版本从1开始
		applyPromoteDistribution.setCreatorId(customerRegionUser.getUserId());
		promoteApplyBatchMapper.insertSelective(applyPromoteDistribution);
		
		if(PROMOTE_SUPERVISOR.equals(customerRoleName))//小区经理  录入审批历史
		{

			//ADD BY BO.LIU 180703 start
			if(PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS.equals(status))//提交的话
			{
				approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION, applyPromoteDistribution.getBatchid(), status, "首次提交"
						,"1.大区经理提交计划分配申请");
			}
			//ADD BY BO.LIU 180703 end
		}
		
		return applyPromoteDistribution.getBatchid();
	}
	
	
	@Override
	public Map<String, Object> getPromoteDistributionDetail2(String queryStatusType,
			Long applyBatchId, boolean isOpen,String queryFiled) {
		log.info("getPromoteDistributionDetail parentDistributionId:{},isOpen:{}",applyBatchId,isOpen);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			Map<String,Object> reqMap = new HashMap<String,Object>();
			// 获取当前用户信息
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			String customerRoleName = customerRegionUser.getCustomerRoleName(); 
			
			reqMap.put("currentSourcePlanId", customerRegionUser.getSourcePlanId());
			reqMap.put("queryFiled", queryFiled);
			reqMap.put("applyKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
			List<PromoteDistribution> lst = new ArrayList<PromoteDistribution>();
			if(PROMOTE_SUPERVISOR.equals(customerRoleName))
			{
				//小区经理  展开一层。。。。TODO 修改的时候怎么处理？？？？？
				reqMap.put("querySupervisor", "true");
				if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PENDING_QUERYTYPE))//待处理，状态为暂存，反驳的
				{
					reqMap.put("saveStatus", PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS);
					reqMap.put("unapprovedStatus", PROMOTE_DISTRIBUTION_BATCH_UNAPPROVED_STATUS);
				}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_INAPPROVAL_QUERYTYPE))//审核中
				{
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
					
				}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_APPROVED_QUERYTYPE))//已审核
				{
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
				}
				reqMap.put("distributionId", customerRegionUser.getUserId());
				lst =  doGetForSupervisorOrChannelManager(customerRegionUser,reqMap,isOpen,applyBatchId);
				
			}else if(PROMOTE_MARKETING.equals(customerRoleName))
			{
				//marketing
				reqMap.put("queryMarketing", "true");
				//需要增加区域过滤
				reqMap.put("regionName", customerRegionUser.getRegionName());
				if(queryStatusType.equals(PROMOTE_DISTRIBUTION_INAPPROVAL_QUERYTYPE))//审核中
				{
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVING_STATUS);
				}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_APPROVED_QUERYTYPE))//已审核   包含状态为驳回的
				{
					//TODO.....
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_APPROVED_STATUS);
				}
				lst = doGetForMarketing(reqMap,isOpen,applyBatchId);
				
			}else
			{
				//大区经理
				reqMap.put("queryChannelManager", "true");
				if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PENDING_QUERYTYPE))//暂存
				{
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_SAVE_STATUS);
				}else if(queryStatusType.equals(PROMOTE_DISTRIBUTION_PROCESSED_QUERYTYPE))//已提交
				{
					reqMap.put("status", PROMOTE_DISTRIBUTION_BATCH_SUBMIT_STATUS);
				}
				reqMap.put("distributionId", customerRegionUser.getUserId());
				lst = doGetForSupervisorOrChannelManager(customerRegionUser,reqMap,isOpen,applyBatchId);
				
			}
			//List<PromoteDistribution> lst = planDistributionMapper.getPromoteDistributionDetailLst2(reqMap);
			resultMap.put(RESULT_LST_KEY, lst);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPromoteDistributionDetail"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}

	/*private List<PromoteDistribution> doGetForChannelManager(
			Map<String, Object> reqMap, CustomerRegionUser customerRegionUser,
			boolean isOpen, Long applyBatchId)throws Exception {
		List<PromoteDistribution> tmpLst = new ArrayList<PromoteDistribution>();
		
		if(isOpen)
		{
			
			//第二层
			tmpLst = 
		}else
		{
			//第二层
			reqMap.put("parentDistributionId", parentDistributionId);
		}
		return tmpLst;
	}*/

	private List<PromoteDistribution> doGetForMarketing(
			Map<String, Object> reqMap, boolean isOpen,Long applyBatchId)throws Exception {
		List<PromoteDistribution> tmpLst = new ArrayList<PromoteDistribution>();
		if(isOpen)
		{
			//第二层传递参数的时候，传递parentDistributionId   获取分配id为，，其父级的id
			reqMap.put("applyBatchId", applyBatchId);
			tmpLst = planDistributionMapper.getPromoteDistributionDetailLst2(reqMap);
			return tmpLst;
		}else
		{
			//tmpLst = planDistributionMapper.getPromoteDistributionDetailLstForMarketingNotOpen(reqMap);
			tmpLst = planDistributionMapper.getPromoteDistributionDetailLstForMarketingNotOpen2(reqMap);
			String lastBatchId = "";
			String currentBatchId = "";
			List<PromoteDistribution> lst2 = new ArrayList<PromoteDistribution>();
			PromoteDistribution  promoteDistribution = null;
			int totalRoadShowActivity = 0;
			int totalRoadShowConsumer = 0;
			int totalRDOpen = 0;
			int totalGDOpen = 0;
			int totalYTHOpen = 0;
			int totalPoints = 0;
			int totalLastAnnualSales = 0;
			Double totalVenueMeal = 0.0;
			int totalYZYTH = 0;
			int totalDZ = 0;
			int totalHWGG = 0;
			int totalNK = 0;
			int totalSY = 0;
			
			for(PromoteDistribution tmp:tmpLst)
			{
				Long applyBatchId1 = tmp.getApplyBatchId();
				currentBatchId = ""+applyBatchId1;
				if(!lastBatchId.equals(currentBatchId))
				{
					totalRoadShowActivity = 0;
					totalRoadShowConsumer = 0;
					totalRDOpen = 0;
					totalGDOpen = 0;
					totalYTHOpen = 0;
					totalPoints = 0;
					totalLastAnnualSales = 0;
					totalVenueMeal = 0.0;
					totalYZYTH = 0;
					totalDZ = 0;
					totalHWGG = 0;
					totalNK = 0;
					totalSY = 0;
					promoteDistribution = new PromoteDistribution();
					promoteDistribution.setAcceptUserName(tmp.getAcceptUserName());
					promoteDistribution.setCreateTime(tmp.getCreateTime());
					promoteDistribution.setApplyBatchId(applyBatchId1);
					promoteDistribution.setApplyBatchStatus(tmp.getApplyBatchStatus());
					promoteDistribution.setVersionFlag(tmp.getVersionFlag());
					lst2.add(promoteDistribution);
				}
				totalRoadShowActivity+=tmp.getRoadShowActiviPacksDistributionCount();
				totalRoadShowConsumer+=tmp.getRoadShowConsumerPacksDistributionCount();
				totalRDOpen+=tmp.getOpenShopPacksDistributionCount();
				totalGDOpen+=tmp.getOpenShopGdPacksDistributionCount();
				totalYTHOpen+=tmp.getSeminarPacksDistributionCount();
				totalPoints+=tmp.getPointsDistributionCount();
				totalLastAnnualSales+=tmp.getLastAnnualSales();
				totalVenueMeal+=tmp.getVenueMealDistribution()==null?0:tmp.getVenueMealDistribution();
				totalYZYTH += tmp.getSeminarHighPacksDistributionCount()==null?0:tmp.getSeminarHighPacksDistributionCount();
				totalDZ += tmp.getStorePacksDistributionCount()==null?0:tmp.getStorePacksDistributionCount();
				totalHWGG += tmp.getAdvertPacksDistributionCount()==null?0:tmp.getAdvertPacksDistributionCount();
				totalNK += tmp.getAgriculturePacksDistributionCount()==null?0:tmp.getAgriculturePacksDistributionCount();
				totalSY += tmp.getTryPacksDistributionCount()==null?0:tmp.getTryPacksDistributionCount();
				
				promoteDistribution.setOpenShopGdPacksDistributionCount(totalGDOpen);
				promoteDistribution.setOpenShopPacksDistributionCount(totalRDOpen);
				promoteDistribution.setRoadShowActiviPacksDistributionCount(totalRoadShowActivity);
				promoteDistribution.setRoadShowConsumerPacksDistributionCount(totalRoadShowConsumer);
				promoteDistribution.setSeminarPacksDistributionCount(totalYTHOpen);
				promoteDistribution.setPointsDistributionCount(totalPoints);
				promoteDistribution.setLastAnnualSales(totalLastAnnualSales);
				promoteDistribution.setVenueMealDistribution(totalVenueMeal);
				promoteDistribution.setSeminarHighPacksDistributionCount(totalYZYTH);
				promoteDistribution.setStorePacksDistributionCount(totalDZ);
				promoteDistribution.setAdvertPacksDistributionCount(totalHWGG);
				promoteDistribution.setAgriculturePacksDistributionCount(totalNK);
				promoteDistribution.setTryPacksDistributionCount(totalSY);
				lastBatchId = currentBatchId;
			}
			return lst2;
		}
		
	}

	private List<PromoteDistribution> doGetForSupervisorOrChannelManager(CustomerRegionUser customerRegionUser,
			Map<String, Object> reqMap, boolean isOpen,Long applyBatchId)throws Exception {
		List<PromoteDistribution> lst = new ArrayList<PromoteDistribution>();
		if(isOpen)
		{
			reqMap.put("applyBatchId", applyBatchId);
			lst = planDistributionMapper.getPromoteDistributionDetailLst2(reqMap);
			return lst;
		}else
		{
			lst = planDistributionMapper.getPromoteDistributionDetailLst2(reqMap);
			String lastBatchId = "";
			String currentBatchId = "";
			List<PromoteDistribution> lst2 = new ArrayList<PromoteDistribution>();
			PromoteDistribution  promoteDistribution = null;
			int totalRoadShowActivity = 0;
			int totalRoadShowConsumer = 0;
			int totalRDOpen = 0;
			int totalGDOpen = 0;
			int totalYTHOpen = 0;
			int totalPoints = 0;
			int totalLastAnnualSales = 0;
			Double totalVenueMeal = 0.0;
			int totalYZYTH = 0;
			int totalDZ = 0;
			int totalHWGG = 0;
			int totalNK = 0;
			int totalSY = 0;
			
			for(PromoteDistribution tmp:lst)
			{
				Long applyBatchId1 = tmp.getApplyBatchId();
				currentBatchId = ""+applyBatchId1;
				if(!lastBatchId.equals(currentBatchId))
				{
					totalRoadShowActivity = 0;
					totalRoadShowConsumer = 0;
					totalRDOpen = 0;
					totalGDOpen = 0;
					totalYTHOpen = 0;
					totalPoints = 0;
					totalLastAnnualSales = 0;
					totalVenueMeal = 0.0;
					totalYZYTH = 0;
					totalDZ = 0;
					totalHWGG = 0;
					totalNK = 0;
					totalSY = 0;
					promoteDistribution = new PromoteDistribution();
					promoteDistribution.setAcceptUserName(customerRegionUser.getChName());
					promoteDistribution.setCreateTime(tmp.getCreateTime());
					promoteDistribution.setApplyBatchId(applyBatchId1);
					promoteDistribution.setApplyBatchStatus(tmp.getApplyBatchStatus());
					promoteDistribution.setVersionFlag(tmp.getVersionFlag());
					lst2.add(promoteDistribution);
				}
				totalRoadShowActivity+=tmp.getRoadShowActiviPacksDistributionCount();
				totalRoadShowConsumer+=tmp.getRoadShowConsumerPacksDistributionCount();
				totalRDOpen+=tmp.getOpenShopPacksDistributionCount();
				totalGDOpen+=tmp.getOpenShopGdPacksDistributionCount();
				totalYTHOpen+=tmp.getSeminarPacksDistributionCount();
				totalPoints+=tmp.getPointsDistributionCount();
				totalLastAnnualSales+=tmp.getLastAnnualSales();
				totalVenueMeal+=tmp.getVenueMealDistribution()==null?0:tmp.getVenueMealDistribution();
				totalYZYTH += tmp.getSeminarHighPacksDistributionCount()==null?0:tmp.getSeminarHighPacksDistributionCount();
				totalDZ += tmp.getStorePacksDistributionCount()==null?0:tmp.getStorePacksDistributionCount();
				totalHWGG += tmp.getAdvertPacksDistributionCount()==null?0:tmp.getAdvertPacksDistributionCount();
				totalNK += tmp.getAgriculturePacksDistributionCount()==null?0:tmp.getAgriculturePacksDistributionCount();
				totalSY += tmp.getTryPacksDistributionCount()==null?0:tmp.getTryPacksDistributionCount();
				
				promoteDistribution.setOpenShopGdPacksDistributionCount(totalGDOpen);
				promoteDistribution.setOpenShopPacksDistributionCount(totalRDOpen);
				promoteDistribution.setRoadShowActiviPacksDistributionCount(totalRoadShowActivity);
				promoteDistribution.setRoadShowConsumerPacksDistributionCount(totalRoadShowConsumer);
				promoteDistribution.setSeminarPacksDistributionCount(totalYTHOpen);
				promoteDistribution.setPointsDistributionCount(totalPoints);
				promoteDistribution.setLastAnnualSales(totalLastAnnualSales);
				promoteDistribution.setVenueMealDistribution(totalVenueMeal);
				promoteDistribution.setSeminarHighPacksDistributionCount(totalYZYTH);
				promoteDistribution.setStorePacksDistributionCount(totalDZ);
				promoteDistribution.setAdvertPacksDistributionCount(totalHWGG);
				promoteDistribution.setAgriculturePacksDistributionCount(totalNK);
				promoteDistribution.setTryPacksDistributionCount(totalSY);
				lastBatchId = currentBatchId;
				
			}
			return lst2;
		}
	}
	
	
	@Override
	public Map<String, Object> countPromoteDistributionInfo() {
		log.info("countPromoteDistributionInfo====");
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			String customerRoleName = customerRegionUser.getCustomerRoleName(); 
			Map<String,Object> reqMap = new HashMap<String,Object>();
			CountPromoteHandleInfo countPromoteHandleInfo = new CountPromoteHandleInfo();
			//marketing
			if(PROMOTE_MARKETING.equals(customerRoleName))
			{	
				reqMap.put("proKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
				reqMap.put("currentStep", PROMOTE_MARKETING);
				//待审核
				reqMap.put("status", PromoteStatusEnum.STATUS_INPROVE.getStatusCode());
				
				int countInApprove = planDistributionMapper.countPromoteDistributionInfoForMarketing(reqMap);
				countPromoteHandleInfo.setInapprovingCount(countInApprove);
				//已审核
				reqMap.put("status", PromoteStatusEnum.STATUS_APPROVED.getStatusCode());
				int countApproved = planDistributionMapper.countPromoteDistributionInfoForMarketing(reqMap);
				countPromoteHandleInfo.setApprovedCount(countApproved);
			}else if(PROMOTE_CHANNELMANAGER.equals(customerRoleName))//大区经理
			{
				reqMap.put("proKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
				reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
				reqMap.put("userId", customerRegionUser.getUserId());
				//待处理
				reqMap.put("status", PromoteStatusEnum.STATUS_SAVE.getStatusCode());
				
				int countSaved = planDistributionMapper.countPromoteDistributionInfoForChannelManager(reqMap);
				countPromoteHandleInfo.setPendingCount(countSaved);
				//已处理
				reqMap.put("status", PromoteStatusEnum.STATUS_SUBMIT.getStatusCode());
				int countSubmited = planDistributionMapper.countPromoteDistributionInfoForChannelManager(reqMap);
				countPromoteHandleInfo.setProcessedCount(countSubmited);
				
				
			}else if(PROMOTE_SUPERVISOR.equals(customerRoleName))//小区
			{
				
				
				
			}
			resultMap.put("countPromoteHandleInfo", countPromoteHandleInfo);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("countPromoteDistributionInfo"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	
	@Override
	public Map<String, Object> statisticsPacksUsage(String promoteUserType) {
		log.info("statisticsPacksUsage promoteUserType:{}",promoteUserType);
		Map<String, Object> resultMap = new HashMap<String,Object>();
		try
		{
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			CountPacksUsageForDistribution countPacksUsageForDistribution = new CountPacksUsageForDistribution();
			if(PROMOTE_MARKETING.equals(promoteUserType))
			{
				//获取所有的包（南区、北区）
				List<PromotePlan> lstPlans = planMapper.getCurrentPlans(new HashMap<String,Object>());
				for(PromotePlan tmpPlan: lstPlans)
				{
					int totalDCJF = tmpPlan.getIntegralTotalCount()==null?0:tmpPlan.getIntegralTotalCount();
					int totalGDLB = tmpPlan.getOpenShopGdPacksCount()==null?0:tmpPlan.getOpenShopGdPacksCount();
					int totalRDLB = tmpPlan.getOpenShopPacksCount()==null?0:tmpPlan.getOpenShopPacksCount();
					int totalLYWLB = tmpPlan.getRoadShowActivitiesMaterialsCount()==null?0:tmpPlan.getRoadShowActivitiesMaterialsCount();
					int totalXFZHDBCB = tmpPlan.getRoadShowConsumerPacksCount()==null?0:tmpPlan.getRoadShowConsumerPacksCount();
					int totalTGHWLB = tmpPlan.getSeminarPacksCount()==null?0:tmpPlan.getSeminarPacksCount();
					Double totalVenueMeal = tmpPlan.getVenueMeal()==null?0:tmpPlan.getVenueMeal();
					int totalYZYTHLB = tmpPlan.getSeminarPacksHighCount()==null?0:tmpPlan.getSeminarPacksHighCount();
					int totalDZLB = tmpPlan.getStorePacksCount()==null?0:tmpPlan.getStorePacksCount();
					int totalHWGGLB = tmpPlan.getAdvertPacksCount()==null?0:tmpPlan.getAdvertPacksCount();
					int totalNKLB = tmpPlan.getAgriculturePacksCount()==null?0:tmpPlan.getAgriculturePacksCount();
					int totalSYLB = tmpPlan.getTryPacksCount()==null?0:tmpPlan.getTryPacksCount();
					if(REGION_SOUTH.equals(tmpPlan.getRegion()))
					{
						countPacksUsageForDistribution.setSouthAreaTotalDCJFACT(totalDCJF);
						countPacksUsageForDistribution.setSouthAreaTotalGDLB(totalGDLB);
						countPacksUsageForDistribution.setSouthAreaTotalLYWLB(totalLYWLB);
						countPacksUsageForDistribution.setSouthAreaTotalRDLB(totalRDLB);
						countPacksUsageForDistribution.setSouthAreaTotalTGHWLB(totalTGHWLB);
						countPacksUsageForDistribution.setSouthAreaTotalXFZHDBCB(totalXFZHDBCB);
						countPacksUsageForDistribution.setSouthAreaSourcePlanId(tmpPlan.getId());
						countPacksUsageForDistribution.setSouthTotalVenueMeal(totalVenueMeal);
						countPacksUsageForDistribution.setSouthAreaTotalYZYTHLB(totalYZYTHLB);
						countPacksUsageForDistribution.setSouthAreaTotalDZLB(totalDZLB);
						countPacksUsageForDistribution.setSouthAreaTotalHWGGLB(totalHWGGLB);
						countPacksUsageForDistribution.setSouthAreaTotalNKLB(totalNKLB);
						countPacksUsageForDistribution.setSouthAreaTotalSYLB(totalSYLB);
					}else
					{
						countPacksUsageForDistribution.setNorthAreaTotalDCJFACT(totalDCJF);
						countPacksUsageForDistribution.setNorthAreaTotalGDLB(totalGDLB);
						countPacksUsageForDistribution.setNorthAreaTotalLYWLB(totalLYWLB);
						countPacksUsageForDistribution.setNorthAreaTotalRDLB(totalRDLB);
						countPacksUsageForDistribution.setNorthAreaTotalTGHWLB(totalTGHWLB);
						countPacksUsageForDistribution.setNorthAreaTotalXFZHDBCB(totalXFZHDBCB);
						countPacksUsageForDistribution.setNorthAreaSourcePlanId(tmpPlan.getId());
						countPacksUsageForDistribution.setNorthTotalVenueMeal(totalVenueMeal);
						countPacksUsageForDistribution.setNorthAreaTotalYZYTHLB(totalYZYTHLB);
						countPacksUsageForDistribution.setNorthAreaTotalDZLB(totalDZLB);
						countPacksUsageForDistribution.setNorthAreaTotalHWGGLB(totalHWGGLB);
						countPacksUsageForDistribution.setNorthAreaTotalNKLB(totalNKLB);
						countPacksUsageForDistribution.setNorthAreaTotalSYLB(totalSYLB);
					}
				}
				
				//南区剩余可用
				countPacksUsageForDistribution = doGetSouthAreaUsedPacks(countPacksUsageForDistribution);
				
				
				//北区剩余可用
				countPacksUsageForDistribution = doGetNorthAreaUsedPacks(countPacksUsageForDistribution);
			}
			resultMap.put("countPacksUsageForDistribution", countPacksUsageForDistribution);
			resultMap.put("regionName", customerRegionUser.getRegionName());
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
		}catch (Exception e) {
			e.printStackTrace();
			log.error("statisticsPacksUsage"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}

	private CountPacksUsageForDistribution doGetNorthAreaUsedPacks(
			CountPacksUsageForDistribution countPacksUsageForDistribution)throws Exception {
		log.info("doGetNorthAreaUsedPacks countPacksUsageForDistribution:{}",countPacksUsageForDistribution.toString());
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("sourcePlanId", countPacksUsageForDistribution.getNorthAreaSourcePlanId());
		//新店开业  入店礼包，攻店礼包
		PromoteXDOpenDetail promoteXDOpenDetail = promoteBizService.getHasApplyXDOpenPacks(reqMap);
		if(null!=promoteXDOpenDetail)
		{
			countPacksUsageForDistribution.setNorthAreaUsedRDLB(promoteXDOpenDetail.getPacksNumber()==null?
					0:promoteXDOpenDetail.getPacksNumber());
			countPacksUsageForDistribution.setNorthAreaUsedGDLB(promoteXDOpenDetail.getGdPacksNumber()==null?
					0:promoteXDOpenDetail.getGdPacksNumber());
			
		}
		
		//路演     路演物料礼包，路演消费者互动礼包
		PromoteRoadshowActivityDetail roadShowDetail =  promoteBizService.getHasApplyRoadshowPacks(reqMap);
		if(null!=roadShowDetail)
		{
			countPacksUsageForDistribution.setNorthAreaUsedLYWLB(roadShowDetail.getRoadShowGifPackageCount()==null?
					0:roadShowDetail.getRoadShowGifPackageCount());
			countPacksUsageForDistribution.setNorthAreaUsedXFZHDBCB(roadShowDetail.getRoadShowConsumerPacksCount()==null?
					0:roadShowDetail.getRoadShowConsumerPacksCount());
		}
		//推广会
		PromoteSeminarActivityDetail seminarDetail =  promoteBizService.getHasApplySeminarPacks(reqMap);
		if(null!=seminarDetail)
		{
			countPacksUsageForDistribution.setNorthAreaUsedTGHWLB(seminarDetail.getApplyPacksCount()==null?
					0:seminarDetail.getApplyPacksCount());
			countPacksUsageForDistribution.setNorthAreaUsedDCJFACT(seminarDetail.getApplyPoints()==null?
					0:seminarDetail.getApplyPoints());
			countPacksUsageForDistribution.setNorthUsedVenueMeal(seminarDetail.getVenueMeal()==null?
					0:seminarDetail.getVenueMeal());
			countPacksUsageForDistribution.setNorthAreaUsedYZYTHLB(seminarDetail.getApplyHighPacks()==null?0:seminarDetail.getApplyHighPacks());
			
		}
		
		//店招
		PromoteShopSignsDetail shopSignsDetail = promoteBizService.getHasApplyDzPacks(reqMap);
		if(!EmptyChecker.isEmpty(shopSignsDetail)) {
			countPacksUsageForDistribution.setNorthAreaUsedDZLB(shopSignsDetail.getPacksNumber()==null?0:shopSignsDetail.getPacksNumber());
		}
		
		//户外广告
		PromoteAdvertisementDetail advertisementDetail = promoteBizService.getHasApplyHwggPacks(reqMap);
		if(!EmptyChecker.isEmpty(advertisementDetail)) {
			countPacksUsageForDistribution.setNorthAreaUsedHWGGLB(advertisementDetail.getPacksNumber()==null?0:advertisementDetail.getPacksNumber());
		}
		
		//农垦/矿山/工程机械物料
		PromoteAgricultureDetail agricultureDetail = promoteBizService.getHasApplyNkPacks(reqMap);
		if(!EmptyChecker.isEmpty(agricultureDetail)) {
			countPacksUsageForDistribution.setNorthAreaUsedNKLB(agricultureDetail.getPacksNumber()==null?0:agricultureDetail.getPacksNumber());
		}
		
		//试用油品/油样检测
		PromoteTryOilDetail tryOilDetail = promoteBizService.getHasApplySyPacks(reqMap);
		if(!EmptyChecker.isEmpty(tryOilDetail)) {
			countPacksUsageForDistribution.setNorthAreaUsedSYLB(tryOilDetail.getPacksNumber()==null?0:tryOilDetail.getPacksNumber());
		}
		
		/*//地促积分
		PromoteRegionalDetail pointsDetail = promoteBizService.getHasApplyPoints(reqMap);
		if(null!=pointsDetail)
		{
			countPacksUsageForDistribution.setNorthAreaUsedDCJFACT(pointsDetail.getApplyPromotionSupportPoints()==null?
					0:pointsDetail.getApplyPromotionSupportPoints());
		}*/
		
		return countPacksUsageForDistribution;
	}

	private CountPacksUsageForDistribution doGetSouthAreaUsedPacks(
			CountPacksUsageForDistribution countPacksUsageForDistribution)throws Exception {
		log.info("doGetSouthAreaUsedPacks countPacksUsageForDistribution:{}",countPacksUsageForDistribution.toString());
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("sourcePlanId", countPacksUsageForDistribution.getSouthAreaSourcePlanId());
		//新店开业  入店礼包，攻店礼包
		PromoteXDOpenDetail promoteXDOpenDetail = promoteBizService.getHasApplyXDOpenPacks(reqMap);
		if(null!=promoteXDOpenDetail)
		{
			countPacksUsageForDistribution.setSouthAreaUsedRDLB(promoteXDOpenDetail.getPacksNumber()==null?
					0:promoteXDOpenDetail.getPacksNumber());
			countPacksUsageForDistribution.setSouthAreaUsedGDLB(promoteXDOpenDetail.getGdPacksNumber()==null?
					0:promoteXDOpenDetail.getGdPacksNumber());
			
		}
		
		//路演     路演物料礼包，路演消费者互动礼包
		PromoteRoadshowActivityDetail roadShowDetail =  promoteBizService.getHasApplyRoadshowPacks(reqMap);
		if(null!=roadShowDetail)
		{
			countPacksUsageForDistribution.setSouthAreaUsedLYWLB(roadShowDetail.getRoadShowGifPackageCount()==null?
					0:roadShowDetail.getRoadShowGifPackageCount());
			countPacksUsageForDistribution.setSouthAreaUsedXFZHDBCB(roadShowDetail.getRoadShowConsumerPacksCount()==null?
					0:roadShowDetail.getRoadShowConsumerPacksCount());
		}
		//研讨会
		PromoteSeminarActivityDetail seminarDetail =  promoteBizService.getHasApplySeminarPacks(reqMap);
		if(null!=seminarDetail)
		{
			countPacksUsageForDistribution.setSouthAreaUsedTGHWLB(seminarDetail.getApplyPacksCount()==null?
					0:seminarDetail.getApplyPacksCount());
			countPacksUsageForDistribution.setSouthAreaUsedDCJFACT(seminarDetail.getApplyPoints()==null?
					0:seminarDetail.getApplyPoints());
			countPacksUsageForDistribution.setSouthUsedVenueMeal(seminarDetail.getVenueMeal()==null?
					0:seminarDetail.getVenueMeal());
			countPacksUsageForDistribution.setSouthAreaUsedYZYTHLB(seminarDetail.getApplyHighPacks()== null?0:seminarDetail.getApplyHighPacks());
		}
		
		//店招
		PromoteShopSignsDetail shopSignsDetail = promoteBizService.getHasApplyDzPacks(reqMap);
		if(!EmptyChecker.isEmpty(shopSignsDetail)) {
			countPacksUsageForDistribution.setSouthAreaUsedDZLB(shopSignsDetail.getPacksNumber()==null?0:shopSignsDetail.getPacksNumber());
		}
		
		//户外广告
		PromoteAdvertisementDetail advertisementDetail = promoteBizService.getHasApplyHwggPacks(reqMap);
		if(!EmptyChecker.isEmpty(advertisementDetail)) {
			countPacksUsageForDistribution.setSouthAreaUsedHWGGLB(advertisementDetail.getPacksNumber()==null?0:advertisementDetail.getPacksNumber());
		}
		
		//农垦/矿山/工程机械物料
		PromoteAgricultureDetail agricultureDetail = promoteBizService.getHasApplyNkPacks(reqMap);
		if(!EmptyChecker.isEmpty(agricultureDetail)) {
			countPacksUsageForDistribution.setSouthAreaUsedNKLB(agricultureDetail.getPacksNumber()==null?0:agricultureDetail.getPacksNumber());
		}
		
		//试用油品/油样检测
		PromoteTryOilDetail tryOilDetail = promoteBizService.getHasApplySyPacks(reqMap);
		if(!EmptyChecker.isEmpty(tryOilDetail)) {
			countPacksUsageForDistribution.setSouthAreaUsedSYLB(tryOilDetail.getPacksNumber()==null?0:tryOilDetail.getPacksNumber());
		}
		
		/*//地促积分
		PromoteRegionalDetail pointsDetail = promoteBizService.getHasApplyPoints(reqMap);
		if(null!=pointsDetail)
		{
			countPacksUsageForDistribution.setSouthAreaUsedDCJFACT(pointsDetail.getApplyPromotionSupportPoints()==null?
					0:pointsDetail.getApplyPromotionSupportPoints());
		}*/
		
		return countPacksUsageForDistribution;
	}
	
	
	@Override
	public Map<String, Object> statisticsPacksUsageByUserType(
			String promoteUserType) {
		log.info("statisticsPacksUsageByUserType promoteUserType:{}",promoteUserType);
		Map<String, Object> resultMap = new HashMap<String,Object>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			CountUsedPacksInfo countUsedPacksInfo = new CountUsedPacksInfo();
			//获取当前用户信息	
			CustomerRegionUser customerRegionUser = promoteBizService.getCustomerRegionUserInfo();
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("userId", customerRegionUser.getUserId());
			reqMap.put("sourcePlanId", customerRegionUser.getSourcePlanId());
			reqMap.put("prokey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_DISTRIBUTION);
			
			//大区经理
			if(PROMOTE_CHANNELMANAGER.equals(promoteUserType))
			{
				reqMap.put("channelManager", "true");
				//计算总的 即南区，或者北区的
				List<PromotePlan> lstPlan =  planMapper.getCurrentPlans(reqMap);
				if(null==lstPlan || lstPlan.isEmpty())
				{
					resultMap.put("countUsedPacksInfo", countUsedPacksInfo);
					return resultMap;
				}
				PromotePlan plan = lstPlan.get(0);
				countUsedPacksInfo.setTotalDCJFACT(plan.getIntegralTotalCount()==null?0:
					plan.getIntegralTotalCount());
				countUsedPacksInfo.setTotalGDLB(plan.getOpenShopGdPacksCount()==null?0:
					plan.getOpenShopGdPacksCount());
				countUsedPacksInfo.setTotalLYWLB(plan.getRoadShowActivitiesMaterialsCount()==null?0:
					plan.getRoadShowActivitiesMaterialsCount());
				countUsedPacksInfo.setTotalRDLB(plan.getOpenShopPacksCount()==null?0:
					plan.getOpenShopPacksCount());
				countUsedPacksInfo.setTotalTGHWLB(plan.getSeminarPacksCount()==null?0:
					plan.getSeminarPacksCount());
				countUsedPacksInfo.setTotalXFZHDBCB(plan.getRoadShowConsumerPacksCount()==null?0:
					plan.getRoadShowConsumerPacksCount());
				countUsedPacksInfo.setTotalVenueMeal(plan.getVenueMeal()==null?0:
					plan.getVenueMeal());
				countUsedPacksInfo.setTotalYZYTHLB(plan.getSeminarPacksHighCount()==null?0:plan.getSeminarPacksHighCount());
				countUsedPacksInfo.setTotalDZLB(plan.getStorePacksCount()==null?0:plan.getStorePacksCount());
				countUsedPacksInfo.setTotalHWGGLB(plan.getAdvertPacksCount()==null?0:plan.getAdvertPacksCount());
				countUsedPacksInfo.setTotalNKLB(plan.getAgriculturePacksCount()==null?0:plan.getAgriculturePacksCount());
				countUsedPacksInfo.setTotalSYLB(plan.getTryPacksCount()==null?0:plan.getTryPacksCount());
				// 计算已经用了的  自己分配出去的
				countUsedPacksInfo = doGetUsedPacksInfo(reqMap,customerRegionUser.getUserId(),countUsedPacksInfo);
			}else if(PROMOTE_SUPERVISOR.equals(promoteUserType))
			{
				//	计算总共的	分配到自己头上的
				reqMap.put("supervisor", "true");
				reqMap.putAll(PromoteBizService.getStatus());
				doGetTotalPacksInfo(reqMap,customerRegionUser.getUserId(),countUsedPacksInfo);
				
				// 	计算已经用领导，，，自己分配出去的
				doGetUsedPacksInfo(reqMap,customerRegionUser.getUserId(),countUsedPacksInfo);
			}
			resultMap.put("countUsedPacksInfo", countUsedPacksInfo);
		}catch (Exception e) {
			e.printStackTrace();
			log.error("statisticsPacksUsageByUserType"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}

	private CountUsedPacksInfo doGetUsedPacksInfo(Map<String, Object> reqMap,Long userId,CountUsedPacksInfo countUsedPacksInfo)throws Exception {
		log.info("doGetUsedPacksInfo userId:{},countUsedPacksInfo:{}",userId,countUsedPacksInfo);
		reqMap.put("distributionUserId", userId);
		PromoteDistribution promoteDistribution = planDistributionMapper.statisticsPacksUsed(reqMap);
		if(null==promoteDistribution)
		{
			reqMap.put("distributionUserId", null);
			return countUsedPacksInfo;
		}
		countUsedPacksInfo.setUsedDCJFACT(promoteDistribution.getPointsDistributionCount()==null?0:
			promoteDistribution.getPointsDistributionCount());
		countUsedPacksInfo.setUsedGDLB(promoteDistribution.getOpenShopGdPacksDistributionCount()==null?0:
			promoteDistribution.getOpenShopGdPacksDistributionCount());
		countUsedPacksInfo.setUsedLYWLB(promoteDistribution.getRoadShowActiviPacksDistributionCount()==null?0:
			promoteDistribution.getRoadShowActiviPacksDistributionCount());
		countUsedPacksInfo.setUsedRDLB(promoteDistribution.getOpenShopPacksDistributionCount()==null?0:
			promoteDistribution.getOpenShopPacksDistributionCount());
		countUsedPacksInfo.setUsedTGHWLB(promoteDistribution.getSeminarPacksDistributionCount()==null?0:
			promoteDistribution.getSeminarPacksDistributionCount());
		countUsedPacksInfo.setUsedXFZHDBCB(promoteDistribution.getRoadShowConsumerPacksDistributionCount()==null?0:
			promoteDistribution.getRoadShowConsumerPacksDistributionCount());
		countUsedPacksInfo.setUsedVenueMeal(promoteDistribution.getVenueMealDistribution()==null?0:
			promoteDistribution.getVenueMealDistribution());
		countUsedPacksInfo.setUsedYZYTHLB(promoteDistribution.getSeminarHighPacksDistributionCount()==null?0:promoteDistribution.getSeminarHighPacksDistributionCount());
		countUsedPacksInfo.setUsedDZLB(promoteDistribution.getStorePacksDistributionCount()==null?0:promoteDistribution.getStorePacksDistributionCount());
		countUsedPacksInfo.setUsedHWGGLB(promoteDistribution.getAdvertPacksDistributionCount()==null?0:promoteDistribution.getAdvertPacksDistributionCount());
		countUsedPacksInfo.setUsedNKLB(promoteDistribution.getAgriculturePacksDistributionCount()==null?0:promoteDistribution.getAgriculturePacksDistributionCount());
		countUsedPacksInfo.setUsedSYLB(promoteDistribution.getTryPacksDistributionCount()==null?0:promoteDistribution.getTryPacksDistributionCount());
		reqMap.put("distributionUserId", null);
		return countUsedPacksInfo;
	}
	
	
	private CountUsedPacksInfo doGetTotalPacksInfo(Map<String, Object> reqMap,Long userId,CountUsedPacksInfo countUsedPacksInfo)throws Exception {
		log.info("doGetTotalPacksInfo userId:{},countUsedPacksInfo:{}",userId,countUsedPacksInfo);
		reqMap.put("acceptUserId", userId);
		PromoteDistribution promoteDistribution = planDistributionMapper.statisticsPacksUsed(reqMap);
		if(null==promoteDistribution)
		{
			reqMap.put("acceptUserId", null);
			return countUsedPacksInfo;
		}
		countUsedPacksInfo.setTotalDCJFACT(promoteDistribution.getPointsDistributionCount()==null?0:
			promoteDistribution.getPointsDistributionCount());
		countUsedPacksInfo.setTotalGDLB(promoteDistribution.getOpenShopGdPacksDistributionCount()==null?0:
			promoteDistribution.getOpenShopGdPacksDistributionCount());
		countUsedPacksInfo.setTotalLYWLB(promoteDistribution.getRoadShowActiviPacksDistributionCount()==null?0:
			promoteDistribution.getRoadShowActiviPacksDistributionCount());
		countUsedPacksInfo.setTotalRDLB(promoteDistribution.getOpenShopPacksDistributionCount()==null?0:
			promoteDistribution.getOpenShopPacksDistributionCount());
		countUsedPacksInfo.setTotalTGHWLB(promoteDistribution.getSeminarPacksDistributionCount()==null?0:
			promoteDistribution.getSeminarPacksDistributionCount());
		countUsedPacksInfo.setTotalXFZHDBCB(promoteDistribution.getRoadShowConsumerPacksDistributionCount()==null?0:
			promoteDistribution.getRoadShowConsumerPacksDistributionCount());
		countUsedPacksInfo.setTotalVenueMeal(promoteDistribution.getVenueMealDistribution()==null?0:
			promoteDistribution.getVenueMealDistribution());
		countUsedPacksInfo.setTotalYZYTHLB(promoteDistribution.getSeminarHighPacksDistributionCount()==null?0:promoteDistribution.getSeminarHighPacksDistributionCount());
		countUsedPacksInfo.setTotalDZLB(promoteDistribution.getStorePacksDistributionCount()==null?0:promoteDistribution.getStorePacksDistributionCount());
		countUsedPacksInfo.setTotalHWGGLB(promoteDistribution.getAdvertPacksDistributionCount()==null?0:promoteDistribution.getAdvertPacksDistributionCount());
		countUsedPacksInfo.setTotalNKLB(promoteDistribution.getAgriculturePacksDistributionCount()==null?0:promoteDistribution.getAgriculturePacksDistributionCount());
		countUsedPacksInfo.setTotalSYLB(promoteDistribution.getTryPacksDistributionCount()==null?0:promoteDistribution.getTryPacksDistributionCount());
		reqMap.put("acceptUserId", null);
		return countUsedPacksInfo;
	}
	
	
	@Override
	public Map<String, Object> tackBackSalesPacksByUserId(Long acceptUserId) {
		//TODO 0.根据指定用户获取已经使用的包数量
		
		//TODO 1.查询指定用户被分配的包数量
		
		//TODO 2.根据条件减掉已经使用了的数量，，用于收回到大区经理的手上
		
		//TODO 3.重置指定用户被分配的包数量为（已经使用的包数量）
		
		
		return null;
	}
	
	
	@Override
	public void doHandlePromotePoints(List<ResponsePromoteSeminarDetail> lst, Long step) {
		try
		{
			/*ResponsePromoteSeminarDetail re1 = new ResponsePromoteSeminarDetail();
			re1.setActivityOrganizersId(56365L);//惠州市惠加实业有限公司
			re1.setApplyPoints(27150);
			re1.setReleaseTime(DateUtils.parseDate("2018-07-11"));
			lst.add(re1);
			ResponsePromoteSeminarDetail re2 = new ResponsePromoteSeminarDetail();
			re2.setActivityOrganizersId(56367L);//深圳市斯贝克进出口有限公司
			re2.setApplyPoints(46400);
			re2.setReleaseTime(DateUtils.parseDate("2018-07-16"));
			lst.add(re2);
			ResponsePromoteSeminarDetail re3 = new ResponsePromoteSeminarDetail();
			re3.setActivityOrganizersId(56421L);//广东润越实业有限公司
			re3.setApplyPoints(5500);
			re3.setReleaseTime(DateUtils.parseDate("2018-07-11"));
			lst.add(re3);
			ResponsePromoteSeminarDetail re4 = new ResponsePromoteSeminarDetail();
			re4.setActivityOrganizersId(56371L);//泉州鑫天贸易有限公司
			re4.setApplyPoints(30000);
			re4.setReleaseTime(DateUtils.parseDate("2018-07-04"));
			lst.add(re4);
			
			ResponsePromoteSeminarDetail re5 = new ResponsePromoteSeminarDetail();
			re5.setActivityOrganizersId(56408L);//福州润泉贸易有限公司
			re5.setApplyPoints(30000);
			re5.setReleaseTime(DateUtils.parseDate("2018-07-03"));
			lst.add(re5);
			
			ResponsePromoteSeminarDetail re6 = new ResponsePromoteSeminarDetail();
			re6.setActivityOrganizersId(56508L);//厦门宸庄贸易有限公司
			re6.setApplyPoints(12000);
			re6.setReleaseTime(DateUtils.parseDate("2018-07-03"));
			lst.add(re6);*/
			
			
			Long i = step;
			for(ResponsePromoteSeminarDetail tmp: lst)
			{
				
				List<ResponsePromoteSeminarDetail> newLst = new ArrayList<ResponsePromoteSeminarDetail>();
				newLst.add(tmp);
				//获取对应的积分数量
				//创建业务类型，用于描述进货积分的类型
				WXTPointBusinessVo careteBusinessVo = promoteWorkFlowService.doCreateBusiness(""+i,tmp.getApplyPoints());
				//创建待插入的积分list
				List<WXTPointValueDetailVo> detailList= promoteWorkFlowService.doCreatePointValueDetails(careteBusinessVo,newLst);
				pointBizService.importCaltexPointCustom(careteBusinessVo, detailList);
				
				//记录到地促积分流向日志表中
				PromoteRegionalPointsLog regionalPointsLog = new PromoteRegionalPointsLog();
				regionalPointsLog.setActivityId(i);
				regionalPointsLog.setBusinessId(careteBusinessVo.getId());
				regionalPointsLog.setCreateTime(tmp.getReleaseTime());
				regionalPointsLog.setPartnerId(tmp.getActivityOrganizersId());
				regionalPointsLog.setPoints(tmp.getApplyPoints());
				regionalPointsLog.setRemark("手动导入："+ContextUtil.getCurUser().getChName()+BusinessType.CALTEX_POINT_FROM_PROMOTE.name()+",进货积分业务id:"+careteBusinessVo.getId());
				pointsLogMapper.insertSelective(regionalPointsLog);
				i++;
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		
	}
}
