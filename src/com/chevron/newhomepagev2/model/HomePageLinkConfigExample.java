package com.chevron.newhomepagev2.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 首页快捷方式配置表单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-08-19 18:11
 */
public class HomePageLinkConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public HomePageLinkConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andMenuIdIsNull() {
            addCriterion("menu_id is null");
            return (Criteria) this;
        }

        public Criteria andMenuIdIsNotNull() {
            addCriterion("menu_id is not null");
            return (Criteria) this;
        }

        public Criteria andMenuIdEqualTo(Long value) {
            addCriterion("menu_id =", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdNotEqualTo(Long value) {
            addCriterion("menu_id <>", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdGreaterThan(Long value) {
            addCriterion("menu_id >", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("menu_id >=", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdLessThan(Long value) {
            addCriterion("menu_id <", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdLessThanOrEqualTo(Long value) {
            addCriterion("menu_id <=", value, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdIn(List<Long> values) {
            addCriterion("menu_id in", values, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdNotIn(List<Long> values) {
            addCriterion("menu_id not in", values, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdBetween(Long value1, Long value2) {
            addCriterion("menu_id between", value1, value2, "menuId");
            return (Criteria) this;
        }

        public Criteria andMenuIdNotBetween(Long value1, Long value2) {
            addCriterion("menu_id not between", value1, value2, "menuId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdIsNull() {
            addCriterion("home_page_id is null");
            return (Criteria) this;
        }

        public Criteria andHomePageIdIsNotNull() {
            addCriterion("home_page_id is not null");
            return (Criteria) this;
        }

        public Criteria andHomePageIdEqualTo(Long value) {
            addCriterion("home_page_id =", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdNotEqualTo(Long value) {
            addCriterion("home_page_id <>", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdGreaterThan(Long value) {
            addCriterion("home_page_id >", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdGreaterThanOrEqualTo(Long value) {
            addCriterion("home_page_id >=", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdLessThan(Long value) {
            addCriterion("home_page_id <", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdLessThanOrEqualTo(Long value) {
            addCriterion("home_page_id <=", value, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdIn(List<Long> values) {
            addCriterion("home_page_id in", values, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdNotIn(List<Long> values) {
            addCriterion("home_page_id not in", values, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdBetween(Long value1, Long value2) {
            addCriterion("home_page_id between", value1, value2, "homePageId");
            return (Criteria) this;
        }

        public Criteria andHomePageIdNotBetween(Long value1, Long value2) {
            addCriterion("home_page_id not between", value1, value2, "homePageId");
            return (Criteria) this;
        }

        public Criteria andMenuAliasIsNull() {
            addCriterion("menu_alias is null");
            return (Criteria) this;
        }

        public Criteria andMenuAliasIsNotNull() {
            addCriterion("menu_alias is not null");
            return (Criteria) this;
        }

        public Criteria andMenuAliasEqualTo(String value) {
            addCriterion("menu_alias =", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasNotEqualTo(String value) {
            addCriterion("menu_alias <>", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasGreaterThan(String value) {
            addCriterion("menu_alias >", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasGreaterThanOrEqualTo(String value) {
            addCriterion("menu_alias >=", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasLessThan(String value) {
            addCriterion("menu_alias <", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasLessThanOrEqualTo(String value) {
            addCriterion("menu_alias <=", value, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasIn(List<String> values) {
            addCriterion("menu_alias in", values, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasNotIn(List<String> values) {
            addCriterion("menu_alias not in", values, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasBetween(String value1, String value2) {
            addCriterion("menu_alias between", value1, value2, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andMenuAliasNotBetween(String value1, String value2) {
            addCriterion("menu_alias not between", value1, value2, "menuAlias");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Double value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Double value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Double value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Double value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Double value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Double value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Double> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Double> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Double value1, Double value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Double value1, Double value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNull() {
            addCriterion("ext_property1 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1IsNotNull() {
            addCriterion("ext_property1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty1EqualTo(String value) {
            addCriterion("ext_property1 =", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotEqualTo(String value) {
            addCriterion("ext_property1 <>", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThan(String value) {
            addCriterion("ext_property1 >", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property1 >=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThan(String value) {
            addCriterion("ext_property1 <", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1LessThanOrEqualTo(String value) {
            addCriterion("ext_property1 <=", value, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1In(List<String> values) {
            addCriterion("ext_property1 in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotIn(List<String> values) {
            addCriterion("ext_property1 not in", values, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1Between(String value1, String value2) {
            addCriterion("ext_property1 between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty1NotBetween(String value1, String value2) {
            addCriterion("ext_property1 not between", value1, value2, "extProperty1");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNull() {
            addCriterion("ext_property2 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2IsNotNull() {
            addCriterion("ext_property2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty2EqualTo(String value) {
            addCriterion("ext_property2 =", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotEqualTo(String value) {
            addCriterion("ext_property2 <>", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThan(String value) {
            addCriterion("ext_property2 >", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property2 >=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThan(String value) {
            addCriterion("ext_property2 <", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2LessThanOrEqualTo(String value) {
            addCriterion("ext_property2 <=", value, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2In(List<String> values) {
            addCriterion("ext_property2 in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotIn(List<String> values) {
            addCriterion("ext_property2 not in", values, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2Between(String value1, String value2) {
            addCriterion("ext_property2 between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty2NotBetween(String value1, String value2) {
            addCriterion("ext_property2 not between", value1, value2, "extProperty2");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNull() {
            addCriterion("ext_property3 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3IsNotNull() {
            addCriterion("ext_property3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty3EqualTo(String value) {
            addCriterion("ext_property3 =", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotEqualTo(String value) {
            addCriterion("ext_property3 <>", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThan(String value) {
            addCriterion("ext_property3 >", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property3 >=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThan(String value) {
            addCriterion("ext_property3 <", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3LessThanOrEqualTo(String value) {
            addCriterion("ext_property3 <=", value, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3In(List<String> values) {
            addCriterion("ext_property3 in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotIn(List<String> values) {
            addCriterion("ext_property3 not in", values, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3Between(String value1, String value2) {
            addCriterion("ext_property3 between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty3NotBetween(String value1, String value2) {
            addCriterion("ext_property3 not between", value1, value2, "extProperty3");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNull() {
            addCriterion("ext_property4 is null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4IsNotNull() {
            addCriterion("ext_property4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtProperty4EqualTo(String value) {
            addCriterion("ext_property4 =", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotEqualTo(String value) {
            addCriterion("ext_property4 <>", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThan(String value) {
            addCriterion("ext_property4 >", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4GreaterThanOrEqualTo(String value) {
            addCriterion("ext_property4 >=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThan(String value) {
            addCriterion("ext_property4 <", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4LessThanOrEqualTo(String value) {
            addCriterion("ext_property4 <=", value, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4In(List<String> values) {
            addCriterion("ext_property4 in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotIn(List<String> values) {
            addCriterion("ext_property4 not in", values, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4Between(String value1, String value2) {
            addCriterion("ext_property4 between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andExtProperty4NotBetween(String value1, String value2) {
            addCriterion("ext_property4 not between", value1, value2, "extProperty4");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
