package com.chevron.dsrkpi.service.impl;

import com.chevron.dsrkpi.business.CdmKpiInfoBizService;
import com.chevron.dsrkpi.dao.CdmKpiHistoryMapper;
import com.chevron.dsrkpi.dao.CdmKpiInfoMapper;
import com.chevron.dsrkpi.dao.CdmKpiIntegralHisInfoMapper;
import com.chevron.dsrkpi.model.CdmKpiFinishVo;
import com.chevron.dsrkpi.model.CdmKpiHistory;
import com.chevron.dsrkpi.model.CdmKpiInfo;
import com.chevron.dsrkpi.model.CdmKpiInfoVo;
import com.chevron.dsrkpi.model.CdmKpiIntegralHisInfoVo;
import com.chevron.dsrkpi.model.DicItemVo;
import com.chevron.dsrkpi.model.ExcelExportToEmail4Cdm;
import com.chevron.dsrkpi.model.TmmBonusCalParam;
import com.chevron.dsrkpi.model.TmmCollectKpiDTO;
import com.chevron.dsrkpi.model.TmmPerformaceDTO;
import com.chevron.dsrkpi.model.app.AppCdmKpiDisInfoVO;
import com.chevron.dsrkpi.model.app.AppCdmTmmAddFinishInfoVo;
import com.chevron.dsrkpi.model.app.AppPointsInfo;
import com.chevron.dsrkpi.service.CdmKpiInfoService;
import com.chevron.dsrkpi.service.DsrKpiService;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmailSendUtils;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;
import com.common.util.SendMessageUtil;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.UserServiceI;
import com.sys.dic.service.DicService;
import com.sys.log.util.LogUtils;
import com.sys.utils.business.LockerBizService;
import com.sys.utils.business.impl.LockerBizServiceImpl;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.ServletContext;
import java.io.File;
import java.io.FileOutputStream;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 金富力kpi绩效操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2020-03-17 14:05
 */
@Service
public class CdmKpiInfoServiceImpl implements CdmKpiInfoService {
	
	@Autowired
	private CdmKpiInfoBizService cdmKpiInfoBizService;
	
	@Autowired
	private CdmKpiInfoMapper cdmKpiInfoMapper;
	
	@Autowired
	private CdmKpiHistoryMapper cdmKpiHistoryMapper;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	
	@Autowired
	private CdmKpiIntegralHisInfoMapper cdmKpiIntegralHisInfoMapper;
	
	@Autowired
	private DicService dicService;
	
	@Autowired
	private UserServiceI UserServiceI;
	
	@Autowired
    private DsrKpiService dsrKpiService;

    @Autowired
    private LockerBizService lockerBizService;
	
	private final static Logger log = Logger.getLogger(CdmKpiInfoServiceImpl.class);

    private final static String LOCKER_ID = "CDM.DSR.PERFORMANCE";

    private final static String LOCKER_NAME = "乘用车绩效发放";

    private final static int LOCK_EFFICTIVE_TIME = 600;//10分钟

	@Override
	public JsonResponse save(CdmKpiInfo record) {
		JsonResponse map = new JsonResponse();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				cdmKpiInfoBizService.insert(record);
			}else{
				cdmKpiInfoBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiInfoServiceImpl.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse saveForEditPage(CdmKpiInfo record) {
		JsonResponse map = new JsonResponse();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				cdmKpiInfoBizService.insert(record);
			}else{
				cdmKpiInfoBizService.updateForEditPage(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiInfoServiceImpl.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	@Override
	public JsonResponse delete(List<Long> ids) {
		JsonResponse map = new JsonResponse();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			cdmKpiInfoBizService.delete(ids);
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.dsrkpi.service.impl.CdmKpiInfoServiceImpl.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}

	@Override
	public JsonResponse getCdmKpiInfoListByCondition(CdmKpiInfoVo cdmKpiInfoVo) {
        cdmKpiInfoVo.setKpiYear(2020l);
		//获取kpi 设置列表
		JsonResponse resMap = new JsonResponse();
		//判断是否有权限
		WxTUser curUser = ContextUtil.getCurUser();
		String partnerCai = curUser.getCai();
		Long userId = curUser.getUserId();
		cdmKpiInfoVo.setTmmUser(cdmKpiInfoVo.getTmmUser() == null ? false : cdmKpiInfoVo.getTmmUser());
		int permissionWeight;
		try {
			//获取当前用户的查看权限
			permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || userId == 1L) {
				permissionWeight = 1;
			}else if(2 == (permissionWeight&2)) {
				permissionWeight = 2;
				cdmKpiInfoVo.setBuManagerCai(partnerCai);
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
		
		//分模块查询
		cdmKpiInfoVo.setPaging(false);
		Map<String,List<CdmKpiInfoVo>> resultMap = new HashMap<String, List<CdmKpiInfoVo>>();
		
		if(StringUtils.isBlank(cdmKpiInfoVo.getKpiType())){
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("dicTypeCode", "customer.kpi.type");
			List<DicItemVo> dicItemVo = cdmKpiInfoMapper.queryDicItemByParams(params);
			if(cdmKpiInfoVo.getTmmUser()) {
				//2kpi设置查询
				for (DicItemVo itemVo : dicItemVo) {
					if(-1 != itemVo.getDicItemCode().indexOf("TMM")) {
						cdmKpiInfoVo.setKpiType(itemVo.getDicItemCode());
						//初始化orgIds
						cdmKpiInfoVo.setOrgIds(null);
						findTmmKpiInfo(cdmKpiInfoVo, resultMap);
					}
				}
			}else {
				//DSR KPI设置查询
				for (DicItemVo itemVo : dicItemVo) {
					if(-1 == itemVo.getDicItemCode().indexOf("TMM") && !"settingReward".equals(itemVo.getDicItemCode())) {
						//初始化orgIds
						cdmKpiInfoVo.setOrgIds(null);
						cdmKpiInfoVo.setKpiType(itemVo.getDicItemCode());
						List<CdmKpiInfoVo> cdmKpiInfoForEntryList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
						
						if(!cdmKpiInfoForEntryList.isEmpty()) {
							//获取 经销商
							Map<Long, Object> orgIdsMap = new HashMap<Long, Object>();
							for (CdmKpiInfoVo cdmKpiInfoVo2 : cdmKpiInfoForEntryList) {
								orgIdsMap.put(cdmKpiInfoVo2.getOrgId(), cdmKpiInfoVo2.getOrgId());
							}
							Set<Long> idsSet = orgIdsMap.keySet();
							List<Long> idsList = new ArrayList<Long>();
							Iterator<Long> iterator = idsSet.iterator();
							while(iterator.hasNext()) {
								idsList.add(iterator.next());
							}
							cdmKpiInfoVo.setOrgIds(idsList);
							List<CdmKpiInfoVo> cdmKpiInfoForCusList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
							if(!cdmKpiInfoForEntryList.isEmpty()) {
								if(!cdmKpiInfoForCusList.isEmpty()) {
									cdmKpiInfoForEntryList.addAll(cdmKpiInfoForCusList);
								}
								resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForEntryList);
							}else {
								if(!cdmKpiInfoForEntryList.isEmpty()) {
									resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForEntryList);
								}
							}
						}
						
					/*	if(!cdmKpiInfoForEntryList.isEmpty()) {
							resultMap.put(itemVo.getDicItemCode(), cdmKpiInfoForEntryList);
						}*/
					}
				}
			}
		}else if(cdmKpiInfoVo.getKpiType().equals("settingReward")) {
				//1 奖励设置查询
				List<CdmKpiInfoVo> cdmKpiInfoForRewardList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
				
				if(!cdmKpiInfoForRewardList.isEmpty()) {
					//获取 经销商
					Map<Long, Object> orgIdsMap = new HashMap<Long, Object>();
					for (CdmKpiInfoVo cdmKpiInfoVo2 : cdmKpiInfoForRewardList) {
						orgIdsMap.put(cdmKpiInfoVo2.getOrgId(), cdmKpiInfoVo2.getOrgId());
					}
					Set<Long> idsSet = orgIdsMap.keySet();
					List<Long> idsList = new ArrayList<Long>();
					Iterator<Long> iterator = idsSet.iterator();
					while(iterator.hasNext()) {
						idsList.add(iterator.next());
					}
					cdmKpiInfoVo.setOrgIds(idsList);
					List<CdmKpiInfoVo> cdmKpiInfoForRewardCusList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
					if(!cdmKpiInfoForRewardList.isEmpty()) {
						if(!cdmKpiInfoForRewardCusList.isEmpty()) {
							cdmKpiInfoForRewardList.addAll(cdmKpiInfoForRewardCusList);
						}
						resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForRewardList);
					}else {
						if(!cdmKpiInfoForRewardCusList.isEmpty()) {
							resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForRewardCusList);
						}
					}
				}
			/*	if(!cdmKpiInfoForRewardList.isEmpty()) {
					resultMap.put("settingReward", cdmKpiInfoForRewardList);
				}*/
		}
		resMap.setDataResult(resultMap);
		return resMap;
	}

	private void findTmmKpiInfo(CdmKpiInfoVo cdmKpiInfoVo, Map<String, List<CdmKpiInfoVo>> resultMap) {
		List<CdmKpiInfoVo> cdmKpiInfoForTMMOrderList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
		if(!cdmKpiInfoForTMMOrderList.isEmpty()) {
			//获取tmm 经销商
			Map<Long, Object> orgIdsMap = new HashMap<Long, Object>();
			for (CdmKpiInfoVo cdmKpiInfoVo2 : cdmKpiInfoForTMMOrderList) {
				orgIdsMap.put(cdmKpiInfoVo2.getOrgId(), cdmKpiInfoVo2.getOrgId());
			}
			Set<Long> idsSet = orgIdsMap.keySet();
			List<Long> idsList = new ArrayList<Long>();
			Iterator<Long> iterator = idsSet.iterator();
			while(iterator.hasNext()) {
				idsList.add(iterator.next());
			}
			cdmKpiInfoVo.setOrgIds(idsList);
			List<CdmKpiInfoVo> cdmKpiInfoForTMMOrderCusList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
			if(!cdmKpiInfoForTMMOrderList.isEmpty()) {
				if(!cdmKpiInfoForTMMOrderCusList.isEmpty()) {
					cdmKpiInfoForTMMOrderList.addAll(cdmKpiInfoForTMMOrderCusList);
				}
				resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForTMMOrderList);
			}else {
				if(!cdmKpiInfoForTMMOrderCusList.isEmpty()) {
					resultMap.put(cdmKpiInfoVo.getKpiType(), cdmKpiInfoForTMMOrderCusList);
				}
			}
		}
	}

	@Override
	@Transactional
	public synchronized JsonResponse updateCdmKpiInfoList(List<CdmKpiInfo> lists) {
		//保存kpi 设置信息
		JsonResponse resMap = new JsonResponse();
		//权限控制
		WxTUser curUser = ContextUtil.getCurUser();
		Long userId = curUser.getUserId();
		int permissionWeight;
		try {
			//获取当前用户的查看权限
			permissionWeight = operationPermissionBizService.getPermissionWeight(userId, "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || userId == 1L) {
				permissionWeight = 1;
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
		//批量更改cdm KPI 信息
		for (CdmKpiInfo cdmKpiInfo : lists) {
			
			//添加历史记录
			List<CdmKpiHistory> historyList = checkKpiHistory(cdmKpiInfo);
			cdmKpiHistoryMapper.insertBatch(historyList);	
			
			//保存kpi设置信息
			cdmKpiInfoMapper.updateByPrimaryKeySelective(cdmKpiInfo);
		}
		return resMap;
	}
	
	public List<CdmKpiHistory> checkKpiHistory(CdmKpiInfo cdmKpiInfo) {
		
		WxTUser curUser = ContextUtil.getCurUser();
		List<CdmKpiHistory> historyList = new ArrayList<CdmKpiHistory>();
		
		CdmKpiInfo startInfo= cdmKpiInfoMapper.selectByPrimaryKey(cdmKpiInfo.getId());
		
		//年度总目标/奖励
		if(cdmKpiInfo.getYearGoals() != null && !cdmKpiInfo.getYearGoals().equals(startInfo.getYearGoals())) {
			CdmKpiHistory cdmKpiHistory13 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),13L);
			Double startInfoNum = startInfo.getYearGoals() == null ? 0 : startInfo.getYearGoals();
			cdmKpiHistory13.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory13.setCdmKpiEndNum(cdmKpiInfo.getYearGoals());
			cdmKpiHistory13.setType(cdmKpiInfo.getYearGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory13);
		}

		//yearAwards
		if(cdmKpiInfo.getYearAwards() != null && !cdmKpiInfo.getYearAwards().equals(startInfo.getYearAwards())) {
			CdmKpiHistory cdmKpiHistory14 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),14L);
			Double startInfoNum = startInfo.getYearAwards() == null ? 0 : startInfo.getYearAwards();
			cdmKpiHistory14.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory14.setCdmKpiEndNum(cdmKpiInfo.getYearAwards());
			cdmKpiHistory14.setType(cdmKpiInfo.getYearAwards() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory14);
		}
		//monGoals
		if(cdmKpiInfo.getMonGoals() != null && !cdmKpiInfo.getMonGoals().equals(startInfo.getMonGoals())) {
			CdmKpiHistory cdmKpiHistory1 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),1L);
			Double startInfoNum = startInfo.getMonGoals() == null ? 0 : startInfo.getMonGoals();
			cdmKpiHistory1.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory1.setCdmKpiEndNum(cdmKpiInfo.getMonGoals());
			cdmKpiHistory1.setType(cdmKpiInfo.getMonGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory1);
		}
		//febGoals
		if(cdmKpiInfo.getFebGoals() != null && !cdmKpiInfo.getFebGoals().equals(startInfo.getFebGoals())) {
			CdmKpiHistory cdmKpiHistory2 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),2L);
			Double startInfoNum = startInfo.getFebGoals() == null ? 0 : startInfo.getFebGoals();
			cdmKpiHistory2.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory2.setCdmKpiEndNum(cdmKpiInfo.getFebGoals());
			cdmKpiHistory2.setType(cdmKpiInfo.getFebGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory2);
		}
		//marGoals
		if(cdmKpiInfo.getAprGoals() != null && !cdmKpiInfo.getAprGoals().equals(startInfo.getAprGoals())) {
			CdmKpiHistory cdmKpiHistory3 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),3L);
			Double startInfoNum = startInfo.getAprGoals() == null ? 0 : startInfo.getAprGoals();
			cdmKpiHistory3.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory3.setCdmKpiEndNum(cdmKpiInfo.getAprGoals());
			cdmKpiHistory3.setType(cdmKpiInfo.getAprGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory3);
		}
		//aprGoals
		if(cdmKpiInfo.getAprGoals() != null && !cdmKpiInfo.getAprGoals().equals(startInfo.getAprGoals())) {
			CdmKpiHistory cdmKpiHistory4 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),4L);
			Double startInfoNum = startInfo.getAprGoals() == null ? 0 : startInfo.getAprGoals();
			cdmKpiHistory4.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory4.setCdmKpiEndNum(cdmKpiInfo.getAprGoals());
			cdmKpiHistory4.setType(cdmKpiInfo.getAprGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory4);
		}
		//mayGoals
		if(cdmKpiInfo.getMayGoals() != null && !cdmKpiInfo.getMayGoals().equals(startInfo.getMayGoals())) {
			CdmKpiHistory cdmKpiHistory5 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),5L);
			Double startInfoNum = startInfo.getMayGoals() == null ? 0 : startInfo.getMayGoals();
			cdmKpiHistory5.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory5.setCdmKpiEndNum(cdmKpiInfo.getMayGoals());
			cdmKpiHistory5.setType(cdmKpiInfo.getMayGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory5);
		}
		//juneGoals
		if(cdmKpiInfo.getJuneGoals() != null && !cdmKpiInfo.getJuneGoals().equals(startInfo.getJuneGoals())) {
			CdmKpiHistory cdmKpiHistory6 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),6L);
			Double startInfoNum = startInfo.getJuneGoals() == null ? 0 : startInfo.getJuneGoals();
			cdmKpiHistory6.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory6.setCdmKpiEndNum(cdmKpiInfo.getJuneGoals());
			cdmKpiHistory6.setType(cdmKpiInfo.getJuneGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory6);
		}
		//julyGoals
		if(cdmKpiInfo.getJulyGoals() != null && !cdmKpiInfo.getJulyGoals().equals(startInfo.getJulyGoals())) {
			CdmKpiHistory cdmKpiHistory7 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),7L);
			Double startInfoNum = startInfo.getJulyGoals() == null ? 0 : startInfo.getJulyGoals();
			cdmKpiHistory7.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory7.setCdmKpiEndNum(cdmKpiInfo.getJulyGoals());
			cdmKpiHistory7.setType(cdmKpiInfo.getJulyGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory7);
		}
		//augGoals
		if(cdmKpiInfo.getAugGoals() != null && !cdmKpiInfo.getAugGoals().equals(startInfo.getAugGoals())) {
			CdmKpiHistory cdmKpiHistory8 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),8L);
			Double startInfoNum = startInfo.getAugGoals() == null ? 0 : startInfo.getAugGoals();
			cdmKpiHistory8.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory8.setCdmKpiEndNum(cdmKpiInfo.getAugGoals());
			cdmKpiHistory8.setType(cdmKpiInfo.getAugGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory8);
		}
		//septGoals
		if(cdmKpiInfo.getSeptGoals() != null && !cdmKpiInfo.getSeptGoals().equals(startInfo.getSeptGoals())) {
			CdmKpiHistory cdmKpiHistory9 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),9L);
			Double startInfoNum = startInfo.getSeptGoals() == null ? 0 : startInfo.getSeptGoals();
			cdmKpiHistory9.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory9.setCdmKpiEndNum(cdmKpiInfo.getSeptGoals());
			cdmKpiHistory9.setType(cdmKpiInfo.getSeptGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory9);
		}
		//octGoals
		if(cdmKpiInfo.getOctGoals() != null && !cdmKpiInfo.getOctGoals().equals(startInfo.getOctGoals())) {
			CdmKpiHistory cdmKpiHistory10 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),10L);
			Double startInfoNum = startInfo.getOctGoals() == null ? 0 : startInfo.getOctGoals();
			cdmKpiHistory10.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory10.setCdmKpiEndNum(cdmKpiInfo.getOctGoals());
			cdmKpiHistory10.setType(cdmKpiInfo.getOctGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory10);
		}
		//novGoals
		if(cdmKpiInfo.getNovGoals() != null && !cdmKpiInfo.getNovGoals().equals(startInfo.getNovGoals())) {
			CdmKpiHistory cdmKpiHistory11 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),11L);
			Double startInfoNum = startInfo.getNovGoals() == null ? 0 : startInfo.getNovGoals();
			cdmKpiHistory11.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory11.setCdmKpiEndNum(cdmKpiInfo.getNovGoals());
			cdmKpiHistory11.setType(cdmKpiInfo.getNovGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory11);
		}
		//decGoals
		if(cdmKpiInfo.getDecGoals() != null && !cdmKpiInfo.getDecGoals().equals(startInfo.getDecGoals())) {
			CdmKpiHistory cdmKpiHistory12 = new CdmKpiHistory(curUser.getUserId(),cdmKpiInfo.getId(),cdmKpiInfo.getOrgId(),cdmKpiInfo.getDsrId(),cdmKpiInfo.getKpiYear(),12L);
			Double startInfoNum = startInfo.getDecGoals() == null ? 0 : startInfo.getDecGoals();
			cdmKpiHistory12.setCdmKpiStartNum(startInfoNum);
			cdmKpiHistory12.setCdmKpiEndNum(cdmKpiInfo.getDecGoals());
			cdmKpiHistory12.setType(cdmKpiInfo.getDecGoals() > startInfoNum ? 1 : 2);
			historyList.add(cdmKpiHistory12);
		}
		return historyList;
	}

	@Override
	public JsonResponse appGetPoints() {
		JsonResponse resMap = new JsonResponse();
		/*Map<String, Object> params = new HashMap<String, Object>();
		WxTUser user = ContextUtil.getCurUser();
		params.put("dsrId", user.getUserId());
		List<String> types = new ArrayList<String>();
		types.add(CdmKpiInfo.CDM_TYPE_ENTRY);
		types.add(CdmKpiInfo.CDM_TYPE_VISIT);
		types.add(CdmKpiInfo.CDM_TYPE_SALES);
		params.put("types", types);
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);*/
		
		WxTUser user = ContextUtil.getCurUser();
		List<AppPointsInfo> appPointsList = cdmKpiInfoMapper.getAppPoints(user.getUserId());
		
		
		
		Map<String, Object> pointMap = new HashMap<String, Object>();
		Double entryAllNum = (double) 0; Double entryRemainNum = (double) 0;Double visitAllNum = (double) 0; Double visitRemainNum = (double) 0;
		Double salesAllNum = (double) 0; Double salesRemainNum = (double) 0;
		
		Double entryTmmAllNum = (double) 0; Double entryTmmRemainNum = (double) 0;
		Double orderTmmAllNum = (double) 0; Double orderTmmRemainNum = (double) 0;
		Double salesTmmAllNum = (double) 0; Double salesTmmRemainNum = (double) 0;
		Double snAboverTmmAllNum = (double) 0; Double snAboverTmmRemainNum = (double) 0;
		for (AppPointsInfo appPointsInfo : appPointsList) {
			if(appPointsInfo.getKpiType().equals(CdmKpiInfo.CDM_TYPE_ENTRY)) {
				entryAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				entryRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(appPointsInfo.getKpiType().equals(CdmKpiInfo.CDM_TYPE_VISIT)) {
				visitAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				visitRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(appPointsInfo.getKpiType().equals(CdmKpiInfo.CDM_TYPE_SALES)) {
				salesAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				salesRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(CdmKpiInfo.CDM_TYPE_TMM_ENTRY.equals(appPointsInfo.getKpiType())) {
				entryTmmAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				entryTmmRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(CdmKpiInfo.CDM_TYPE_TMM_ORDER.equals(appPointsInfo.getKpiType())) {
				orderTmmAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				orderTmmRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(CdmKpiInfo.CDM_TYPE_TMM_SALES.equals(appPointsInfo.getKpiType())) {
				salesTmmAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				salesTmmRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
			if(CdmKpiInfo.CDM_TYPE_TMM_SNABOVE.equals(appPointsInfo.getKpiType())) {
				snAboverTmmAllNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
				snAboverTmmRemainNum += appPointsInfo.getPoints() == null ? 0 : appPointsInfo.getPoints();
			}
		}
		pointMap.put("entryAllNum", entryAllNum);
		pointMap.put("entryRemainNum", entryRemainNum);
		pointMap.put("visitAllNum", visitAllNum);
		pointMap.put("visitRemainNum", visitRemainNum);
		pointMap.put("salesAllNum", salesAllNum);
		pointMap.put("salesRemainNum", salesRemainNum);
		
		pointMap.put("entryTmmAllNum", entryTmmAllNum);
		pointMap.put("entryTmmRemainNum", entryTmmRemainNum);
		pointMap.put("orderTmmAllNum", orderTmmAllNum);
		pointMap.put("orderTmmRemainNum", orderTmmRemainNum);
		pointMap.put("salesTmmAllNum", salesTmmAllNum);
		pointMap.put("salesTmmRemainNum", salesTmmRemainNum);
		pointMap.put("snAboverTmmAllNum", snAboverTmmAllNum);
		pointMap.put("snAboverTmmRemainNum", snAboverTmmRemainNum);
		
		pointMap.put("AllNum", entryAllNum+visitAllNum+salesAllNum+entryTmmAllNum+orderTmmAllNum+salesTmmAllNum+snAboverTmmAllNum);
		pointMap.put("RemainNum", entryRemainNum+visitRemainNum+salesRemainNum+entryTmmRemainNum+orderTmmRemainNum+salesTmmRemainNum+snAboverTmmRemainNum);
		resMap.setDataResult(pointMap);
		return resMap;
	}

	@Override
	public JsonResponse appGetCdmVisitInfo(String date) {
		JsonResponse resMap = new JsonResponse();
		WxTUser user = ContextUtil.getCurUser();
		

		Map<String, Object> params = new HashMap<String, Object>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		Date startDay = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDay = DateUtil.addMonths(startDay, 1);
		params.put("dsrId", user.getUserId());
		params.put("startDay", dateFormat.format(startDay));
		params.put("endDay", dateFormat.format(endDay));
		//获取CDM走访详情
		List<AppCdmKpiDisInfoVO> appGetCDMVisitInfo = cdmKpiInfoMapper.appGetCDMVisitInfo(params);
		
		//统计实际走访数
		AppCdmKpiDisInfoVO appCdmVisitKpiInfo = new AppCdmKpiDisInfoVO();
		Long factNum = 0L;
		for (AppCdmKpiDisInfoVO appCdmKpiDisInfoVO : appGetCDMVisitInfo) {
			factNum += appCdmKpiDisInfoVO.getFactNum();
		}
		appCdmVisitKpiInfo.setFactNum(factNum);
		
		//获取kpi设置详情
		SimpleDateFormat dayDate = new SimpleDateFormat("yyyy");
		List<String> types = new ArrayList<String>();
		types.add(CdmKpiInfo.CDM_TYPE_VISIT);
		params.put("types", types);
		params.put("kpiYear", dayDate.format(startDay));
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);
		if(!queryByParams.isEmpty()) {
			SimpleDateFormat monDate = new SimpleDateFormat("MM");
			log.info(Integer.valueOf(monDate.format(startDay)));
			Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(queryByParams.get(0),Integer.valueOf(monDate.format(startDay)));
			//获取目标数
			appCdmVisitKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
		}
		
		resMap.setDataResult(appCdmVisitKpiInfo);  //app 走访门店 拜访数和目标数
		resMap.setListResult(appGetCDMVisitInfo); //走访的每天的详情
		return resMap;
	}
	
	
	//统计当月目标数以及YTD数
	public Map<String, Double> CountArimAndYtdArim(CdmKpiInfo cdmKpiInfo,Integer mon) {
		
		Map<String, Double> resMap = new HashMap<String , Double>();
		Double ytdNum = (double)0;
		Double arimNum = (double)0;
		switch (mon) {
		case 1:
			arimNum = cdmKpiInfo.getMonGoals() == null ? 0 : cdmKpiInfo.getMonGoals();
			break;
		case 2:
			arimNum = cdmKpiInfo.getFebGoals() == null ? 0 : cdmKpiInfo.getFebGoals();
			break;
		case 3:
			arimNum = cdmKpiInfo.getMarGoals() == null ? 0 : cdmKpiInfo.getMarGoals();
			break;
		case 4:
			arimNum = cdmKpiInfo.getAprGoals() == null ? 0 : cdmKpiInfo.getAprGoals();
			break;
		case 5:
			arimNum = cdmKpiInfo.getMayGoals() == null ? 0 : cdmKpiInfo.getMayGoals();
			break;
		case 6:
			arimNum = cdmKpiInfo.getJuneGoals() == null ? 0 : cdmKpiInfo.getJuneGoals();
			break;
		case 7:
			arimNum = cdmKpiInfo.getJulyGoals() == null ? 0 : cdmKpiInfo.getJulyGoals();
			break;
		case 8:
			arimNum = cdmKpiInfo.getAugGoals() == null ? 0 : cdmKpiInfo.getAugGoals();
			break;
		case 9:
			arimNum = cdmKpiInfo.getSeptGoals() == null ? 0 : cdmKpiInfo.getSeptGoals();
			break;
		case 10:
			arimNum = cdmKpiInfo.getOctGoals() == null ? 0 : cdmKpiInfo.getOctGoals();
			break;
		case 11:
			arimNum = cdmKpiInfo.getNovGoals() == null ? 0 : cdmKpiInfo.getNovGoals();
			break;
		case 12:
			arimNum = cdmKpiInfo.getDecGoals() == null ? 0 : cdmKpiInfo.getDecGoals();
			break;		
		default:
			break;
		}
		switch (mon) {
		case 12:
			ytdNum += cdmKpiInfo.getDecGoals() == null ? 0 : cdmKpiInfo.getDecGoals();
		case 11:
			ytdNum += cdmKpiInfo.getNovGoals() == null ? 0 : cdmKpiInfo.getNovGoals();
		case 10:
			ytdNum += cdmKpiInfo.getOctGoals() == null ? 0 : cdmKpiInfo.getOctGoals();
		case 9:
			ytdNum += cdmKpiInfo.getSeptGoals() == null ? 0 : cdmKpiInfo.getSeptGoals();
		case 8:
			ytdNum += cdmKpiInfo.getAugGoals() == null ? 0 : cdmKpiInfo.getAugGoals();
		case 7:
			ytdNum += cdmKpiInfo.getJulyGoals() == null ? 0 : cdmKpiInfo.getJulyGoals();
		case 6:
			ytdNum += cdmKpiInfo.getJuneGoals() == null ? 0 : cdmKpiInfo.getJuneGoals();
		case 5:
			ytdNum += cdmKpiInfo.getMayGoals() == null ? 0 : cdmKpiInfo.getMayGoals();
		case 4:
			ytdNum += cdmKpiInfo.getAprGoals() == null ? 0 : cdmKpiInfo.getAprGoals();
		case 3:
			ytdNum += cdmKpiInfo.getMarGoals() == null ? 0 : cdmKpiInfo.getMarGoals();
		case 2:
			ytdNum += cdmKpiInfo.getFebGoals() == null ? 0 : cdmKpiInfo.getFebGoals();
		case 1:
			ytdNum += cdmKpiInfo.getMonGoals() == null ? 0 : cdmKpiInfo.getMonGoals();
			break;
		default:
			break;
		}
		resMap.put("ytdNum", ytdNum);
		resMap.put("arimNum", arimNum);
		return resMap;
	}

	@Override
	public JsonResponse appGetCdmEntryInfo(String date) {
		JsonResponse resMap = new JsonResponse();
		WxTUser user = ContextUtil.getCurUser();
		
		Map<String, Object> params = new HashMap<String, Object>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		Date startDay = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDay = DateUtil.addMonths(startDay, 1);
		params.put("dsrId", user.getUserId());
		params.put("startDay", dateFormat.format(startDay));
		params.put("endDay", dateFormat.format(endDay));
		 //获取CDM新客户录入详情
		List<AppCdmKpiDisInfoVO> appGetCDMEntryInfo = cdmKpiInfoMapper.appGetCDMEntryInfo(params);
		
		AppCdmKpiDisInfoVO appCdmEntryKpiInfo = new AppCdmKpiDisInfoVO();
		Long commitNum = 0L; Long mktConfirmNum = 0L;
		for (AppCdmKpiDisInfoVO appCdmKpiDisInfoVO : appGetCDMEntryInfo) {
			commitNum += appCdmKpiDisInfoVO.getCommitNum();
			if(appCdmKpiDisInfoVO.getMktConfirmNum() != null && appCdmKpiDisInfoVO.getMktConfirmNum() == 3) {
				mktConfirmNum += appCdmKpiDisInfoVO.getCommitNum();
			}
			
		}
		Long partnerConfirmNum = cdmKpiInfoMapper.countPartnerConfirmCDMEntry(params);
		appCdmEntryKpiInfo.setCommitNum(commitNum);
		appCdmEntryKpiInfo.setPartnerConfirmNum(partnerConfirmNum);
		appCdmEntryKpiInfo.setMktConfirmNum(mktConfirmNum);
		
		//获取kpi设置详情
		SimpleDateFormat dayDate = new SimpleDateFormat("yyyy");
		List<String> types = new ArrayList<String>();
		types.add(CdmKpiInfo.CDM_TYPE_ENTRY);
		params.put("types", types);
		params.put("kpiYear", dayDate.format(startDay));
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);
		if(!queryByParams.isEmpty()) {
			SimpleDateFormat monDate = new SimpleDateFormat("MM");
			Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(queryByParams.get(0),Integer.valueOf(monDate.format(startDay)));
			//获取目标数
			appCdmEntryKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
		}
		resMap.setDataResult(appCdmEntryKpiInfo);
		return resMap;
	}

	@Override
	public JsonResponse appGetCdmSalesInfo(String date) {
		
		JsonResponse resMap = new JsonResponse();
		WxTUser user = ContextUtil.getCurUser();
		
		Map<String, Object> params = new HashMap<String, Object>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		Date startDay = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDay = DateUtil.addMonths(startDay, 1);
		params.put("dsrId", user.getUserId());
		params.put("startDay", dateFormat.format(startDay));
		params.put("endDay", dateFormat.format(endDay));
		 //获取CDM有效销量详情
		List<AppCdmKpiDisInfoVO> appGetCDMSalesInfo = cdmKpiInfoMapper.appGetCDMSalesInfo(params);
		 
		AppCdmKpiDisInfoVO appCdmSalesKpiInfo = new AppCdmKpiDisInfoVO();
		Long commitNum = 0L; Long partnerConfirmNum = 0L; Long outSalesNum = 0L;
		Map<Long, String> workShopNumMap = new HashMap<Long, String>();
		for (AppCdmKpiDisInfoVO appCdmKpiDisInfoVO : appGetCDMSalesInfo) {
			commitNum += appCdmKpiDisInfoVO.getFactNum();
			if(appCdmKpiDisInfoVO.getPartnerConfirmNum() != null && appCdmKpiDisInfoVO.getPartnerConfirmNum() == 1) {
				partnerConfirmNum += appCdmKpiDisInfoVO.getFactNum();
			}
			if(appCdmKpiDisInfoVO.getOutSalesNum() != null && appCdmKpiDisInfoVO.getOutSalesNum() == 11) {
				outSalesNum += appCdmKpiDisInfoVO.getFactNum();
			}
			workShopNumMap.put(appCdmKpiDisInfoVO.getOtherNum(), appCdmKpiDisInfoVO.getOtherNum().toString());
		}
		appCdmSalesKpiInfo.setOutSalesNum(outSalesNum);
		appCdmSalesKpiInfo.setPartnerConfirmNum(partnerConfirmNum);
		appCdmSalesKpiInfo.setCommitNum(commitNum);
		appCdmSalesKpiInfo.setOtherNum(Long.valueOf(workShopNumMap.size()));
		 
		//获取kpi设置详情
		SimpleDateFormat dayDate = new SimpleDateFormat("yyyy");
		List<String> types = new ArrayList<String>();
		types.add(CdmKpiInfo.CDM_TYPE_SALES);
		params.put("types", types);
		params.put("kpiYear", dayDate.format(startDay));
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);
		if(!queryByParams.isEmpty()) {
			SimpleDateFormat monDate = new SimpleDateFormat("MM");
			Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(queryByParams.get(0),Integer.valueOf(monDate.format(startDay)));
			//获取目标数
			appCdmSalesKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
		}
		 
		resMap.setDataResult(appCdmSalesKpiInfo);
		return resMap;
	}

	@Override
	public JsonResponse getCdmKpiFinishListByCondition(CdmKpiInfoVo cdmKpiInfoVo) {
        cdmKpiInfoVo.setKpiYear(2020l);
        cdmKpiInfoVo.setStartDate("2020-12-01");
		JsonResponse resMap = new JsonResponse();
		WxTUser curUser = ContextUtil.getCurUser();
		String userCai = curUser.getCai();
		//权限控制
		try {
			//获取当前用户的查看权限
			int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || curUser.getUserId() == 1L) {
				permissionWeight = 1;
			}else if(2 == (permissionWeight&2)) {
				permissionWeight = 2;
				cdmKpiInfoVo.setBuManagerCai(userCai);
			}else if(4 == (permissionWeight&4)) {
				permissionWeight = 4;
				cdmKpiInfoVo.setChannelManagerCai(userCai);
			}else if(8 == (permissionWeight&8)) {
				permissionWeight = 8;
				cdmKpiInfoVo.setSuppervisorCai(userCai);
			}else if(128 == (permissionWeight&128)) {
				permissionWeight = 128;
				cdmKpiInfoVo.setTeamLeaderCai(userCai);
			}else if(16 == (permissionWeight&16)) {
				cdmKpiInfoVo.setSalesCai(userCai);
			}else if(32 == (permissionWeight&32)){
				String userModel = curUser.getUserModel();
				if(WxTUser.USER_MODEL_SP.equals(userModel)) {						
					permissionWeight = 32; 
					cdmKpiInfoVo.setPartnerId(curUser.getOrgId());
				}
			}else if(64 == (permissionWeight&64)) {
				permissionWeight = 1;
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		
		

		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-01-01");
		SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMM");	
		//String date = dateFormat1.format(new Date());
		String date = cdmKpiInfoVo.getStartDate();
		Date startDate = DateUtil.parseDate(date, "yyyy-MM-01");
		Date endDate = DateUtil.addMonths(DateUtil.parseDate(date,"yyyy-MM-dd"), 1);
		
		cdmKpiInfoVo.setEndDate(dateFormat.format(endDate));
		cdmKpiInfoVo.setTmmUser(cdmKpiInfoVo.getTmmUser() == null ? false : cdmKpiInfoVo.getTmmUser());
		cdmKpiInfoVo.setPaging(false);
		Map<Long, CdmKpiFinishVo> finishMap = new HashMap<Long, CdmKpiFinishVo>();
		
		//经销商处理
		Map<Long , CdmKpiFinishVo> orgMap = new HashMap<Long, CdmKpiFinishVo>();
		
		Integer mon = Integer.valueOf(DateUtil.getMonth(startDate));
		
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("kpiYear", cdmKpiInfoVo.getKpiYear());
		String maxEffectiveTime = cdmKpiIntegralHisInfoMapper.getMaxEffectiveTime(paraMap);
		if(null == maxEffectiveTime) {
			startDate = DateUtil.parseDate(simpleDateFormat1.format(startDate), "yyyy-MM-01");
		}else {
			startDate =   DateUtil.addMonths(DateUtil.parseDate(maxEffectiveTime, "yyyy-MM"),1);
		}
		cdmKpiInfoVo.setStartDate(dateFormat.format(startDate));
		if(!cdmKpiInfoVo.getTmmUser()) {

			//查询历史数据
			//查询数据
			//List<CdmKpiInfoVo> cdmKpiFinishListByCondition = cdmKpiInfoMapper.getCdmKpiFinishListByCondition(cdmKpiInfoVo);
			List<CdmKpiInfoVo> cdmKpiFinishListByCondition = cdmKpiInfoBizService.getCdmKpiFinishList(cdmKpiInfoVo);
			//查询sell in 信息
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("year", cdmKpiInfoVo.getKpiYear());
			params.put("productChannel", "Consumer");
			params.put("month",dateFormat1.format(DateUtil.parseDate(date, "yyyy-MM-01")));
			Map<Long, CdmKpiFinishVo> sellInMap = cdmKpiInfoBizService.getSellInList(params);
			//处理Dsr数据
			for (CdmKpiInfoVo cdmKpiInfoVo2 : cdmKpiFinishListByCondition) {
				
				CdmKpiFinishVo cdmKpiFinishVo = finishMap.get(cdmKpiInfoVo2.getDsrId());
				CdmKpiFinishVo cdmKpiCurFinishVo = orgMap.get(cdmKpiInfoVo2.getOrgId());
				
				//处理经销商
				if(null == cdmKpiCurFinishVo) {
					cdmKpiCurFinishVo = new CdmKpiFinishVo();
					cdmKpiCurFinishVo.setCustomerNameCn(cdmKpiInfoVo2.getCustomerNameCn());
					cdmKpiCurFinishVo.setRegionName(cdmKpiInfoVo2.getRegionName());
					cdmKpiCurFinishVo.setSuppervisorName(cdmKpiInfoVo2.getSuppervisorName());
					cdmKpiCurFinishVo.setSalesName(cdmKpiInfoVo2.getSalesName());
					cdmKpiCurFinishVo.setId(cdmKpiInfoVo2.getId());
					cdmKpiCurFinishVo.setOrgId(cdmKpiInfoVo2.getOrgId());
					cdmKpiCurFinishVo.setKpiYear(cdmKpiInfoVo2.getKpiYear());
					CdmKpiFinishVo cdmKpiFinishVo2 = sellInMap.get(cdmKpiInfoVo2.getDistributorId());
					if(null != cdmKpiFinishVo2) {
						//经销商添加SN+ 销量 
						cdmKpiCurFinishVo.setFactSnSalesNum(cdmKpiFinishVo2.getFactSnSalesNum());
						cdmKpiCurFinishVo.setYTDSnSalesNum(cdmKpiFinishVo2.getYTDSnSalesNum());
						//经销商添加sell in 销量 
						cdmKpiCurFinishVo.setFactSellInNum(cdmKpiFinishVo2.getFactSellInNum());
						cdmKpiCurFinishVo.setYTDSellInNum(cdmKpiFinishVo2.getYTDSellInNum());
					}
				}
				if(cdmKpiFinishVo == null) {
					cdmKpiFinishVo = new CdmKpiFinishVo();
					cdmKpiFinishVo.setCustomerNameCn(cdmKpiInfoVo2.getCustomerNameCn());
					cdmKpiFinishVo.setRegionName(cdmKpiInfoVo2.getRegionName());
					cdmKpiFinishVo.setSuppervisorName(cdmKpiInfoVo2.getSuppervisorName());
					cdmKpiFinishVo.setSalesName(cdmKpiInfoVo2.getSalesName());
					cdmKpiFinishVo.setDsrName(cdmKpiInfoVo2.getDsrName());
					cdmKpiFinishVo.setId(cdmKpiInfoVo2.getId());
					cdmKpiFinishVo.setOrgId(cdmKpiInfoVo2.getOrgId());
					cdmKpiFinishVo.setDsrId(cdmKpiInfoVo2.getDsrId());
					cdmKpiFinishVo.setKpiYear(cdmKpiInfoVo2.getKpiYear());
				}
				
				//添加KPI数据
				if(cdmKpiInfoVo2.getKpiType().equals("settingVisit")) {
					//统计DSR YTD目标
					Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiInfoVo2,mon);
					cdmKpiFinishVo.setYTDVisitNum(countArimAndYtdArim.get("ytdNum"));
					cdmKpiFinishVo.setVisitAimsByYearNum(cdmKpiInfoVo2.getYearGoals());
					cdmKpiFinishVo.setFactVisitNum(cdmKpiInfoVo2.getFaceVisitNum());
					
					//统计经销商 
					Integer factVisitNum = cdmKpiCurFinishVo.getFactVisitNum();
					if(null == factVisitNum) {
						factVisitNum  = cdmKpiInfoVo2.getFaceVisitNum();
					}else {
						factVisitNum += cdmKpiInfoVo2.getFaceVisitNum()==null ? 0 : cdmKpiInfoVo2.getFaceVisitNum();
					}
					cdmKpiCurFinishVo.setFactVisitNum(factVisitNum);
					orgMap.put(cdmKpiInfoVo2.getOrgId(),cdmKpiCurFinishVo);
				}
				
				if(cdmKpiInfoVo2.getKpiType().equals("settingEntry")) {
					//统计YTD目标
					Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiInfoVo2,mon);
					cdmKpiFinishVo.setYTDEntryNum(countArimAndYtdArim.get("ytdNum"));
					cdmKpiFinishVo.setEntryAimsByYearNum(cdmKpiInfoVo2.getYearGoals());
					cdmKpiFinishVo.setFactEntryNum(cdmKpiInfoVo2.getFactEntryNum());
					
					Integer factEntryNum = cdmKpiCurFinishVo.getFactEntryNum();
					if(null == factEntryNum) {
						factEntryNum  = cdmKpiInfoVo2.getFactEntryNum();
					}else {
						factEntryNum += cdmKpiInfoVo2.getFactEntryNum() == null ? 0 : cdmKpiInfoVo2.getFactEntryNum();
					}
					cdmKpiCurFinishVo.setFactEntryNum(factEntryNum);
					orgMap.put(cdmKpiInfoVo2.getOrgId(),cdmKpiCurFinishVo);
				}
				if(cdmKpiInfoVo2.getKpiType().equals("settingSales")) {
					//统计YTD目标
					//cdmKpiFinishVo.setFactSnSalesNum(cdmKpiInfoVo2.getFactSnSalesNum());
					Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiInfoVo2,mon);
					cdmKpiFinishVo.setYTDSalesNum(countArimAndYtdArim.get("ytdNum"));
					cdmKpiFinishVo.setFactSalesNum(cdmKpiInfoVo2.getFactSalesNum());
					cdmKpiFinishVo.setSalesAimsByYearNum(cdmKpiInfoVo2.getYearGoals());
					
					Double factSalesNum = cdmKpiCurFinishVo.getFactSalesNum();
					if(null == factSalesNum) {
						factSalesNum  = cdmKpiInfoVo2.getFactSalesNum();
					}else {
						factSalesNum += cdmKpiInfoVo2.getFactSalesNum() == null ? 0 : cdmKpiInfoVo2.getFactSalesNum();
					}
					cdmKpiCurFinishVo.setFactSalesNum(factSalesNum);
					orgMap.put(cdmKpiInfoVo2.getOrgId(),cdmKpiCurFinishVo);
				}
		/*		if(cdmKpiInfoVo2.getKpiType().equals("settingTMMOrder")) {
					//统计YTD目标
					cdmKpiFinishVo.setFactTmmNum(cdmKpiInfoVo2.getFactTmmNum());
					//cdmKpiFinishVo.setYTDTmmNum(yTDTmmNum);
					cdmKpiFinishVo.setTmmAimsByYearNum(cdmKpiInfoVo2.getYearGoals());
				}*/
				
				//回写map
				finishMap.put(cdmKpiInfoVo2.getDsrId(), cdmKpiFinishVo);
				orgMap.put(cdmKpiInfoVo2.getOrgId(), cdmKpiCurFinishVo);
			}
			
			//查询经销商信息
			Set<Long> idsSet = orgMap.keySet();
			List<Long> idsList = new ArrayList<Long>();
			Iterator<Long> iterator = idsSet.iterator();
			while(iterator.hasNext()) {
				idsList.add(iterator.next());
			}
			cdmKpiInfoVo.setOrgIds(idsList);
			List<CdmKpiInfoVo> cdmKpiInfoForCusList = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
			if(!cdmKpiInfoForCusList.isEmpty()) {
				for (CdmKpiInfoVo cdmKpiCursInfoVo : cdmKpiInfoForCusList) {
					CdmKpiFinishVo cdmKpiFinishVo = orgMap.get(cdmKpiCursInfoVo.getOrgId());
					if(null == cdmKpiFinishVo) {
						cdmKpiFinishVo = new CdmKpiFinishVo();
					}
					if("settingVisit".equals(cdmKpiCursInfoVo.getKpiType())) {
						Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiCursInfoVo,mon);
						cdmKpiFinishVo.setYTDVisitNum(countArimAndYtdArim.get("ytdNum"));
						cdmKpiFinishVo.setVisitAimsByYearNum(cdmKpiCursInfoVo.getYearGoals());
					}
					if("settingEntry".equals(cdmKpiCursInfoVo.getKpiType())) {
						Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiCursInfoVo,mon);
						cdmKpiFinishVo.setYTDEntryNum(countArimAndYtdArim.get("ytdNum"));
						cdmKpiFinishVo.setEntryAimsByYearNum(cdmKpiCursInfoVo.getYearGoals());
					}
					if("settingSales".equals(cdmKpiCursInfoVo.getKpiType())) {
						Map<String, Double> countArimAndYtdArim = CountArimAndYtdArim(cdmKpiCursInfoVo,mon);
						cdmKpiFinishVo.setSalesAimsByYearNum(cdmKpiCursInfoVo.getYearGoals());
						cdmKpiFinishVo.setYTDSalesNum(countArimAndYtdArim.get("ytdNum"));
					}
					orgMap.put(cdmKpiCursInfoVo.getOrgId(), cdmKpiFinishVo);
				}
			}
			finishMap.putAll(orgMap);
		}else {
			//查看TMM数据
		}
		Collection<CdmKpiFinishVo> valueCollection2 = finishMap.values();
		
	    List<CdmKpiFinishVo> resList= new ArrayList<CdmKpiFinishVo>(valueCollection2);
		resMap.setListResult(resList);
		
		resMap.setTotalOfPaging(cdmKpiInfoVo.getTotalCount());
		return resMap;
		} catch (WxPltException e) {
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,e.getMessage());
			return resMap;
		}catch (Exception e) {
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,e.getMessage());
			return resMap;
		}
	}
	
	//统计当月目标数以及YTD数
	public Map<String, Double> CountArimAndYtdArim(CdmKpiInfoVo cdmKpiInfo,Integer mon) {
		Map<String, Double> resMap = new HashMap<String , Double>();
		Double ytdNum = (double)0;
		Double arimNum = (double)0;
		switch (mon) {
		case 1:
			arimNum = cdmKpiInfo.getMonGoals() == null ? 0 : cdmKpiInfo.getMonGoals();
			break;
		case 2:
			arimNum = cdmKpiInfo.getFebGoals() == null ? 0 : cdmKpiInfo.getFebGoals();
			break;
		case 3:
			arimNum = cdmKpiInfo.getMarGoals() == null ? 0 : cdmKpiInfo.getMarGoals();
			break;
		case 4:
			arimNum = cdmKpiInfo.getAprGoals() == null ? 0 : cdmKpiInfo.getAprGoals();
			break;
		case 5:
			arimNum = cdmKpiInfo.getMayGoals() == null ? 0 : cdmKpiInfo.getMayGoals();
			break;
		case 6:
			arimNum = cdmKpiInfo.getJuneGoals() == null ? 0 : cdmKpiInfo.getJuneGoals();
			break;
		case 7:
			arimNum = cdmKpiInfo.getJulyGoals() == null ? 0 : cdmKpiInfo.getJulyGoals();
			break;
		case 8:
			arimNum = cdmKpiInfo.getAugGoals() == null ? 0 : cdmKpiInfo.getAugGoals();
			break;
		case 9:
			arimNum = cdmKpiInfo.getSeptGoals() == null ? 0 : cdmKpiInfo.getSeptGoals();
			break;
		case 10:
			arimNum = cdmKpiInfo.getOctGoals() == null ? 0 : cdmKpiInfo.getOctGoals();
			break;
		case 11:
			arimNum = cdmKpiInfo.getNovGoals() == null ? 0 : cdmKpiInfo.getNovGoals();
			break;
		case 12:
			arimNum = cdmKpiInfo.getDecGoals() == null ? 0 : cdmKpiInfo.getDecGoals();
			break;		
		default:
			break;
		}
		switch (mon) {
		case 12:
			ytdNum += (cdmKpiInfo.getDecGoals() == null ? 0 : cdmKpiInfo.getDecGoals());
		case 11:
			ytdNum += cdmKpiInfo.getNovGoals()  == null ? 0 : cdmKpiInfo.getNovGoals();
		case 10:
			ytdNum += cdmKpiInfo.getOctGoals()  == null ? 0 : cdmKpiInfo.getOctGoals();
		case 9:
			ytdNum += cdmKpiInfo.getSeptGoals()  == null ? 0 : cdmKpiInfo.getSeptGoals();
		case 8:
			ytdNum += cdmKpiInfo.getAugGoals()  == null ? 0 : cdmKpiInfo.getAugGoals();
		case 7:
			ytdNum += cdmKpiInfo.getJulyGoals()  == null ? 0 : cdmKpiInfo.getJulyGoals();
		case 6:
			ytdNum += cdmKpiInfo.getJuneGoals()  == null ? 0 : cdmKpiInfo.getJuneGoals();
		case 5:
			ytdNum += cdmKpiInfo.getMayGoals()  == null ? 0 : cdmKpiInfo.getMayGoals();
		case 4:
			ytdNum += cdmKpiInfo.getAprGoals()  == null ? 0 : cdmKpiInfo.getAprGoals();
		case 3:
			ytdNum += cdmKpiInfo.getMarGoals()  == null ? 0 : cdmKpiInfo.getMarGoals();
		case 2:
			ytdNum += cdmKpiInfo.getFebGoals()  == null ? 0 : cdmKpiInfo.getFebGoals();
		case 1:
			ytdNum += cdmKpiInfo.getMonGoals() == null ? 0 : cdmKpiInfo.getMonGoals();
			break;
		default:
			break;
		}
		resMap.put("ytdNum", ytdNum);
		resMap.put("arimNum", arimNum);
		return resMap;
	}
	

	@Override
	public JsonResponse getPartnerNamList(String partnerName) {
		JsonResponse resMap = new JsonResponse();
		WxTUser curUser = ContextUtil.getCurUser();
		String userCai = curUser.getCai();
		CdmKpiInfoVo cdmKpiInfoVo = new CdmKpiInfoVo();
		cdmKpiInfoVo.setPaging(false);
		cdmKpiInfoVo.setField("customerNameCn");
		if(!partnerName.isEmpty()) {
			cdmKpiInfoVo.setCustomerNameCn(partnerName);
		}
		
		//权限控制
		try {
			//获取当前用户的查看权限
			int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "TaskPage.CDM.KPI");
			if(1 == (permissionWeight&1) || curUser.getUserId() == 1L) {
				permissionWeight = 1;
			}else if(2 == (permissionWeight&2)) {
				permissionWeight = 2;
				cdmKpiInfoVo.setBuManagerCai(userCai);
			}else if(4 == (permissionWeight&4)) {
				permissionWeight = 4;
				cdmKpiInfoVo.setChannelManagerCai(userCai);
			}else if(8 == (permissionWeight&8)) {
				permissionWeight = 8;
				cdmKpiInfoVo.setSuppervisorCai(userCai);
			}else if(128 == (permissionWeight&128)) {
				permissionWeight = 128;
				cdmKpiInfoVo.setTeamLeaderCai(userCai);
			}else if(32 == (permissionWeight&32)){
				String userModel = curUser.getUserModel();
				if(WxTUser.USER_MODEL_SP.equals(userModel)) {						
					permissionWeight = 32; 
					cdmKpiInfoVo.setPartnerId(curUser.getOrgId());
				}
			}else if(64 == (permissionWeight&64)) {
				permissionWeight = 1;
			}else {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
				return resMap;
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
			return resMap;
		}
		List<String> partnerNamList = cdmKpiInfoMapper.getPartnerNamList(cdmKpiInfoVo);
		resMap.setListResult(partnerNamList);
		return resMap;
	}

	@Override
	@Transactional
	public JsonResponse deleteCdmKpiInfoByDsr(CdmKpiInfo cdmKpiInfo) {
		JsonResponse resMap = new JsonResponse();
		//清空CDM KPI 数据
		cdmKpiInfo.setTotalPoints(null);
		cdmKpiInfo.setRemainingPoints(null);
		cdmKpiInfoMapper.updateByPrimaryKeySelective(cdmKpiInfo);
		//清空CDM KPI History 数据
		cdmKpiHistoryMapper.deleteByCdmKpiId(cdmKpiInfo.getId());
		return resMap;
	}

	@Override
	public JsonResponse findDsrIsJionCdmKpi(Integer year,Long userId) {
		JsonResponse resMap = new JsonResponse();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("dsrId", userId == null ? ContextUtil.getCurUserId() : userId);
		params.put("kpiYear",year);
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);
		if(queryByParams.isEmpty()) {
			resMap.put("JionCdmKpi", false);
		}else {
			resMap.put("JionCdmKpi", true);
		}
		Long selectTmmByUserId = cdmKpiInfoMapper.selectTmmByUserId(userId == null ? ContextUtil.getCurUserId() : userId);
		if(null == selectTmmByUserId) {
			resMap.put("TmmUser", false);
		}else {
			resMap.put("TmmUser", true);
		}
		return resMap;
	}

	@Override
    public JsonResponse providePoints(String provideTime) throws WxPltException {
        Long userId = ContextUtil.getCurUserId();
        if (!lockerBizService.lock(LOCKER_ID, LOCKER_NAME, LOCK_EFFICTIVE_TIME, LockerBizServiceImpl.SERVER_ID, userId)) {
            throw new WxPltException("重复操作");
        }
        try {
            return doCmdProvidePoint(provideTime);
        } finally {
            lockerBizService.unlock(LOCKER_ID, LockerBizServiceImpl.SERVER_ID, userId);
        }
    }

	protected JsonResponse doCmdProvidePoint(String provideTime){
        JsonResponse resMap = new JsonResponse();
        //权限控制
        WxTUser curUser = ContextUtil.getCurUser();
        try {
            int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "TaskPage.CDM.KPI");
            if(1 == (permissionWeight&1) || curUser.getUserId() == 1L) {
                permissionWeight = 1;
            }else {
                resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
                resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
                return resMap;
            }
        } catch (WxPltException e) {
            e.printStackTrace();
            resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_NONE_PERMISSION.getCode());
            resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_NONE_PERMISSION.getErrorMsg());
            return resMap;
        }
        //检测当月积分是否发放
        Date parseDate = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        try {
            parseDate= simpleDateFormat.parse(provideTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String kpiYear = DateUtil.getYear(parseDate);
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("kpiYear", Long.valueOf(kpiYear));
		String maxEffectiveTime = cdmKpiIntegralHisInfoMapper.getMaxEffectiveTime(paraMap);
		if(maxEffectiveTime != null) {
			int compareTo = maxEffectiveTime.compareTo(simpleDateFormat.format(parseDate));
			if(compareTo >= 0 ) {
				resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
				resMap.put(JsonResponse.KEY_ERROR_MSG,"当月积分已经发放");
				return resMap;
			}
		}
		try {
            cdmKpiInfoBizService.grantBonus(provideTime);
        }catch (Exception e){
            e.printStackTrace();
            resMap.put(Constants.RESULT_CODE_KEY,Constants.RESULT_ERROR);
            resMap.put(Constants.RESULT_ERROR_MSG_KEY,e.getMessage());
        }
        return resMap;
    }

	@Override
	public void sendEmail(String yearMonth,Long orgId) {
		try {
			//获取邮件发送配置
			String[] to = null;
			String[] cc = null;
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("orgId", orgId);
			List<WxTUser> userList = new ArrayList<WxTUser>();
			userList = cdmKpiInfoMapper.getToList(param);
			List<String> toList = new ArrayList<String>();
			for(WxTUser user:userList) {
				if(user.getEmail()!=null) {
					toList.add(user.getEmail());
				}
			}
			to=toList.toArray(new String[toList.size()]);
			Map<String, Object> dicMap = dicService.getDicItemByDicTypeCode("task.monthemail.cdmkpi.comfirm");
			if("success".equals(dicMap.get("result"))){
				List<DicItemVo> list = (List<DicItemVo>) dicMap.get("data");
				if(list != null && !list.isEmpty()){
					for(DicItemVo vo : list){
						if("cc".equals(vo.getDicItemCode())){
							cc = vo.getDicItemDesc().split(",");
						}
					}
				}
			}else{
				throw new WxPltException((String) dicMap.get("errorMsg"));
			}
			WebApplicationContext webApplicationContext = ContextLoader
					.getCurrentWebApplicationContext();
			ServletContext servletContext = webApplicationContext
					.getServletContext();
			String[] strings = yearMonth.split("-");
			
			int year = Integer.parseInt(strings[0]);
			int month = Integer.parseInt(strings[1]);
			String yearMonthChString = "" + year + "年" + month + "月";
			
			SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
			Date startTime = null;
			try {
				startTime = simpleDateFormat2.parse(yearMonth);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("yearMonth",startTime);
			params.put("orgId", orgId);
			List<ExcelExportToEmail4Cdm> dataList = cdmKpiInfoMapper.selecExcelExportToEmailsByYearMonth(params);
			List<ExcelExportToEmail4Cdm> sellList = cdmKpiInfoMapper.selecSellinByYearMonth(params);
			dataList.addAll(sellList);
			List<File> fileList = new ArrayList<File>();
			fileList.add(formFile(yearMonthChString,dataList));
			String customerName = dataList.get(0).getOrganizationName();
			//发送周报邮件
			Map<String, Object> dataMap = new HashMap<String, Object>();
			dataMap.put("honorific", "您好！");
			dataMap.put("customerName", customerName);
			dataMap.put("yearmonth", yearMonthChString);
			dataMap.put("detaillink", "http://wwwstg.cvx-sh.com/login.do");
			dataMap.put("attachmentname", "导出文件");
			
			EmailSendUtils.sendEmailForListContent(servletContext, to, cc, "乘用车雪佛龙合伙人APP DSR积分审核通过通知提醒", dataMap,
					fileList.isEmpty() ? null : fileList.toArray(new File[fileList.size()]), "cdmkpi_confirm_distribute_point.ftl");
			log.info("KPI sendEmail end=============================");
			LogUtils.addInfoLog(1l, "KPIServiceImpl.sendEmail", "乘用车雪佛龙合伙人APP DSR积分审核通过通知提醒");

			
		} catch (Exception e) {
			log.error("KPIServiceImpl error. " + e.getMessage(), e);
			LogUtils.addErrorLog(1l, "KPIServiceImpl.sendEmail", "乘用车雪佛龙合伙人APP DSR积分审核通过通知提醒发送失败。" + e.getMessage(), null);
		}
		
	}
	
	public File formFile(String yearMonthChString,List<ExcelExportToEmail4Cdm> dataList0) {
		try {
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("kpiName", "KPI");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("organizationName", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			exportCol = new ExportCol("dsrName", "销售");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol.setWidth(20);
			exportCol = new ExportCol("kpiCode", "销售类别");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol.setWidth(20);
			exportCol = new ExportCol("allPoint", "获得总积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			exportCol = new ExportCol("leftPoint", "剩余积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);
			exportCol = new ExportCol("kpiValue", "当月完成情况");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			exportCol = new ExportCol("awardPoint", "当月发放积分");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);

			List<Map<String, Object>> dataList = transform(dataList0);

			File file = new File("");
	        String filePath = file.getCanonicalPath();
	        System.out.println(filePath);
	        
			filePath = filePath + "\\" + yearMonthChString + "乘用车DSR积分详情" + ".xlsx";
			FileOutputStream fileOutputStream = new FileOutputStream(filePath);
			
			
			//转换
			
			PoiWriteExcel.exportLargeData(dataList, exportCols, fileOutputStream, "sheet1", true);
			fileOutputStream.flush();
			fileOutputStream.close();
			File file1 = new File(filePath);
			return file1;			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			/*request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());*/
			//return "forward:/common/jsp/downloadError.jsp";
			return null;
		}
	}
	
	public List<Map<String, Object>> transform(List<ExcelExportToEmail4Cdm> dataList){
	     List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
	     for(ExcelExportToEmail4Cdm excelExportToEmail : dataList) {
	    	 Map<String, Object> map = new HashMap<String, Object>();
	    	 map.put( "organizationName", excelExportToEmail.getOrganizationName());
	    	 map.put( "kpiValue", excelExportToEmail.getKpiValue());
	    	 map.put( "dsrName", excelExportToEmail.getDsrName());
	    	 map.put( "kpiName", excelExportToEmail.getKpiName());
	    	 map.put( "kpiCode", excelExportToEmail.getKpiCode());
	    	 if(excelExportToEmail.getKpiName().equals("SN+销量(L)")||excelExportToEmail.getKpiName().equals("Sell-In销量(L)")) {
		    	 map.put("allPoint", "");
		    	 map.put( "leftPoint", "");
		    	 map.put( "awardPoint", "");
	    	 }else {
		    	 map.put("allPoint", excelExportToEmail.getAllPoint());
		    	 map.put( "leftPoint", excelExportToEmail.getLeftPoint());
		    	 map.put( "awardPoint", excelExportToEmail.getAwardPoint());
	    	 }
	    	 newDataList.add(map);
	     }
	     return newDataList;
	}
	
	@Override
    public void sendMobile(String yearMonth, Long orgId) {
		String[] strings = yearMonth.split("-");
		int year = Integer.parseInt(strings[0]);
		int month = Integer.parseInt(strings[1]);
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("orgId", orgId);
		List<WxTUser> userList = new ArrayList<WxTUser>();
		userList = cdmKpiInfoMapper.getToList(param);
		String mobileMessageContent ="您负责区域的"+year+"年"+month+"月乘用车DSR积分已审核通过，请登陆雪佛龙合伙人平台查看。";
        try {
            for (WxTUser tuser : userList) {
                String mobile = tuser.getMobileTel();
                if (null != mobile && !mobile.isEmpty()) {
                    log.info("doSendMobile mobile:" + mobile);
                    SendMessageUtil.sndMessageToPhone(mobile, mobileMessageContent);
                } else {
                    log.info("doSendMobile is null,user:" + tuser.getLoginName());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("doSendMobile" + e.getLocalizedMessage());
        }

    }
	
	@Override
	public JsonResponse getCdmKpiFinishHisListByCondition(CdmKpiInfoVo cdmKpiInfoVo) {
		JsonResponse resMap = new JsonResponse();
		try {
		Map<String, CdmKpiInfoVo> resDataMap = new HashMap<String, CdmKpiInfoVo>();
		
		List<Long> orgIds = cdmKpiInfoVo.getOrgIds();
		cdmKpiInfoVo.setPaging(false);
		//获取dsr/经销商的全年目标数
		List<CdmKpiInfoVo> cdmKpiInfoListByCondition = cdmKpiInfoMapper.getCdmKpiInfoListByCondition(cdmKpiInfoVo);
		if(!cdmKpiInfoListByCondition.isEmpty()) {
			resDataMap.put("ArimCdmKpiInfo", cdmKpiInfoListByCondition.get(0));
		}
		
		//获取dsr/经销商全年实际完成数
		String endTime = cdmKpiInfoVo.getEndDate();
	
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-01-01");
		SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM");
		Date parseDate = simpleDateFormat.parse(endTime);
		String kpiYear = DateUtil.getYear(parseDate);
		CdmKpiInfoVo factCdmKpinInfo = new CdmKpiInfoVo();
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("kpiYear", Long.valueOf(kpiYear));
		String maxEffectiveTime = cdmKpiIntegralHisInfoMapper.getMaxEffectiveTime(paraMap);
		String format = simpleDateFormat1.format(parseDate);
		Date hisDate = simpleDateFormat1.parse(format);
		if(maxEffectiveTime != null) {
			//获取历史数据
			int compareTo = maxEffectiveTime.compareTo(simpleDateFormat2.format(parseDate));
			hisDate = compareTo <= 0  ? simpleDateFormat2.parse(maxEffectiveTime):parseDate;
			CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo = new CdmKpiIntegralHisInfoVo();
			cdmKpiIntegralHisInfoVo.setKpiType(cdmKpiInfoVo.getKpiType());
			cdmKpiIntegralHisInfoVo.setTmmUser(cdmKpiInfoVo.getTmmUser());
			cdmKpiIntegralHisInfoVo.setKpiYear(cdmKpiInfoVo.getKpiYear());
			cdmKpiIntegralHisInfoVo.setDateTo(hisDate);
			cdmKpiIntegralHisInfoVo.setOrgId(cdmKpiInfoVo.getOrgId());
			if(null != cdmKpiInfoVo.getDsrId()) {
				cdmKpiIntegralHisInfoVo.setDsrId(cdmKpiInfoVo.getDsrId());
			}
			List<CdmKpiIntegralHisInfoVo> cdmKpiIntegralHisVo = cdmKpiIntegralHisInfoMapper.getCdmKpiIntegralHisByRole(cdmKpiIntegralHisInfoVo);
			for (CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo2 : cdmKpiIntegralHisVo) {
				getKpiValue(factCdmKpinInfo, cdmKpiIntegralHisInfoVo2);
			}
		}
		//获取实时数据
		if(0>simpleDateFormat2.format(hisDate).compareTo(simpleDateFormat2.format(parseDate))) {
			Date startTime2 = hisDate;
			if(maxEffectiveTime!= null) {
				startTime2 = DateUtil.addMonths(hisDate, 1);
			}
			Date endTime2 = DateUtil.addMonths(parseDate, 1);
			cdmKpiInfoVo.setStartDate(simpleDateFormat.format(startTime2));
			cdmKpiInfoVo.setEndDate(simpleDateFormat.format(endTime2));
			cdmKpiInfoVo.setField("finish_date");
			//根据不通的模块查询
			if(CdmKpiInfo.CDM_TYPE_VISIT.equals(cdmKpiInfoVo.getKpiType())) {
				List<CdmKpiIntegralHisInfoVo> cdmKpiVisitFinishList = cdmKpiInfoMapper.getCdmKpiVisitFinishList(cdmKpiInfoVo);
				for (CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo : cdmKpiVisitFinishList) {
					getKpiValue(factCdmKpinInfo, cdmKpiIntegralHisInfoVo);
				}
			}
			if(CdmKpiInfo.CDM_TYPE_ENTRY.equals(cdmKpiInfoVo.getKpiType())) {
				List<CdmKpiIntegralHisInfoVo> cdmKpiEntryFinishList = cdmKpiInfoMapper.getCdmKpiEntryFinishList(cdmKpiInfoVo);
				for (CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo : cdmKpiEntryFinishList) {
					getKpiValue(factCdmKpinInfo, cdmKpiIntegralHisInfoVo);
				}
			}
			if(CdmKpiInfo.CDM_TYPE_SALES.equals(cdmKpiInfoVo.getKpiType())) {
				List<CdmKpiIntegralHisInfoVo> cdmKpiSalesFinishList = cdmKpiInfoMapper.getCdmKpiSalesFinishList(cdmKpiInfoVo);
				for (CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo : cdmKpiSalesFinishList) {
					getKpiValue(factCdmKpinInfo, cdmKpiIntegralHisInfoVo);
				}
			}
		}
		
		resDataMap.put("FavtCdmKpiInfo", factCdmKpinInfo);
		resMap.setDataResult(resDataMap);
		return resMap;
		} catch (ParseException e) {
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.ERROR_EXCEPTION.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,ResponseStatus.ERROR_EXCEPTION.getErrorMsg());
			return resMap;
		}
	}

	private void getKpiValue(CdmKpiInfoVo factCdmKpinInfo, CdmKpiIntegralHisInfoVo cdmKpiIntegralHisInfoVo2) {
		Integer mon = 0;
		if(null != cdmKpiIntegralHisInfoVo2.getMon()) {
			mon = cdmKpiIntegralHisInfoVo2.getMon();
		}
		if(null != cdmKpiIntegralHisInfoVo2.getFinishDate()) {
			mon = Integer.valueOf(DateUtil.getMonth(DateUtil.parseDate(cdmKpiIntegralHisInfoVo2.getFinishDate())));
		}
		DecimalFormat df = new DecimalFormat("#.00");
		switch (mon) {
		case 12:
			Double kpiValue12 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue12 +=(factCdmKpinInfo.getDecGoals() == null ? 0d : factCdmKpinInfo.getDecGoals());
			factCdmKpinInfo.setDecGoals(Double.valueOf(df.format(kpiValue12+0.0001)));
			break;
		case 11:
            Double kpiValue11 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue11 += (factCdmKpinInfo.getNovGoals()  == null ? 0 : factCdmKpinInfo.getNovGoals());
			factCdmKpinInfo.setNovGoals(Double.valueOf(df.format(kpiValue11+0.0001)));
			break;
		case 10:
            Double kpiValue10 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue10 += (factCdmKpinInfo.getOctGoals()  == null ? 0 : factCdmKpinInfo.getOctGoals());
			factCdmKpinInfo.setOctGoals(Double.valueOf(df.format(kpiValue10+0.0001)));
			break;
		case 9:
            Double kpiValue9 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue9 += (factCdmKpinInfo.getSeptGoals()  == null ? 0 : factCdmKpinInfo.getSeptGoals());
			factCdmKpinInfo.setSeptGoals(Double.valueOf(df.format(kpiValue9+0.0001)));
			break;
		case 8:
            Double kpiValue8 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue8 += (factCdmKpinInfo.getAugGoals()  == null ? 0 : factCdmKpinInfo.getAugGoals());
			factCdmKpinInfo.setAugGoals(Double.valueOf(df.format(kpiValue8+0.0001)));
			break;
		case 7:
            Double kpiValue7 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue7 += (factCdmKpinInfo.getJulyGoals()  == null ? 0 : factCdmKpinInfo.getJulyGoals());
			factCdmKpinInfo.setJulyGoals(Double.valueOf(df.format(kpiValue7)));
			break;
		case 6:
            Double kpiValue6 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue6 += (factCdmKpinInfo.getJuneGoals()  == null ? 0 : factCdmKpinInfo.getJuneGoals());
			factCdmKpinInfo.setJuneGoals(Double.valueOf(df.format(kpiValue6+0.0001)));
			break;
		case 5:
            Double kpiValue5 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue5 += (factCdmKpinInfo.getMayGoals()  == null ? 0 : factCdmKpinInfo.getMayGoals());
			factCdmKpinInfo.setMayGoals(Double.valueOf(df.format(kpiValue5+0.0001)));
			break;
		case 4:
            Double kpiValue4 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue4 += (factCdmKpinInfo.getAprGoals()  == null ? 0 : factCdmKpinInfo.getAprGoals());
			factCdmKpinInfo.setAprGoals(Double.valueOf(df.format(kpiValue4+0.0001)));
			break;
		case 3:
            Double kpiValue3 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue3 += (factCdmKpinInfo.getMarGoals()  == null ? 0 : factCdmKpinInfo.getMarGoals());
			factCdmKpinInfo.setMarGoals(Double.valueOf(df.format(kpiValue3+0.0001)));
			break;
		case 2:
            Double kpiValue2 = cdmKpiIntegralHisInfoVo2.getKpiValue()== null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue2 += (factCdmKpinInfo.getFebGoals()  == null ? 0 : factCdmKpinInfo.getFebGoals());
			factCdmKpinInfo.setFebGoals(Double.valueOf(df.format(kpiValue2+0.0001)));
			break;
		case 1:
            Double kpiValue1 = cdmKpiIntegralHisInfoVo2.getKpiValue() == null ? 0 : cdmKpiIntegralHisInfoVo2.getKpiValue();
			kpiValue1 += (factCdmKpinInfo.getMonGoals() == null ? 0 : factCdmKpinInfo.getMonGoals());
			factCdmKpinInfo.setMonGoals(Double.valueOf(df.format(kpiValue1+0.0001)));
			break;
		default:
			break;
		}
	}

	@Override
	public JsonResponse appGetTmmFinishInfo(String date) {
		JsonResponse resMap = new JsonResponse();
		try {
		WxTUser user = ContextUtil.getCurUser();
		
		Map<String,Object> tmmFinishMap = new HashMap<String, Object>();
		//获取新客户录入数
		Map<String, Object> params = new HashMap<String, Object>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
		Date startDay = DateUtil.parseDate(date,"yyyy-MM-dd");
		Date endDay = DateUtil.addMonths(startDay, 1);
		params.put("dsrId", user.getUserId());
		params.put("startDay", dateFormat.format(startDay));
		params.put("endDay", dateFormat.format(endDay));
		 //获取CDM新客户录入详情
		List<AppCdmKpiDisInfoVO> appGetCDMEntryInfo = cdmKpiInfoMapper.appGetCDMEntryInfo(params);
		
		AppCdmKpiDisInfoVO appCdmTmmEntryKpiInfo = new AppCdmKpiDisInfoVO();
		Long commitNum = 0L; Long mktConfirmNum = 0L;
		for (AppCdmKpiDisInfoVO appCdmKpiDisInfoVO : appGetCDMEntryInfo) {
			commitNum += appCdmKpiDisInfoVO.getCommitNum();
			if(appCdmKpiDisInfoVO.getMktConfirmNum() != null && appCdmKpiDisInfoVO.getMktConfirmNum() == 3) {
				mktConfirmNum += appCdmKpiDisInfoVO.getCommitNum();
			}
			
		}
		
		//TODO 可以读取历史表数据
		Long partnerConfirmNum = cdmKpiInfoMapper.countPartnerConfirmCDMEntry(params);
		appCdmTmmEntryKpiInfo.setCommitNum(commitNum);
		appCdmTmmEntryKpiInfo.setPartnerConfirmNum(partnerConfirmNum);
		appCdmTmmEntryKpiInfo.setMktConfirmNum(mktConfirmNum);
		
		//获取kpi设置详情
		SimpleDateFormat dayDate = new SimpleDateFormat("yyyy");
		List<String> types = new ArrayList<String>();
		types.add(CdmKpiInfo.CDM_TYPE_TMM_ENTRY);
		types.add(CdmKpiInfo.CDM_TYPE_TMM_ORDER);
		types.add(CdmKpiInfo.CDM_TYPE_TMM_SALES);
		types.add(CdmKpiInfo.CDM_TYPE_TMM_SNABOVE);
		params.put("types", types);
		params.put("kpiYear", dayDate.format(startDay));
		params.put("tmm", true);
		List<CdmKpiInfo> queryByParams = cdmKpiInfoMapper.queryByParams(params);
	
		
		//获取销量信息
		List<AppCdmKpiDisInfoVO> appGetCDMSalesInfo = cdmKpiInfoMapper.appGetCDMTMMFinishInfo(params);
		AppCdmKpiDisInfoVO appCdmTmmOrderKpiInfo = new AppCdmKpiDisInfoVO();
		AppCdmKpiDisInfoVO appCdmTmmSalesKpiInfo = new AppCdmKpiDisInfoVO();
		AppCdmKpiDisInfoVO appCdmTMMSnAboverKpiInfo = new AppCdmKpiDisInfoVO();
		commitNum = 0L; partnerConfirmNum = 0L; Long outSalesNum = 0L;
		Long snCommitNum = 0L; Long snPartnerConfirmNum = 0L; Long snOutSalesNum = 0L;
		Map<Long, String> workShopNumMap = new HashMap<Long, String>();
		Set<Long> orderConfirmSet = new HashSet<Long>();
		for (AppCdmKpiDisInfoVO appCdmKpiDisInfoVO : appGetCDMSalesInfo) {
			if("SN Above".equals(appCdmKpiDisInfoVO.getProductProperty())) {
				snCommitNum += appCdmKpiDisInfoVO.getFactNum();
				if(appCdmKpiDisInfoVO.getPartnerConfirmNum() != null && appCdmKpiDisInfoVO.getPartnerConfirmNum() == 1) {
					snPartnerConfirmNum += appCdmKpiDisInfoVO.getFactNum();
					/*//获取经销商老板确定的订货客户数
					orderConfirmSet.add(appCdmKpiDisInfoVO.getOtherNum());*/
				}
				if(appCdmKpiDisInfoVO.getOutSalesNum() != null && appCdmKpiDisInfoVO.getOutSalesNum() == 11) {
					snOutSalesNum += appCdmKpiDisInfoVO.getFactNum();
					
					//获取销量已确定的订货客户数
					orderConfirmSet.add(appCdmKpiDisInfoVO.getOtherNum());
				}
			}
			commitNum += appCdmKpiDisInfoVO.getFactNum();
			if(appCdmKpiDisInfoVO.getPartnerConfirmNum() != null && appCdmKpiDisInfoVO.getPartnerConfirmNum() == 1) {
				partnerConfirmNum += appCdmKpiDisInfoVO.getFactNum();
			}
			if(appCdmKpiDisInfoVO.getOutSalesNum() != null && appCdmKpiDisInfoVO.getOutSalesNum() == 11) {
				outSalesNum += appCdmKpiDisInfoVO.getFactNum();
			}
			workShopNumMap.put(appCdmKpiDisInfoVO.getOtherNum(), appCdmKpiDisInfoVO.getOtherNum().toString());
		}
		
		//设置订货客户数
		appCdmTmmOrderKpiInfo.setFactNum(Long.valueOf(workShopNumMap.size()));
		appCdmTmmOrderKpiInfo.setCommitNum(Long.valueOf(workShopNumMap.size()));
		appCdmTmmOrderKpiInfo.setPartnerConfirmNum(Long.valueOf(orderConfirmSet.size()));

		//设置销量
		appCdmTmmSalesKpiInfo.setCommitNum(commitNum);
		appCdmTmmSalesKpiInfo.setPartnerConfirmNum(partnerConfirmNum);
		appCdmTmmSalesKpiInfo.setOutSalesNum(outSalesNum);
	
		//设置高端销量
		appCdmTMMSnAboverKpiInfo.setCommitNum(snCommitNum);
		appCdmTMMSnAboverKpiInfo.setPartnerConfirmNum(snPartnerConfirmNum);
		appCdmTMMSnAboverKpiInfo.setOutSalesNum(snOutSalesNum);
		
		SimpleDateFormat monDate = new SimpleDateFormat("MM");
		SimpleDateFormat yearDate = new SimpleDateFormat("yyyy");
		if(!queryByParams.isEmpty()) {
			for (CdmKpiInfo cdmKpiInfo : queryByParams) {
				if(CdmKpiInfo.CDM_TYPE_TMM_ENTRY.equals(cdmKpiInfo.getKpiType())) {
					Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(cdmKpiInfo,Integer.valueOf(monDate.format(startDay)));
					//获取目标数
					appCdmTmmEntryKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
				}
				if(CdmKpiInfo.CDM_TYPE_TMM_ORDER.equals(cdmKpiInfo.getKpiType())) {
					Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(cdmKpiInfo,Integer.valueOf(monDate.format(startDay)));
					//获取目标数
					appCdmTmmOrderKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
				}
				if(CdmKpiInfo.CDM_TYPE_TMM_SALES.equals(cdmKpiInfo.getKpiType())) {
					Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(cdmKpiInfo,Integer.valueOf(monDate.format(startDay)));
					//获取目标数
					appCdmTmmSalesKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
				}
				if(CdmKpiInfo.CDM_TYPE_TMM_SNABOVE.equals(cdmKpiInfo.getKpiType())) {
					Map<String, Double> ArimAndYtdArimMap = this.CountArimAndYtdArim(cdmKpiInfo,Integer.valueOf(monDate.format(startDay)));
					//获取目标数
					appCdmTMMSnAboverKpiInfo.setArimNum(ArimAndYtdArimMap.get("arimNum"));
				}
			}
		}
		
		//获取累计值
		
			TmmBonusCalParam tmmBonusCalParam = new TmmBonusCalParam();
			List<Long> dsrIds = new ArrayList<Long>();
			dsrIds.add(user.getUserId());
			tmmBonusCalParam.setDsrIds(dsrIds);
			tmmBonusCalParam.setProvideTime(date);
			List<TmmCollectKpiDTO> tmmCollectKpiDTOS = dsrKpiService.cdmTmmBonusDetail(tmmBonusCalParam);
			List<TmmPerformaceDTO> tmmDatas = tmmCollectKpiDTOS.get(0).getTmmDatas();

			AppCdmTmmAddFinishInfoVo entryAddFinishInfo = new AppCdmTmmAddFinishInfoVo();
			AppCdmTmmAddFinishInfoVo orderAddFinishInfo = new AppCdmTmmAddFinishInfoVo();
			AppCdmTmmAddFinishInfoVo salesAddFinishInfo = new AppCdmTmmAddFinishInfoVo();
			AppCdmTmmAddFinishInfoVo snAboveAddFinishInfo = new AppCdmTmmAddFinishInfoVo();
			
			if(!CollectionUtils.isEmpty(tmmDatas)) {
				Map<String, TmmPerformaceDTO> tmmAddInfoMap = new HashMap<String, TmmPerformaceDTO>();
				for (TmmPerformaceDTO tmmPerformaceDTO : tmmDatas) {
					String tmmKey = tmmPerformaceDTO.getKpiType()+"/"+tmmPerformaceDTO.getYear()+"/"+tmmPerformaceDTO.getMonth();
					if(null == tmmAddInfoMap.get(tmmKey)) {
						tmmAddInfoMap.put(tmmKey, tmmPerformaceDTO);
					}
				}
				
				String tmmEndKey = yearDate.format(startDay)+"/"+Integer.valueOf(monDate.format(startDay));

				TmmPerformaceDTO tmmPerformaceDTO = null;
				tmmPerformaceDTO = tmmAddInfoMap.get(CdmKpiInfo.CDM_TYPE_TMM_ENTRY+"/"+tmmEndKey);
				if(null != tmmPerformaceDTO) {
					setCdmTmmFinishInfo(entryAddFinishInfo, tmmPerformaceDTO);
				}
					
				tmmPerformaceDTO = tmmAddInfoMap.get(CdmKpiInfo.CDM_TYPE_TMM_ORDER+"/"+tmmEndKey);
				if(null != tmmPerformaceDTO) {
					setCdmTmmFinishInfo(orderAddFinishInfo, tmmPerformaceDTO);
				}
				
				tmmPerformaceDTO = tmmAddInfoMap.get(CdmKpiInfo.CDM_TYPE_TMM_SALES+"/"+tmmEndKey);
				if(null != tmmPerformaceDTO) {
					setCdmTmmFinishInfo(salesAddFinishInfo, tmmPerformaceDTO);
				}
				
				tmmPerformaceDTO = tmmAddInfoMap.get(CdmKpiInfo.CDM_TYPE_TMM_SNABOVE+"/"+tmmEndKey);
				if(null != tmmPerformaceDTO) {
					setCdmTmmFinishInfo(snAboveAddFinishInfo, tmmPerformaceDTO);
				}
			}
			
		tmmFinishMap.put("entryAddFinishInfo", entryAddFinishInfo);
		tmmFinishMap.put("orderAddFinishInfo", orderAddFinishInfo);
		tmmFinishMap.put("salesAddFinishInfo", salesAddFinishInfo);
		tmmFinishMap.put("snAboveAddFinishInfo", snAboveAddFinishInfo);
		tmmFinishMap.put("appCdmTmmEntryKpiInfo", appCdmTmmEntryKpiInfo);
		tmmFinishMap.put("appCdmTmmOrderKpiInfo", appCdmTmmOrderKpiInfo);
		tmmFinishMap.put("appCdmTmmSalesKpiInfo", appCdmTmmSalesKpiInfo);
		tmmFinishMap.put("appCdmTMMSnAboverKpiInfo", appCdmTMMSnAboverKpiInfo);

		resMap.setDataResult(tmmFinishMap);
		return resMap;
		} catch (Exception e) {
			log.info("TMM查询绩效报错 ："+e.getMessage());
			resMap.put(JsonResponse.KEY_CODE,ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG,"查询TMM绩效失败");
			return resMap;
		}
	}

	private void setCdmTmmFinishInfo(AppCdmTmmAddFinishInfoVo entryAddFinishInfo,
			TmmPerformaceDTO tmmPerformaceDTO) {
		entryAddFinishInfo.setDsrId(tmmPerformaceDTO.getDsrId());
		entryAddFinishInfo.setKpiType(tmmPerformaceDTO.getKpiType());
		entryAddFinishInfo.setKpiValues(tmmPerformaceDTO.getKpiValue());
		entryAddFinishInfo.setKpiTragetValues(tmmPerformaceDTO.getKpiTarget());
		entryAddFinishInfo.setKpiProportion(tmmPerformaceDTO.getKpiPercent());
		entryAddFinishInfo.setKpiMonthValues(tmmPerformaceDTO.getKpiMonthValue());
		entryAddFinishInfo.setKpiMonthTarget(tmmPerformaceDTO.getKpiMonthTarget());
	}
}
