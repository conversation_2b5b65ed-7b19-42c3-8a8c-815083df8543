package com.chevron.pms.model;

import com.chevron.exportdata.ExpAnnotation;

/**
 * 
 * @Author: bo.liu  2017-3-15 下午4:50:14 
 * @Version: $Id$
 * @Desc: <p>易修车新保订单导出，用于确认</p>
 */
public class XYCOrderForConfirmByEmailExport {
    @ExpAnnotation(id=0,name="序号",name_en="index",width=10)
    private String indexNo;
    @ExpAnnotation(id=1,name="订单时间",name_en="orderTime",width=15)
    private String orderTime="";
    @ExpAnnotation(id=2,name="姓名",name_en="userName",width=15)
    private String buyUser="";
    @ExpAnnotation(id=3,name="手机号",name_en="telPhone",width=20)
    private String telPhone="";
    @ExpAnnotation(id=4,name="车牌号",name_en="plateNumber",width=20)
    private String plateNumber="";
    @ExpAnnotation(id=5,name="车型",name_en="carType",width=50)
    private String carType="";
    @ExpAnnotation(id=6,name="VIN码",name_en="vinCode",width=15)
    private String vinCode="";
    @ExpAnnotation(id=7,name="区域",name_en="reginName",width=15)
    private String reginName="";
    @ExpAnnotation(id=8,name="优惠生效期",name_en="effTime",width=15)
    private String effTime="";
    @ExpAnnotation(id=9,name="优惠失效期",name_en="invalidTime",width=15)
    private String invalidTime="";
    @ExpAnnotation(id=10,name="升数",name_en="oilCost",width=15)
    private String oilCost="";
    @ExpAnnotation(id=11,name="机油规格",name_en="viscosity",width=15)
    private String viscosity="";
    @ExpAnnotation(id=12,name="数量",name_en="amount",width=15)
    private int amount=0;
    @ExpAnnotation(id=13,name="采购价格",name_en="orderType",width=10)
    private String oilInjection="";
    @ExpAnnotation(id=14,name="备注",name_en="remark",width=70)
    private String remark="";//浙江的-上海     辽宁的-天津
	public String getIndexNo() {
		return indexNo;
	}
	public void setIndexNo(String indexNo) {
		this.indexNo = indexNo;
	}
	public String getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}
	public String getBuyUser() {
		return buyUser;
	}
	public void setBuyUser(String buyUser) {
		this.buyUser = buyUser;
	}
	public String getTelPhone() {
		return telPhone;
	}
	public void setTelPhone(String telPhone) {
		this.telPhone = telPhone;
	}
	public String getPlateNumber() {
		return plateNumber;
	}
	public void setPlateNumber(String plateNumber) {
		this.plateNumber = plateNumber;
	}
	public String getCarType() {
		return carType;
	}
	public void setCarType(String carType) {
		this.carType = carType;
	}
	public String getVinCode() {
		return vinCode;
	}
	public void setVinCode(String vinCode) {
		this.vinCode = vinCode;
	}
	public String getReginName() {
		return reginName;
	}
	public void setReginName(String reginName) {
		this.reginName = reginName;
	}
	public String getEffTime() {
		return effTime;
	}
	public void setEffTime(String effTime) {
		this.effTime = effTime;
	}
	public String getInvalidTime() {
		return invalidTime;
	}
	public void setInvalidTime(String invalidTime) {
		this.invalidTime = invalidTime;
	}
	public String getOilCost() {
		return oilCost;
	}
	public void setOilCost(String oilCost) {
		this.oilCost = oilCost;
	}
	public String getViscosity() {
		return viscosity;
	}
	public void setViscosity(String viscosity) {
		this.viscosity = viscosity;
	}
	public int getAmount() {
		return amount;
	}
	public void setAmount(int amount) {
		this.amount = amount;
	}
	public String getOilInjection() {
		return oilInjection;
	}
	public void setOilInjection(String oilInjection) {
		this.oilInjection = oilInjection;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

}
