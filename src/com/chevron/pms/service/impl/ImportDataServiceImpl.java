package com.chevron.pms.service.impl;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.importdata.ImportDataUtil;
import com.chevron.material.dao.WXTMaterialSkuVoMapper;
import com.chevron.ordersettlement.model.OrderSettlementVo;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.VehicleTypeVoMapper;
import com.chevron.pms.dao.WholeVehicleOilVoMapper;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.VehicleTypeVo;
import com.chevron.pms.model.WholeVehicleOilVo;
import com.chevron.pms.service.ImportDataService;
import com.chevron.pms.service.WorkShopService;
import com.chevron.point.business.PointBizService;
import com.chevron.point.dto.CaltexPointExcelVo;
import com.chevron.point.dto.PointType;
import com.chevron.point.model.PointExcelVo;
import com.chevron.thirdorder.business.WebThirdOrderService;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.chevron.thirdorder.model.OrderXBVo;
import com.chevron.thirdorder.service.OrderForThirdOrderService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.EmptyChecker;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.impl.UserService;
import com.sys.log.util.LogUtils;
import com.sys.master.business.ProductMasterBizService;
import com.sys.master.dao.ProductMasterMapper;
import com.sys.master.model.ProductMaster;
import com.sys.master.service.ProductMasterService;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.service.OrganizationService;
import com.sys.properties.service.WxTPropertiesService;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
public class ImportDataServiceImpl implements ImportDataService {
	public final Logger log = Logger.getLogger(this.getClass());
	@Resource
	WholeVehicleOilVoMapper wholeVehicleOilVoMapper;

	@Resource
	WorkShopService workShopServiceImpl;

	@Resource
	VehicleTypeVoMapper mvehicleTypeVomapper;

	@Resource
	WebThirdOrderService webThirdOrderService;
	
	@Resource
	private WxTPropertiesService propertiesServiceImpl;
	
	@Resource
	OrderForThirdOrderService thirdOrderService;
	@Resource
	OrganizationVoMapper organizationVoMapper;
	@Resource
	private OrderVoMapper orderVoMapper;
	@Resource
	private PointBizService pointBizService;
	@Resource
	private UserService userService;
	@Resource
	private WXTMaterialSkuVoMapper materialSkuMapper;
	@Resource
	private OrganizationService organizationService;
	
	@Autowired
	private ProductMasterMapper productMasterMapper;
	
	@Autowired
	private ProductMasterService productMasterService;
	
	@Autowired
	private ProductMasterBizService productMasterBizService;
	
	//定义车型数据模板字段
	public static final String[] VEHICLETYPES = new String[] { "remark",
			"levelId", "factory", "brand", "vehicleSeries", "vehicleType",
			"salesName", "modelYear", "emissionStandard", "vehicleCategory",
			"salesYear", "salesMonth", "productYear", "stopProductionYear",
			"stopProductionStatus", "country", "vehicleKinds",
			"cylinderVolume", "displacement", "airIntakeForm", "fuelType",
			"fuelLabel", "maximumHorsepower", "maximumPower",
			"cylinderArrangement", "numberOfCylinders", "valvesPerCylinder",
			"gearboxType", "gearboxDesc", "numberOfGears", "frontBrakeType",
			"backBrakeType", "assistType", "enginePosition", "drivingMode",
			"wheelBase", "doorNumber", "numberOfSeats", "frontTireSize",
			"backTireSize", "frontBossOfWheelSize", "backBossOfWheelSize",
			"wheelMaterial", "spareTireSpecifications", "electricSkylight",
			"panoramicSkylight", "xenonLamp", "frontFogLamp", "rearWiper",
			"airConditioner", "autoAirConditioning" };// 这里的顺序要与实体类，模板中的保持一致，且名字要与实体类的属性保持一致
	
	//定义机油模板字段
	public static final String[] VEHICLEOIL = new String[] { "levelId",
		"oilCategory", "maintainFilledQuantity", "oilType", "oilGrade",
		"specification" };

	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	// 优化机油导入数据----modify by ken.zhang
	public Map<String, Object> importWholeVehicleOilBat(Workbook wb) {
		WholeVehicleOilVo whoVehicleOilVo = new WholeVehicleOilVo();
		List<WholeVehicleOilVo> lstWhoVehicleOilVo = new ArrayList<WholeVehicleOilVo>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 0.解析数据
			Map<String, Object> responseMap = ImportDataUtil
					.getImportDataByReflect(wb, whoVehicleOilVo,VEHICLEOIL);
			// 1.判断解析结果是否正确.
			String resultCode = (String) responseMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "error");
				resultMap.put("codeMsg", "导入数据格式错误");
				return resultMap;
			}
			// 2.返回结果正确，继续执行。
			lstWhoVehicleOilVo = (List<WholeVehicleOilVo>) responseMap
					.get("datalst");
			// 3.对结果进行分页处理
			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
					lstWhoVehicleOilVo, ImportDataUtil.MAX_BATCH_ACCOUNT);
			int toltalPage = newPage.getTotalPages();
			// 4.把数据插入数据库中
			for (int i = 1; i <= toltalPage; i++) {
				List<WholeVehicleOilVo> volst = newPage.getObjects(i);
				wholeVehicleOilVoMapper.insertBatchOil(volst);
			}
			// 5.是否有异常情况.
		} catch (Exception e) {
			log.error("导入数据失败!",e);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入数据失败,异常信息："+MyPropertyConfigurer.getVal("import.data.common.error"));
			return resultMap;
		}
		int uploadDataAccount = lstWhoVehicleOilVo.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
		return resultMap;
	}

	@Override
	// 优化机油导入数据----modify by tovid.chen
	// 包含对以前的数据进行判断是否存在，有则更新，没有则插入
	public Map<String, Object> importWholeVehicleOilBatPlus(Workbook wb) {
		WholeVehicleOilVo whoVehicleOilVo = new WholeVehicleOilVo();
		List<WholeVehicleOilVo> lstWhoVehicleOilVo = new ArrayList<WholeVehicleOilVo>();
		List<WholeVehicleOilVo> lstWhoVehicleOilVoExisted = new ArrayList<WholeVehicleOilVo>();
		List<WholeVehicleOilVo> lstWhoVehicleOilVoInsert = new ArrayList<WholeVehicleOilVo>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 0.解析数据
			Map<String, Object> responseMap = ImportDataUtil
					.getImportDataByReflect(wb, whoVehicleOilVo,
							VEHICLEOIL);
			// 1.判断解析结果是否正确.
			String resultCode = (String) responseMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "error");
				resultMap.put("codeMsg", "导入数据格式错误");
				return resultMap;
			}
			// 2.返回结果正确，继续执行。
			lstWhoVehicleOilVo = (List<WholeVehicleOilVo>) responseMap
					.get("datalst");
			// 3.对结果进行分页处理
			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
					lstWhoVehicleOilVo, ImportDataUtil.MAX_BATCH_ACCOUNT);
			int toltalPage = newPage.getTotalPages();
			// 4.判断是否存在相关记录，如果有，则从list里除去现有的
			for (int i = 1; i <= toltalPage; i++) {
				List<WholeVehicleOilVo> volst = newPage.getObjects(i);
				for(WholeVehicleOilVo tempVehicleOil : volst){
					List<WholeVehicleOilVo>  listVo = wholeVehicleOilVoMapper.selectByLevelId(tempVehicleOil.getLevelId());
					if(listVo != null && (!listVo.isEmpty())){
						lstWhoVehicleOilVoExisted.add(tempVehicleOil);
					}
					else{
						tempVehicleOil.setCreateTime(new Date());
						lstWhoVehicleOilVoInsert.add(tempVehicleOil);
					}
				}
			}
			// 5.插入以前没有的
			if(!lstWhoVehicleOilVoInsert.isEmpty()){
				ImportDataPageModelUtil newPageInsert = new ImportDataPageModelUtil(
						lstWhoVehicleOilVoInsert, ImportDataUtil.MAX_BATCH_ACCOUNT);
				int toltalPageInsert = newPageInsert.getTotalPages();
				for (int i = 1; i <= toltalPageInsert; i++) {
					List<WholeVehicleOilVo> volst = newPageInsert.getObjects(i);
					wholeVehicleOilVoMapper.insertBatchOil(volst);
				}
			}
			//6.更新以前存在的
			if(!lstWhoVehicleOilVoExisted.isEmpty()){
				for(WholeVehicleOilVo voTemp : lstWhoVehicleOilVoExisted){
					List<WholeVehicleOilVo>  listVo = wholeVehicleOilVoMapper.selectByLevelId(voTemp.getLevelId());
					WholeVehicleOilVo updateVo = listVo.get(0);
					if(voTemp.getOilCategory() != null){
						updateVo.setOilCategory(voTemp.getOilCategory());
					}
					if(voTemp.getMaintainFilledQuantity() != null){
						updateVo.setMaintainFilledQuantity(voTemp.getMaintainFilledQuantity());
					}
					if(voTemp.getOilType() != null){
						updateVo.setOilType(voTemp.getOilType());
					}
					if(voTemp.getOilGrade() != null){
						updateVo.setOilCategory(voTemp.getOilCategory());
					}
					if(voTemp.getSpecification() != null){
						updateVo.setSpecification(voTemp.getSpecification());
					}	
					updateVo.setUpdateTime(new Date());
					wholeVehicleOilVoMapper.updateByPrimaryKeySelective(updateVo);
				}				
			}			
			
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入数据失败,异常信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
			return resultMap;
		}
		int uploadDataAccount = lstWhoVehicleOilVo.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
		return resultMap;
	}

	// 优化车型导入数据-批量导入.
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importBatchData(Workbook wb) {
		VehicleTypeVo vehicleTypeVo = new VehicleTypeVo();
		List<VehicleTypeVo> lstAllVehicleTypeVo = new ArrayList<VehicleTypeVo>();
		List<VehicleTypeVo> voList_new = new ArrayList<VehicleTypeVo>();
		List<VehicleTypeVo> voList_modify = new ArrayList<VehicleTypeVo>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 0.解析数据.
			Map<String, Object> responseMap = ImportDataUtil
					.getImportDataByReflect(wb, vehicleTypeVo,VEHICLETYPES);

			// 1.判断返回结果是否正确.
			String resultCode = (String) responseMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "error");
				resultMap.put("codeMsg", "导入数据格式错误");
				return resultMap;
			}
			// 2.返回结果正确，继续执行
			lstAllVehicleTypeVo = (List<VehicleTypeVo>) responseMap
					.get("datalst");
			// 3.循环结果，判断备注是新增还是修改
			for (VehicleTypeVo mVehicleTypeVo : lstAllVehicleTypeVo) {
				String remark = mVehicleTypeVo.getRemark();
				if (remark.toString().equals("新增")) {
					voList_new.add(mVehicleTypeVo);
				} else if (remark.toString().equals("修订")) {
					voList_modify.add(mVehicleTypeVo);
				}
			}
			// 4.对新增车型数据进行分页处理
			ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(
					voList_new, 40);
			// a.获取总页数.
			int toltalPage = newPage.getTotalPages();
			// b.遍历每一页数据
			for (int i = 1; i <= toltalPage; i++) {
				List<VehicleTypeVo> volst = newPage.getObjects(i);
				// 访问数据库 进行批量操作
				mvehicleTypeVomapper.insertBatch(volst);
			}

			// 5.对修订的车型数据进行分页处理
			ImportDataPageModelUtil modifyPage = new ImportDataPageModelUtil(
					voList_modify, 40);
			int sumPage = modifyPage.getTotalPages();
			for (int i = 1; i <= sumPage; i++) {
				List<VehicleTypeVo> volst = modifyPage.getObjects(i);
				// 访问数据库 进行批量更新操作
				mvehicleTypeVomapper.updateBatch(volst);
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			e.printStackTrace();
			log.info("导入数据失败" + ",异常信息:" + e.getMessage());
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "导入数据失败,异常信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
			return resultMap;
		}
		int uploadDataAccount = voList_new.size() + voList_modify.size();
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + uploadDataAccount);
		return resultMap;
	}

//	@SuppressWarnings("unchecked")
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> importBatchOrderDataFromExcel(Workbook wb,
//			String operType) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<OrderVo> importOrderLst = new ArrayList<OrderVo>();
//		int mOperType = 0;
//		try {
//			// 0.解析数据,获取响应，判断响应正确性
//			Map<String, Object> repsMap = new HashMap<String, Object>();
//			if (operType.equals(ImportDataUtil.OPER_INERT_ORDER_TYPE))// 采购订单
//			{
//				mOperType = 1;
//			} else if (operType.equals(ImportDataUtil.OPER_UPDATE_ORDER_TYPE))// 发货订单
//			{
//				mOperType = 2;
//			} else {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入订单类型异常");
//				return resultMap;
//			}
//
//			OrderVo importOrderVo = new OrderVo();
//			String[] ORDER_MODEL = MyPropertyConfigurer.getVal("ORDER_MODEL").split(",");
//			repsMap = ImportDataUtil.getImportDataByReflect(wb,
//					importOrderVo, ORDER_MODEL);
//			String resultCode = (String) repsMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "数据格式异常");
//				return resultMap;
//			}
//			importOrderLst = (List<OrderVo>) repsMap.get("datalst");
//			if (null == importOrderLst || importOrderLst.size() == 0) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入数据为null");
//				return resultMap;
//			}
//			// 1.重新组装
//			List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//			WxTUser curUser = ContextUtil.getCurUser();
//			for(OrderVo newOrUpdateOrderVo:importOrderLst)
//			{
//				if(mOperType==1)
//				{
//					newOrUpdateOrderVo.setCreateTime(new Date());
//					newOrUpdateOrderVo.setUpdateTime(new Date());
//					if(null!=curUser)
//					{
//						newOrUpdateOrderVo.setCreator(curUser.getUserId() + "");
//					}
//					
//					//newOrUpdateOrderVo.setOrderNo(CommonUtil.generateCode("DD"));
//					
//					//delete by bo.liu 0905 start
//					//String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.NEW_CONFIRMED_ORDER,6,1);
//					//newOrUpdateOrderVo.setOrderNo(CommonUtil.generateOrderCode(0)+sequenceNo);
//					//delete by bo.liu 0905 end
//					
//					//newOrUpdateOrderVo.setOrderNo(CommonUtil.generateOrderCode(0));
//					newOrUpdateOrderVo.setOrderType("DP");
//					newOrUpdateOrderVo.setSource("DDBX");
//					newOrUpdateOrderVo.setSourceId("DDBX1");
//					newOrUpdateOrderVo.setTotalOrderPrice(Double.parseDouble(newOrUpdateOrderVo.getPreferentialTypePrice()));
//					newOrUpdateOrderVo.setStatus("3");//已下单
//				}
//			    if(mOperType==2)//发货订单
//				{
//					newOrUpdateOrderVo.setRemark(newOrUpdateOrderVo.getSendRemark());
//					if(null!=curUser)
//					{
//						newOrUpdateOrderVo.setCreator(curUser.getUserId() + "");
//					}
//					newOrUpdateOrderVo.setPlateNumber(newOrUpdateOrderVo.getDeliveryPlateNumber());
//					newOrUpdateOrderVo.setOrderType("DA");
//					newOrUpdateOrderVo.setStatus("4");//待发货
//					newOrUpdateOrderVo.setUpdateTime(new Date());
//				}
//				lstNewOrderVo.add(newOrUpdateOrderVo);
//			}
//			// 2.对解析后的数据进行分页处理,分页批量插入
//			Map<String,Object> respMap = new HashMap<String,Object>();
//			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
//					lstNewOrderVo, 100);
//			int order_page_account = order_all_page.getTotalPages();
//			for (int i = 1; i <= order_page_account; i++) {
//				switch (mOperType) {
//				case 1:
//					// 批量插入采购订单
//					List<OrderVo> lstOrders = order_all_page
//							.getObjects(i);
//					respMap = webThirdOrderService.reCreateXBOrder(lstOrders,OrderCommonVo.ORDER_SOURCE_TYPE[0]);
//					break;
//				case 2:
//					// 批量更新订单（发货订单）
//					List<OrderVo> lstUpdateOrders = order_all_page
//							.getObjects(i);
//					respMap = webThirdOrderService.reCreateFWOrder(lstUpdateOrders,OrderCommonVo.ORDER_SOURCE_TYPE[0]);//updateOrderForDeliveryNotice(lstUpdateOrders);
//					break;
//				default:
//					break;
//				}
//
//			}
//			String respCode = (String) respMap.get("code");
//			if(respCode.equals("error"))
//			{
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "" +respMap.get("errorMsg").toString());
//				return resultMap;
//			}
//
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			TransactionAspectSupport.currentTransactionStatus()
//					.setRollbackOnly();
//			log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
//			return resultMap;
//
//		}
//		resultMap.put("code", "success");
//		resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//		return resultMap;
//	}

	
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importBatchDDXBOrderDataFromExcel(Workbook wb) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<OrderXBVo> importOrderLst = new ArrayList<OrderXBVo>();
		try {
			// 0.解析数据,获取响应，判断响应正确性
			Map<String, Object> repsMap = new HashMap<String, Object>();
			
			OrderXBVo importOrderVo = new OrderXBVo();
			String[] ORDER_DDXB_MODEL = MyPropertyConfigurer.getVal("ORDER_DDXB_MODEL").split(",");
			repsMap = ImportDataUtil.getImportDataByReflect(wb,
					importOrderVo, ORDER_DDXB_MODEL);
			String resultCode = (String) repsMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "数据格式异常");
				return resultMap;
			}
			importOrderLst = (List<OrderXBVo>) repsMap.get("datalst");
			if (null == importOrderLst || importOrderLst.size() == 0) {
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "导入数据为null");
				return resultMap;
			}
			Long spid = 0L;
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
			spid = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
			// 1.重新组装
			List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
			WxTUser curUser = ContextUtil.getCurUser();
			for(OrderXBVo newOrUpdateOrderVo:importOrderLst)
			{
				OrderVo orderVo = new OrderVo();
				
				orderVo.setCreateTime(new Date());
				orderVo.setUpdateTime(new Date());
				if(null!=curUser)
				{
					orderVo.setCreator(curUser.getUserId() + "");
				}
				
				//delete by bo.liu 0905 start
				//String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.NEW_CONFIRMED_ORDER,6,1);
				//orderVo.setOrderNo(CommonUtil.generateOrderCode(0)+sequenceNo);
				//delete by bo.liu 0905 end
				
				//newOrUpdateOrderVo.setOrderNo(CommonUtil.generateOrderCode(0));
				orderVo.setOrderType("DP");
				orderVo.setSource("DDBX");
				orderVo.setSourceId("DDBX1");
				//orderVo.setTotalOrderPrice(Double.parseDouble(newOrUpdateOrderVo.getPreferentialTypePrice()));
				orderVo.setStatus("3");//已下单
				
				orderVo.setBuyUserName(newOrUpdateOrderVo.getBuyUserName());
				orderVo.setPhoneNo(newOrUpdateOrderVo.getPhoneNo());
				orderVo.setPlateNumber(newOrUpdateOrderVo.getPlateNumber().replace(" ", "").trim());
				orderVo.setCarType(newOrUpdateOrderVo.getCarType());
				orderVo.setLevelId(newOrUpdateOrderVo.getLevelId());
				orderVo.setVinCode(newOrUpdateOrderVo.getVinCode());
				orderVo.setRegionName(newOrUpdateOrderVo.getRegionName());
				orderVo.setEffectiveTime(newOrUpdateOrderVo.getEffectiveTime());
				orderVo.setInvalidTime(newOrUpdateOrderVo.getInvalidTime());
				//orderVo.setType(newOrUpdateOrderVo.getType());
				orderVo.setCouponCardType(newOrUpdateOrderVo.getCardType());
				orderVo.setQuantity(newOrUpdateOrderVo.getXbQuantity());
				orderVo.setCardType(newOrUpdateOrderVo.getCardType());
				//orderVo.setPreferentialTypePrice(newOrUpdateOrderVo.getPreferentialTypePrice());
				orderVo.setRemark(newOrUpdateOrderVo.getRemark());
				orderVo.setSettlementModelType(OrderVo.SETTLEMENTMODELTYPE1);
				orderVo.setPoOrderPartnerId(spid);
				lstNewOrderVo.add(orderVo);
			}
			// 2.对解析后的数据进行分页处理,分页批量插入
			Map<String,Object> respMap = new HashMap<String,Object>();
			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
					lstNewOrderVo, 100);
			int order_page_account = order_all_page.getTotalPages();
			for (int i = 1; i <= order_page_account; i++) {
					// 批量插入采购订单
					List<OrderVo> lstOrders = order_all_page
							.getObjects(i);
					respMap = webThirdOrderService.reCreateXBOrder(lstOrders,OrderCommonVo.ORDER_SOURCE_TYPE[0]);
					String respCode = (String) respMap.get("code");
					if(!respCode.equals(MessageContants.SUCCESS_CODE))
					{
						resultMap.put("code", "syserror");
						resultMap.put("codeMsg", "数据异常：" +respMap.get("errorMsg").toString());
						return resultMap;
					}

			}
			

		} catch (Exception ex) {
			ex.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			log.info("导入数据失败" + ",异常信息:" , ex);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "错误信息："+ex.getLocalizedMessage());
			return resultMap;

		}
		log.info("msg:ljc 0927=======================================新保订单导入结束");
		resultMap.put("code", "success");
		resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
		return resultMap;
	}
	
	
//	@SuppressWarnings("unchecked")
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> importBatchDDFWOrderDataFromExcel(Workbook wb) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<OrderFWVo> importOrderLst = new ArrayList<OrderFWVo>();
//		try {
//			// 0.解析数据,获取响应，判断响应正确性
//			Map<String, Object> repsMap = new HashMap<String, Object>();
//			OrderFWVo importOrderVo = new OrderFWVo();
//			String[] ORDER_DDFW_MODEL = MyPropertyConfigurer.getVal("ORDER_DDFW_MODEL").split(",");
//			repsMap = ImportDataUtil.getImportDataByReflect(wb,
//					importOrderVo, ORDER_DDFW_MODEL);
//			String resultCode = (String) repsMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "数据格式异常");
//				return resultMap;
//			}
//			importOrderLst = (List<OrderFWVo>) repsMap.get("datalst");
//			if (null == importOrderLst || importOrderLst.size() == 0) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入数据为null");
//				return resultMap;
//			}
//			//Long spid = 0L;
//			//Map<String,Object> reqMap = new HashMap<String,Object>();
//			//reqMap.put("orgName", OrderCommonVo.ORDER_SP[0]);
//			//spid = organizationVoMapper.selectByOrganizationName(reqMap).get(0).getId();
//			
//			// 1.重新组装
//			List<OrderVo> lstNewOrderVo = new ArrayList<OrderVo>();
//			WxTUser curUser = ContextUtil.getCurUser();
//			for(OrderFWVo newOrUpdateOrderVo:importOrderLst)
//			{
//				OrderVo orderVo = new OrderVo();
//				orderVo.setRemark(newOrUpdateOrderVo.getSendRemark());
//				if(null!=curUser)
//				{
//					orderVo.setCreator(curUser.getUserId() + "");
//				}
//				orderVo.setPlateNumber(newOrUpdateOrderVo.getDeliveryPlateNumber().replace(" ", "").trim());
//				orderVo.setOrderType("DA");
//				orderVo.setStatus("4");//待发货
//				orderVo.setUpdateTime(new Date());
//				orderVo.setCreateTime(new Date());
//				
//				orderVo.setServiceTime(newOrUpdateOrderVo.getServiceTime());
//				orderVo.setWorkshopName(newOrUpdateOrderVo.getWorkshopName());
//				orderVo.setReceiveUserName(newOrUpdateOrderVo.getReceiveUserName());
//				orderVo.setReceivePhoneNo(newOrUpdateOrderVo.getReceivePhoneNo());
//				orderVo.setReceiveRegionName(newOrUpdateOrderVo.getReceiveRegionName());
//				orderVo.setRegionName(newOrUpdateOrderVo.getReceiveRegionName());
//				orderVo.setAddress(newOrUpdateOrderVo.getAddress());
//				orderVo.setSendRemark(newOrUpdateOrderVo.getSendRemark());
//				//orderVo.setPoOrderPartnerId(spid);
//				lstNewOrderVo.add(orderVo);
//			}
//			// 2.对解析后的数据进行分页处理,分页批量插入
//			Map<String,Object> respMap = new HashMap<String,Object>();
//			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
//					lstNewOrderVo, 100);
//			int order_page_account = order_all_page.getTotalPages();
//			for (int i = 1; i <= order_page_account; i++) {
//				// 批量更新订单（发货订单）
//				List<OrderVo> lstUpdateOrders = order_all_page
//						.getObjects(i);
//				//System.out.println("------------------llllllllllllllllllllllljc:"+lstUpdateOrders.toString());
//				respMap = webThirdOrderService.reCreateFWOrder(lstUpdateOrders,OrderCommonVo.ORDER_SOURCE_TYPE[0]);//updateOrderForDeliveryNotice(lstUpdateOrders);
//
//				String respCode = (String) respMap.get("code");
//				if(!respCode.equals(MessageContants.SUCCESS_CODE))
//				{
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "数据异常：" +respMap.get("errorMsg").toString());
//					return resultMap;
//				}
//			}
//			
//
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			TransactionAspectSupport.currentTransactionStatus()
//					.setRollbackOnly();
//			log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "错误信息：" +ex.getLocalizedMessage());
//			return resultMap;
//
//		}
//		log.info("msg:ljc 0927=======================================服务订单导入结束");
//		resultMap.put("code", "success");
//		resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//		return resultMap;
//	}
	
//	
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> importBatchJSBFWOrderDataFromExcel(Workbook wb) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<OrderFWJSBVo> importOrderLst = new ArrayList<OrderFWJSBVo>();
//		try {
//			// 0.解析数据,获取响应，判断响应正确性
//			Map<String, Object> repsMap = new HashMap<String, Object>();
//			OrderFWJSBVo importOrderVo = new OrderFWJSBVo();
//			String[] ORDER_JSB_MODEL = MyPropertyConfigurer.getVal("ORDER_JSB_MODEL").split(",");
//			repsMap = ImportDataUtil.getImportDataByReflect(wb,
//					importOrderVo, ORDER_JSB_MODEL);
//			String resultCode = (String) repsMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "数据格式异常");
//				return resultMap;
//			}
//			importOrderLst = (List<OrderFWJSBVo>) repsMap.get("datalst");
//			if (null == importOrderLst || importOrderLst.isEmpty()) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入数据为null");
//				return resultMap;
//			}
//			//获取操作类型
//			String operType = importOrderLst.get(0).getOperFlag();
//			if(null == operType || operType.isEmpty())
//			{
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "操作类型不能为空");
//				return resultMap;
//			}
//			
//			//组装数据
//			List<OrderVo> lstOrder = new ArrayList<OrderVo>();
//			if(operType.equals(OrderFWJSBVo.INSERT_OPER))
//			{
//				for(OrderFWJSBVo orderJSB:importOrderLst)
//				{
//					OrderVo orderVo = new OrderVo();
//					//orderVo.setOrderNo(CommonUtil.generateCode("DD"));
//					//orderVo.setOrderNo(CommonUtil.generateOrderCode(5));
//					String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.TECH_HELP_ORDER,6,1);
//					orderVo.setOrderNo(CommonUtil.generateOrderCode(5)+sequenceNo);
//					orderVo.setAddress(orderJSB.getUserAdd());
//					orderVo.setReceiveUserName(orderJSB.getUserName());
//					orderVo.setReceivePhoneNo(orderJSB.getUserPhone());
//					orderVo.setBuyUserName(orderJSB.getUserName());
//					orderVo.setPhoneNo(orderJSB.getUserPhone());
//					orderVo.setSource(OrderVo.JSB_ORDER_SOURCE);
//					orderVo.setOrderType(OrderVo.DDFW_ORDER_TYPE);
//					orderVo.setSourceId(OrderVo.JSB_ORDER_SOURCE_ID);
//					orderVo.setCreateTime(new Date());
//					orderVo.setUpdateTime(new Date());
//					orderVo.setStatus("4");//待发货
//					
//					List<OrderLineVo> lstOrderLineVos = new ArrayList<OrderLineVo>();
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku(orderJSB.getSku());
//					if(null!=orderJSB.getSkuId() && !orderJSB.getSkuId().isEmpty())
//					{
//						orderLineVo.setProductId(Long.parseLong(orderJSB.getSkuId()));
//					}else
//					{
//						orderLineVo.setProductId(90001L);
//					}
//					orderLineVo.setProductName(orderJSB.getProductName());
//					orderLineVo.setAmount(1);
//					orderLineVo.setActualAmount(1);
//					orderLineVo.setUnits(orderJSB.getUnits());
//					lstOrderLineVos.add(orderLineVo);
//					
//					
//					orderVo.setOrderLines(lstOrderLineVos);
//					lstOrder.add(orderVo);
//				}
//			}else if(operType.equals(OrderFWJSBVo.UPDATE_OPER))
//			{
//				for(OrderFWJSBVo orderJSB:importOrderLst)
//				{
//					OrderVo orderVo = new OrderVo();
//					orderVo.setBillId(orderJSB.getBillNo());
//					orderVo.setOrderNo(orderJSB.getOrderNo());
//					if(null==orderJSB.getDeliveryTime())
//					{
//						orderVo.setDeliveryTime(new Date());
//					}else
//					{
//						orderVo.setDeliveryTime(orderJSB.getDeliveryTime());
//					}
//					orderVo.setStatus("5");//代表已发货
//					lstOrder.add(orderVo);
//				}
//				
//			}else
//			{
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "没找到合适的操作类型");
//				return resultMap;
//			}
//			
//			//插入订单  订单行信息
//			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
//					lstOrder, 100);
//			int order_page_account = order_all_page.getTotalPages();
//			for (int i = 1; i <= order_page_account; i++) {
//				List<OrderVo> lstNewOrders = order_all_page
//						.getObjects(i);
//				if(operType.equals(OrderFWJSBVo.INSERT_OPER))
//				{
//					webThirdOrderService.createOrderForOthersSource(lstNewOrders);
//				}else
//				{
//					webThirdOrderService.updateBatchOrderForOthersSource(lstNewOrders);
//				}
//			}
//			
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			TransactionAspectSupport.currentTransactionStatus()
//					.setRollbackOnly();
//			log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
//			return resultMap;
//
//		}
//		log.info("msg:ljc 1028=======================================服务订单导入结束");
//		resultMap.put("code", "success");
//		resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//		return resultMap;
//	}
	
//	
//	@Override
//	public Map<String, Object> importSPIDOrderForESFromExcel(Workbook wb) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<ImportSPIDOrderForES> importOrderLst = new ArrayList<ImportSPIDOrderForES>();
//		try {
//			// 0.解析数据,获取响应，判断响应正确性
//			Map<String, Object> repsMap = new HashMap<String, Object>();
//			ImportSPIDOrderForES importOrderVo = new ImportSPIDOrderForES();
//			String[] ORDER_SPID_FRO_ES = MyPropertyConfigurer.getVal("ORDER_SPID_FRO_ES").split(",");
//			repsMap = ImportDataUtil.getImportDataByReflect(wb,
//					importOrderVo, ORDER_SPID_FRO_ES);
//			String resultCode = (String) repsMap.get("result");
//			if (resultCode.equals("dataInvalid")) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "数据格式异常");
//				return resultMap;
//			}
//			importOrderLst = (List<ImportSPIDOrderForES>) repsMap.get("datalst");
//			if (null == importOrderLst || importOrderLst.isEmpty()) {
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "导入数据为null");
//				return resultMap;
//			}
//			//组装数据
//			List<OrderVo> lstOrder = new ArrayList<OrderVo>();
//			for(ImportSPIDOrderForES importOrder : importOrderLst)
//			{
//				OrderVo  orderVo = new OrderVo();
//				orderVo.setAddress(importOrder.getWorkshopAddr().toString());
//				orderVo.setWorkshopName(importOrder.getWorkshopName().toString());
//				orderVo.setReceiveRegionName(importOrder.getRegion().toUpperCase());
//				orderVo.setReceiveUserName(importOrder.getReceiveName().toString());
//				orderVo.setReceivePhoneNo(importOrder.getReceivePhone().toString());
//				orderVo.setOrderPartnerName(importOrder.getPartnerName().toString());
//				double totalPrice = 0.0;
//				
//				double p500244lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500244LPK());
//				int p500244lpkAmount = Integer.parseInt(importOrder.getP500244LPK());
//				
//				double p500244njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500244NJK());
//				int p500244njkAmount = Integer.parseInt(importOrder.getP500244NJK());
//				List<OrderLineVo> lstOrderLine = new ArrayList<OrderLineVo>();
//				//0合成开始
//				if(null!=importOrder.getP500244LPK() && !importOrder.getP500244LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500244LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500244LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500244LPK()));
//					orderLineVo.setPrice(p500244lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500244lpkPrice*p500244lpkAmount;
//				}
//				if(null!=importOrder.getP500244NJK() &&  !importOrder.getP500244NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500244NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500244NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500244NJK()));
//					orderLineVo.setPrice(p500244njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500244njkPrice*p500244njkAmount;
//				}
//				
//				double p500245lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500245LPK());
//				int p500245lpkAmount = Integer.parseInt(importOrder.getP500245LPK());
//				
//				double p500245njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500245NJK());
//				int p500245njkAmount = Integer.parseInt(importOrder.getP500245NJK());
//				if(null!=importOrder.getP500245LPK() &&  !importOrder.getP500245LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500245LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500245LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500245LPK()));
//					orderLineVo.setPrice(p500245lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500245lpkPrice*p500245lpkAmount;
//				}
//				if(null!=importOrder.getP500245NJK() &&  !importOrder.getP500245NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500245NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500245NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500245NJK()));
//					orderLineVo.setPrice(p500245njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500245njkPrice*p500245njkAmount;
//				}
//				
//				
//				double p500246lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500246LPK());
//				int p500246lpkAmount = Integer.parseInt(importOrder.getP500246LPK());
//				
//				double p500246njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500246NJK());
//				int p500246njkAmount = Integer.parseInt(importOrder.getP500246NJK());
//				if(null!=importOrder.getP500246LPK() &&  !importOrder.getP500246LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500246LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500246LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500246LPK()));
//					orderLineVo.setPrice(p500246lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500246lpkPrice*p500246lpkAmount;
//				}
//				
//				if(null!=importOrder.getP500246NJK() &&  !importOrder.getP500246NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500246NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500246NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500246NJK()));
//					orderLineVo.setPrice(p500246njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500246njkPrice*p500246njkAmount;
//				}
//				double p500247lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500247LPK());
//				int p500247lpkAmount = Integer.parseInt(importOrder.getP500247LPK());
//				
//				double p500247njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500247NJK());
//				int p500247njkAmount = Integer.parseInt(importOrder.getP500247NJK());
//				if(null!=importOrder.getP500247LPK() &&  !importOrder.getP500247LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500247LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500247LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500247LPK()));
//					orderLineVo.setPrice(p500247lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500247lpkPrice*p500247lpkAmount;
//				}
//				if(null!=importOrder.getP500247NJK() &&  !importOrder.getP500247NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500247NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500247NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500247NJK()));
//					orderLineVo.setPrice(p500247njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500247njkPrice*p500247njkAmount;
//				}
//				double p500248lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500248LPK());
//				int p500248lpkAmount = Integer.parseInt(importOrder.getP500248LPK());
//				
//				double p500248njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500248NJK());
//				int p500248njkAmount = Integer.parseInt(importOrder.getP500248NJK());
//				if(null!=importOrder.getP500248LPK() &&  !importOrder.getP500248LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500248LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500248LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500248LPK()));
//					orderLineVo.setPrice(p500248lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500248lpkPrice*p500248lpkAmount;
//				}
//				if(null!=importOrder.getP500248NJK() &&  !importOrder.getP500248NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500248NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500248NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500248NJK()));
//					orderLineVo.setPrice(p500248njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500248njkPrice*p500248njkAmount;
//				}
//				double p500249lpkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500249LPK());
//				int p500249lpkAmount = Integer.parseInt(importOrder.getP500249LPK());
//				
//				double p500249njkPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500249NJK());
//				int p500249njkAmount = Integer.parseInt(importOrder.getP500249NJK());
//				if(null!=importOrder.getP500249LPK() &&  !importOrder.getP500249LPK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500249LPK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500249LPK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500249LPK()));
//					orderLineVo.setPrice(p500249lpkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500249lpkPrice*p500249lpkAmount;
//				}
//				
//				if(null!=importOrder.getP500249NJK() &&  !importOrder.getP500249NJK().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500249NJK");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500249NJK()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500249NJK()));
//					orderLineVo.setPrice(p500249njkPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500249njkPrice*p500249njkAmount;
//				}
//				//合成结束
//				
//				
//				//1全合成开始
//				double p500263lpk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500263LPK01A());
//				int p500263lpk01aAmount = Integer.parseInt(importOrder.getP500263LPK01A());
//				
//				double p500263njk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500263NJK01A());
//				int p500263njk01aAmount = Integer.parseInt(importOrder.getP500263NJK01A());
//				if(null!=importOrder.getP500263LPK01A() &&  !importOrder.getP500263LPK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500263LPK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500263LPK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500263LPK01A()));
//					orderLineVo.setPrice(p500263lpk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500263lpk01aPrice*p500263lpk01aAmount;
//				}
//				if(null!=importOrder.getP500263NJK01A() &&  !importOrder.getP500263NJK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500263NJK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500263NJK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500263NJK01A()));
//					orderLineVo.setPrice(p500263njk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500263njk01aPrice*p500263njk01aAmount;
//				}
//				double p500264lpk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500264LPK01A());
//				int p500264lpk01aAmount = Integer.parseInt(importOrder.getP500264LPK01A());
//				
//				double p500264njk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500264NJK01A());
//				int p500264njk01aAmount = Integer.parseInt(importOrder.getP500264NJK01A());
//				if(null!=importOrder.getP500264LPK01A() &&  !importOrder.getP500264LPK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500264LPK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500264LPK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500264LPK01A()));
//					orderLineVo.setPrice(p500264lpk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500264lpk01aPrice*p500264lpk01aAmount;
//				}
//				if(null!=importOrder.getP500264NJK01A() &&  !importOrder.getP500264NJK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500264NJK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500264NJK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500264NJK01A()));
//					orderLineVo.setPrice(p500264njk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500264njk01aPrice*p500264njk01aAmount;
//				}
//				
//				double p500268lpk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500268LPK01A());
//				int p500268lpk01aAmount = Integer.parseInt(importOrder.getP500268LPK01A());
//				
//				double p500268njk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500268NJK01A());
//				int p500268njk01aAmount = Integer.parseInt(importOrder.getP500268NJK01A());
//				if(null!=importOrder.getP500268LPK01A() &&  !importOrder.getP500268LPK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500268LPK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500268LPK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500268LPK01A()));
//					orderLineVo.setPrice(p500268lpk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500268lpk01aPrice*p500268lpk01aAmount;
//				}
//				if(null!=importOrder.getP500268NJK01A() &&  !importOrder.getP500268NJK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500268NJK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500268NJK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500268NJK01A()));
//					orderLineVo.setPrice(p500268njk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500268njk01aPrice*p500268njk01aAmount;
//				}
//				
//				double p500269lpk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500269LPK01A());
//				int p500269lpk01aAmount = Integer.parseInt(importOrder.getP500269LPK01A());
//				
//				double p500269njk01aPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP500269NJK01A());
//				int p500269njk01aAmount = Integer.parseInt(importOrder.getP500269NJK01A());
//				if(null!=importOrder.getP500269LPK01A() &&  !importOrder.getP500269LPK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500269LPK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500269LPK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500269LPK01A()));
//					orderLineVo.setPrice(p500269lpk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500269lpk01aPrice*p500269lpk01aAmount;
//				}
//				if(null!=importOrder.getP500269NJK01A() &&  !importOrder.getP500269NJK01A().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("500269NJK01A");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP500269NJK01A()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP500269NJK01A()));
//					orderLineVo.setPrice(p500269njk01aPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p500269njk01aPrice*p500269njk01aAmount;
//				}
//				//全合成结束
//				
//				//2添加剂	开始
//				double p510725URE01Price = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP510725URE01());
//				int p510725URE01Amount = Integer.parseInt(importOrder.getP510725URE01());
//				
//				double p510727UREPrice = Double.parseDouble(importOrderLst.get(importOrderLst.size()-1).getP510727URE());
//				int p510727UREAmount = Integer.parseInt(importOrder.getP510727URE());
//				if(null!= importOrder.getP510725URE01() && !importOrder.getP510725URE01().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("510725URE01");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP510725URE01()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP510725URE01()));
//					orderLineVo.setPrice(p510725URE01Price);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p510725URE01Price*p510725URE01Amount;
//				}
//				
//				if(null!= importOrder.getT510725URE01() && !importOrder.getT510725URE01().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("510725URE01");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getT510725URE01()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getT510725URE01()));
//					orderLineVo.setPrice(0.0);
//					orderLineVo.setRemark("赠送");
//					lstOrderLine.add(orderLineVo);
//				}
//				
//				if(null!= importOrder.getP510727URE() && !importOrder.getP510727URE().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("510727URE");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getP510727URE()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getP510727URE()));
//					orderLineVo.setPrice(p510727UREPrice);
//					lstOrderLine.add(orderLineVo);
//					totalPrice+= p510727UREPrice*p510727UREAmount;
//				}
//				
//				if(null!= importOrder.getT510727URE() && !importOrder.getT510727URE().equals("0"))
//				{
//					OrderLineVo orderLineVo = new OrderLineVo();
//					orderLineVo.setSku("510727URE");
//					orderLineVo.setAmount(Integer.parseInt(importOrder.getT510727URE()));
//					orderLineVo.setActualAmount(Integer.parseInt(importOrder.getT510727URE()));
//					orderLineVo.setPrice(0.0);
//					orderLineVo.setRemark("赠送");
//					lstOrderLine.add(orderLineVo);
//				}
//				//添加剂结束
//				orderVo.setOrderLines(lstOrderLine);
//				orderVo.setTotalOrderPayPrice(totalPrice);
//				orderVo.setTotalOrderPrice(totalPrice);
//				orderVo.setTotalProductPrice(totalPrice);
//				orderVo.setTotalProductPreferPrice(totalPrice);
//				lstOrder.add(orderVo);
//			}
//			
//			
//			//插入订单  订单行信息
//			ImportDataPageModelUtil order_all_page = new ImportDataPageModelUtil(
//					lstOrder, 100);
//			int order_page_account = order_all_page.getTotalPages();
//			for (int i = 1; i <= order_page_account; i++) {
//				List<OrderVo> lstNewOrders = order_all_page
//						.getObjects(i);
//				webThirdOrderService.createFWOrderForSPID(lstNewOrders);
//				
//			}
//			
//		} catch (Exception ex) {
//			ex.printStackTrace();
//			TransactionAspectSupport.currentTransactionStatus()
//					.setRollbackOnly();
//			log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//			resultMap.put("code", "syserror");
//			resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
//			return resultMap;
//
//		}
//		log.info("msg:ljc 06=======================================特殊合伙人订单导入结束");
//		resultMap.put("code", "success");
//		resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//		return resultMap;
//	}
	
	
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> importBatchDDYYOrderDataFromExcel(Workbook wb) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<OrderDDYYFWVo> importOrderLst = new ArrayList<OrderDDYYFWVo>();
//		try {
//				// 0.解析数据,获取响应，判断响应正确性
//				Map<String, Object> repsMap = new HashMap<String, Object>();
//				OrderDDYYFWVo importOrderVo = new OrderDDYYFWVo();
//				String[] ORDER_DDYYFW_MODEL = MyPropertyConfigurer.getVal("ORDER_DDYYFW_MODEL").split(",");
//				repsMap = ImportDataUtil.getImportDataByReflect(wb,
//						importOrderVo, ORDER_DDYYFW_MODEL);
//				String resultCode = (String) repsMap.get("result");
//				if (resultCode.equals("dataInvalid")) {
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "数据格式异常");
//					return resultMap;
//				}
//				importOrderLst = (List<OrderDDYYFWVo>) repsMap.get("datalst");
//				if (null == importOrderLst || importOrderLst.isEmpty()) {
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "导入数据为null");
//					return resultMap;
//				}
//				// 1.插入数据...
//				Map<String,Object> respMap = thirdOrderService.createDDYYFWOrder(importOrderLst);
//				String respCode = (String) respMap.get("code");
//				if(!respCode.equals("success"))
//				{
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "数据异常：" +respMap.get("codeMsg").toString());
//					return resultMap;
//				}
//			
//		} catch (Exception ex) {
//				ex.printStackTrace();
//				TransactionAspectSupport.currentTransactionStatus()
//						.setRollbackOnly();
//				log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
//				return resultMap;
//
//		}
//			log.info("msg:ljc 07=======================================滴滴预约订单导入");
//			resultMap.put("code", "success");
//			resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//			return resultMap;
//	}
	
	
	
//	@Override
//	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
//	public Map<String, Object> importYYFWOrderBatchDataFromExcel(Workbook wb) {
//		Map<String, Object> resultMap = new HashMap<String, Object>();
//		List<OrderYYFWVo> importOrderLst = new ArrayList<OrderYYFWVo>();
//		try {
//				// 0.解析数据,获取响应，判断响应正确性
//				Map<String, Object> repsMap = new HashMap<String, Object>();
//				OrderYYFWVo importOrderVo = new OrderYYFWVo();
//				String[] ORDER_YYFW_MODEL = MyPropertyConfigurer.getVal("ORDER_YYFW_MODEL").split(",");
//				repsMap = ImportDataUtil.getImportDataByReflect(wb,
//						importOrderVo, ORDER_YYFW_MODEL);
//				String resultCode = (String) repsMap.get("result");
//				if (resultCode.equals("dataInvalid")) {
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "数据格式异常");
//					return resultMap;
//				}
//				importOrderLst = (List<OrderYYFWVo>) repsMap.get("datalst");
//				if (null == importOrderLst || importOrderLst.isEmpty()) {
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "导入数据为null");
//					return resultMap;
//				}
//				// 1.插入数据...
//				Map<String,Object> respMap = thirdOrderService.createYYFWOrder(importOrderLst);
//				String respCode = (String) respMap.get("code");
//				if(!respCode.equals("success"))
//				{
//					resultMap.put("code", "syserror");
//					resultMap.put("codeMsg", "数据异常：" +respMap.get("codeMsg").toString());
//					return resultMap;
//				}
//			
//		} catch (Exception ex) {
//				ex.printStackTrace();
//				TransactionAspectSupport.currentTransactionStatus()
//						.setRollbackOnly();
//				log.info("导入数据失败" + ",异常信息:" + ex.getLocalizedMessage());
//				resultMap.put("code", "syserror");
//				resultMap.put("codeMsg", "错误信息：" +MyPropertyConfigurer.getVal("import.data.common.error"));
//				return resultMap;
//
//		}
//			log.info("msg:ljc 07=======================================通用预约订单导入");
//			resultMap.put("code", "success");
//			resultMap.put("codeMsg", "导入成功,总数量：" + importOrderLst.size());
//			return resultMap;
//	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String,Object> importOrderSettlementBatchDataFromExcel(Workbook wb){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<OrderSettlementVo> orderList = new ArrayList<OrderSettlementVo>();

		try {
			OrderSettlementVo settlmenet = new OrderSettlementVo();
			Map<String, Object> respMap = new HashMap<String, Object>();
			String[] ORDER_SETTLEMENT_MODEL = MyPropertyConfigurer.getVal("ORDER_SETTLEMENT_MODEL").split(",");
			respMap = ImportDataUtil.getImportDataByReflect(wb, settlmenet, ORDER_SETTLEMENT_MODEL);
			String resultCode = (String) respMap.get("result");
			if (resultCode.equals("dataInvalid")) {
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "数据格式异常");
				return resultMap;
			}
			orderList = (List<OrderSettlementVo>) respMap.get("datalst");
			if (null == orderList || orderList.isEmpty()) {
				resultMap.put("code", "syserror");
				resultMap.put("codeMsg", "导入数据为null");
				return resultMap;
			}
			// 遍历更新excel中的数据
			List<OrderSettlementVo> settledOrderList = new ArrayList<OrderSettlementVo>();
			for (int i = 0; i < orderList.size(); i++) {
				OrderSettlementVo settleVo = orderList.get(i);//导入的结算信息
				if ("结算".equals(settleVo.getSettleStatus())) {
					// 获取该条订单信息
					Map<String, Object> reqMap = new HashMap<String, Object>();
					reqMap.put("orderCode", settleVo.getOrderNo());
					OrderVo orderVo = orderVoMapper.getOrderByOrderNo(reqMap);
					// 检查订单状态
					if (!settleVo.getCarType().equals(orderVo.getCarType())) {
						//resultMap.put("code", "syserror");
						//resultMap.put("codeMsg", "订单类型错误 订单号：" + settleVo.getOrderNo());
						throw new Exception("订单类型错误 订单号：" + settleVo.getOrderNo());
						//return resultMap;
					}
					if (settleVo.getSettledNum() <= 0) {
						//resultMap.put("code", "syserror");
						//resultMap.put("codeMsg", "订单数量不正确 订单号：" + settleVo.getSettledNum());
						throw new Exception("订单数量不正确 订单号：" + settleVo.getSettledNum());
						//return resultMap;
					}
					//单次卡
					if(orderVo.getCardType().equals("单次卡")) {
						if ((orderVo.getSettledNum() + settleVo.getSettledNum()) > orderVo.getServiceAcount()) {
							//resultMap.put("code", "syserror");
							//resultMap.put("codeMsg", "订单数量超出限制 订单号：" + settleVo.getOrderNo());
							throw new Exception("订单数量超出限制 订单号：" + settleVo.getOrderNo());
							//return resultMap;
						}
					}
					//年卡
					if(orderVo.getCardType().equals("年卡")) {
						if (((orderVo.getSettledNum() + settleVo.getSettledNum()))*3 > orderVo.getServiceAcount()) {
							//resultMap.put("code", "syserror");
							//resultMap.put("codeMsg", "订单数量超出限制 订单号：" + settleVo.getOrderNo());
							throw new Exception("订单数量超出限制 订单号：" + settleVo.getOrderNo());
							//return resultMap;
						}
					}
					// 更新订单状态
					if (settleVo.getCardType().equals("年卡")) {
						if(orderVo.getRemainingServiceTimes()<3) {
							settledOrderList.add(settleVo);
						}
						Map<String,Object> settleNiankaMap = new HashMap<String,Object>();
						settleNiankaMap.put("orderNo", settleVo.getOrderNo());
						settleNiankaMap.put("settledNum", settleVo.getSettledNum()+orderVo.getSettledNum());
						orderVoMapper.updateOrderSettledNumByOrderNo(settleNiankaMap);
					}
					if (settleVo.getCardType().equals("单次卡")) {
						// 检查订单的剩余数量
						if (orderVo.getRemainingServiceTimes() == 0) {
							settledOrderList.add(settleVo);
						}
						// 还有剩余次数，结算标记变为未全部结算2，更新结算次数
						Map<String, Object> settleMap = new HashMap<String, Object>();
						settleMap.put("orderNo", settleVo.getOrderNo());
						settleMap.put("settledNum", settleVo.getSettledNum() + orderVo.getSettledNum());
						orderVoMapper.updateOrderSettledNumByOrderNo(settleMap);
					}
				}
			}
			// 批量将结算完的订单状态修改
			Map<String, Object> settledOrderListMap = new HashMap<String, Object>();
			settledOrderListMap.put("settledOrderList", settledOrderList);
			settledOrderListMap.put("isSettled", 1);
			if(settledOrderList.size()>0) {
				orderVoMapper.updateBatchOrderSettledStatus(settledOrderListMap);
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "error");
			resultMap.put("errorMsg",e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return resultMap;
		}
		
		resultMap.put("code", "success");
		return resultMap;		
	}

	/**
	 * 这个是复杂的模板——cdm的进货积分
	 * @param wb
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importCdmStockBatchDataFromExcel(Workbook wb) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<CaltexPointExcelVo> pointVoList = new ArrayList<CaltexPointExcelVo>();
			
			Calendar.getInstance().setTime(new Date());
			int curMonth=Calendar.getInstance().get(Calendar.MONDAY)+1;

			Sheet sheet = wb.getSheetAt(0);
			// 获取经销商id
			Iterator<Row> rowIt = sheet.rowIterator();
			while (rowIt.hasNext()) {
				CaltexPointExcelVo pointVo = new CaltexPointExcelVo();
				Row row = rowIt.next();
				Cell cell = row.getCell(3);
				Long partnerId;
				try {
					partnerId = Math.round(cell.getNumericCellValue());
				} catch (NullPointerException ex) {
					continue;
				} catch (IllegalStateException ex2) {
					continue;
				}
				pointVo.setDealerNo(partnerId);
				// 获取积分
				try {
					// 所以导入不了别的月份的值
					// 这里改成1~12月份的遍历
					for(int i=1;i<=12;i++) {
						Cell curCell = row.getCell(6 + i);
						if (curCell != null) {
							pointVo.setPoint(i, row.getCell(6 + i).getNumericCellValue());
						}
					}
				} catch(IllegalStateException ex) {
					continue;
				} catch(NullPointerException ex2) {
					System.out.println("-----------------------------rowNum:"+row.getRowNum());
					continue;
				}
				pointVoList.add(pointVo);
			}
			
			Map<String,Object> importMap=pointBizService.importMonthlyPoint4CdmStock(curMonth, pointVoList);
			if(importMap.get(Constants.RESULT_CODE_KEY).equals(Constants.ERROR_CODE)) {
				return importMap;
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			e.printStackTrace();
			return resultMap;
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		return resultMap;
	}

	/**
	 * 这个接口来导入那种简单的积分模板,
	 * 目前CDM_MATERIAL_POINT，CDM_PROMOTION_POINT，PROMOTION_POINT 都是使用这个函数
	 * @param wb
	 * @param pointType
	 * @return
	 */
	@Override
    @Transactional(rollbackFor = Exception.class)
	public Map<String, Object> importPointBatchDataFromExcel(Workbook wb, PointType pointType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<PointExcelVo> pointVoList = new ArrayList<PointExcelVo>();

//			Calendar.getInstance().setTime(new Date());
//			int month=Calendar.getInstance().get(Calendar.MONDAY)+1;

			Sheet sheet = wb.getSheetAt(0);
			// 获取经销商id
			Iterator<Row> rowIt = sheet.rowIterator();
			while (rowIt.hasNext()) {
				PointExcelVo pointVo = new PointExcelVo();
				Row row = rowIt.next();
				//跳过表头
				if(row.getRowNum() == 0){
					continue;
				}
				Cell cell = row.getCell(0);
				//空白行跳过，主要针对最后一行可能会是空白行
				if((cell==null||StringUtils.isBlank(cell.getStringCellValue())) && (null==row.getCell(2)||StringUtils.isBlank(row.getCell(2).getStringCellValue()))){
					continue;
				}
				// 第一个那个cell是sapCode
				Long partnerId = null;
				try {
					if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
						partnerId = Math.round(cell.getNumericCellValue());
					} else if(cell.getCellType() == Cell.CELL_TYPE_STRING){
						if(StringUtils.isBlank(cell.getStringCellValue())){
							throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【SAP Number】列数据不能为空");
						}
						partnerId = Long.parseLong(cell.getStringCellValue());
					} else if(cell.getCellType() == Cell.CELL_TYPE_BLANK){
						throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【SAP Number】列数据不能为空");
					}
				} catch (NullPointerException ex) {
					throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【SAP Number】列数据不能为空");
//					continue;
				} catch (IllegalStateException ex2) {
					continue;
				} catch (NumberFormatException ex3) {
					throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【SAP Number】列格式不正确");
//					continue;
				}
				pointVo.setSapNumber(partnerId);

				// 第3那个cell是积分
				try {
					Cell cell2 = row.getCell(2);
					if(cell2.getCellType() == Cell.CELL_TYPE_NUMERIC){
						pointVo.setPromotionPoint(cell2.getNumericCellValue());
					}else if(cell2.getCellType() == Cell.CELL_TYPE_STRING){
						if(StringUtils.isBlank(cell2.getStringCellValue())){
							throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【Promotion Points】列数据不能为空");
						}
						pointVo.setPromotionPoint(Double.parseDouble(cell2.getStringCellValue()));
					}else if(cell2.getCellType() == Cell.CELL_TYPE_BLANK){
						throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【Promotion Points】列数据不能为空");
					}
				} catch(IllegalStateException ex) {
					continue;
				} catch(NullPointerException ex2) {
					log.info("-----------------------------rowNum:"+row.getRowNum());
					throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【Promotion Points】列数据不能为空");
//					continue;
				} catch (NumberFormatException ex3) {
					throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【Promotion Points】列格式不正确");
//					continue;
				}

				// 第4那个cell是日期
				try {
					Cell cell3 = row.getCell(3);
					if(cell3.getCellType() == Cell.CELL_TYPE_NUMERIC){
						pointVo.setTransTime(cell3.getDateCellValue());
					}else if(cell3.getCellType() == Cell.CELL_TYPE_STRING){
						if(cell3.getStringCellValue() != null && DateUtil.parseDate(cell3.getStringCellValue()) == null){
							throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【month】列格式不正确");
						}
						pointVo.setTransTime(DateUtil.parseDate(cell3.getStringCellValue()));
					}else if(cell3.getCellType() == Cell.CELL_TYPE_BLANK){
						throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【month】列数据不能为空");
					}
				} catch(IllegalStateException ex) {
					continue;
				} catch(NullPointerException ex2) {
					log.info("-----------------------------rowNum:"+row.getRowNum());
					throw new WxPltException("第"+(row.getRowNum()+1)+"行数据【month】列数据不能为空");
//					continue;
				}

				// 第5那个cell是备注
				try {
					Cell cell4 = row.getCell(4);
					pointVo.setRemark(cell4.getStringCellValue());
				} catch(IllegalStateException ex) {
					continue;
				} catch(NullPointerException ex2) {
					continue;
				}
				pointVoList.add(pointVo);
			}

			Map<String,Object> importMap=pointBizService.importPoint(pointVoList,pointType);
			if(importMap.get(Constants.RESULT_CODE_KEY).equals(Constants.ERROR_CODE)) {
				return importMap;
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			log.error(e.getMessage(), e);
			LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.pms.service.impl.ImportDataServiceImpl.importPointBatchDataFromExcel", 
					"导入积分失败。" + e.getMessage(), null);
			return resultMap;
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		return resultMap;
	}

	/**
	 *  从excel导入德乐进货积分,这个使用的excel模板是复杂的
	 * @param wb
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> importCaltexPointBatchDataFromExcel(Workbook wb) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<CaltexPointExcelVo> pointVoList = new ArrayList<CaltexPointExcelVo>();

			// 这里获取的当前的月份
			Calendar.getInstance().setTime(new Date());
			int curMonth=Calendar.getInstance().get(Calendar.MONDAY)+1;

			Sheet sheet = wb.getSheetAt(0);
			// 获取经销商id
			Iterator<Row> rowIt = sheet.rowIterator();
			while (rowIt.hasNext()) {
				CaltexPointExcelVo pointVo = new CaltexPointExcelVo();
				Row row = rowIt.next();
				Cell cell = row.getCell(3);
				Long partnerId;
				try {
					partnerId = Math.round(cell.getNumericCellValue());
				} catch (NullPointerException ex) {
					continue;
				} catch (IllegalStateException ex2) {
					continue;
				}
				pointVo.setDealerNo(partnerId);
				// 获取积分
				try {
					// 所以导入不了别的月份的值
					// 这里改成1~12月份的遍历
					for(int i=1;i<=12;i++){
						Cell curCell = row.getCell(6+i);
						if(curCell!=null){
							pointVo.setPoint(i, row.getCell(6+i).getNumericCellValue());
						}
					}
				} catch(IllegalStateException ex) {
					continue;
				} catch(NullPointerException ex2) {
					System.out.println("-----------------------------rowNum:"+row.getRowNum());
					continue;
				}
				pointVoList.add(pointVo);
			}
			
			Map<String,Object> importMap=pointBizService.importMonthlyPoint4Dealer(curMonth, pointVoList);
			if(importMap.get(Constants.RESULT_CODE_KEY).equals(Constants.ERROR_CODE)) {
				return importMap;
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
			e.printStackTrace();
			return resultMap;
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		return resultMap;
	}
	
	@Override
	public Map<String,Object> importMasterProductFromExcel1(Workbook wb, String userId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Sheet sheet = wb.getSheetAt(0);
		Iterator<Row> rowIt = sheet.rowIterator();
		List<ProductMaster> importProductList = new ArrayList<ProductMaster>();
		int rows = 0;
		while (rowIt.hasNext()) {
			if(rows%10==0){
				log.info("====导入产品,excel已处理"+rows+"行");
			}
			ProductMaster productMaster = new ProductMaster();
			Row row = rowIt.next();
			Cell cell = row.getCell(1);
			String sku = StringUtils.getCellStringValue(cell);
			if(EmptyChecker.isEmpty(sku) || "Material Code".equals(sku)) {
				continue;
			}
			productMaster.setSku(sku);
			//NameEn
			Cell cell2 = row.getCell(2);
			String nameEn = StringUtils.getCellStringValue(cell2);
			productMaster.setNameEn(nameEn);
			//NameZh
			Cell cell3 = row.getCell(3);
			String nameZh = StringUtils.getCellStringValue(cell3);
			productMaster.setName(nameZh);
			//CreationDate
			Cell cell5 = row.getCell(5);
			Date creationDate = cell5.getDateCellValue();
			productMaster.setCreationDate(creationDate);
			//Status
			Cell cell6 = row.getCell(6);
			String status = StringUtils.getCellStringValue(cell6);
			productMaster.setMasterStatus(status);
			//BlockDate
			Cell cell7 = row.getCell(7);
			if(cell7.getCellType() != Cell.CELL_TYPE_BLANK && cell7.getCellType() != Cell.CELL_TYPE_STRING) {
				Date blockDate = cell7.getDateCellValue();
				productMaster.setBlockDate(blockDate);
			}
			//UnBlockDate
			Cell cell8 = row.getCell(8);
			if(cell8.getCellType() != Cell.CELL_TYPE_BLANK && cell8.getCellType() != Cell.CELL_TYPE_STRING) {
				Date unblockDate = cell8.getDateCellValue();
				productMaster.setUnblockDate(unblockDate);
			}
			//BaseUn
			Cell cell13 = row.getCell(13);
			String baseUn = StringUtils.getCellStringValue(cell13);
			if("#N/A".equals(baseUn)) {
				baseUn = "";
			}
			productMaster.setBaseUn(baseUn);
			//Unit
			Cell cell14 = row.getCell(14);
			if(null!=cell14 && cell14.getCellType()==Cell.CELL_TYPE_NUMERIC){
				Double unit = cell14.getNumericCellValue();
				productMaster.setPack(unit);
			}

			//Package Unit
			Cell cell15 = row.getCell(15);
			if(null!=cell15 && cell15.getCellType()==Cell.CELL_TYPE_NUMERIC){
				String packageUnit = StringUtils.getCellStringValue(cell15);
				productMaster.setPackType(packageUnit);
			}

			//ABC Class
			Cell cell33 = row.getCell(33);
			if(cell33.getCellType() != Cell.CELL_TYPE_BLANK) {
				String abcClass = StringUtils.getCellStringValue(cell33);
				if(!EmptyChecker.isEmpty(abcClass)) {
					productMaster.setGradeAbc(abcClass);
				}
			}
			//PackCode
			Cell cell45 = row.getCell(45);
			String packCode = StringUtils.getCellStringValue(cell45);
			productMaster.setPackCode(packCode);
			//Bottle Qty
			Cell cell59 = row.getCell(59);
			if(cell59.getCellType() != Cell.CELL_TYPE_BLANK) {
				String bottleQtyStr = StringUtils.getCellStringValue(cell59);
				if(!EmptyChecker.isEmpty(bottleQtyStr)) {
					Integer bottleQty = new Integer(bottleQtyStr);
					productMaster.setBottleQty(bottleQty);
				}
			}
			rows++;
			importProductList.add(productMaster);
		}
		log.info("导入产品1,excel解析完成，共"+rows+"行");
		LogUtils.addInfoLog(Long.parseLong(userId), "ImportDataServiceImpl.importMasterProductFromExcel1", "excel解析完成，共 "+rows+" 行");
		productMasterService.updateImportExcel(importProductList, userId);
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_CODE_MSG_KEY, "导入成功");
		return resultMap;
	}
	
	@Override
	public Map<String,Object> importMasterProductFromExcel2(Workbook wb,String userId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		Sheet sheet = wb.getSheetAt(0);
		Iterator<Row> rowIt = sheet.rowIterator();
		while (rowIt.hasNext()) {
			ProductMaster productMaster = new ProductMaster();
			Row row = rowIt.next();
			Cell cell = row.getCell(1);
			String sku = StringUtils.getCellStringValue(cell);
			if(EmptyChecker.isEmpty(sku) || "product_code_SAP".equals(sku)) {
				continue;
			}
			//比对SKU是否已存在
			ProductMaster product = productMasterMapper.selectBySku(sku);
			if(EmptyChecker.isEmpty(product)) {
				log.info("sku is exist, don't insert data");
				continue;
			}
			productMaster.setId(product.getId());
			productMaster.setSku(sku);
			productMaster.setMasterStatus("Active");
			//Unit
			Cell cell4 = row.getCell(4);
			String unitStr = StringUtils.getCellStringValue(cell4);
			productMaster.setPack(new Double(unitStr));
			//PackCode
			Cell cell5 = row.getCell(5);
			String packCode = StringUtils.getCellStringValue(cell5);
			productMaster.setPackCode(packCode);
			//Package Unit
			Cell cell6 = row.getCell(6);
			String packageUnit = StringUtils.getCellStringValue(cell6);
			productMaster.setPackType(packageUnit);
			//Hydraulic_AGO_for_C_I
			Cell cell7 = row.getCell(7);
			if(!EmptyChecker.isEmpty(cell7) && cell7.getCellType() != Cell.CELL_TYPE_BLANK) {
				String hAFCI = StringUtils.getCellStringValue(cell7);
				if(!EmptyChecker.isEmpty(hAFCI)) {
					productMaster.setHydraulicAgoCi(hAFCI);
				}
			}
			//Hydralic_Transmission_AGO_for_Indirect_Commercial
			Cell cell8 = row.getCell(8);
			if(!EmptyChecker.isEmpty(cell8) && cell8.getCellType() != Cell.CELL_TYPE_BLANK) {
				String hTAGFIC = StringUtils.getCellStringValue(cell8);
				if(!EmptyChecker.isEmpty(hTAGFIC)) {
					productMaster.setHydTraAgoIndCom(hTAGFIC);
				}
			}
			//product_channel
			Cell cell9 = row.getCell(9);
			if(!EmptyChecker.isEmpty(cell9) && cell9.getCellType() != Cell.CELL_TYPE_BLANK) {
				String channel = StringUtils.getCellStringValue(cell9);
				if(!EmptyChecker.isEmpty(channel)) {
					productMaster.setProductChannel(channel);
				}
			}
			//brand
			Cell cell10 = row.getCell(10);
			if(!EmptyChecker.isEmpty(cell10) && cell10.getCellType() != Cell.CELL_TYPE_BLANK) {
				String brand = StringUtils.getCellStringValue(cell10);
				if(!EmptyChecker.isEmpty(brand)) {
					productMaster.setInnerBrand(brand);
				}
			}
			//product_sector
			Cell cell11 = row.getCell(11);
			if(!EmptyChecker.isEmpty(cell11) && cell11.getCellType() != Cell.CELL_TYPE_BLANK) {
				String sector = StringUtils.getCellStringValue(cell11);
				if(!EmptyChecker.isEmpty(sector)) {
					productMaster.setProductSector(sector);
				}
			}
			//t1_category
			Cell cell12 = row.getCell(12);
			if(!EmptyChecker.isEmpty(cell12) && cell12.getCellType() != Cell.CELL_TYPE_BLANK) {
				String t1Category = StringUtils.getCellStringValue(cell12);
				if(!EmptyChecker.isEmpty(t1Category)) {
					productMaster.setT1Category(t1Category);
				}
			}
			//t2_category
			Cell cell13 = row.getCell(13);
			if(!EmptyChecker.isEmpty(cell13) && cell13.getCellType() != Cell.CELL_TYPE_BLANK) {
				String t2Category = StringUtils.getCellStringValue(cell13);
				if(!EmptyChecker.isEmpty(t2Category)) {
					productMaster.setT2Category(t2Category);
				}
			}
			//t3_category
			Cell cell14 = row.getCell(14);
			if(!EmptyChecker.isEmpty(cell14) && cell14.getCellType() != Cell.CELL_TYPE_BLANK) {
				String t3Category = StringUtils.getCellStringValue(cell14);
				if(!EmptyChecker.isEmpty(t3Category)) {
					productMaster.setT3Category(t3Category);
				}
			}
			try {
//				String userId = ContextUtil.getCurUserId().toString();
				productMasterBizService.save(productMaster, userId);
			} catch (WxPltException e) {
				log.error(e.getMessage(), e);
			}
		}
		
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		resultMap.put(Constants.RESULT_CODE_MSG_KEY, "导入成功");
		return resultMap;
	}
}
