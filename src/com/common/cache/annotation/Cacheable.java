package com.common.cache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 缓存注解
 * 用于标记需要缓存的方法
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Cacheable {
    
    /**
     * 缓存键前缀
     */
    String keyPrefix() default "";
    
    /**
     * 缓存过期时间（秒）
     */
    int expireSeconds() default 300;
    
    /**
     * 是否使用参数生成缓存键
     */
    boolean useParams() default true;
    
    /**
     * 缓存条件（SpEL表达式）
     */
    String condition() default "";
    
    /**
     * 缓存类型
     */
    CacheType type() default CacheType.NORMAL;
    
    enum CacheType {
        SHORT,   // 短期缓存（1分钟）
        NORMAL,  // 普通缓存（5分钟）
        LONG     // 长期缓存（30分钟）
    }
}
