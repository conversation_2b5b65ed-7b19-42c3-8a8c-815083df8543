package com.common.cache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 缓存清除注解
 * 用于标记需要清除缓存的方法
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheEvict {
    
    /**
     * 要清除的缓存键前缀
     */
    String keyPrefix() default "";
    
    /**
     * 是否清除所有相关缓存
     */
    boolean allEntries() default false;
    
    /**
     * 缓存清除条件（SpEL表达式）
     */
    String condition() default "";
    
    /**
     * 是否在方法执行前清除缓存
     */
    boolean beforeInvocation() default false;
}
