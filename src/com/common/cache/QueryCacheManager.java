package com.common.cache;

import com.common.util.JsonUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.security.MessageDigest;
import java.util.concurrent.TimeUnit;

/**
 * 查询缓存管理器
 * 用于缓存数据库查询结果，减少数据库压力
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
public class QueryCacheManager {
    
    private static final Logger logger = Logger.getLogger(QueryCacheManager.class);
    
    @Autowired
    private RedisTemplate<Serializable, Serializable> redisTemplate;
    
    // 缓存键前缀
    private static final String CACHE_PREFIX = "query_cache:";
    
    // 默认缓存时间（秒）
    private static final int DEFAULT_EXPIRE_SECONDS = 300; // 5分钟
    private static final int SHORT_EXPIRE_SECONDS = 60;    // 1分钟
    private static final int LONG_EXPIRE_SECONDS = 1800;   // 30分钟
    
    /**
     * 获取缓存数据
     * 
     * @param cacheKey 缓存键
     * @param clazz 返回类型
     * @return 缓存的数据，如果不存在返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getCache(String cacheKey, Class<T> clazz) {
        try {
            String fullKey = CACHE_PREFIX + cacheKey;
            Object cached = redisTemplate.opsForValue().get(fullKey);
            
            if (cached == null) {
                logger.debug("Cache miss for key: " + cacheKey);
                return null;
            }
            
            logger.debug("Cache hit for key: " + cacheKey);
            
            if (cached instanceof String) {
                // JSON字符串反序列化
                return JsonUtil.readValue((String) cached, clazz);
            } else {
                // 直接类型转换
                return (T) cached;
            }
            
        } catch (Exception e) {
            logger.error("Error getting cache for key: " + cacheKey, e);
            return null;
        }
    }
    
    /**
     * 设置缓存数据（默认过期时间）
     * 
     * @param cacheKey 缓存键
     * @param data 要缓存的数据
     */
    public void setCache(String cacheKey, Object data) {
        setCache(cacheKey, data, DEFAULT_EXPIRE_SECONDS);
    }
    
    /**
     * 设置缓存数据（指定过期时间）
     * 
     * @param cacheKey 缓存键
     * @param data 要缓存的数据
     * @param expireSeconds 过期时间（秒）
     */
    public void setCache(String cacheKey, Object data, int expireSeconds) {
        try {
            String fullKey = CACHE_PREFIX + cacheKey;
            
            if (data instanceof String || data instanceof Number || data instanceof Boolean) {
                // 基本类型直接存储
                redisTemplate.opsForValue().set(fullKey, (Serializable) data, expireSeconds, TimeUnit.SECONDS);
            } else {
                // 复杂对象序列化为JSON
                String jsonData = JsonUtil.writeValue(data);
                redisTemplate.opsForValue().set(fullKey, jsonData, expireSeconds, TimeUnit.SECONDS);
            }
            
            logger.debug("Cache set for key: " + cacheKey + ", expire: " + expireSeconds + "s");
            
        } catch (Exception e) {
            logger.error("Error setting cache for key: " + cacheKey, e);
        }
    }
    
    /**
     * 删除缓存
     * 
     * @param cacheKey 缓存键
     */
    public void deleteCache(String cacheKey) {
        try {
            String fullKey = CACHE_PREFIX + cacheKey;
            redisTemplate.delete(fullKey);
            logger.debug("Cache deleted for key: " + cacheKey);
        } catch (Exception e) {
            logger.error("Error deleting cache for key: " + cacheKey, e);
        }
    }
    
    /**
     * 批量删除缓存（通过模式匹配）
     * 
     * @param pattern 缓存键模式，如 "user:*"
     */
    public void deleteCacheByPattern(String pattern) {
        try {
            String fullPattern = CACHE_PREFIX + pattern;
            redisTemplate.delete(redisTemplate.keys(fullPattern));
            logger.debug("Cache deleted by pattern: " + pattern);
        } catch (Exception e) {
            logger.error("Error deleting cache by pattern: " + pattern, e);
        }
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param cacheKey 缓存键
     * @return true如果存在，false如果不存在
     */
    public boolean hasCache(String cacheKey) {
        try {
            String fullKey = CACHE_PREFIX + cacheKey;
            return redisTemplate.hasKey(fullKey);
        } catch (Exception e) {
            logger.error("Error checking cache existence for key: " + cacheKey, e);
            return false;
        }
    }
    
    /**
     * 生成SQL查询的缓存键
     * 
     * @param sql SQL语句
     * @param params 参数数组
     * @return 缓存键
     */
    public String generateSqlCacheKey(String sql, Object... params) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("sql:");
            sb.append(sql);
            
            if (params != null && params.length > 0) {
                sb.append(":params:");
                for (Object param : params) {
                    sb.append(param != null ? param.toString() : "null").append(",");
                }
            }
            
            // 使用MD5生成短键名
            return md5(sb.toString());
            
        } catch (Exception e) {
            logger.error("Error generating SQL cache key", e);
            return "error_key_" + System.currentTimeMillis();
        }
    }
    
    /**
     * 生成业务查询的缓存键
     * 
     * @param module 模块名
     * @param method 方法名
     * @param params 参数
     * @return 缓存键
     */
    public String generateBusinessCacheKey(String module, String method, Object... params) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("biz:").append(module).append(":").append(method);
            
            if (params != null && params.length > 0) {
                sb.append(":");
                for (Object param : params) {
                    sb.append(param != null ? param.toString() : "null").append(",");
                }
            }
            
            return md5(sb.toString());
            
        } catch (Exception e) {
            logger.error("Error generating business cache key", e);
            return "error_key_" + System.currentTimeMillis();
        }
    }
    
    /**
     * MD5加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            logger.error("Error generating MD5", e);
            return String.valueOf(input.hashCode());
        }
    }
    
    /**
     * 获取短期缓存（1分钟）
     */
    public <T> T getShortCache(String cacheKey, Class<T> clazz) {
        return getCache(cacheKey, clazz);
    }
    
    /**
     * 设置短期缓存（1分钟）
     */
    public void setShortCache(String cacheKey, Object data) {
        setCache(cacheKey, data, SHORT_EXPIRE_SECONDS);
    }
    
    /**
     * 获取长期缓存（30分钟）
     */
    public <T> T getLongCache(String cacheKey, Class<T> clazz) {
        return getCache(cacheKey, clazz);
    }
    
    /**
     * 设置长期缓存（30分钟）
     */
    public void setLongCache(String cacheKey, Object data) {
        setCache(cacheKey, data, LONG_EXPIRE_SECONDS);
    }
}
