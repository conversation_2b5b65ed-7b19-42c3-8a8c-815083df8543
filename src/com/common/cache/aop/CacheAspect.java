package com.common.cache.aop;

import com.common.cache.QueryCacheManager;
import com.common.cache.annotation.CacheEvict;
import com.common.cache.annotation.Cacheable;
import org.apache.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 缓存切面
 * 处理@Cacheable和@CacheEvict注解
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Aspect
@Component
public class CacheAspect {
    
    private static final Logger logger = Logger.getLogger(CacheAspect.class);
    
    @Autowired
    private QueryCacheManager cacheManager;
    
    /**
     * 处理@Cacheable注解
     */
    @Around("@annotation(cacheable)")
    public Object handleCacheable(ProceedingJoinPoint joinPoint, Cacheable cacheable) throws Throwable {
        
        // 生成缓存键
        String cacheKey = generateCacheKey(joinPoint, cacheable);
        
        // 检查条件
        if (!evaluateCondition(cacheable.condition(), joinPoint)) {
            logger.debug("Cache condition not met, executing method directly");
            return joinPoint.proceed();
        }
        
        try {
            // 尝试从缓存获取
            Object cached = getCachedResult(cacheKey, joinPoint, cacheable);
            if (cached != null) {
                logger.debug("Cache hit for key: " + cacheKey);
                return cached;
            }
            
            // 缓存未命中，执行方法
            logger.debug("Cache miss for key: " + cacheKey);
            Object result = joinPoint.proceed();
            
            // 缓存结果
            setCachedResult(cacheKey, result, cacheable);
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error in cache aspect for key: " + cacheKey, e);
            // 缓存出错时直接执行方法
            return joinPoint.proceed();
        }
    }
    
    /**
     * 处理@CacheEvict注解
     */
    @Around("@annotation(cacheEvict)")
    public Object handleCacheEvict(ProceedingJoinPoint joinPoint, CacheEvict cacheEvict) throws Throwable {
        
        // 检查条件
        if (!evaluateCondition(cacheEvict.condition(), joinPoint)) {
            logger.debug("Cache evict condition not met, executing method directly");
            return joinPoint.proceed();
        }
        
        try {
            // 方法执行前清除缓存
            if (cacheEvict.beforeInvocation()) {
                evictCache(joinPoint, cacheEvict);
            }
            
            // 执行方法
            Object result = joinPoint.proceed();
            
            // 方法执行后清除缓存
            if (!cacheEvict.beforeInvocation()) {
                evictCache(joinPoint, cacheEvict);
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error in cache evict aspect", e);
            throw e;
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(ProceedingJoinPoint joinPoint, Cacheable cacheable) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        
        String keyPrefix = cacheable.keyPrefix();
        if (keyPrefix.isEmpty()) {
            keyPrefix = className + ":" + methodName;
        }
        
        if (cacheable.useParams()) {
            Object[] args = joinPoint.getArgs();
            return cacheManager.generateBusinessCacheKey(keyPrefix, methodName, args);
        } else {
            return cacheManager.generateBusinessCacheKey(keyPrefix, methodName);
        }
    }
    
    /**
     * 从缓存获取结果
     */
    private Object getCachedResult(String cacheKey, ProceedingJoinPoint joinPoint, Cacheable cacheable) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class<?> returnType = signature.getReturnType();
        
        switch (cacheable.type()) {
            case SHORT:
                return cacheManager.getShortCache(cacheKey, returnType);
            case LONG:
                return cacheManager.getLongCache(cacheKey, returnType);
            default:
                return cacheManager.getCache(cacheKey, returnType);
        }
    }
    
    /**
     * 设置缓存结果
     */
    private void setCachedResult(String cacheKey, Object result, Cacheable cacheable) {
        if (result == null) {
            return; // 不缓存null结果
        }
        
        switch (cacheable.type()) {
            case SHORT:
                cacheManager.setShortCache(cacheKey, result);
                break;
            case LONG:
                cacheManager.setLongCache(cacheKey, result);
                break;
            default:
                cacheManager.setCache(cacheKey, result, cacheable.expireSeconds());
                break;
        }
    }
    
    /**
     * 清除缓存
     */
    private void evictCache(ProceedingJoinPoint joinPoint, CacheEvict cacheEvict) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        
        String keyPrefix = cacheEvict.keyPrefix();
        if (keyPrefix.isEmpty()) {
            keyPrefix = className;
        }
        
        if (cacheEvict.allEntries()) {
            // 清除所有相关缓存
            cacheManager.deleteCacheByPattern(keyPrefix + "*");
            logger.debug("Evicted all cache entries for pattern: " + keyPrefix + "*");
        } else {
            // 清除特定缓存
            Object[] args = joinPoint.getArgs();
            String cacheKey = cacheManager.generateBusinessCacheKey(keyPrefix, methodName, args);
            cacheManager.deleteCache(cacheKey);
            logger.debug("Evicted cache for key: " + cacheKey);
        }
    }
    
    /**
     * 评估条件表达式（简单实现）
     */
    private boolean evaluateCondition(String condition, ProceedingJoinPoint joinPoint) {
        if (condition == null || condition.trim().isEmpty()) {
            return true;
        }
        
        // 这里可以实现SpEL表达式解析
        // 简单实现：只支持一些基本条件
        try {
            Object[] args = joinPoint.getArgs();
            
            // 示例：支持 "#p0 != null" 这样的条件
            if (condition.equals("#p0 != null")) {
                return args.length > 0 && args[0] != null;
            }
            
            // 默认返回true
            return true;
            
        } catch (Exception e) {
            logger.warn("Error evaluating condition: " + condition, e);
            return true;
        }
    }
}
