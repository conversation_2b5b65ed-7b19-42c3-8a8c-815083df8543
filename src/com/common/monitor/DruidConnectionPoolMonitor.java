package com.common.monitor;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Druid连接池监控组件
 * 实现连接池动态扩容和监控预警
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
public class DruidConnectionPoolMonitor {
    
    private static final Logger logger = Logger.getLogger(DruidConnectionPoolMonitor.class);
    
    @Autowired
    @Qualifier("dataSource")
    private DruidDataSource mainDataSource;
    
    @Autowired
    @Qualifier("qrCodeDataSource")
    private DruidDataSource qrCodeDataSource;
    
    // 连接池使用率阈值
    private static final double HIGH_USAGE_THRESHOLD = 0.8; // 80%
    private static final double CRITICAL_USAGE_THRESHOLD = 0.9; // 90%
    
    // 动态扩容参数
    private static final int EXPANSION_STEP = 20; // 每次扩容20个连接
    private static final int MAX_DYNAMIC_CONNECTIONS = 500; // 最大动态连接数
    
    @PostConstruct
    public void init() {
        logger.info("DruidConnectionPoolMonitor initialized");
        logConnectionPoolStatus();
    }
    
    /**
     * 每分钟检查连接池状态
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorConnectionPools() {
        try {
            monitorDataSource("MainDataSource", mainDataSource);
            monitorDataSource("QrCodeDataSource", qrCodeDataSource);
        } catch (Exception e) {
            logger.error("Error monitoring connection pools", e);
        }
    }
    
    /**
     * 每5分钟详细日志记录
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void logDetailedStatus() {
        logConnectionPoolStatus();
    }
    
    /**
     * 监控单个数据源
     */
    private void monitorDataSource(String name, DruidDataSource dataSource) {
        if (dataSource == null) {
            logger.warn(name + " is null, skipping monitoring");
            return;
        }
        
        try {
            int activeCount = dataSource.getActiveCount();
            int maxActive = dataSource.getMaxActive();
            int poolingCount = dataSource.getPoolingCount();
            long waitThreadCount = dataSource.getWaitThreadCount();
            
            double usageRate = (double) activeCount / maxActive;
            
            // 记录基本状态
            if (logger.isDebugEnabled()) {
                logger.debug(String.format("%s - Active: %d/%d (%.1f%%), Pooling: %d, Waiting: %d", 
                    name, activeCount, maxActive, usageRate * 100, poolingCount, waitThreadCount));
            }
            
            // 高使用率预警
            if (usageRate >= CRITICAL_USAGE_THRESHOLD) {
                logger.warn(String.format("%s CRITICAL: Connection usage %.1f%% (Active: %d/%d, Waiting: %d)", 
                    name, usageRate * 100, activeCount, maxActive, waitThreadCount));
                
                // 尝试动态扩容
                attemptDynamicExpansion(name, dataSource);
                
            } else if (usageRate >= HIGH_USAGE_THRESHOLD) {
                logger.warn(String.format("%s HIGH USAGE: Connection usage %.1f%% (Active: %d/%d, Waiting: %d)", 
                    name, usageRate * 100, activeCount, maxActive, waitThreadCount));
            }
            
            // 等待线程预警
            if (waitThreadCount > 0) {
                logger.warn(String.format("%s has %d waiting threads", name, waitThreadCount));
            }
            
        } catch (Exception e) {
            logger.error("Error monitoring " + name, e);
        }
    }
    
    /**
     * 尝试动态扩容
     */
    private void attemptDynamicExpansion(String name, DruidDataSource dataSource) {
        try {
            int currentMaxActive = dataSource.getMaxActive();
            
            if (currentMaxActive >= MAX_DYNAMIC_CONNECTIONS) {
                logger.warn(String.format("%s already at maximum connections (%d), cannot expand further", 
                    name, currentMaxActive));
                return;
            }
            
            int newMaxActive = Math.min(currentMaxActive + EXPANSION_STEP, MAX_DYNAMIC_CONNECTIONS);
            
            // 动态调整连接池大小
            dataSource.setMaxActive(newMaxActive);
            
            // 同时调整最大空闲连接数
            int newMaxIdle = (int) (newMaxActive * 0.7); // 70%作为最大空闲
            dataSource.setMaxIdle(newMaxIdle);
            
            logger.warn(String.format("%s EXPANDED: MaxActive %d -> %d, MaxIdle -> %d", 
                name, currentMaxActive, newMaxActive, newMaxIdle));
                
        } catch (Exception e) {
            logger.error("Failed to expand connection pool for " + name, e);
        }
    }
    
    /**
     * 记录详细的连接池状态
     */
    private void logConnectionPoolStatus() {
        logger.info("=== Connection Pool Status ===");
        logDataSourceDetails("MainDataSource", mainDataSource);
        logDataSourceDetails("QrCodeDataSource", qrCodeDataSource);
        logger.info("==============================");
    }
    
    /**
     * 记录数据源详细信息
     */
    private void logDataSourceDetails(String name, DruidDataSource dataSource) {
        if (dataSource == null) {
            logger.info(name + ": NULL");
            return;
        }
        
        try {
            logger.info(String.format("%s:", name));
            logger.info(String.format("  URL: %s", dataSource.getUrl()));
            logger.info(String.format("  Active: %d/%d (%.1f%%)", 
                dataSource.getActiveCount(), 
                dataSource.getMaxActive(),
                (double) dataSource.getActiveCount() / dataSource.getMaxActive() * 100));
            logger.info(String.format("  Pooling: %d (Min: %d, Max: %d)", 
                dataSource.getPoolingCount(),
                dataSource.getMinIdle(),
                dataSource.getMaxIdle()));
            logger.info(String.format("  Waiting Threads: %d", dataSource.getWaitThreadCount()));
            logger.info(String.format("  Connect Count: %d", dataSource.getConnectCount()));
            logger.info(String.format("  Close Count: %d", dataSource.getCloseCount()));
            logger.info(String.format("  Connect Error Count: %d", dataSource.getConnectErrorCount()));
            logger.info(String.format("  SQL Stat: Execute %d, Error %d", 
                dataSource.getExecuteCount(),
                dataSource.getErrorCount()));
                
        } catch (Exception e) {
            logger.error("Error logging details for " + name, e);
        }
    }
    
    /**
     * 获取连接池健康状态
     */
    public boolean isHealthy() {
        try {
            return isDataSourceHealthy(mainDataSource) && isDataSourceHealthy(qrCodeDataSource);
        } catch (Exception e) {
            logger.error("Error checking health status", e);
            return false;
        }
    }
    
    /**
     * 检查单个数据源健康状态
     */
    private boolean isDataSourceHealthy(DruidDataSource dataSource) {
        if (dataSource == null) return false;
        
        try {
            double usageRate = (double) dataSource.getActiveCount() / dataSource.getMaxActive();
            long waitThreadCount = dataSource.getWaitThreadCount();
            
            // 使用率超过95%或有等待线程认为不健康
            return usageRate < 0.95 && waitThreadCount == 0;
        } catch (Exception e) {
            return false;
        }
    }
}
