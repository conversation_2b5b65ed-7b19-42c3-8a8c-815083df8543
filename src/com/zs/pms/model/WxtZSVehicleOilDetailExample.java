package com.zs.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxtZSVehicleOilDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WxtZSVehicleOilDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNull() {
            addCriterion("t_model_id is null");
            return (Criteria) this;
        }

        public Criteria andTModelIdIsNotNull() {
            addCriterion("t_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andTModelIdEqualTo(String value) {
            addCriterion("t_model_id =", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotEqualTo(String value) {
            addCriterion("t_model_id <>", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThan(String value) {
            addCriterion("t_model_id >", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdGreaterThanOrEqualTo(String value) {
            addCriterion("t_model_id >=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThan(String value) {
            addCriterion("t_model_id <", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLessThanOrEqualTo(String value) {
            addCriterion("t_model_id <=", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdLike(String value) {
            addCriterion("t_model_id like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotLike(String value) {
            addCriterion("t_model_id not like", value, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdIn(List<String> values) {
            addCriterion("t_model_id in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotIn(List<String> values) {
            addCriterion("t_model_id not in", values, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdBetween(String value1, String value2) {
            addCriterion("t_model_id between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andTModelIdNotBetween(String value1, String value2) {
            addCriterion("t_model_id not between", value1, value2, "tModelId");
            return (Criteria) this;
        }

        public Criteria andOilNameIsNull() {
            addCriterion("oil_name is null");
            return (Criteria) this;
        }

        public Criteria andOilNameIsNotNull() {
            addCriterion("oil_name is not null");
            return (Criteria) this;
        }

        public Criteria andOilNameEqualTo(String value) {
            addCriterion("oil_name =", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameNotEqualTo(String value) {
            addCriterion("oil_name <>", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameGreaterThan(String value) {
            addCriterion("oil_name >", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameGreaterThanOrEqualTo(String value) {
            addCriterion("oil_name >=", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameLessThan(String value) {
            addCriterion("oil_name <", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameLessThanOrEqualTo(String value) {
            addCriterion("oil_name <=", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameLike(String value) {
            addCriterion("oil_name like", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameNotLike(String value) {
            addCriterion("oil_name not like", value, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameIn(List<String> values) {
            addCriterion("oil_name in", values, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameNotIn(List<String> values) {
            addCriterion("oil_name not in", values, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameBetween(String value1, String value2) {
            addCriterion("oil_name between", value1, value2, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilNameNotBetween(String value1, String value2) {
            addCriterion("oil_name not between", value1, value2, "oilName");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoIsNull() {
            addCriterion("oil_parts_no is null");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoIsNotNull() {
            addCriterion("oil_parts_no is not null");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoEqualTo(String value) {
            addCriterion("oil_parts_no =", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoNotEqualTo(String value) {
            addCriterion("oil_parts_no <>", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoGreaterThan(String value) {
            addCriterion("oil_parts_no >", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoGreaterThanOrEqualTo(String value) {
            addCriterion("oil_parts_no >=", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoLessThan(String value) {
            addCriterion("oil_parts_no <", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoLessThanOrEqualTo(String value) {
            addCriterion("oil_parts_no <=", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoLike(String value) {
            addCriterion("oil_parts_no like", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoNotLike(String value) {
            addCriterion("oil_parts_no not like", value, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoIn(List<String> values) {
            addCriterion("oil_parts_no in", values, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoNotIn(List<String> values) {
            addCriterion("oil_parts_no not in", values, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoBetween(String value1, String value2) {
            addCriterion("oil_parts_no between", value1, value2, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilPartsNoNotBetween(String value1, String value2) {
            addCriterion("oil_parts_no not between", value1, value2, "oilPartsNo");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionIsNull() {
            addCriterion("oil_consumption is null");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionIsNotNull() {
            addCriterion("oil_consumption is not null");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionEqualTo(String value) {
            addCriterion("oil_consumption =", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionNotEqualTo(String value) {
            addCriterion("oil_consumption <>", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionGreaterThan(String value) {
            addCriterion("oil_consumption >", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionGreaterThanOrEqualTo(String value) {
            addCriterion("oil_consumption >=", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionLessThan(String value) {
            addCriterion("oil_consumption <", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionLessThanOrEqualTo(String value) {
            addCriterion("oil_consumption <=", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionLike(String value) {
            addCriterion("oil_consumption like", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionNotLike(String value) {
            addCriterion("oil_consumption not like", value, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionIn(List<String> values) {
            addCriterion("oil_consumption in", values, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionNotIn(List<String> values) {
            addCriterion("oil_consumption not in", values, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionBetween(String value1, String value2) {
            addCriterion("oil_consumption between", value1, value2, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilConsumptionNotBetween(String value1, String value2) {
            addCriterion("oil_consumption not between", value1, value2, "oilConsumption");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceIsNull() {
            addCriterion("oil_total_price is null");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceIsNotNull() {
            addCriterion("oil_total_price is not null");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceEqualTo(String value) {
            addCriterion("oil_total_price =", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceNotEqualTo(String value) {
            addCriterion("oil_total_price <>", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceGreaterThan(String value) {
            addCriterion("oil_total_price >", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceGreaterThanOrEqualTo(String value) {
            addCriterion("oil_total_price >=", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceLessThan(String value) {
            addCriterion("oil_total_price <", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceLessThanOrEqualTo(String value) {
            addCriterion("oil_total_price <=", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceLike(String value) {
            addCriterion("oil_total_price like", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceNotLike(String value) {
            addCriterion("oil_total_price not like", value, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceIn(List<String> values) {
            addCriterion("oil_total_price in", values, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceNotIn(List<String> values) {
            addCriterion("oil_total_price not in", values, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceBetween(String value1, String value2) {
            addCriterion("oil_total_price between", value1, value2, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTotalPriceNotBetween(String value1, String value2) {
            addCriterion("oil_total_price not between", value1, value2, "oilTotalPrice");
            return (Criteria) this;
        }

        public Criteria andOilTypeIsNull() {
            addCriterion("oil_type is null");
            return (Criteria) this;
        }

        public Criteria andOilTypeIsNotNull() {
            addCriterion("oil_type is not null");
            return (Criteria) this;
        }

        public Criteria andOilTypeEqualTo(String value) {
            addCriterion("oil_type =", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeNotEqualTo(String value) {
            addCriterion("oil_type <>", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeGreaterThan(String value) {
            addCriterion("oil_type >", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeGreaterThanOrEqualTo(String value) {
            addCriterion("oil_type >=", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeLessThan(String value) {
            addCriterion("oil_type <", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeLessThanOrEqualTo(String value) {
            addCriterion("oil_type <=", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeLike(String value) {
            addCriterion("oil_type like", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeNotLike(String value) {
            addCriterion("oil_type not like", value, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeIn(List<String> values) {
            addCriterion("oil_type in", values, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeNotIn(List<String> values) {
            addCriterion("oil_type not in", values, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeBetween(String value1, String value2) {
            addCriterion("oil_type between", value1, value2, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilTypeNotBetween(String value1, String value2) {
            addCriterion("oil_type not between", value1, value2, "oilType");
            return (Criteria) this;
        }

        public Criteria andOilViscosityIsNull() {
            addCriterion("oil_viscosity is null");
            return (Criteria) this;
        }

        public Criteria andOilViscosityIsNotNull() {
            addCriterion("oil_viscosity is not null");
            return (Criteria) this;
        }

        public Criteria andOilViscosityEqualTo(String value) {
            addCriterion("oil_viscosity =", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityNotEqualTo(String value) {
            addCriterion("oil_viscosity <>", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityGreaterThan(String value) {
            addCriterion("oil_viscosity >", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityGreaterThanOrEqualTo(String value) {
            addCriterion("oil_viscosity >=", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityLessThan(String value) {
            addCriterion("oil_viscosity <", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityLessThanOrEqualTo(String value) {
            addCriterion("oil_viscosity <=", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityLike(String value) {
            addCriterion("oil_viscosity like", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityNotLike(String value) {
            addCriterion("oil_viscosity not like", value, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityIn(List<String> values) {
            addCriterion("oil_viscosity in", values, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityNotIn(List<String> values) {
            addCriterion("oil_viscosity not in", values, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityBetween(String value1, String value2) {
            addCriterion("oil_viscosity between", value1, value2, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilViscosityNotBetween(String value1, String value2) {
            addCriterion("oil_viscosity not between", value1, value2, "oilViscosity");
            return (Criteria) this;
        }

        public Criteria andOilGradeIsNull() {
            addCriterion("oil_grade is null");
            return (Criteria) this;
        }

        public Criteria andOilGradeIsNotNull() {
            addCriterion("oil_grade is not null");
            return (Criteria) this;
        }

        public Criteria andOilGradeEqualTo(String value) {
            addCriterion("oil_grade =", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeNotEqualTo(String value) {
            addCriterion("oil_grade <>", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeGreaterThan(String value) {
            addCriterion("oil_grade >", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeGreaterThanOrEqualTo(String value) {
            addCriterion("oil_grade >=", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeLessThan(String value) {
            addCriterion("oil_grade <", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeLessThanOrEqualTo(String value) {
            addCriterion("oil_grade <=", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeLike(String value) {
            addCriterion("oil_grade like", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeNotLike(String value) {
            addCriterion("oil_grade not like", value, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeIn(List<String> values) {
            addCriterion("oil_grade in", values, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeNotIn(List<String> values) {
            addCriterion("oil_grade not in", values, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeBetween(String value1, String value2) {
            addCriterion("oil_grade between", value1, value2, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andOilGradeNotBetween(String value1, String value2) {
            addCriterion("oil_grade not between", value1, value2, "oilGrade");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNull() {
            addCriterion("import_time is null");
            return (Criteria) this;
        }

        public Criteria andImportTimeIsNotNull() {
            addCriterion("import_time is not null");
            return (Criteria) this;
        }

        public Criteria andImportTimeEqualTo(Date value) {
            addCriterion("import_time =", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotEqualTo(Date value) {
            addCriterion("import_time <>", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThan(Date value) {
            addCriterion("import_time >", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("import_time >=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThan(Date value) {
            addCriterion("import_time <", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeLessThanOrEqualTo(Date value) {
            addCriterion("import_time <=", value, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeIn(List<Date> values) {
            addCriterion("import_time in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotIn(List<Date> values) {
            addCriterion("import_time not in", values, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeBetween(Date value1, Date value2) {
            addCriterion("import_time between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andImportTimeNotBetween(Date value1, Date value2) {
            addCriterion("import_time not between", value1, value2, "importTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}