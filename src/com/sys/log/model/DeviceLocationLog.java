package com.sys.log.model;

import java.util.Date;

/**
 * 设备位置日志-wx_device_location_log
 * <AUTHOR>
 * @version 1.0 2017-06-08 09:29
 */
public class DeviceLocationLog {

	/**  ID  **/
	private Long id;

	/**  设备类型  **/
	private String deviceType;

	/**  设备编号  **/
	private String deviceCode;

	/**  设备名称  **/
	private String deviceName;

	/**  经度  **/
	private Double longitude;

	/**  纬度  **/
	private Double latitude;

	/**  操作员  **/
	private Long operator;

	/**  操作员姓名  **/
	private String operatorName;

	/**  创建时间  **/
	private Date createTime;

	/**  扩展属性1  **/
	private String extProperty1;

	/**  扩展属性2  **/
	private String extProperty2;

	/**  扩展属性3  **/
	private String extProperty3;

	/**  扩展属性4  **/
	private String extProperty4;

	/**  扩展属性5  **/
	private String extProperty5;
	
	/** 扫码枪类型设备 */
	public final static String DEVICE_TYPE_SCANNER = "scanner";

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Long getOperator() {
		return operator;
	}

	public void setOperator(Long operator) {
		this.operator = operator;
	}

	public String getOperatorName() {
		return operatorName;
	}

	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getExtProperty1() {
		return extProperty1;
	}

	public void setExtProperty1(String extProperty1) {
		this.extProperty1 = extProperty1;
	}

	public String getExtProperty2() {
		return extProperty2;
	}

	public void setExtProperty2(String extProperty2) {
		this.extProperty2 = extProperty2;
	}

	public String getExtProperty3() {
		return extProperty3;
	}

	public void setExtProperty3(String extProperty3) {
		this.extProperty3 = extProperty3;
	}

	public String getExtProperty4() {
		return extProperty4;
	}

	public void setExtProperty4(String extProperty4) {
		this.extProperty4 = extProperty4;
	}

	public String getExtProperty5() {
		return extProperty5;
	}

	public void setExtProperty5(String extProperty5) {
		this.extProperty5 = extProperty5;
	}
}
