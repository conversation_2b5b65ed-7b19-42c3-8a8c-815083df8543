1. 查询经销商是否已存在
="union all select (select 1 from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on o.id=pe.partner_id where o.organization_name='"&D2&"' or pe.sap_code='"&C2&"')"
选不等于1的经销商导入

2. 查询经销商管理用户登录名是否存在
="union all select (select 1 from wx_t_user where login_name='"&H2&"')"
手动处理等于1的经销商管理员登录名

3. 插入经销商
="INSERT INTO [dbo].[wx_t_organization]"&
"           ([organization_code]"&
 "          ,[organization_name]"&
"           ,[parent_id]"&
  "         ,[type]"&
   "        ,[status]"&
    "       ,[sort]"&
     "      ,[create_time]"&
      "     ,[creator]"&
         "  ,[esspflag])"&
     "VALUES ('"&
       C2&
        " '  ,'"&D2&"'"&
         "  ,6"&
          " ,1"&
          " ,1"&
         "  ,10"&
         "  ,getdate()"&
          " ,1"&
           ",0);"
3.1 合伙人属性   
="INSERT INTO [dbo].[wx_t_partner_o2o_enterprise]"&
         "  ([partner_id]"&
          " ,[creation_time]"&
          " ,[created_by]"&
          " ,[partner_property]"&
          " ,[sap_code])"&
     "VALUES"&
     "      ((select o.id from wx_t_organization o where o.organization_name='"&D2&"')"&
      "     ,getdate()"&
      "     ,1"&
       "    ,'Delo Distributor'"&
       "    ,'"&C2&"');"
 
4. 插入经销商管理员用户
="INSERT INTO [dbo].[wx_t_user]"&
"           ([login_name],user_no"&
  "         ,[password]"&
 "          ,[salt]"&
 "          ,[pwd_lasttime]"&
 "          ,[ch_name]"&
 "          ,[allow_login]"&
 "          ,[email]"&
 "          ,[mobile_tel]"&
"           ,[org_id]"&
 "          ,[is_valid]"&
 "          ,[xz_time]"&
 "          ,[xz_user]"&
 "          ,[xg_time]"&
 "          ,[xg_user]"&
 "          ,[status],[reset_flag])"&
 "    VALUES"&
 "          ('"&H2&"'"&
 "          ,' ', ' '"&
 "          ,'9047D908EA0729D0'"&
 "          ,getdate()"&
 "          ,'"&G2&"'"&
 "          ,'T'"&
 "          ,'"&J2&"'"&
 "          ,'"&I2&"'"&
 "          ,(select o.id from wx_t_organization o where o.type=1 and o.organization_name='"&D2&"')"&
 "          ,'1'"&
 "          ,getdate()"&
 "          ,'cheronadmin'"&
 "          ,getdate()"&
 "          ,'cheronadmin'"&
 "          ,'1', 1);"
 
 5. 经销商用户授予合伙人超级管理员角色
 ="INSERT INTO [dbo].[wx_t_userrole]"&
  "         ([user_id]"&
  "         ,[grant_userid]"&
  "         ,[role_id]"&
  "         ,[xg_sj]"&
  "         ,[xg_user]"&
  "         ,[status]"&
 "          ,[tenant_id])"&
 "    VALUES"&
 "          ((select user_id from wx_t_user  where login_name='"&H2&"')"&
 "          ,1"&
"           ,(select role_id from wx_t_role where ch_role_name='Caltex_Dealer')"&
"           ,getdate()"&
"           ,1"&
"           ,1"&
"           ,1);"

6. 经销商授予促销积分权限 《促销积分业务配置》已配置
="INSERT INTO [dbo].[wx_t_dealer_business_fun]"&
 "          ([dealer_id]"&
 "          ,[business_fun_code]"&
 "          ,[business_custom_id])"&
 "    VALUES"&
 "          ((select id from wx_t_organization o where o.type=1 and o.organization_name='"&D2&"')"&
 "          ,'PROMOTION_POINT'"&
 "          ,(select id from wx_t_dealer_business_custom));"
 
7. 已有经销商授予促销积分权限


select u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Caltex_Dealer')           ,getdate()           ,1           ,1           ,1 from wx_t_user u 
where exists (select 1 from wx_t_dealer_business_fun dbf
 where dbf.business_custom_id=1 and dbf.dealer_id!=56497 and dbf.dealer_id=u.org_id and (u.type is null or u.type!='1'))
 and exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager', 'Service_Partner_Admin'))
 and not exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Caltex_Dealer'))


INSERT INTO [dbo].[wx_t_userrole]  ([user_id]         ,[grant_userid]         ,[role_id]         ,[xg_sj]         ,[xg_user]         ,[status]          ,[tenant_id])    
select u.user_id, 1, (select role_id from wx_t_role where ch_role_name='Caltex_Dealer')           ,getdate()           ,1           ,1           ,1 from wx_t_user u 
where exists (select 1 from wx_t_dealer_business_fun dbf
 where dbf.business_custom_id=1 and dbf.dealer_id!=56497 and dbf.dealer_id=u.org_id and (u.type is null or u.type!='1'))
 and exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager', 'Service_Partner_Admin'))
 and not exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Caltex_Dealer'))


INSERT INTO [dbo].[wx_t_partner_responsible_main]
           ([fun_flag]
           ,[user_id]
           ,[day_report_cc]
           ,[create_time]
           ,[creator])
select [fun_flag]
           ,(select u.user_id from wx_t_user u where u.login_name='promotionadmin7')
           ,[day_report_cc]
           ,getdate()
           ,[creator] from wx_t_partner_responsible_main prm 
where exists (select 1 from wx_t_user u where u.user_id=prm.user_id and u.login_name='promotionadmin1')

INSERT INTO [dbo].[wx_t_partner_responsible]
           ([partner_id]
           ,[create_time]
           ,[creator]
           ,[remark]
           ,[responsible_main_id])
select [partner_id]
           ,getdate()
           ,[creator]
           ,[remark]
           ,(select prm.id from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='promotionadmin7')
		   from [wx_t_partner_responsible] pr
where exists (select 1 from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='promotionadmin1' and prm.id=pr.responsible_main_id)

INSERT INTO [dbo].[wx_t_dealer_business_fun]          ([dealer_id]          ,[business_fun_code]          ,[business_custom_id])    
--select o.id,'MATERIAL_POINT'          ,(select id from wx_t_dealer_business_custom));

 select o.id,'MATERIAL_POINT',-1
 from wx_t_organization o 
 join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 where pe.sap_code in ('8301476',
'8148592',
'8382277')

 and not exists (select 1 from wx_t_dealer_business_fun dbf1 where dbf1.dealer_id=o.id and dbf1.business_fun_code='MATERIAL_POINT')

 --select * from wx_t_dealer_business_fun

 select * from wx_t_dealer_business_fun dbf1 where dbf1.business_fun_code='MATERIAL_POINT'    
 
 
 INSERT INTO [dbo].[wx_t_partner_responsible]
           ([partner_id]
           ,[create_time]
           ,[creator]
           ,[responsible_main_id])

 select [partner_id]
           ,getdate()
           ,1
           ,(select prm.id from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='promotionadmin7')

 from wx_t_organization o 
 join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 where not exists (select 1 from wx_t_partner_responsible pr1 where pr1.partner_id=o.id and pr1.responsible_main_id=(select prm.id from wx_t_user u 
join wx_t_partner_responsible_main prm on u.user_id=prm.user_id where u.login_name='promotionadmin7')) and pe.sap_code in ()

--select id from wx_t_dealer_business_custom
INSERT INTO [dbo].[wx_t_dealer_business_fun]          ([dealer_id]          ,[business_fun_code]          ,[business_custom_id])    
--select o.id,'MATERIAL_POINT'          ,(select id from wx_t_dealer_business_custom));

 select o.id,'MATERIAL_POINT',-1 --,o.organization_name
 from wx_t_organization o 
 join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
 where 
  not exists (select 1 from wx_t_dealer_business_fun dbf1 where dbf1.dealer_id=o.id and dbf1.business_fun_code='MATERIAL_POINT')
and o.organization_name in ('滨州市金淼商贸有限公司',
'遵义华来金汽车贸易有限公司')

delete ur from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id
left join wx_t_user u on u.user_id=ur.user_id
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where r.ch_role_name='Caltex_Dealer'
and (pe.partner_property is null or pe.partner_property != 'Delo Distributor')
and (pe.partner_property is null or (pe.partner_property != 'NORMAL' or o.create_time < '2018-01-01'))        