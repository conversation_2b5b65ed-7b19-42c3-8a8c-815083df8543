CREATE VIEW [dbo].[view_distributor_in_stock]
AS
(
	select pe1.distributor_id, o1.id partner_id, o1.organization_name, is1.ext_property1 store_id, 
	p1.name product_name, isp1.sku, 
	isnull(isnull(di2.dic_item_code, di1.dic_item_code), 'OTHERS') product_line,
	isnull(isnull(di2.dic_item_name, di1.dic_item_name), '其他') product_line_text, 
	isnull(di3.dic_item_code, 'OTHERS') category, isnull(di3.dic_item_name, '其他') category_text, 
	p1.pack*isp1.actual_in_count liters, is1.update_time in_stock_time,p1.pack, p1.capacity,p1.viscosity,s1.name store_name
	from wx_t_in_stock is1 
	left join wx_t_partner_order po1 on po1.order_no=is1.order_no
	left join wx_t_in_stock_product isp1 on isp1.stock_in_no=is1.stock_in_no
	left join wx_t_product p1 on p1.sku=isp1.sku
	left join wx_t_dic_item di1 on di1.dic_type_code='product.oilType' and di1.dic_item_code=p1.oil_type
	left join wx_t_dic_item di2 on di2.dic_type_code='product.productLine' and di2.dic_item_code=di1.dic_item_desc
	left join wx_t_dic_item di3 on di3.dic_type_code='product.category' and di3.dic_item_code=p1.category
	left join wx_t_organization o1 on o1.id=is1.stock_to
	left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=o1.id
	left join wx_t_store2021 s1 on s1.id=is1.ext_property1
	where is1.order_type='DISTRIBUTOR_DELIVERY_ORDER' and is1.status='2'
)