SELECT total_info.SAP_CODE, total_info.organization_name AS ORGANIZATION_NAME, total_info.FROZEN_TYPE_STATE, import_info.TOTAL_IMPORT_VALUE
	, ISNULL(total_info.TOTAL_PAID_VALUE - ISNULL(rollback_info.TOTAL_ROLLBACK_VALUE, 0), 0) AS TOTAL_PAID_VALUE
	, total_info.TOTAL_POINT_VALUE, ISNULL(rollback_info.TOTAL_ROLL<PERSON>CK_VALUE, 0) AS TOTAL_ROLLBACK_VALUE
	, ISNULL(minus_info.TOTAL_MINUS_VALUE, 0) AS TOTAL_MINUS_VALUE, total_info.LEFT_POINT AS TOTAL_LEFT_VALUE
	, total_info.POINT_TYPE, total_info.POINT_ACCOUNT_ID
FROM (
	SELECT pac.id AS POINT_ACCOUNT_ID, o.id AS ORG_ID, en.sap_code AS SAP_CODE, o.organization_name
		, SUM(pvd.POINT_VALUE) AS TOTAL_POINT_VALUE, SUM(pvd.POINT_PAYED) AS TOTAL_PAID_VALUE
		, SUM(pvd.POINT_VALUE - pvd.POINT_PAYED) AS LEFT_POINT, pvd.POINT_TYPE
		, CASE 
			WHEN CHARINDEX('promotion', pac.FROZEN_TYPE) > 0 THEN '冻结'
			ELSE '正常'
		END AS FROZEN_TYPE_STATE
	FROM wx_t_organization o
		LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
		LEFT JOIN wx_t_point_account pac ON pac.POINT_ACCOUNT_OWNER_ID = o.id
		LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID = pac.ID
		LEFT JOIN (
			SELECT kpi.KPI_TARGET, kpi.START_TIME, kpi.END_TIME, kpi.PARTNER_ID
			FROM dbo.wx_t_dealer_business_kpi kpi
			WHERE kpi.start_time <= getdate()
				AND kpi.end_time > getdate()
		) k
		ON k.partner_id = o.id
		LEFT JOIN dbo.dw_base_trans_sell_in dw
		ON dw.customer_name_cn = o.organization_name
			AND dw.trans_time >= k.start_time
			AND dw.trans_time < k.end_time
		LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
	WHERE 1 = 1
		AND (EXISTS (
				SELECT 1
				FROM view_customer_region_sales_channel dwrsr
				WHERE dwrsr.distributor_id = en.distributor_id
					AND dwrsr.channel_weight & 2 > 0
			)
			OR EXISTS (
				SELECT 1
				FROM wx_t_user u1
					LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
					LEFT JOIN wx_t_role r1 ON r1.role_id = ur1.role_id
					LEFT JOIN wx_t_value_transform_map vtm1
					ON vtm1.transform_type = 'ChannelWieghtMapping'
						AND vtm1.value_before_transform = r1.sales_channel
				WHERE (u1.type IS NULL
						OR u1.type != '1')
					AND u1.org_id = o.id
					AND u1.status = 1
					AND vtm1.value_after_transform & 2 > 0
			))
		AND len(en.sap_code) >= 7
		AND pvd.POINT_TYPE = 'PROMOTION_POINT'
		AND pvd.DELETE_FLAG = 0
		AND pvd.sub_type IS NULL
	GROUP BY o.id, pac.id, en.sap_code, o.organization_name, pvd.POINT_TYPE, pac.FROZEN_TYPE
) total_info
	LEFT JOIN (
		SELECT en.sap_code AS SAP_CODE, SUM(pvd.POINT_VALUE) AS TOTAL_MINUS_VALUE
		FROM wx_t_organization o
			LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
			LEFT JOIN wx_t_point_account pac ON pac.POINT_ACCOUNT_OWNER_ID = o.id
			LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID = pac.ID
			LEFT JOIN (
				SELECT kpi.KPI_TARGET, kpi.START_TIME, kpi.END_TIME, kpi.PARTNER_ID
				FROM dbo.wx_t_dealer_business_kpi kpi
				WHERE kpi.start_time <= getdate()
					AND kpi.end_time > getdate()
			) k
			ON k.partner_id = o.id
			LEFT JOIN dbo.dw_base_trans_sell_in dw
			ON dw.customer_name_cn = o.organization_name
				AND dw.trans_time >= k.start_time
				AND dw.trans_time < k.end_time
			LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
		WHERE 1 = 1
			AND len(en.sap_code) >= 7
			AND pvd.POINT_TYPE = 'PROMOTION_POINT'
			AND pvd.DELETE_FLAG = 0
			AND pb.BUSINESS_TYPE_CODE = 'CLEAR_POINT'
			AND pvd.sub_type IS NULL
		GROUP BY o.id, pac.id, en.sap_code, o.organization_name, pvd.POINT_TYPE, pac.FROZEN_TYPE
	) minus_info
	ON total_info.SAP_CODE = minus_info.SAP_CODE
	LEFT JOIN (
		SELECT en.sap_code AS SAP_CODE, SUM(pvd.POINT_VALUE) AS TOTAL_IMPORT_VALUE
		FROM wx_t_organization o
			LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
			LEFT JOIN wx_t_point_account pac ON pac.POINT_ACCOUNT_OWNER_ID = o.id
			LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID = pac.ID
			LEFT JOIN (
				SELECT kpi.KPI_TARGET, kpi.START_TIME, kpi.END_TIME, kpi.PARTNER_ID
				FROM dbo.wx_t_dealer_business_kpi kpi
				WHERE kpi.start_time <= getdate()
					AND kpi.end_time > getdate()
			) k
			ON k.partner_id = o.id
			LEFT JOIN dbo.dw_base_trans_sell_in dw
			ON dw.customer_name_cn = o.organization_name
				AND dw.trans_time >= k.start_time
				AND dw.trans_time < k.end_time
			LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
		WHERE 1 = 1
			AND len(en.sap_code) >= 7
			AND pvd.POINT_TYPE = 'PROMOTION_POINT'
			AND pvd.sub_type IS NULL
			AND pvd.DELETE_FLAG = 0
			AND pb.BUSINESS_TYPE_CODE IN (
				'DEALER_POINT_MONTHLY_IMPORT', 
				'PROMOTION_POINT_IMPORT', 
				'CALTEX_POINT_FROM_BI', 
				'CALTEX_POINT_FROM_PROMOTE', 
				'CDM_MATERIAL_POINT_IMPORT', 
				'CDM_STOCK_POINT_IMPORT', 
				'CDM_PROMOTION_POINT_IMPORT', 
				'CDM_STOCK_POINT_FROM_BI', 
				'OEM_STOCK_POINT_IMPORT', 
				'OEM_STOCK_POINT_FROM_BI', 
				'POINT_IMPORT', 
				'ADJUST_POINT', 
				'CDM_STORE_OPEN_POINT_IMPORT'
			)
		GROUP BY o.id, pac.id, en.sap_code, o.organization_name, pvd.POINT_TYPE, pac.FROZEN_TYPE
	) import_info
	ON total_info.SAP_CODE = import_info.SAP_CODE
	LEFT JOIN (
		SELECT en.sap_code AS SAP_CODE, SUM(pvd.POINT_VALUE) AS TOTAL_ROLLBACK_VALUE
		FROM wx_t_organization o
			LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
			LEFT JOIN wx_t_point_account pac ON pac.POINT_ACCOUNT_OWNER_ID = o.id
			LEFT JOIN wx_t_point_value_detail pvd ON pvd.POINT_ACCOUNT_ID = pac.ID
			LEFT JOIN (
				SELECT kpi.KPI_TARGET, kpi.START_TIME, kpi.END_TIME, kpi.PARTNER_ID
				FROM dbo.wx_t_dealer_business_kpi kpi
				WHERE kpi.start_time <= getdate()
					AND kpi.end_time > getdate()
			) k
			ON k.partner_id = o.id
			LEFT JOIN dbo.dw_base_trans_sell_in dw
			ON dw.customer_name_cn = o.organization_name
				AND dw.trans_time >= k.start_time
				AND dw.trans_time < k.end_time
			LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
		WHERE 1 = 1
			AND len(en.sap_code) >= 7
			AND pvd.POINT_TYPE = 'PROMOTION_POINT'
			AND pvd.DELETE_FLAG = 0
			AND pvd.POINT_VALUE > 0
			AND pb.BUSINESS_TYPE_CODE = 'ROLLBACK_POINT'
			AND pvd.sub_type IS NULL
		GROUP BY o.id, pac.id, en.sap_code, o.organization_name, pvd.POINT_TYPE, pac.FROZEN_TYPE
	) rollback_info
	ON total_info.SAP_CODE = rollback_info.SAP_CODE
ORDER BY total_info.organization_name

SELECT CONCAT(point_value_detail.SAP_CODE, '-', point_value_detail.IMPORT_TIME) AS IMPORT_KEY, point_value_detail.POINT_ACCOUNT_ID
	, point_value_detail.SAP_CODE, point_value_detail.ORGANIZATION_NAME, point_value_detail.POINT_TYPE, point_value_detail.IMPORT_TIME
	, SUM(point_value_detail.POINT_VALUE) AS TOTAL_IMPORT_VALUE
FROM (
	SELECT pvd.POINT_ACCOUNT_ID, o.organization_name AS ORGANIZATION_NAME, pvd.POINT_TYPE, en.sap_code AS SAP_CODE, pvd.POINT_VALUE AS POINT_VALUE
		, CASE 
			WHEN pvd.TRANS_TIME IS NOT NULL THEN FORMAT(pvd.TRANS_TIME, 'yyyy-MM-01 00:00:00')
			ELSE FORMAT(pvd.CREATION_TIME, 'yyyy-MM-01 00:00:00')
		END AS IMPORT_TIME
	FROM dbo.wx_t_point_value_detail pvd
		LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
		LEFT JOIN dbo.wx_t_organization o ON pa.POINT_ACCOUNT_OWNER_ID = o.id
		LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
		LEFT JOIN (
			SELECT kpi.KPI_TARGET, kpi.START_TIME, kpi.END_TIME, kpi.PARTNER_ID
			FROM dbo.wx_t_dealer_business_kpi kpi
			WHERE kpi.start_time <= getdate()
				AND kpi.end_time > getdate()
		) k
		ON k.partner_id = o.id
		LEFT JOIN dbo.dw_base_trans_sell_in dw
		ON dw.customer_name_cn = o.organization_name
			AND dw.trans_time >= k.start_time
			AND dw.trans_time < k.end_time
		LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
	WHERE 1 = 1
		AND (EXISTS (
				SELECT 1
				FROM view_customer_region_sales_channel dwrsr
				WHERE dwrsr.distributor_id = en.distributor_id
					AND dwrsr.channel_weight & 2 > 0
			)
			OR EXISTS (
				SELECT 1
				FROM wx_t_user u1
					LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
					LEFT JOIN wx_t_role r1 ON r1.role_id = ur1.role_id
					LEFT JOIN wx_t_value_transform_map vtm1
					ON vtm1.transform_type = 'ChannelWieghtMapping'
						AND vtm1.value_before_transform = r1.sales_channel
				WHERE (u1.type IS NULL
						OR u1.type != '1')
					AND u1.org_id = o.id
					AND u1.status = 1
					AND vtm1.value_after_transform & 2 > 0
			))
		AND len(en.sap_code) >= 7
		AND pvd.POINT_TYPE = 'PROMOTION_POINT'
		AND pvd.DELETE_FLAG = 0
		AND pvd.POINT_VALUE != 0
		AND pvd.sub_type = 'CALTEX_POINT_FROM_PROMOTE'
		AND pb.BUSINESS_TYPE_CODE IN (
			'DEALER_POINT_MONTHLY_IMPORT', 
			'PROMOTION_POINT_IMPORT', 
			'CALTEX_POINT_FROM_BI', 
			'CALTEX_POINT_FROM_PROMOTE', 
			'CDM_MATERIAL_POINT_IMPORT', 
			'CDM_STOCK_POINT_IMPORT', 
			'CDM_PROMOTION_POINT_IMPORT', 
			'CDM_STOCK_POINT_FROM_BI', 
			'OEM_STOCK_POINT_IMPORT', 
			'OEM_STOCK_POINT_FROM_BI', 
			'POINT_IMPORT', 
			'ADJUST_POINT', 
			'CDM_STORE_OPEN_POINT_IMPORT'
		)
) point_value_detail
WHERE point_value_detail.SAP_CODE IS NOT NULL
GROUP BY point_value_detail.IMPORT_TIME, point_value_detail.ORGANIZATION_NAME, point_value_detail.SAP_CODE, point_value_detail.POINT_ACCOUNT_ID, point_value_detail.POINT_TYPE
ORDER BY point_value_detail.IMPORT_TIME, ORGANIZATION_NAME