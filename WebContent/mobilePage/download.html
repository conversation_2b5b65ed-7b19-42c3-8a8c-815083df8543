<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>二维码下载</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">

    <link rel="stylesheet" href="css/mui.min.css">
    <script type="text/javascript" src="js/mui.min.js"></script>

    <style type="text/css">
        .mui-btn {
            color: #fff;
            border: 1px solid #019dda;
            background-color: #019dda;
        }
        .mui-bar {
            background-color: #019dda;
        }
        .mui-title {
            color: #fff;
        }

        .hide {
            display: none;
        }
        .popup {
            position: fixed;
            text-align: right;
            top: 0;
            z-index: 11;
        }
        .popup img {width: 100%;}
        .weixin-app-download-mask {
            background-color: #000000;
            height: 100%;
            opacity: 0.6;
            position: fixed;
            width: 100%;
            z-index: 10;
            top: 0;
        }
    </style>
</head>
<body>
<div id="weixin-app-download-mask" class="weixin-app-download-mask hide" onclick="javascript:closeCover();"></div>
<article class="popup weixin-popup hide" onclick="javascript:closeCover();">
    <img class="popup" src="img/weixin.png" alt="微信提示">
</article>
<header class="mui-bar mui-bar-nav">
    <h1 class="mui-title">Chevron合伙人</h1>
</header>
<div class="mui-content">
    <div class="mui-text-center">
        <img class="mui-media-object" src="img/logo.png">
    </div>
    <div class="mui-content-padded">
        <button type="button" class="mui-btn mui-btn-block" onclick="javascript:gotoIOS();">iOS 下载</button>
        <button type="button" class="mui-btn mui-btn-block" onclick="javascript:gotoAndroid();">Android 下载</button>
    </div>
</div>

<script>
    function addClass(el,css){
        el.className = el.className+' '+css;
    }
    function removeClass(el,css){
        el.className = el.className.replace(css,'');
    }
    function closeCover(){
        removeClass(document.querySelector('.popup'),'show');
        addClass(document.querySelector('.popup'),'hide');
        removeClass(document.querySelector('.weixin-app-download-mask'),'show');
        addClass(document.querySelector('.weixin-app-download-mask'),'hide');
        return false;
    }
    function showCover(){
        removeClass(document.querySelector('.popup'),'hide');
        addClass(document.querySelector('.popup'),'show');
        removeClass(document.querySelector('.weixin-app-download-mask'),'hide');
        addClass(document.querySelector('.weixin-app-download-mask'),'show');
        return false;
    }
    function is_weixin() {
        var ua = navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            return true;
        } else {
            return false;
        }
    }
    function gotoIOS(){
        //var str = "itms-services://?action=download-manifest&url=<%=basePath %>//app/anta-plus.plist";
        if(is_weixin()){
            showCover();
        } else {
            var str = "https://www.pgyer.com/bPbn";
            window.location.href=str;
        }
    }
    function gotoAndroid(){
        window.location.href="../app/Chevron_V1.0.apk";
    }
</script>
</body>
</html>