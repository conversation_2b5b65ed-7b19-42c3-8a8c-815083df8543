;'use strict';

$(function () {
    save_widget.init();
});

var save_widget = (function ($) {
    ;'use strict';

    var activityTypeCtrls = null,
        fundTypeCtrls = null,
        fundOwnershipCtrls = null,
        billboardTypeCtrls = null,
        promoteTypeCtrls = null,
        hasRentInvoiceSelectCtrl = null,

        orgSelect = null,
        orgStore = null,

        fundGrid = null,
        fundStore = null,

        totalFundGrid = null,
        totalFundStore = null,
        usedFundGrid = null,
        usedFundStore = null,
        actualFundGrid = null,
        actualFundStore = null,
        applyFundGrid = null,
        applyFundStore = null,
        remainInvoiceFundGrid = null,
        remainInvoiceFundStore = null,

        orgList = null,
        selectedOrgInfo = null,
        validFund = 0,
        remainFundData = [],

        imgInvoiceAddDialog = null,

        billboardPhoto = [],

        managerMailFile = [],
        scenePhotoFile = [],
        invoiceFile = [],
        marketingMailFile = [],
        customerInteractionFile = [],
        signInFile = [],
        otherFile = [],
        marketPlanFile = [],
        promotionReceiptFile = [],
        splitMailFile = [],

        requireFileList = [],

        form = null,

        detailData = null,

        loader = {},
        util = {},
        action = {};

    var foo = new $.JsonRpcClient({
        ajaxUrl: '/wxPublicRpc.do'
    });

    var req = {
            getDicItemCode: function (dicCode, callback) {
                foo.call("dicService.getDicItemByDicTypeCode", [dicCode], function (data) {
                    if (data.code == 'success') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getFileList: function (fileTypeCode, callback) {
                LoadMask.show();
                foo.call("rebateApply2Service.getRebateFileList", [fileTypeCode], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getOrgList: function (userId, callback) {
                LoadMask.show();
                foo.call("rebateApply2Service.getOrgList", [userId, rebateId], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        orgList = data.data;
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            saveRebate: function (saveRequest, callback) {
                LoadMask.show();
                $.ajax({
                    dataType: 'json',
                    type: 'post',
                    contentType: 'application/json',
                    url: '/elites2/rebate/save.do',
                    data: JSON.stringify(saveRequest),
                    success: function (data) {
                        LoadMask.hide();
                        $.isFunction(callback) && callback(data);
                    },
                })
            },
            submitRebate: function (saveRequest, callback) {
                LoadMask.show();
                $.ajax({
                    dataType: 'json',
                    type: 'post',
                    contentType: 'application/json',
                    url: '/elites2/rebate/submit.do',
                    data: JSON.stringify(saveRequest),
                    success: function (data) {
                        LoadMask.hide();
                        $.isFunction(callback) && callback(data);
                    },
                })
            },
            getDetail: function (id, callback) {
                LoadMask.show();
                foo.call("rebateApply2Service.detail", [id], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getActivityTypeByFundCode: function (fundCode, callback) {
                LoadMask.show();
                foo.call("rebateApply2Service.getActivityTypeByFundCode", [fundCode], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getPromoteTypeDic: function (callback) {
                LoadMask.show();
                common.rpcClient.call('dicService.getDicItemByDicTypeCode', ["elites2.promoteType"], function (result) {
                    LoadMask.hide();
                    if (result.code == 'success') {
                        $.isFunction(callback) && callback(result.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getFundTypeList: function (callback) {
                LoadMask.show();
                foo.call("rebateApply2Service.getFundTypeList", [], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            },
            getRemainInvoiceAmount: function () {
                LoadMask.show();
                foo.call("rebateApply2Service.getRemainInvoiceAmount", [rebateId], function (data) {
                    LoadMask.hide();
                    if (data.code == '0000') {
                        $.isFunction(callback) && callback(data.data);
                    } else {
                        common.alertMes(data.message, 'error');
                        return;
                    }
                });
            }
        },
        util = {
            getFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.bdFundCDM;
                        mktFund = data.mktFundCDM;
                        iviFund = data.iviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.bdFundCI;
                        mktFund = data.mktFundCI;
                        iviFund = data.iviFundCI;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            getTotalFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.totalBdFundCDM;
                        mktFund = data.totalMktFundCDM;
                        iviFund = data.totalIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.totalBdFundCI;
                        mktFund = data.totalMktFundCI;
                        iviFund = data.totalIviFundCI;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            getUsedFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.usedBdFundCDM;
                        mktFund = data.usedMktFundCDM;
                        iviFund = data.usedIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.usedBdFundCI;
                        mktFund = data.usedMktFundCI;
                        iviFund = data.userIviFundCI;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            getActualFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.actualBdFundCDM;
                        mktFund = data.actualMktFundCDM;
                        iviFund = data.actualIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.actualBdFundCIO;
                        mktFund = data.actualMktFundCIO;
                        iviFund = data.actualIviFundCIO;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            getApplyFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.applyBdFundCDM;
                        mktFund = data.applyMktFundCDM;
                        iviFund = data.applyIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.applyBdFundCIO;
                        mktFund = data.applyMktFundCIO;
                        iviFund = data.applyIviFundCIO;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            getRemainInvoiceFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.remainInvoiceBdFundCDM;
                        mktFund = data.remainInvoiceMktFundCDM;
                        iviFund = data.remainInvoiceIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.remainInvoiceBdFundCIO;
                        mktFund = data.remainInvoiceMktFundCIO;
                        iviFund = data.remainInvoiceIviFundCIO;
                    }

                    gridData.push({
                        channel: channel,
                        bdFund: util.transferMoneyDeciaml(bdFund),
                        mktFund: util.transferMoneyDeciaml(mktFund),
                        iviFund: util.transferMoneyDeciaml(iviFund)
                    });
                }
                return gridData;
            },
            validApplyFund: function (applyFund) {
                var invoiceAmount = $("#invoiceAmountTotal").val();
                /*if ("" != invoiceAmount && parseFloat(applyFund) != parseFloat(invoiceAmount)) {
                    common.alertMes('申请金额必须等于发票金额，请检查！', null, 'error');
                    return false;
                }*/
            },
            transferMoneyDeciaml: function (fund) {
                return parseFloat(fund).toFixed(2) * 1;
            },
            validFileAudit: function () {
                for (var billBoarIndex in billboardPhoto) {
                    if (billboardPhoto[billBoarIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var managerIndex in managerMailFile) {
                    if (managerMailFile[managerIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var sceneIndex in scenePhotoFile) {
                    if (scenePhotoFile[sceneIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var invoiceIndex in invoiceFile) {
                    if (invoiceFile[invoiceIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var marketingIndex in marketingMailFile) {
                    if (marketingMailFile[marketingIndex].auditStatus == "0") {
                        return false;
                    }
                }

                /*for (var customerBoarIndex in customerInteractionFile) {
                    if (customerInteractionFile[customerBoarIndex].auditStatus == "0") {
                        return false;
                    }
                }*/

                for (var signInIndex in signInFile) {
                    if (signInFile[signInIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var otherIndex in otherFile) {
                    if (otherFile[otherIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var marketPlanIndex in marketPlanFile) {
                    if (marketPlanFile[marketPlanIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var promotionReceiptIndex in promotionReceiptFile) {
                    if (promotionReceiptFile[promotionReceiptIndex].auditStatus == "0") {
                        return false;
                    }
                }

                for (var splitMailIndex in splitMailFile) {
                    if (splitMailFile[splitMailIndex].auditStatus == "0") {
                        return false;
                    }
                }
                return true;
            },
            getChannelText: function (channel) {
                if ("CDM" == channel) {
                    return "金富力";
                } else if ("C&I" == channel) {
                    return "德乐";
                }
            },
            initValidFund: function (data) {
                if (data == null) {
                    return;
                }

                validFund = 0;
                var fundType = $("#fundType").val();
                if (data == undefined) return;

                if (salesChannel == 'CDM') {
                    if (fundType == '2') {//市场营销基金
                        validFund = util.transferMoneyDeciaml(data.mktFundCDM);
                    }
                } else if (salesChannel == 'C&I') {
                    if (fundType == '1') {//业务发展基金
                        validFund = util.transferMoneyDeciaml(data.bdFundCDM + data.bdFundCI);
                    } else {//市场营销基金
                        validFund = util.transferMoneyDeciaml(data.mktFundCI);
                    }
                }
            },
            getImgAmountByImgId: function (imgId) {
                for (var i in invoiceFile) {
                    if (invoiceFile[i].attId == imgId) {
                        return invoiceFile[i].invoiceAmount;
                    }
                }
                return "";
            },
            verifyAmount: function (amount) {
                var reg = /^((?:-?0)|(?:-?[1-9]\d*))(?:\.\d{1,2})?$/;
                if (reg.test(amount)) {
                    return true;
                } else {
                    return false;
                }
            },
            addAmountToImg: function (imgId, amount) {
                for (var i in invoiceFile) {
                    if (invoiceFile[i].attId == imgId) {
                        if (false == util.verifyAmount(amount)) {
                            common.alertMes("请检查发票金额！", 'error');
                            return false;
                        }
                        invoiceFile[i].invoiceAmount = amount;
                        break;
                    }
                }
                return true;
            },
            getInvoiceAmountTotal: function () {
                var totalAmout = 0;
                if (invoiceFile.length > 0) {
                    for (var i in invoiceFile) {
                        totalAmout += parseFloat(invoiceFile[i].invoiceAmount);
                    }
                }
                return totalAmout.toFixed(2);
            },
            getSumApplyFund: function () {
                var totalAmount = 0;
                var applyFund = $("#applyFund").val() != null ? $("#applyFund").val() : '0';
                var useInvoiceAmount = $("#useInvoiceAmount").val() != null ? $("#useInvoiceAmount").val() : '0';
                var useIviFund = $("#useIviFund").val() != null ? $("#useIviFund").val() : '0';
                totalAmount = parseFloat(applyFund) + parseFloat(useInvoiceAmount) + parseFloat(useIviFund);
                return totalAmount.toFixed(2);
            },
            validFileInvoiceAmount: function () {
                for (var i in invoiceFile) {
                    if (invoiceFile[i].invoiceAmount == "" || invoiceFile[i].invoiceAmount == null) {
                        return false;
                    }
                }
            },
            restImgAuditStatus: function (imgId) {
                for (var i in invoiceFile) {
                    if (invoiceFile[i].attId == imgId) {
                        invoiceFile[i].auditStatus = null;
                        break;
                    }
                }
            },
            initTooltipTop: function (target, alignType) {
                BUI.use('bui/tooltip', function (Tooltip) {
                    var _tips = new Tooltip.Tips({
                        tip: {
                            trigger: target,
                            alignType: alignType,
                            elCls: 'fundTooltip',
                            offset: 10,
                            width: 750,
                            titleTpl: '<p>{title}</p>'
                        }
                    });
                    _tips.render();
                });
            },
            getIviRemain: function () {
                var fundType = $("#fundType").val();
                var fundInfo = {};
                var iviRemain = 0.0;
                if (selectedOrgInfo.salesChannel == "CDM") fundInfo = remainFundData[0];
                if (selectedOrgInfo.salesChannel == "C&I") fundInfo = remainFundData[1];
                var applyFundVal = parseFloat($("#applyFund").val());
                if (fundType == "1") { //BD
                    if (fundInfo.bdFund > 0) {
                        if (fundInfo.bdFund >= applyFundVal) {
                            iviRemain = 0.0;
                            $("#useIviFund").val(0.0);
                            $("#useIviFundDiv").hide();
                        } else {
                            iviRemain = applyFundVal - fundInfo.bdFund;
                            if (actionType == "upload")
                                $("#useIviFundDiv").show();
                        }
                    } else {
                        iviRemain = fundInfo.iviFund;
                        if (actionType == "upload")
                            $("#useIviFundDiv").show();
                        if (applyFundVal > fundInfo.iviFund) {
                            applyFundVal = fundInfo.iviFund;
                            //$("#applyFund").val(applyFundVal);
                        }
                        //$("#useIviFund").val(applyFundVal);
                    }
                }
                if (fundType == "2") { //MKT
                    if (fundInfo.mktFund > 0) {
                        if (fundInfo.mktFund >= applyFundVal) {
                            iviRemain = 0.0;
                            $("#useIviFund").val(0.0);
                            $("#useIviFundDiv").hide();
                        } else {
                            iviRemain = applyFundVal - fundInfo.mktFund;
                            if (actionType == "upload")
                                $("#useIviFundDiv").show();
                        }
                    } else {
                        iviRemain = fundInfo.iviFund;
                        if (actionType == "upload")
                            $("#useIviFundDiv").show();
                        if (applyFundVal > fundInfo.iviFund) {
                            applyFundVal = fundInfo.iviFund;
                            //$("#applyFund").val(applyFundVal);
                        }
                        //$("#useIviFund").val(applyFundVal);
                    }
                }
                if (iviRemain > fundInfo.iviFund) iviRemain = fundInfo.iviFund;
                return iviRemain.toFixed(2);
            },
            calculateUsedFund: function () {
                var fundInfo = {};
                if (selectedOrgInfo) {
                    if (selectedOrgInfo.salesChannel == "CDM") fundInfo = remainFundData[0];
                    if (selectedOrgInfo.salesChannel == "C&I") fundInfo = remainFundData[1];
                    //fundInfo = remainFundData ? remainFundData[0] : {};
                    var fundType = $("#fundType").val();
                    var applyFundVal = parseFloat($("#applyFund").val());
                    var useIviFundVal = parseFloat($('#useIviFund').val());
                    if (fundType == "1") { //BD
                        if (fundInfo.bdFund > 0) {
                            if (fundInfo.bdFund >= applyFundVal) {
                                $("#thisUsedFund").text(applyFundVal);
                            } else {
                                if (fundInfo.bdFund + useIviFundVal >= applyFundVal)
                                    $("#thisUsedFund").text(applyFundVal);
                                else {
                                    $("#thisUsedFund").text(fundInfo.bdFund + useIviFundVal);
                                }
                            }
                        } else {
                            if (useIviFundVal >= applyFundVal)
                                $("#thisUsedFund").text(applyFundVal);
                            else {
                                $("#thisUsedFund").text(useIviFundVal);
                            }
                        }
                    }
                    if (fundType == "2") { //MKT
                        if (fundInfo.mktFund > 0) {
                            if (fundInfo.mktFund >= applyFundVal) {
                                $("#thisUsedFund").text(applyFundVal);
                            } else {
                                if (fundInfo.mktFund + useIviFundVal >= applyFundVal)
                                    $("#thisUsedFund").text(applyFundVal);
                                else {
                                    $("#thisUsedFund").text(fundInfo.mktFund + useIviFundVal);
                                }
                            }
                        } else {
                            if (useIviFundVal >= applyFundVal)
                                $("#thisUsedFund").text(applyFundVal);
                            else {
                                $("#thisUsedFund").text(useIviFundVal);
                            }

                        }
                    }
                    return $("#thisUsedFund").text() != null ? parseFloat($("#thisUsedFund").text()) : 0.0;
                }
                return 0.0;
            },
            checkApplyFundInput: function () {
                var invoiceRedundant = parseFloat($("#invoiceAmountTotal").val()) + parseFloat($('#useInvoiceAmount').val()) - parseFloat($('#applyFund').val());
                if (invoiceRedundant < 0) {
                    return {code: -1, msg: "发票累加金额与使用剩余发票金额之和 必须大于等于申请金额！"};
                }
                if (selectedOrgInfo) {
                    var thisUsedFund = util.calculateUsedFund();
                    util.setExcessInvoice(thisUsedFund);
                }
                return {code: 0, msg: ""};
            },
            setExcessInvoice: function (thisUsedFund) {
                var invoiceRedundant = parseFloat($("#invoiceAmountTotal").val()) + parseFloat($('#useInvoiceAmount').val()) - thisUsedFund;
                if (isNaN(invoiceRedundant)) invoiceRedundant = 0.00;
                $("#excessInvoice").text(invoiceRedundant.toFixed(2));
            }
        },
        loader = {
            initFundInput: function () {

                $('#useInvoiceAmount').bind('input propertychange', function (e) {
                    var totalAmount = util.getSumApplyFund();
                    $("#sumFund").html(totalAmount + "元");
                    var ret = util.checkApplyFundInput();
                    //if (ret.code != 0) common.alertMes(ret.msg, 'error');
                });


                $('#useIviFund').bind('input propertychange', function (e) {
                    var totalAmount = util.getSumApplyFund();
                    var iviRemain = util.getIviRemain();
                    $("#sumFund").html(totalAmount + "元");
                    var useIviFund = $("#useIviFund").val() != null ? $("#useIviFund").val() : '0';
                    if (parseFloat(useIviFund) > iviRemain) {
                        common.alertMes("已超本次可用的奖励基金（IVI）金额：" + iviRemain + "元", 'error');
                        $("#useIviFund").val(iviRemain);
                    }
                    var usedFund = util.calculateUsedFund();
                    util.setExcessInvoice(usedFund);
                });

                $('#applyFund').bind('input propertychange', function (e) {
                    var iviRemain = util.getIviRemain();
                    $("#iviRemain").text(iviRemain);

                    //申请金额大于当前BD/MKT时，使用IVI
                    var applyFund = parseFloat($('#applyFund').val());

                    //var ret = util.checkApplyFundInput();
                    //if (ret.code != 0) common.alertMes(ret.msg, 'error');
                    var usedFund = util.calculateUsedFund();
                    util.setExcessInvoice(usedFund);
                });

                $("#invoiceAmountTotal").bind('input propertychange', function (e) {
                    var ret = util.checkApplyFundInput();
                    //if (ret.code != 0) common.alertMes(ret.msg, 'error');
                });
            },
            initRebateSave: function () {
                loader.initFundInput();
                loader.initPromoteType();

                if (rebateId) {
                    req.getDetail(rebateId, function (data) {
                        detailData = data;
                        loader.initFundType(loader.initFundTypeCallback, data);
                        loader.initOrgList();
                        loader.renderForm();
                        loader.initView(data);
                    });
                } else {
                    if (!rebateId) $("#applyFundGroup").hide();
                    loader.initFundType();
                    loader.initOrgList();
                    loader.renderForm();
                }
            },
            initView: function (data) {
                if (rebateId) {
                    if (actionType == "update") {
                        $("#invoiceAmountDiv").hide();
                    } else if (actionType == "upload") {
                        $(".requireFile").each(function () {
                            $(this).show();
                        });
                        $("#applyOption").show();
                        $("#invoiceAmountDiv").show();
                    } else {
                        $(".requireFile").each(function () {
                            $(this).hide();
                        });
                    }
                    if (hasRentInvoiceSelectCtrl)
                        hasRentInvoiceSelectCtrl.setSelectedValue(data.hasRentInvoice);
                }
                if (actionType == "upload") {
                    salesChannel = detailData.fundOwnership;
                    action.disableActivityControls();
                }
                if (data.status >= 2 && data.status != 3) {
                    $("#applyFundGroup").show();
                }
                var ret = util.checkApplyFundInput();

            },
            renderForm: function () {
                BUI && BUI.use('bui/form', function (Form) {
                    //设置全局表单对象
                    form = new Form.Form({
                        srcNode: '#saveRebateForm',
                    }).render();

                    if (detailData) {
                        form.setFieldValue("organizationName", detailData.organizationName);
                        form.setFieldValue("applyFund", detailData.applyFundStr);
                        form.setFieldValue("applyFundPlan", detailData.applyFundPlanStr);
                        form.setFieldValue("fundOwnership", detailData.fundOwnership);
                        form.setFieldValue("activityDesc", detailData.activityDesc);
                        form.setFieldValue("customerInteractionDesc", detailData.customerInteractionDesc);
                        form.setFieldValue("descirption", detailData.descirption);
                        form.setFieldValue("invoiceAmountTotal", detailData.invoiceAmountTotalStr);
                        form.setFieldValue("activityTheme", detailData.activityTheme);
                        form.setFieldValue("customerNumber", detailData.customerNumber);
                        $("#invoiceAmountTotal").val(detailData.invoiceAmountTotalStr);
                        $("#descirption").val(detailData.descirption);
                        $("#fundOwnershipText").val(util.getChannelText(detailData.fundOwnership));
                        $(".activity-detail input").removeAttr("data-tip");

                        $("#fundOwnershipDiv").show();
                        $("#orgName").val(detailData.organizationName);
                        $("#fundOwnership").val(detailData.fundOwnership);
                        $("#fundOwnershipText").val(util.getChannelText(detailData.fundOwnership));
                        $("#useInvoiceAmount").val(detailData.useInvoiceAmount);
                        $("#useIviFund").val(detailData.useIviFund);
                        $("#sumFund").html((detailData.applyFundStr + detailData.useInvoiceAmount + detailData.useIviFund) + "元");
                        $("#invoiceRemain").html(detailData.remainInvoiceAmount);
                        if (detailData.status > 0) $("#header-title").html("兑换申请");
                    }
                    if ((actionType == "update" || actionType == "upload") && detailData.currentStep > 1) {
                        if (detailData.remainInvoiceAmount > 0)
                            $("#useInvoiceAmountDiv").show();
                        //$("#useIviFundDiv").show();
                        $("#sumFundDiv").show();
                    }
                });
            },
            hasRentInvoiceSelect: function () {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    var items = [{"value": "-1", "text": "请选择"}, {"value": "1", "text": "可提供"}, {
                        "value": "0",
                        "text": "不可提供"
                    }];

                    //基金类型下拉框初始化
                    hasRentInvoiceSelectCtrl = new Select.Select({
                        render: '#hasRentInvoiceDiv',
                        items: items,
                        valueField: '#hasRentInvoice'
                    });
                    hasRentInvoiceSelectCtrl.render();

                    hasRentInvoiceSelectCtrl.on('change', function (ev) {
                        var data;
                        if (ev.value == -1) {
                            return;
                        } else {
                            if (ev.value == 1) {
                                $("#invoiceTip").hide();
                                $("#invoiceUploaderLabel").html("发票复印件");
                            }
                            if (ev.value == 0) {
                                $("#invoiceTip").show();
                                $("#invoiceUploaderLabel").html("房屋租赁合同复印件");
                            }
                        }
                    });

                    if (rebateId && detailData) {
                        hasRentInvoiceSelectCtrl.setSelectedValue(detailData.hasRentInvoice + "");
                    } else {
                        hasRentInvoiceSelectCtrl.setSelectedValue("-1");
                    }
                });
            },
            initActivityType: function (data) {
                if (!activityTypeCtrls) {
                    if (!data) {
                        data = [{"value": "-1", "text": "请选择申报项目"}];
                    }

                    BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                        activityTypeCtrls = new Select.Select({
                            render: '#activityTypeDiv',
                            items: data,
                            valueField: '#activityType',
                            width: 230
                        });
                        activityTypeCtrls.render();
                        $(".activity-detail").hide();

                        activityTypeCtrls.on('change', function (ev) {
                            //隐藏所有证明文件上传域
                            $(".requireFile").each(function () {
                                $(this).hide();
                            });

                            //隐藏广告牌文件上传域和文本框
                            $("#billboardGroup").hide();
                            $("#billboardUploader").hide();
                            //隐藏广告牌说明
                            $("#billboardTypeDesc").html("");

                            if (ev.value == -1) {
                                $(".activity-detail").hide();
                                $("#activityType").val(null);
                                $("#customerInteractionDescDiv").hide();
                                $("#activityDescDiv").hide();
                            } else {
                                $("#activityType").val(ev.value);
                                if (ev.value != 2) $("#customerInteractionDescDiv").hide();
                                if (ev.value == 2) {//客户互动
                                    if (actionType == "upload" && rebateId)
                                        $("#customerInteractionDescDiv").show();
                                    $("#activityDescLabel").text("预计顾客互动效果说明:");
                                    $("#activityDesc").attr("data-tip", "{text:'请填写预计顾客互动效果说明',iconCls:'icon-ok'}");
                                    $("#activityDescDiv").show();
                                    if (!rebateId) {
                                        form.getField('activityDesc').addRule("required", true);
                                        form.getField('customerInteractionDesc').removeRule("required");
                                    } else {
                                        form.getField('activityDesc').removeRule("required");
                                        form.getField('customerInteractionDesc').addRule("required", true);
                                    }
                                } else if (ev.value == 3) {//广告牌
                                    $("#billboardGroup").show();
                                    loader.initBillboardType();
                                    //更改实物/会议文本说明
                                    $("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物照：');
                                } else if (ev.value == 5) {
                                    $("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>活动图片：');
                                } else if (ev.value == 6 || ev.value == 7 || ev.value == 8 || ev.value == 9 || ev.value == 10) {
                                    $("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物照片：');
                                } else if (ev.value == 11 || ev.value == 12) {
                                    $("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>会议照片：');
                                } else {
                                    $("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物/会议现场照：');
                                }

                                $(".activity-detail").hide();
                                if (ev.value == 1 || ev.value >= 6) {
                                    $("#activityDescLabel").text("申报项目说明:");
                                    $("#activityDesc").attr("data-tip", "{text:'请填写申报项目说明',iconCls:'icon-ok'}");
                                    $("#activityDescDiv").show();
                                }
                                if (ev.value == 3 || ev.value == 4 || ev.value == 5) {
                                    $("#activityDescDiv").hide();
                                }

                                //liyu
                                //$(".activity-detail input").val("");
                                //$(".activity-detail textarea").val("");
                                $("#detail-div-" + ev.value).show();

                                if (ev.value == 9 || ev.value == 10) {
                                    if (rebateId && detailData.currentStep >= 2) {
                                        $("#hasRentInvoiceGroup").show();
                                        loader.hasRentInvoiceSelect();
                                    }
                                } else {
                                    $("#hasRentInvoiceGroup").hide();
                                    $("#invoiceTip").hide();
                                }


                                //加载证明文件
                                req.getFileList(ev.value, function (data) {
                                    for (var i in data) {
                                        var uploaderDom = data[i].fileTypeCode + "Uploader";
                                        var pickerDom = data[i].fileTypeCode + "Picker";
                                        var fileListDom = data[i].fileTypeCode + "List";
                                        var fileList = eval(data[i].fileTypeCode + "File");

                                        $("#" + fileListDom).empty();
                                        var uploadBtnDiv = '<div class="uploadBtn" id=' + pickerDom + '></div>';
                                        $("#" + fileListDom).html(uploadBtnDiv);
                                        fileList.splice(0, fileList.length);

                                        if (detailData) {
                                            var detailDataList = data[i].fileTypeCode + "FileList";
                                            if (detailData[detailDataList].length != 0) {
                                                var detailFileList = detailData[detailDataList];
                                                for (var key in detailFileList) {
                                                    fileList.push({
                                                        attId: detailFileList[key].attId,
                                                        auditStatus: detailFileList[key].auditStatus,
                                                        opinion: detailFileList[key].opinion,
                                                        invoiceAmount: detailFileList[key].invoiceAmount,
                                                        downloadType: detailFileList[key].downloadType,
                                                        fileName: detailFileList[key].fileName,
                                                        storageName: detailFileList[key].storageName,
                                                    });
                                                }
                                            }
                                        }

                                        if (actionType == "upload") {
                                            $("#" + uploaderDom).show();
                                        }
                                        if (data[i].fileTypeCode != "other") {
                                            requireFileList.push(fileList);
                                        }
                                        loader.initFileUpload(data[i].fileTypeCode, pickerDom, fileListDom, "21", fileList);
                                    }
                                });
                            }
                        });

                        if (detailData) {
                            activityTypeCtrls.setSelectedValue(detailData.activityType);
                        } else {
                            activityTypeCtrls.setSelectedValue('-1');
                        }
                        if (actionType == "upload") {
                            activityTypeCtrls.disable();
                        }
                    });
                } else {
                    activityTypeCtrls.set("items", data);
                    activityTypeCtrls.setSelectedValue('-1');
                }
            },
            initFundType: function (callback, ret) {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    req.getFundTypeList(function (data) {
                        var items = [{"value": "-1", "text": "请选择基金类型"}];

                        //获取基金类型数据
                        for (var i = 0; i < data.length; i++) {
                            items.push({
                                text: data[i].message,
                                value: data[i].code,
                                activityTypeList: data[i].activityTypeList
                            });
                        }

                        //基金类型下拉框初始化
                        fundTypeCtrls = new Select.Select({
                            render: '#fundTypeDiv',
                            items: items,
                            valueField: '#fundType',
                            width: 230
                        });
                        fundTypeCtrls.render();

                        fundTypeCtrls.on('change', function (ev) {
                            $("#fundType").val(ev.value);
                            var data;
                            if (ev.value == -1) {
                                data = [{"value": "-1", "text": "请选择申报项目"}];
                                $("#fundType").val(null);
                                loader.initActivityType(data);
                                validFund = 0;
                                loader.initFundInput();
                                return;
                            } else {
                                req.getActivityTypeByFundCode(ev.value, function (data) {
                                    data.unshift({
                                        value: "-1",
                                        text: "请选择申报项目"
                                    });
                                    loader.initActivityType(data);
                                });
                            }
                            loader.renderFundApplyInput(-1);
                        });

                        fundTypeCtrls.setSelectedValue(detailData ? detailData.fundType : "-1");

                        $.isFunction(callback) && callback(ret);
                    });
                });
            },
            initFundTypeCallback: function (data) {
                fundTypeCtrls.setSelectedValue(data.fundType);

                var activityType = data.activityType;
                if (activityTypeCtrls)
                    activityTypeCtrls.setSelectedValue(activityType);
                if (data.fundType == "1") {
                    $(".activity-detail").hide();
                }
                if (hasRentInvoiceSelectCtrl)
                    hasRentInvoiceSelectCtrl.setSelectedValue(data.hasRentInvoice + "");

                var dateText = "{startDate} 至 {endDate}";
                var startDate = common.formatDate(new Date(data.startDate), 'yyyy-MM-dd');
                var endDate = common.formatDate(new Date(data.endDate), 'yyyy-MM-dd')
                if (activityType == "2") {//客户互动
                    $("#activityTheme").text(data.activityTheme);
                    $("#customerNumber").text(data.customerNumber);
                    $("#dateActivity").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                    $("#startDateActivity").val(startDate);
                    $("#endDateActivity").val(endDate);
                } else if (activityType == "3") {//广告牌使用
                    $("#billboardContent").val(data.billboardContent);
                    $("#dateBillboard").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                    $("#startDateBillboard").val(startDate);
                    $("#endDateBillboard").val(endDate);
                } else if (activityType == "4") {//小型设备
                    $("#deviceDetail").val(data.deviceDetail);
                } else if (activityType == "5") {//当地促销
                    /*req.getPromoteTypeDic(function (dic) {
                        for (var i = 0; i < dic.length; i++) {
                            if (data.promoteType == dic[i].dicItemCode) {
                                $("#promoteType").text(dic[i].dicItemName);
                                break;
                            }
                        }
                    });*/
                    if (promoteTypeCtrls) {
                        promoteTypeCtrls.setSelectedValue(data.promoteType);
                        if (actionType == "upload")
                            promoteTypeCtrls.disable();
                    }
                    $("#promoteProduct").val(data.promoteProduct);
                    $("#promoteNumber").val(data.promoteNumber);
                    $("#datePromote").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                    $("#startDatePromote").val(startDate);
                    $("#endDatePromote").val(endDate);
                }
                $("#detail-div-" + activityType).show();
                if (actionType == "upload") {
                    fundTypeCtrls.disable();
                    $("#applyFund").val(data.applyFundStr);
                    if (detailData && (detailData.status == "2" || detailData.status == "4")) {
                        $("#applyFundPlan").attr("disabled", "disabled");
                    } else {

                    }
                } else if (actionType == "update") {
                    $("#applyFund").val(data.applyFundStr);
                }
            },
            initPromoteType: function () {
                /*promoteTypeCtrls = Ctrls.DicItem.init('#saveRebateForm input[name=promoteType]', 'elites2.promoteType', {
                    emptyValue: '',
                    emptyText: '空'
                });*/
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    req.getPromoteTypeDic(function (data) {
                        var items = [{"value": "-1", "text": "请选择促销类型"}];

                        //获取基金类型数据
                        for (var i = 0; i < data.length; i++) {
                            items.push({
                                text: data[i].dicItemName,
                                value: data[i].dicItemCode
                            });
                        }

                        //下拉框初始化
                        promoteTypeCtrls = new Select.Select({
                            render: '#promoteTypeDiv',
                            items: items,
                            valueField: '#promoteType',
                            width: 230
                        });
                        promoteTypeCtrls.render();

                        /*for (var i = 0; i < dic.length; i++) {
                            if (data.promoteType == dic[i].dicItemCode) {
                                $("#promoteType").text(dic[i].dicItemName);
                                break;
                            }
                        }*/
                    });
                });
                if (promoteTypeCtrls) {
                    if (detailData) {
                        promoteTypeCtrls.setSelectedValue(detailData.promoteType);
                    } else {
                        promoteTypeCtrls.setSelectedValue('-1');
                    }
                }
            },
            renderFundApplyInput: function (type) {

                if (-1 == type) {
                    $("#applyFund").val(0);
                    //$("#sumFund").html("0元");
                }

                if (actionType != "upload") {
                    $("#applyFund").removeAttr("disabled");
                }

                //$("#applyFundTip").html("申报金额可以大于剩余可用基金(含税)");

                //正常模式
                if (null != selectedOrgInfo) {
                    $("#fundOwnership").val(salesChannel);
                    $("#fundOwnershipText").val(util.getChannelText(salesChannel));
//                    $("#fundOwnership").val(selectedOrgInfo.salesChannel);
//                    $("#fundOwnershipText").val(util.getChannelText(selectedOrgInfo.salesChannel));
                } else {
                    if (rebateId && detailData) {
                        $("#fundOwnership").val(detailData.fundOwnership);
                        $("#fundOwnershipText").val(util.getChannelText(detailData.fundOwnership));
                    }
                }
                if (salesChannel == "CDM") {
                    //CDM无法申请特殊企业MKT
                    if (fundTypeCtrls.getSelectedValue() == 1) {
                        $("#applyFundTip").html("无法申请基金");
                        $("#applyFund").attr("disabled", "disabled");
                        $("#fundOnwershipDiv").hide();
                        common.alertMes("无法申请基金, 请重新选择基金类型或经销商！", 'error');
                        return;
                    }
                    //CDM申请特殊企业BD+MKT都是双选框
                    loader.changeCdmApplyFund();
                    //loader.changeCioApplyFund();
                    $("#applyFundGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额(元,含税):');

                    //CDM申请非特殊企业MKT单选框
                    if (fundTypeCtrls.getSelectedValue() == 2) {
                        $("#fundOnwershipDiv").show();

                        if (detailData && detailData.status >= 2 && detailData.status != 3)
                            $("#applyFundGroup").show();

                        $("#applyFund").attr("data-rules", "{}");
                    }
                } else if (salesChannel == "C&I") {
                    loader.changeCdmApplyFund();
                    //loader.changeCioApplyFund();
                    $("#applyFundGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额(元,含税):');
                    //$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额(元,含税):');

                    //CIO申请非特殊企业MKT单选框
                    if (fundTypeCtrls.getSelectedValue() == 2) {
                        $("#fundOnwershipDiv").show();
                        if (detailData && detailData.status >= 2 && detailData.status != 3)
                            $("#applyFundGroup").show();
                        $("#applyFund").attr("data-rules", "{}");
                    }
                }

                util.initValidFund(selectedOrgInfo);
            },
            changeCdmApplyFund: function () {
                $("#applyFund").on("blur", function (e) {
                    var total = util.transferMoneyDeciaml($("#applyFund").val()) + util.transferMoneyDeciaml($("#applyFund").val());
                    $("#sumFund").html(total + "元");
                });
            },
            /*changeCioApplyFund: function () {
                $("#applyFundCio").on("blur", function (e) {
                    var total = util.transferMoneyDeciaml($("#applyFundCdm").val()) + util.transferMoneyDeciaml($("#applyFundCio").val());
                    $("#sumFund").html(total + "元");
                });
            },*/
            initOrgList: function () {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    var userId = (detailData ? detailData.createBy : null);
                    req.getOrgList(userId, function (data) {
                        if (orgSelect) {
                            orgStore.setResult(data);
                            return;
                        }
                        orgStore = new Data.Store({
                            data: data,
                            autoload: true
                        });
                        orgSelect = new Select.Select({
                            render: '#orgListDiv',
                            list: {
                                itemTpl: '<li>{orgName}</li>',
                                idField: 'orgId',
                                elCls: 'bui-select-list'
                            },
                            store: orgStore,
                            valueField: '#orgList',
                            width: 230
                        });
                        orgSelect.render();
                        orgSelect._uiSetDisabled();

                        orgSelect.on('change', function (ev) {
                            var item = ev.item;
                            $("#orgName").val(item.orgName);
                            //$("#fundOnwershipDiv").hide();
//                            $("#fundOwnership").val(item.salesChannel);
//                            $("#fundOwnershipText").val(util.getChannelText(item.salesChannel));

                            loader.initTotalFundGrid(item);//已累计基金总额（含税）
                            util.initTooltipTop('#totalToolTip', 'bottom-left');
                            $("#totalfundGridDiv").show();

                            loader.initUsedFundGrid(item);//已申报发票总额（含税）
                            util.initTooltipTop('#declaredToolTip', 'bottom-right');
                            $("#usedfundGridDiv").show();

                            loader.initApplyFundGrid(item);//已申报基金总额（含税）
                            util.initTooltipTop('#applyFundToolTip', 'bottom-left');
                            $("#applyfundGridDiv").show();

                            loader.initActualFundGrid(item);//已兑现基金总额（含税）
                            util.initTooltipTop('#actualFundToolTip', 'bottom-right');
                            $("#actualfundGridDiv").show();

                            loader.initFundGrid(item);//剩余
                            util.initTooltipTop('#surplusToolTip', 'bottom-left');
                            $("#fundGridDiv").show();

//					        loader.initRemainInvoiceFundGrid(item);
//					        $("#remainInvoicefundGridDiv").show();

                            $(".tableDiv").show().css("display", "inline-block");

                            //初始化基金类型
                            if (detailData) {
                                $("#fundOnwershipDiv").show();
                                fundTypeCtrls.setSelectedValue(detailData.fundType);
                            } else {
                                fundTypeCtrls.setSelectedValue("-1");
                            }

                            selectedOrgInfo = item;
                            remainFundData = util.getFundGridData(item);
                            var iviRemain = util.getIviRemain();
                            $("#iviRemain").text(iviRemain);
                            if (iviRemain > 0 && actionType == "upload")
                                $("#useIviFundDiv").show();
                            var usedFund = util.calculateUsedFund();
                            util.setExcessInvoice(usedFund);
                        });
                        if (actionType == "upload") {
                            if (orgSelect)
                                orgSelect.disable();
                            if (activityTypeCtrls)
                                activityTypeCtrls.disable();
                        }

                        if (detailData) {
                            orgSelect.setSelectedValue(detailData.organizationId);
                            loader.renderFundApplyInput()
                            $("#applyFund").val(detailData.applyFundStr);
                            //$("#sumFund").html(util.transferMoneyDeciaml(detailData.applyFundStr) + "元");
                            $("#fundOnwershipDiv").show();
                        }
                    });
                });
            },
            initBillboardType: function () {
                if (!billboardTypeCtrls) {
                    var items = [{"value": "-1", "text": "请选择门头类型"}, {"value": "0", "text": "全包"}, {
                        "value": "1",
                        "text": "安装费"
                    }];

                    BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                        billboardTypeCtrls = new Select.Select({
                            render: '#billboardTypeDiv',
                            items: items,
                            valueField: '#billboardType',
                            width: 230
                        });
                        billboardTypeCtrls.render();
                        billboardTypeCtrls.on('change', function (ev) {
                            if (ev.value == -1) {
                                $("#billboardType").val(null)
                            } else {
                                $("#billboardType").val(ev.value);

                                var billboardDesc = "";
                                if (ev.value == 0) {//全包
                                    billboardDesc = "设计及安装全包";
                                } else {//安装费
                                    billboardDesc = "市场部已经负担门头制作费用";
                                }

                                if ($("#billboardTypeDesc").length > 0) {
                                    $("#billboardTypeDesc").html(billboardDesc);
                                } else {
                                    var $billboardTypeDesc = $('<label id="billboardTypeDesc" style="margin-left:-15px;">' + billboardDesc + ' </label>');
                                    $("#billboardGroup").append($billboardTypeDesc);
                                }
                            }
                        });

                        if (detailData) {
                            billboardTypeCtrls.setSelectedValue(detailData.billboardType);
                        } else {
                            billboardTypeCtrls.setSelectedValue('-1');
                        }
                    });
                } else {
                    billboardTypeCtrls.setSelectedValue('-1');
                }
            },
            initFileUpload: function (fileType, pickerDom, listDom, sourceType, photoList) {
                var srouceId = 1;
                if (detailData) {
                    srouceId = rebateId;
                }
                //针对发票每次只能上传单张图片
                var imgMultiple = true;
                if ("invoice" == fileType) {
                    imgMultiple = false;
                }
                //针对excel和图片不同处理, //liyu,不使用了放开权限，让后台去判断
                var accept = {};
                if (fileType == "marketPlan" || fileType == "promotionReceipt") {
                    accept = {
                        title: 'Applications',
                        extensions: 'xls,xlsx,msg,pdf,gif,jpg,jpeg,bmp,png',
                        mimeTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf,application/vnd.ms-outlook,image/*'
                    };
                } else {
                    accept = {
                        title: 'Images/PDF',
                        extensions: 'gif,jpg,jpeg,bmp,png,pdf,msg',
                        mimeTypes: 'image/*,application/pdf,application/vnd.ms-outlook'
                    };
                }
                var uploader = WebUploader.create({
                    auto: true,
                    server: common.ctx + 'uploadForAppAttchmentFile.do',
                    pick: {
                        id: '#' + pickerDom,
                        multiple: imgMultiple,
                    },
                    fileVal: 'myfiles',
                    formData: {
                        sourceType: sourceType,
                        sourceId: srouceId
                    }
                    //,accept: accept
                });
                //文件上传队列
                uploader.on('fileQueued', function (file) {
                    var $li = $(
                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                        '<img>' +
                        '</div>'
                    );
                    var $img = $li.find('img');
                    $("#" + listDom).prepend($li);

                    var thumbnailWidth = 100;
                    var thumbnailHeight = 100;
                    uploader.makeThumb(file, function (error, src) {
                        if (error) {
                            $img.replaceWith('<span>不能预览</span>');
                            return;
                        }

                        $img.attr('src', src);
                    }, thumbnailWidth, thumbnailHeight);

                });
                //上传进度
                uploader.on('uploadProgress', function (file, percentage) {
                    var $li = $('#' + file.id),
                        $percent = $li.find('.progress span');

                    // 避免重复创建
                    if (!$percent.length) {
                        $percent = $('<p class="progress"><span></span></p>').appendTo($li).find('span');
                    }

                    $percent.css('width', percentage * 100 + '%');
                });
                //上传成功
                uploader.on('uploadSuccess', function (file, response) {
                    if (response.code == "success") {
                        $("#" + file.id).remove();
                        uploader.removeFile(file);

                        var fileAttId = response.attachmentFileList[0].attId;
                        var responseFileType = response.attachmentFileList[0].fileType;
                        var storageName = response.attachmentFileList[0].storageName;
                        var fileName = response.attachmentFileList[0].fileName;
                        var extension = "";
                        if (storageName != undefined && storageName != "") {
                            var i = storageName.lastIndexOf(".");
                            if (i > -1) extension = storageName.substring(i + 1);
                        }

                        var $imgDom = null;
                        if (responseFileType.indexOf("application") != -1) {//针对EXCEL,PDF
                            var imgSrc = "/images/" + extension + ".png";
                            /*var mimeTypeSuffix=responseFileType.substring(12);
                            if(mimeTypeSuffix=="pdf"){
                                imgSrc = "/images/pdf.png";
                            }else if(mimeTypeSuffix=="vnd.ms-outlook"|| mimeTypeSuffix=="octet-stream"){
                                imgSrc = "/images/outlook-msg.png";
                            }else{
                                imgSrc = "/images/excel-l.png";
                            }*/
                            $imgDom = $('<div id="' + fileAttId + '" class="file-item thumbnail">' +
                                '<img src="' + imgSrc + '"' +
                                //'onclick="rebate_common.showLargerImg(' + fileAttId + ')"' +
                                '>' + '<span class="desc">' + fileName + '</span>' +
                                '</div>');
                        } else {//针对图片
                            $imgDom = $('<div id="' + fileAttId + '" class="file-item thumbnail">' +
                                '<img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=' +
                                sourceType + '&attId=' + fileAttId + '" ' +
                                'onclick="rebate_common.showLargerImg(' + fileAttId + ')"' +
                                'onerror="rebate_common.imageError(this)">' + fileName +
                                '</div>');
                        }

                        var $actionDom = $(
                            '<div class="file-panel">' +
                            '<span class="cancel" title="删除图片" onclick="save_widget.removeImg(' + fileAttId + ',\'' + fileType + '\')">删除</span>' +
                            '</div>'
                        );

                        //针对发票需要录入发票金额
                        if ("invoice" == fileType) {
                            var $amountInput = $(
                                '<span class="addAmount" title="添加发票金额" onclick="save_widget.addInvoiceAmount(' + fileAttId + ')">添加发票金额</span>'
                            );
                            $actionDom.append($amountInput);
                        }

                        //针对EXCEL需要下载文件
                        if (responseFileType.indexOf("application") != -1) {
                            var $download = $(
                                '<a class="downloadFile" title="下载文件" href="/downloadAttachmentFile.do?attId=' + fileAttId + '"></a>'
                            );
                            $actionDom.append($download);
                        }

                        $imgDom.append($actionDom);

                        $("#" + listDom).prepend($imgDom);

                        photoList.push({
                            attId: response.attachmentFileList[0].attId,
                            auditStatus: null,
                            opinion: null,
                            invoiceAmount: null,
                            downloadType: responseFileType
                        });

                        $('#' + fileAttId).addClass('upload-state-done');

                        if ("invoice" == fileType) {
                            loader.showAddInvoiceAmountDialog(fileAttId);
                        }

                    } else {
                        var $li = $('#' + file.id);
                        var $error = $li.find('div.error');
                        $error = $('<div class="error"></div>').appendTo($li);
                        $error.attr("onclick", "$('#" + file.id + "').remove();");
                        $error.text('上传失败，点击删除图片');
                    }
                });
                //上传失败
                uploader.on('uploadError', function (file) {
                    var $li = $('#' + file.id),
                        $error = $li.find('div.error');
                    // 避免重复创建
                    if (!$error.length) {
                        $error = $('<div class="error"></div>').appendTo($li);
                        $error.attr("onclick", "$('#" + file.id + "').remove();");
                    }
                    $error.text('上传失败，点击删除图片');
                });
                ///上传完成
                uploader.on('uploadComplete', function (file) {
                    $('#' + file.id).find('.progress').remove();
                });
                //预加载数据
                if (detailData) {
                    var imgSrc = "";
                    for (var i in photoList) {
                        var fileAttId = photoList[i].attId;
                        var fileAuditStatus = photoList[i].auditStatus;
                        var fileOpinion = photoList[i].opinion ? photoList[i].opinion : "";
                        var downloadType = photoList[i].downloadType;
                        var fileName = photoList[i].fileName;
                        var storageName = photoList[i].storageName;
                        var extension = "";
                        if (storageName != undefined && storageName != "") {
                            var fileNameI = storageName.lastIndexOf(".");
                            if (fileNameI > -1) extension = storageName.substring(fileNameI + 1);
                        }
                        var $imgBoxDom = $('<div id="' + fileAttId + '" class="file-item thumbnail"></div>');

                        var $imgDom = null;
                        if (downloadType.indexOf("application") != -1) {//针对下载的文件类型
                            var imgSrc = "/images/" + extension + ".png";
                            $imgDom = $('<div id="' + fileAttId + '" class="file-item thumbnail">' +
                                '<img src="' + imgSrc + '"' +
                                ' onclick="rebate_common.showLargerImg(' + fileAttId + ',' + fileAuditStatus + ',\'' + fileOpinion + '\');">' +
                                '<span class="desc">' + fileName + '</span>' +
                                '</div>');
                        } else {//针对图片
                            $imgDom = $('<div id="' + fileAttId + '" class="file-item thumbnail">' +
                                '<img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=' +
                                sourceType + '&attId=' + fileAttId + '" ' +
                                'onclick="rebate_common.showLargerImg(' + fileAttId + ',' + fileAuditStatus + ',\'' + fileOpinion + '\');"' +
                                'onerror="rebate_common.imageError(this)">' +
                                '</div>');
                        }

                        var $operationDom = $('<div class="file-panel">' +
                            '<span class="cancel" onclick="save_widget.removeImg(' + fileAttId + ',\'' + fileType + '\')">删除</span>' +
                            '</div>');
                        //发票需要有发票金额
                        if (fileType == "invoice") {
                            var $amountInput = $('<span class="addAmount" title="添加发票金额" onclick="save_widget.addInvoiceAmount(' + fileAttId + ')">添加发票金额</span>');
                            $operationDom.append($amountInput);
                        }

                        //针对EXCEL需要下载文件
                        if (downloadType.indexOf("application") != -1) {
                            var $download = $('<a class="downloadFile" title="下载文件" href="/downloadAttachmentFile.do?attId=' + fileAttId + '"></a>');
                            $operationDom.append($download);
                        }

                        if (fileAuditStatus == "0") {//审核不通过
                            var $rejectSpan = $('<span class="auditReject" id="' + fileAttId + 'reject">拒绝</span>');
                            $rejectSpan.addClass("selected");
                            $operationDom.attr("style", "display:block");
                            $operationDom.append($rejectSpan);
                        }
                        $imgDom.append($operationDom);

                        //发票金额
                        if (fileType == "invoice") {
                            var $invoiceAmount = $('<div id="' + fileAttId + 'amount">' + photoList[i].invoiceAmount + '元</div>');
                            $imgDom.find("div.file-panel").animate({bottom: "+=18px"});
                            $imgDom.append($invoiceAmount);
                        }
                        //审核意见
                        if (fileAuditStatus == "0") {//审核不通过
                            var $rejectOpinion = $('<div id="' + fileAttId + 'opinion" style="color:red">点击查看审核意见</div>');
                            $operationDom.animate({bottom: "+=18px"});
                            if (fileOpinion != null && fileOpinion != "")
                                $imgDom.append($rejectOpinion);
                            var onclickStr = "rebate_common.showLargerImg(" + fileAttId + ",0,'" + fileOpinion + "');"
                            $rejectOpinion.attr("onclick", onclickStr);
                        }
                        $("#" + listDom).prepend($imgDom);
                    }
                }
            },

            initGrid: function (Grid, renderId, dataStore, showIviColumn) {
                var dataGrid;
                var columns = [{
                    title: '渠道',
                    sortable: false,
                    dataIndex: 'channel',
                    width: '16%',
                    renderer: function (value, obj, index) {
                        return util.getChannelText(value);
                    }
                }, {
                    title: '业务发展基金',
                    sortable: false,
                    dataIndex: 'bdFund',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return "￥" + $.formatMoney(value, 2);
                        } else {
                            return "￥0";
                        }
                    }
                }, {
                    title: '市场营销基金',
                    sortable: false,
                    dataIndex: 'mktFund',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return "￥" + $.formatMoney(value, 2);
                        } else {
                            return "￥0";
                        }
                    }
                }

                ];
                if (showIviColumn) {
                    columns.push({
                        title: '奖励基金（IVI）',
                        sortable: false,
                        dataIndex: 'iviFund',
                        width: '28%',
                        renderer: function (value, obj, index) {
                            if (value != undefined && "" != value && !isNaN(value)) {
                                return "￥" + $.formatMoney(value, 2);
                            } else {
                                return "￥0";
                            }
                        }
                    });
                }
                dataGrid = new Grid.Grid({
                    render: renderId,
                    columns: columns,
                    store: dataStore,
                    bbar: false,
                    width: '100%'
                });
                return dataGrid;
            },
            //剩余
            initFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getFundGridData(data);
                    if (fundGrid) {
                        fundStore.setResult(gridData);
                        return;
                    }

                    fundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    fundGrid = loader.initGrid(Grid, "#fundGrid", fundStore, true);
                    fundGrid.render();
                });
            },
            //累计
            initTotalFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getTotalFundGridData(data);
                    if (totalFundGrid) {
                        totalFundStore.setResult(gridData);
                        return;
                    }

                    totalFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    totalFundGrid = loader.initGrid(Grid, "#totalfundGrid", totalFundStore, true);
                    totalFundGrid.render();
                });
            },
            //已申报发票总额（含税）
            initUsedFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getUsedFundGridData(data);
                    if (usedFundGrid) {
                        usedFundStore.setResult(gridData);
                        return;
                    }

                    usedFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    usedFundGrid = loader.initGrid(Grid, "#usedfundGrid", usedFundStore);
                    usedFundGrid.render();
                });
            },
            initApplyFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getApplyFundGridData(data);
                    if (applyFundGrid) {
                        applyFundStore.setResult(gridData);
                        return;
                    }

                    applyFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    applyFundGrid = loader.initGrid(Grid, '#applyfundGrid', applyFundStore);
                    applyFundGrid.render();
                });
            },
            //已兑现基金总额（含税）
            initActualFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getActualFundGridData(data);
                    if (actualFundGrid) {
                        actualFundStore.setResult(gridData);
                        return;
                    }

                    actualFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    actualFundGrid = loader.initGrid(Grid, '#actualfundGrid', actualFundStore, true);
                    actualFundGrid.render();
                });
            },
            //没有使用
            initRemainInvoiceFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getRemainInvoiceFundGridData(data);
                    if (remainInvoiceFundGrid) {
                        remainInvoiceFundStore.setResult(gridData);
                        return;
                    }

                    remainInvoiceFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    remainInvoiceFundGrid = loader.initGrid(Grid, '#remainInvoicefundGrid', remainInvoiceFundStore);
                    remainInvoiceFundGrid.render();
                });
            },
            showAddInvoiceAmountDialog: function (imgId) {
                $("#imgIdForAmount").val(imgId);
                $("#invoiceAmount").val(util.getImgAmountByImgId(imgId));
                if (imgInvoiceAddDialog == null) {
                    BUI.use("bui/overlay", function (Overlay) {
                        imgInvoiceAddDialog = new Overlay.Dialog({
                            title: '发票金额',
                            width: 400,
                            height: 200,
                            mask: true,  //设置是否模态
                            contentId: "invoiceAmountDialog",
                            buttons: [{
                                text: '确认',
                                elCls: 'btn-create',
                                handler: function () {
                                    var imgIdCurrent = $("#imgIdForAmount").val();
                                    var invoiceAmount = $("#invoiceAmount").val();
                                    var result = util.addAmountToImg(imgIdCurrent, invoiceAmount);
                                    if (result) {
                                        var $imgDom = $("#" + imgIdCurrent);
                                        //添加说明文字
                                        if ($("#" + imgIdCurrent + "amount").length <= 0) {
                                            var $invoiceAmount = $('<div id="' + imgIdCurrent + 'amount">' + invoiceAmount + '元</div>');
                                            $imgDom.find("div.file-panel").css("bottom", "18px");
                                            $imgDom.append($invoiceAmount);
                                        } else {
                                            $("#" + imgIdCurrent + "amount").html(invoiceAmount + "元");
                                        }

                                        //计算发票总额
                                        $("#invoiceAmountTotal").val(util.getInvoiceAmountTotal());
                                        var ret = util.checkApplyFundInput();
                                        //if (ret.code != 0) common.alertMes(ret.msg, 'error');

                                        //如果是审核不通过，则更改为审核通过
                                        $("#" + imgIdCurrent + "reject").remove();
                                        util.restImgAuditStatus(imgIdCurrent);
                                        this.close();
                                    } else {
                                        return;
                                    }
                                }
                            }, {
                                text: '取消',
                                elCls: 'btn-cancel',
                                handler: function () {
                                    this.close();
                                }
                            }]
                        });
                    });
                    imgInvoiceAddDialog.show();
                } else {
                    $("#invoiceAmount").val(util.getImgAmountByImgId(imgId));
                    imgInvoiceAddDialog.show();
                }
            }
        },
        action = {
            removeValidRule: function (activityType) {
                if (activityType == 2) {
                    form.getField("activityTheme").addRule("required", true);//2
                    form.getField("customerNumber").addRule("required");
                    form.getField("customerNumber").addRule("number", true);
                    form.getField("customerNumber").addRule("min", 1);
                    form.getField("startDateActivity").addRule("required");
                    form.getField("endDateActivity").addRule("required");
                    form.getField("billboardType").removeRule("required");//3
                    form.getField("billboardContent").removeRule("required");
                    form.getField("startDateBillboard").removeRule("required");
                    form.getField("endDateBillboard").removeRule("required");
                    form.getField("deviceDetail").removeRule("required");//4
                    form.getField("promoteType").removeRule("required");//5
                    form.getField("startDatePromote").removeRule("required");
                    form.getField("endDatePromote").removeRule("required");
                    form.getField("promoteNumber").removeRule("required");
                    form.getField("promoteNumber").removeRule("number");
                    form.getField("promoteNumber").removeRule("regexp");
                    form.getField("promoteProduct").removeRule("required");
                } else if (activityType == 3) {
                    form.getField("activityTheme").removeRule("required");//2
                    form.getField("customerNumber").removeRule("required");
                    form.getField("customerNumber").removeRule("number");
                    form.getField("customerNumber").removeRule("min");
                    form.getField("startDateActivity").removeRule("required");
                    form.getField("endDateActivity").removeRule("required");
                    form.getField("billboardType").addRule("required", true);//3
                    form.getField("billboardContent").addRule("required", true);
                    form.getField("startDateBillboard").addRule("required", true);
                    form.getField("endDateBillboard").addRule("required", true);
                    form.getField("deviceDetail").removeRule("required");//4
                    form.getField("promoteType").removeRule("required");//5
                    form.getField("startDatePromote").removeRule("required");
                    form.getField("endDatePromote").removeRule("required");
                    form.getField("promoteNumber").removeRule("required");
                    form.getField("promoteNumber").removeRule("number");
                    form.getField("promoteNumber").removeRule("regexp");
                    form.getField("promoteProduct").removeRule("required");
                } else if (activityType == 4) {
                    form.getField("activityTheme").removeRule("required");//2
                    form.getField("customerNumber").removeRule("required");
                    form.getField("customerNumber").removeRule("number");
                    form.getField("customerNumber").removeRule("min");
                    form.getField("startDateActivity").removeRule("required");
                    form.getField("endDateActivity").removeRule("required");
                    form.getField("billboardType").removeRule("required");//3
                    form.getField("billboardContent").removeRule("required");
                    form.getField("startDateBillboard").removeRule("required");
                    form.getField("endDateBillboard").removeRule("required");
                    form.getField("deviceDetail").addRule("required", true);//4
                    form.getField("promoteType").removeRule("required");//5
                    form.getField("startDatePromote").removeRule("required");
                    form.getField("endDatePromote").removeRule("required");
                    form.getField("promoteNumber").removeRule("required");
                    form.getField("promoteNumber").removeRule("number");
                    form.getField("promoteNumber").removeRule("regexp");
                    form.getField("promoteProduct").removeRule("required");
                } else if (activityType == 5) {
                    form.getField("activityTheme").removeRule("required");//2
                    form.getField("customerNumber").removeRule("required");
                    form.getField("customerNumber").removeRule("number");
                    form.getField("customerNumber").removeRule("min");
                    form.getField("startDateActivity").removeRule("required");
                    form.getField("endDateActivity").removeRule("required");
                    form.getField("billboardType").removeRule("required");//3
                    form.getField("billboardContent").removeRule("required");
                    form.getField("startDateBillboard").removeRule("required");
                    form.getField("endDateBillboard").removeRule("required");
                    form.getField("deviceDetail").removeRule("required");//4
                    form.getField("promoteType").addRule("required", true);//5
                    form.getField("startDatePromote").addRule("required", true);
                    form.getField("endDatePromote").addRule("required", true);
                    form.getField("promoteNumber").addRule("required", true);
                    form.getField("promoteNumber").addRule("number", true);
                    form.getField("promoteNumber").addRule("regexp", [/(^[1-9]([0-9]+)?$)/, '请输入整数格式数字(例：123)']);
                    form.getField("promoteProduct").addRule("required", true);
                } else if (activityType == -1) {
                    form.getField("activityTheme").removeRule("required");//2
                    form.getField("customerNumber").removeRule("required");
                    form.getField("customerNumber").removeRule("number");
                    form.getField("customerNumber").removeRule("min");
                    form.getField("startDateActivity").removeRule("required");
                    form.getField("endDateActivity").removeRule("required");
                    form.getField("billboardType").removeRule("required");//3
                    form.getField("billboardContent").removeRule("required");
                    form.getField("startDateBillboard").removeRule("required");
                    form.getField("endDateBillboard").removeRule("required");
                    form.getField("deviceDetail").removeRule("required");//4
                    form.getField("promoteType").removeRule("required");//5
                    form.getField("startDatePromote").removeRule("required");
                    form.getField("endDatePromote").removeRule("required");
                    form.getField("promoteNumber").removeRule("required");
                    form.getField("promoteNumber").removeRule("number");
                    form.getField("promoteNumber").removeRule("regexp");
                    form.getField("promoteProduct").removeRule("required");
                } else {
                    form.getField("activityTheme").addRule("required", true);//2
                    form.getField("customerNumber").addRule("required", true);
                    form.getField("customerNumber").addRule("number", true);
                    form.getField("customerNumber").addRule("min", 1);
                    form.getField("startDateActivity").addRule("required", true);
                    form.getField("endDateActivity").addRule("required", true);
                    form.getField("billboardType").addRule("required", true);//3
                    form.getField("billboardContent").addRule("required", true);
                    form.getField("startDateBillboard").addRule("required", true);
                    form.getField("endDateBillboard").addRule("required", true);
                    form.getField("deviceDetail").addRule("required", true);//4
                    form.getField("promoteType").addRule("required", true);//5
                    form.getField("startDatePromote").addRule("required", true);
                    form.getField("endDatePromote").addRule("required", true);
                    form.getField("promoteNumber").addRule("required", true);
                    form.getField("promoteNumber").addRule("number", true);
                    form.getField("promoteNumber").addRule("regexp", [/(^[1-9]([0-9]+)?$)/, '请输入整数格式数字(例：123)']);
                    form.getField("promoteProduct").addRule("required", true);
                }
            },
            disableActivityControls: function () {
                form.getField("activityTheme").disable();//2
                form.getField("customerNumber").disable();
                form.getField("customerNumber").disable();
                form.getField("customerNumber").disable();
                form.getField("startDateActivity").disable();
                form.getField("endDateActivity").disable();
                form.getField("billboardType").disable();//3
                form.getField("billboardContent").disable();
                form.getField("startDateBillboard").disable();
                form.getField("endDateBillboard").disable();
                form.getField("deviceDetail").disable();//4
                form.getField("promoteType").disable();//5
                form.getField("startDatePromote").disable();
                form.getField("endDatePromote").disable();
                form.getField("promoteNumber").disable();
                form.getField("promoteProduct").disable();
                form.getField("hasRentInvoice").disable();
            },
            beforeSaveRequest: function (type) {
                var saveRequest = $.extend({}, form.getRecord(), {});
                var invoiceAmountTotal = $("#invoiceAmountTotal").val();
                if (detailData) {
                    saveRequest["id"] = rebateId;
                }
                if (type == 2) {//提交数据，不需要校验结果

                }
                if (type == 3) {//提交数据，需要校验结果
                    if (!rebateId || (detailData && detailData.currentStep < 2)) {
                        form.getField('applyFund').removeRule("required");
                        form.getField('applyFund').removeRule("min");
                    }

                    //form.getField('customerInteractionDesc').addRule('required', true);
                    if ((detailData && detailData.currentStep < 2) || !rebateId) { //FLSR update
                        if ((saveRequest.activityType == 1 || saveRequest.activityType >= 6)) {
                            form.getField('customerInteractionDesc').removeRule("required");
                            action.removeValidRule(0);
                            action.removeValidRule(-1);
                        } else if (saveRequest.activityType == 2) {
                            form.getField('customerInteractionDesc').removeRule("required");
                            action.removeValidRule(0);
                            action.removeValidRule(2);
                        } else {
                            form.getField('activityDesc').removeRule("required");
                            form.getField('customerInteractionDesc').removeRule("required");
                            action.removeValidRule(0);
                            action.removeValidRule(saveRequest.activityType);
                        }
                        if (saveRequest.activityType != 9 && saveRequest.activityType != 10) {
                            form.getField('hasRentInvoice').removeRule("required");
                        }
                        if (saveRequest.activityType == 9 || saveRequest.activityType == 10) {
                            if ((detailData && detailData.currentStep < 2) || !rebateId) {
                                form.getField('hasRentInvoice').removeRule("required");
                            }
                        }
                        if ((detailData && detailData.currentStep < 2) && (saveRequest.activityType == 9 || saveRequest.activityType == 10)) {
                            form.getField('hasRentInvoice').removeRule("required");
                        }
                    }

                    if ((detailData && detailData.currentStep >= 2)) { //PARTNER update/upload
                        action.removeValidRule(0);
                        action.removeValidRule(saveRequest.activityType);
                    }

                    if (actionType == "upload") {
                        //action.disableActivityControls();
                        action.removeValidRule(-1);
                        if (saveRequest.activityType != 2) {
                            form.getField('activityDesc').removeRule("required");
                            form.getField('customerInteractionDesc').removeRule("required");
                        }
                    }

                    form.valid();
                    if (!form.isValid())
                        return false;

                    if (rebateId && detailData.currentStep >= 2) {
                        if (!saveRequest.applyFund)
                            saveRequest.applyFund = 0;

                        //验证文件上传
                        if (actionType == "upload") {
                            for (var i in requireFileList) {
                                if (requireFileList[i].length == 0) {
                                    common.alertMes("缺少上传文件，请检查！", 'error');
                                    return false;
                                }
                            }
                            //验证文件审核状态
                            var result = util.validFileAudit();
                            if (result == false) {
                                common.alertMes("文件未通过审核，请检查！", 'error');
                                return false;
                            }
                        }
                        result = util.validFileInvoiceAmount();
                        if (result == false) {
                            common.alertMes("发票未填写金额，请检查！", 'error');
                            return false;
                        }

                        //验证发票金额
                        if (invoiceAmountTotal != util.getInvoiceAmountTotal()) {
                            common.alertMes("本次发票累加金额验证不通过，请检查！", 'error');
                            return false;
                        }
                        var ret = util.checkApplyFundInput();
                        if (ret.code != 0) {
                            common.alertMes(ret.msg, 'error');
                            return false;
                        }
                    }
                }


                saveRequest["invoiceAmountTotal"] = invoiceAmountTotal;
                saveRequest["billboardFileList"] = billboardPhoto;
                saveRequest["managerMailFileList"] = managerMailFile;
                saveRequest["scenePhotoFileList"] = scenePhotoFile;
                saveRequest["invoiceFileList"] = invoiceFile;
                saveRequest["marketingMailFileList"] = marketingMailFile;
                //saveRequest["customerInteractionFileList"] = customerInteractionFile;
                saveRequest["signInFileList"] = signInFile;
                saveRequest["otherFileList"] = otherFile;
                saveRequest["marketPlanFileList"] = marketPlanFile;
                saveRequest["promotionReceiptFileList"] = promotionReceiptFile;
                saveRequest["splitMailFileList"] = splitMailFile;
                saveRequest["activityTheme"] = $("#activityTheme").val();
                saveRequest["customerNumber"] = $("#customerNumber").val();
                saveRequest["billboardContent"] = $("#billboardContent").val();
                saveRequest["customerInteractionDesc"] = $("#customerInteractionDesc").val();
                saveRequest["activityDesc"] = $("#activityDesc").val();
                saveRequest["deviceDetail"] = $("#deviceDetail").val();
                saveRequest["promoteProduct"] = $("#promoteProduct").val();
                if ($("#useInvoiceAmount").val() != null)
                    saveRequest["useInvoiceAmount"] = $("#useInvoiceAmount").val();
                if ($("#useIviFund").val() != null)
                    saveRequest["useIviFund"] = $("#useIviFund").val();
                if ($("#orgName").val() != null)
                    saveRequest["organizationName"] = $("#orgName").val();
                if ($("#fundOwnership").val() != null)
                    saveRequest["fundOwnership"] = $("#fundOwnership").val();

                if ($("#startDateActivity").val() != null && $("#startDateActivity").val() != "")
                    saveRequest["startDateActivity"] = $("#startDateActivity").val();
                if ($("#endDateActivity").val() != null && $("#endDateActivity").val() != "")
                    saveRequest["endDateActivity"] = $("#endDateActivity").val();
                if ($("#startDateBillboard").val() != null && $("#startDateBillboard").val() != "")
                    saveRequest["startDateBillboard"] = $("#startDateBillboard").val();
                if ($("#endDateBillboard").val() != null && $("#endDateBillboard").val() != "")
                    saveRequest["endDateBillboard"] = $("#endDateBillboard").val();
                if ($("#startDatePromote").val() != null && $("#startDatePromote").val() != "")
                    saveRequest["startDatePromote"] = $("#startDatePromote").val();
                if ($("#endDatePromote").val() != null && $("#endDatePromote").val() != "")
                    saveRequest["endDatePromote"] = $("#endDatePromote").val();
                delete saveRequest["undefined"];
                delete saveRequest["file"];
                return saveRequest;
            },
            save: function () {
                var saveRequest = action.beforeSaveRequest(1);
                if (false == saveRequest) {
                    return false;
                }

                req.saveRebate(saveRequest, function (data) {
                    if (data.code == '0000') {
                        common.alertMes("处理成功", 'success');
                        window.history.go(-1);
                    } else {
                        common.alertMes(data.message, 'error');
                    }
                });
            },
            submit: function () {
                var saveRequest = action.beforeSaveRequest(3);
                if (false == saveRequest) {
                    return false;
                }

                var confMsg = '请确认是否提交数据？';
                common.confirmMes(confMsg, function () {
                    req.submitRebate(saveRequest, function (data) {
                        if (data.code == '0000') {
                            common.alertMes("处理成功", 'success');
                            window.history.go(-1);
                        } else {
                            common.alertMes(data.message, 'error');
                        }
                    });
                });
            },
            removeImg: function (attId, fileType) {
                $("#" + attId).remove();
                var photoList = [];
                if (fileType == "billboardPhoto") {
                    photoList = eval(fileType);
                } else {
                    photoList = eval(fileType + "File");
                }

                var removeIndex = -1;
                for (var i in photoList) {
                    if (photoList[i].attId == attId) {
                        removeIndex = i;
                        break;
                    }
                }
                photoList.splice(removeIndex, 1);

                if (fileType == "invoice") {
                    $("#invoiceAmountTotal").val(util.getInvoiceAmountTotal());
                    var ret = util.checkApplyFundInput();
                }

            },
            addInvoiceAmount: function (imgId) {
                loader.showAddInvoiceAmountDialog(imgId);
            }
        };

    return {
        init: function () {
            loader.initRebateSave();
        },
        returnListPage: function () {
            return window.history.go(-1);
        },
        save: function () {
            return action.save();
        },
        submit: function () {
            return action.submit();
        },
        removeImg: function (attId, fileType) {
            return action.removeImg(attId, fileType);
        },
        addInvoiceAmount: function (imgId) {
            return action.addInvoiceAmount(imgId);
        }
    }
}($));