;'use strict';

$(function () {
    detail_widget.init();
});

var detail_widget = (function ($) {

    var rebateHistoryGrid = null,
        rebateFileGrid = null,

        auditDialog = null,
        auditStatus = null,

        imgAuditDialog = null,

        fileAuditList = [],
        supplementFile = [],

        roleCode = null,
        detailData = {},
        applyInChannel = null,

        fundGrid = null,
        fundStore = null,

        totalFundGrid = null,
        totalFundStore = null,

        usedFundGrid = null,
        usedFundStore = null,

        applyFundGrid = null,
        applyFundStore = null,

        actualFundGrid = null,
        actualFundStore = null,

        req = {},
        util = {},
        loader = {},
        action = {};

    var foo = new $.JsonRpcClient({
        ajaxUrl: '/wxPublicRpc.do'
    });

    req = {
        getDetail: function (id, callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.detail", [id], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        },
        audit: function (rebateId, auditResult, opinion, fileList, callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.audit", [rebateId, auditResult, opinion, fileList], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        },
        reject: function (rebateId, opinion, fileList, callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.reject", [rebateId, opinion, fileList], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        },
        supplement: function (rebateId, fileList, callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.supplement", [rebateId, fileList], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        },
        getUserRole: function (callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.getRebateRoleCode", [rebateId], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    roleCode = data.data;
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        },
        getRebateFlow: function (rebateId, callback) {
            LoadMask.show();
            foo.call("rebateApply2Service.getRebateFlow", [rebateId, "rebate"], function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                    return;
                }
            });
        }
    },
        util = {
            pushFileList: function (fileList) {
                for (var i in fileList) {
                    fileAuditList.push(fileList[i]);
                }
                return;
            },
            beforeSubmitFile: function () {
                for (var i in fileAuditList) {
                    if (fileAuditList[i].auditStatus == null) {
                        fileAuditList[i].auditStatus = "1";
                    }
                }
                return;
            },
            getChannelText: function (channel) {
                if ("CDM" == channel) {
                    return "金富力";
                } else if ("C&I" == channel) {
                    return "德乐";
                }
            },
            addOpinionToImg: function (imgId, opinion) {
                for (var i in fileAuditList) {
                    if (fileAuditList[i].attId == imgId) {
                        if (null == opinion || "" == opinion) {
                            common.alertMes("请填写图片审核意见", 'error');
                            return false;
                        }
                        fileAuditList[i].opinion = opinion;
                        break;
                    }
                }
                return true;
            },
            getImgOpinionByImgId: function (imgId) {
                for (var i in fileAuditList) {
                    if (fileAuditList[i].attId == imgId) {
                        return fileAuditList[i].opinion;
                    }
                }
                return "";
            },
            removeAuditInfo: function (imgId) {
                for (var i in fileAuditList) {
                    if (fileAuditList[i].attId == imgId) {
                        fileAuditList[i].opinion = null;
                    }
                }

                var $imgDom = $("#" + imgId);
                //更改图片onclick事件
                var onclickStr = "rebate_common.showLargerImg(" + imgId + ",1,'null');"
                $imgDom.find("img").attr("onclick", onclickStr);
                //添加说明文字
                if ($("#" + imgId + "opinion").length > 0) {
                    $("#" + imgId + "opinion").remove();
                    $imgDom.find("div.file-panel").animate({bottom: "-=18px"});
                }
            },
            initTooltipTop: function (target, alignType) {
                BUI.use('bui/tooltip', function (Tooltip) {
                    var _tips = new Tooltip.Tips({
                        tip: {
                            trigger: target,
                            alignType: alignType,
                            elCls: 'fundTooltip',
                            offset: 10,
                            width: 750,
                            titleTpl: '<p>{title}</p>'
                        }
                    });
                    _tips.render();
                });
            },
            getFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.bdFundCDM;
                        mktFund = data.mktFundCDM;
                        iviFund = data.iviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.bdFundCI;
                        mktFund = data.mktFundCI;
                        iviFund = data.iviFundCI;
                    }
                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            },
            getTotalFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.totalBdFundCDM;
                        mktFund = data.totalMktFundCDM;
                        iviFund = data.totalIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.totalBdFundCI;
                        mktFund = data.totalMktFundCI;
                        iviFund = data.totalIviFundCI;
                    }

                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            },
            getUsedFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.usedBdFundCDM;
                        mktFund = data.usedMktFundCDM;
                        iviFund = data.usedIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.usedBdFundCI;
                        mktFund = data.usedMktFundCI;
                        iviFund = data.userIviFundCI;
                    }
                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            },
            getApplyFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.applyBdFundCDM;
                        mktFund = data.applyMktFundCDM;
                        iviFund = data.applyIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.applyBdFundCIO;
                        mktFund = data.applyMktFundCIO;
                        iviFund = data.applyIviFundCIO;
                    }

                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            },
            getActualFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.actualBdFundCDM;
                        mktFund = data.actualMktFundCDM;
                        iviFund = data.actualIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.actualBdFundCIO;
                        mktFund = data.actualMktFundCIO;
                        iviFund = data.actualIviFundCIO;
                    }

                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            },
            getRemainInvoiceFundGridData: function (data) {
                var gridData = [];
                for (var i = 0; i <= 1; i++) {
                    var channel = "";
                    var bdFund;
                    var mktFund;
                    var iviFund;
                    if (i == 0) {
                        channel = "CDM";
                        bdFund = data.remainInvoiceBdFundCDM;
                        mktFund = data.remainInvoiceMktFundCDM;
                        iviFund = data.remainInvoiceIviFundCDM;
                    } else {
                        channel = "C&I";
                        bdFund = data.remainInvoiceBdFundCIO;
                        mktFund = data.remainInvoiceMktFundCIO;
                        iviFund = data.remainInvoiceIviFundCIO;
                    }
                        gridData.push({
                            channel: channel,
                            bdFund: util.transferMoneyDeciaml(bdFund),
                            mktFund: util.transferMoneyDeciaml(mktFund),
                            iviFund: util.transferMoneyDeciaml(iviFund)
                        });
                }
                return gridData;
            }
            , transferMoneyDeciaml: function (fund) {
                return parseFloat(fund).toFixed(2) * 1;
            }
        },
        loader = {
            initDetail: function () {
                util.initTooltipTop('#noSplitModeTooltip', 'bottom-right');
                util.initTooltipTop('#splitModeTooltip', 'bottom-right');
                $(".activity-detail").hide();

                req.getDetail(rebateId, function (data) {
                    detailData = data;
                    if (data.organizationName != null) {
                        $("#orgName").text(data.organizationName);
                    }
                    applyInChannel=data.fundOwnership;
                    loader.initOrgFundGrid(data.orgInfo);

                    if (data.activityTypeName != null) {
                        $("#activityType").text(data.activityTypeName);
                    }
                    $("#activityDesc").text(data.activityDesc);
                    $("#activityDescDiv").show();
                    $("#descirption").text(data.descirption);
                    $("#useIviFund").text($.formatMoney(data.useIviFund, 2));
                    $("#useInvoiceAmount").text($.formatMoney(data.useInvoiceAmount, 2));
                    if (data.currentStep > 2) {
                        $("#invoiceAmountTotalDiv").show();
                        $("#useIviFundDiv").show();
                        //$("#useInvoiceAmountDiv").show();
                    } else {
                        $("#invoiceAmountTotalDiv").hide();
                    }


                    $("#detail-div-" + data.activityType).show();
                    var dateText = "{startDate} 至 {endDate}";
                    var startDate = common.formatDate(new Date(data.startDate), 'yyyy-MM-dd');
                    var endDate = common.formatDate(new Date(data.endDate), 'yyyy-MM-dd')

                    data.activityType = parseInt(data.activityType);
                    if (data.activityType != 2)
                        $("#customerInteractionDescDiv").hide();

                    if (data.activityType == 2) {
                        $("#activityDescLabel").text("预计顾客互动效果说明:");
                        if (data.customerInteractionDesc) {
                            $("#customerInteractionDesc").text(data.customerInteractionDesc);
                        }
                        $("#dateActivity").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                        $("#activityTheme").text(data.activityTheme);
                        $("#customerNumber").text(data.customerNumber);
                        if (data.currentStep <= 2) {
                            $("#activityDescDiv").show();
                            $("#customerInteractionDescDiv").hide();
                        } else {
                            $("#activityDescDiv").hide();
                            $("#customerInteractionDescDiv").show();
                        }
                    } else if (data.activityType == 3) {//广告牌
                        $("#scenePhotoUploader").find("label.field-label").html('实物照片：');
                        $("#dateBillboard").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                        $("#billboardContent").text(data.billboardContent);
                        $("#activityDescDiv").hide();
                    } else if (data.activityType == 4) {
                        $("#deviceDetail").text(data.deviceDetail);
                        $("#activityDescDiv").hide();
                    } else if (data.activityType == 5) {//当地促销
                        $("#scenePhotoUploader").find("label.field-label").html('活动图片：');
                        $("#datePromote").text(dateText.replace(/{startDate}/, startDate).replace(/{endDate}/, endDate));
                        $("#promoteProduct").html(data.promoteProduct);
                        $("#promoteType").text(data.promoteTypeName);
                        $("#promoteNumber").text(data.promoteNumber);
                        $("#activityDescDiv").hide();
                    } else if (data.activityType == 6 || data.activityType == 7 || data.activityType == 8 || data.activityType == 9 || data.activityType == 10) {
                        $("#scenePhotoUploader").find("label.field-label").html('实物照片：');
                        if (data.activityType == 9 || data.activityType == 10) {
                            if (data.hasRentInvoice == 1) {
                                $("#invoiceUploaderLabel").html("发票复印件");
                            } else {
                                $("#invoiceTip").show();
                                $("#invoiceUploaderLabel").html("房屋租赁合同复印件");
                            }
                        }
                    } else if (data.activityType == 11 || data.activityType == 12) {
                        $("#scenePhotoUploader").find("label.field-label").html('会议照片：');
                    } else {
                        $("#scenePhotoUploader").find("label.field-label").html('实物/会议现场照：');
                    }

                    if (data.currentStep == 1 && data.activityType == 2) {

                    }


                    if (data.fundTypeName != null) {
                        $("#fundType").text(data.fundTypeName);
                    }

                    $("#fundOwnershipDiv").show();
                    $("#fundOwnership").text(util.getChannelText(data.fundOwnership));


                    //$("#applyFundCDM").text(data.applyFundCdmStr);
                    //$("#applyFundCIO").text(data.applyFundCioStr);
                    $("#applyFund").text($.formatMoney(data.applyFundStr, 2));

                    if (data.invoiceAmountTotalStr != null) {
                        $("#invoiceAmountTotal").text($.formatMoney(data.invoiceAmountTotalStr, 2));
                    }

                    $("#activityDesc").html(data.activityDesc);
                    if (data.billboardTypeName) {
                        $("#billboardTypeDiv").show();

                        if (data.billboardType == 0) {
                            $("#billboardType").text(data.billboardTypeName + "（设计及安装全包）");
                        } else {
                            $("#billboardType").text(data.billboardTypeName + "（市场部已经负担门头制作费用）");
                        }
                    } else {
                        $("#billboardTypeDiv").hide();
                    }

                    /*if (data.managerMailFileList.length > 0) {
                        $("#managerMailUploader").show();
                        util.pushFileList(data.managerMailFileList);
                        loader.initPictureDiv("managerMailList", data.managerMailFileList, "21");
                    }*/

                    if (data.scenePhotoFileList.length > 0) {
                        $("#scenePhotoUploader").show();
                        util.pushFileList(data.scenePhotoFileList);
                        loader.initPictureDiv("scenePhotoList", data.scenePhotoFileList, "21");
                    }

                    if (data.invoiceFileList.length > 0) {
                        $("#invoiceUploader").show();
                        util.pushFileList(data.invoiceFileList);
                        loader.initPictureDiv("invoiceList", data.invoiceFileList, "21");
                    }

                    if (data.marketingMailFileList.length > 0) {
                        $("#marketingMailUploader").show();
                        util.pushFileList(data.marketingMailFileList);
                        loader.initPictureDiv("marketingMailList", data.marketingMailFileList, "21");
                    }

                    if (data.customerInteractionFileList.length > 0) {
                        $("#customerInteractionUploader").show();
                        util.pushFileList(data.customerInteractionFileList);
                        loader.initPictureDiv("customerInteractionList", data.customerInteractionFileList, "21");
                    }

                    if (data.signInFileList.length > 0) {
                        $("#signInUploader").show();
                        util.pushFileList(data.signInFileList);
                        loader.initPictureDiv("signInList", data.signInFileList, "21");
                    }

                    if (data.otherFileList.length > 0) {
                        $("#otherUploader").show();
                        util.pushFileList(data.otherFileList);
                        loader.initPictureDiv("otherList", data.otherFileList, "21");
                    }

                    if (data.marketPlanFileList.length > 0) {
                        $("#marketPlanUploader").show();
                        util.pushFileList(data.marketPlanFileList);
                        loader.initPictureDiv("marketPlanList", data.marketPlanFileList, "21");
                    }

                    if (data.promotionReceiptFileList.length > 0) {
                        $("#promotionReceiptUploader").show();
                        util.pushFileList(data.promotionReceiptFileList);
                        loader.initPictureDiv("promotionReceiptList", data.promotionReceiptFileList, "21");
                    }

                    $("#applyFundPlan").text($.formatMoney(data.applyFundPlanStr, 2));
                    $("#applyFund").text($.formatMoney(data.applyFundStr, 2));
                    if (data.status > 3) {
                        $("#applyFundDiv").show();
                    }

                    loader.renderRebateHistoryGrid(data.historyList);
                    loader.renderRebateFlow(rebateId);
                    loader.initTabs();
                    //补充材料
                    if (type == "supplement") {
                        $("#supplementUploader").show();
                        loader.initFileUpload("supplement", "supplementPicker", "supplementList", "21", supplementFile);
                    } else {
                        if (data.supplementFileList.length > 0) {
                            $("#supplementUploader").show();
                            $("#supplementPicker").remove();
                            util.pushFileList(data.supplementFileList);
                            loader.initPictureDiv("supplementList", data.supplementFileList, "21");
                        }
                    }

                });

            },
            initPictureDiv: function (fileListDom, fileList, sourceType) {
                for (var key in fileList) {
                    var fileAttId = fileList[key].attId;
                    var fileAuditStatus = fileList[key].auditStatus;
                    var fileOpinion = fileList[key].opinion;
                    var downloadType = fileList[key].downloadType;
                    var storageName = fileList[key].storageName;
                    var fileName = fileList[key].fileName;
                    var extension = "";
                    if (storageName != undefined && storageName != "") {
                        var i = storageName.lastIndexOf(".");
                        if (i > -1) extension = storageName.substring(i + 1);
                    }

                    var imgSrc = null;
                    if (downloadType && downloadType.indexOf("application") != -1) {
                        imgSrc = "/images/" + extension + ".png";
                    } else {
                        imgSrc = common.ctx + 'downloadAttachmentFile.do?sourceType=' + sourceType + '&attId=' + fileAttId;
                    }

                    var $imgDom = $('<div id="' + fileAttId + '" class="file-item thumbnail">' +
                        '<img src="' + imgSrc + '"' +
                        'onclick="rebate_common.showLargerImg(' + fileAttId + ',' + fileAuditStatus + ',\'' + fileOpinion + '\');"' +
                        'onerror="rebate_common.imageError(this)">' +
                        '<span class="desc">' + fileName + '</span>' +
                        '</div>');


                    var $auditDom = $('<div class="file-panel"></div>');
                    $auditDom.attr("style", "display:block");

                    //用于EXCEL
                    if (downloadType && downloadType.indexOf("application") != -1) {
                        var $download = $(
                            '<a style="margin: 2px 0px 0px 0px;" class="downloadFile" title="下载文件" href="/downloadAttachmentFile.do?attId=' + fileAttId + '"></a>'
                        );
                        $auditDom.append($download);
                    }

                    //如果是审核页面需要显示每张图片的审核状态
                    if (type == "audit") {
                        var $passSpann = $('<span class="auditPass" id="' + fileAttId + 'pass" onclick="detail_widget.auditImg(' + fileAttId + ', 1)">通过</span>');
                        var $rejectSpan = $('<span class="auditReject" id="' + fileAttId + 'reject" onclick="detail_widget.auditImg(' + fileAttId + ', 0)">拒绝</span>');
                        if (fileList[key].auditStatus == 0) {
                            $rejectSpan.addClass("selected");
                        } else {
                            $passSpann.addClass("selected");
                        }

                        if (fileList[key].auditStatus == 0) {
                            var $rejectOpinion = $('<div id="' + fileAttId + 'opinion" style="color:red">点击查看审核意见</div>');
                            $auditDom.css("bottom", "18px");
                            $imgDom.append($rejectOpinion);
                            var onclickStr = "rebate_common.showLargerImg(" + fileAttId + ",0,'" + fileOpinion + "');"
                            $rejectOpinion.attr("onclick", onclickStr);
                        }

                        $auditDom.append($passSpann);
                        $auditDom.append($rejectSpan);
                    }
                    $imgDom.append($auditDom);

                    if (fileListDom == "invoiceList") {
                        var $invoiceAmount = $('<div id="' + fileAttId + 'amount">' + fileList[key].invoiceAmount + '元</div>');
                        $imgDom.find("div.file-panel").css("bottom", "18px");
                        $imgDom.append($invoiceAmount);
                    }

                    $("#" + fileListDom).append($imgDom);
                }
            },
            initTabs: function () {
                BUI.use(['bui/tab', 'bui/mask'], function (Tab) {
                    var tabs = [];
                    if (detailData && detailData.status <= 2) {
                        tabs.push({title: '流程历史', value: '2'})
                    } else {
                        tabs.push({title: '相关附件', value: '1'});
                        tabs.push({title: '流程历史', value: '2'})
                    }
                    var tab = new Tab.TabPanel({
                        render: '#tabs',
                        elCls: 'nav-tabs',
                        panelContainer: '#tab_content',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
                        autoRender: true,
                        children: tabs
                    });
                    tab.setSelected(tab.getItemAt(0));
                    tab.on('selectedchange', function (ev) {
                        var item = ev.item, _value = item.get('value'), _text = item.get('text');
                        $(window).trigger('resize');
                    });
                });
            },
            renderRebateHistoryGrid: function (historyData) {
                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
                    var Grid = Grid,
                        Store = Data.Store,
                        columns = [{
                            title: '序号',
                            dataIndex: '',
                            sortable: false,
                            width: 65,
                            renderer: function (value, obj, index) {
                                return index + 1;
                            }
                        }, {
                            title: '步骤',
                            sortable: false,
                            dataIndex: 'stepName',
                            width: '20%'
                        }, {
                            title: '处理意见',
                            sortable: false,
                            dataIndex: 'opinion',
                            width: '50%'
                        }, {
                            title: '处理人',
                            dataIndex: 'operatorName',
                            sortable: false,
                            width: '15%'
                        }, {
                            title: '处理时间',
                            dataIndex: 'operationTimeStr',
                            sortable: false,
                            width: '15%',
                            renderer: function (value, obj, index) {
                                //return common.formatDate(new Date(value), 'yyyy-MM-dd hh:mm:ss');
                                return value;
                            }
                        }],
                        store = new Store({
                            data: historyData,
                            autoLoad: true
                        }),
                        rebateHistoryGrid = new Grid.Grid({
                            render: '#rebateHistoryGrid',
                            columns: columns,
                            loadMask: true, // 加载数据时显示屏蔽层
                            store: store,
                            bbar: false,
                            width: '100%'
                        });
                    rebateHistoryGrid.render();
                });
            },
            showAuidtDialog: function (type) {
                auditStatus = type;
                if (auditDialog == null) {
                    BUI.use("bui/overlay", function (Overlay) {
                        auditDialog = new Overlay.Dialog({
                            title: '审核意见',
                            width: 500,
                            height: 300,
                            mask: true,  //设置是否模态
                            contentId: "approveDialog",
                            buttons: [{
                                text: '确认',
                                elCls: 'btn-create',
                                handler: function () {
                                    var opinion = $("#opinion").val();
                                    util.beforeSubmitFile();
                                    if (auditStatus == "pass") {
                                        for (var i in fileAuditList) {
                                            if (fileAuditList[i].auditStatus == "0") {
                                                common.alertMes("相关附件未通过审核，请检查!", 'error');
                                                return;
                                            }
                                        }
                                        action.pass(opinion, fileAuditList);
                                    } else {
                                        action.reject(opinion, fileAuditList);
                                    }
                                    this.close();
                                }
                            }, {
                                text: '取消',
                                elCls: 'btn-cancel',
                                handler: function () {
                                    this.close();
                                }
                            }]
                        });
                    });
                    auditDialog.show();
                } else {
                    auditDialog.show();
                }
            },
            initFileUpload: function (fileType, pickerDom, listDom, sourceType, photoList) {
                var uploader = WebUploader.create({
                    auto: true,
                    server: common.ctx + 'uploadForAppAttchmentFile.do',
                    pick: '#' + pickerDom,
                    fileVal: 'myfiles',
                    formData: {
                        sourceType: sourceType,
                        sourceId: rebateId
                    }
                });
                //文件上传队列
                uploader.on('fileQueued', function (file) {
                    var $li = $(
                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                        '<img>' +
                        '</div>'
                    );
                    var $img = $li.find('img');
                    $("#" + listDom).prepend($li);

                    var thumbnailWidth = 100,
                        thumbnailHeight = 100;
                    uploader.makeThumb(file, function (error, src) {
                        if (error) {
                            $img.replaceWith('<span>不能预览</span>');
                            return;
                        }

                        $img.attr('src', src);
                    }, thumbnailWidth, thumbnailHeight);

                });
                //上传进度
                uploader.on('uploadProgress', function (file, percentage) {
                    var $li = $('#' + file.id),
                        $percent = $li.find('.progress span');

                    // 避免重复创建
                    if (!$percent.length) {
                        $percent = $('<p class="progress"><span></span></p>').appendTo($li).find('span');
                    }

                    $percent.css('width', percentage * 100 + '%');
                });
                //上传成功
                uploader.on('uploadSuccess', function (file, response) {
                    if (response.code == "success") {
                        $("#" + file.id).remove();

                        var fileAttId = response.attachmentFileList[0].attId;
                        var $li = $(
                            '<div id="' + fileAttId + '" class="file-item thumbnail">' +
                            '<img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=' +
                            sourceType + '&attId=' + fileAttId + '" ' +
                            'onclick="rebate_common.showLargerImg(' + fileAttId + ')"' +
                            'onerror="rebate_common.imageError(this)">' +
                            '<div class="file-panel"><span class="cancel" onclick="save_widget.removeImg(' + fileAttId + ',\'' + fileType + '\')">删除</span></div>' +
                            '</div>'
                        );
                        $("#" + listDom).prepend($li);
                        photoList.push({
                            attId: response.attachmentFileList[0].attId,
                            auditStatus: null,
                            opinion: null
                        });

                        $('#' + file.id).addClass('upload-state-done');

                    } else {
                        var $li = $('#' + file.id),
                            $error = $li.find('div.error');
                        $error = $('<div class="error"></div>').appendTo($li);
                        $error.text('上传失败');
                    }
                });
                //上传失败
                uploader.on('uploadError', function (file) {
                    var $li = $('#' + file.id),
                        $error = $li.find('div.error');
                    // 避免重复创建
                    if (!$error.length) {
                        $error = $('<div class="error"></div>').appendTo($li);
                    }
                    $error.text('上传失败');
                });
                ///上传完成
                uploader.on('uploadComplete', function (file) {
                    $('#' + file.id).find('.progress').remove();
                });
            },
            showImgAuditDialog: function (imgId) {
                $("#imgIdForOpinion").val(imgId);
                $("#imgOpinion").val(util.getImgOpinionByImgId(imgId));
                if (imgAuditDialog == null) {
                    BUI.use("bui/overlay", function (Overlay) {
                        imgAuditDialog = new Overlay.Dialog({
                            title: '图片审核意见',
                            width: 500,
                            height: 300,
                            mask: true,  //设置是否模态
                            contentId: "imgApproveDialog",
                            buttons: [{
                                text: '确认',
                                elCls: 'btn-create',
                                handler: function () {
                                    var imgIdCurrent = $("#imgIdForOpinion").val();
                                    var imgOpinion = $("#imgOpinion").val();
                                    var result = util.addOpinionToImg(imgIdCurrent, imgOpinion);
                                    var $imgDom;
                                    if (result) {
                                        $imgDom = $("#" + imgIdCurrent);
                                        //更改图片onclick事件
                                        var onclickStr = "rebate_common.showLargerImg(" + imgIdCurrent + ",0,'" + imgOpinion + "');"
                                        $imgDom.find("img").attr("onclick", onclickStr);
                                        //添加说明文字
                                        if ($("#" + imgIdCurrent + "opinion").length <= 0) {
                                            var $rejectOpinion = $('<div id="' + imgIdCurrent + 'opinion" style="color:red">点击查看审核意见</div>');
                                            $imgDom.find("div.file-panel").animate({bottom: "+=18px"});
                                            $imgDom.append($rejectOpinion);
                                            $rejectOpinion.attr("onclick", onclickStr);
                                        } else {
                                            $("#" + imgIdCurrent + 'opinion').attr("onclick", onclickStr);
                                        }
                                        this.close();
                                    } else {
                                        return;
                                    }
                                }
                            }, {
                                text: '取消',
                                elCls: 'btn-cancel',
                                handler: function () {
                                    this.close();
                                }
                            }]
                        });
                    });
                    imgAuditDialog.show();
                } else {
                    $("#imgOpinion").val(util.getImgOpinionByImgId(imgId));
                    imgAuditDialog.show();
                }
            },
            renderRebateFlow: function (rebateId) {
                req.getRebateFlow(rebateId, function (data) {
                    if (null == data || data.length == 0) {
                        $("#rebateFlowDiv").hide();
                        return;
                    }

                    var $flowDiv = $("#rebateFlowDiv");
                    var $steps = $('<ul class="ystep-container-steps"></ul>');
                    var preStep = 0;
                    for (var i = 0; i < data.length; i++) {
                        //步骤
                        var $nameSpan = $('<span class="step-name"><i class="fa fa-check fa-fw icon-white"></i>' + data[i].operatorName + '</span>');
                        var doneClass = "ystep-step-undone";
                        if (data[i].status == "1") {
                            $nameSpan.find("i").removeClass("icon-white");
                            doneClass = "ystep-step-done";
                        }
                        if (data[i].step != preStep) {
                            var $li = $('<li id="step' + data[i].step + '" class= ' + doneClass + ' style="width: 90px;margin-top: 25px;"></li>');
                            $li.append($nameSpan);
                            preStep = data[i].step;
                        } else {
                            var $currentLi = $steps.find("#step" + data[i].step);
                            $nameSpan.css("margin-top", "10px");
                            $currentLi.append($nameSpan);
                            preStep = data[i].step;
                            continue;
                        }
                        $steps.append($li);
                    }
                    $flowDiv.append($steps);
                    //横线
                    var $progressBar = $('<div class="ystep-progress">' +
                        '<p class="ystep-progress-bar"><span class="ystep-progress-highlight" style="width:0%"></span></p>' +
                        '</div>');
                    $flowDiv.append($progressBar);
                });
            },

            initGrid: function (Grid, renderId, dataStore, showIviColumn) {
                var dataGrid;
                var columns = [{
                    title: '渠道',
                    sortable: false,
                    dataIndex: 'channel',
                    width: '16%',
                    renderer: function (value, obj, index) {
                        return util.getChannelText(value);
                    }
                }, {
                    title: '业务发展基金',
                    sortable: false,
                    dataIndex: 'bdFund',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return "￥" + $.formatMoney(value, 2);
                        } else {
                            return "￥0";
                        }
                    }
                }, {
                    title: '市场营销基金',
                    sortable: false,
                    dataIndex: 'mktFund',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return "￥" + $.formatMoney(value, 2);
                        } else {
                            return "￥0";
                        }
                    }
                }

                ];
                if (showIviColumn) {
                    columns.push({
                        title: '奖励基金（IVI）',
                        sortable: false,
                        dataIndex: 'iviFund',
                        width: '28%',
                        renderer: function (value, obj, index) {
                            if (value != undefined && "" != value && !isNaN(value)) {
                                return "￥" + $.formatMoney(value, 2);
                            } else {
                                return "￥0";
                            }
                        }
                    });
                }
                dataGrid = new Grid.Grid({
                    render: renderId,
                    columns: columns,
                    store: dataStore,
                    bbar: false,
                    width: '100%'
                });
                return dataGrid;
            },

            initOrgFundGrid: function (item) {

                loader.initTotalFundGrid(item);//已累计基金总额（含税）
                util.initTooltipTop('#totalToolTip', 'bottom-left');
                $("#totalfundGridDiv").show();

                loader.initUsedFundGrid(item);//已申报发票总额（含税）
                util.initTooltipTop('#declaredToolTip', 'bottom-right');
                $("#usedfundGridDiv").show();

                loader.initApplyFundGrid(item);//已申报基金总额（含税）
                util.initTooltipTop('#applyFundToolTip', 'bottom-left');
                $("#applyfundGridDiv").show();

                loader.initActualFundGrid(item);//已兑现基金总额（含税）
                util.initTooltipTop('#actualFundToolTip', 'bottom-right');
                $("#actualfundGridDiv").show();

                loader.initFundGrid(item); //剩余可用
                util.initTooltipTop('#surplusToolTip', 'bottom-left');
                $("#fundGridDiv").show();

                $(".tableDiv").show().css("display", "inline-block");
            },
            //剩余
            initFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getFundGridData(data);
                    if (fundGrid) {
                        fundStore.setResult(gridData);
                        return;
                    }

                    fundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    fundGrid = loader.initGrid(Grid, "#fundGrid", fundStore, true);
                    fundGrid.render();
                });
            },
            //累计
            initTotalFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getTotalFundGridData(data);
                    if (totalFundGrid) {
                        totalFundStore.setResult(gridData);
                        return;
                    }

                    totalFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    totalFundGrid = loader.initGrid(Grid, "#totalfundGrid", totalFundStore, true);
                    totalFundGrid.render();
                });
            },
            //已申报
            initUsedFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getUsedFundGridData(data);
                    if (usedFundGrid) {
                        usedFundStore.setResult(gridData);
                        return;
                    }

                    usedFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    usedFundGrid = loader.initGrid(Grid, "#usedfundGrid", usedFundStore);
                    usedFundGrid.render();
                });
            },
            initApplyFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getApplyFundGridData(data);
                    if (applyFundGrid) {
                        applyFundStore.setResult(gridData);
                        return;
                    }

                    applyFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    applyFundGrid = loader.initGrid(Grid, '#applyfundGrid', applyFundStore);
                    applyFundGrid.render();
                });
            },
            //已兑现
            initActualFundGrid: function (data) {
                if (data.orgId == "-1") {
                    return;
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var gridData = util.getActualFundGridData(data);
                    if (actualFundGrid) {
                        actualFundStore.setResult(gridData);
                        return;
                    }

                    actualFundStore = new Data.Store({
                        data: gridData,
                        autoLoad: true
                    });

                    actualFundGrid = loader.initGrid(Grid, '#actualfundGrid', actualFundStore, true);
                    actualFundGrid.render();
                });
            },
        },
        action = {
            pass: function (opinion, fileList) {
                var nextStep = 1;
                /*if (roleCode == "areaAudit" || roleCode == 'channelAudit') {
                    nextStep = "2";
                } else if (roleCode == "bsNorth" || roleCode == "bsSouth" || roleCode == "bsSW") {
                    nextStep = "4";
                }*/

                req.audit(rebateId, nextStep, opinion, fileList, function (data) {
                    common.alertMes("处理成功", 'success');
                    window.history.go(-1);
                });
            },
            reject: function (opinion, fileList) {
                var nextStep = 0;
                /*if (roleCode == "areaAudit" || roleCode == 'channelAudit') {
                    nextStep = "3";
                } else if (roleCode == "bsNorth" || roleCode == "bsSouth" || roleCode == "bsSW") {
                    nextStep = "5";
                }*/

                req.reject(rebateId, opinion, fileList, function (data) {
                    common.alertMes("处理成功", 'success');
                    window.history.go(-1);
                });
            },
            auditImg: function (imgId, auditType) {
                if (auditType == "1") {//审核通过
                    $("#" + imgId + "pass").addClass("selected");
                    $("#" + imgId + "reject").removeClass("selected");

                    util.removeAuditInfo(imgId);
                } else if (auditType == "0") {//审核不通过
                    $("#" + imgId + "reject").addClass("selected");
                    $("#" + imgId + "pass").removeClass("selected");

                    loader.showImgAuditDialog(imgId);
                }

                for (var i in fileAuditList) {
                    if (imgId == fileAuditList[i].attId) {
                        fileAuditList[i].auditStatus = auditType;
                        return;
                    }
                }
            },
            submit: function () {
                var confMsg = '请确认是否提交数据？';
                common.confirmMes(confMsg, function () {
                    req.supplement(rebateId, supplementFile, function (data) {
                        common.alertMes("处理成功", 'success');
                        window.history.go(-1);
                    });
                });
            }
        };

    return {
        init: function () {
            $('input:text:first').bind('keydown', function (e) {
                var key = e.which;
                if (key == 13) {
                    e.preventDefault();
                    action.search();
                }
            });

            req.getUserRole(function () {
                loader.initDetail();
            });
        },
        returnListPage: function () {
            window.history.go(-1);
        },
        showAuidtDialog: function (type) {
            loader.showAuidtDialog(type);
        },
        auditImg: function (imgId, auditType) {
            action.auditImg(imgId, auditType);
        },
        submit: function () {
            return action.submit();
        }
    }

}($));