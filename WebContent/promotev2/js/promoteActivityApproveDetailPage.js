$(document).ready(function(){
	initTab();
	initActivityInfo();
	initHistoryGrid();
	if(feedbackType=='feedback'){
		initFeedbackInfo();
	}
});	
var foo = new $.JsonRpcClient({
	ajaxUrl: '/wxPublicRpc.do'
});

var item;
var activityTypeName;
var selectWorkshopCtrl=null;
var partnerId = null;
var v2SeminarOilDatas;
function initActivityInfo() {
	foo.call("v2PromoteApplicationService.queryActivityDetails", [activityId, activityTypeCode], function (result) {
		if (result.code == 'success') {
			item = result.resultObj;
			v2SeminarOilDatas = item.v2SeminarOilsDetail;
			var uri;
			if (activityTypeCode == 'YTH') {
				//研讨会
				uri = "/promotev2/jsp/detail/seminar.jsp?t=" + new Date().getTime();
				activityTypeName = "研讨会"
				$("#activityContentBox").load(uri, function (res, status) {
					var seminarDetailGrid;
					var highSeminarDetailGrid;
					var cdSeminarDetailGrid;
					foo.call("v2PromoteApplicationService.getPacksDetailInfo", [activityId], function (result) {
						if(result.code=='success'){
							BUI.use(["bui/grid"], function (Grid) {
								
//								if(item.applyHighPacks && item.applyHighPacks>0){
								//普通研讨会
								var columns=[{
									title: "名称",
									dataIndex: "materialName",
									sortable: false,
									width: "30%"
								}, {
									title: "数量",
									dataIndex: "materialAmount",
									sortable: false,
									width: "30%",
									renderer: function(v, item) {
					            		if(item.materialsFlag == "1") {
					            			return "-"
					            		} else {
					            			return v;
					            		}
					            	}
								}, 
//								{
//									title: "总积分",
//									dataIndex: "materialsPrice",
//									sortable: false,
//									width: "15%",
//									renderer: function(v, item) {
//					            		if(item.materialsFlag == "1") {
//					            			return v
//					            		} else {
//					            			return "-";
//					            		}
//					            	}
//								},
								{
									title: "描述",
									dataIndex: "remark",
									sortable: false,
									width: "40%"
								}];
								seminarDetailGrid = new Grid.Grid({
									columns: columns,
									items:result.responseActivityPacks.ythPacksLst,
									render: '#seminarDetailGrid',
									width: "100%",
								});
								seminarDetailGrid.render();
//								}else{
//									$('#highSeminarId').hide();
//								}
								//优质研讨会
//								if(item.applyPacksCount && item.applyPacksCount>0){
								var columns2=[{
									title: "名称",
									dataIndex: "materialName",
									sortable: false,
									width: "30%"
								}, {
									title: "数量",
									dataIndex: "materialAmount",
									sortable: false,
									width: "30%",
									renderer: function(v, item) {
					            		if(item.materialsFlag == "1") {
					            			return "-"
					            		} else {
					            			return v;
					            		}
					            	}
								},
//								{
//									title: "总积分",
//									dataIndex: "materialsPrice",
//									sortable: false,
//									width: "15%",
//									renderer: function(v, item) {
//					            		if(item.materialsFlag == "1") {
//					            			return v
//					            		} else {
//					            			return "-";
//					            		}
//					            	}
//								},
								{
									title: "描述",
									dataIndex: "remark",
									sortable: false,
									width: "40%"
								}];
								highSeminarDetailGrid = new Grid.Grid({
									columns: columns2,
									items:result.responseActivityPacks.yzythPacksLst,
									render: '#highSeminarDetailGrid',
									width: "100%",
								});
								highSeminarDetailGrid.render();
//								}else{
//									$("#seminarId").hide();
//								}
								//车队研讨会
								//if(item.applyCdPacksCount && item.applyCdPacksCount>0){
								var columns3=[{
									title: "名称",
									dataIndex: "materialName",
									sortable: false,
									width: "30%"
								}, {
									title: "数量",
									dataIndex: "materialAmount",
									sortable: false,
									width: "30%",
									renderer: function(v, item) {
					            		if(item.materialsFlag == "1") {
					            			return "-"
					            		} else {
					            			return v;
					            		}
					            	}
								},
								{
									title: "描述",
									dataIndex: "remark",
									sortable: false,
									width: "40%"
								}];
								cdSeminarDetailGrid = new Grid.Grid({
									columns: columns3,
									items:result.responseActivityPacks.cdythPacksLst,
									render: '#cdSeminarDetailGrid',
									width: "100%",
								});
								cdSeminarDetailGrid.render();
//								}else{
//									$('#cdDivId').hide();
//								}
							});
						}
					});
					$("#seminarPac").html(item.applyPacksCount);
					$("#highSeminarPac").html(item.applyHighPacks);
					$("#applyCDPacks").html(item.applyCdPacksCount);
					$("#activityType").html(activityTypeName);
					$("#dealer").html(item.activityOrganizers);
					$("#contactPerson").html(item.contactPerson);
					$("#contactTel").html(item.contactTel);
					$("#contactAddress").html(item.contactAddress);
					$("#applyPoints").html(item.applyPoints);
					$("#venueMeal").html(item.venueMeal + "元");
					$("#activityTime").html(common.formatDate(new Date(item.activityStartTime), 'yyyy.MM.dd') + ' - ' + common.formatDate(new Date(item.activityEndTime), 'yyyy.MM.dd'))
					$("#activityLocation").html(item.activityAddress);
					$("#inviteClientNum").html(item.invitedCustomersCount);
					$("#B2BfollowerNum").html(item.b2bWechatPlatformsCount);
					//$("#preOrderNum").html(item.estimatedOrderQuantity + "L");
					$("#activitySubject").html(item.activitySubject);
					$("#seminarTarget").html(item.seminarPurpose);
					$("#seminarContent").html(item.seminarContent);
					$("#comments").html(item.remark);
					
					//产品油料信息
					$("#applicant").html(v2SeminarOilDatas.applicant);
					$("#applicantDateId").html(v2SeminarOilDatas.applicantDate);
					$("#departmentId").html(v2SeminarOilDatas.department);
					$("#warehouseId").html(v2SeminarOilDatas.warehouse);
					$("#costCenterId").html(v2SeminarOilDatas.costCenter);
					$("#glNoId").html(v2SeminarOilDatas.glNo);
					$("#customerNameId").html(v2SeminarOilDatas.customerName);
					$("#typeId").html(v2SeminarOilDatas.type);
					$("#customerSapCodeId").html(v2SeminarOilDatas.customerSapCode);
					$("#responsibleCsrId").html(v2SeminarOilDatas.responsibleCsr);
					$("#shipToCodeId").html(v2SeminarOilDatas.shipToCode);
					$("#deliveryDressId").html(v2SeminarOilDatas.deliveryDress);
					$("#deliveryDressId").html(v2SeminarOilDatas.deliveryDress);
					$("#seminarOilReasonId").html(v2SeminarOilDatas.applayOilReason);
					var flagTotal = false;
					if(v2SeminarOilDatas.yzProductQuantity && parseFloat(v2SeminarOilDatas.yzProductQuantity)>0.0 ){
						flagTotal = true;
						$('#yzythProductId').show();
						$("#yzProductCode").val(v2SeminarOilDatas.yzProductCode);
						$("#yzProductName").val(v2SeminarOilDatas.yzProductName);
						$("#yzProductPackage").val(v2SeminarOilDatas.yzProductPackage);
						$("#yzQuantity").val(v2SeminarOilDatas.yzProductQuantity);
						$("#yzVolumn").val(v2SeminarOilDatas.yzVolumn);
						$("#yzCost").val(v2SeminarOilDatas.yzCost);
					}
                    if(v2SeminarOilDatas.ptProductQuantity && parseFloat(v2SeminarOilDatas.ptProductQuantity)>0.0 ){
                    	flagTotal = true;
                    	$('#ptythProductId').show();
						$("#ptProductCode").val(v2SeminarOilDatas.ptProductCode);
						$("#ptProductName").val(v2SeminarOilDatas.ptProductName);
						$("#ptProductPackage").val(v2SeminarOilDatas.ptProductPackage);
						$("#ptQuantity").val(v2SeminarOilDatas.ptProductQuantity);
						$("#ptVolumn").val(v2SeminarOilDatas.ptVolumn);
						$("#ptCost").val(v2SeminarOilDatas.ptCost);
					}
                    if(flagTotal){
                    	$('#productTotalId').show();
                    	$("#quantityTotal").val(v2SeminarOilDatas.quantityTotal);
                    	$("#volumnTotal").val(v2SeminarOilDatas.volumnTotal);
                    	$("#costTotal").val(v2SeminarOilDatas.costTotal);
                    }
				});
			} else if(activityTypeCode == 'XDKYLB'){
				//新店开业礼包
				uri = "/promotev2/jsp/detail/newshop.jsp?t=" + new Date().getTime();
				activityTypeName = "新店开业礼包"
				$("#activityContentBox").load(uri, function (res, status) {
					var entershopDetailGrid,attackShopDetailGrid,trunkGiftGrid,trunkGiftStore,gdGiftGrid;
					BUI.use(["bui/grid","bui/data"], function (Grid,Data) {
						foo.call("v2PromoteApplicationService.getPacksDetailInfo", [activityId], function (result) {
							var columns=[{
								title: "名称",
								dataIndex: "materialName",
								sortable: false,
								width: "30%"
							}, {
								title: "数量",
								dataIndex: "materialAmount",
								sortable: false,
								width: "30%",
								renderer: function(v, item) {
				            		if(item.materialsFlag == "1") {
				            			return "-"
				            		} else {
				            			return v;
				            		}
				            	}
							}, 
//							{
//								title: "总积分",
//								dataIndex: "materialsTotalAmount",
//								sortable: false,
//								width: "15%",
//								renderer: function(v, item) {
//				            		if(item.materialsFlag == "1") {
//				            			return v
//				            		} else {
//				            			return "-";
//				            		}
//				            	}
//							},
							{
								title: "描述",
								dataIndex: "remark",
								sortable: false,
								width: "40%"
							}];
							entershopDetailGrid = new Grid.Grid({
								columns: columns,
								items:result.responseActivityPacks.rdPacksLst,
								render: '#entershopDetailGrid',
								width: "100%",
							});
							entershopDetailGrid.render();
							
//							var columns2=[{
//								title: "名称",
//								dataIndex: "materialsName",
//								sortable: false,
//								width: "30%"
//							}, {
//								title: "数量",
//								dataIndex: "materialsCount",
//								sortable: false,
//								width: "15%",
//								renderer: function(v, item) {
//				            		if(item.materialsFlag == "1") {
//				            			return "-"
//				            		} else {
//				            			return v;
//				            		}
//				            	}
//							}, {
//								title: "总积分",
//								dataIndex: "materialsTotalAmount",
//								sortable: false,
//								width: "15%",
//								renderer: function(v, item) {
//				            		if(item.materialsFlag == "1") {
//				            			return v
//				            		} else {
//				            			return "-";
//				            		}
//				            	}
//							}, {
//								title: "描述",
//								dataIndex: "materialsSpecification",
//								sortable: false,
//								width: "40%"
//							}];
//							attackShopDetailGrid = new Grid.Grid({
//								columns: columns2,
//								items:result.responseActivityPacks.gdPacksLst,
//								render: '#attackShopDetailGrid',
//								width: "100%",
//							});
//							attackShopDetailGrid.render();
						});

//						trunkGiftStore=new Data.Store({
//							data:item.promotionalDetailLst							
//						});
//						trunkGiftGrid=new Grid.Grid({
//							columns:[{
//								title:"物料名称",
//								dataIndex:'productName',
//								sortable:false,
//								width:'50%'
//							},{
//								title:"数量",
//								dataIndex:"productCount",
//								sortable:false,
//								width:'50%'
//							}],
//							store:trunkGiftStore,
//							render:'#trunkGiftGrid',
//							width:"100%"
//						});
//						trunkGiftGrid.render();
						
//						if(item.promoteActivityGifLst.length>0){
//							$("#gdGiftGrid").parents(".field-group").show();
//							var gdGiftStore=new Data.Store({
//								data:item.promoteActivityGifLst							
//							});
//							gdGiftGrid=new Grid.Grid({
//								columns:[{
//									title:"物料名称",
//									dataIndex:'gifCode',
//									sortable:false,
//									width:'30%'
//								},{
//									title:"描述",
//									dataIndex:"gifRemark",
//									sortable:false,
//									width:'50%'
//								},{
//									title:"数量",
//									dataIndex:"gifCount",
//									sortable:false,
//									width:'20%'
//								}],
//								store:gdGiftStore,
//								render:'#gdGiftGrid',
//								width:"100%"
//							});
//							gdGiftGrid.render();
//						}
					});
					
					//加载门店信息
					partnerId = item.activityOrganizersId;
					var workShopRelation = item.workshopRelationships;
					initSelectWorkshopCtrl(workShopRelation);
					$("#entershopPac").html(item.packsNumber);
					$("#attackShopPac").html(item.gdPacksNumber);
					$("#activityType").html(activityTypeName);
					$("#dealer").html(item.activityOrganizers);
					$("#contactPerson").html(item.contactPerson);
					$("#contactTel").html(item.contactTel);
					$("#contactAddress").html(item.contactAddress);
					$("#activityTime").html(common.formatDate(new Date(item.activityStartTime), 'yyyy.MM.dd') + ' - ' + common.formatDate(new Date(item.activityEndTime), 'yyyy.MM.dd'))
					$("#participateWorkshop").html(item.participateWorkshopName);
					//$("#salesNum").html(item.expectedCompleteSalesPromotion + "L");
					$("#activitySubject").html(item.activitySubject);
					$("#address").html(item.address);
					$("#comments").html(item.remark);
//					if(item.lstSpecialGifts.length > 0) {
//						var specialList = item.lstSpecialGifts;
//						var clothesInfo = '<label class="value-label" style="font-weight:700">工作服尺码数量备注：</label>';
//						for(var i in specialList) {
//							var itemInfo = '<label class="value-label">' + specialList[i].specialGifRemark + "：" 
//							+ specialList[i].materialCount + '件</label>';
//							clothesInfo += itemInfo;
//						}
//						$("#clothesDiv").append(clothesInfo);
//					}
					
				});
			} else if (activityTypeCode == 'LYXFLB'){
				//路演消费者互动包
				uri = "/promotev2/jsp/detail/roadshow.jsp?t=" + new Date().getTime();
				activityTypeName = "路演消费者互动包"
				$("#activityContentBox").load(uri, function (res, status) {
					var roadshowDetailGrid;
					foo.call("v2PromoteApplicationService.getPacksDetailInfo", [activityId], function (result) {
						if (result.code == 'success') {
							BUI.use(["bui/grid"], function (Grid) {
								var columns=[{
									title: "名称",
									dataIndex: "materialName",
									sortable: false,
									width: "30%"
								}, {
									title: "数量",
									dataIndex: "materialAmount",
									sortable: false,
									width: "30%",
									renderer: function(v, item) {
					            		if(item.materialsFlag == "1") {
					            			return "-"
					            		} else {
					            			return v;
					            		}
					            	}
								},
//								{
//									title: "总积分",
//									dataIndex: "materialsTotalAmount",
//									sortable: false,
//									width: "15%",
//									renderer: function(v, item) {
//					            		if(item.materialsFlag == "1") {
//					            			return v
//					            		} else {
//					            			return "-";
//					            		}
//					            	}
//								},
								{
									title: "描述",
									dataIndex: "remark",
									sortable: false,
									width: "40%"
								}];
								roadshowDetailGrid=new Grid.Grid({
									columns: columns,
									items: result.responseActivityPacks.roadshowPacksLst,
									render: '#roadshowDetailGrid',
									width: "100%",
								});
								roadshowDetailGrid.render();
							});
						}
					});	
					var workShopRelation = item.workshopRelationships;
					initSelectWorkshopCtrl(workShopRelation);
					$("#roadShowPac").html(item.roadShowGifPackageCount);
					$("#activityType").html(activityTypeName);
					$("#dealer").html(item.activityOrganizers);
					$("#contactPerson").html(item.contactPerson);
					$("#contactTel").html(item.contactTel);
					$("#contactAddress").html(item.contactAddress);
					$("#activityTime").html(common.formatDate(new Date(item.activityStartTime), 'yyyy.MM.dd') + ' - ' + common.formatDate(new Date(item.activityEndTime), 'yyyy.MM.dd'))
					$("#trunkDriverNum").html(item.estimatedTruckDriversNumbers);
					//$("#sellProduct").html(item.expectSellProducts);
					$("#activitySubject").html(item.activitySubject);
					$("#B2BfollowerNum").html(item.b2bWechatPlatformsCount);
					$("#participateWorkshop").html(item.activityWorkshops);
					$("#comments").html(item.remark);
				});
			}
		}
	});
}

function renderGiftGridLoader(data, gridName) {
	BUI.use(['bui/grid','bui/data'], function(Grid, Data) {
		var materialStore = new Data.Store({
			data: data,
			autoLoad: true
		});
		
		var loaderGiftGrid = new Grid.Grid({
			columns: [{
            	title: '名称',
            	width: '30%',
            	dataIndex: 'materialsName',
            	renderer: function(v, item) {
//            		var imgDomStr = util.getMaterialImagesDom(item.materialsId, item.imgId, item.materialsName, item.materialsSpecification);
            		var des = '<div class="grid-cell-block"><div class="grid-cell-line">'+ v + '</div>'
//            		return imgDomStr + des;
            		return des;
            	}
            }, {
            	title: '数量',
            	width: '10%',
            	dataIndex: 'materialsCount',
            	renderer: function(v, item) {
            		if(item.materialsFlag == "1") {
            			return "-"
            		} else {
            			return v;
            		}
            	}
            }, {
            	title: '总积分',
            	width: '10%',
            	dataIndex: 'materialsTotalAmount',
            	renderer: function(v, item) {
            		if(item.materialsFlag == "1") {
            			return v
            		} else {
            			return "-";
            		}
            	}
            }, {
            	title: '描述',
            	width: '50%',
            	dataIndex: 'materialsSpecification'
            }],
			render: '#' + gridName,
            width: "100%",
            store: materialStore
		});
		loaderGiftGrid.render();
	});
}

function initTab(){
	BUI.use(['bui/tab','bui/mask'],function(Tab){
        tab = new Tab.TabPanel({
            render : '#tabs',
            elCls : 'nav-tabs',
            panelContainer : '#tab-content-wrapper',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
            autoRender: false,
            children: [
                { title: '活动详情', value: '1', type: "xiangqing" },
				{ title: '操作历史', value: '2', type: "lishi" },
				{ title: '活动反馈', value: '3', type: "fankui" ,visible:false}
            ]
        }); 
        tab.on('selectedchange', function(ev){
            var item = ev.item,_value = item.get('value'),_text = item.get('text');
            var type=ev.item.get("type");
            $(window).trigger('resize');
        });
		tab.render();
		if(feedbackType=='feedback'){
//			tab.getItemAt(2).show();
			tab.setSelected(tab.getItemAt(0));
		} else{
			var items=tab.getItems();
			tab.setSelected(tab.getItemAt(0));
		}
	});
}

var historyStore,historyGrid;
function initHistoryGrid(){
	foo.call("v2PromoteApplicationWorkFlowService.queryPromoteActivityApprovalHistory",[batchId],function(result){
		if(result.code=='success'){
			BUI.use(["bui/grid",'bui/data'],function(Grid,Data){
				var columns=[
					{
						title:'序号',
						renderer:function(value,obj,index){
							return index+1;
						},
						sortable:false,
						width:"10%"
					},{
						title:"步骤",
						dataIndex:"stepSequence",
						sortable:false,
						width:"20%"
					},{
						title:"处理意见",
						dataIndex:'approveRemark',
						sortable:false,
						width:"30%"
					},{
						title:"处理结果",
						dataIndex:"approveStatus",
						renderer:function(value,obj,index){
							if(value=='3'){
								return "通过";
							} else if(value=="4"){
								return "驳回";
							} else if(value=="1"){
								return "提交";
							} else if(value=='6'){
								return "已反馈";
							}
						},
						sortable:false,
						width:"10%"
					},{
						title:"处理人",
						dataIndex:"approveName",
						sortable:false,
						width:"10%"
					},{
						title:"处理时间",
						dataIndex:"approveTime",
						renderer:function(value,obj,index){
							return common.formatDate(new Date(value), 'yyyy-MM-dd hh:mm:ss');
						},
						sortable:false,
						width:"20%"
					}
				];
				historyStore=new Data.Store({
					data:result.resultLst
				});
				historyGrid=new Grid.Grid({
					columns:columns,
					store:historyStore,
					width:"100%",
					render:"#historyGrid"
				});
				historyGrid.render();
			});
		}
	});
}

var approveDialog;
function showActivityApproveDialog(status) {
	$("#approveReason").val("");
	if (approveDialog == null) {
		BUI.use("bui/overlay", function (Overlay) {
			approveDialog = new Overlay.Dialog({
				title: '审批理由',
				width: 500,
				height: 300,
				mask: true,  //设置是否模态
				contentId: "approveDialog",
				success: function(){
					approveActivity(status);
					this.close();
				}
			});
			approveDialog.show();
		});
	} else {
		approveDialog.show();
	}
}
function showActivityRollbackDialog(status) {
	$("#approveReason").val("");
	if (approveDialog == null) {
		BUI.use("bui/overlay", function (Overlay) {
			approveDialog = new Overlay.Dialog({
				title: '撤回理由',
				width: 500,
				height: 300,
				mask: true,  //设置是否模态
				contentId: "approveDialog",
				success: function(){
					approveActivity(status);
					this.close();
				}
			});
			approveDialog.show();
		});
	} else {
		approveDialog.show();
	}
}
function approveActivity(status){
	var reqData={
		applyBatchId:item.applyBatchId,
		approveRemark:$("#approveReason").val(),
		status:status,
		version:versionFlag,
		activityType:item.activityType,
		queryUserType:promoteUserType,
		activityAmount:item.activityAmount,
		activityId:activityId
	}
	LoadMask.show();
	foo.call("v2PromoteApplicationWorkFlowService.approvePromoteApplication",[reqData],function(result){
		LoadMask.hide();
		if(result.code=='success'){
			if(status==3){
				common.alertMes("审批成功","success");
//				approveDialog.close();
				window.location.href="/promotev2/jsp/promoteActivityApplyPage.jsp?promoteUserType="+promoteUserType+"&pageType=approve";
			} else if(status==4){
				common.alertMes("驳回成功","success");
//				approveActivity.close();
				window.location.href="/promotev2/jsp/promoteActivityApplyPage.jsp?promoteUserType="+promoteUserType+"&pageType=approve";
			} else if(status==5){
				common.alertMes("撤回成功","success");
//				approveActivity.close();
				if(promoteUserType=='sales'){
					window.location.href="/promotev2/jsp/promoteActivityApplyPage.jsp?promoteUserType="+promoteUserType+"&pageType=apply";
				}else{
					window.location.href="/promotev2/jsp/promoteActivityApplyPage.jsp?promoteUserType="+promoteUserType+"&pageType=approve";
				}
			}
		}
	});
}

// var activityFeedbackPicList;
function initFeedbackInfo(){
	foo.call("v2PromoteApplicationService.getActivityFeedBackDetail",[activityId],function(result){
		if(result.code=='success'){
			var item=result.responseActivityFeedbackDetail;
			$("#activityFeedBackInfo").html(item.activityFeedBackInfo);
			
			if(activityTypeCode=='YTH'){
				//研讨会活动信息
				//$("#venueMealFee").html(item.venueMeal);

				var a14PicList=item.a14ApplyFormImgLst;
				if(a14PicList!=null&&a14PicList!=''){
					for(var i=0;i<a14PicList.length;i++){
						$('#a14PicList').append('<li class="img-item"><div onclick="showImgDetail('+a14PicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +a14PicList[i].attId+'"></div></li>');
					}
				} else{
					$("#a14PicList").append('无');
				}
				var a14FileList=item.a14ApplyFormFileLst;
				if(a14FileList.length>0&&a14FileList[0]!=''){
					for(var i=0;i<a14FileList.length;i++){
						$("#a14FileList").append('<li><a onclick="downloadFiles('+a14FileList[i].attId+',\''+a14FileList[i].fileName+'\')">'+a14FileList[i].fileName+'</a></li>');
					}
				} else{
					$("#a14FileList").append('无');
				}

				var ticketPicList=item.complianceInvoiceImgLst;
				if(ticketPicList!=null&&ticketPicList!=''){
					for(var i=0;i<ticketPicList.length;i++){
						$('#ticketPicList').append('<li class="img-item"><div onclick="showImgDetail('+ticketPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +ticketPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#ticketPicList").append('无');
				}
				var ticketFileList=item.complianceInvoiceFileLst;
				if(ticketFileList.length>0&&ticketFileList[0]!=''){
					for(var i=0;i<ticketFileList.length;i++){
						$("#ticketFileList").append('<li><a onclick="downloadFiles('+ticketFileList[i].attId+',\''+ticketFileList[i].fileName+'\')">'+ticketFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#ticketFileList").append('无');
				}

				var timesheetPicList=item.conferenceScheduleImgLst;
				if(timesheetPicList!=null&&timesheetPicList!=''){
					for(var i=0;i<timesheetPicList.length;i++){
						$('#timesheetPicList').append('<li class="img-item"><div onclick="showImgDetail('+timesheetPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +timesheetPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#timesheetPicList").append('无');
				}
				var timesheetFileList=item.conferenceScheduleFileLst;
				if(timesheetFileList.length>0&&timesheetFileList[0]!=''){
						for(var i=0;i<timesheetFileList.length;i++){
						$("#timesheetFileList").append('<li><a onclick="downloadFiles('+timesheetFileList[i].attId+',\''+timesheetFileList[i].fileName+'\')">'+timesheetFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#timesheetFileList").append('无');
				}

				var registerPicList=item.checkInSlipImgLst;
				if(registerPicList!=null&&registerPicList!=''){
					for(var i=0;i<registerPicList.length;i++){
						$('#registerPicList').append('<li class="img-item"><div onclick="showImgDetail('+registerPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +registerPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#registerPicList").append('无');
				}
				var registerFileList=item.checkInSlipFileLst;
				if(registerFileList.length>0&&registerFileList[0]!=''){
					for(var i=0;i<registerFileList.length;i++){
						$("#registerFileList").append('<li><a onclick="downloadFiles('+registerFileList[i].attId+',\''+registerFileList[i].fileName+'\')">'+registerFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#registerFileList").append('无');
				}

				var protocalPicList=item.tripartiteAgreementImgLst;
				if(protocalPicList!=null&&protocalPicList!=''){
					for(var i=0;i<protocalPicList.length;i++){
						$('#protocalPicList').append('<li class="img-item"><div onclick="showImgDetail('+protocalPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +protocalPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#protocalPicList").append('无');
				}
				var protocalFileList=item.tripartiteAgreementFileLst;
				if(protocalFileList.length>0&&protocalFileList[0]!=''){
					for(var i=0;i<protocalFileList.length;i++){
						$("#protocalFileList").append('<li><a onclick="downloadFiles('+protocalFileList[i].attId+',\''+protocalFileList[i].fileName+'\')">'+protocalFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#protocalFileList").append('无');
				}

				var hotelPicList=item.hotelFeeStatementImgLst;
				if(hotelPicList!=null&&hotelPicList!=''){
					for(var i=0;i<hotelPicList.length;i++){
						$('#hotelPicList').append('<li class="img-item"><div onclick="showImgDetail('+hotelPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +hotelPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#hotelPicList").append('无');
				}
				var hotelFileList=item.hotelFeeStatementFileLst;
				if(hotelFileList.length>0&&hotelFileList[0]!=''){
					for(var i=0;i<hotelFileList.length;i++){
						$("#hotelFileList").append('<li><a onclick="downloadFiles('+hotelFileList[i].attId+',\''+hotelFileList[i].fileName+'\')">'+hotelFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#hotelFileList").append('无');
				}

				var provePicList=item.proofPurchaseImgLst;
				if(provePicList!=null&&provePicList!=''){
					for(var i=0;i<provePicList.length;i++){
						$('#provePicList').append('<li class="img-item"><div onclick="showImgDetail('+provePicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +provePicList[i].attId+'"></div></li>');
					}
				} else{
					$("#provePicList").append('无');
				}
				var proveFileList=item.proofPurchaseFileLst;
				if(proveFileList.length>0&&proveFileList[0]!=''){
					for(var i=0;i<proveFileList.length;i++){
						$("#proveFileList").append('<li><a onclick="downloadFiles('+proveFileList[i].attId+',\''+proveFileList[i].fileName+'\')">'+proveFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#proveFileList").append('无');
				}

				var meetingPicList=item.proofPurchaseImgLst;
				if(meetingPicList!=null&&meetingPicList!=''){
					for(var i=0;i<meetingPicList.length;i++){
						$('#meetingPicList').append('<li class="img-item"><div onclick="showImgDetail('+meetingPicList[i].attId+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +meetingPicList[i].attId+'"></div></li>');
					}
				} else{
					$("#meetingPicList").append('无');
				}
				var meetingFileList=item.proofPurchaseFileLst;
				if(meetingFileList.length>0&&meetingFileList[0]!=''){
					for(var i=0;i<meetingFileList.length;i++){
						$("#meetingFileList").append('<li><a onclick="downloadFiles('+meetingFileList[i].attId+',\''+meetingFileList[i].fileName+'\')">'+meetingFileList[i].fileName+'</a></li>');
					}
				} else{
					$("#meetingFileList").append('无');
				}				
			} else{
				//普通活动信息
				if(null!=item.activityFeedBackImgIds)
				{
    				var fileIdList=item.activityFeedBackImgIds.split(",");
    				if(fileIdList!=null&&fileIdList[0]!=''){
    					for(var i=0;i<fileIdList.length;i++){
    						$('#activityFeedbackPicList').append('<li class="img-item"><div onclick="showImgDetail('+fileIdList[i]+');" class="img-wrapper"><img src="'+common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +fileIdList[i]+'"></div></li>');
    					}
    				}
				}
			}
			
		}
	});
	if(activityTypeCode=='YTH'){
		$("#ythFeedbackInfo").show();
	} else {
		$("#normalFeedbackInfo").show();
	}
}

var imgDetailDialog;
var imgViewer;
function showImgDetail(attId){
	if(imgDetailDialog==null){
		BUI.use(['bui/overlay', 'bui/imgview'], function (Overlay, ImgView) {
			imgDetailDialog = new Overlay.Dialog({
				title: '图片浏览器',
				width: 500,
				height: 500,
				elCls: 'imgview-dialog',
				buttons: [],
				//配置DOM容器的编号
				contentId: 'imgDetailDialog',
				success: function () {
					// alert('确认');
					this.close();
				}
			});
			imgViewer = new ImgView.ViewContent({
				render: "#imgViewer",
				width: 470,
				height: 400,
				autoRender: true, // 是否自动渲染,默认为false
				drag: false
			});
			imgViewer.on("click", function () {
				imgDetailDialog.close();
			});
			imgViewer.set("imgSrc",common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +attId);
			imgDetailDialog.show();
		});
	} else{
		imgViewer.set("imgSrc",common.ctx + 'downloadAttachmentFile.do?sourceType=8&attId=' +attId);
		imgDetailDialog.show();
	}
}

function downloadFiles(attId,attfileName){
	var a=document.createElement('a');
	a.href="/downloadAttachmentFile.do?attId="+attId;
	a.download=attfileName;
	a.click();
}

//门店控件开始
function initSelectWorkshopCtrl(selectedWorkshops){
	if(selectedWorkshops){
		for(var i = 0; i < selectedWorkshops.length; i++){
			var item = selectedWorkshops[i];
			item.id = item.workshopId;
		}
	}
	selectWorkshopCtrl = new SelectItemCtrl($('#selectedWorkshops'), $('#selectWorkshopDialog'), "选择促销活动门店", 
			'workshop/queryforctrlpage.do', "#selectWorkshopGrid", 
			[
				{
                    title: '序号',
                    dataIndex: '',
                    sortable: false,
                    width: 120,
                    renderer: function (value, obj, index) {
                        return index + 1;
                    }
                },
			//	{title : '经销商名称',dataIndex : 'partnerName', width:'30%' },
				{title : '门店名称',dataIndex : 'workshopName', width:'30%' },
				{title : '门店地址',dataIndex : 'workShopAddress', width:'50%' },
//				{title : '激活时间',dataIndex : 'activeTime', width:120, sortable: false, renderer: function(value){
//					if(value){
//						return common.formatDate(new Date(value), 'yyyy-MM-dd hh:mm');
//					}
//					return '';
//				} }
			], {
		remotePage: true,
		valueField: 'id',
		textField: 'workshopName',
		selectedItems: selectedWorkshops,
		storeOpts: {
			sortField: 'id',
			sortDirection: 'ASC',
            root: "resultLst",
            totalProperty: "total",
			params: $.extend(buildWorkshopDialogParams(),{partnerId: partnerId})
           // params: $.extend(buildWorkshopDialogParams(),{})
		},
		dialogOpts: {width: 850},
		remoteDataPreprocess: function(result){
			if(result.code == 'success'){
				this._data = result[this.remoteStoreRoot];
				this._updateSelectedStatus();
			}else{
				this._data = [];
				result[this.remoteStoreRoot] = [];
				common.alertMes(result.errorMsg, 'error');
			}
		},
		buildButtons: function(){
			var self = this;
			return [{
				text: '确认',
				elCls: 'button btn-create',
				handler: function () {
					self.dialogSubmit();
					self.closeDialog();
				}
			},
			{
				text: "取消",
				elCls: 'button btn-cancel',
				handler: function () {
					self.closeDialog();
				}
			}];
		}
	});
}
//根据参数查询
function workshopDialogQuery() {
		selectWorkshopCtrl.store.load(buildWorkshopDialogParams());
}
function buildWorkshopDialogParams(){
	if ($("#selectWorkshopDialog .query-panel").hasClass("query-adv")) {
		return {
			partnerId: partnerId,
			workshopId: $('#selectWorkshopDialog input[name=workshopId]').val(),
			prov: $('#selectWorkshopDialog input[name=prov]').val(),
			city: $('#selectWorkshopDialog input[name=city]').val(),
			dist: $('#selectWorkshopDialog input[name=dist]').val(),
			activeDateFrom: $('#selectWorkshopDialog input[name=activeDateFrom]').val(),
			activeDateTo: $('#selectWorkshopDialog input[name=activeDateTo]').val(),
			queryType: 1,
			queryField: null,
			start: 0
		};
	}else{
		return {
			partnerId: partnerId,
			workshopId:null,
			prov: null,
			city: null,
			dist: null,
			activeDateFrom: null,
			activeDateTo: null,
			queryType: 2,
			queryField: $('#selectWorkshopDialog input[name=gKeyWord]').val(),
			start: 0
		};
	}
}
function selectWorkshop(){
	    selectWorkshopCtrl.selectItems();
	if(!selectWorkshopCtrl.queryCtrlInited){
		var workshopCtrl = Ctrls.WorkshopAutoSelect.init('#selectWorkshopDialog [name=workshopId]', {placeholder: '支持名称或名称首字母搜索', params: {partnerId: partnerId}});
		Ctrls.PartnerAutoSelect.init('#selectWorkshopDialog [name=partnerId]', {params: {resourceId: 'schedule',includeSpWithNoWs: false},
			onchange: function(value){
				workshopCtrl.refresh({partnerId: value});
			}
		});
		var distCtrl = Ctrls.District.init('#selectWorkshopDialog [name=dist]', $('#selectWorkshopDialog [name=dist]').parent(),{
			autoLoad: false,
			emptyValue: '-1'
		});
		var cityCtrl = Ctrls.City.init('#selectWorkshopDialog [name=city]', $('#selectWorkshopDialog [name=city]').parent(), {
			autoLoad: false,
			emptyValue: '-1',
			events: {
				'change': function(e){
					if(distCtrl.cityId == e.value){
						return;
					}
			    	if(e.value == '-1'){
			    		distCtrl.empty();
			    	}else{
			    		distCtrl.load({cityId: e.value});
			    	}
			    	distCtrl.cityId = e.value;
			    }
			}
		});
		Ctrls.Province.init('#selectWorkshopDialog [name=prov]', $('#selectWorkshopDialog [name=prov]').parent(), {
			autoLoad: true,
			emptyValue: '-1',
			events: {
				'change': function(e){
					if(cityCtrl.provinceId == e.value){
						return;
					}
			    	if(e.value == '-1'){
			    		cityCtrl.empty();
			    		distCtrl.empty();
			    	}else{
			    		cityCtrl.load({provinceId: e.value});
			    		distCtrl.empty();
			    	}
			    	cityCtrl.provinceId = e.value;
			    }
			}
		});
		Ctrls.DicItem.init('#partnerProperty', 'partner.partnerProperty', {
			emptyValue: '-1'
		});
		Ctrls.Area.init('#selectWorkshopDialog [name=area]', {
			emptyValue: '-1',
			canEmpty: true
		});
		$('#selectWorkshopDialog .query-panel input[type=text]').keydown(function(e){
			if(e.keyCode==13){
				$(this).parents('.query-panel:first').find('.btn-query').click();
			}
		});
		selectWorkshopCtrl.queryCtrlInited = true;
	}
}
//门店控件结束



