(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-24391f54"],{"1e29":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"tempBasic",staticClass:"form",attrs:{model:e.applyForm,rules:e.rules}},[a("header-piece"),a("div",{staticClass:"form-title"},[e._v("Application Information 申请人信息")]),a("application"),a("div",{staticClass:"form-title"},[e._v("Customer Basic Information 客户基础信息")]),a("customer-basic"),a("div",{staticClass:"form-title"},[e._v("Contract Information 财务相关信息")]),a("other"),a("upload")],1)},l=[],i=a("cebc"),n=a("4ade"),s=a("2b3b"),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-row",[a("el-col",{attrs:{span:12}},[a("customer-id")],1),a("el-col",{attrs:{span:12}},[a("customer-name")],1),a("el-col",{attrs:{span:12}},[a("current-credit-limit")],1),a("el-col",{attrs:{span:12}},[a("current-payment-term")],1),a("el-col",{attrs:{span:12}},[a("requested-temp-credit-limit")],1),a("el-col",{attrs:{span:12}},[a("requested-temp-payment-term")],1),a("el-col",{attrs:{span:12}},[a("total-temp-credit-limit")],1),a("el-col",{attrs:{span:12}},[a("expire-date")],1)],1)},o=[],p=a("f149"),m=a("cb85"),u=a("d119"),d=a("434a"),b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{attrs:{label:"Request to Add Temp. Credit limit 申请增加的临时额度 : ","label-width":e.labelWidth,prop:"cbiRequestedTempCreditLimit"}},[a("template",{slot:"label"},[a("el-tooltip",{staticClass:"form-item-label-tooltip",attrs:{content:"请填写需增加的临时额度，请勿填写总额度",placement:"top-start"}},[a("i",{staticClass:"el-icon-question"})]),a("span",[e._v("Request to Add Temp. Credit limit")]),a("span",{staticStyle:{color:"red"}},[e._v(" 申请增加的临时额度 :")])],1),a("el-input",{attrs:{size:"small",disabled:e.disabled,placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},[a("template",{slot:"append"},[e._v("$ "+e._s(e.money))])],2)],2)},f=[],y=(a("a481"),a("c5f6"),a("2f62")),h=a("5fe9"),T=a.n(h),v=a("65e9"),E={name:"credit-apply-cbiRequestedTempCreditLimit",computed:Object(i["a"])({},Object(y["b"])(["applyForm","canEditApply","isApplyNotInProcess"]),{disabled:function(){return!this.isApplyNotInProcess},value:{get:function(){return Object(v["a"])(this.applyForm.cbiRequestedTempCreditLimit)},set:function(e){this.$store.commit("UPDATE_APPLY_FORM",{cbiRequestedTempCreditLimit:Object(v["b"])(e)})}},labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"},money:function(){var e=Number((""+this.applyForm.cbiRequestedTempCreditLimit).replace(/,/g,"")),t=Number(this.applyForm.creditDollarRate);return Object(v["a"])(T.a.round(T.a.divide(e,t),2)||"")}})},C=E,O=a("2877"),g=Object(O["a"])(C,b,f,!1,null,null,null),_=g.exports,P=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{attrs:{label:"Requested Temp. Payment Term 申请临时信用账期 : ","label-width":e.labelWidth,prop:"cbiRequestedTempPaymentTerm"}},[a("el-select",{attrs:{placeholder:"select",disabled:e.disabled,size:"small"},model:{value:e.applyForm.cbiRequestedTempPaymentTerm,callback:function(t){e.$set(e.applyForm,"cbiRequestedTempPaymentTerm",t)},expression:"applyForm.cbiRequestedTempPaymentTerm"}},e._l(e.options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)},F=[],q={name:"credit-apply-cbiRequestedTempPaymentTerm",computed:Object(i["a"])({},Object(y["b"])(["applyForm","cfiInfo","canEditApply","isApplyNotInProcess","paymentTermListOptions"]),{disabled:function(){return!this.isApplyNotInProcess},labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"},options:{get:function(){var e=this.paymentTermListOptions||[];return e.map(function(e){return{label:e,value:e}})}}})},x=q,j=Object(O["a"])(x,P,F,!1,null,null,null),R=j.exports,A=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{attrs:{label:"Total Temp. Credit limit 临时总额度 : ","label-width":"320px"}},[a("el-input",{attrs:{size:"small",disabled:"",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},w=[],$={name:"credit-apply-cbiRequestedTempCreditLimit",computed:Object(i["a"])({},Object(y["b"])(["applyForm","canEditApply"]),{disabled:function(){return!this.canEditApply},value:{get:function(){var e=T.a.plus(+this.applyForm.cbiRequestedTempCreditLimit,+this.applyForm.cbiCreditLimitOfYearN1);return e?Object(v["d"])(e):""}}}),watch:{value:function(e){var t=this,a=Object(v["c"])(e),r=Number(this.applyForm.creditDollarRate);this.$nextTick(function(){t.$store.commit("UPDATE_APPLY_FORM",{applyAmountUsd:T.a.round(T.a.divide(a,r),2)||""})})}}},I=$,D=Object(O["a"])(I,A,w,!1,null,null,null),L=D.exports,N=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{attrs:{label:"Expire Date 到期日 : ","label-width":e.labelWidth,prop:"cbiExpireDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"select date",disabled:!e.isApplyNotInProcess,size:"small"},model:{value:e.applyForm.cbiExpireDate,callback:function(t){e.$set(e.applyForm,"cbiExpireDate",t)},expression:"applyForm.cbiExpireDate"}})],1)},W=[],k={name:"credit-apply-cbiExpireDate",computed:Object(i["a"])({},Object(y["b"])(["applyForm","canEditApply","isApplyNotInProcess"]),{labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"}})},B=k,S=Object(O["a"])(B,N,W,!1,null,null,null),U=S.exports,Y={name:"credit-apply-customerBasic",components:{CustomerId:p["a"],CustomerName:m["a"],CurrentCreditLimit:u["a"],CurrentPaymentTerm:d["a"],RequestedTempCreditLimit:_,RequestedTempPaymentTerm:R,TotalTempCreditLimit:L,ExpireDate:U}},M=Y,z=Object(O["a"])(M,c,o,!1,null,null,null),G=z.exports,Q=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-row",[a("el-col",{attrs:{span:12}},[a("comments-from-bu")],1)],1)},V=[],J=a("495a"),H={name:"credit-apply-application",components:{CommentsFromBu:J["a"]}},K=H,X=Object(O["a"])(K,Q,V,!1,null,null,null),Z=X.exports,ee=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-row",[a("el-col",{attrs:{span:12}},[a("financial-statements")],1),a("el-col",{attrs:{span:12}},[a("payment-commitment")],1),a("el-col",{attrs:{span:24}},[a("cash-deposit-with-amount")],1),a("el-col",{attrs:{span:24}},[a("the3rd-party-guarantee-with-amount")],1),a("el-col",{attrs:{span:24}},[a("bank-guarantee-with-amount")],1),a("el-col",{attrs:{span:24}},[a("personal-guarantee-with-amount")],1)],1)},te=[],ae=a("9178"),re=a("03f2"),le=a("bd08"),ie=a("be6d"),ne=a("f093"),se=a("cb99"),ce={name:"credit-apply-application",components:{FinancialStatements:ae["a"],PaymentCommitment:re["a"],CashDepositWithAmount:le["a"],The3rdPartyGuaranteeWithAmount:ie["a"],BankGuaranteeWithAmount:ne["a"],PersonalGuaranteeWithAmount:se["a"]}},oe=ce,pe=Object(O["a"])(oe,ee,te,!1,null,null,null),me=pe.exports,ue=/((^[1-9]\d*)|^0)(\.\d{2}){0,1}$/,de={aiRequestedBy:[{required:!0,message:"",trigger:"blur"}],aiTelephone:[{required:!0,message:"",trigger:"blur"}],aiSalesTeam:[{required:!0,message:"",trigger:"blur"}],cbiCustomerId:[{required:!0,message:"",trigger:"blur"}],cbiRequestedTempCreditLimit:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,a){t?ue.test(Object(v["b"])(t))?a():a(new Error("")):a()}}],cbiRequestedTempPaymentTerm:[{required:!0,message:"",trigger:"blur"}],cbiExpireDate:[{required:!0,message:"",trigger:"blur"}],cbiCashDepositWithAmount:[{validator:function(e,t,a){t?ue.test(Object(v["b"])(t))?a():a(new Error("")):a()}}],cbiThe3rdPartyGuaranteeWithAmount:[{validator:function(e,t,a){t?ue.test(Object(v["b"])(t))?a():a(new Error("")):a()}}],cbiBankGuaranteeWithAmount:[{validator:function(e,t,a){t?ue.test(Object(v["b"])(t))?a():a(new Error("")):a()}}],cbiPersonalGuaranteeWithAmount:[{validator:function(e,t,a){t?ue.test(Object(v["b"])(t))?a():a(new Error("")):a()}}]},be=a("e681"),fe={name:"credit-apply-basic-temp",components:{HeaderPiece:n["a"],Application:s["a"],CustomerBasic:G,Other:Z,Upload:me},data:function(){return{rules:de}},computed:Object(i["a"])({},Object(y["b"])(["applyForm"])),created:function(){var e=this;be["a"].$on("tempBasicValidate",function(t){e.$refs.tempBasic.validate(t)}),be["a"].$on("tempCustomerSelectChange",function(){console.log(e.applyForm.customerType),"CIA"===e.applyForm.customerType?e.rules=Object.assign({},e.rules,{cbiFinancialStatementsAttId:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,a){0===t?a(new Error("")):a()},message:"",trigger:"blur"}],cbiPaymentCommitmentAttId:[{required:!0,message:"",trigger:"blur"},{validator:function(e,t,a){0===t?a(new Error("")):a()},message:"",trigger:"blur"}]}):e.rules=Object.assign({},e.rules,{cbiFinancialStatementsAttId:void 0,cbiPaymentCommitmentAttId:void 0}),e.$nextTick(function(){e.$refs.tempBasic&&e.$refs.tempBasic.clearValidate()})})},destroyed:function(){be["a"].$off("tempBasicValidate")}},ye=fe,he=Object(O["a"])(ye,r,l,!1,null,null,null);t["a"]=he.exports},"2b3b":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-row",[a("el-col",{attrs:{span:8}},[a("prepared-by")],1),a("el-col",{attrs:{span:8}},[a("region")],1),a("el-col",{attrs:{span:8}},[a("request-date")],1),a("el-col",{attrs:{span:8}},[a("requested-by")],1),a("el-col",{attrs:{span:8}},[a("telephone")],1),a("el-col",{attrs:{span:8}},[a("sales-team")],1)],1)},l=[],i=a("3998"),n=a("6981"),s=a("6599"),c=a("e688"),o=a("22bf"),p=a("ff04"),m={name:"credit-apply-application",components:{PreparedBy:i["a"],Region:n["a"],RequestDate:s["a"],RequestedBy:c["a"],Telephone:o["a"],SalesTeam:p["a"]}},u=m,d=a("2877"),b=Object(d["a"])(u,r,l,!1,null,null,null);t["a"]=b.exports},"434a":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{staticClass:"is-required",attrs:{label:"Current Payment Term 现有信用账期 : ","label-width":e.labelWidth}},[a("el-input",{attrs:{size:"small",disabled:"",placeholder:""},model:{value:e.applyForm.cbiPaymentTermOfYearN1,callback:function(t){e.$set(e.applyForm,"cbiPaymentTermOfYearN1",t)},expression:"applyForm.cbiPaymentTermOfYearN1"}})],1)},l=[],i=a("cebc"),n=a("2f62"),s={name:"credit-apply-cbiCurrentPaymentTerm",computed:Object(i["a"])({},Object(n["b"])(["applyForm","isApplyNotInProcess"]),{labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"},value:{get:function(){return this.applyForm.cbiPaymentTermOfYearN1}}}),watch:{value:function(e){/^cash in advance$/i.test(e)&&"CV_REQUEST"===this.applyForm.creditType&&this.isApplyNotInProcess&&this.applyForm.cbiFinancialStatementsAttId<=0&&this.$alert("现金客户要求最新财报 Financial statements is required when current payment term equals cash in advance","Prompt",{confirmButtonText:"Confirm"})}}},c=s,o=a("2877"),p=Object(o["a"])(c,r,l,!1,null,null,null);t["a"]=p.exports},d119:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form-item",{staticClass:"is-required",attrs:{label:"Current Credit limit 现有信用额度 : ","label-width":e.labelWidth}},[a("el-input",{attrs:{size:"small",disabled:"",placeholder:""},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},l=[],i=a("cebc"),n=a("2f62"),s=a("65e9"),c={name:"credit-apply-cbiCurrentCreditLimit",computed:Object(i["a"])({},Object(n["b"])(["applyForm"]),{value:{get:function(){return Object(s["d"])(this.applyForm.cbiCreditLimitOfYearN1)}},labelWidth:function(){return"TEMP_CREDIT_REQUEST"===this.applyForm.creditType?"320px":"280px"}})},o=c,p=a("2877"),m=Object(p["a"])(o,r,l,!1,null,null,null);t["a"]=m.exports}}]);
//# sourceMappingURL=chunk-24391f54.c746afe8.js.map