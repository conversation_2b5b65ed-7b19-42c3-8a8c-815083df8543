<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.master.dao.ProductMasterMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.master.model.ProductMaster">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="name_en" property="nameEn" jdbcType="VARCHAR"/>
		<result column="sale_price" property="salePrice" jdbcType="NUMERIC"/>
		<result column="pack" property="pack" jdbcType="NUMERIC"/>
		<result column="size" property="size" jdbcType="VARCHAR"/>
		<result column="pack_type" property="packType" jdbcType="VARCHAR"/>
		<result column="pack_type_text" property="packTypeText" jdbcType="VARCHAR"/>
		<result column="pack_code" property="packCode" jdbcType="VARCHAR"/>
		<result column="product_channel" property="productChannel" jdbcType="VARCHAR"/>
		<result column="product_channel_text" property="productChannelText" jdbcType="VARCHAR"/>
		<result column="grade_abc" property="gradeAbc" jdbcType="VARCHAR"/>
		<result column="grade_abc_text" property="gradeAbcText" jdbcType="VARCHAR"/>
		<result column="t1_category" property="t1Category" jdbcType="VARCHAR"/>
		<result column="t1_category_text" property="t1CategoryText" jdbcType="VARCHAR"/>
		<result column="t2_category" property="t2Category" jdbcType="VARCHAR"/>
		<result column="t2_category_text" property="t2CategoryText" jdbcType="VARCHAR"/>
		<result column="t3_category" property="t3Category" jdbcType="VARCHAR"/>
		<result column="t3_category_text" property="t3CategoryText" jdbcType="VARCHAR"/>
		<result column="digit_product_code" property="digitProductCode" jdbcType="VARCHAR"/>
		<result column="inner_brand" property="innerBrand" jdbcType="VARCHAR"/>
		<result column="inner_brand_text" property="innerBrandText" jdbcType="VARCHAR"/>
		<result column="product_sector" property="productSector" jdbcType="VARCHAR"/>
		<result column="product_sector_text" property="productSectorText" jdbcType="VARCHAR"/>
		<result column="hydraulic_ago_ci" property="hydraulicAgoCi" jdbcType="VARCHAR"/>
		<result column="hydraulic_ago_ci_text" property="hydraulicAgoCiText" jdbcType="VARCHAR"/>
		<result column="hyd_tra_ago_ind_com" property="hydTraAgoIndCom" jdbcType="VARCHAR"/>
		<result column="hyd_tra_ago_ind_com_text" property="hydTraAgoIndComText" jdbcType="VARCHAR"/>
		<result column="capacity" property="capacity" jdbcType="VARCHAR"/>
		<result column="viscosity" property="viscosity" jdbcType="VARCHAR"/>
		<result column="oil_type" property="oilType" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="creation_date" property="creationDate" jdbcType="TIMESTAMP"/>
		<result column="base_un" property="baseUn" jdbcType="NVARCHAR"/>
		<result column="forecast_leading_time" property="forecastLeadingTime" jdbcType="INTEGER"/>
		<result column="qbr_product_channel" property="qbrProductChannel" jdbcType="NVARCHAR"/>
		<result column="effect_time" property="effectTime" jdbcType="TIMESTAMP"/>
		<result column="is_competing" property="isCompeting" jdbcType="INTEGER"/>
		<result column="is_collect" property="isCollect" jdbcType="INTEGER"/>
		<result column="box_price" property="boxPrice" jdbcType="NUMERIC"/>
		<result column="box_capacity" property="boxCapacity" jdbcType="NUMERIC"/>
		<result column="product_property" property="productProperty" jdbcType="NVARCHAR"/>
		<result column="support_order" property="supportOrder" jdbcType="INTEGER"/>
		<result column="block_date" property="blockDate" jdbcType="TIMESTAMP"/>
		<result column="unblock_date" property="unblockDate" jdbcType="TIMESTAMP"/>
		<result column="master_status" property="masterStatus" jdbcType="INTEGER"/>
		<result column="bottle_qty" property="bottleQty" jdbcType="INTEGER"/>
		<result column="alias_name" property="aliasName" jdbcType="NVARCHAR"/>
		<result column="category" property="category" jdbcType="NVARCHAR"/>
    	<result column="icon_id" property="iconId" jdbcType="BIGINT"/>
		<result column="cdm_category" property="cdmCategory" jdbcType="VARCHAR"/>
		<result column="cdm_category_text" property="cdmCategoryText" jdbcType="VARCHAR"/>
		<result column="ci_category" property="ciCategory" jdbcType="VARCHAR"/>
		<result column="ci_category_text" property="ciCategoryText" jdbcType="VARCHAR"/>
		<result column="db2b_name_alias" property="db2bNameAlias" jdbcType="VARCHAR" />
        <result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
        <result column="support_sell_through" property="supportSellThrough" jdbcType="INTEGER"/>
		<result column="chevron_brand" property="chevronBrand" jdbcType="INTEGER"/>
		<result column="pack_units" property="packUnits" jdbcType="NVARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,sku,name,name_en,sale_price,pack,size,pack_type,pack_code,product_channel,grade_abc,t1_category,t2_category,
		t3_category,digit_product_code,inner_brand,product_sector,hydraulic_ago_ci,hyd_tra_ago_ind_com,can_verify,capacity,
		viscosity,oil_type,remark,status,create_time,update_time,creator, creation_date, base_un, forecast_leading_time, 
		qbr_product_channel, effect_time, is_competing, is_collect, box_price, box_capacity, product_property, support_order,
		block_date, unblock_date, master_status, bottle_qty, alias_name, category,cdm_category, ci_category ,db2b_name_alias, ext_flag , support_sell_through,
		chevron_brand
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.master.model.ProductMaster">
		update wx_t_product
		<set>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="name != null" >
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="nameEn != null" >
				name_en = #{nameEn,jdbcType=VARCHAR},
			</if>
			<if test="salePrice != null" >
				sale_price = #{salePrice,jdbcType=NUMERIC},
			</if>
			<if test="pack != null" >
				pack = #{pack,jdbcType=NUMERIC},
			</if>
			<if test="size != null" >
				size = #{size,jdbcType=VARCHAR},
			</if>
			<if test="packType != null" >
				pack_type = #{packType,jdbcType=VARCHAR},
			</if>
			<if test="packCode != null" >
				pack_code = #{packCode,jdbcType=VARCHAR},
			</if>
			<if test="productChannel != null" >
				product_channel = #{productChannel,jdbcType=VARCHAR},
			</if>
			<if test="gradeAbc != null" >
				grade_abc = #{gradeAbc,jdbcType=VARCHAR},
			</if>
			<if test="t1Category != null" >
				t1_category = #{t1Category,jdbcType=VARCHAR},
			</if>
			<if test="t2Category != null" >
				t2_category = #{t2Category,jdbcType=VARCHAR},
			</if>
			<if test="t3Category != null" >
				t3_category = #{t3Category,jdbcType=VARCHAR},
			</if>
			<if test="digitProductCode != null" >
				digit_product_code = #{digitProductCode,jdbcType=VARCHAR},
			</if>
			<if test="innerBrand != null" >
				inner_brand = #{innerBrand,jdbcType=VARCHAR},
			</if>
			<if test="productSector != null" >
				product_sector = #{productSector,jdbcType=VARCHAR},
			</if>
			<if test="hydraulicAgoCi != null" >
				hydraulic_ago_ci = #{hydraulicAgoCi,jdbcType=VARCHAR},
			</if>
			<if test="hydTraAgoIndCom != null" >
				hyd_tra_ago_ind_com = #{hydTraAgoIndCom,jdbcType=VARCHAR},
			</if>
			<if test="capacity != null" >
				capacity = #{capacity,jdbcType=VARCHAR},
			</if>
			<if test="viscosity != null" >
				viscosity = #{viscosity,jdbcType=VARCHAR},
			</if>
			<if test="oilType != null" >
				oil_type = #{oilType,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="creator != null" >
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="creationDate != null">
				creation_date = #{creationDate, jdbcType=TIMESTAMP},
			</if>
			<if test="baseUn != null and baseUn != '' ">
				base_un = #{baseUn, jdbcType=NVARCHAR},
			</if>
			<if test="forecastLeadingTime != null ">
				forecast_leading_time = #{forecastLeadingTime, jdbcType=INTEGER},
			</if>
			<if test="qbrProductChannel != null and qbrProductChannel != '' ">
				qbr_product_channel = #{qbrProductChannel, jdbcType=NVARCHAR},
			</if>
			<if test="effectTime != null ">
				effect_time = #{effectTime, jdbcType=TIMESTAMP},
			</if>
			<if test="isCompeting != null ">
				is_competing = #{isCompeting, jdbcType=INTEGER},
			</if>
			<if test="supportOrder != null ">
				support_order = #{supportOrder, jdbcType=INTEGER},
			</if>
			<if test="isCollect != null ">
				is_collect = #{isCollect, jdbcType=INTEGER},
			</if>
			<if test="boxPrice != null ">
				box_price = #{boxPrice, jdbcType=NUMERIC},
			</if>
			<if test="boxCapacity != null ">
				box_capacity = #{boxCapacity, jdbcType=NUMERIC},
			</if>
			<if test="productProperty != null and productProperty != '' ">
				product_property = #{productProperty, jdbcType=NVARCHAR},
			</if>
			<if test="blockDate != null ">
				block_date = #{blockDate, jdbcType=TIMESTAMP},
			</if>
			<if test="unblockDate != null ">
				unblock_date = #{unblockDate, jdbcType=TIMESTAMP},
			</if>
			<if test="masterStatus != null and masterStatus != '' ">
				master_status = #{masterStatus, jdbcType=NVARCHAR},
			</if>
			<if test="bottleQty != null ">
				bottle_qty = #{bottleQty, jdbcType=INTEGER},
			</if>
			<if test="aliasName != null and aliasName != '' ">
				alias_name = #{aliasName, jdbcType=NVARCHAR},
			</if>
			<if test="category != null and category != '' ">
				category = #{category, jdbcType=NVARCHAR},
			</if>
			<if test="cdmCategory != null" >
				cdm_category = #{cdmCategory,jdbcType=VARCHAR},
			</if>
			<if test="ciCategory != null" >
				ci_category = #{ciCategory,jdbcType=VARCHAR},
			</if>
			<if test="db2bNameAlias != null">
				db2b_name_alias = #{db2bNameAlias,jdbcType=VARCHAR},
			</if>
            <if test="extFlag != null">
                ext_flag = #{extFlag,jdbcType=INTEGER},
            </if>
            <if test="supportSellThrough != null">
                support_sell_through = #{supportSellThrough,jdbcType=INTEGER},
            </if>
			<if test="chevronBrand != null" >
				chevron_brand = #{chevronBrand,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.master.model.ProductMasterExample">
    	delete from wx_t_product
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.master.model.ProductMasterExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_product
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.master.model.ProductMaster" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_product
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="sku != null">
				sku,
			</if>
			<if test="name != null">
				name,
			</if>
			<if test="nameEn != null">
				name_en,
			</if>
			<if test="salePrice != null">
				sale_price,
			</if>
			<if test="pack != null">
				pack,
			</if>
			<if test="size != null">
				size,
			</if>
			<if test="packType != null">
				pack_type,
			</if>
			<if test="packCode != null">
				pack_code,
			</if>
			<if test="productChannel != null">
				product_channel,
			</if>
			<if test="gradeAbc != null">
				grade_abc,
			</if>
			<if test="t1Category != null">
				t1_category,
			</if>
			<if test="t2Category != null">
				t2_category,
			</if>
			<if test="t3Category != null">
				t3_category,
			</if>
			<if test="digitProductCode != null">
				digit_product_code,
			</if>
			<if test="innerBrand != null">
				inner_brand,
			</if>
			<if test="productSector != null">
				product_sector,
			</if>
			<if test="hydraulicAgoCi != null">
				hydraulic_ago_ci,
			</if>
			<if test="hydTraAgoIndCom != null">
				hyd_tra_ago_ind_com,
			</if>
			<if test="capacity != null">
				capacity,
			</if>
			<if test="viscosity != null">
				viscosity,
			</if>
			<if test="oilType != null">
				oil_type,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="creator != null">
				creator,
			</if>
			<if test="creationDate != null ">
				creation_date,
			</if>
			<if test="baseUn != null and baseUn != '' ">
				base_un,
			</if>
			<if test="forecastLeadingTime != null ">
				forecast_leading_time,
			</if>
			<if test="qbrProductChannel != null and qbrProductChannel != '' ">
				qbr_product_channel,
			</if>
			<if test="effectTime != null ">
				effect_time,
			</if>
			<if test="isCompeting != null ">
				is_competing,
			</if>
			<if test="isCollect != null ">
				is_collect,
			</if>
			<if test="boxPrice != null ">
				box_price,
			</if>
			<if test="boxCapacity != null ">
				box_capacity,
			</if>
			<if test="productProperty != null and productProperty != '' ">
				product_property,
			</if>
			<if test="supportOrder != null ">
				support_order,
			</if>
			<if test="blockDate != null ">
				block_date,
			</if>
			<if test="unblockDate != null ">
				unblock_date,
			</if>
			<if test="masterStatus != null and masterStatus != '' ">
				master_status,
			</if>
			<if test="bottleQty != null ">
				bottle_qty,
			</if>
			<if test="aliasName != null and aliasName != '' ">
				alias_name,
			</if>
			<if test="category != null and category != '' ">
				category,
			</if>
			<if test="cdmCategory != null">
				cdm_category,
			</if>
			<if test="ciCategory != null">
				ci_category,
			</if>
            <if test="extFlag != null">
                ext_flag,
            </if>
            <if test="supportSellThrough != null">
                support_sell_through,
            </if>
			<if test="chevronBrand != null">
				chevron_brand,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				#{name,jdbcType=VARCHAR},
			</if>
			<if test="nameEn != null">
				#{nameEn,jdbcType=VARCHAR},
			</if>
			<if test="salePrice != null">
				#{salePrice,jdbcType=NUMERIC},
			</if>
			<if test="pack != null">
				#{pack,jdbcType=NUMERIC},
			</if>
			<if test="size != null">
				#{size,jdbcType=VARCHAR},
			</if>
			<if test="packType != null">
				#{packType,jdbcType=VARCHAR},
			</if>
			<if test="packCode != null">
				#{packCode,jdbcType=VARCHAR},
			</if>
			<if test="productChannel != null">
				#{productChannel,jdbcType=VARCHAR},
			</if>
			<if test="gradeAbc != null">
				#{gradeAbc,jdbcType=VARCHAR},
			</if>
			<if test="t1Category != null">
				#{t1Category,jdbcType=VARCHAR},
			</if>
			<if test="t2Category != null">
				#{t2Category,jdbcType=VARCHAR},
			</if>
			<if test="t3Category != null">
				#{t3Category,jdbcType=VARCHAR},
			</if>
			<if test="digitProductCode != null">
				#{digitProductCode,jdbcType=VARCHAR},
			</if>
			<if test="innerBrand != null">
				#{innerBrand,jdbcType=VARCHAR},
			</if>
			<if test="productSector != null">
				#{productSector,jdbcType=VARCHAR},
			</if>
			<if test="hydraulicAgoCi != null">
				#{hydraulicAgoCi,jdbcType=VARCHAR},
			</if>
			<if test="hydTraAgoIndCom != null">
				#{hydTraAgoIndCom,jdbcType=VARCHAR},
			</if>
			<if test="capacity != null">
				#{capacity,jdbcType=VARCHAR},
			</if>
			<if test="viscosity != null">
				#{viscosity,jdbcType=VARCHAR},
			</if>
			<if test="oilType != null">
				#{oilType,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=VARCHAR},
			</if>
			<if test="creationDate != null ">
				#{creationDate, jdbcType=TIMESTAMP},
			</if>
			<if test="baseUn != null and baseUn != '' ">
				#{baseUn, jdbcType=NVARCHAR},
			</if>
			<if test="forecastLeadingTime != null ">
				#{forecastLeadingTime, jdbcType=INTEGER},
			</if>
			<if test="qbrProductChannel != null and qbrProductChannel != '' ">
				#{qbrProductChannel, jdbcType=NVARCHAR},
			</if>
			<if test="effectTime != null ">
				#{effectTime, jdbcType=TIMESTAMP},
			</if>
			<if test="isCompeting != null ">
				#{isCompeting, jdbcType=INTEGER},
			</if>
			<if test="isCollect != null ">
				#{isCollect, jdbcType=INTEGER},
			</if>
			<if test="boxPrice != null ">
				#{boxPrice, jdbcType=NUMERIC},
			</if>
			<if test="boxCapacity != null ">
				#{boxCapacity, jdbcType=NUMERIC},
			</if>
			<if test="productProperty != null and productProperty!= '' ">
				#{productProperty, jdbcType=NVARCHAR},
			</if>
			<if test="supportOrder != null ">
				#{supportOrder, jdbcType=INTEGER},
			</if>
			<if test="blockDate != null ">
				#{blockDate, jdbcType=INTEGER},
			</if>
			<if test="unblockDate != null ">
				#{unblockDate, jdbcType=INTEGER},
			</if>
			<if test="masterStatus != null and masterStatus != '' ">
				#{masterStatus, jdbcType=NVARCHAR},
			</if>
			<if test="bottleQty != null ">
				#{bottleQty, jdbcType=INTEGER},
			</if>
			<if test="aliasName != null and aliasName != '' ">
				#{aliasName, jdbcType=NVARCHAR},
			</if>
			<if test="category != null and category != '' ">
				#{category, jdbcType=NVARCHAR},
			</if>
			<if test="cdmCategory != null">
				#{cdmCategory,jdbcType=VARCHAR},
			</if>
			<if test="ciCategory != null">
				#{ciCategory,jdbcType=VARCHAR},
			</if>
            <if test="extFlag != null">
                #{extFlag,jdbcType=INTEGER},
            </if>
            <if test="supportSellThrough != null">
                #{supportSellThrough,jdbcType=INTEGER},
            </if>
			<if test="chevronBrand != null">
				#{chevronBrand,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_product
		<set>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.name != null">
				name = #{record.name,jdbcType=VARCHAR},
			</if>
			<if test="record.nameEn != null">
				name_en = #{record.nameEn,jdbcType=VARCHAR},
			</if>
			<if test="record.salePrice != null">
				sale_price = #{record.salePrice,jdbcType=NUMERIC},
			</if>
			<if test="record.pack != null">
				pack = #{record.pack,jdbcType=NUMERIC},
			</if>
			<if test="record.size != null">
				size = #{record.size,jdbcType=VARCHAR},
			</if>
			<if test="record.packType != null">
				pack_type = #{record.packType,jdbcType=VARCHAR},
			</if>
			<if test="record.packCode != null">
				pack_code = #{record.packCode,jdbcType=VARCHAR},
			</if>
			<if test="record.productChannel != null">
				product_channel = #{record.productChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.gradeAbc != null">
				grade_abc = #{record.gradeAbc,jdbcType=VARCHAR},
			</if>
			<if test="record.t1Category != null">
				t1_category = #{record.t1Category,jdbcType=VARCHAR},
			</if>
			<if test="record.t2Category != null">
				t2_category = #{record.t2Category,jdbcType=VARCHAR},
			</if>
			<if test="record.t3Category != null">
				t3_category = #{record.t3Category,jdbcType=VARCHAR},
			</if>
			<if test="record.digitProductCode != null">
				digit_product_code = #{record.digitProductCode,jdbcType=VARCHAR},
			</if>
			<if test="record.innerBrand != null">
				inner_brand = #{record.innerBrand,jdbcType=VARCHAR},
			</if>
			<if test="record.productSector != null">
				product_sector = #{record.productSector,jdbcType=VARCHAR},
			</if>
			<if test="record.hydraulicAgoCi != null">
				hydraulic_ago_ci = #{record.hydraulicAgoCi,jdbcType=VARCHAR},
			</if>
			<if test="record.hydTraAgoIndCom != null">
				hyd_tra_ago_ind_com = #{record.hydTraAgoIndCom,jdbcType=VARCHAR},
			</if>
			<if test="record.capacity != null">
				capacity = #{record.capacity,jdbcType=VARCHAR},
			</if>
			<if test="record.viscosity != null">
				viscosity = #{record.viscosity,jdbcType=VARCHAR},
			</if>
			<if test="record.oilType != null">
				oil_type = #{record.oilType,jdbcType=VARCHAR},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.creator != null">
				creator = #{record.creator,jdbcType=VARCHAR},
			</if>
			<if test="record.cdmCategory != null">
				cdm_category = #{record.cdmCategory,jdbcType=VARCHAR},
			</if>
			<if test="record.ciCategory != null">
				ci_category = #{record.ciCategory,jdbcType=VARCHAR},
			</if>
			<if test="record.db2bNameAlias != null">
				db2b_name_alias = #{record.db2bNameAlias,jdbcType=VARCHAR},
			</if>
            <if test="record.extFlag != null">
                ext_flag = #{record.extFlag,jdbcType=INTEGER},
            </if>
            <if test="record.supportSellThrough != null">
                support_sell_through = #{record.supportSellThrough,jdbcType=INTEGER}
            </if>
			<if test="record.chevronBrand != null">
				chevron_brand = #{record.chevronBrand,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.master.model.ProductMasterExample">
		delete from wx_t_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.master.model.ProductMasterExample">
		select count(1) from wx_t_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.master.model.ProductMasterExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select 
		<if test="limit != null">
		top ${limit}
		</if>
		t1.id, t1.sku, t1.name, t1.name_en, t1.sale_price, t1.pack, t1.size, t1.pack_type, t1.pack_code,t1.forecast_leading_time,
			 t1.grade_abc, t1.t1_category, t1.t2_category, t1.t3_category, t1.digit_product_code,
			 t1.inner_brand, t1.product_sector, t1.hydraulic_ago_ci, t1.hyd_tra_ago_ind_com, t1.can_verify, t1.capacity,
			 t1.viscosity, t1.oil_type, t1.remark, t1.status, t1.create_time, t1.update_time, t1.creator,creation_date,t1.cdm_category, t1.ci_category, t1.ext_flag, t1.support_sell_through,
			 t1.chevron_brand,t1.box_capacity,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.packType' and di1.dic_item_code=t1.pack_type) pack_type_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.salesChannel' and di1.dic_item_code=t1.product_channel) product_channel_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.gradeAbc' and di1.dic_item_code=t1.grade_abc) grade_abc_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t1Category' and di1.dic_item_code=t1.t1_category) t1_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t2Category' and di1.dic_item_code=t1.t2_category) t2_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t3Category' and di1.dic_item_code=t1.t3_category) t3_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.innerBrand' and di1.dic_item_code=t1.inner_brand) inner_brand_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.sector' and di1.dic_item_code=t1.product_sector) product_sector_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.hydraulicAgoCi' and di1.dic_item_code=t1.hydraulic_ago_ci) hydraulic_ago_ci_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.hydTraAgoIndCom' and di1.dic_item_code=t1.hyd_tra_ago_ind_com) hyd_tra_ago_ind_com_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ProductMaster.cdmCategory' and di1.dic_item_code=t1.cdm_category) cdm_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ProductMaster.ciCategory' and di1.dic_item_code=t1.ci_category) ci_category_text
			 ,(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = t1.ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id
			 ,di.dic_item_name pack_units
		  from wx_t_product t1
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=t1.sku
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=isnull(bp.[pack_type],isnull((select vtm.value_after_transform from wx_t_value_transform_map vtm where vtm.transform_type='Sku2BiPackType' and vtm.value_before_transform=t1.sku), 'Small Pack'))
		 where 1=1
		<if test="includeDisabled == null or includeDisabled != 1">
		and t1.status=1
		</if>
		<if test="sku != null and sku != ''">
			and t1.sku like '%' + #{sku, jdbcType=VARCHAR} + '%'
		</if>
		<if test="skus != null">
			and t1.sku in 
	    	<foreach item="item" index="index" collection="skus" open="(" separator="," close=")">  
			 #{item}  
			</foreach> 
		</if>
		<if test="name != null and name != ''">
			and t1.name like '%' + #{name, jdbcType=VARCHAR} + '%'
		</if>
		<if test="productChannel != null and productChannel != ''">
			and t1.product_channel = #{productChannel, jdbcType=VARCHAR}
		</if>
		<if test="gradeAbc != null and gradeAbc != ''">
			and t1.grade_abc = #{gradeAbc, jdbcType=VARCHAR}
		</if>
		<if test="t1Category != null and t1Category != ''">
			and t1.t1_category = #{t1Category, jdbcType=VARCHAR}
		</if>
		<if test="t2Category != null and t2Category != ''">
			and t1.t2_category = #{t2Category, jdbcType=VARCHAR}
		</if>
		<if test="t3Category != null and t3Category != ''">
			and t1.t3_category = #{t3Category, jdbcType=VARCHAR}
		</if>
		<if test="innerBrand != null and innerBrand != ''">
			and t1.inner_brand = #{innerBrand, jdbcType=VARCHAR}
		</if>
		<if test="keyWord != null and keyWord != ''">
			and (t1.sku like '%' + #{keyWord} + '%' or t1.name like '%' + #{keyWord} + '%' or t1.name_en like '%' + #{keyWord} + '%' or left(dbo.f_GetPyToAboutHanyu(name),500) LIKE '%' + #{keyWord} + '%')
		</if>
		<if test="_orderBy != null">
		order by ${_orderBy}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.master.model.ProductMasterParams">
		select t1.id, t1.sku, t1.name, t1.name_en, t1.sale_price, t1.pack, t1.size, t1.pack_type, t1.pack_code,
			 t1.grade_abc, t1.t1_category, t1.t2_category, t1.t3_category, t1.digit_product_code,
			 t1.inner_brand, t1.product_sector, t1.hydraulic_ago_ci, t1.hyd_tra_ago_ind_com, t1.can_verify, t1.capacity,
			 t1.viscosity, t1.oil_type, t1.remark, t1.status, t1.create_time, t1.update_time, t1.creator, t1.cdm_category, t1.ci_category, t1.ext_flag, t1.support_sell_through,
			 t1.chevron_brand,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.packType' and di1.dic_item_code=t1.pack_type) pack_type_text
			,
			(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='product.salesChannel' and di1.dic_item_code=t1.product_channel) product_channel_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.gradeAbc' and di1.dic_item_code=t1.grade_abc) grade_abc_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t1Category' and di1.dic_item_code=t1.t1_category) t1_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t2Category' and di1.dic_item_code=t1.t2_category) t2_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.t3Category' and di1.dic_item_code=t1.t3_category) t3_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.innerBrand' and di1.dic_item_code=t1.inner_brand) inner_brand_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.sector' and di1.dic_item_code=t1.product_sector) product_sector_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.hydraulicAgoCi' and di1.dic_item_code=t1.hydraulic_ago_ci) hydraulic_ago_ci_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.hydTraAgoIndCom' and di1.dic_item_code=t1.hyd_tra_ago_ind_com) hyd_tra_ago_ind_com_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ProductMaster.cdmCategory' and di1.dic_item_code=t1.cdm_category) cdm_category_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='ProductMaster.ciCategory' and di1.dic_item_code=t1.ci_category) ci_category_text
		  from wx_t_product t1
		 where t1.status=1
		<if test="sku != null and sku != ''">
			and t1.sku like '%' + #{sku, jdbcType=VARCHAR} + '%'
		</if>
		<if test="name != null and name != ''">
			and t1.name like '%' + #{name, jdbcType=VARCHAR} + '%'
		</if>
		<if test="productChannel != null and productChannel != ''">
			and t1.product_channel = #{productChannel, jdbcType=VARCHAR}
		</if>
		<if test="gradeAbc != null and gradeAbc != ''">
			and t1.grade_abc = #{gradeAbc, jdbcType=VARCHAR}
		</if>
		<if test="t1Category != null and t1Category != ''">
			and t1.t1_category = #{t1Category, jdbcType=VARCHAR}
		</if>
		<if test="t2Category != null and t2Category != ''">
			and t1.t2_category = #{t2Category, jdbcType=VARCHAR}
		</if>
		<if test="t3Category != null and t3Category != ''">
			and t1.t3_category = #{t3Category, jdbcType=VARCHAR}
		</if>
		<if test="innerBrand != null and innerBrand != ''">
			and t1.inner_brand = #{innerBrand, jdbcType=VARCHAR}
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.sku like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
		<if test="isCompeting != null">
			and t1.is_competing = #{isCompeting, jdbcType=INTEGER}
		</if>
	</select>
	
	<select id="selectBySku" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"/>
		,(select top 1 af.ATT_ID from WX_ATT_FILE af where af.SOURCE_ID = p.ID and af.SOURCE_TYPE = '5' order by af.create_time asc) as icon_id
		,(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Product.innerBrand' and di1.dic_item_code=p.inner_brand) inner_brand_text
		from wx_t_product p
		where sku = #{sku,jdbcType=BIGINT}
	</select>

	<select id="selectBySkuV2" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
			p.id,p.sku,p.name,p.sale_price,p.pack,pp.product_channel,p.capacity,
			p.viscosity, p.ext_flag , p.support_sell_through,p.pack,p.chevron_brand
		from wx_t_product p
		left join [PP_MID].[DBO].syn_dw_to_pp_product pp on p.sku = pp.product_code_SAP
		where sku = #{sku,jdbcType=NVARCHAR}
	</select>
	
	<select id="selectAllProduct" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"/>
		from wx_t_product
		where status = 1
	</select>
	
	<select id="selectByName" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"/>
		from wx_t_product
		where status = 1
		and name like '%' + #{name, jdbcType=VARCHAR} + '%'
		and product_channel = #{productChannel,jdbcType=NVARCHAR}
	</select>
	
	<select id="selectByNameForOilApply" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
			a.id,a.sku,a.name,a.name_en,a.sale_price,a.pack,a.size,a.pack_type,a.pack_code,a.product_channel,a.grade_abc,a.t1_category,a.t2_category,
		a.t3_category,a.digit_product_code,a.inner_brand,a.product_sector,a.hydraulic_ago_ci,a.hyd_tra_ago_ind_com,a.can_verify,a.capacity,
		a.viscosity,a.oil_type,a.remark,a.status,a.create_time,a.update_time,a.creator, a.creation_date, a.base_un, a.forecast_leading_time, 
		a.qbr_product_channel, a.effect_time, a.is_competing, a.is_collect, a.box_price, a.box_capacity, a.product_property, a.support_order,
		a.block_date, a.unblock_date, a.master_status, a.bottle_qty,a.alias_name, a.category,a.cdm_category, a.ci_category,a.ext_flag, a.support_sell_through
		FROM(
		SELECT
			*
		FROM
			wx_t_product 
		WHERE
			status = 1 
			AND (name LIKE '%'+ '雪佛龙' +'%'+ '齿轮油' + '%' or name LIKE '%'+ '德乐' + '%')
			AND product_channel = #{productChannel,jdbcType=NVARCHAR}) a
		where name like '%' + #{name, jdbcType=VARCHAR} + '%'
	</select>

	<select id="selectByNameForOilApplyNew" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
			a.id,a.sku,a.name,a.name_en,a.sale_price,a.pack,a.size,a.pack_type,a.pack_code,a.product_channel,a.grade_abc,a.t1_category,a.t2_category,
		a.t3_category,a.digit_product_code,a.inner_brand,a.product_sector,a.hydraulic_ago_ci,a.hyd_tra_ago_ind_com,a.can_verify,a.capacity,
		a.viscosity,a.oil_type,a.remark,a.status,a.create_time,a.update_time,a.creator, a.creation_date, a.base_un, a.forecast_leading_time,
		a.qbr_product_channel, a.effect_time, a.is_competing, a.is_collect, a.box_price, a.box_capacity, a.product_property, a.support_order,
		a.block_date, a.unblock_date, a.master_status, a.bottle_qty,a.alias_name, a.category,a.cdm_category, a.ci_category,a.ext_flag, a.support_sell_through,a.chevron_brand
		FROM(
		SELECT
			*
		FROM
			wx_t_product
		WHERE
			status = 1
-- 			AND (name LIKE '%'+ '雪佛龙' +'%'+ '齿轮油' + '%' or name LIKE '%'+ '德乐' + '%')
			AND chevron_brand = #{brand,jdbcType=INTEGER}) a
		where name like '%' + #{name, jdbcType=VARCHAR} + '%'
	</select>
</mapper>