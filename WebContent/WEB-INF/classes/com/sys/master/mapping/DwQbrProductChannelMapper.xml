<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.master.dao.DwQbrProductChannelMapper">
	<resultMap id="BaseResultMap" type="com.sys.master.model.DwQbrProductChannel">
		<id column="kn_qbr_product_channel_id" property="knQbrProductChannelId" jdbcType="INTEGER"/>
		<id column="qbr_product_channel_name" property="qbrProductChannelName" jdbcType="NVARCHAR"/>
	</resultMap>
	
	<sql id="Base_Column_List">
		kn_qbr_product_channel_id, qbr_product_channel_name
	</sql>
	
	<select id="selectAll" resultMap="BaseResultMap" >
    	select
		<include refid="Base_Column_List"/>
		from dw_qbr_product_channel
	</select>
</mapper>