<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTUserMapper" >
<resultMap id="BaseResultMap" type="com.sys.auth.model.WxTUser" >
	<!--
	WARNING - @mbggenerated
	This element is automatically generated by MyBatis Generator, do not modify.
	This element was generated on Wed Jun 03 10:58:07 CST 2015.
	-->
	<id column="user_id" property="userId" jdbcType="BIGINT" />
	<result column="login_name" property="loginName" jdbcType="VARCHAR" />
	<result column="user_no" property="userNo" jdbcType="VARCHAR" />
	<result column="password" property="password" jdbcType="VARCHAR" />
	<result column="salt" property="salt" jdbcType="VARCHAR" />
	<result column="pwd_lasttime" property="pwdLasttime" jdbcType="TIMESTAMP" />
	<result column="ch_name" property="chName" jdbcType="VARCHAR" />
	<result column="cn_name" property="cnName" jdbcType="VARCHAR" />
	<result column="pinyin" property="pinyin" jdbcType="VARCHAR" />
	<result column="allow_login" property="allowLogin" jdbcType="VARCHAR" />
	<result column="sex" property="sex" jdbcType="VARCHAR" />
	<result column="birthday" property="birthday" jdbcType="TIMESTAMP" />
	<result column="address" property="address" jdbcType="VARCHAR" />
	<result column="email" property="email" jdbcType="VARCHAR" />
	<result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
	<result column="fixed_tel" property="fixedTel" jdbcType="VARCHAR" />
	<result column="photo_id" property="photoId" jdbcType="BIGINT" />
	<result column="org_id" property="orgId" jdbcType="BIGINT" />
	<result column="org_type" property="orgType" jdbcType="INTEGER" />
	<result column="org_name"   property="orgName" jdbcType="VARCHAR" />
	<result column="postion"   property="postion" jdbcType="VARCHAR"  />
	<result column="postion_name"   property="postionName" jdbcType="VARCHAR"  />
	<result column="branch_name"   property="branchName" />
	<result column="branch_id"   property="branchId" />
	<result column="user_intime" property="userIntime" jdbcType="TIMESTAMP" />
	<result column="user_outtime" property="userOuttime" jdbcType="TIMESTAMP" />
	<result column="description" property="description" jdbcType="VARCHAR" />
	<result column="device_id" property="deviceId" jdbcType="VARCHAR" />
	<result column="device_type" property="deviceType" jdbcType="VARCHAR" />
	<result column="receive_msg" property="receiveMsg" jdbcType="INTEGER" />
	<result column="post" property="post" jdbcType="BIGINT" />
	<result column="is_valid" property="isValid" jdbcType="VARCHAR" />
	<result column="xz_time" property="xzTime" jdbcType="TIMESTAMP" />
	<result column="xz_user" property="xzUser" jdbcType="VARCHAR" />
	<result column="xg_time" property="xgTime" jdbcType="TIMESTAMP" />
	<result column="xg_user" property="xgUser" jdbcType="VARCHAR" />
	<result column="status" property="status" jdbcType="INTEGER" />
	<result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>

	<result column="ch_role_name"   property="chRoleName" />
	<result column="type" property="type" jdbcType="VARCHAR" />
	<result column="change_pwd_permission" property="changePwdPermission" jdbcType="INTEGER" />
	<result column="reset_flag" property="resetFlag" jdbcType="INTEGER" />
	<result column="cai" property="cai" jdbcType="VARCHAR"/>
	<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
	<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
	<result column="region_name_cn" property="regionNameCn" jdbcType="VARCHAR"/>
	<result column="bu" property="bu" jdbcType="VARCHAR"/>
	<result column="user_model" property="userModel" jdbcType="VARCHAR"/>
	<result column="default_locale" property="defaultLocale" jdbcType="VARCHAR"/>
	<result column="partner_flag" property="partnerFlag" jdbcType="VARCHAR"/>
	<result column="distributor_id" property="distributorId" jdbcType="BIGINT" />
		<result column="user_flag" property="userFlag" jdbcType="INTEGER"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="system_integration_user" property="systemIntegrationUser" jdbcType="INTEGER"/>
</resultMap>
<resultMap id="UserSimpleMap" type="com.sys.auth.model.WxSimpleUser" >
	<id column="user_id" property="userId" jdbcType="BIGINT" />
	<result column="login_name" property="loginName" jdbcType="VARCHAR" />
	<result column="ch_name" property="chName" jdbcType="VARCHAR" />
	<result column="user_name" property="userName" jdbcType="VARCHAR" />
	<result column="pinyin" property="pinyin" jdbcType="VARCHAR" />
	<result column="sex" property="sex" jdbcType="VARCHAR" />
	<result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
	<result column="photo_id" property="photoId" jdbcType="BIGINT" />
	<result column="org_id" property="orgId" jdbcType="BIGINT" />
	<result column="org_name"   property="orgName" />
	<result column="postion"   property="postion" />
	<result column="postion_name"   property="postionName" />
</resultMap>
<sql id="Example_Where_Clause" >
	<where>
	<foreach collection="oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause" >
	<where >
	<foreach collection="example.oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List" >
	user_id, login_name, user_no, password, salt, pwd_lasttime, ch_name, cn_name, pinyin,
	allow_login, sex, birthday, address, email, mobile_tel, fixed_tel, org_id,postion,postion_name, user_intime,
	user_outtime, description,device_id,device_type,receive_msg, is_valid, xz_time, xz_user, xg_time, xg_user, status,
	tenant_id,post, type, cai,region_name,ext_flag
</sql>
<sql id="simple_Column_List" >
	user_id, login_name, ch_name, pinyin,
	sex, mobile_tel, org_id,postion,postion_name
</sql>
<sql id="Key_Column_List" >
	user_id, login_name, user_no,  pwd_lasttime, ch_name, cn_name, pinyin,
	allow_login, sex, birthday, address, email, mobile_tel, fixed_tel, org_id,postion,postion_name, user_intime,
	user_outtime, description,device_id,device_type,receive_msg,tenant_id,post, type
</sql>
<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTUserExample" >
	select
	<if test="distinct" >
	distinct
	</if>
	<include refid="Base_Column_List" />
	from wx_t_user
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null" >
	order by ${orderByClause}
	</if>
</select>
<!--查询通讯录信息 增加返回职位 和所属组织id 名称 -->
<select id="selectAddressList" parameterType="com.sys.auth.model.WxTUserExample" resultMap="UserSimpleMap">
	select
	<if test="distinct">
	distinct
	</if>
	<include refid="simple_Column_List" /> ,
	(select o.organization_name org_name from wx_t_organization o  where o.id= u.org_id )  as  org_name,
	(select f.att_id from wx_att_file f where f.source_type='2' and f.source_id =u.user_id ) as photo_id
	from wx_t_user u
	where u.org_id in(
		(
		select DISTINCT role_org.org_id  from(
			select  source_id  as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{curUserId,jdbcType=BIGINT}

			) and rs_type = 1
				union
			select source_id as org_id from wx_t_userbase where rs_type = 1
			and user_id = #{curUserId,jdbcType=BIGINT}
				union
			select org_id  from wx_t_user u where u.user_id =#{curUserId,jdbcType=BIGINT}
			)as role_org
	)
	)
	<if test="orderByClause != null">
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
	select u.*,
	case when u.type='1' then (select w.work_shop_name from wx_t_work_shop w where w.id=u.org_id) else o.organization_name end  as  org_name,
	(select f.att_id from wx_att_file f where f.source_type='2' and f.source_id =u.user_id ) as photo_id,
	case when u.type='1' then 2 else o.type end org_type
	from wx_t_user u
	left join wx_t_organization o on o.id = u.org_id
	where user_id = #{userId,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
	delete from wx_t_user
	where user_id = #{userId,jdbcType=BIGINT}
</delete>
<delete id="deleteByExample" parameterType="com.sys.auth.model.WxTUserExample" >
	delete from wx_t_user
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.sys.auth.model.WxTUser" >
	insert into wx_t_user (login_name, user_no,
	password, salt, pwd_lasttime,
	ch_name, cn_name, pinyin,
	allow_login, sex, birthday,
	address, email, mobile_tel,
	fixed_tel, org_id,postion, user_intime,
	user_outtime, description,device_id, is_valid,
	xz_time, xz_user, xg_time,
	xg_user, status, tenant_id,postion_name, type
	)
	values (#{loginName,jdbcType=VARCHAR}, #{userNo,jdbcType=VARCHAR},
	#{password,jdbcType=VARCHAR}, #{salt,jdbcType=VARCHAR}, #{pwdLasttime,jdbcType=TIMESTAMP},
	#{chName,jdbcType=VARCHAR}, #{cnName,jdbcType=VARCHAR}, #{pinyin,jdbcType=VARCHAR},
	#{allowLogin,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{birthday,jdbcType=TIMESTAMP},
	#{address,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{mobileTel,jdbcType=VARCHAR},
	#{fixedTel,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT},#{postion,jdbcType=VARCHAR}, #{userIntime,jdbcType=TIMESTAMP},
	#{userOuttime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR},#{deviceId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
	getdate(), #{xzUser,jdbcType=VARCHAR}, getdate(),
	#{xgUser,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT},#{postionName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}
	)
</insert>
<insert id="insertSelective" useGeneratedKeys="true" keyProperty="userId" parameterType="com.sys.auth.model.WxTUser" >
	insert into wx_t_user
	<trim prefix="(" suffix=")" suffixOverrides="," >
	<if test="userId != null" >
		user_id,
	</if>
	<if test="loginName != null" >
		login_name,
	</if>
	<if test="userNo != null" >
		user_no,
	</if>
	<if test="password != null" >
		password,
	</if>
	<if test="salt != null" >
		salt,
	</if>
	<if test="pwdLasttime != null" >
		pwd_lasttime,
	</if>
	<if test="chName != null" >
		ch_name,
	</if>
	<if test="cnName != null" >
		cn_name,
	</if>
	<if test="pinyin != null" >
		pinyin,
	</if>
	<if test="allowLogin != null" >
		allow_login,
	</if>
	<if test="sex != null" >
		sex,
	</if>
	<if test="birthday != null" >
		birthday,
	</if>
	<if test="address != null" >
		address,
	</if>
	<if test="email != null" >
		email,
	</if>
	<if test="mobileTel != null" >
		mobile_tel,
	</if>
	<if test="fixedTel != null" >
		fixed_tel,
	</if>
	<if test="orgId != null" >
		org_id,
	</if>
	<if test="postion != null" >
		postion,
	</if>
	<if test="userIntime != null" >
		user_intime,
	</if>
	<if test="userOuttime != null" >
		user_outtime,
	</if>
	<if test="description != null" >
		description,
	</if>
	<if test="deviceId != null" >
		device_id,
	</if>
	<if test="isValid != null" >
		is_valid,
	</if>
	<if test="xzTime != null" >
		xz_time,
	</if>
	<if test="xzUser != null" >
		xz_user,
	</if>
	<if test="xgTime != null" >
		xg_time,
	</if>
	<if test="xgUser != null" >
		xg_user,
	</if>
	<if test="status != null" >
		status,
	</if>
	<if test="tenantId != null" >
		tenant_id,
	</if>
	<if test="type != null" >
		type,
	</if>
			<if test="cai != null">
				cai,
			</if>
			<if test="regionName != null">
				region_name,
			</if>
			<if test="resetFlag != null">
				reset_flag,
			</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides="," >
	<if test="userId != null" >
		#{userId,jdbcType=BIGINT},
	</if>
	<if test="loginName != null" >
		#{loginName,jdbcType=VARCHAR},
	</if>
	<if test="userNo != null" >
		#{userNo,jdbcType=VARCHAR},
	</if>
	<if test="password != null" >
		#{password,jdbcType=VARCHAR},
	</if>
	<if test="salt != null" >
		#{salt,jdbcType=VARCHAR},
	</if>
	<if test="pwdLasttime != null" >
		#{pwdLasttime,jdbcType=TIMESTAMP},
	</if>
	<if test="chName != null" >
		#{chName,jdbcType=VARCHAR},
	</if>
	<if test="cnName != null" >
		#{cnName,jdbcType=VARCHAR},
	</if>
	<if test="pinyin != null" >
		#{pinyin,jdbcType=VARCHAR},
	</if>
	<if test="allowLogin != null" >
		#{allowLogin,jdbcType=VARCHAR},
	</if>
	<if test="sex != null" >
		#{sex,jdbcType=VARCHAR},
	</if>
	<if test="birthday != null" >
		#{birthday,jdbcType=TIMESTAMP},
	</if>
	<if test="address != null" >
		#{address,jdbcType=VARCHAR},
	</if>
	<if test="email != null" >
		#{email,jdbcType=VARCHAR},
	</if>
	<if test="mobileTel != null" >
		#{mobileTel,jdbcType=VARCHAR},
	</if>
	<if test="fixedTel != null" >
		#{fixedTel,jdbcType=VARCHAR},
	</if>
	<if test="orgId != null" >
		#{orgId,jdbcType=BIGINT},
	</if>
	<if test="postion != null" >
		#{postion,jdbcType=INTEGER},
	</if>
	<if test="userIntime != null" >
		#{userIntime,jdbcType=TIMESTAMP},
	</if>
	<if test="userOuttime != null" >
		#{userOuttime,jdbcType=TIMESTAMP},
	</if>
	<if test="description != null" >
		#{description,jdbcType=VARCHAR},
	</if>
	<if test="deviceId != null" >
		#{device_id,jdbcType=VARCHAR},
	</if>
	<if test="isValid != null" >
		#{isValid,jdbcType=VARCHAR},
	</if>
	<if test="xzTime != null" >
		#{xzTime,jdbcType=TIMESTAMP},
	</if>
	<if test="xzUser != null" >
		#{xzUser,jdbcType=VARCHAR},
	</if>
	<if test="xgTime != null" >
		#{xgTime,jdbcType=TIMESTAMP},
	</if>
	<if test="xgUser != null" >
		#{xgUser,jdbcType=VARCHAR},
	</if>
	<if test="status != null" >
		#{status,jdbcType=INTEGER},
	</if>
	<if test="tenantId != null" >
		#{tenantId,jdbcType=BIGINT},
	</if>
	<if test="type != null" >
		#{type,jdbcType=VARCHAR},
	</if>
			<if test="cai != null">
				#{cai,jdbcType=VARCHAR},
			</if>
			<if test="regionName != null">
				#{regionName,jdbcType=VARCHAR},
			</if>
			<if test="resetFlag != null">
				#{resetFlag,jdbcType=INTEGER},
			</if>
	</trim>
</insert>
<select id="countByExample" parameterType="com.sys.auth.model.WxTUserExample" resultType="java.lang.Integer" >
	select count(*) from wx_t_user
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
</select>
<update id="updateByExampleSelective" parameterType="map" >
	update wx_t_user
	<set >
	<if test="record.userId != null" >
		user_id = #{record.userId,jdbcType=BIGINT},
	</if>
	<if test="record.loginName != null" >
		login_name = #{record.loginName,jdbcType=VARCHAR},
	</if>
	<if test="record.userNo != null" >
		user_no = #{record.userNo,jdbcType=VARCHAR},
	</if>
	<if test="record.password != null" >
		password = #{record.password,jdbcType=VARCHAR},
	</if>
	<if test="record.salt != null" >
		salt = #{record.salt,jdbcType=VARCHAR},
	</if>
	<if test="record.pwdLasttime != null" >
		pwd_lasttime = #{record.pwdLasttime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.chName != null" >
		ch_name = #{record.chName,jdbcType=VARCHAR},
	</if>
	<if test="record.cnName != null" >
		cn_name = #{record.cnName,jdbcType=VARCHAR},
	</if>
	<if test="record.pinyin != null" >
		pinyin = #{record.pinyin,jdbcType=VARCHAR},
	</if>
	<if test="record.allowLogin != null" >
		allow_login = #{record.allowLogin,jdbcType=VARCHAR},
	</if>
	<if test="record.sex != null" >
		sex = #{record.sex,jdbcType=VARCHAR},
	</if>
	<if test="record.birthday != null" >
		birthday = #{record.birthday,jdbcType=TIMESTAMP},
	</if>
	<if test="record.address != null" >
		address = #{record.address,jdbcType=VARCHAR},
	</if>
	<if test="record.email != null" >
		email = #{record.email,jdbcType=VARCHAR},
	</if>
	<if test="record.mobileTel != null" >
		mobile_tel = #{record.mobileTel,jdbcType=VARCHAR},
	</if>
	<if test="record.fixedTel != null" >
		fixed_tel = #{record.fixedTel,jdbcType=VARCHAR},
	</if>
	<if test="record.orgId != null" >
		org_id = #{record.orgId,jdbcType=BIGINT},
	</if>
	<if test="record.postion != null" >
		postion = #{record.postion,jdbcType=BIGINT},
	</if>
	<if test="record.userIntime != null" >
		user_intime = #{record.userIntime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.userOuttime != null" >
		user_outtime = #{record.userOuttime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.description != null" >
		description = #{record.description,jdbcType=VARCHAR},
	</if>
	<if test="record.deviceId != null" >
		device_id = #{record.deviceId,jdbcType=VARCHAR},
	</if>
	<if test="record.deviceType != null" >
		device_type = #{record.deviceType,jdbcType=VARCHAR},
	</if>
	<if test="record.receiveMsg != null" >
		receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
	</if>
	<if test="record.isValid != null" >
		is_valid = #{record.isValid,jdbcType=VARCHAR},
	</if>
	<if test="record.xzTime != null" >
		xz_time = #{record.xzTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.xzUser != null" >
		xz_user = #{record.xzUser,jdbcType=VARCHAR},
	</if>
	<if test="record.xgTime != null" >
		xg_time = #{record.xgTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.xgUser != null" >
		xg_user = #{record.xgUser,jdbcType=VARCHAR},
	</if>
	<if test="record.status != null" >
		status = #{record.status,jdbcType=INTEGER},
	</if>
	<if test="record.tenantId != null" >
		tenant_id = #{record.tenantId,jdbcType=BIGINT},
	</if>
	<if test="record.type != null" >
		type = #{record.type,jdbcType=VARCHAR},
	</if>
			<if test="record.cai != null">
				cai = #{record.cai,jdbcType=VARCHAR},
			</if>
			<if test="record.regionName != null">
				region_name = #{record.regionName,jdbcType=VARCHAR},
			</if>
	</set>
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map" >
	update wx_t_user
	set user_id = #{record.userId,jdbcType=BIGINT},
	login_name = #{record.loginName,jdbcType=VARCHAR},
	user_no = #{record.userNo,jdbcType=VARCHAR},
	password = #{record.password,jdbcType=VARCHAR},
	salt = #{record.salt,jdbcType=VARCHAR},
	pwd_lasttime = #{record.pwdLasttime,jdbcType=TIMESTAMP},
	ch_name = #{record.chName,jdbcType=VARCHAR},
	cn_name = #{record.cnName,jdbcType=VARCHAR},
	pinyin = #{record.pinyin,jdbcType=VARCHAR},
	allow_login = #{record.allowLogin,jdbcType=VARCHAR},
	sex = #{record.sex,jdbcType=VARCHAR},
	birthday = #{record.birthday,jdbcType=TIMESTAMP},
	address = #{record.address,jdbcType=VARCHAR},
	email = #{record.email,jdbcType=VARCHAR},
	mobile_tel = #{record.mobileTel,jdbcType=VARCHAR},
	fixed_tel = #{record.fixedTel,jdbcType=VARCHAR},
	org_id = #{record.orgId,jdbcType=BIGINT},
	postion = #{record.postion,jdbcType=VARCHAR},
	user_intime = #{record.userIntime,jdbcType=TIMESTAMP},
	user_outtime = #{record.userOuttime,jdbcType=TIMESTAMP},
	description = #{record.description,jdbcType=VARCHAR},
	device_id = #{record.deviceId,jdbcType=VARCHAR},
	is_valid = #{record.isValid,jdbcType=VARCHAR},
	xz_time = #{record.xzTime,jdbcType=TIMESTAMP},
	xz_user = #{record.xzUser,jdbcType=VARCHAR},
	xg_time = #{record.xgTime,jdbcType=TIMESTAMP},
	xg_user = #{record.xgUser,jdbcType=VARCHAR},
	status = #{record.status,jdbcType=INTEGER},
	tenant_id = #{record.tenantId,jdbcType=BIGINT}
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.sys.auth.model.WxTUser" >
	update wx_t_user
	<set >
	<if test="loginName != null" >
		login_name = #{loginName,jdbcType=VARCHAR},
	</if>
	<if test="userNo != null" >
		user_no = #{userNo,jdbcType=VARCHAR},
	</if>
	<if test="password != null" >
		password = #{password,jdbcType=VARCHAR},
	</if>
	<if test="salt != null" >
		salt = #{salt,jdbcType=VARCHAR},
	</if>
	<if test="pwdLasttime != null" >
		pwd_lasttime = #{pwdLasttime,jdbcType=TIMESTAMP},
	</if>
	<if test="chName != null" >
		ch_name = #{chName,jdbcType=VARCHAR},
	</if>
	<if test="cnName != null" >
		cn_name = #{cnName,jdbcType=VARCHAR},
	</if>
	<if test="pinyin != null" >
		pinyin = #{pinyin,jdbcType=VARCHAR},
	</if>
	<if test="allowLogin != null" >
		allow_login = #{allowLogin,jdbcType=VARCHAR},
	</if>
	<if test="sex != null" >
		sex = #{sex,jdbcType=VARCHAR},
	</if>
	<if test="birthday != null" >
		birthday = #{birthday,jdbcType=TIMESTAMP},
	</if>
	<if test="address != null" >
		address = #{address,jdbcType=VARCHAR},
	</if>
	<if test="email != null" >
		email = #{email,jdbcType=VARCHAR},
	</if>
	<if test="mobileTel != null" >
		mobile_tel = #{mobileTel,jdbcType=VARCHAR},
	</if>
	<if test="fixedTel != null" >
		fixed_tel = #{fixedTel,jdbcType=VARCHAR},
	</if>
	<if test="userIntime != null" >
		user_intime = #{userIntime,jdbcType=TIMESTAMP},
	</if>
	<if test="userOuttime != null" >
		user_outtime = #{userOuttime,jdbcType=TIMESTAMP},
	</if>
	<if test="description != null" >
		description = #{description,jdbcType=VARCHAR},
	</if>
	<if test="deviceId != null" >
		device_id = #{deviceId,jdbcType=VARCHAR},
	</if>
	<if test="deviceType != null" >
		device_type = #{deviceType,jdbcType=VARCHAR},
	</if>
	<if test="receiveMsg != null" >
		receive_msg = #{receiveMsg,jdbcType=VARCHAR},
	</if>

	<if test="isValid != null" >
		is_valid = #{isValid,jdbcType=VARCHAR},
	</if>
	<if test="xzTime != null" >
		xz_time = #{xzTime,jdbcType=TIMESTAMP},
	</if>
	<if test="xzUser != null" >
		xz_user = #{xzUser,jdbcType=VARCHAR},
	</if>
	<if test="xgTime != null" >
		xg_time = #{xgTime,jdbcType=TIMESTAMP},
	</if>
	<if test="xgUser != null" >
		xg_user = #{xgUser,jdbcType=VARCHAR},
	</if>
	<if test="status != null" >
		status = #{status,jdbcType=INTEGER},
	</if>
	<if test="orgId != null" >
		org_id = #{orgId,jdbcType=INTEGER},
	</if>
	<if test="post != null" >
		post = #{post,jdbcType=INTEGER},
	</if>
	<if test="postion != null" >
		postion = #{postion,jdbcType=INTEGER},
	</if>
	<if test="postionName != null" >
		postion_name = #{postionName,jdbcType=INTEGER},
	</if>
	<if test="tenantId != null" >
		tenant_id = #{tenantId,jdbcType=BIGINT},
	</if>
	<if test="resetFlag != null" >
		reset_flag = #{resetFlag,jdbcType=INTEGER},
	</if>
			<if test="cai != null" >
				cai = #{cai,jdbcType=VARCHAR},
			</if>
			<if test="regionName != null" >
				region_name = #{regionName,jdbcType=VARCHAR},
			</if>
			<choose>
				<when test="extFlag != null">
				ext_flag = #{extFlag,jdbcType=INTEGER},
				</when>
				<when test="addExtFlag != null and removeExtFlag != null">
				ext_flag = (ext_flag|#{addExtFlag,jdbcType=INTEGER}) - (ext_flag&amp;#{removeExtFlag,jdbcType=INTEGER}),
				</when>
				<when test="addExtFlag != null">
				ext_flag = ext_flag|#{addExtFlag,jdbcType=INTEGER},
				</when>
				<when test="removeExtFlag != null">
				ext_flag = ext_flag - (ext_flag&amp;#{removeExtFlag,jdbcType=INTEGER}),
				</when>
			</choose>
	</set>
	where user_id = #{userId,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.sys.auth.model.WxTUser" >
	<!--
	WARNING - @mbggenerated
	This element is automatically generated by MyBatis Generator, do not modify.
	This element was generated on Wed Jun 03 10:58:07 CST 2015.
	-->
	update wx_t_user
	set login_name = #{loginName,jdbcType=VARCHAR},
	user_no = #{userNo,jdbcType=VARCHAR},
	password = #{password,jdbcType=VARCHAR},
	salt = #{salt,jdbcType=VARCHAR},
	pwd_lasttime = #{pwdLasttime,jdbcType=TIMESTAMP},
	ch_name = #{chName,jdbcType=VARCHAR},
	cn_name = #{cnName,jdbcType=VARCHAR},
	pinyin = #{pinyin,jdbcType=VARCHAR},
	allow_login = #{allowLogin,jdbcType=VARCHAR},
	sex = #{sex,jdbcType=VARCHAR},
	birthday = #{birthday,jdbcType=TIMESTAMP},
	address = #{address,jdbcType=VARCHAR},
	email = #{email,jdbcType=VARCHAR},
	mobile_tel = #{mobileTel,jdbcType=VARCHAR},
	fixed_tel = #{fixedTel,jdbcType=VARCHAR},
	org_id = #{orgId,jdbcType=BIGINT},
	postion = #{postion,jdbcType=VARCHAR},
	user_intime = #{userIntime,jdbcType=TIMESTAMP},
	user_outtime = #{userOuttime,jdbcType=TIMESTAMP},
	description = #{description,jdbcType=VARCHAR},
	device_id = #{deviceId,jdbcType=VARCHAR},
	is_valid = #{isValid,jdbcType=VARCHAR},
	xz_time = #{xzTime,jdbcType=TIMESTAMP},
	xz_user = #{xzUser,jdbcType=VARCHAR},
	xg_time = #{xgTime,jdbcType=TIMESTAMP},
	xg_user = #{xgUser,jdbcType=VARCHAR},
	status = #{status,jdbcType=INTEGER},
	tenant_id = #{tenantId,jdbcType=BIGINT}
	where user_id = #{userId,jdbcType=BIGINT}
</update>

<select id="findUserByOrgPermissions" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTUser">
		select user1.*,org.org_allname as org_name
		from (select *
		from wx_t_user u where org_id in (
			select source_id from wx_t_rolesource where role_id in (
				select ur.role_id from wx_t_userrole ur ,wx_t_role r where ur.user_id = #{userId,jdbcType=BIGINT}
				and ur.role_id = r.role_id and r.role_type in( 4,3 )

			) and rs_type=4
		) and   <![CDATA[ user_id <> #{userId,jdbcType=BIGINT} ]]>
		<if test="chName != null">
			and (ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and status = 1
		)as user1 ,wx_t_org as org where user1.org_id = org.org_id

</select>

<select id="CountUserByOrgPermissions" resultType="java.lang.Integer" parameterType="com.sys.auth.model.WxTUser">
		select count(*)
		from (select *
		from wx_t_user u where org_id in (
			select source_id from wx_t_rolesource where role_id in (
				select ur.role_id from wx_t_userrole ur ,wx_t_role r where ur.user_id = #{userId,jdbcType=BIGINT}
				and ur.role_id = r.role_id and r.role_type in( 4,3 )

			) and rs_type=4
		) and   <![CDATA[ user_id <> #{userId,jdbcType=BIGINT} ]]>
		<if test="chName != null">
			and (ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and status = 1
		)as user1 ,wx_t_org as org where user1.org_id = org.org_id

</select>

<select id="countUserList" resultType="java.lang.Integer" parameterType="com.sys.auth.model.WxTUser">
		select count(*)  from wx_t_user u  where  status = 1 and  is_valid = '1'
</select>

<select id="findUserByOrgPermissionsByPage" parameterType="map" resultMap="BaseResultMap">
select top ${pageSize} * from (
		select row_number() over(order by user1.ch_name) as rownumber, user1.*,org.org_allname as org_name
		from (select *
		from wx_t_user u where <!-- org_id in (
			select source_id from wx_t_rolesource where role_id in (
				select ur.role_id from wx_t_userrole ur ,wx_t_role r where ur.user_id = #{userId,jdbcType=BIGINT}
				and ur.role_id = r.role_id and r.role_type in( 4,3 )
			) and rs_type=4
		) and --> user_id != #{userId,jdbcType=BIGINT}
		<if test="chName != null">
			and (ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and status = 1
		)as user1 ,wx_t_org as org where user1.org_id = org.org_id) t where t.rownumber > #{start, jdbcType=INTEGER}
</select>

<select id="findUserByPage" parameterType="map" resultMap="BaseResultMap">
select top ${pageSize} * from (
		select row_number() over(order by user1.user_id) as rownumber, user1.*,org.org_allname as org_name
		from (
		select * from wx_t_user where  status = 1 and is_valid = '1' )as user1 ,wx_t_org as org where user1.org_id = org.org_id
		) t
		where t.rownumber > #{start, jdbcType=INTEGER}
</select>

<select id="selUserListByOrgId" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select * from wx_t_user where status = 1 and is_valid = '1'
	and org_id = #{orgId, jdbcType=BIGINT}
</select>

<select id="selUserListByOrgIdList" resultMap="BaseResultMap">
	select * from wx_t_user where status = 1
	and org_id in
	<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
		#{item}
	</foreach>
</select>

<select id="selUserListByOrgIdAndOrgPidList" parameterType="map" resultMap="BaseResultMap">
	select user.*,
		org_name, branch_name
		from wx_t_user user inner join wx_t_org org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
	<if test="postionList != null">
		and user.postion in
		<foreach item="item" index="index" collection="postionList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
		<if test="orgTypeList != null">
			and org.org_type in
			<foreach item="item" index="index" collection="orgTypeList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and (org.org_id in
		<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
		or org.org_pid in
		<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
		)
</select>

<select id="selAllUserListByName" parameterType="map" resultMap="BaseResultMap">
	select user.*,
		org_type, org_pid,org_allname as org_name, branch_name, org_code
		from wx_t_user user inner join
		(select  org.org_id, org.org_name,org.org_allname, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status from (
			select DISTINCT qxorg.org_id from (
			select DISTINCT source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			)qxorg
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="userNameOrLoginName != null">
			and (user.ch_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%')
				or user.login_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%'))
		</if>
		<!-- <if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if> -->
	order by org_pid, org_id, postion
</select>
<select id="selAllUserListByNameList" parameterType="map" resultMap="BaseResultMap">
	select user.*,
		org_type, org_pid,org_allname as org_name, branch_name, org_code
		from wx_t_user user inner join
		(select  org.org_id, org.org_name,org.org_allname, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status from (
			select DISTINCT qxorg.org_id from (
			select DISTINCT source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) qxorg
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="userNameOrLoginNameList != null">
			and (
			<foreach collection="userNameOrLoginNameList" index="index" item="userName" open=""
				separator="or" close="">
				user.ch_name like  concat('%', #{userName}, '%')
			</foreach>
			or
			<foreach collection="userNameOrLoginNameList" index="index" item="userName" open=""
				separator="or" close="">
				user.login_name like  concat('%', #{userName}, '%')
			</foreach>
			)

		</if>
		<!-- <if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if> -->
	order by org_pid, org_id, postion
</select>


	<select id="selCityUserListByOrgList" parameterType="map" resultMap="BaseResultMap">
		select wxTUser.*,wxRole.ch_role_name, wxTOrg.org_name from wx_t_user wxTUser
		LEFT JOIN wx_t_org wxTOrg on wxTOrg.org_id = wxTUser.org_id
		LEFT JOIN wx_t_userrole wxTUserRole ON wxTUser.user_id=wxTUserRole.user_id
		LEFT JOIN wx_t_role wxRole ON wxRole.role_id = wxTUserRole.role_id
		where wxTUser.tenant_id = #{tenantId, jdbcType=BIGINT} and wxTUser.status = 1
		<if test="orgIdList != null">
			and wxTUser.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="selUserListByOrgList" parameterType="map" resultMap="BaseResultMap">
		select wxTUser.*,wxRole.ch_role_name, wxTOrg.org_name from wx_t_user wxTUser
			LEFT JOIN wx_t_org wxTOrg on wxTOrg.org_id = wxTUser.org_id
			LEFT JOIN wx_t_userrole wxTUserRole ON wxTUser.user_id=wxTUserRole.user_id
			LEFT JOIN wx_t_role wxRole ON wxRole.role_id = wxTUserRole.role_id
			where wxTUser.status = 1
		<if test="orgIdList != null">
			and wxTUser.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="selCityUserListByOrgListCount" parameterType="map" resultType="java.lang.Integer">
		select count(*) from wx_t_user wxTUser
		LEFT JOIN wx_t_org wxTOrg on wxTOrg.org_id = wxTUser.org_id
		where wxTUser.tenant_id = #{tenantId, jdbcType=BIGINT} and wxTUser.status = 1 and wxTUser.org_id in
		<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selCityUserListByQueryForPagesParent" parameterType="map" resultMap="BaseResultMap">
	select * from (
		select user.*,
		org_type, org_pid, org_name, branch_name, org_code,
		dept.brand, dept.dplx, dept.scjb, dept.dpjb,dept.lsdpjb,dept.cmstype, dept.sqjb, dept.area, dept.jylx, dept.dpxz, dept.qjlx, dept.status as dept_status
		from wx_t_user user inner join
		(select DISTINCT org.org_id, org.org_name, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type, org.cmscode from (
			select source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = #{tenantId, jdbcType=BIGINT}
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = #{tenantId, jdbcType=BIGINT}
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = #{tenantId, jdbcType=BIGINT}
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = #{tenantId, jdbcType=BIGINT}) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="postion != null">
			and user.postion = #{postion, jdbcType=VARCHAR}
		</if>
		<if test="orgType != null">
		<![CDATA[
			and org.org_type = #{orgType, jdbcType=VARCHAR} and org.dept_type <> 0
		]]>
		</if>
		<if test="orgIdList != null">
			and (org.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			or org.org_pid in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<!-- <if test="orgIdList == null">
			and (org.org_id in (null)
			or org.org_pid in (null)
			)
		</if> -->
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		left join wx_dept_option dept
		on org.cmscode = dept.cmscode and dept.status = 1
	) as v_user_org
	where 1 = 1
	<if test="brandList != null">
		and brand in
		<foreach item="item" index="index" collection="brandList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dplxList != null">
		and dplx in
		<foreach item="item" index="index" collection="dplxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="zdjbList != null">
		and lsdpjb in
		<foreach item="item" index="index" collection="zdjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="cmstypeList != null">
		and cmstype in
		<foreach item="item" index="index" collection="cmstypeList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="scjbList != null">
		and scjb in
		<foreach item="item" index="index" collection="scjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpjbList != null">
		and dpjb in
		<foreach item="item" index="index" collection="dpjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="sqjbList != null">
		and sqjb in
		<foreach item="item" index="index" collection="sqjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="jylxList != null">
		and jylx in
		<foreach item="item" index="index" collection="jylxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpxzList != null">
		and dpxz in
		<foreach item="item" index="index" collection="dpxzList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="qjlxList != null">
		and qjlx in
		<foreach item="item" index="index" collection="qjlxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="area != null and area == 'AREA01'">
		<![CDATA[and area > 0 and area < 200]]>
	</if>
	<if test="area != null and area == 'AREA02'">
		<![CDATA[and area >= 200]]>
	</if>
	order by org_pid, org_id, postion
	<!-- limit #{startNo, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER} -->
</select>
	<sql id="qxorg_sql">
			select source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
</sql>
<select id="selCityUserListByQueryForPages" parameterType="map" resultMap="BaseResultMap">
	select * from (
		select user.*,
		org_type, org_pid, org_name, branch_name, org_code,
		dept.brand, dept.dplx, dept.scjb, dept.dpjb,dept.lsdpjb,dept.cmstype, dept.sqjb, dept.area, dept.jylx, dept.dpxz, dept.qjlx, dept.status as dept_status
		from wx_t_user user inner join
		(
		select DISTINCT org.org_id, org.org_name, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type, org.cmscode from (

		<if test="orgIdList != null">
			select DISTINCT qxorg.org_id from ( <include refid="qxorg_sql" />) as qxorg
			inner join (
			select DISTINCT allorg.org_id from wx_t_org allorg where
			allorg.org_pid in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			) as  selectedOrg on selectedOrg.org_id = qxorg.org_id

		</if>
		<if test="orgIdList == null">
			<include refid="qxorg_sql" />
		</if>
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1
		) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="postion != null">
			and user.postion = #{postion, jdbcType=VARCHAR}
		</if>
		<if test="orgType != null">
		<![CDATA[
			and org.org_type = #{orgType, jdbcType=VARCHAR} and org.dept_type <> 0
		]]>
		</if>

		<!-- <if test="orgIdList == null">
			and (org.org_id in (null)
			or org.org_pid in (null)
			)
		</if> -->
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		left join wx_dept_option dept
		on org.cmscode = dept.cmscode and dept.status = 1
	) as v_user_org
	where 1 = 1
	<if test="brandList != null">
		and brand in
		<foreach item="item" index="index" collection="brandList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dplxList != null">
		and dplx in
		<foreach item="item" index="index" collection="dplxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="zdjbList != null">
		and lsdpjb in
		<foreach item="item" index="index" collection="zdjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="cmstypeList != null">
		and cmstype in
		<foreach item="item" index="index" collection="cmstypeList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="scjbList != null">
		and scjb in
		<foreach item="item" index="index" collection="scjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpjbList != null">
		and dpjb in
		<foreach item="item" index="index" collection="dpjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="sqjbList != null">
		and sqjb in
		<foreach item="item" index="index" collection="sqjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="jylxList != null">
		and jylx in
		<foreach item="item" index="index" collection="jylxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpxzList != null">
		and dpxz in
		<foreach item="item" index="index" collection="dpxzList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="qjlxList != null">
		and qjlx in
		<foreach item="item" index="index" collection="qjlxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="area != null and area == 'AREA01'">
		<![CDATA[and area > 0 and area < 200]]>
	</if>
	<if test="area != null and area == 'AREA02'">
		<![CDATA[and area >= 200]]>
	</if>
	order by org_pid, org_id, postion
	<!-- limit #{startNo, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER} -->
</select>

<select id="selCityUserListByQueryCount" parameterType="map" resultType="java.lang.Integer">
	select count(user_id) from (
		select user.*,
		org_type, org_pid, org_name, branch_name, org_code,
		dept.brand, dept.dplx, dept.scjb, dept.dpjb,dept.lsdpjb,dept.cmstype, dept.sqjb, dept.area, dept.jylx, dept.dpxz, dept.qjlx, dept.status as dept_status
		from wx_t_user user inner join
		(select DISTINCT org.org_id, org.org_name, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type, org.cmscode from (
		select source_id as org_id from wx_t_rolesource where role_id in (
		select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
		) and rs_type = 1
		union
		select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
		union
		select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="postion != null">
			and user.postion = #{postion, jdbcType=VARCHAR}
		</if>
		<if test="orgType != null">
			<![CDATA[
			and org.org_type = #{orgType, jdbcType=VARCHAR} and org.dept_type <> 0
			]]>
		</if>
		<if test="orgIdList != null">
			and (org.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			or org.org_pid in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<!-- <if test="orgIdList == null">
			and (org.org_id in (null)
			or org.org_pid in (null)
			)
		</if> -->
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		left join wx_dept_option dept
		on org.cmscode = dept.cmscode and dept.status = 1
	) as v_user_org
	where 1 = 1
	<if test="brandList != null">
		and brand in
		<foreach item="item" index="index" collection="brandList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dplxList != null">
		and dplx in
		<foreach item="item" index="index" collection="dplxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="scjbList != null">
		and scjb in
		<foreach item="item" index="index" collection="scjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpjbList != null">
		and dpjb in
		<foreach item="item" index="index" collection="dpjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="zdjbList != null">
		and lsdpjb in
		<foreach item="item" index="index" collection="zdjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="cmstypeList != null">
		and cmstype in
		<foreach item="item" index="index" collection="cmstypeList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="sqjbList != null">
		and sqjb in
		<foreach item="item" index="index" collection="sqjbList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="jylxList != null">
		and jylx in
		<foreach item="item" index="index" collection="jylxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="dpxzList != null">
		and dpxz in
		<foreach item="item" index="index" collection="dpxzList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="qjlxList != null">
		and qjlx in
		<foreach item="item" index="index" collection="qjlxList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</if>
	<if test="area != null and area == 'AREA01'">
		<![CDATA[and area > 0 and area < 200]]>
	</if>
	<if test="area != null and area == 'AREA02'">
		<![CDATA[and area >= 200]]>
	</if>
</select>

<select id="selUserListByQueryForPages" parameterType="map" resultMap="BaseResultMap">
	select user.*,
		org_type, org_pid,org_allname as org_name, branch_name, org_code
		from wx_t_user user inner join
		(select  org.org_id, org.org_name,org.org_allname, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type from (
			select DISTINCT qxorg.org_id from(
				select DISTINCT source_id as org_id from wx_t_rolesource where role_id in (
				select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
				) and rs_type = 1
				union
				select  source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
				union
				select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) qxorg
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="userNameOrLoginName != null">
			and (user.ch_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%')
				or user.login_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%'))
		</if>
		<!-- <if test="orgCodeOrOrgName != null">
			and (org.org_name like concat('%', #{orgCodeOrOrgName, jdbcType=VARCHAR}, '%')
				or org.org_code like concat('%', #{orgCodeOrOrgName, jdbcType=VARCHAR}, '%'))
		</if> -->
		<if test="postionList != null">
			and user.postion in
			<foreach item="item" index="index" collection="postionList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="post != null">
			and user.post = #{post, jdbcType=BIGINT}
		</if>
		<!-- <if test="orgTypeList != null">
			and org.org_type in
			<foreach item="item" index="index" collection="orgTypeList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if> -->
		<if test="orgType != null">
			<![CDATA[
			and org.org_type = #{orgType, jdbcType=VARCHAR} and org.dept_type <> 0
			]]>
		</if>
		<if test="orgIdList != null">
			and (org.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			or org.org_pid in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<!-- <if test="orgIdList == null">
			and (org.org_id in (null)
			or org.org_pid in (null)
			)
		</if> -->
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	order by org_pid, org_id, postion
	<!-- limit #{startNo, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER} -->
</select>

<select id="selUserListByQueryCount" parameterType="map" resultType="java.lang.Integer">
	select count(user_id) from (
		select user.user_id
		from wx_t_user user inner join
		(select  org.org_id, org.org_name, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type from (
			select DISTINCT qxorg.org_id from(
			select DISTINCT source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) as qxorg
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1) as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		<if test="userNameOrLoginName != null">
			and (user.ch_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%')
				or user.login_name like concat('%', #{userNameOrLoginName, jdbcType=VARCHAR}, '%'))
		</if>
		<!-- <if test="orgCodeOrOrgName != null">
			and (org.org_name like concat('%', #{orgCodeOrOrgName, jdbcType=VARCHAR}, '%')
				or org.org_code like concat('%', #{orgCodeOrOrgName, jdbcType=VARCHAR}, '%'))
		</if> -->
		<if test="postionList != null">
			and user.postion in
			<foreach item="item" index="index" collection="postionList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="post != null">
			and user.post = #{post, jdbcType=BIGINT}
		</if>
		<!-- <if test="orgTypeList != null">
			and org.org_type in
			<foreach item="item" index="index" collection="orgTypeList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if> -->
		<if test="orgType != null">
			<![CDATA[
			and org.org_type = #{orgType, jdbcType=VARCHAR} and org.dept_type <> 0
			]]>
		</if>
		<if test="orgIdList != null">
			and (org.org_id in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			or org.org_pid in
			<foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<!-- <if test="orgIdList == null">
			and (org.org_id in (null)
			or org.org_pid in (null)
			)
		</if> -->
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	) as v_user_org
</select>

<select id="selUserListByUserIdList" resultMap="BaseResultMap">
	select user.*,
		org_type, org_pid, org_name, branch_name, org_code
		from wx_t_user user inner join wx_t_org org
		on user.org_id = org.org_id and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		and user.user_id in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
		#{item}
		</foreach>
</select>

<select id="selBranchSupervisionUserList" parameterType="map" resultMap="BaseResultMap">
	select user.*,
		org_type, org_pid, org_name, branch_name, org_code
		from wx_t_user user
		INNER JOIN wx_t_org as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and user.user_id in (
			select user_id from wx_dept_super where status = 1
			and branch_code in
			<foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
				#{item}
			</foreach>
		)
		<if test="selectedUserIdList != null">
			and user.user_id not in
			<foreach item="item" index="index" collection="selectedUserIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
</select>

<select id="selMobileUserListByUserIdList" resultMap="BaseResultMap">
	select * from wx_t_user
		where user_id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
		#{item}
		</foreach>
	<![CDATA[
		and receive_msg <> 0
		and device_id is not null
		and device_type is not null
	]]>
</select>
<insert id="insertByBatch" useGeneratedKeys="true" parameterType="java.util.List" >
insert into wx_t_user (login_name, user_no, password, salt, pwd_lasttime, ch_name,
	allow_login, sex, email, mobile_tel,org_id,postion, user_intime,  xz_time, xz_user, xg_time, xg_user, status,
	tenant_id,postion_name,is_valid,type)
	values
	<foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
	#{item.loginName,jdbcType=VARCHAR}, #{item.userNo,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR},
	#{item.salt,jdbcType=VARCHAR}, #{item.pwdLasttime,jdbcType=TIMESTAMP}, #{item.chName,jdbcType=VARCHAR},
	#{item.allowLogin,jdbcType=VARCHAR}, #{item.sex,jdbcType=VARCHAR},#{item.email,jdbcType=VARCHAR},
	#{item.mobileTel,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},#{item.postion,jdbcType=VARCHAR},
	#{item.userIntime,jdbcType=TIMESTAMP}, #{item.xzTime,jdbcType=TIMESTAMP}, #{item.xzUser,jdbcType=VARCHAR},
	#{item.xgTime,jdbcType=TIMESTAMP}, #{item.xgUser,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
	#{item.tenantId,jdbcType=BIGINT},#{item.postionName,jdbcType=BIGINT},
	#{item.status,jdbcType=INTEGER},
	#{item.type,jdbcType=VARCHAR}
		</trim>
	</foreach>
</insert>
<update id="updateByBatch" parameterType="java.util.List">
<foreach collection="list" index="index" item="item" separator=";">
	update wx_t_user
	set ch_name = #{item.chName,jdbcType=VARCHAR},
		login_name = #{item.loginName,jdbcType=VARCHAR},
		allow_login = #{item.allowLogin,jdbcType=VARCHAR},
		sex = #{item.sex,jdbcType=VARCHAR},
		email = #{item.email,jdbcType=VARCHAR},
		mobile_tel = #{item.mobileTel,jdbcType=VARCHAR},
		org_id = #{item.orgId,jdbcType=BIGINT},
		postion = #{item.postion,jdbcType=VARCHAR},
		user_intime = #{item.userIntime,jdbcType=TIMESTAMP},
		xg_time=getdate(),
		status = #{item.status,jdbcType=INTEGER},
		postion_name = #{item.postionName,jdbcType=INTEGER},
		is_valid =  #{item.status,jdbcType=INTEGER}
	where user_no = #{item.userNo,jdbcType=VARCHAR}
	</foreach>
</update>
<update id="updateForPinyin">
	update wx_t_user set  pinyin = to_pinyin(ch_name);
</update>
<update id="updateForPinyinWithNullOrBlank">
	update wx_t_user set  pinyin = '#' where pinyin is null or pinyin = '';
</update>
<update id="updateByUserNo"  parameterType="map">
	update wx_t_user
	set  org_id = #{orgId,jdbcType=BIGINT},
			ch_name =  #{chName,jdbcType=VARCHAR},
		postion_name = #{postionName,jdbcType=VARCHAR},
		status = #{status,jdbcType=INTEGER},
		is_valid = #{status,jdbcType=INTEGER},
		allow_login = #{allowLogin,jdbcType=VARCHAR},
		xg_time=getdate()
	where user_no = #{userNo,jdbcType=VARCHAR}
</update>
<select id="selectForInvalidUser" resultMap="BaseResultMap">
<![CDATA[
	select user_id,0 as status,'0' as is_valid from  wx_t_user u1 where u1.org_id in(
		select u.org_id from wx_t_user  u ,wx_t_org o where u.org_id = o.org_id and o.dept_type<>0
			GROUP BY u.org_id HAVING count(1)>1) and u1.status=1
	and u1.user_no not in( select od.cmscode from wx_t_user od where od.dept_type<>0)

]]>
</select>
<update id="correctionUserNotShopowner">
	<![CDATA[ update wx_t_user INNER JOIN
	(select user_id,0 as status,'0' as is_valid from  wx_t_user u1 where u1.org_id in(
			select u.org_id from wx_t_user  u ,wx_t_org o where u.org_id = o.org_id and o.dept_type<>0
				GROUP BY u.org_id HAVING count(1)>1) and u1.status=1
		and u1.user_no not in( select od.cmscode from wx_t_org od where od.dept_type<>0) ) as vu
	on wx_t_user.user_id = vu.user_id set  wx_t_user.`status`=vu.`status`, wx_t_user.is_valid=vu.is_valid
	]]>
</update>
<select id="selWholeUserByUserIDAndBranchID" parameterType="map" resultMap="BaseResultMap">
	select * from (
		select user.*,
		org_type, org_pid, org_name, branch_name, org_code,
		dept.brand, dept.dplx, dept.scjb, dept.dpjb, dept.sqjb, dept.area, dept.jylx, dept.dpxz, dept.qjlx, dept.status as dept_status
		from wx_t_user user inner join
		(select DISTINCT org.org_id, org.org_name, org.org_code, org.org_pid as org_pid, org.branch_name,
		org.org_type, org.org_root_id, org.status, org.dept_type, org.cmscode from (
			select source_id as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			) and rs_type = 1
			union
			select source_id as org_id from wx_t_userbase where rs_type = 1 and user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
			union
			select org_id from wx_t_user u where u.user_id = #{userId, jdbcType=BIGINT} and tenant_id = 1
		) as role_org
		INNER JOIN wx_t_org as org on role_org.org_id = org.org_id
		and status = 1 and org_root_id = '1' and tenant_id = 1
		<if test="branchId != null">
			and org.branch_id = #{branchId, jdbcType=BIGINT}
		</if>)
		as org
		on user.org_id = org.org_id
		and user.status = 1 and user.is_valid = '1'
		and org.status = 1 and org.org_root_id = '1'
		left join wx_dept_option dept
		on org.cmscode = dept.cmscode and dept.status = 1
	) as v_user_org
	order by org_pid, org_id, postion
</select>

<select id="countUserByLoginName" parameterType="map" resultType="java.lang.Integer">
	select count(*) from wx_t_user where login_name = #{loginName, jdbcType=VARCHAR}
		<if test="tenantId != null">
			and tenant_id = #{tenantId, jdbcType=BIGINT}
		</if>
		and status = 1
</select>

<select id="findUserByCompanyAccountAndLoginName" parameterType="map" resultMap="BaseResultMap">
	SELECT
		*,
		(select o.organization_name org_name from wx_t_organization o  where o.id= wxtuser.org_id )  as  org_name
	FROM
		wx_t_user wxtuser
	LEFT JOIN wx_company_info company ON (
		wxtuser.tenant_id = company.tenant_id
	)
	WHERE
		wxtuser.login_name = #{loginName, jdbcType=VARCHAR}
	AND company.company_account = #{companyAccount, jdbcType=VARCHAR}
	AND wxtuser.STATUS = 1
</select>

<sql id="sql_login_user">
	SELECT
		u.*,
		o.organization_name as org_name,
		(select f.att_id from wx_att_file f where f.source_type='2' and f.source_id =u.user_id ) as photo_id,
		case when u.cai is not null and u.cai!='' then (select top 1 sales_channel_name from (select rsc.sales_channel_name
			from dw_region_sales_channel_rel rsc 
			where rsc.channel_manager_cai=u.cai
			union all
			select rsc.sales_channel_name
			from dw_customer_region_sales_supervisor_rel crss
			left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
			where crss.sales_cai=u.cai or crss.suppervisor_cai=u.cai) biu) else null end sales_channel,
		(case when (u.type is null or u.type != '1') then pe.ext_flag else 0 end) partner_flag,
	case when u.type='1' then 2 else o.type end org_type,
	pe.distributor_id,
	case when u.ext_flag&amp;4>0 then isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
		(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))
			- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),CONVERT(varchar(100), GETDATE(), 23)) else null end ext_property1 /*[密码即将过期]：有效天数=密码有效期-上次密码修改距今天数*/
	, case when u.user_id!= 1 and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 where ur1.user_id=u.user_id and r1.ch_role_name='Chevron_System_Integration') then 1 else 0 end system_integration_user
	, case when o.type=1 and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 where ur1.user_id=u.user_id and r1.user_flag&amp;32>0) then 32/*经销商老板*/ else 0 end user_flag
	from wx_t_user u
	left join wx_t_organization o on o.id = u.org_id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
</sql>
<select id="findUserByLoginName" parameterType="map" resultMap="BaseResultMap">
	<include refid="sql_login_user"/>
		WHERE u.login_name = #{loginName, jdbcType=VARCHAR}
		/*AND u.allow_login='T'*/
		AND u.status = 1
</select>

<select id="findUserByUserNo" parameterType="map" resultType="java.lang.Integer">
	select count(*) from wx_t_user where user_no = #{userNo, jdbcType=VARCHAR}
		<if test="tenantId != null">
			and tenant_id = #{tenantId, jdbcType=BIGINT}
		</if>
		and status = 1
</select>

<select id="findUserByLoginNameOrUserNoOrUserId" parameterType="map" resultType="java.lang.Integer">
	select count(*) from wx_t_user where (login_name = #{loginName, jdbcType=VARCHAR} and user_no = #{userNo, jdbcType=VARCHAR} and user_id = #{userId, jdbcType=BIGINT}) and status = 1
</select>

<select id="selUserByUserid" parameterType="map" resultMap="BaseResultMap">
	<include refid="sql_login_user"/>
		WHERE u.user_id = #{userId, jdbcType=BIGINT}
		AND u.allow_login='T'
		AND u.status = 1
</select>


<update id="removeUserByUserid">
	update wx_t_user
	set status = 0, is_valid = 0, xg_time=getdate()
	where user_id = #{userId, jdbcType=BIGINT}
</update>

<select id="selMaxIdData" parameterType="map" resultMap="BaseResultMap">
	select * from wx_t_user where user_id = (select max(user_id) from wx_t_user)
</select>

<select id="selMaxTenantId" parameterType="map" resultType="java.lang.Long">
	select DISTINCT tenant_id from wx_t_user where tenant_id = (select max(tenant_id) from wx_t_user)
</select>
<update id="delCityByOrgId">
	update wx_t_user set status = 0 where org_id = #{orgId, jdbcType=BIGINT}
</update>


<select id="selUserByPartnerId" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="simple_Column_List" />
	from wx_t_user where tenant_id = #{tenantId, jdbcType=BIGINT}
</select>

	<select id="seletUsers" parameterType="map" resultMap="BaseResultMap">
	select
	<if test="limit != null"> top ${limit} </if>
	tuser.*, 
	o.organization_name as  org_name,
	o.type org_type
	from wx_t_user tuser
	left join wx_t_organization o  on o.id = tuser.org_id 
		where tuser.status=1
		<if test="userId != null">
			and tuser.user_id=#{userId}
		</if>
		<if test="excludeUserId != null">
			and tuser.user_id!=#{excludeUserId}
		</if>
		<if test="includeAdmin != true">
			and tuser.user_id != 1 <!--'chevronadmin'-->
		</if>
		<if test="conditionSql != null and conditionSql != ''">
			and (${conditionSql})
		</if>
		<if test="userName!=null">
				and (<!-- tuser.login_name like '%${userName}%'
			or -->
				tuser.ch_name like #{userName} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(tuser.ch_name),500) LIKE #{userNameFirstWord} + '%'
				)
		</if>
		<!--  <if test="tenantid!=1">
				tuser.org_id = ${tenantid}
			and
		</if> -->
		<if test="orgmodel!=null">
			and
			tuser.org_id IN
			<foreach item="orgID" index="index" collection="orgIdLst" open="(" separator="," close=")">
			#{orgID}
			</foreach>
			</if>
			<if test="fun != 'pushMessage'">
			and (tuser.type is null or tuser.type != 1) and tuser.org_id != 1
			</if>
			<if test="userList != null">
				and tuser.user_id in
				<foreach item="item" index="index" collection="userList" open="(" separator="," close=")">
				#{item}
				</foreach>
			</if>
			<choose>
				<when test="orgType == 2">
					and tuser.type=1
				</when>
				<when test="orgType != null">
					and (tuser.type is null or tuser.type != 1)
					and exists (select 1 from wx_t_organization o1 where o1.id=tuser.org_id and o1.type=#{orgType})
				</when>
			</choose>
			<if test="orgId != null">
				and tuser.org_id=#{orgId}
			</if>
			<!-- and
				tuser.user_id != #{userId, jdbcType=BIGINT} delete by bo.liu 0829-->

</select>

	<select id="seletTodoUsers" parameterType="map" resultMap="BaseResultMap">
	select
	tuser.*, 
	o.organization_name as  org_name,
	o.type org_type,
	case when exists (select 1 from wx_t_credit_app_user_absent_info credit_uai1 where credit_uai1.user_id=tuser.user_id and credit_uai1.delete_flag=0 and credit_uai1.start_time&lt;=getdate() and credit_uai1.end_time>dateadd(day, -1, getdate()))
		then 1 else 0 end user_flag
	from wx_t_user tuser
	left join wx_t_organization o  on o.id = tuser.org_id 
		where tuser.status=1
		<if test="conditionSql != null and conditionSql != ''">
			and (${conditionSql})
		</if>
</select>
	<select id="checkUserExists" parameterType="map" resultMap="BaseResultMap">
	select
	tuser.*
	from wx_t_user tuser
		where tuser.status=1
		<if test="excludeUserId != null">
			and tuser.user_id!=#{excludeUserId}
		</if>
			and (1!=1
			<if test="loginName != null and loginName != ''">
			or tuser.login_name=#{loginName}
			</if>
			<if test="cai != null and cai != ''">
			or tuser.cai=#{cai}
			</if>
			)
</select>

<!-- add by bo.liu start -->
<select id="seletUserByPratnerIdOrNameList" parameterType="map" resultMap="BaseResultMap">
	select
	<if test="limit != null"> top ${limit} </if>
	tuser.*, wxTOrg.organization_name org_name , wxRole.role_descript AS ch_role_name from wx_t_user tuser
		LEFT JOIN wx_t_organization wxTOrg on wxTOrg.id = tuser.org_id
		LEFT JOIN wx_t_userrole userrole ON userrole.user_id = tuser.user_id
		LEFT JOIN wx_t_role wxRole ON wxRole.role_id = userrole.role_id
		where
	 		tuser.user_id != 1 <!--tuser.login_name != 'chevronadmin'-->
		<if test="userName!=null">
				AND (<!-- tuser.login_name like '%${userName}%'
			or -->
				tuser.ch_name like '%' + #{userName} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(tuser.ch_name),500) LIKE #{userNameFirstWord} + '%'
				)
		</if>
		<!--  <if test="tenantid!=1">
				tuser.org_id = ${tenantid}
			and
		</if> -->
		<if test="orgmodel!=null">
			and
			tuser.org_id IN
			<foreach item="orgID" index="index" collection="orgIdLst" open="(" separator="," close=")">
			#{orgID}
			</foreach>
			</if>
			and
				wxTOrg.organization_name!=''
			and
				wxTOrg.organization_code!='0001'
			and
				tuser.status=1
			<!-- and
				tuser.user_id != #{userId, jdbcType=BIGINT} delete by bo.liu 0829-->
</select>

<select id="seletUserByPratnerIdOrgId" parameterType="map" resultMap="BaseResultMap">
	SELECT t_userrole.role_id roleid,tt_role.ch_role_name ch_role_name,
	tuser.*, wxTOrg.organization_name as org_name
	from wx_t_user tuser
	LEFT JOIN wx_t_organization wxTOrg on wxTOrg.id = tuser.org_id
	LEFT JOIN wx_t_userrole t_userrole ON t_userrole.user_id = tuser.user_id
	LEFT JOIN wx_t_role tt_role ON tt_role.role_id = t_userrole.role_id
		where
	tuser.user_id != 1 <!--tuser.login_name != 'chevronadmin'-->
		<if test="userName!=null">
			AND	(tuser.login_name like '%' + #{userName} + '%' or tuser.ch_name like '%' + #{userName} + '%')
		</if>
		<if test="tenantid!=1">
			AND tuser.tenant_id = ${tenantid}
		</if>

		<if test="orgmodel!=null">
			and
			tuser.org_id IN
			<foreach item="orgID" index="index" collection="orgIdLst" open="(" separator="," close=")">
			#{orgID}
			</foreach>
		</if>
</select>

<select id="getUsersByOrgList" parameterType="map" resultMap="BaseResultMap">
	SELECT tuser.*, wxTOrg.organization_name org_name
	from wx_t_user tuser
	LEFT JOIN wx_t_organization wxTOrg on wxTOrg.id = tuser.org_id
	where
	<if test="orgmodel!=null">
		tuser.org_id IN
		<foreach item="orgID" index="index" collection="orgIdLst" open="(" separator="," close=")">
			#{orgID}
		</foreach>
		and
		</if>
		<if test="mUserNameOrAccount!=null">
			(tuser.ch_name like concat('%',#{mUserNameOrAccount,jdbcType=VARCHAR},'%') or
			tuser.login_name like concat('%',#{mUserNameOrAccount,jdbcType=VARCHAR},'%')
			)
			and
		</if>
		tuser.user_id != #{userId,jdbcType=BIGINT}
		and
		wxTOrg.organization_name!=''
	and
		wxTOrg.status!=0
	and
		(tuser.type is null or tuser.type != '1')
</select>

<select id="selUserByUseridNew" parameterType="map" resultMap="BaseResultMap">
	select t_user.*, t_organization.organization_name org_name,t_organization.type org_type
	, case when t_organization.type=1 and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 where ur1.user_id=t_user.user_id and r1.user_flag&amp;32>0) then 32/*经销商老板*/ else 0 end user_flag
	from wx_t_user t_user left join wx_t_organization t_organization on t_user.org_id = t_organization.id
	where t_user.user_id = #{userId, jdbcType=BIGINT}
</select>


<update id="modifyUserStatus" parameterType="map">
	update wx_t_user
		set status = #{status},
	xg_time = #{xgTime,jdbcType=TIMESTAMP},
	xg_user = #{xgUser,jdbcType=VARCHAR}
	where user_id = #{userId, jdbcType=BIGINT}
</update>

<select id="validateUserDelete" parameterType="map" resultType="int">
select case when exists (select 1 from wx_t_work_shop ws where ws.delete_flag=0 and (convert(int, ws.status) > -1) and ws.excute_user_id=#{userId, jdbcType=BIGINT}) then 1 else 0 end
</select>

<select id="findUserByOrgPermissionsByPageNew" parameterType="map" resultMap="BaseResultMap">
<!-- select top ${pageSize} * from (
		select row_number() over(order by user1.ch_name) as rownumber, user1.*,org.organization_name as org_name
		from (select *
		from wx_t_user u where user_id != #{userId,jdbcType=BIGINT}
		<if test="chName != null">
			and (ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and status = 1
		)as user1 ,wx_t_organization as org where user1.org_id = org.id and org.status=1
		<if test="userType==0">
			and org.id = #{orgId,jdbcType=BIGINT}
		</if>) t where t.rownumber > #{start, jdbcType=INTEGER} -->

		<!--
	select top ${pageSize} * from (
		select row_number() over(order by user1.ch_name) as rownumber, user1.*,org.organization_name as org_name,tt_userrole.role_id,tt_role.ch_role_name
		from (select *
		from wx_t_user u where user_id != #{userId,jdbcType=BIGINT}
		<if test="chName != null">
			and (ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and status = 1
		)as user1 ,wx_t_organization as org,wx_t_userrole AS tt_userrole,wx_t_role AS tt_role
			where user1.org_id = org.id and org.status=1
		<if test="userType==0">
			and org.id = #{orgId,jdbcType=BIGINT} AND tt_userrole.user_id = user1.user_id AND tt_role.role_id = tt_userrole.role_id
		</if>
		<if test="userType==3">
			and org.id = #{orgId,jdbcType=BIGINT} AND tt_userrole.user_id = user1.user_id AND tt_role.role_id = tt_userrole.role_id
			AND tt_role.ch_role_name not LIKE '%#{serviceManager}%' AND tt_role.ch_role_name not LIKE '%#{serviceAdmin}%'
		</if>) t where t.rownumber > #{start, jdbcType=INTEGER}-->
		SELECT   top ${pageSize} *
			FROM
			(SELECT row_number() over(order by user1.user_id) as rownumber,
				user1.user_id,
				user1.login_name,
				user1.user_no,
				user1.password,
				user1.salt,
				user1.pwd_lasttime,
				user1.ch_name,
				user1.cn_name,
				user1.pinyin,
				user1.allow_login,
				user1.sex,
				user1.birthday,
				user1.address,
				user1.email,
				user1.mobile_tel,
				user1.fixed_tel,
				user1.org_id,
				user1.postion,
				user1.user_intime,
				user1.user_outtime,
				user1.description,
				user1.device_id,
				user1.device_type,
				user1.receive_msg,
				user1.is_valid,
				user1.post,
				user1.xz_time,
				user1.xz_user,
				user1.xg_time,
				user1.xg_user,
				user1.status,
				user1.tenant_id,
				user1.postion_name,
				<choose >
					<when test="orgType == 1" >
					(select ws0.work_shop_name from wx_t_work_shop ws0 where ws0.id=user1.org_id)
					</when>
					<otherwise>
					(select org.organization_name from wx_t_organization as org where user1.org_id = org.id)
					</otherwise>
				</choose>
				as org_name
			FROM
			(SELECT  u.* from  wx_t_user u where user_id != #{userId,jdbcType=BIGINT}
				<if test="userName != null">
					and (ch_name like concat('%',#{userName,jdbcType=VARCHAR},'%') or
					login_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
					)
				</if>
			<choose >
				<when test="orgType == 1" >
				and u.org_id= #{orgId,jdbcType=BIGINT} and u.type='1'
				</when>
				<otherwise>
					<if test="userType==0 || userType==3">
						and u.org_id = #{orgId,jdbcType=BIGINT}
					</if>
					and (u.type is null or u.type != '1')
				<if test="userType==3">
				AND user_id NOT IN (SELECT
					DISTINCT tt_user_role.user_id
					FROM
					wx_t_userrole tt_user_role
					LEFT
					JOIN
					wx_t_role tt_role
					ON
					tt_user_role.role_id = tt_role.role_id
					WHERE
					tt_role.ch_role_name = #{serviceManager,jdbcType=VARCHAR}
					or
					tt_role.ch_role_name = #{serviceAdmin,jdbcType=VARCHAR}
					OR
					tt_role.ch_role_name = #{chevronManager,jdbcType=VARCHAR})
				</if>
				<if test="userType==0">
					AND user_id NOT IN (SELECT
					DISTINCT tt_user_role.user_id
					FROM
					wx_t_userrole tt_user_role
					LEFT
					JOIN
					wx_t_role tt_role
					ON
					tt_user_role.role_id = tt_role.role_id
					WHERE
					tt_role.ch_role_name = #{chevronManager,jdbcType=VARCHAR})
				</if>
				</otherwise>
			</choose>
		)as user1
			) t where t.rownumber > #{start, jdbcType=INTEGER}


</select>
<select id="findPermissionUser" parameterType="com.sys.auth.model.PermissionParams" resultMap="BaseResultMap">
		SELECT u.user_id,
				u.login_name,
				u.user_no,
				u.password,
				u.salt,
				u.pwd_lasttime,
				u.ch_name,
				u.cn_name,
				u.pinyin,
				u.allow_login,
				u.sex,
				u.birthday,
				u.address,
				u.email,
				u.mobile_tel,
				u.fixed_tel,
				u.org_id,
				u.postion,
				u.user_intime,
				u.user_outtime,
				u.description,
				u.device_id,
				u.device_type,
				u.receive_msg,
				u.is_valid,
				u.post,
				u.xz_time,
				u.xz_user,
				u.xg_time,
				u.xg_user,
				u.status,
				u.tenant_id,
				u.postion_name,
				case when u.type = 1 then 1 else (select o1.type + 2 from wx_t_organization o1 where o1.id=u.org_id) end type,
				<choose >
					<when test="orgType == 1" >
					(select ws0.work_shop_name from wx_t_work_shop ws0 where ws0.id=u.org_id)
					</when>
					<otherwise>
					(select org.organization_name from wx_t_organization as org where u.org_id = org.id)
					</otherwise>
				</choose>
				as org_name
			FROM wx_t_user u where u.status=1 and u.user_id != #{userId,jdbcType=BIGINT}
			<if test="dataFilterType == 'spadmin'">
			and not exists (SELECT 1 FROM wx_t_role i JOIN wx_t_userrole i1 on i.role_id=i1.role_id WHERE (i.ch_role_name='${spadminRoleName}' or i.ch_role_name='${spmanagerRoleName}') and i1.user_id=u.user_id)
			</if>
				<if test="userName != null and userName != ''">
					and (ch_name like concat('%',#{userName,jdbcType=VARCHAR},'%') or
					login_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
					)
				</if>
				<choose>
					<when test="orgType == 1">
				and u.org_id= #{orgId,jdbcType=BIGINT} and u.type='1'
					</when>
					<otherwise>
				and (u.type is null or u.type != '1')
						<choose>
					<when test="orgIdInCond != null">
				and u.org_id IN ${orgIdInCond}
					</when>
					<when test="loginUserType == 'chevron'">
			and (exists (select 1 from wx_t_organization o5 where o5.type=0) 
			or not exists (select 1 from wx_t_partner_responsible_main xx_001_prm where xx_001_prm.user_id=#{userId,jdbcType=BIGINT})
			or exists (select 1 from wx_t_partner_responsible_main xx_001_prm join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id where xx_001_pr.partner_id=u.org_id and xx_001_prm.user_id=#{userId,jdbcType=BIGINT})
			)
					</when>
						</choose>
					</otherwise>
				</choose>
</select>
<select id="countUserByOrgPermissionsByPageNew" parameterType="map" resultType="int">
		<!-- select count(1)
		from wx_t_user u,wx_t_organization org where u.user_id != #{userId,jdbcType=BIGINT}
		<if test="chName != null">
			and (u.ch_name like concat('%',#{chName,jdbcType=VARCHAR},'%') or
			u.login_name like concat('%',#{chName,jdbcType=VARCHAR},'%')
			)
		</if>
		and u.status = 1
		and u.org_id = org.id and org.status=1
		<if test="userType==0">
			and org.id = #{orgId,jdbcType=BIGINT}
		</if> -->


		SELECT  count(1)
			FROM wx_t_user u where user_id != #{userId,jdbcType=BIGINT}
				<if test="userName != null">
					and (ch_name like concat('%',#{userName,jdbcType=VARCHAR},'%') or
					login_name like concat('%',#{userName,jdbcType=VARCHAR},'%')
					)
				</if>
			<choose >
				<when test="orgType == 1" >
				and u.org_id= #{orgId,jdbcType=BIGINT} and u.type='1'
				</when>
				<otherwise>
					<if test="userType==0 || userType==3">
						and u.org_id = #{orgId,jdbcType=BIGINT}
					</if>
					and (u.type is null or u.type != '1')
				<if test="userType==3">
				AND user_id NOT IN (SELECT
					DISTINCT tt_user_role.user_id
					FROM
					wx_t_userrole tt_user_role
					LEFT
					JOIN
					wx_t_role tt_role
					ON
					tt_user_role.role_id = tt_role.role_id
					WHERE
					tt_role.ch_role_name = #{serviceManager,jdbcType=VARCHAR}
					or
					tt_role.ch_role_name = #{serviceAdmin,jdbcType=VARCHAR}
					OR
					tt_role.ch_role_name = #{chevronManager,jdbcType=VARCHAR})
				</if>
				<if test="userType==0">
					AND user_id NOT IN (SELECT
					DISTINCT tt_user_role.user_id
					FROM
					wx_t_userrole tt_user_role
					LEFT
					JOIN
					wx_t_role tt_role
					ON
					tt_user_role.role_id = tt_role.role_id
					WHERE
					tt_role.ch_role_name = #{chevronManager,jdbcType=VARCHAR})
				</if>
				</otherwise>
			</choose>
</select>

<select id="getUsersByPartnerIdsForWorkshopUpdate" parameterType="map" resultMap="BaseResultMap">
select *
from
	wx_t_user t_user
where
	<if test="mPartnerIds!=null">
			t_user.org_id IN
		<foreach item="orgID" index="index" collection="mPartnerIds" open="(" separator="," close=")">
			#{orgID}
		</foreach>
		and
	</if>
	1=1
</select>
<!-- end by bo.liu -->

<select id="selWorkshopManagers" parameterType="map" resultMap="BaseResultMap">
SELECT i1.*
FROM wx_t_user i1
where i1.type='1' and i1.org_id=#{workshopId} and
exists (select 1 from wx_t_userrole ii1 join wx_t_role ii2 on ii1.role_id=ii2.role_id and ii2.ch_role_name='${shopManagerRole}' where ii1.user_id=i1.user_id)
</select>

<sql id="selectOrgUsers_cond">
FROM wx_t_user u_t
join wx_t_organization o_t on u_t.org_id=o_t.id
where u_t.status=1 and (u_t.type is null or u_t.type != '1') and o_t.type not in (2,3)
<if test="dataFilterType == 'spadmin'">
and not exists (SELECT 1 FROM wx_t_role i JOIN wx_t_userrole i1 on i.role_id=i1.role_id WHERE (i.ch_role_name='${spadminRoleName}' or i.ch_role_name='${spmanagerRoleName}') and i1.user_id=u_t.user_id)
</if>
<if test="loginUserType == 'SP'">
and (not exists (select 1 from wx_t_user_charge_region xx_001_ucr where xx_001_ucr.user_id=#{loginUserId}) or exists (select 1 from wx_t_user_charge_region xx_001_ucr where xx_001_ucr.user_id=u_t.user_id 
and exists (select 1 from wx_t_user_charge_region xx_001_ucr1 where xx_001_ucr1.user_id=#{loginUserId} and xx_001_ucr1.charge_region_id = xx_001_ucr.charge_region_id)))
</if>
<if test="orgLimit and loginUserId != 1">
	and (#{salesChannel} is null or not exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe 
			join wx_t_business_region_config xx_001_brc on xx_001_pe.partner_property=xx_001_brc.ext_property1 and 
			xx_001_brc.business_name='DATA_PERMISSION_EXCLUDE_SP_IN_SALES_CHANNEL' where xx_001_pe.partner_id=o_t.id and xx_001_brc.sales_channel=#{salesChannel}))
	
	and (exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe 
			left join dw_customer_region_sales_supervisor_rel xx_001_crss on xx_001_pe.distributor_id=xx_001_crss.distributor_id
			left join dw_region_sales_channel_rel xx_001_rsc on xx_001_rsc.region_name=xx_001_crss.region_name
			left join wx_t_partner_responsible_main xx_001_prm on xx_001_prm.config_type=1 and xx_001_prm.sales_cai= xx_001_crss.sales_cai
			where xx_001_pe.partner_id=o_t.id and (xx_001_crss.sales_cai=#{loginCai} or xx_001_crss.suppervisor_cai=#{loginCai} or xx_001_rsc.sales_channel_name=#{loginCai} or xx_001_prm.user_id=#{loginUserId}))
		or exists (select 1 from wx_t_partner_responsible_main xx_001_prm 
			join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id 
			where xx_001_pr.partner_id=o_t.id and xx_001_prm.user_id=#{loginUserId} and xx_001_prm.config_type=0))
</if>
<if test="rolePermission">
and exists (SELECT 1 FROM wx_t_userrole ur00 where ur00.user_id=u_t.user_id 
	and exists (SELECT 1 FROM wx_t_userrole ur01 where ur01.user_id=#{loginUserId} 
		and (ur01.role_id=ur00.role_id or exists (select 1 from wx_t_role_manage_role rmr where rmr.f_manage_role_id=ur00.role_id and rmr.f_manager_role_id=ur01.role_id))))
	<if test="excludeRoles != null">
		and not exists (SELECT 1 FROM wx_t_userrole ur03 join wx_t_role r03 on r03.role_id=ur03.role_id WHERE ur03.user_id=u_t.user_id and r03.ch_role_name in 
	<foreach item="item" index="index" collection="excludeRoles" open="(" separator="," close=")">
		'${item}'
	</foreach>
		)
	</if>
</if>
</sql>
<select id="selectOrgUsers" parameterType="com.sys.auth.model.UserParams" resultMap="BaseResultMap">
SELECT u_t.user_id, u_t.login_name, u_t.user_no, u_t.password, u_t.salt, u_t.pwd_lasttime, u_t.ch_name, u_t.cn_name, u_t.pinyin,
	u_t.allow_login, u_t.sex, u_t.birthday, u_t.address, u_t.email, u_t.mobile_tel, u_t.fixed_tel, u_t.org_id,u_t.postion,u_t.postion_name, u_t.user_intime,
	u_t.user_outtime, u_t.description,u_t.device_id,device_type,u_t.receive_msg, u_t.is_valid, u_t.xz_time, u_t.xz_user, u_t.xg_time, u_t.xg_user, u_t.status,
	u_t.type, o_t.organization_name org_name, o_t.type org_type,u_t.ext_flag,
	case when exists (SELECT 1 FROM wx_t_role i JOIN wx_t_userrole i1 on i.role_id=i1.role_id WHERE i.role_password_power=2 and i1.user_id=#{loginUserId}) then 2 else 1 end change_pwd_permission
	, case when o_t.type=1 and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 where ur1.user_id=u_t.user_id and r1.user_flag&amp;32>0) then 32/*经销商老板*/ else 0 end user_flag
	<include refid="selectOrgUsers_cond"/>
	 and u_t.user_id != #{loginUserId}
<if test="orgId != null and orgIdInCond == null">
	and u_t.org_id=#{orgId}
</if>
<if test="orgIdInCond != null">
and u_t.org_id IN ${orgIdInCond}
</if>
<if test="keyWord != null and keyWord != ''">
and (u_t.ch_name like ('%' + #{keyWord} + '%') or u_t.login_name like ('%' + #{keyWord} + '%'))
</if>
<if test="chName != null and chName != ''">
and u_t.ch_name like ('%' + #{chName} + '%')
</if>
<if test="loginName != null and loginName != ''">
and u_t.login_name like ('%' + #{loginNames} + '%')
</if>
<if test="loginOrgId != null">
and u_t.org_id=#{loginOrgId}
</if>

	<if test="includeRetailer == 1">
	union all SELECT u1.user_id, u1.login_name, u1.user_no, u1.password, u1.salt, u1.pwd_lasttime, u1.ch_name, u1.cn_name, u1.pinyin,
	u1.allow_login, u1.sex, u1.birthday, u1.address, u1.email, u1.mobile_tel, u1.fixed_tel, u1.org_id,u1.postion,u1.postion_name, u1.user_intime,
	u1.user_outtime, u1.description,u1.device_id,device_type,u1.receive_msg, u1.is_valid, u1.xz_time, u1.xz_user, u1.xg_time, u1.xg_user, u1.status,
	u1.type, oo1.organization_name+' - ' + o1.organization_name org_name, o1.type org_type, u1.ext_flag,
	case when exists (SELECT 1 FROM wx_t_role i JOIN wx_t_userrole i1 on i.role_id=i1.role_id WHERE i.role_password_power=2 and i1.user_id=#{loginUserId}) then 2 else 1 end change_pwd_permission
	, 0 user_flag
FROM wx_t_user u1
join wx_t_organization o1 on u1.org_id=o1.id
left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=o1.id
left join wx_t_organization oo1 on oo1.id=pe1.ext_property1
where u1.status=1 and (u1.type is null or u1.type != '1') and u1.user_id != #{loginUserId} and o1.type=3
and exists (select 1 <include refid="selectOrgUsers_cond"/> and oo1.id=o_t.id)
<if test="keyWord != null and keyWord != ''">
and (u1.ch_name like ('%' + #{keyWord} + '%') or u1.login_name like ('%' + #{keyWord} + '%'))
</if>
<if test="chName != null and chName != ''">
and u1.ch_name like ('%' + #{chName} + '%')
</if>
<if test="orgId != null and orgIdInCond == null">
	and u1.org_id=#{orgId}
</if>
<if test="orgIdInCond != null">
and u1.org_id IN ${orgIdInCond}
</if>
<if test="loginName != null and loginName != ''">
and u1.login_name like ('%' + #{loginNames} + '%')
</if>
<if test="loginOrgId != null">
and (u1.org_id=#{loginOrgId} or oo1.id=#{loginOrgId})
</if>
	
	</if>
</select>
<select id="selectWsUsers" parameterType="com.sys.auth.model.UserParams" resultMap="BaseResultMap">
SELECT u_t.user_id, u_t.login_name, u_t.user_no, u_t.password, u_t.salt, u_t.pwd_lasttime, u_t.ch_name, u_t.cn_name, u_t.pinyin,
	u_t.allow_login, u_t.sex, u_t.birthday, u_t.address, u_t.email, u_t.mobile_tel, u_t.fixed_tel, u_t.org_id,u_t.postion,u_t.postion_name, u_t.user_intime,
	u_t.user_outtime, u_t.description,u_t.device_id,device_type,u_t.receive_msg, u_t.is_valid, u_t.xz_time, u_t.xz_user, u_t.xg_time, u_t.xg_user, u_t.status,
	u_t.type, ws_t.work_shop_name org_name, case when exists (SELECT 1 FROM wx_t_role i JOIN wx_t_userrole i1 on i.role_id=i1.role_id WHERE i.role_password_power=2 and i1.user_id=#{loginUserId}) then 2 else 1 end change_pwd_permission
FROM wx_t_user u_t
join wx_t_work_shop ws_t on u_t.org_id=ws_t.id
where u_t.status=1 and u_t.type='1' and u_t.user_id != #{loginUserId}
<if test="keyWord != null and keyWord != ''">
and (u_t.ch_name like ('%' + #{keyWord} + '%') or u_t.login_name like ('%' + #{keyWord} + '%'))
</if>
<if test="chName != null and chName != ''">
and u_t.ch_name like ('%' + #{chName} + '%')
</if>
<if test="loginName != null and loginName != ''">
and u_t.login_name like ('%' + #{loginName} + '%')
</if>
<if test="workshopId != null">
and u_t.org_id=#{workshopId}
</if>
	<if test="orgId != null">
		and exists (select 1 from wx_t_workshop_partner i1 where i1.partner_id=#{orgId,jdbcType=BIGINT} and i1.workshop_id=u_t.org_id)
	</if>
	<if test="loginUserType == 'SP'">
		and (not exists (select 1 from wx_t_user_charge_region xx_001_ucr where xx_001_ucr.user_id=#{loginUserId}) or exists (select 1 from wx_t_work_shop xx_001_ws join wx_t_user_charge_region xx_001_ucr on xx_001_ucr.charge_region_id=xx_001_ws.region_id where xx_001_ucr.user_id=#{loginUserId} and xx_001_ws.id=u_t.org_id))
	</if>
</select>


	<select id="selectMechainUser" resultMap="BaseResultMap">
	select * from wx_t_user
		where login_name in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
		#{item}
		</foreach>
</select>
	<select id="seletUsersForCtrlPage" parameterType="com.sys.auth.model.UserCtrlParams" resultMap="BaseResultMap">
	select
	<if test="isPaging == false and limit != 0">
	top ${limit}
	</if>
	tuser.*, case when tuser.type='1' then (select work_shop_name from wx_t_work_shop ws where ws.id=tuser.org_id) else
		o2.organization_name end org_name
	from wx_t_user tuser
	left join wx_t_organization o2 on o2.id=tuser.org_id and (tuser.type is null or tuser.type!='1')
		where tuser.status=1
		<if test="includeAdmin == false">
			AND tuser.user_id != 1
		</if>
		<if test="includeChevron == false">
			and (tuser.type='1' or not exists (select 1 from wx_t_organization oo1 where oo1.id=tuser.org_id and oo1.status=1 and oo1.type=0))
		</if>
		<choose>
			<when test="includeSp">
				and (tuser.type='1' or exists (select 1 from wx_t_organization oo1 left join wx_t_workshop_partner wpp1 on wpp1.partner_id=oo1.id where oo1.id=tuser.org_id and (oo1.type!=1 or (1=1 $Permission_Clause$))))
			</when>
			<otherwise>
				and (tuser.type='1' or not exists (select 1 from wx_t_organization oo1 where oo1.id=tuser.org_id and oo1.type=1)
			</otherwise>
		</choose>
		<choose>
			<when test="includeWs">
				and (tuser.type is null or tuser.type!='1' or exists (select 1 from wx_t_workshop_partner wpp1 where wpp1.workshop_id=tuser.org_id $Permission_Clause$))
			</when>
			<otherwise>
				and (tuser.type is null or tuser.type!='1')
			</otherwise>
		</choose>
			<choose>
				<when test="orgType == 2">
					and tuser.type='1'
				</when>
				<when test="orgType != null">
					and o2.type=#{orgType}
				</when>
				<when test="orgId != null">
					and (tuser.type is null or tuser.type!='1')
				</when>
			</choose>
		<choose>
			<when test="orgId != null">
				and tuser.org_id=#{orgId}
			</when>
  	    <when test="loginUserId != 1 and userRoleName == null and roleIds == null">
		        and exists (select 1 from wx_t_userrole wx_userole where 1 = 1 and exists (
							select 1 from wx_t_role r where r.status = 1 and r.preset_bj = '0'
							            and (EXISTS ( SELECT 1
										FROM wx_t_userrole ur WHERE ur.user_id = #{loginUserId} AND ur.role_id = r.role_id)
									    OR EXISTS (SELECT 1 FROM wx_t_role_manage_role rmr JOIN wx_t_userrole ur ON ur.role_id = rmr.f_manager_role_id
										WHERE ur.user_id = #{loginUserId}
									    AND rmr.f_manage_role_id = r.role_id
									)) and r.role_id = wx_userole.role_id
									) and tuser.user_id = wx_userole.user_id
									)
		    </when>
		    </choose>
		  
	<!--  
			<when test="salesChannel != null and salesChannel != ''">
and (o2.type is null or (o2.type=0 and exists (select 1 from wx_t_userrole ur left join wx_t_role r on r.role_id=ur.role_id where ur.user_id=tuser.user_id and r.sales_channel=#{salesChannel})) 
	or ((#{salesChannel} is null or not exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe 
			join wx_t_business_region_config xx_001_brc on xx_001_pe.partner_property=xx_001_brc.ext_property1 and 
			xx_001_brc.business_name='DATA_PERMISSION_EXCLUDE_SP_IN_SALES_CHANNEL' where xx_001_pe.partner_id=o2.id and xx_001_brc.sales_channel=#{salesChannel}))
	
	and (#{loginUserId} = 1 or exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe 
			left join dw_customer_region_sales_supervisor_rel xx_001_crss on xx_001_pe.distributor_id=xx_001_crss.distributor_id
			left join dw_region_sales_channel_rel xx_001_rsc on xx_001_rsc.region_name=xx_001_crss.region_name
			left join wx_t_partner_responsible_main xx_001_prm on xx_001_prm.config_type=1 and xx_001_prm.sales_cai= xx_001_crss.sales_cai
			where xx_001_pe.partner_id=o2.id and (xx_001_crss.sales_cai=#{loginCai} or xx_001_crss.suppervisor_cai=#{loginCai} or xx_001_rsc.sales_channel_name=#{loginCai} or xx_001_prm.user_id=#{loginUserId}))
		or exists (select 1 from wx_t_partner_responsible_main xx_001_prm 
			join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id 
			where xx_001_pr.partner_id=o2.id and xx_001_prm.user_id=#{loginUserId} and xx_001_prm.config_type=0))))
			</when>
			<when test="loginUserId != 1">
	and (o2.type is null or exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe 
			left join dw_customer_region_sales_supervisor_rel xx_001_crss on xx_001_pe.distributor_id=xx_001_crss.distributor_id
			left join wx_t_partner_responsible_main xx_001_prm on xx_001_prm.config_type=1 and xx_001_prm.sales_cai= xx_001_crss.sales_cai
			where xx_001_pe.partner_id=o2.id and xx_001_prm.user_id=#{loginUserId})
		or exists (select 1 from wx_t_partner_responsible_main xx_001_prm 
			join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id 
			where xx_001_pr.partner_id=o2.id and xx_001_prm.user_id=#{loginUserId} and xx_001_prm.config_type=0))
			</when>		
		</choose>
		--> 
		<if test="userName!=null and userName != ''">
				and (
				tuser.ch_name like #{userName} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(tuser.ch_name),500) LIKE #{userName} + '%'
				)
		</if>
		<if test="keyWord!=null and keyWord != ''">
				and (
				tuser.ch_name like #{keyWord} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(tuser.ch_name),500) LIKE #{keyWord} + '%'
			or tuser.login_name like #{keyWord} + '%'
				)
		</if>
		<if test="userId != null">
			and tuser.user_id=#{userId}
		</if>

		<if test="userRoleName != null">
			and EXISTS(
				SELECT 1
				FROM wx_t_userrole ur
				LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
				WHERE ur.user_id = tuser.user_id
				AND CHARINDEX(',' + r.ch_role_name + ',', ',' + #{userRoleName} + ',')>0
			)
		</if>
		<if test="roleIds != null">
		and exists (select 1 from wx_t_userrole ur where ur.user_id=tuser.user_id
		<foreach collection="roleIds" item="listItem" open=" and ur.role_id in (" close=")" separator="," >
					${listItem}
		</foreach>
		)
		</if>
</select>
<select id="selectRegionsForCtrlPage" parameterType="com.sys.auth.model.UserCtrlParams" resultMap="BaseResultMap">
	select distinct rsc.region_name, isnull(di.dic_item_name, rsc.region_name) region_name_cn,
	isnull(di.sort_numb, 0) sort_numb from [dw_region_sales_channel_rel] rsc
	join wx_t_dic_item di on di.dic_type_code='Region.' + rsc.bu and di.dic_item_code=rsc.region_name
	where exists (select 1 from dw_customer_org_sales crs 
			where rsc.region_name=crs.region
			<choose>
				<when test="spResource">
				and exists (select 1 from wx_t_organization o 
						left join wx_t_partner_o2o_enterprise pe on pe.partner_id=(case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id=o.id) else o.id end) 
						where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
				and o.id=#{partnerId}
					</if>
				)
				</when>
				<otherwise>
				and exists (select 1 from wx_t_work_shop w 
					left join wx_t_workshop_partner wp on w.id=wp.workshop_id
					left join wx_t_organization o on o.id=wp.partner_id 
					left join wx_t_partner_o2o_enterprise pe on pe.partner_id=(case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id=o.id) else o.id end)
					where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
					and o.id=#{partnerId}
					</if>
					)
				</otherwise>
			</choose>
			<if test="distributorId != null">
			and crs.distributor_id=#{distributorId}
			</if>
			<if test="salesCai != null and salesCai != ''">
			and crs.sales_cai=#{salesCai}
			</if>
			<if test="salesId != null">
			and crs.sales_cai=(select u.cai from wx_t_user u where u.user_id=#{salesId})
			</if>
			<if test="asmCai != null and asmCai != ''">
			and crs.supervisor_cai=#{asmCai}
			</if>
			<if test="asmId != null">
			and crs.supervisor_cai=(select u.cai from wx_t_user u where u.user_id=#{asmId})
			</if>
		)
		<if test="bu != null and bu != ''">
		and rsc.bu=#{bu}
		</if>
		<if test="buSalesChannel != null and buSalesChannel != ''">
		and rsc.sales_channel_name=#{buSalesChannel}
		</if>
		<if test="regionName != null and regionName != ''">
		and rsc.region_name = #{regionName}
		</if>
</select>
<select id="selectFlsrUsersForCtrlPage" parameterType="com.sys.auth.model.UserCtrlParams" resultMap="BaseResultMap">
	select distinct crs.region region_name, u.ch_name, u.cai,u.user_id,u.mobile_tel,u.email 
	from PP_MID.dbo.syn_dw_to_pp_customer_org_sales crs
	join wx_t_user u on u.status=1 and u.cai=crs.sales_cai
	where 1=1
			<choose>
				<when test="spResource">
				and exists (select 1 from wx_t_organization o 
						left join wx_t_partner_o2o_enterprise pe on pe.partner_id=(case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id=o.id) else o.id end) 
						where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
				and o.id=#{partnerId}
					</if>
				)
				</when>
				<otherwise>
				and exists (select 1 from wx_t_work_shop w 
					left join wx_t_workshop_partner wp on w.id=wp.workshop_id 
					left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
					where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
					and pe.partner_id=#{partnerId}
					</if>
					)
				</otherwise>
			</choose>
			<if test="distributorId != null">
			and crs.distributor_id=#{distributorId}
			</if>
			<if test="salesCai != null and salesCai != ''">
			and crs.sales_cai=#{salesCai}
			</if>
			<if test="salesId != null">
			and u.user_id=#{salesId}
			</if>
			<if test="asmCai != null and asmCai != ''">
			and crs.supervisor_cai=#{asmCai}
			</if>
			<if test="asmId != null">
			and crs.supervisor_cai=(select u1.cai from wx_t_user u1 where u1.user_id=#{asmId})
			</if>
		<if test="bu != null and bu != ''">
		and (select rsc.bu from [dw_region_sales_channel_rel] rsc where rsc.region_name=crs.region)=#{bu}
		</if>
		<if test="buSalesChannel != null and buSalesChannel != ''">
		and (select rsc.sales_channel_name from [dw_region_sales_channel_rel] rsc where rsc.region_name=crs.region)=#{buSalesChannel}
		</if>
		<if test="regionName != null and regionName != ''">
		and crs.region = #{regionName}
		</if>
</select>
<select id="selectAsmUsersForCtrlPage" parameterType="com.sys.auth.model.UserCtrlParams" resultMap="BaseResultMap">
	select distinct  u.ch_name, u.cai,u.user_id
	from PP_MID.dbo.syn_dw_to_pp_customer_org_sales crs
	join wx_t_user u on u.status=1 and u.cai=crs.supervisor_cai
	where 1=1
			<choose>
				<when test="spResource">
				and exists (select 1 from wx_t_organization o 
						left join wx_t_partner_o2o_enterprise pe on pe.partner_id=(case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id=o.id) else o.id end) 
						where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
				and o.id=#{partnerId}
					</if>
				)
				</when>
				<otherwise>
				and exists (select 1 from wx_t_work_shop w 
					left join wx_t_workshop_partner wp on w.id=wp.workshop_id 
					left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
					where pe.distributor_id=crs.distributor_id $Permission_Clause$
					<if test="partnerId != null">
					and pe.partner_id=#{partnerId}
					</if>
					)
				</otherwise>
			</choose>
			<if test="distributorId != null">
			and crs.distributor_id=#{distributorId}
			</if>
			<if test="salesCai != null and salesCai != ''">
			and crs.sales_cai=#{salesCai}
			</if>
			<if test="salesId != null">
			and crs.sales_cai=(select u1.cai from wx_t_user u1 where u1.user_id=#{salesId})
			</if>
			<if test="asmCai != null and asmCai != ''">
			and crs.supervisor_cai=#{asmCai}
			</if>
			<if test="asmId != null">
			and u.user_id=#{asmId}
			</if>
		<if test="bu != null and bu != ''">
		and (select rsc.bu from [dw_region_sales_channel_rel] rsc where rsc.region_name=crs.region)=#{bu}
		</if>
		<if test="buSalesChannel != null and buSalesChannel != ''">
		and (select rsc.sales_channel_name from [dw_region_sales_channel_rel] rsc where rsc.region_name=crs.region)=#{buSalesChannel}
		</if>
		<if test="excludeEC">
			and crs.region != 'EC'
		</if>
		<if test="regionName != null and regionName != ''">
		and crs.region = #{regionName}
		</if>
</select>
<select id="seletPartnerUsersForCtrlPage" parameterType="com.sys.auth.model.UserCtrlParams" resultMap="BaseResultMap">
	select tuser.*, o.id partner_id from wx_t_user tuser
	left join wx_t_organization o on o.id=tuser.org_id
	where tuser.status=1 and (tuser.type is null or tuser.type != '1')
	<choose>
		<when test="includeRetailer == null or includeRetailer == 0">
	and o.type=1
		</when>
		<otherwise>
	and o.type in (1, 3)
		</otherwise>
	</choose>
	<choose>
		<when test="spResource">
		$Permission_Clause$
		</when>
		<otherwise>
		and exists (select 1 from wx_t_workshop_partner wpp1 where wpp1.partner_id=tuser.org_id $Permission_Clause$)
		</otherwise>
	</choose>
		<if test="userRoleName != null">
			and EXISTS(
				SELECT 1
				FROM wx_t_userrole ur
				LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
				WHERE ur.user_id = tuser.user_id
				AND r.ch_role_name =#{userRoleName}
			)
		</if>
		<if test="roleIds != null">
		and exists (select 1 from wx_t_userrole ur where ur.user_id=tuser.user_id
		<foreach collection="roleIds" item="listItem" open=" and ur.role_id in (" close=")" separator="," >
					${listItem}
		</foreach>
		)
		</if>
	<if test="orgId != null">
	and o.id=#{orgId}
	</if>
		<if test="keyWord!=null and keyWord != ''">
				and (
				tuser.ch_name like #{keyWord} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(tuser.ch_name),500) LIKE #{keyWord} + '%'
			or tuser.login_name like #{keyWord} + '%'
				)
		</if>
</select>
<select id="selectUserByCai" parameterType="map" resultMap="BaseResultMap">
	select * from wx_t_user where cai=#{cai} and status=1
</select>

<select id="getChevronSuperManagerInfo" parameterType="map" resultMap="BaseResultMap">
    SELECT u.* 
	from wx_t_user u 
	join wx_t_organization o on o.id=u.org_id
	left join wx_t_userrole ur on u.user_id=ur.user_id
	left join wx_t_role r on r.role_id=ur.role_id
	where o.type=1 and (u.type is null or u.type != '1')
	and r.ch_role_name in ( 'Service_Partner_Manager')
	and u.status=1 
	AND u. org_id = #{partnerId}
</select>

<resultMap id="CustomerRegionUserResultMap" type="com.chevron.promote.model.CustomerRegionUser" >
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="login_name" property="loginName" jdbcType="VARCHAR" />
    <result column="ch_name" property="chName" jdbcType="VARCHAR" />
    <result column="marketing_region_name" property="marketingRegionName" jdbcType="VARCHAR" />
    <result column="channel_region_name" property="channelRegionName" jdbcType="VARCHAR" />
    <result column="supervisor_region_name" property="supervisorRegionName" jdbcType="VARCHAR" />
    <result column="sales_region_name" property="salesRegionName" jdbcType="VARCHAR" />
    <result column="marketing_region_manager_cai" property="marketingRegionManagerCai" jdbcType="VARCHAR" />
    <result column="channel_manager_cai" property="channelManagerCai" jdbcType="VARCHAR" />
    <result column="suppervisor_cai" property="supervisorCai" jdbcType="VARCHAR" />
    <result column="sales_cai" property="salesCai" jdbcType="VARCHAR" />
    <result column="cai" property="cai" jdbcType="VARCHAR" />
  </resultMap>
  
  <select id="getCustomerRegionUserInfo" parameterType="map" resultMap="CustomerRegionUserResultMap" >
    SELECT DISTINCT tt_user.user_id,tt_user.login_name,tt_user.ch_name,tt_user.cai,
    t_region_config.region_alias channel_region_name,
    t_region_config1.region_alias supervisor_region_name,
    t_region_config2.region_alias sales_region_name,
    dw_sales_channel.channel_manager_cai,
    dw_c_s_supervisor.suppervisor_cai,
    dw_c_s_sales.sales_cai  
    FROM wx_t_user tt_user 
    LEFT JOIN dw_region_sales_channel_rel dw_sales_channel
    ON dw_sales_channel.channel_manager_cai = tt_user.cai
    LEFT JOIN wx_t_business_region_config t_region_config 
    ON t_region_config.region_name = dw_sales_channel.region_name and t_region_config.business_name = #{regionType}
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_supervisor
    ON dw_c_s_supervisor.suppervisor_cai = tt_user.cai
    LEFT JOIN wx_t_business_region_config t_region_config1
    ON  t_region_config1.region_name = dw_c_s_supervisor.region_name and t_region_config1.business_name = #{regionType}
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_sales 
    ON dw_c_s_sales.sales_cai = tt_user.cai
    LEFT JOIN wx_t_business_region_config t_region_config2
    ON  t_region_config2.region_name = dw_c_s_sales.region_name and t_region_config2.business_name = #{regionType}
    WHERE tt_user.user_id = #{userId}
  </select>
  
  
  <select id="getMarketingMobile" parameterType="map" resultMap="BaseResultMap" >
    SELECT * FROM wx_t_user t_user
	INNER  JOIN wx_t_dic_item t_dic_item
	ON t_dic_item.dic_item_code = t_user.login_name
	WHERE t_user.region_name = #{regionName}
  </select>
  
  <select id="getSupervisorUser" parameterType="map" resultMap="BaseResultMap" >
	SELECT * FROM wx_t_user u WHERE u.status=1 
	<if test="currentUserCai != null">
	AND cai = (
	SELECT DISTINCT t_region_sales_supervisor.suppervisor_cai
	FROM dw_customer_region_sales_supervisor_rel t_region_sales_supervisor
	WHERE sales_cai = #{currentUserCai})
	</if>
	<if test="salesChannel != null">
		and exsits (select 1 from dw_customer_region_sales_supervisor_rel crss2 
			left join dw_region_sales_channel_rel rsc2 on rsc2.region_name=crss2.region_name
			where rsc2.sales_channel_name=#{salesChannel})
	</if>
  
  </select>
  
  <select id="getChevronSupervisorUserByRole" parameterType="map" resultMap="BaseResultMap" >
	SELECT * FROM wx_t_user u WHERE u.status=1 
	and exists (select 1 from dw_sales_role sr1 where sr1.del_flag=0 and sr1.supervisor_cai=u.cai
	<choose>
		<when test="salesCai != null and salesCai != ''">
		and sr1.sales_cai=#{salesCai}
		</when>
		<otherwise>
		and sr1.sales_cai=(select u1.cai from wx_t_user u1 where u1.user_id=#{salesId})
		</otherwise>
	</choose>
	)
	<if test="currentUserCai != null">
	AND cai = (
	SELECT DISTINCT t_region_sales_supervisor.suppervisor_cai
	FROM dw_customer_region_sales_supervisor_rel t_region_sales_supervisor
	WHERE sales_cai = #{currentUserCai})
	</if>
	<if test="salesChannel != null">
		and exsits (select 1 from dw_customer_region_sales_supervisor_rel crss2 
			left join dw_region_sales_channel_rel rsc2 on rsc2.region_name=crss2.region_name
			where rsc2.sales_channel_name=#{salesChannel})
	</if>
  
  </select>
  
  <select id="getSalesBySupervisor" parameterType="map" resultMap="BaseResultMap" >
	SELECT * FROM wx_t_user u WHERE status=1 and exists (select 1 from dw_customer_region_sales_supervisor_rel crss2 
		where crss2.suppervisor_cai=#{supervisorCai} and crss2.sales_cai=u.cai)
  </select>
  
  <select id="getChannelManagerOrSupervisorUser" parameterType="map" resultMap="BaseResultMap" >
    SELECT * FROM wx_t_user WHERE user_id IN (
	SELECT DISTINCT  t_dis.distribution_user_id FROM  wx_promote_distribution t_dis
	WHERE t_dis.accept_user_id = #{currentUserId})
  </select>
  
  <select id="getV2SupervisorUser" parameterType="map" resultMap="BaseResultMap" >
	SELECT
		* 
	FROM
		wx_t_user 
	WHERE
		cai = (
	SELECT DISTINCT
		crss.suppervisor_cai 
	FROM
		dw_customer_region_sales_supervisor_rel crss
		JOIN wx_t_user tu ON crss.sales_cai = tu.cai 
	WHERE
		tu.user_id = #{currentUserId})
  </select>
  
    <select id="getV2MktManagerUser" parameterType="map" resultMap="BaseResultMap" >
	SELECT
		* 
	FROM
		wx_t_user 
	WHERE
		cai = (
	SELECT DISTINCT
		crss.suppervisor_cai 
	FROM
		dw_customer_region_sales_supervisor_rel crss
		JOIN wx_t_user tu ON crss.sales_cai = tu.cai 
	WHERE
		tu.role_id = #{currentUserId})
  </select>
  
  <select id="getV2RegionManagerUser" parameterType="map" resultMap="BaseResultMap" >
	SELECT
	* 
	FROM
		wx_t_user 
	WHERE
		cai = (
	SELECT TOP
		1 
		u.cai
	FROM
		wx_t_user u
		LEFT JOIN wx_t_userrole ur ON u.user_id = ur.user_id
		LEFT JOIN wx_t_role r ON ur.role_id = r.role_id 
	WHERE
		u.status = 1 
		AND r.ch_role_name = ( SELECT p0.code FROM wx_t_properties p0 WHERE p0.codetype= 'promote.mktRegionApproverRole' ) 
		AND EXISTS (
	SELECT
		1 
	FROM
		dw_customer_region_sales_supervisor_rel xx_001_crss
		
		LEFT JOIN dw_region_sales_channel_rel xx_001_rsc ON xx_001_rsc.region_name= xx_001_crss.region_name
		LEFT JOIN wx_t_partner_responsible_main xx_001_prm ON xx_001_prm.config_type= 1 
		AND xx_001_prm.sales_cai= xx_001_crss.sales_cai 
		LEFT JOIN wx_t_user xx_001_tu ON xx_001_tu.cai= xx_001_crss.suppervisor_cai
	WHERE
		xx_001_tu.user_id = #{currentUserId}
		AND xx_001_prm.user_id = u.user_id 
		) 
	ORDER BY
		u.user_id DESC)
  </select>
  
  <select id="getFifteenDaysNoLoginUser" resultMap="BaseResultMap" >
    SELECT u.user_id, u.login_name, u.ch_name
	FROM wx_t_user u
	LEFT JOIN wx_t_userrole ur ON u.user_id = ur.user_id 
	LEFT JOIN wx_t_role r ON ur.role_id = r.role_id
	WHERE u.user_id NOT IN 
	(SELECT distinct l.user_id FROM wx_t_log l WHERE l.log_type = 1 AND l.xg_sj >= DATEDIFF(DAY, 15, GETDATE()))
	AND
	r.block_informer = '1'
	AND
	u.allow_login = 'T'
	AND
	u.status = 1
  </select>
  
  <select id="findUserWithAllowLogin" resultMap="BaseResultMap" >
  	SELECT
		u.*,
		(select o.organization_name org_name from wx_t_organization o  where o.id= u.org_id ) as org_name,
		(select f.att_id from wx_att_file f where f.source_type='2' and f.source_id =u.user_id ) as photo_id,
		case when u.cai is not null and u.cai!='' then (select top 1 sales_channel_name from (select rsc.sales_channel_name
			from dw_region_sales_channel_rel rsc 
			where rsc.channel_manager_cai=u.cai
			union all
			select rsc.sales_channel_name
			from dw_customer_region_sales_supervisor_rel crss
			left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
			where crss.sales_cai=u.cai or crss.suppervisor_cai=u.cai) biu) else null end sales_channel
	FROM wx_t_user u
		WHERE u.login_name = #{loginName, jdbcType=VARCHAR}
		AND u.status = 1
  </select>
  <select id="getPartnerUserByOrgIdAndRoleName" parameterType="map" resultMap="BaseResultMap" >
    select  * from wx_t_user tb_user 
		LEFT JOIN wx_t_userrole t_userrole  ON t_userrole.user_id = tb_user.user_id
		LEFT JOIN wx_t_role tb_role  ON tb_role.role_id = t_userrole.role_id
	WHERE tb_user.org_id = #{partnerId} AND tb_role.ch_role_name = #{roleName}
  </select>
  
  <resultMap id="ExportUserMap" type="com.sys.auth.model.vo.ExportUserVo">
  	<id column="user_id" property="userId" jdbcType="BIGINT" />
	<result column="ch_name" jdbcType="NVARCHAR" property="userName" />
	<result column="login_name" jdbcType="NVARCHAR" property="loginName" />
	<result column="org_name" jdbcType="NVARCHAR" property="orgName" />
  </resultMap>
  
  <select id="getExportUser" parameterType="java.lang.Long" resultMap="ExportUserMap" >
  	SELECT 
  	u.user_id,
  	u.login_name,
  	u.ch_name,
  	org.organization_name as org_name
	FROM wx_t_user u
    LEFT JOIN wx_t_organization org ON u.org_id = org.id
    WHERE 
    u.status=1 
    AND u.user_id != #{userId, jdbcType=BIGINT}
	AND (u.type is null or u.type != '1')
	AND (exists (select 1 from wx_t_organization o5 where o5.type=0) 
		or not exists (select 1 from wx_t_partner_responsible_main xx_001_prm where xx_001_prm.user_id=#{userId, jdbcType=BIGINT})
		or exists (select 1 from wx_t_partner_responsible_main xx_001_prm join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id where xx_001_pr.partner_id=u.org_id and xx_001_prm.user_id=#{userId, jdbcType=BIGINT}))
  </select>
  <select id="getChevronUserInfo" parameterType="map" resultMap="BaseResultMap" >
  select top 1 * from (
select distinct rsc.bu, 'BuManager' user_model, null sales_channel from dw_region_sales_channel_rel rsc where rsc.bu_manager_cai=#{cai}
union all 
select distinct rsc.bu, 'ChannelManager' user_model, rsc.sales_channel_name sales_channel from dw_region_sales_channel_rel rsc where rsc.channel_manager_cai=#{cai}
union all
select distinct rsc.bu, 'Supervisor' user_model, rsc.sales_channel_name sales_channel from dw_customer_region_sales_supervisor_rel crss
left join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name where crss.suppervisor_cai=#{cai} and crss.sales_cai!=#{cai}
union all
select distinct rsc.bu, 'Sales' user_model, rsc.sales_channel_name sales_channel from dw_customer_region_sales_supervisor_rel crss
left join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name where crss.sales_cai=#{cai}) a
  </select>
  
<select id="getUserFineByUserId" parameterType="map" resultType="java.lang.Long">
	SELECT top 1 id FROM wx_t_user_define where user_id = #{userId}
</select>
<insert id="insertUserDeFine">
	INSERT INTO wx_t_user_define (user_id) VALUES (#{userId})
</insert>
<select id="getChevronUserFlag" parameterType="map" resultType="int">
	SELECT (case when exists (select 1 from dw_sales_role sr where sr.sales_role='Team Leader'
				<if test="salesId != null">
				and exists (select 1 from wx_t_user u left join dw_sales_role sr1 on u.cai=sr1.sales_cai where u.user_id=#{salesId} and sr1.supervisor_cai=sr.sales_cai)
				</if>
				) then 1 else 0 end
			)
</select>
<select id="getPartnerAdmins" resultMap="BaseResultMap">
        select DISTINCT t1.* from wx_t_user t1
        inner join wx_t_userrole t2 on t1.user_id = t2.user_id
        inner join wx_t_role t3 on t3.role_id  = t2.role_id
        INNER JOIN wx_t_partner_o2o_enterprise p ON t1.org_id = p.partner_id
		INNER JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON cos.distributor_id = p.distributor_id
        where t1.status = 1 and t2.status  = 1 and t3.status = 1
        <if test="partnerId != null">
            and t1.org_id = #{partnerId}
        </if>
         <if test="distributorId != null">
            and cos.distributor_id = #{distributorId}
        </if>
        <if test="roles !=null and roles.size > 0">
            and t3.ch_role_name in
            <foreach collection="roles" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>
        </if>
    </select>
    
    <update id="changeCustomerExcuteUserId" parameterType="map">
    	UPDATE  w set w.excute_user_id =(SELECT top 1  u1.user_id FROM  wx_t_user u1 
		left join wx_t_user u2 on u1.org_id  = u2.org_id 
		left join wx_t_userrole ur on u1.user_id = ur.user_id 
		left join wx_t_role r on  ur.role_id  = r.role_id
		where 1 = 1 and u1.status  = 1 and u2.status  = 1 and  (r.user_flag &amp; 32) >0
		and u1.user_id  != u2.user_id and u2.user_id  = #{userId, jdbcType=BIGINT} ORDER  BY u2.xz_time)
		from wx_t_work_shop w  where w.excute_user_id =  #{userId, jdbcType=BIGINT}  
    </update>

	<resultMap id="userMap" type="com.sys.auth.model.WxTUser" >
		<!--
        WARNING - @mbggenerated
        This element is automatically generated by MyBatis Generator, do not modify.
        This element was generated on Wed Jun 03 10:58:07 CST 2015.
        -->
		<id column="user_id" property="userId" jdbcType="BIGINT" />
		<result column="login_name" property="loginName" jdbcType="VARCHAR" />
		<result column="user_no" property="userNo" jdbcType="VARCHAR" />
		<result column="password" property="password" jdbcType="VARCHAR" />
		<result column="salt" property="salt" jdbcType="VARCHAR" />
		<result column="pwd_lasttime" property="pwdLasttime" jdbcType="TIMESTAMP" />
		<result column="ch_name" property="chName" jdbcType="VARCHAR" />
		<result column="cn_name" property="cnName" jdbcType="VARCHAR" />
		<result column="pinyin" property="pinyin" jdbcType="VARCHAR" />
		<result column="allow_login" property="allowLogin" jdbcType="VARCHAR" />
		<result column="sex" property="sex" jdbcType="VARCHAR" />
		<result column="birthday" property="birthday" jdbcType="TIMESTAMP" />
		<result column="address" property="address" jdbcType="VARCHAR" />
		<result column="email" property="email" jdbcType="VARCHAR" />
		<result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
		<result column="fixed_tel" property="fixedTel" jdbcType="VARCHAR" />
		<result column="photo_id" property="photoId" jdbcType="BIGINT" />
		<result column="org_id" property="orgId" jdbcType="BIGINT" />
		<result column="org_type" property="orgType" jdbcType="INTEGER" />
		<result column="org_name"   property="orgName" jdbcType="VARCHAR" />
		<result column="postion"   property="postion" jdbcType="VARCHAR"  />
		<result column="postion_name"   property="postionName" jdbcType="VARCHAR"  />
		<result column="branch_name"   property="branchName" />
		<result column="branch_id"   property="branchId" />
		<result column="user_intime" property="userIntime" jdbcType="TIMESTAMP" />
		<result column="user_outtime" property="userOuttime" jdbcType="TIMESTAMP" />
		<result column="description" property="description" jdbcType="VARCHAR" />
		<result column="device_id" property="deviceId" jdbcType="VARCHAR" />
		<result column="device_type" property="deviceType" jdbcType="VARCHAR" />
		<result column="receive_msg" property="receiveMsg" jdbcType="INTEGER" />
		<result column="post" property="post" jdbcType="BIGINT" />
		<result column="is_valid" property="isValid" jdbcType="VARCHAR" />
		<result column="xz_time" property="xzTime" jdbcType="TIMESTAMP" />
		<result column="xz_user" property="xzUser" jdbcType="VARCHAR" />
		<result column="xg_time" property="xgTime" jdbcType="TIMESTAMP" />
		<result column="xg_user" property="xgUser" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="INTEGER" />
		<result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
		<result column="ch_role_name"   property="chRoleName" />
		<result column="type" property="type" jdbcType="VARCHAR" />
		<result column="change_pwd_permission" property="changePwdPermission" jdbcType="INTEGER" />
		<result column="reset_flag" property="resetFlag" jdbcType="INTEGER" />
		<result column="cai" property="cai" jdbcType="VARCHAR"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<!-- 针对C&I marketing region name -->
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="user_model" property="userModel" jdbcType="VARCHAR"/>
		<result column="default_locale" property="defaultLocale" jdbcType="VARCHAR"/>
	</resultMap>

	<select id="getUserListByDsrIds" resultMap="userMap" parameterType="map">
		SELECT
		u.*
		FROM
		wx_t_user u
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = u.org_id
		LEFT JOIN  view_customer_region_sales_channel crsc ON crsc.distributor_id = pe.distributor_id
		WHERE
		1 =1
		AND crsc.channel_weight &amp; 3 &gt; 0
		<if test="dsrIds != null and dsrIds.size > 0">
			and u.user_id IN
			<foreach collection="dsrIds" item="dsrId" index="index" open="(" close=")" separator=",">
				#{dsrId}
			</foreach>
		</if>

	</select>
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		SELECT u.user_id, u.login_name, u.user_no, u.password, u.salt, u.pwd_lasttime, u.ch_name, u.cn_name, u.pinyin,
	u.allow_login, u.sex, u.birthday, u.address, u.email, u.mobile_tel, u.fixed_tel, u.org_id,postion,postion_name, u.user_intime,
	u.user_outtime, u.description,u.device_id,u.device_type,u.receive_msg, u.is_valid, u.xz_time, u.xz_user, u.xg_time, u.xg_user, u.status,
	u.tenant_id,u.post, u.type, u.cai,u.region_name
	<choose>
		<when test="funFlag == 'QueryByTobeExpireEmail'">
		 ,isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))
						- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),CONVERT(varchar(100), GETDATE(), 23)) ext_flag/*距离到期天数=密码有效期 - 上次密码修改距今时长*/
		 ,CONVERT(varchar(100), dateadd(day, convert(int,isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))), (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id)), 111) ext_property1/*距离到期日期=上次密码修改日期+密码有效期*/
		 ,isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)) ext_property2/*密码有效期*/
		</when>
		<otherwise>
	,u.ext_flag, null ext_property1, null ext_property2
		</otherwise>	
	</choose>
		FROM
		wx_t_user u
		WHERE 1 =1
		<choose>
			<when test="funFlag == 'QueryFlsrByDistributor'">
		and exists (select 1 from dw_customer_org_sales cos1 where cos1.distributor_id=#{funParam1} and cos1.sales_cai=u.cai and u.status=1 and cos1.channel_weight&amp;#{funParam2}>0)	
			</when>
			<when test="funFlag == 'QueryFlsrByPartner'">
		and exists (select 1 from dw_customer_org_sales cos1 left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=cos1.distributor_id where pe.partner_id=#{funParam1} and cos1.sales_cai=u.cai and u.status=1 and cos1.channel_weight&amp;#{funParam2}>0)	
			</when>
			<when test="funFlag == 'QueryByTobeExpireEmail'">
		 and u.reset_flag=0 and u.status=1 and u.ext_flag&amp;12=4/*将过期且未停用账号*/
			and CHARINDEX(',' + cast(isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))
						 - datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),CONVERT(varchar(100), GETDATE(), 23)/*邮件发送和打标记都统一成0点*/) as varchar) + ','
				,(select ',' + di1.dic_item_desc + ',' from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='expireEmailNotifyDays' and di1.status=1))>0
			</when>
			<when test="funFlag == 'QueryByFreezeNotifyEmail'">
		 and u.status=1 /*距将停用时间该提醒时 且 未停用账号，排除集成账号*/
                and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where r1.ch_role_name='Chevron_System_Integration' and ur1.user_id=u.user_id and ur1.user_id!=1)
                and CHARINDEX(',' + cast((select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='frozenLimitDays' and di1.status=1)
						 - datediff(day, (case when exists (select 1 from wx_t_log ll1 where ll1.user_id=u.user_id and ll1.xg_sj>'2022-12-01')
								then (select top 1 ll1.xg_sj from wx_t_log ll1 where ll1.user_id=u.user_id order by ll1.log_id desc) 
								when u.xz_time>'2022-12-01' then u.xz_time else '2022-12-01' end),CONVERT(varchar(100), GETDATE(), 23)/*邮件发送和打标记都统一成0点*/) as varchar) + ','
				,(select ',' + di1.dic_item_desc + ',' from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='frozenLimitNotifyDays' and di1.status=1))>0
			</when>
			<when test="funFlag == 'QueryByFrozenNotifyEmail'">
		 and u.status=1 and u.ext_flag&amp;16>0 /*未停用账号 and 还未发送停用提醒邮件*/
			</when>
		</choose>
	</select>
	<select id="queryUserRoleForMktResource" resultMap="BaseResultMap" parameterType="map">
		select role_name user_model, ch_name from (
			select distinct di.dic_item_name role_name, di.sort_numb, u.ch_name from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id left join wx_t_role r on r.role_id=ur.role_id
			left join wx_t_dic_item di on di.dic_type_code='MktResourcePlatform.roleMap' and di.dic_item_code=r.ch_role_name
			where u.mobile_tel=#{mobile} and u.status=1
			union all select distinct 'Distributor' role_name, 99910, u.ch_name sort_numb from wx_t_user u left join wx_t_organization o on o.id=u.org_id where u.mobile_tel=#{mobile} and u.status=1 and (u.type is null or u.type!='1')
			and o.type=1
			union all select distinct 'Retailer' role_name, 99920, u.ch_name  from wx_t_user u left join wx_t_organization o on o.id=u.org_id where u.mobile_tel=#{mobile} and u.status=1 
			and o.type=3
			union all select 'Mechanic' role_name, 99930, we.name from wx_t_workshop_employee we where we.mobile=#{mobile}) a order by a.sort_numb
	</select>
	<select id="queryBuUsersByDistributor" resultMap="BaseResultMap" parameterType="map">
	select distinct 'flsr' user_model, u.* from dw_customer_org_sales cr left join wx_t_user u on cr.sales_cai=u.cai and u.status=1
	where 
	<choose>
		<when test="distributorId != null">
		cr.distributor_id=#{distributorId}
		</when>
		<otherwise>
		exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.distributor_id=cr.distributor_id and pe.partner_id=#{partnerId})
		</otherwise>
	</choose>
	<if test="channelWeight != null">
	and cr.channel_weight&amp;#{channelWeight}>0
	</if>
	union all 
	select distinct 'asm' user_model, u.* from dw_customer_org_sales cr left join wx_t_user u on cr.supervisor_cai=u.cai and u.status=1
	where 
	<choose>
		<when test="distributorId != null">
		cr.distributor_id=#{distributorId}
		</when>
		<otherwise>
		exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.distributor_id=cr.distributor_id and pe.partner_id=#{partnerId})
		</otherwise>
	</choose>
	union all 
	select distinct 'abm' user_model, u.* from dw_customer_org_sales cr 
	left join dw_region_sales_channel_rel sr on sr.region_name=cr.region
	left join wx_t_user u on sr.bu_manager_cai=u.cai and u.status=1
	where 
	<choose>
		<when test="distributorId != null">
		cr.distributor_id=#{distributorId}
		</when>
		<otherwise>
		exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.distributor_id=cr.distributor_id and pe.partner_id=#{partnerId})
		</otherwise>
	</choose>
	</select>
	<select id="getRegionMktByDistributor" resultMap="BaseResultMap" parameterType="map">
	select distinct u.* from dw_customer_org_sales cr 
	left join wx_t_partner_responsible_main m on m.region_name=cr.region
	left join wx_t_user u on u.user_id=m.user_id
	where 
	<choose>
		<when test="distributorId != null">
		cr.distributor_id=#{distributorId}
		</when>
		<otherwise>
		exists (select 1 from wx_t_partner_o2o_enterprise pe where pe.distributor_id=cr.distributor_id and pe.partner_id=#{partnerId})
		</otherwise>
	</choose>
	 and u.status=1 and u.login_name != '18018075828'
	 and m.fun_flag='mkt_specialist'
	</select>
	<select id="querySalesRegions" resultType="string" parameterType="map">
	select distinct region from 
	<choose>
		<when test="biSource == true">
	[PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] cr
		</when>
		<otherwise>
	dw_customer_org_sales cr 
		</otherwise>
	</choose>
	where 1=1
	<if test="salesCai != null and salesCai != ''">
	and cr.sales_cai=#{salesCai}
	</if>
	</select>
<update id="updateByParamsSelective" parameterType="map" >
	update u
	<set >
	<if test="record.userId != null" >
		user_id = #{record.userId,jdbcType=BIGINT},
	</if>
	<if test="record.loginName != null" >
		login_name = #{record.loginName,jdbcType=VARCHAR},
	</if>
	<if test="record.userNo != null" >
		user_no = #{record.userNo,jdbcType=VARCHAR},
	</if>
	<if test="record.password != null" >
		password = #{record.password,jdbcType=VARCHAR},
	</if>
	<if test="record.salt != null" >
		salt = #{record.salt,jdbcType=VARCHAR},
	</if>
	<if test="record.pwdLasttime != null" >
		pwd_lasttime = #{record.pwdLasttime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.chName != null" >
		ch_name = #{record.chName,jdbcType=VARCHAR},
	</if>
	<if test="record.cnName != null" >
		cn_name = #{record.cnName,jdbcType=VARCHAR},
	</if>
	<if test="record.pinyin != null" >
		pinyin = #{record.pinyin,jdbcType=VARCHAR},
	</if>
	<if test="record.allowLogin != null" >
		allow_login = #{record.allowLogin,jdbcType=VARCHAR},
	</if>
	<if test="record.sex != null" >
		sex = #{record.sex,jdbcType=VARCHAR},
	</if>
	<if test="record.birthday != null" >
		birthday = #{record.birthday,jdbcType=TIMESTAMP},
	</if>
	<if test="record.address != null" >
		address = #{record.address,jdbcType=VARCHAR},
	</if>
	<if test="record.email != null" >
		email = #{record.email,jdbcType=VARCHAR},
	</if>
	<if test="record.mobileTel != null" >
		mobile_tel = #{record.mobileTel,jdbcType=VARCHAR},
	</if>
	<if test="record.fixedTel != null" >
		fixed_tel = #{record.fixedTel,jdbcType=VARCHAR},
	</if>
	<if test="record.orgId != null" >
		org_id = #{record.orgId,jdbcType=BIGINT},
	</if>
	<if test="record.postion != null" >
		postion = #{record.postion,jdbcType=BIGINT},
	</if>
	<if test="record.userIntime != null" >
		user_intime = #{record.userIntime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.userOuttime != null" >
		user_outtime = #{record.userOuttime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.description != null" >
		description = #{record.description,jdbcType=VARCHAR},
	</if>
	<if test="record.deviceId != null" >
		device_id = #{record.deviceId,jdbcType=VARCHAR},
	</if>
	<if test="record.deviceType != null" >
		device_type = #{record.deviceType,jdbcType=VARCHAR},
	</if>
	<if test="record.receiveMsg != null" >
		receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
	</if>
	<if test="record.isValid != null" >
		is_valid = #{record.isValid,jdbcType=VARCHAR},
	</if>
	<if test="record.xzTime != null" >
		xz_time = #{record.xzTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.xzUser != null" >
		xz_user = #{record.xzUser,jdbcType=VARCHAR},
	</if>
	<if test="record.xgTime != null" >
		xg_time = #{record.xgTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.xgUser != null" >
		xg_user = #{record.xgUser,jdbcType=VARCHAR},
	</if>
	<if test="record.status != null" >
		status = #{record.status,jdbcType=INTEGER},
	</if>
	<if test="record.tenantId != null" >
		tenant_id = #{record.tenantId,jdbcType=BIGINT},
	</if>
	<if test="record.type != null" >
		type = #{record.type,jdbcType=VARCHAR},
	</if>
			<if test="record.cai != null">
				cai = #{record.cai,jdbcType=VARCHAR},
			</if>
			<if test="record.regionName != null">
				region_name = #{record.regionName,jdbcType=VARCHAR},
			</if>
			<choose>
				<when test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
				</when>
				<when test="record.addExtFlag != null and record.removeExtFlag != null">
				ext_flag = (ext_flag|#{record.addExtFlag,jdbcType=INTEGER}) - (ext_flag&amp;#{record.removeExtFlag,jdbcType=INTEGER}),
				</when>
				<when test="record.addExtFlag != null">
				ext_flag = ext_flag|#{record.addExtFlag,jdbcType=INTEGER},
				</when>
				<when test="record.removeExtFlag != null">
				ext_flag = ext_flag - (ext_flag&amp;#{record.removeExtFlag,jdbcType=INTEGER}),
				</when>
			</choose>
	</set>
	from wx_t_user u where 1=1 
	<choose>
		<when test="params.opFlag == 'markUserStopped'">
		/** 排除disabled账号，已停用账号，系统集成账号 and 账号未登录时长 > 未登录限制天数 */
		 and u.status=1 and u.ext_flag&amp;8=0
		 and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where r1.ch_role_name='Chevron_System_Integration' and ur1.user_id=u.user_id and ur1.user_id!=1)
		and datediff(day, (case when exists (select 1 from wx_t_log ll1 where ll1.user_id=u.user_id and ll1.xg_sj>'2022-12-01')
								then (select top 1 ll1.xg_sj from wx_t_log ll1 where ll1.user_id=u.user_id order by ll1.log_id desc) 
								when u.xz_time>'2022-12-01' then u.xz_time else '2022-12-01' end) ,getdate())
				>(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='frozenLimitDays' and di1.status=1)
		</when>
		<when test="params.opFlag == 'markPwdExpired'">
		/** 排除首次登录用户，disabled账号，密码已过期账号，系统集成账号 and 最新密码修改时长 > 密码有效期*/
		 and (u.reset_flag=0 or u.reset_flag is null) and u.status=1
		 and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where r1.ch_role_name='Chevron_System_Integration' and ur1.user_id=u.user_id and ur1.user_id!=1)
			and datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate())
				>isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id),
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1))
		</when>
		<when test="params.opFlag == 'markPwdTobeExpire'">
		/** 排除首次登录用户，disabled账号，密码已过期和即将过期账号*/
		 and (u.reset_flag=0 or u.reset_flag is null) and u.status=1 and u.ext_flag&amp;6=0
		 /** 密码有效期 - 最新密码修改时长 &lt;= 开始逾期提醒天数 */
			and isnull((select max(convert(int, di1.dic_item_desc)) from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 left join wx_t_dic_item di1 on di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay.'+r1.ch_role_name and di1.status=1 where ur1.user_id=u.user_id), 
					(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='effectiveDay' and di1.status=1)) 
				- datediff(day, (select max(pcl1.create_time) from wx_t_pwd_change_log pcl1 where pcl1.user_id=u.user_id),getdate())
				&lt;=(select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='User.pwdPolicy' and di1.dic_item_code='expireNotifyDay' and di1.status=1)
		</when>
		<otherwise>
		and 1!=1
		</otherwise>
	</choose>
</update>
	<select id="countKaByOrgId" parameterType="map" resultType="java.lang.Integer">
		SELECT count(1)
		FROM [PP_MID].[dbo].[syn_dw_to_pp_customer] where customer_type='KA' and partner_id=#{orgId, jdbcType=BIGINT}
	</select>

	<select id="selectRoleByUserId" resultType="java.lang.Long">
		SELECT role.role_id
		FROM wx_t_user u
				 left join wx_t_userrole ur on u.user_id = ur.user_id
				 left join wx_t_role role on ur.role_id = role.role_id
		where u.user_id = #{curUserId,jdbcType=BIGINT}
	</select>

	<select id="selectRegionByUser" resultType="java.lang.String">
		select DISTINCT region
		from wx_t_user u
				 left join dw_customer_org_sales dcos
						   on u.login_name  = dcos.supervisor_cai
		where u.user_id = #{curUserId,jdbcType=BIGINT}
	</select>

	<select id="getOrgBossUser" resultMap="BaseResultMap">
		select * from wx_t_user wtu WHERE
			wtu.status=1 and is_valid = '1' and org_id = #{orgId}
		and EXISTS (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id and r1.status=1 where ur1.user_id=wtu.user_id and r1.user_flag &amp; 32>0)
		ORDER BY wtu.user_id asc
	</select>

	<select id="getRoleByUser" resultType="java.lang.String">
		select wtr.ch_role_name from
		wx_t_userrole usrr left join wx_t_role wtr
		on usrr.role_id = wtr.role_id
		where user_id = #{userId,jdbcType=BIGINT}
    </select>

	<select id="listUserByUserId" resultMap="BaseResultMap">
		select * from wx_t_user
		where user_id in
		<foreach item="item" index="index" collection="userIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getFlsrccEmailByOrg" resultType="java.lang.String">
		SELECT TOP 1 ccEmail from (
		select top 1 1 'sort', u.email 'ccEmail'
		from wx_t_partner_o2o_enterprise pe
		left join dw_customer_org_sales v on pe.distributor_id=v.distributor_id
		left join wx_t_user u on  u.cai=v.sales_cai
		where pe.partner_id=#{orgId}
		UNION all
		select top 1 2 'sort', u.email 'ccEmail'
		from  dw_customer_org_sales v
		join wx_t_user u on u.cai=v.supervisor_cai
		where v.sales_cai =#{cai}
		)t WHERE t.ccEmail is NOT NULL order by t.sort

	</select>
</mapper>