<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTMenuMapper" >

  <resultMap id="BaseResultMap" type="com.sys.auth.model.WxTMenu" >
    <id column="menu_id" property="menuId" jdbcType="BIGINT" />
    <result column="menu_pid" property="menuPid" jdbcType="BIGINT" />
    <result column="menu_name" property="menuName" jdbcType="VARCHAR" />
    <result column="menu_url" property="menuUrl" jdbcType="VARCHAR" />
    <result column="menu_imgpath" property="menuImgpath" jdbcType="VARCHAR" />
    <result column="preset_bj" property="presetBj" jdbcType="VARCHAR" />
    <result column="xg_sj" property="xgSj" jdbcType="TIMESTAMP" />
    <result column="xg_user" property="xgUser" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="CHAR" />
    <result column="add_flag" property="addFlag" jdbcType="VARCHAR" />
    <result column="del_flag" property="delFlag" jdbcType="VARCHAR" />
    <result column="update_flag" property="updateFlag" jdbcType="VARCHAR" />
    <result column="view_flag" property="viewFlag" jdbcType="VARCHAR" />
    <result column="menu_code" property="menuCode" jdbcType="VARCHAR" />
    <result column="i18n_code" property="i18nCode" jdbcType="VARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="check_flag" property="checkFlag" jdbcType="INTEGER" />
    <result column="ext_flag" property="extFlag" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap id="RoleMenuMap" type="com.sys.auth.model.WxRoleMenu" >
    <result column="menu_id" property="menuId" jdbcType="BIGINT" />
    <result column="menu_pid" property="menuPid" jdbcType="BIGINT" />
    <result column="menu_code" property="menuCode" jdbcType="VARCHAR" />    
    <result column="menu_name" property="menuName" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="CHAR" />
    <result column="add_flag" property="addFlag" jdbcType="VARCHAR" />
    <result column="del_flag" property="delFlag" jdbcType="VARCHAR" />
    <result column="update_flag" property="updateFlag" jdbcType="VARCHAR" />
    <result column="view_flag" property="viewFlag" jdbcType="VARCHAR" />
    <result column="menu_url" property="menuUrl" jdbcType="VARCHAR"/>
  </resultMap>
  
  <sql id="Example_Where_Clause" >
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    menu_id, menu_pid, menu_name, menu_url, menu_imgpath, preset_bj, xg_sj, xg_user, 
    status, tenant_id,sort,i18n_code
  </sql>
   <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTMenuExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_menu
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_menu
    where menu_id = #{menuId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_menu
    where menu_id = #{menuId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sys.auth.model.WxTMenuExample" >
    delete from wx_t_menu
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sys.auth.model.WxTMenu" >
    insert into wx_t_menu (menu_id, menu_pid, menu_name, 
      menu_url, menu_imgpath, preset_bj, 
      xg_sj, xg_user, status, 
      tenant_id,sort)
    values (#{menuId,jdbcType=BIGINT}, #{menuPid,jdbcType=BIGINT}, #{menuName,jdbcType=VARCHAR}, 
      #{menuUrl,jdbcType=VARCHAR}, #{menuImgpath,jdbcType=VARCHAR}, #{presetBj,jdbcType=VARCHAR}, 
      #{xgSj,jdbcType=TIMESTAMP}, #{xgUser,jdbcType=BIGINT}, #{status,jdbcType=BIGINT}, 
      #{tenantId,jdbcType=CHAR},#{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="menuId" parameterType="com.sys.auth.model.WxTMenu" >
    insert into wx_t_menu
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="menuId != null" >
        menu_id,
      </if>
      <if test="menuPid != null" >
        menu_pid,
      </if>
      <if test="menuName != null" >
        menu_name,
      </if>
      <if test="menuUrl != null" >
        menu_url,
      </if>
      <if test="menuImgpath != null" >
        menu_imgpath,
      </if>
      <if test="presetBj != null" >
        preset_bj,
      </if>
      <if test="xgSj != null" >
        xg_sj,
      </if>
      <if test="xgUser != null" >
        xg_user,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
      <if test="sort != null" >
        sort,
      </if>
      <if test="i18nCode != null" >
        i18n_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="menuId != null" >
        #{menuId,jdbcType=BIGINT},
      </if>
      <if test="menuPid != null" >
        #{menuPid,jdbcType=BIGINT},
      </if>
      <if test="menuName != null" >
        #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="menuUrl != null" >
        #{menuUrl,jdbcType=VARCHAR},
      </if>
      <if test="menuImgpath != null" >
        #{menuImgpath,jdbcType=VARCHAR},
      </if>
      <if test="presetBj != null" >
        #{presetBj,jdbcType=VARCHAR},
      </if>
      <if test="xgSj != null" >
        #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null" >
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=CHAR},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="i18nCode != null" >
        #{i18nCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sys.auth.model.WxTMenuExample" resultType="java.lang.Integer" >
    select count(*) from wx_t_menu
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_menu
    <set >
      <if test="record.menuId != null" >
        menu_id = #{record.menuId,jdbcType=BIGINT},
      </if>
      <if test="record.menuPid != null" >
        menu_pid = #{record.menuPid,jdbcType=BIGINT},
      </if>
      <if test="record.menuName != null" >
        menu_name = #{record.menuName,jdbcType=VARCHAR},
      </if>
      <if test="record.menuUrl != null" >
        menu_url = #{record.menuUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.menuImgpath != null" >
        menu_imgpath = #{record.menuImgpath,jdbcType=VARCHAR},
      </if>
      <if test="record.presetBj != null" >
        preset_bj = #{record.presetBj,jdbcType=VARCHAR},
      </if>
      <if test="record.xgSj != null" >
        xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xgUser != null" >
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=CHAR},
      </if>
      <if test="record.sort != null" >
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_menu
    set menu_id = #{record.menuId,jdbcType=BIGINT},
      menu_pid = #{record.menuPid,jdbcType=BIGINT},
      menu_name = #{record.menuName,jdbcType=VARCHAR},
      menu_url = #{record.menuUrl,jdbcType=VARCHAR},
      menu_imgpath = #{record.menuImgpath,jdbcType=VARCHAR},
      preset_bj = #{record.presetBj,jdbcType=VARCHAR},
      xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      status = #{record.status,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=CHAR},
      sort = #{record.sort,jdbcType=INTEGER},
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sys.auth.model.WxTMenu" >
    update wx_t_menu
    <set >
      <if test="menuPid != null" >
        menu_pid = #{menuPid,jdbcType=BIGINT},
      </if>
      <if test="menuName != null" >
        menu_name = #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="menuUrl != null" >
        menu_url = #{menuUrl,jdbcType=VARCHAR},
      </if>
      <if test="menuImgpath != null" >
        menu_imgpath = #{menuImgpath,jdbcType=VARCHAR},
      </if>
      <if test="presetBj != null" >
        preset_bj = #{presetBj,jdbcType=VARCHAR},
      </if>
      <if test="xgSj != null" >
        xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null" >
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=CHAR},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="menuCode != null" >
      	menu_code = #{menuCode,jdbcType=TIMESTAMP},
      </if>
      <if test="i18nCode != null" >
        i18n_code = #{i18nCode,jdbcType=VARCHAR},
      </if>
        <if test="allowShortcut != null " >
            <choose>
                <when test="allowShortcut">
                    ext_flag = (ISNULL(ext_flag,0) | 1),
                </when>
                <otherwise>
                    ext_flag = <![CDATA[(ISNULL(ext_flag,0) & (~1))]]>,
                </otherwise>
            </choose>
        </if>


    </set>
    where menu_id = #{menuId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sys.auth.model.WxTMenu" >
    update wx_t_menu
    set menu_pid = #{menuPid,jdbcType=BIGINT},
      menu_name = #{menuName,jdbcType=VARCHAR},
      menu_url = #{menuUrl,jdbcType=VARCHAR},
      menu_imgpath = #{menuImgpath,jdbcType=VARCHAR},
      preset_bj = #{presetBj,jdbcType=VARCHAR},
      xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      xg_user = #{xgUser,jdbcType=BIGINT},
      status = #{status,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=CHAR},
      sort=#{sort,jdbcType=INTEGER}
    where menu_id = #{menuId,jdbcType=BIGINT}
  </update>
  
  <select id="selMenuByPermissions" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTRolesource" >
      select DISTINCT m.*, add_flag as add_flag  from (
		select source_id, max(add_flag) add_flag from (
		(
		select DISTINCT rs.source_id,rs.add_flag from wx_t_userrole ur ,
			wx_t_rolesource rs where 
			ur.user_id=#{xgUser,jdbcType=BIGINT} 
			and ur.role_id = rs.role_id 
			and rs.rs_type=#{rsType,jdbcType=INTEGER}
		)
		UNION
		(
		select DISTINCT ub.source_id,ub.add_flag from wx_t_userbase ub
		where user_id=#{xgUser,jdbcType=BIGINT}
		and ub.rs_type=#{rsType,jdbcType=INTEGER}
		)
	   ) 
		a
		where not exists (select 1 from wx_t_user u join wx_t_organization o on o.id=u.org_id and o.type=1 where u.user_id=#{xgUser,jdbcType=BIGINT} and (u.type is null or u.type != 1))/*非合伙人用户*/ or
		not exists (select 1 from wx_t_business_fun_resource bfr1 join wx_t_business_fun bf1 on bfr1.business_fun_code=bf1.business_fun_code and bf1.enable_flag=1 where bfr1.resource_type=2 and bfr1.resource_id=a.source_id) /*非合伙人业务菜单*/ or
		exists (select 1 from wx_t_user u 
		join wx_t_organization o on o.id=u.org_id and o.type=1
		join wx_t_dealer_business_fun dbf1 on dbf1.dealer_id=o.id
		join wx_t_business_fun bf1 on bf1.business_fun_code=dbf1.business_fun_code and bf1.enable_flag=1
		join wx_t_business_fun_resource bfr1 on bfr1.business_fun_code=bf1.business_fun_code
		where u.user_id=#{xgUser,jdbcType=BIGINT} and (u.type is null or u.type != 1)
		and bfr1.resource_type=2 and bfr1.resource_id=a.source_id and (bf1.business_fun_type is null or bf1.business_fun_type!='CUSTOM' 
		 or exists (select 1 from wx_t_dealer_business_custom dbc1 where dbc1.id=dbf1.business_custom_id and dbc1.enable_flag=1 
		 and (dbc1.start_date is null or dbc1.start_date&lt;=getdate()) and (dbc1.end_date is null or dbc1.end_date &gt;=getdate())))) /*所属合伙人包含业务功能*/
 	group by source_id
	) source
	 INNER JOIN wx_t_menu m on m.menu_id=source.source_id and m.status = 1 Order by m.sort asc
  </select>
  <select id="selRoleSourceByPermissions" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTRolesource" >
  select m.*, case when exists (select 1 from wx_t_rolesource where role_id = #{roleId,jdbcType=BIGINT} and rs_type = 2 and 
		  tenant_id = #{tenantId,jdbcType=CHAR} and status = 1 and m.menu_id=source_id) then 1 else 0 end check_flag
  from wx_t_menu m
  where m.status = 1
  and (exists (select 1 from wx_t_userrole ur join wx_t_role r on ur.role_id = r.role_id 
  						join wx_t_rolesource rs on r.role_id = rs.role_id and rs.rs_type=2 
  						where ur.user_id = #{xgUser,jdbcType=BIGINT} and rs.source_id=m.menu_id and rs.status = 1) 
  		or exists (select 1 from wx_t_userbase where user_id = #{xgUser,jdbcType=BIGINT} and rs_type = 2 and 
		  tenant_id = #{tenantId,jdbcType=CHAR} and status = 1 and source_id=m.menu_id))
  order by m.sort asc
  </select>
  
  <!-- <select id="selRoleSourceByPermissions" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTRolesource" >
      SELECT DISTINCT
	m.*, source.add_flag,
	source.del_flag,
	source.update_flag,
	source.view_flag
FROM
	(
		SELECT DISTINCT
			rs1.source_id AS source_id,
			rs2.add_flag AS add_flag,
			rs2.del_flag AS del_flag,
			rs2.update_flag AS update_flag,
			rs2.view_flag AS view_flag
		FROM
			(
				SELECT DISTINCT
					rs.source_id
				FROM
					wx_t_userrole ur,
					wx_t_role r,
					wx_t_rolesource rs
				WHERE
				 ur.user_id = #{xgUser,jdbcType=BIGINT} 
				AND ur.role_id = r.role_id
				AND r.role_id = rs.role_id
				AND rs.rs_type = #{rsType,jdbcType=BIGINT} 
			) AS rs1
		LEFT JOIN (
			SELECT DISTINCT
				rs_t.source_id,
				rs_t.add_flag,
				rs_t.del_flag,
				rs_t.update_flag,
				rs_t.view_flag
			FROM
				wx_t_rolesource rs_t
			WHERE
				rs_t.rs_type = #{rsType,jdbcType=BIGINT} 
			AND rs_t.role_id = #{roleId,jdbcType=BIGINT} 
		) AS rs2 ON rs1.source_id = rs2.source_id
	) AS source
INNER JOIN wx_t_menu m ON m.menu_id = source.source_id
AND m. STATUS = 1
  </select> -->
  
  <select id="selMenuByHavePermissions" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTRolesource" >
    select * from wx_t_menu where menu_id in (
		select source_id from wx_t_rolesource where role_id = #{roleId,jdbcType=BIGINT} and rs_type = #{rsType,jdbcType=INTEGER} and 
		  tenant_id = #{tenantId,jdbcType=CHAR} and status = 1
	)
  </select>
  
  <select id="selMenuToUser" resultMap="BaseResultMap" parameterType="com.sys.auth.model.WxTUserbase" >
    select * from wx_t_menu where menu_id in (
		select source_id from wx_t_userbase where user_id = #{userId,jdbcType=BIGINT} and rs_type = #{rsType,jdbcType=INTEGER} and 
		  tenant_id = #{tenantId,jdbcType=CHAR} and status = 1
	)
  </select>
  
  <select id="selAllMenu" resultMap="BaseResultMap">
    select * from wx_t_menu order by sort
  </select>
  
  <select id="selectCountAllChildMenu" parameterType="java.lang.Long" resultType="int">
  	select count(*) from wx_t_menu where menu_pid=#{menuId};
   </select>
  <select id="queryByParams" resultMap="BaseResultMap" parameterType="com.sys.auth.model.MenuParams" >
    select * from wx_t_menu m where m.status = 1 and not exists (select 1 from wx_t_menu m1 where m1.menu_pid=m.menu_id)
    <if test="menuName != null and menuName != ''">
    and (left(dbo.f_GetPyToAboutHanyu(m.menu_name),500) like '%' + #{menuName} + '%' or m.menu_name like '%' + #{menuName} + '%')
    </if>
  </select>
</mapper>