<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTPermissionMapper">
    <resultMap id="BaseResultMap" type="com.sys.auth.model.WxTPermission" >
        <id column="permission_id" property="permissionId" jdbcType="BIGINT" />
        <result column="permission_key" property="permissionKey" jdbcType="NVARCHAR" />
        <result column="permission_name" property="permissionName" jdbcType="NVARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="visible" property="visible" jdbcType="INTEGER" />
        <result column="common_permission" property="commonPermission" jdbcType="INTEGER" />
        <result column="permission_source" property="permissionSource" jdbcType="NVARCHAR" />
        <result column="creator" property="creator" jdbcType="NVARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updator" property="updator" jdbcType="NVARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remark" property="remark" jdbcType="NVARCHAR" />
        <result column="permission_subject" property="permissionSubject" jdbcType="NVARCHAR" />
        <result column="used_page" property="usedPage" jdbcType="NVARCHAR" />
    </resultMap>

    <select id="selectPermissionById" parameterType="com.sys.auth.model.WxTPermission" resultMap="BaseResultMap">
        select * from wx_t_permission WHERE status = 1 and permission_id = ${permissionId}
    </select>

    <select id="selectAllPermission" parameterType="com.sys.auth.model.WxTPermission" resultMap="BaseResultMap">
        select * from wx_t_permission WHERE status = 1
    </select>

    <select id="selectPermissionByOwnerId" parameterType="com.sys.auth.model.WxTPermission" resultMap="BaseResultMap">
        select a.* from wx_t_permission a
        WHERE a.status = 1
        and a.permission_id in (
            select b.permission_id from wx_t_ownerpermission b
            where b.status = 1 and b.owner_id in
            <foreach collection="ownerList" item="owner_id" separator="," open="(" close=")">
                #{owner_id}
            </foreach>
            )

        UNION

        select a.* from wx_t_permission a
        WHERE a.status = 1 and a.common_permission = 1
    </select>

    <select id="selectPermissionKeyByOwnerId" resultType="java.util.Map">
        select distinct p.permission_key as permissionKey FROM (
          select a.* from wx_t_permission a
          WHERE a.status = 1
            and a.permission_id in (
              select b.permission_id from wx_t_ownerpermission b
              where b.status = 1 and b.owner_id in
                <foreach collection="ownerList" item="owner_id" separator="," open="(" close=")">
                    #{owner_id}
                </foreach>
          )

          union

          select a.* from wx_t_permission a
          where a.status = 1 and a.common_permission = 1
      ) p

    </select>
</mapper>