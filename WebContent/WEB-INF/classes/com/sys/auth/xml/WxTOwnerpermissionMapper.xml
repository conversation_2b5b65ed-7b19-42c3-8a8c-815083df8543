<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTOwnerpermissionMapper">
    <resultMap id="BaseResultMap" type="com.sys.auth.model.WxTOwnerpermission" >
        <id column="ownerpermission_id" property="ownerpermissionId" jdbcType="BIGINT" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT" />
        <result column="owner_type" property="ownerType" jdbcType="VARCHAR" />
        <result column="permission_id" property="permissionId" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="creator" property="creator" jdbcType="NVARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updator" property="updator" jdbcType="NVARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="remark" property="remark" jdbcType="NVARCHAR" />
    </resultMap>
    <select id="selectPermissionByUserId" parameterType="com.sys.auth.model.WxTOwnerpermission" resultMap="BaseResultMap">
        select * from wx_t_ownerpermission WHERE owner_type = 'USER' and status = 1 and owner_id = ${userId}
    </select>

    <select id="selectPermissionByRoleId" parameterType="com.sys.auth.model.WxTOwnerpermission" resultMap="BaseResultMap">
        select * from wx_t_ownerpermission WHERE owner_type = 'ROLE' and status = 1 and owner_id = ${roleId}
    </select>

    <select id="selectPermissionByOwnerId" parameterType="com.sys.auth.model.WxTOwnerpermission" resultMap="BaseResultMap">
        select * from wx_t_ownerpermission WHERE status = 1 and owner_id = ${ownerId}
    </select>
</mapper>