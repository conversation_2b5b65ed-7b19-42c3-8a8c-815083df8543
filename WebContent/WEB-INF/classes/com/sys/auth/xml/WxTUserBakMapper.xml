<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.auth.dao.WxTUserBakMapper" >
  <resultMap id="BaseResultMap" type="com.sys.auth.model.WxTUserBak" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 03 10:58:07 CST 2015.
    -->
    <id column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="login_name" property="loginName" jdbcType="VARCHAR" />
    <result column="user_no" property="userNo" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="ch_name" property="chName" jdbcType="VARCHAR" />
    <result column="allow_login" property="allowLogin" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="VARCHAR" />
    <result column="birthday" property="birthday" jdbcType="TIMESTAMP" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
    <result column="postion" property="postion" jdbcType="VARCHAR"  />
    <result column="postion_name"   property="postionName" jdbcType="VARCHAR"  />
    <result column="user_intime" property="userIntime" jdbcType="TIMESTAMP" />
    <result column="post" property="post" jdbcType="BIGINT" />   
    <result column="xz_time" property="xzTime" jdbcType="TIMESTAMP" />
    <result column="xz_user" property="xzUser" jdbcType="VARCHAR" />
    <result column="xg_time" property="xgTime" jdbcType="TIMESTAMP" />
    <result column="xg_user" property="xgUser" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
    <result column="temp_flag" property="tempFlag" jdbcType="BIGINT" />
  </resultMap>
  <select id="selMaxIdData" parameterType="map" resultMap="BaseResultMap">
  	select * from wx_t_user_bak where user_id = (select max(user_id) from wx_t_user_bak)
  </select>
  <delete id="delAllUserByTempFlag">
  	delete from wx_t_user_bak where temp_flag = #{tempFlag, jdbcType=BIGINT}
  </delete>
  <insert id="save">
  	insert into wx_t_user_bak 
  		(user_id, login_name, user_no, password, salt, ch_name, allow_login, sex, birthday, address, email, mobile_tel, org_id, org_name,
  		postion, postion_name, user_intime, post, xz_time, xz_user, xg_time, xg_user, status, tenant_id, temp_flag) values
  		 (#{userId, jdbcType=BIGINT}, #{loginName, jdbcType=BIGINT}, #{userNo, jdbcType=VARCHAR}, #{password, jdbcType=VARCHAR}, #{salt, jdbcType=VARCHAR},
  		 #{chName, jdbcType=VARCHAR}, #{allowLogin, jdbcType=VARCHAR}, #{sex, jdbcType=VARCHAR}, #{birthday, jdbcType=TIMESTAMP},
  		 #{address, jdbcType=VARCHAR}, #{email, jdbcType=VARCHAR}, #{mobileTel, jdbcType=VARCHAR}, #{orgId, jdbcType=BIGINT},
  		 #{orgName, jdbcType=VARCHAR}, #{postion, jdbcType=VARCHAR}, #{postionName, jdbcType=VARCHAR}, #{userIntime, jdbcType=TIMESTAMP},
  		 #{post, jdbcType=VARCHAR}, getdate(), #{tenantId, jdbcType=BIGINT}, getdate(),
  		 #{tenantId, jdbcType=BIGINT}, 1, #{tenantId, jdbcType=BIGINT}, #{tempFlag, jdbcType=BIGINT})
  </insert>
  <select id="findUserByLoginName" parameterType="map" resultType="java.lang.Integer">
  	select count(*) from wx_t_user_bak where login_name = #{loginName, jdbcType=VARCHAR} and temp_flag = #{tempFlag, jdbcType=BIGINT}
  	and tenant_id = #{tenantId, jdbcType=BIGINT} and status = 1
  </select>
  
  <select id="findUserByUserNo" parameterType="map" resultType="java.lang.Integer">
  	select count(*) from wx_t_user_bak where user_no = #{userNo, jdbcType=VARCHAR} and temp_flag = #{tempFlag, jdbcType=BIGINT}
  	and tenant_id = #{tenantId, jdbcType=BIGINT} and status = 1
  </select>
  
  <insert id="copyUserBakToUser">
  	INSERT INTO wx_t_user (user_id, login_name, user_no, password, salt, ch_name, allow_login, sex, birthday, address, email, mobile_tel, org_id, postion, user_intime, post, xz_time, xz_user, xg_time, xg_user, status, tenant_id, postion_name) 
		SELECT user_id, login_name, user_no, password, salt, ch_name, allow_login, sex, birthday, address, email, mobile_tel, org_id, postion, user_intime, post, xz_time, xz_user, xg_time, xg_user, status, tenant_id, postion_name  FROM wx_t_user_bak
		where temp_flag = #{tempFlag, jdbcType=BIGINT}
  </insert>
</mapper>