<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.log.dao.DeviceLocationLogMapper">
  <resultMap id="BaseResultMap" type="com.sys.log.model.DeviceLocationLog">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
    <result column="device_code" property="deviceCode" jdbcType="VARCHAR"/>
    <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
    <result column="longitude" property="longitude" jdbcType="NUMERIC"/>
    <result column="latitude" property="latitude" jdbcType="NUMERIC"/>
    <result column="operator" property="operator" jdbcType="BIGINT"/>
    <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
    <result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
    <result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
    <result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
    <result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id,device_type,device_code,device_name,longitude,latitude,operator,operator_name,create_time,ext_property1,
    ext_property2,ext_property3,ext_property4,ext_property5
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.log.model.DeviceLocationLogExample">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from wx_device_location_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.sys.log.model.DeviceLocationLogExample">
    delete from wx_device_location_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sys.log.model.DeviceLocationLog">
    insert into wx_device_location_log (id,device_type,device_code,device_name,longitude,latitude,operator,operator_name,create_time,ext_property1,
      ext_property2,ext_property3,ext_property4,ext_property5)
    values (#{id,jdbcType=BIGINT},#{device_type,jdbcType=VARCHAR},#{device_code,jdbcType=VARCHAR},#{device_name,jdbcType=VARCHAR},#{longitude,jdbcType=NUMERIC},
      #{latitude,jdbcType=NUMERIC},#{operator,jdbcType=BIGINT},#{operator_name,jdbcType=VARCHAR},#{create_time,jdbcType=TIMESTAMP},#{ext_property1,jdbcType=VARCHAR},
      #{ext_property2,jdbcType=VARCHAR},#{ext_property3,jdbcType=VARCHAR},#{ext_property4,jdbcType=VARCHAR},#{ext_property5,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sys.log.model.DeviceLocationLog">
    insert into wx_device_location_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="deviceType != null" >
        device_type,
      </if>
      <if test="deviceCode != null" >
        device_code,
      </if>
      <if test="deviceName != null" >
        device_name,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="operator != null" >
        operator,
      </if>
      <if test="operatorName != null" >
        operator_name,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="extProperty1 != null" >
        ext_property1,
      </if>
      <if test="extProperty2 != null" >
        ext_property2,
      </if>
      <if test="extProperty3 != null" >
        ext_property3,
      </if>
      <if test="extProperty4 != null" >
        ext_property4,
      </if>
      <if test="extProperty5 != null" >
        ext_property5,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="deviceType != null" >
        #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null" >
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null" >
        #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null" >
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extProperty1 != null" >
        #{extProperty1,jdbcType=VARCHAR},
      </if>
      <if test="extProperty2 != null" >
        #{extProperty2,jdbcType=VARCHAR},
      </if>
      <if test="extProperty3 != null" >
        #{extProperty3,jdbcType=VARCHAR},
      </if>
      <if test="extProperty4 != null" >
        #{extProperty4,jdbcType=VARCHAR},
      </if>
      <if test="extProperty5 != null" >
        #{extProperty5,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sys.log.model.DeviceLocationLogExample">
    select count(1) from wx_device_location_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.sys.log.model.DeviceLocationLog">
    update wx_device_location_log
    <set>
      <if test="deviceType != null" >
        device_type = #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null" >
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceName != null" >
        device_name = #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="operator != null" >
        operator = #{operator,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null" >
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extProperty1 != null" >
        ext_property1 = #{extProperty1,jdbcType=VARCHAR},
      </if>
      <if test="extProperty2 != null" >
        ext_property2 = #{extProperty2,jdbcType=VARCHAR},
      </if>
      <if test="extProperty3 != null" >
        ext_property3 = #{extProperty3,jdbcType=VARCHAR},
      </if>
      <if test="extProperty4 != null" >
        ext_property4 = #{extProperty4,jdbcType=VARCHAR},
      </if>
      <if test="extProperty5 != null" >
        ext_property5 = #{extProperty5,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_device_location_log
    <set>
      <if test="record.deviceType != null" >
        device_type = #{record.deviceType,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceCode != null" >
        device_code = #{record.deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceName != null" >
        device_name = #{record.deviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.longitude != null" >
        longitude = #{record.longitude,jdbcType=NUMERIC},
      </if>
      <if test="record.latitude != null" >
        latitude = #{record.latitude,jdbcType=NUMERIC},
      </if>
      <if test="record.operator != null" >
        operator = #{record.operator,jdbcType=BIGINT},
      </if>
      <if test="record.operatorName != null" >
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extProperty1 != null" >
        ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
      </if>
      <if test="record.extProperty2 != null" >
        ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
      </if>
      <if test="record.extProperty3 != null" >
        ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
      </if>
      <if test="record.extProperty4 != null" >
        ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
      </if>
      <if test="record.extProperty5 != null" >
        ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.log.model.DeviceLocationLogParams">
<![CDATA[
    select t1.id,t1.device_type,t1.device_code,t1.device_name,t1.longitude,t1.latitude,t1.operator,
           t1.operator_name,t1.create_time,t1.ext_property1,t1.ext_property2,t1.ext_property3,t1.ext_property4,t1.ext_property5
      from wx_device_location_log t1
     where 1=1
]]>
  </select>
</mapper>
