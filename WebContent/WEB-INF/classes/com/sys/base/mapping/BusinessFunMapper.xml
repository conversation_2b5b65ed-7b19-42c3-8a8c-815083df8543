<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.base.dao.BusinessFunMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.base.model.BusinessFun">
		<id column="business_fun_code" property="businessFunCode" jdbcType="VARCHAR"/>
		<result column="business_fun_name" property="businessFunName" jdbcType="VARCHAR"/>
		<result column="business_fun_type" property="businessFunType" jdbcType="VARCHAR"/>
		<result column="business_fun_type_text" property="businessFunTypeText" jdbcType="VARCHAR"/>
		<result column="func_code" property="funcCode" jdbcType="VARCHAR"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="order_sequence" property="orderSequence" jdbcType="NUMERIC"/>
		<result column="remarks" property="remarks" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		business_fun_code,business_fun_name,business_fun_type,func_code,enable_flag,order_sequence,remarks,create_user_id,
		create_time,update_user_id,update_time
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.base.model.BusinessFun">
		update wx_t_business_fun
		<set>
			<if test="businessFunName != null" >
				business_fun_name = #{businessFunName,jdbcType=VARCHAR},
			</if>
			<if test="businessFunType != null" >
				business_fun_type = #{businessFunType,jdbcType=VARCHAR},
			</if>
			<if test="funcCode != null" >
				func_code = #{funcCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null" >
				order_sequence = #{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null" >
				remarks = #{remarks,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where business_fun_code = #{businessFunCode,jdbcType=VARCHAR}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.base.model.BusinessFunExample">
    	delete from wx_t_business_fun
		where business_fun_code = #{businessFunCode,jdbcType=VARCHAR}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessFunExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_business_fun
		where business_fun_code = #{businessFunCode,jdbcType=VARCHAR}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.base.model.BusinessFun">
		insert into wx_t_business_fun
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="businessFunCode != null">
				business_fun_code,
			</if>
			<if test="businessFunName != null">
				business_fun_name,
			</if>
			<if test="businessFunType != null">
				business_fun_type,
			</if>
			<if test="funcCode != null">
				func_code,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="orderSequence != null">
				order_sequence,
			</if>
			<if test="remarks != null">
				remarks,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="businessFunCode != null">
				#{businessFunCode,jdbcType=VARCHAR},
			</if>
			<if test="businessFunName != null">
				#{businessFunName,jdbcType=VARCHAR},
			</if>
			<if test="businessFunType != null">
				#{businessFunType,jdbcType=VARCHAR},
			</if>
			<if test="funcCode != null">
				#{funcCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null">
				#{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null">
				#{remarks,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_business_fun
		<set>
			<if test="businessFunName != null">
				business_fun_name = #{businessFunName,jdbcType=VARCHAR},
			</if>
			<if test="businessFunType != null">
				business_fun_type = #{businessFunType,jdbcType=VARCHAR},
			</if>
			<if test="funcCode != null">
				func_code = #{funcCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null">
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null">
				order_sequence = #{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null">
				remarks = #{remarks,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.base.model.BusinessFunExample">
		delete from wx_t_business_fun
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.base.model.BusinessFunExample">
		select count(1) from wx_t_business_fun
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessFunExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_business_fun
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.business_fun_code, t1.business_fun_name, t1.business_fun_type, t1.func_code, t1.enable_flag,
			 t1.order_sequence, t1.remarks, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='BusinessFun.businessFunType' and di1.dic_item_code=t1.business_fun_type) business_fun_type_text
		  from wx_t_business_fun t1
		 where 1=1
		<if test="businessFunCode != null and businessFunCode != ''">
			and t1.business_fun_code like '%' + #{businessFunCode, jdbcType=VARCHAR} + '%'
		</if>
		<if test="businessFunName != null and businessFunName != ''">
			and t1.business_fun_name like '%' + #{businessFunName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="businessFunType != null and businessFunType != ''">
			and t1.business_fun_type = #{businessFunType, jdbcType=VARCHAR}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessFunParams">
		select t1.business_fun_code, t1.business_fun_name, t1.business_fun_type, t1.func_code, t1.enable_flag,
			 t1.order_sequence, t1.remarks, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='BusinessFun.businessFunType' and di1.dic_item_code=t1.business_fun_type) business_fun_type_text
		  from wx_t_business_fun t1
		 where 1=1
		<if test="businessFunCode != null and businessFunCode != ''">
			and t1.business_fun_code like '%' + #{businessFunCode, jdbcType=VARCHAR} + '%'
		</if>
		<if test="businessFunName != null and businessFunName != ''">
			and t1.business_fun_name like '%' + #{businessFunName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="businessFunType != null and businessFunType != ''">
			and t1.business_fun_type = #{businessFunType, jdbcType=VARCHAR}
		</if>
	</select>
</mapper>
