<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.base.dao.BusinessModuleMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.base.model.BusinessModule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="business_module_name" property="businessModuleName" jdbcType="VARCHAR"/>
		<result column="business_module_code" property="businessModuleCode" jdbcType="VARCHAR"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="effect_on_task" property="effectOnTask" jdbcType="INTEGER"/>
		<result column="order_sequence" property="orderSequence" jdbcType="NUMERIC"/>
		<result column="remarks" property="remarks" jdbcType="VARCHAR"/>
		<result column="predefine_flag" property="predefineFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,business_module_name,business_module_code,enable_flag,effect_on_task,order_sequence,remarks,predefine_flag,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.base.model.BusinessModule">
		update wx_t_business_module
		<set>
			<if test="businessModuleName != null" >
				business_module_name = #{businessModuleName,jdbcType=VARCHAR},
			</if>
			<if test="businessModuleCode != null" >
				business_module_code = #{businessModuleCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="effectOnTask != null" >
				effect_on_task = #{effectOnTask,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null" >
				order_sequence = #{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null" >
				remarks = #{remarks,jdbcType=VARCHAR},
			</if>
			<if test="predefineFlag != null" >
				predefine_flag = #{predefineFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.base.model.BusinessModuleExample">
    	delete from wx_t_business_module
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessModuleExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_business_module
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.base.model.BusinessModule" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_business_module
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="businessModuleName != null">
				business_module_name,
			</if>
			<if test="businessModuleCode != null">
				business_module_code,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="effectOnTask != null">
				effect_on_task,
			</if>
			<if test="orderSequence != null">
				order_sequence,
			</if>
			<if test="remarks != null">
				remarks,
			</if>
			<if test="predefineFlag != null">
				predefine_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="businessModuleName != null">
				#{businessModuleName,jdbcType=VARCHAR},
			</if>
			<if test="businessModuleCode != null">
				#{businessModuleCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="effectOnTask != null">
				#{effectOnTask,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null">
				#{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null">
				#{remarks,jdbcType=VARCHAR},
			</if>
			<if test="predefineFlag != null">
				#{predefineFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_business_module
		<set>
			<if test="businessModuleName != null">
				business_module_name = #{businessModuleName,jdbcType=VARCHAR},
			</if>
			<if test="businessModuleCode != null">
				business_module_code = #{businessModuleCode,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null">
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="effectOnTask != null">
				effect_on_task = #{effectOnTask,jdbcType=INTEGER},
			</if>
			<if test="orderSequence != null">
				order_sequence = #{orderSequence,jdbcType=NUMERIC},
			</if>
			<if test="remarks != null">
				remarks = #{remarks,jdbcType=VARCHAR},
			</if>
			<if test="predefineFlag != null">
				predefine_flag = #{predefineFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.base.model.BusinessModuleExample">
		delete from wx_t_business_module
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.base.model.BusinessModuleExample">
		select count(1) from wx_t_business_module
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessModuleExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_business_module
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.business_module_name, t1.business_module_code, t1.enable_flag, t1.effect_on_task, t1.order_sequence,
			 t1.remarks, t1.predefine_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_business_module t1
		 where 1=1
		<if test="businessModuleName != null and businessModuleName != ''">
			and t1.business_module_name like '%' + #{businessModuleName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="effectOnTask != null">
			and t1.effect_on_task = #{effectOnTask, jdbcType=INTEGER}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessModuleParams">
		select t1.id, t1.business_module_name, t1.business_module_code, t1.enable_flag, t1.effect_on_task, t1.order_sequence,
			 t1.remarks, t1.predefine_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_business_module t1
		 where 1=1
		<if test="businessModuleName != null and businessModuleName != ''">
			and t1.business_module_name like '%' + #{businessModuleName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="effectOnTask != null">
			and t1.effect_on_task = #{effectOnTask, jdbcType=INTEGER}
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.business_module_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.remarks like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
	</select>
</mapper>
