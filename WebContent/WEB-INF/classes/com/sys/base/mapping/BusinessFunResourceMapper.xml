<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.base.dao.BusinessFunResourceMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.base.model.BusinessFunResource">
		<result column="resource_type" property="resourceType" jdbcType="INTEGER"/>
		<result column="resource_id" property="resourceId" jdbcType="VARCHAR"/>
		<result column="business_fun_code" property="businessFunCode" jdbcType="VARCHAR"/>
		<result column="extend_prop1" property="extendProp1" jdbcType="VARCHAR"/>
		<result column="extend_prop2" property="extendProp2" jdbcType="VARCHAR"/>
		<result column="extend_prop3" property="extendProp3" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		resource_type,resource_id,business_fun_code,extend_prop1,extend_prop2,extend_prop3
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.base.model.BusinessFunResource">
		insert into wx_t_business_fun_resource
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="resourceType != null">
				resource_type,
			</if>
			<if test="resourceId != null">
				resource_id,
			</if>
			<if test="businessFunCode != null">
				business_fun_code,
			</if>
			<if test="extendProp1 != null">
				extend_prop1,
			</if>
			<if test="extendProp2 != null">
				extend_prop2,
			</if>
			<if test="extendProp3 != null">
				extend_prop3,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="resourceType != null">
				#{resourceType,jdbcType=INTEGER},
			</if>
			<if test="resourceId != null">
				#{resourceId,jdbcType=VARCHAR},
			</if>
			<if test="businessFunCode != null">
				#{businessFunCode,jdbcType=VARCHAR},
			</if>
			<if test="extendProp1 != null">
				#{extendProp1,jdbcType=VARCHAR},
			</if>
			<if test="extendProp2 != null">
				#{extendProp2,jdbcType=VARCHAR},
			</if>
			<if test="extendProp3 != null">
				#{extendProp3,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_business_fun_resource
		<set>
			<if test="extendProp1 != null">
				extend_prop1 = #{extendProp1,jdbcType=VARCHAR},
			</if>
			<if test="extendProp2 != null">
				extend_prop2 = #{extendProp2,jdbcType=VARCHAR},
			</if>
			<if test="extendProp3 != null">
				extend_prop3 = #{extendProp3,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.base.model.BusinessFunResourceExample">
		delete from wx_t_business_fun_resource
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.base.model.BusinessFunResourceExample">
		select count(1) from wx_t_business_fun_resource
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.base.model.BusinessFunResourceExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_business_fun_resource
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.resource_type, t1.resource_id, t1.business_fun_code, t1.extend_prop1, t1.extend_prop2, t1.extend_prop3
		  from wx_t_business_fun_resource t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_business_fun_resource (resource_type, resource_id, business_fun_code, extend_prop1, extend_prop2, extend_prop3) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.resourceType, jdbcType=INTEGER}, #{item.resourceId, jdbcType=VARCHAR}, #{item.businessFunCode, jdbcType=VARCHAR}, #{item.extendProp1, jdbcType=VARCHAR}, #{item.extendProp2, jdbcType=VARCHAR}, #{item.extendProp3, jdbcType=VARCHAR}
			</trim>
		</foreach>
	</insert>
</mapper>
