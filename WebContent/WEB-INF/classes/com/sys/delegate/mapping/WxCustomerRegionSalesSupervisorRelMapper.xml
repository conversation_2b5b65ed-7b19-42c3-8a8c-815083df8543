<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.delegate.dao.WxCustomerRegionSalesSupervisorRelMapper">
  <resultMap id="BaseResultMap" type="com.sys.delegate.model.WxCustomerRegionSalesSupervisorRel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_name_cn" jdbcType="NVARCHAR" property="customerNameCn" />
    <result column="region_name" jdbcType="NVARCHAR" property="regionName" />
    <result column="sales_cai" jdbcType="NVARCHAR" property="salesCai" />
    <result column="sales_name" jdbcType="NVARCHAR" property="salesName" />
    <result column="suppervisor_cai" jdbcType="NVARCHAR" property="suppervisorCai" />
    <result column="suppervisor_name" jdbcType="NVARCHAR" property="suppervisorName" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="customer_name_en" jdbcType="NVARCHAR" property="customerNameEn" />
    <result column="available_date" jdbcType="TIMESTAMP" property="availableDate" />
    <result column="expired_date" jdbcType="TIMESTAMP" property="expiredDate" />
  </resultMap>

  <sql id="Base_Column_List">
    customer_name_cn, region_name, sales_cai, sales_name, suppervisor_cai, suppervisor_name, 
    distributor_id, customer_name_en, available_date, expired_date,id
  </sql>

</mapper>