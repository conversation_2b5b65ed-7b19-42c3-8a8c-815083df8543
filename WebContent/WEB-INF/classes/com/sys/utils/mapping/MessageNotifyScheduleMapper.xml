<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.utils.dao.MessageNotifyScheduleMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.utils.model.MessageNotifySchedule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="source_id" property="sourceId" jdbcType="BIGINT"/>
		<result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
		<result column="content" property="content" jdbcType="VARCHAR"/>
		<result column="user_id" property="userId" jdbcType="BIGINT"/>
		<result column="phone_number" property="phoneNumber" jdbcType="VARCHAR"/>
		<result column="expected_time" property="expectedTime" jdbcType="DATE"/>
		<result column="execution_time" property="executionTime" jdbcType="DATE"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="batch_flag" property="batchFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="BIT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,source_id,source_type,content,user_id,phone_number,expected_time,execution_time,status,batch_flag,delete_flag,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.sys.utils.model.MessageNotifySchedule">
		update wx_t_message_notify_schedule set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.utils.model.MessageNotifySchedule">
		update wx_t_message_notify_schedule
		<set>
			<if test="sourceId != null" >
				source_id = #{sourceId,jdbcType=BIGINT},
			</if>
			<if test="sourceType != null" >
				source_type = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="content != null" >
				content = #{content,jdbcType=VARCHAR},
			</if>
			<if test="userId != null" >
				user_id = #{userId,jdbcType=BIGINT},
			</if>
			<if test="phoneNumber != null" >
				phone_number = #{phoneNumber,jdbcType=VARCHAR},
			</if>
			<if test="expectedTime != null" >
				expected_time = #{expectedTime,jdbcType=TIMESTAMP},
			</if>
			<if test="executionTime != null" >
				execution_time = #{executionTime,jdbcType=TIMESTAMP},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="batchFlag != null" >
				batch_flag = #{batchFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=BIT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.utils.model.MessageNotifyScheduleExample">
    	delete from wx_t_message_notify_schedule
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.utils.model.MessageNotifySchedule" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_message_notify_schedule
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="sourceId != null">
				source_id,
			</if>
			<if test="sourceType != null">
				source_type,
			</if>
			<if test="content != null">
				content,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="phoneNumber != null">
				phone_number,
			</if>
			<if test="expectedTime != null">
				expected_time,
			</if>
			<if test="executionTime != null">
				execution_time,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="batchFlag != null">
				batch_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="sourceId != null">
				#{sourceId,jdbcType=BIGINT},
			</if>
			<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="content != null">
				#{content,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=BIGINT},
			</if>
			<if test="phoneNumber != null">
				#{phoneNumber,jdbcType=VARCHAR},
			</if>
			<if test="expectedTime != null">
				#{expectedTime,jdbcType=TIMESTAMP},
			</if>
			<if test="executionTime != null">
				#{executionTime,jdbcType=TIMESTAMP},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="batchFlag != null">
				#{batchFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=BIT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_message_notify_schedule
		<set>
			<if test="record.sourceId != null">
				source_id = #{record.sourceId,jdbcType=BIGINT},
			</if>
			<if test="record.sourceType != null">
				source_type = #{record.sourceType,jdbcType=VARCHAR},
			</if>
			<if test="record.content != null">
				content = #{record.content,jdbcType=VARCHAR},
			</if>
			<if test="record.userId != null">
				user_id = #{record.userId,jdbcType=BIGINT},
			</if>
			<if test="record.phoneNumber != null">
				phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
			</if>
			<if test="record.expectedTime != null">
				expected_time = #{record.expectedTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.executionTime != null">
				execution_time = #{record.executionTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.batchFlag != null">
				batch_flag = #{record.batchFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=BIT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.utils.model.MessageNotifyScheduleExample">
		delete from wx_t_message_notify_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.utils.model.MessageNotifyScheduleExample" resultType="int">
		select count(1) from wx_t_message_notify_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.utils.model.MessageNotifyScheduleExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_message_notify_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.utils.model.MessageNotifyScheduleExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_message_notify_schedule
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.source_id, t1.source_type, t1.content, t1.user_id, t1.phone_number, t1.expected_time,
			 t1.execution_time, t1.status, t1.batch_flag, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_message_notify_schedule t1
		 where 1=1
	</select>

	<!-- 分页查询 -->
    <select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.utils.model.MessageNotifyScheduleParams">
        select t1.id, t1.source_id, t1.source_type, t1.content, t1.user_id, t1.phone_number, t1.expected_time,
        t1.execution_time, t1.status, t1.batch_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
        t1.update_user_id,
        t1.update_time
        from wx_t_message_notify_schedule t1
        where t1.delete_flag=0
        <if test="time != null">
            and t1.expected_time <![CDATA[ <= ]]> #{time}
        </if>
        <if test="id != null">
            and t1.id = #{id}
        </if>
    </select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_message_notify_schedule (source_id, source_type, content, user_id, phone_number, expected_time, execution_time, status, batch_flag, delete_flag, create_user_id, create_time, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.sourceId, jdbcType=BIGINT}, #{item.sourceType, jdbcType=VARCHAR}, #{item.content, jdbcType=VARCHAR}, #{item.userId, jdbcType=BIGINT}, #{item.phoneNumber, jdbcType=VARCHAR}, #{item.expectedTime, jdbcType=TIMESTAMP}, #{item.executionTime, jdbcType=TIMESTAMP}, #{item.status, jdbcType=INTEGER}, #{item.batchFlag, jdbcType=INTEGER}, #{item.deleteFlag, jdbcType=BIT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>

    <!-- 获取需要自动推送的消息 -->
    <select id="queryMessages" resultMap="BaseResultMap" parameterType="com.sys.utils.model.MessageNotifyScheduleParams">
        select t1.id, t1.source_id, t1.source_type, t1.content, t1.user_id, t1.phone_number, t1.expected_time,
        t1.execution_time, t1.status, t1.batch_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
        t1.update_user_id,
        t1.update_time
        from wx_t_message_notify_schedule t1
        where t1.delete_flag=0
        <if test="status == null">
            and t1.status <![CDATA[ < 10 ]]>
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        <if test="time != null">
            and t1.expected_time <![CDATA[ <= ]]> #{time}
        </if>
        <if test="id != null">
            and t1.id = #{id}
        </if>
        <if test="sourceId != null">
            and t1.source_id = #{sourceId}
        </if>
        <if test="sourceType != null and sourceType != ''">
            and t1.source_type = #{sourceType}
        </if>
    </select>
</mapper>
