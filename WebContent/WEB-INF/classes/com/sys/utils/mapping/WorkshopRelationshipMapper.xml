<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.utils.dao.WorkshopRelationshipMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.utils.model.WorkshopRelationship">
		<result column="relation_type" property="relationType" jdbcType="VARCHAR"/>
		<result column="source_key" property="sourceKey" jdbcType="VARCHAR"/>
		<result column="workshop_id" property="workshopId" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="workshop_name" property="workShopName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		relation_type,source_key,workshop_id,create_user_id,create_time
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.utils.model.WorkshopRelationship">
		insert into wx_t_workshop_relationship
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="relationType != null">
				relation_type,
			</if>
			<if test="sourceKey != null">
				source_key,
			</if>
			<if test="workshopId != null">
				workshop_id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="relationType != null">
				#{relationType,jdbcType=VARCHAR},
			</if>
			<if test="sourceKey != null">
				#{sourceKey,jdbcType=VARCHAR},
			</if>
			<if test="workshopId != null">
				#{workshopId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_workshop_relationship
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.utils.model.WorkshopRelationshipExample">
		delete from wx_t_workshop_relationship
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.utils.model.WorkshopRelationshipExample" resultType="int">
		select count(1) from wx_t_workshop_relationship
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.utils.model.WorkshopRelationshipExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_workshop_relationship
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.relation_type, t1.source_key, t1.workshop_id, t1.create_user_id, t1.create_time,t2.work_shop_name as workshop_name
		  from wx_t_workshop_relationship t1 left join wx_t_work_shop t2 on t1.workshop_id = t2.id
		 where 1=1
		 <if test="activityId != null">
		     and t1.source_key = #{activityId,jdbcType=VARCHAR}
		 </if>
		  <if test="relationType != null">
		     and t1.relation_type = #{relationType,jdbcType=VARCHAR}
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_workshop_relationship (relation_type, source_key, workshop_id, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.relationType, jdbcType=VARCHAR}, #{item.sourceKey, jdbcType=VARCHAR}, #{item.workshopId, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	
	<!-- 列表查询 -->
	<select id="queryResourceByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.relation_type, t1.source_key, t1.workshop_id, t1.create_user_id, t1.create_time,
          case 
		  when RIGHT(t1.workshop_id,1)=1
		  then (select shop.work_shop_name from wx_t_work_shop shop where 1=1 and shop.id= (convert(INTEGER,t1.workshop_id)-1)/10) 
		  when RIGHT(t1.workshop_id,1)=2
		  then (select fleet.fleet_name from wx_t_fleet_info fleet where 1=1  and fleet.id= (convert(INTEGER,t1.workshop_id)-2)/10) 
		   when RIGHT(t1.workshop_id,1)=3
		  then (select construction.project_name from wx_t_construction_machinery construction where 1=1  and construction.id= (convert(INTEGER,t1.workshop_id)-3)/10) 
		  else '' end as workshop_name
		  from wx_t_workshop_relationship t1 left join wx_t_work_shop t2 on t1.workshop_id = t2.id
		  where 1=1 
		 <if test="activityId != null">
		     and t1.source_key = #{activityId,jdbcType=VARCHAR}
		 </if>
		  <if test="relationType != null">
		     and t1.relation_type = #{relationType,jdbcType=VARCHAR}
		 </if>
	</select>
</mapper>
