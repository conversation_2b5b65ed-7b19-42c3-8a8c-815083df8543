<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.utils.dao.SupplierPartnerMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.utils.model.SupplierPartner">
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="supplier_id" property="supplierId" jdbcType="BIGINT"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		partner_id,supplier_id
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.utils.model.SupplierPartner">
		insert into wx_t_supplier_partner
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="supplierId != null">
				supplier_id,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="supplierId != null">
				#{supplierId,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_supplier_partner
		<set>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.utils.model.SupplierPartnerExample">
		delete from wx_t_supplier_partner
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.utils.model.SupplierPartnerExample" resultType="int">
		select count(1) from wx_t_supplier_partner
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.utils.model.SupplierPartnerExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_supplier_partner
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.partner_id, t1.supplier_id
		  from wx_t_supplier_partner t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_supplier_partner (partner_id, supplier_id) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.partnerId, jdbcType=BIGINT}, #{item.supplierId, jdbcType=BIGINT}
			</trim>
		</foreach>
	</insert>
	
	<select id="queryBySupplierId" resultType="com.sys.utils.model.SupplierPartner"  parameterType="map" >
	    SELECT   sp.supplier_id supplierId, sp.partner_id partnerId, p.organization_name partnerName, s.supplier_name supplierName
			 from wx_t_supplier_partner sp 
			 left JOIN wx_t_supplier s on sp.supplier_id = s.id
			 left JOIN wx_t_organization p on sp.partner_id = p.id
			     where 1=1
			     <if test="supplierId != null">
			          and sp.supplier_id = #{supplierId, jdbcType=BIGINT}
		         </if>
			     
	</select>
	
	
</mapper>
