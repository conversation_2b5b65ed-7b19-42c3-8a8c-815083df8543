<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.utils.dao.SupplierMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.utils.model.Supplier">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="supplier_name" property="supplierName" jdbcType="VARCHAR"/>
		<result column="code" property="code" jdbcType="VARCHAR"/>
		<result column="fun_flag" property="funFlag" jdbcType="BIGINT"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="login_name" property="loginName" jdbcType="VARCHAR"/>
		<result column="email" property="email" jdbcType="VARCHAR"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="cai" property="cai" jdbcType="VARCHAR"/>
		<result column="user_id" property="userId" jdbcType="BIGINT"/>
		<result column="user_name" property="userName" jdbcType="VARCHAR"/>
	</resultMap>
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,supplier_name,code,fun_flag,enable_flag,delete_flag,create_user_id,create_time,update_user_id,update_time, login_name
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.sys.utils.model.Supplier">
		update wx_t_supplier set
				supplier_name = #{supplierName,jdbcType=VARCHAR},
				code = #{code,jdbcType=VARCHAR},
				fun_flag = #{funFlag,jdbcType=BIGINT},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP},
				login_name = #{loginName,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.utils.model.Supplier">
		update wx_t_supplier
		<set>
			<if test="supplierName != null" >
				supplier_name = #{supplierName,jdbcType=VARCHAR},
			</if>
			<if test="code != null" >
				code = #{code,jdbcType=VARCHAR},
			</if>
			<if test="funFlag != null" >
				fun_flag = #{funFlag,jdbcType=BIGINT},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="loginName != null" >
				login_name = #{loginName,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.utils.model.SupplierExample">
    	delete from wx_t_supplier
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.utils.model.Supplier" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_supplier
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="supplierName != null">
				supplier_name,
			</if>
			<if test="code != null">
				code,
			</if>
			<if test="funFlag != null">
				fun_flag,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="loginName != null">
				login_name,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="supplierName != null">
				#{supplierName,jdbcType=VARCHAR},
			</if>
			<if test="code != null">
				#{code,jdbcType=VARCHAR},
			</if>
			<if test="funFlag != null">
				#{funFlag,jdbcType=BIGINT},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="loginName != null">
				#{loginName,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_supplier
		<set>
			<if test="record.supplierName != null">
				supplier_name = #{record.supplierName,jdbcType=VARCHAR},
			</if>
			<if test="record.code != null">
				code = #{record.code,jdbcType=VARCHAR},
			</if>
			<if test="record.funFlag != null">
				fun_flag = #{record.funFlag,jdbcType=BIGINT},
			</if>
			<if test="record.enableFlag != null">
				enable_flag = #{record.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.loginName != null">
				update_time = #{record.loginName,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.utils.model.SupplierExample">
		delete from wx_t_supplier
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.utils.model.SupplierExample" resultType="int">
		select count(1) from wx_t_supplier
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.utils.model.SupplierExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_supplier
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.utils.model.SupplierExample">
    	select
		s.*,u.ch_name userName, u.mobile_tel phone, u.user_id userId, u.email, u.cai
		from wx_t_supplier s LEFT JOIN wx_t_user u on s.login_name = u.login_name and u.status=1
		where s.id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.supplier_name, t1.code, t1.fun_flag, t1.enable_flag, t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time, t1.login_name
		  from wx_t_supplier t1
		 where 1=1
		<if test="supplierName != null and supplierName != ''">
			and t1.supplier_name like '%' + #{supplierName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="code != null and code != ''">
			and t1.code like '%' + #{code, jdbcType=VARCHAR} + '%'
		</if>
		<if test="funFlag != null and funFlag != ''">
			and t1.fun_flag like '%' + #{funFlag, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.utils.model.SupplierParams">
		select t1.id, t1.supplier_name, t1.code, t1.fun_flag, t1.enable_flag, t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time, t1.login_name, u.ch_name userName, u.mobile_tel phone, u.user_id userId, u.email, u.cai
		  from wx_t_supplier t1 LEFT JOIN wx_t_user u on t1.login_name = u.login_name and u.status=1
		 where t1.delete_flag=0
		<if test="supplierName != null and supplierName != ''">
			and t1.supplier_name like '%' + #{supplierName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="code != null and code != ''">
			and t1.code like '%' + #{code, jdbcType=VARCHAR} + '%'
		</if>
		<if test="funFlag != 0 and funFlag != ''">
			and t1.fun_flag &amp; #{funFlag, jdbcType=BIGINT} > 0
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.supplier_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.code like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.login_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
	</select>
	
	<select id="getSuppliers" resultMap="BaseResultMap" parameterType="map">
	    select a.*, u.user_id, u.ch_name user_name, u.email, u.mobile_tel phone, u.cai from (
			select s.* from wx_t_supplier s
			where
			 s.fun_flag &amp; #{funFlag, jdbcType=BIGINT} &gt; 0
			and exists (select 1 from wx_t_supplier_partner sp 
			where
			  sp.supplier_id=s.id
			  <if test="partnerId != null" >
			     and sp.partner_id=  #{partnerId, jdbcType=BIGINT}
			  </if>
			  )
			union all 
			select s.* from wx_t_supplier s
			where
			 s.fun_flag &amp; #{funFlag, jdbcType=BIGINT} &gt; 0
			and not exists (select 1 from wx_t_supplier_partner sp
			where
				sp.supplier_id=s.id
				<if test="partnerId != null" >
					and sp.partner_id=  #{partnerId, jdbcType=BIGINT}
				</if>
				)
			) a
			left join wx_t_user u on a.login_name=u.login_name and u.status=1
			order by a.id desc
	</select>
</mapper>
