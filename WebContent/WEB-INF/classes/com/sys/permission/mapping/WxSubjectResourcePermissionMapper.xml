<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.permission.dao.WxSubjectResourcePermissionMapper" >
  <resultMap id="BaseResultMap" type="com.sys.permission.model.WxSubjectResourcePermission" >
    <id column="permission_id" property="permissionId" jdbcType="BIGINT" />
    <result column="subject_type" property="subjectType" jdbcType="VARCHAR" />
    <result column="subject_id" property="subjectId" jdbcType="BIGINT" />
    <result column="resource_id" property="resourceId" jdbcType="BIGINT" />
    <result column="permission_type_id" property="permissionTypeId" jdbcType="VARCHAR" />
    <result column="permission_expression" property="permissionExpression" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    permission_id, subject_type, subject_id, resource_id, permission_type_id, permission_expression, 
    creation_time, created_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.permission.model.WxSubjectResourcePermissionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_subject_res_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_subject_res_permission
    where permission_id = #{permissionId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_subject_res_permission
    where permission_id = #{permissionId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sys.permission.model.WxSubjectResourcePermission" >
    insert into wx_t_subject_res_permission (permission_id, subject_type, subject_id, 
      resource_id, permission_type_id, permission_expression, 
      creation_time, created_by)
    values (#{permissionId,jdbcType=BIGINT}, #{subjectType,jdbcType=VARCHAR}, #{subjectId,jdbcType=BIGINT}, 
      #{resourceId,jdbcType=BIGINT}, #{permissionTypeId,jdbcType=VARCHAR}, #{permissionExpression,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sys.permission.model.WxSubjectResourcePermission" >
    insert into wx_t_subject_res_permission
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="permissionId != null" >
        permission_id,
      </if>
      <if test="subjectType != null" >
        subject_type,
      </if>
      <if test="subjectId != null" >
        subject_id,
      </if>
      <if test="resourceId != null" >
        resource_id,
      </if>
      <if test="permissionTypeId != null" >
        permission_type_id,
      </if>
      <if test="permissionExpression != null" >
        permission_expression,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="permissionId != null" >
        #{permissionId,jdbcType=BIGINT},
      </if>
      <if test="subjectType != null" >
        #{subjectType,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null" >
        #{subjectId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null" >
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="permissionTypeId != null" >
        #{permissionTypeId,jdbcType=VARCHAR},
      </if>
      <if test="permissionExpression != null" >
        #{permissionExpression,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sys.permission.model.WxSubjectResourcePermission" >
    update wx_t_subject_res_permission
    <set >
      <if test="subjectType != null" >
        subject_type = #{subjectType,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null" >
        subject_id = #{subjectId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null" >
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="permissionTypeId != null" >
        permission_type_id = #{permissionTypeId,jdbcType=VARCHAR},
      </if>
      <if test="permissionExpression != null" >
        permission_expression = #{permissionExpression,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
    </set>
    where permission_id = #{permissionId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sys.permission.model.WxSubjectResourcePermission" >
    update wx_t_subject_res_permission
    set subject_type = #{subjectType,jdbcType=VARCHAR},
      subject_id = #{subjectId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      permission_type_id = #{permissionTypeId,jdbcType=VARCHAR},
      permission_expression = #{permissionExpression,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT}
    where permission_id = #{permissionId,jdbcType=BIGINT}
  </update>
  <select id="selectByParams" resultMap="BaseResultMap" parameterType="map" >
    select
    <include refid="Base_Column_List" />
    from wx_t_subject_res_permission srp1 where 1=1
    <if test="userId != null">
    	and ((srp1.subject_type='USER' and srp1.subject_id=#{userId}) or (srp1.subject_type='ROLE' and exists (select 1 from wx_t_userrole ur1 where ur1.role_id=srp1.subject_id and ur1.user_id=#{userId})))
    </if>
    <if test="sqlId != null">
    	and exists (select 1 from wx_t_resource r join wx_t_resource_sql rs on r.resource_id=rs.resource_id where r.resource_id=srp1.resource_id and rs.sqlid=#{sqlId})
    </if>
  </select>
</mapper>