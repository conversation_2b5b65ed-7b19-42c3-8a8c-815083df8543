<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.organization.dao.OrganizationVoMapper">
    <resultMap id="BaseResultMap" type="com.sys.organization.model.OrganizationVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="organization_code" property="organizationCode" jdbcType="NVARCHAR"/>
        <result column="organization_name" property="organizationName" jdbcType="NVARCHAR"/>
        <result column="organization_name_py" property="organizationNamePy" jdbcType="NVARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="updator" property="updator" jdbcType="BIGINT"/>
        <result column="esspflag" property="esspflag" jdbcType="INTEGER"/>
        <result column="esspflag" property="esspflag" jdbcType="INTEGER"/>
        <result column="Customer_Grade" property="customerGrade" jdbcType="VARCHAR"/>
        <result column="CDM_Customer_Grade" property="cdmCustomerGrade" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , organization_code, organization_name, parent_id, type, status, sort, create_time,
        creator, update_time, updator,remark
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.sys.organization.model.OrganizationVoExample">
        select
        <if test="distinct">
            distinct
        </if>
        'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from wx_t_organization
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from wx_t_organization
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wx_t_organization
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.sys.organization.model.OrganizationVo">
        insert into wx_t_organization (id, organization_code, organization_name,
        parent_id, type, status,
        sort, create_time, creator,
        update_time, updator)
        values (#{id,jdbcType=BIGINT}, #{organizationCode,jdbcType=NVARCHAR}, #{organizationName,jdbcType=NVARCHAR},
        #{parentId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
        #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.sys.organization.model.OrganizationVo">
        insert into wx_t_organization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="organizationCode != null">
                organization_code,
            </if>
            <if test="organizationName != null">
                organization_name,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="sort != null">
                sort,
            </if>
            create_time,
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updator != null">
                updator,
            </if>
            <if test="esspflag != null">
                esspflag,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="organizationCode != null">
                #{organizationCode,jdbcType=NVARCHAR},
            </if>
            <if test="organizationName != null">
                #{organizationName,jdbcType=NVARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            getdate(),
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=BIGINT},
            </if>
            <if test="esspflag != null">
                #{esspflag,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=NVARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into wx_t_organization (organization_code, organization_name,parent_id, type, status,sort, create_time,
        creator,
        update_time, updator) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.organizationCode,jdbcType=NVARCHAR}, #{item.organizationName,jdbcType=NVARCHAR},
            #{item.parentId,jdbcType=BIGINT}, #{item.type,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},
            #{item.sort,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updator,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.sys.organization.model.OrganizationVo">
        update wx_t_organization
        <set>
            <if test="organizationCode != null">
                organization_code = #{organizationCode,jdbcType=NVARCHAR},
            </if>
            <if test="organizationName != null">
                organization_name = #{organizationName,jdbcType=NVARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                updator = #{updator,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=NVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sys.organization.model.OrganizationVo">
        update wx_t_organization
        set organization_code = #{organizationCode,jdbcType=NVARCHAR},
        organization_name = #{organizationName,jdbcType=NVARCHAR},
        parent_id         = #{parentId,jdbcType=BIGINT},
        type              = #{type,jdbcType=INTEGER},
        status            = #{status,jdbcType=INTEGER},
        sort              = #{sort,jdbcType=INTEGER},
        create_time       = #{createTime,jdbcType=TIMESTAMP},
        creator           = #{creator,jdbcType=BIGINT},
        update_time       = #{updateTime,jdbcType=TIMESTAMP},
        updator           = #{updator,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- add by bo.liu start -->
    <select id="selectByOrgCode" resultMap="BaseResultMap" parameterType="map">
        select *
        from wx_t_organization
        where organization_code = #{orgCode,jdbcType=NVARCHAR}
    </select>

    <select id="selectByOrgNameAndPid" resultMap="BaseResultMap" parameterType="map">
        select
        *
        from
        wx_t_organization
        where parent_id = #{pid,jdbcType=BIGINT}
        and
        <if test="orgName!=null and orgName!=''">
            organization_name = #{orgName,jdbcType=NVARCHAR}
            and
        </if>
        status = 1
    </select>

    <select id="selectByOrganizationName" resultMap="BaseResultMap" parameterType="map">
        select *
        from wx_t_organization
        where status = 1
        and organization_name = #{orgName,jdbcType=NVARCHAR}
    </select>

    <resultMap id="orgTreeNode" type="com.sys.organization.model.OrganizationVoTree">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="organization_code" property="organizationCode" jdbcType="NVARCHAR"/>
        <result column="organization_name" property="organizationName" jdbcType="NVARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="updator" property="updator" jdbcType="BIGINT"/>
        <result column="leaf_flag" property="leafFlag" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="selectByOrgCodeForTreeNodes_cond">
        FROM wx_t_organization t
        WHERE
        t.organization_code!='0001' AND t.status !=0 AND t.type in (0,1)
        <if test="orgLimit and loginUserId != 1">
            and (#{salesChannel} is null or not exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe
            join wx_t_business_region_config xx_001_brc on xx_001_pe.partner_property=xx_001_brc.ext_property1 and
            xx_001_brc.business_name='DATA_PERMISSION_EXCLUDE_SP_IN_SALES_CHANNEL' where xx_001_pe.partner_id=t.id and
            xx_001_brc.sales_channel=#{salesChannel}))

            and (exists (select 1 from wx_t_partner_o2o_enterprise xx_001_pe
            left join dw_customer_region_sales_supervisor_rel xx_001_crss on
            xx_001_pe.distributor_id=xx_001_crss.distributor_id
            left join dw_region_sales_channel_rel xx_001_rsc on xx_001_rsc.region_name=xx_001_crss.region_name
            left join wx_t_partner_responsible_main xx_001_prm on xx_001_prm.config_type=1 and xx_001_prm.sales_cai=
            xx_001_crss.sales_cai
            where xx_001_pe.partner_id=t.id and (xx_001_crss.sales_cai=#{loginCai} or
            xx_001_crss.suppervisor_cai=#{loginCai} or xx_001_rsc.sales_channel_name=#{loginCai} or
            xx_001_prm.user_id=#{loginUserId}))
            or exists (select 1 from wx_t_partner_responsible_main xx_001_prm
            join wx_t_partner_responsible xx_001_pr on xx_001_pr.responsible_main_id=xx_001_prm.id
            where xx_001_pr.partner_id=t.id and xx_001_prm.user_id=#{loginUserId} and xx_001_prm.config_type=0))
        </if>

    </sql>
    <select id="selectByOrgCodeForTreeNodes" parameterType="map" resultMap="orgTreeNode">
        <!-- modify by bo.liu 0907 select *
         from wx_t_organization t where
          t.organization_code!='${orgCode}' AND t.status !=0
         order by t.sort asc
          -->
        SELECT t.id, t.organization_code, t.organization_name, t.parent_id, t.type
        <include refid="selectByOrgCodeForTreeNodes_cond"/>
        <if test="organizationName != null">
            and (t.organization_name like '%' + #{organizationName} + '%' or
            left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) LIKE '%' + #{organizationName} + '%')
        </if>
        union all
        select r1t.id, r1t.organization_code, oo1.organization_name + ' - ' + r1t.organization_name, r1t.parent_id,
        r1t.type
        from wx_t_organization r1t
        left join wx_t_partner_o2o_enterprise pe1 on r1t.id=pe1.partner_id
        left join wx_t_organization oo1 on oo1.id=pe1.ext_property1
        where r1t.status =1 AND r1t.type=3
        and exists (select 1
        <include refid="selectByOrgCodeForTreeNodes_cond"/>
        and t.id=pe1.ext_property1)
        <if test="organizationName != null">
            and (r1t.organization_name like '%' + #{organizationName} + '%' or
            left(dbo.f_GetPyToAboutHanyu(r1t.organization_name),500) LIKE '%' + #{organizationName} + '%')
        </if>
    </select>

    <update id="updateByOrganizationCode" parameterType="com.sys.organization.model.OrganizationVo">
        update wx_t_organization
        <set>
            <if test="organizationName != null">
                organization_name = #{organizationName,jdbcType=NVARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>

            update_time = getdate(),

            <if test="updator != null">
                updator = #{updator,jdbcType=BIGINT},
            </if>

            <if test="esspflag != null">
                esspflag = #{esspflag,jdbcType=INTEGER},
            </if>
        </set>
        where organization_code = #{organizationCode,jdbcType=NVARCHAR}
    </update>

    <delete id="deleteById" parameterType="Map">
        delete
        from wx_t_organization
        where id = #{id,jdbcType=BIGINT}
        or parent_id = #{id,jdbcType=BIGINT}
    </delete>


    <select id="getAllNewChildOrgId" parameterType="map" resultType="java.lang.Long">
        select a.id
        from wx_t_organization a,
        f_getOrgNewIds(${id}) b
        where a.id = b.id
        and a.status!=0
        order by a.id
    </select>

    <update id="modifyOrgAndChildsStatus" parameterType="map">
        update wx_t_organization
        set status = 0
        where
        <if test="orgIds!=null">
            id in
            <foreach item="id" index="index" collection="orgIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            and
        </if>
        1 = 1
    </update>
    <select id="getMaxId" resultType="java.lang.Long">
        select max(id)
        from wx_t_organization
    </select>

    <select id="getAllPartnerByRegionId" resultMap="BaseResultMap" parameterType="map">
        SELECT tt_organization.organization_name organization_name,
        tt_organization.id                id
        FROM wx_t_region_partner t_region_partner
        LEFT join
        wx_t_organization tt_organization
        ON t_region_partner.partner_id = tt_organization.id
        WHERE tt_organization.status = 1
        and t_region_partner.region_id = #{regionId}
        AND tt_organization.id!=''
        and
        tt_organization.organization_name!=''
    </select>
    <select id="selectByOrgByParentId" resultMap="orgTreeNode" parameterType="map">
        SELECT
        tt_organization.*, case when exists (select 1 from wx_t_organization t0 where t0.parent_id=tt_organization.id
        AND t0.status = 1 AND t0.type!=2) then 0 else 1 end leaf_flag
        From
        wx_t_organization tt_organization
        WHERE
        tt_organization.parent_id = #{parentId,jdbcType=BIGINT}
        and
        tt_organization.status = 1
        and tt_organization.type!=2
        <if test="userType == 'chevron'">
            and (tt_organization.type=0
            or not exists (select 1 from wx_t_partner_responsible_main xx_001_prm where xx_001_prm.user_id=#{loginUser})
            or exists (select 1 from wx_t_partner_responsible_main xx_001_prm join wx_t_partner_responsible xx_001_pr on
            xx_001_pr.responsible_main_id=xx_001_prm.id where xx_001_pr.partner_id=tt_organization.id and
            xx_001_prm.user_id=#{loginUser})
            )
        </if>
    </select>


    <!-- add by bo.liu end -->
    <!-- 根据合伙人控件参数查询合伙人 add by lizhentao 20160920 -->
    <select id="selPartners" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.PartnerCtrlParam">
        select t.* from wx_t_organization t where t.type=1 and t.status=1
        <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and
            i1.partner_id=t.id)
        </if>
        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
    </select>
    <!-- 根据参数查询组织 add lizhentao 20161201 -->
    <select id="selectOrgsByParams" resultMap="BaseResultMap" parameterType="map">
        select t.* from wx_t_organization t where t.status=1
        <if test="type != null">
            and t.type=#{type}
        </if>
        <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and
            i1.partner_id=t.id)
        </if>
        <if test="userType != null and userType != 1">
            and t.id=#{orgId,jdbcType=BIGINT}
        </if>
        <if test="workshopId != null">
            and t.type=1 and exists (select 1 from wx_t_workshop_partner t_wp where t_wp.partner_id = t.id and
            t_wp.workshop_id=#{workshopId,jdbcType=BIGINT})
        </if>
    </select>

    <!-- 根据参数查询合伙人列表 add by lizhentao 20161027 -->
    <resultMap id="PartnerView" type="com.sys.organization.model.PartnerView">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="organization_code" property="code" jdbcType="VARCHAR"/>
        <result column="organization_name" property="name" jdbcType="VARCHAR"/>
        <result column="enterprise_code" property="enterpriseCode" jdbcType="VARCHAR"/>
        <result column="enterprise_name" property="enterpriseName" jdbcType="VARCHAR"/>
        <result column="partner_property" property="partnerProperty" jdbcType="VARCHAR"/>
        <result column="new_sp_flag" property="newSpFlag" jdbcType="VARCHAR"/>
        <result column="partner_property_text" property="partnerPropertyText" jdbcType="VARCHAR"/>
        <result column="create_time" property="partnerCreateTime" jdbcType="TIMESTAMP"/>
        <result column="per_liter_reward" property="perLiterReward" jdbcType="VARCHAR"/>
        <result column="esspflag" property="esspflag" jdbcType="INTEGER"/>
        <result column="region_name" property="regionName" jdbcType="VARCHAR"/>

        <result column="distribution_rule_id" property="distributionRuleId" jdbcType="BIGINT"/>
        <result column="owner_reward" property="ownerReward" jdbcType="NUMERIC"/>
        <result column="mechanic_reward" property="mechanicReward" jdbcType="NUMERIC"/>
        <result column="award_realtime" property="awardRealtime" jdbcType="VARCHAR"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="sap_code" property="sapCode" jdbcType="VARCHAR"/>
        <result column="ship_to_code" property="shipToCode" jdbcType="VARCHAR"/>
        <result column="material_stock" property="materialStock" jdbcType="VARCHAR"/>
        <result column="material_stock_text" property="materialStockText" jdbcType="VARCHAR"/>
        <result column="include_dms_workshop" jdbcType="INTEGER" property="includeDmsWorkshop"/>
        <result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
        <result column="channel_weight" property="channelWeight" jdbcType="INTEGER"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
        <result column="partner_property_label" property="partnerPropertyLabel" jdbcType="INTEGER"/>
        <result column="eto_company_id" property="etoCompanyId" jdbcType="INTEGER"/>
        <result column="type" property="orgType" jdbcType="INTEGER"/>
        <result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
        <result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
        <result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
        <result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
        <result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
        <result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
        <result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
        <result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
        <result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
        <result column="root_partner_name" property="rootPartnerName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectPartnersByParams" resultMap="PartnerView" parameterType="map">
        select t.*, t1.enterprise_code, t1.enterprise_name, t1.distributor_id, t1.partner_property,
        t1.partner_property_label, t1.sap_code,t1.ship_to_code,t1.material_stock,
        verirule.per_liter_reward, dr.id distribution_rule_id, t1.ext_flag,
        dr.owner_reward, dr.mechanic_reward, dr.award_realtime, oo1.organization_name root_partner_name,
        (select sum(rv.channel_weight) from view_customer_region_sales_channel rv where
        rv.distributor_id=t1.distributor_id) channel_weight,
        (select top 1 rv.region from dw_customer_org_sales rv where rv.distributor_id=t1.distributor_id) region
        from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        LEFT JOIN wx_t_verification_rule verirule ON verirule.partner_id = t.id AND verirule.enable_flag = 'Y'
        left join wx_t_ws_distribution_rule dr on dr.workshop_id=-1 and dr.enable_flag='Y' and dr.partner_id=t.id and
        dr.sku is null
        left join wx_t_organization oo1 on t.type=3 and t1.ext_property1=oo1.id
        where t.status=1
        <choose>
            <when test="existCheck">
                <if test="partnerId != null">
                    and t.id!=#{partnerId,jdbcType=BIGINT}
                </if>
                and (1!=1
                <if test="partnerName != null and partnerName != ''">
                    or t.organization_name = #{partnerName}
                </if>
                <if test="sapCode != null">
                    or t1.sap_code=#{sapCode}
                </if>
                <if test="distributorId != null">
                    or t1.distributor_id=#{distributorId}
                </if>
                )
            </when>
            <when test="partnerId != null">
                and t.id=#{partnerId,jdbcType=BIGINT}
            </when>
            <when test="distributorId != null">
                and t1.distributor_id=#{distributorId,jdbcType=BIGINT}
            </when>
            <otherwise>
                and t.type=1
                <if test="regionId != null">
                    <!-- and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and i1.partner_id=t.id) -->
                    and exists (select 1 from wx_t_region_partner i1 where i1.region_id like '${regionId}%' and
                    i1.partner_id=t.id)
                </if>
                <if test="partnerName != null and partnerName != ''">
                    and t.organization_name like #{partnerName}
                </if>
                <if test="loginUserType != 1 and loginUserOrg != null">
                    and t.id=#{loginUserOrg,jdbcType=BIGINT}
                </if>
                <if test="sapCode">
                    and t1.sap_code=#{sapCode}
                </if>
            </otherwise>
        </choose>
    </select>
    <select id="selectPartnerFullInfoByPartnerId" resultMap="PartnerView" parameterType="long">
        select t.*,
        t.id                                            partner_id,
        t1.distributor_id,
        t1.enterprise_code,
        t1.enterprise_name,
        t1.partner_property,
        t1.new_sp_flag,
        t1.sap_code,
        t1.material_stock,
        t1.eto_company_id,
        (select di1.dic_item_name
        from wx_t_dic_item di1
        where di1.dic_type_code = 'partner.partnerProperty'
        and di1.dic_item_code = t1.partner_property) partner_property_text,
        (select di1.dic_item_name
        from wx_t_dic_item di1
        where di1.dic_type_code = 'partner.materialStock'
        and di1.dic_item_code = t1.material_stock)   material_stock_text,
        (select top 1 s.region
        from dw_customer_org_sales s
        where s.distributor_id = t1.distributor_id
        order by isnull(s.channel_weight, *********))  region,
        verirule.per_liter_reward,
        dr.id                                           distribution_rule_id,
        dr.owner_reward,
        dr.mechanic_reward,
        dr.award_realtime
        from wx_t_organization t
        left join wx_t_partner_o2o_enterprise t1 on t.id = t1.partner_id
        LEFT JOIN wx_t_verification_rule verirule ON verirule.partner_id = t.id AND verirule.enable_flag = 'Y'
        left join wx_t_ws_distribution_rule dr
        on dr.workshop_id = -1 and dr.enable_flag = 'Y' and dr.partner_id = t.id and dr.sku is null
        where t.type = 1
        and t.id = #{partnerId,jdbcType=NVARCHAR}
    </select>

    <select id="queryPartnerForCtrl" resultMap="PartnerView" parameterType="com.chevron.pms.model.PartnerCtrlParam">
        select
        <if test="limit > 0">
            top ${limit}
        </if>
        t.id, t.organization_code,
        <choose>
            <when test="includeDistributorPrefix == 1">(case when t.type=3 then r1o1.organization_name + ' - ' else ''
                end) +
            </when>
        </choose>
        t.organization_name organization_name, t.type, t1.enterprise_code, t1.enterprise_name, t1.distributor_id,
        t1.partner_property,t1.sap_code,t1.ship_to_code,t1.material_stock
        <if test="includeDmsWorkshopField == 1">
            ,case when exists (select 1 from dw_dms_workshop dwt1 where dwt1.distributor_id=t1.distributor_id) then 1
            else 0 end include_dms_workshop
        </if>
        from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        left join wx_t_organization r1o1 on t.type=3 and t1.ext_property1=r1o1.id
        left join wx_t_partner_o2o_enterprise r1pe on r1o1.id=r1pe.partner_id
        where
        <choose>
            <when test="includeRetailer == 1">
                t.type in (1,3)
                <if test="loginUserOrg != null">
                    and (t.id=#{loginUserOrg} or r1o1.id=#{loginUserOrg})
                </if>
            </when>
            <otherwise>t.type=1</otherwise>
        </choose>
        <if test="partnerIds != null and partnerIds.size()>0">
            and t.id in
            <foreach collection="partnerIds" item="pid" open="(" close=")" separator=",">
                ${pid}
            </foreach>
        </if>
        <if test="joinSalesOrg">
            and exists (select 1 from dw_customer_org_sales cos left join dw_region_sales_channel_rel rsc on
            rsc.region_name=cos.region where cos.distributor_id=t1.distributor_id and cos.sales_cai!='NA'
            <if test="productChannelWeight != null">
                and cos.channel_weight&amp;${productChannelWeight}>0
            </if>
			<if test="buSalesChannel != null and buSalesChannel.size()>0">
                and rsc.sales_channel_name in
                <foreach item="channel" index="index" collection="buSalesChannel" open="(" separator=","
                         close=")">
                    #{channel}
                </foreach>
            </if>
			
            <if test="region != null and region != ''">
                and cos.region=#{region}
            </if>
            <if test="bu != null and bu != ''">
                and rsc.bu=#{bu}
            </if>
            )
        </if>
        <if test="includeInactive == false">
            and t.status=1
        </if>
        <choose>
            <when test="partnerId != null">
                and t.id=#{partnerId,jdbcType=BIGINT}
            </when>
            <when test="distributorId != null">
                and t1.distributor_id=#{distributorId,jdbcType=BIGINT}
            </when>
            <when test="salesId != null">
                and exists (select 1 from wx_t_user u1 left join dw_access_control_customer_org_sales cs1 on
                u1.cai=cs1.sales_cai
                where u1.user_id=#{salesId,jdbcType=BIGINT} and cs1.distributor_id=t1.distributor_id
                <if test="channelWeight != null">
                    and cs1.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
            <when test="salesCai != null and salesCai != ''">
                and exists (select 1 from dw_access_control_customer_org_sales crss where
                crss.distributor_id=t1.distributor_id and crss.sales_cai=#{salesCai,jdbcType=NVARCHAR}
                <if test="channelWeight != null">
                    and crss.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
        </choose>
        <choose>
            <when test="spResource">
                $Permission_Clause$
            </when>
            <otherwise>
                and exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$)
            </otherwise>
        </choose>
        <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and
            i1.partner_id=t.id)
        </if>
        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null">
            and (t.organization_name like '%' + #{partnerName} + '%' or
            left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) LIKE '%' + #{partnerName} + '%')
        </if>
        order by t.update_time desc
    </select>
    <select id="selPartnersNew" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.PartnerCtrlParam">
        select
        <if test="limit != null">
            top ${limit}
        </if>
        t.*,
        left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) organizationNamePy
        <if test="withCustomerGrade">
            ,(select top 1 pcg.grade from dbo.dw_pp_customer_grade pcg where pcg.distributor_id = poe.distributor_id and
            pcg.sales_channel_name = <![CDATA['C&I']]>) as Customer_Grade
            ,(select top 1 pcg.grade from dbo.dw_pp_customer_grade pcg where pcg.distributor_id = poe.distributor_id and
            pcg.sales_channel_name = 'CDM') as CDM_Customer_Grade
        </if>
        from wx_t_organization t
        <if test="withCustomerGrade">
            left join wx_t_partner_o2o_enterprise poe on poe.partner_id = t.id
        </if>
        where t.type=1 and t.status=1
        <choose>
            <when test="spResource">
                $Permission_Clause$
            </when>
            <when test="includeSpWithNoWs">
                and (not exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id) or exists (select 1
                from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$))
            </when>
            <otherwise>
                and exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$)
            </otherwise>
        </choose>
        <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and
            i1.partner_id=t.id)
        </if>
        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null">
            and (t.organization_name like '%' + #{partnerName} + '%' or
            left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) LIKE '%' + #{partnerName} + '%')
        </if>
        <if test="salesChannel != null and salesChannel != ''">
            and exists (select 1 from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel
            crss on pe.distributor_id=crss.distributor_id
            left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
            where pe.partner_id=t.id and rsc.sales_channel_name=#{salesChannel})
        </if>

        <if test="partnerIds != null and partnerIds.size()>0">
            and t.id in
            <foreach collection="partnerIds" item="pid" open="(" close=")" separator=",">
                ${pid}
            </foreach>
        </if>
    </select>

    <select id="selectPartnersByParamsNew" resultMap="PartnerView" parameterType="com.chevron.pms.model.PartnerParams">
        select t.*, t.id partner_id, t1.enterprise_code, t1.distributor_id, t1.enterprise_name,t1.partner_property,
        t1.sap_code,t1.material_stock,t1.partner_property_label,
        (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.partnerProperty' and
        di1.dic_item_code=t1.partner_property) partner_property_text,
        (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.materialStock' and
        di1.dic_item_code=t1.material_stock) material_stock_text,
        verirule.per_liter_reward, dr.id distribution_rule_id,
        dr.owner_reward, dr.mechanic_reward, dr.award_realtime from wx_t_organization t left join
        wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        LEFT JOIN wx_t_verification_rule verirule ON verirule.partner_id = t.id AND verirule.enable_flag = 'Y'
        left join wx_t_ws_distribution_rule dr on dr.workshop_id=-1 and dr.enable_flag='Y' and dr.partner_id=t.id and
        dr.sku is null
        where t.type=1 and t.status=1 $Permission_Clause$
        <if test="queryField!=null">
            <if test="regionId != null">
                and exists (select 1 from wx_t_region_partner i1 where i1.region_id like '${regionId}%' and
                i1.partner_id=t.id)
            </if>
            <if test="queryField != ''">
                and (t.organization_name like #{queryField} or (left(dbo.f_GetPyToAboutHanyu(t.organization_name),500)
                like #{firtPyForQueryField} + '%') or t1.sap_code like '%' + #{queryField} + '%')
            </if>
            <if test="loginUserType != 1">
                and t.id=#{loginUserOrg,jdbcType=BIGINT}
            </if>
        </if>
        <if test="queryField==null">
            <choose>
                <when test="partnerId != null">
                    and t.id=#{partnerId,jdbcType=BIGINT}
                </when>
                <otherwise>
                    <if test="regionId != null">
                        and exists (select 1 from wx_t_region_partner i1 where i1.region_id like '${regionId}%' and
                        i1.partner_id=t.id)
                    </if>
                    <if test="sapCode != null and sapCode != ''">
                        and t1.sap_code like '%' + #{sapCode} + '%'
                    </if>
                    <if test="partnerName != null and partnerName != ''">
                        and
                        (t.organization_name like #{partnerName} or
                        (left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) like #{firtPyForQueryField} + '%'))
                    </if>
                    <if test="loginUserType != 1">
                        and t.id=#{loginUserOrg,jdbcType=BIGINT}
                    </if>
                    <if test="area != null and area != ''">
                        and exists (select 1 from wx_t_region_partner rp
                        join wx_t_region r1 on r1.id=rp.region_id
                        join wx_t_region r2 on r1.parent_id=r2.id
                        join wx_t_region r3 on r2.parent_id=r3.id where rp.partner_id=t.id and r3.remark like '%' +
                        #{area} + '%')
                    </if>
                    <if test="salesChannel != null and salesChannel != ''">
                        and exists (select 1 from dw_customer_region_sales_supervisor_rel crss
                        left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
                        where t1.distributor_id=crss.distributor_id and rsc.sales_channel_name=#{salesChannel})
                    </if>
                    <if test="partnerProperty != null and partnerProperty != ''">
                        and t1.partner_property=#{partnerProperty}
                    </if>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectRetailerForPage" resultMap="PartnerView" parameterType="com.chevron.pms.model.PartnerParams">
        select r1t.*, r1t.id partner_id, r1t1.enterprise_code, r1t1.distributor_id, r1t1.enterprise_name,
        r1t1.partner_property, r1t1.sap_code,r1t1.material_stock,r1t1.partner_property_label,
        oo1.organization_name root_partner_name
        from wx_t_organization r1t
        left join wx_t_partner_o2o_enterprise r1t1 on r1t.id=r1t1.partner_id
        left join wx_t_organization oo1 on r1t.type=3 and r1t1.ext_property1=oo1.id
        where r1t.type=3 and r1t.status=1
        and exists (select 1 from wx_t_organization t
        left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id where t.id=r1t1.ext_property1 $Permission_Clause$

        )
        <if test="queryField != null and queryField != ''">
            and (r1t.organization_name like '%' + #{queryField} + '%' or
            (left(dbo.f_GetPyToAboutHanyu(r1t.organization_name),500) like '%' + #{queryField} + '%'))
        </if>
        <if test="partnerName != null and partnerName != ''">
            and
            (r1t.organization_name like '%' + #{partnerName} + '%' or
            (left(dbo.f_GetPyToAboutHanyu(r1t.organization_name),500) like '%' + #{partnerName} + '%'))
        </if>
        <if test="extProperty1 != null and extProperty1 != ''">
            and r1t1.ext_property1=#{extProperty1}
        </if>
        <if test="partnerId != null">
            and r1t.id=#{partnerId,jdbcType=BIGINT}
        </if>
    </select>


    <select id="countPartnersByParamsNew" resultType="Long" parameterType="com.chevron.pms.model.PartnerParams">
        select count(1) from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        LEFT JOIN wx_t_verification_rule verirule ON verirule.partner_id = t.id AND verirule.enable_flag = 'Y'
        where t.type=1 and t.status=1
        <if test="queryField!=null">
            <if test="regionId != null">
                and exists (select 1 from wx_t_region_partner i1 where i1.region_id like '${regionId}%' and
                i1.partner_id=t.id)
            </if>
            <if test="queryField != ''">
                and (t.organization_name like #{queryField} or (left(dbo.f_GetPyToAboutHanyu(t.organization_name),500)
                like #{firtPyForQueryField} + '%'))
            </if>
            <if test="loginUserType != 1">
                and t.id=#{loginUserOrg,jdbcType=BIGINT}
            </if>
        </if>
        <if test="queryField==null">
            <choose>
                <when test="partnerId != null">
                    and t.id=#{partnerId,jdbcType=BIGINT}
                </when>
                <otherwise>
                    <if test="regionId != null">
                        and exists (select 1 from wx_t_region_partner i1 where i1.region_id like '${regionId}%' and
                        i1.partner_id=t.id)
                    </if>
                    <if test="partnerName != null and partnerName != ''">
                        and
                        (t.organization_name like #{partnerName} or
                        (left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) like #{firtPyForQueryField} + '%'))
                    </if>
                    <if test="loginUserType != 1">
                        and t.id=#{loginUserOrg,jdbcType=BIGINT}
                    </if>
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="selectPartnersByWorkshopId" resultMap="PartnerView" parameterType="java.lang.Long">
        select tt_org.id, tt_org.organization_code, tt_org.organization_name
        from wx_t_organization tt_org
        LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.partner_id = tt_org.id
        LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_par.workshop_id
        where tt_workshop.id = #{workshopId}
    </select>
    <!-- 统计dashboard页面合伙人数量　数据权限 -->
    <select id="getPartnersCount" resultType="Long" parameterType="com.sys.organization.model.OrganizationParam">
        select count(*)
        from wx_t_organization wp1
        where wp1.type = 1
        and wp1.status = 1 $Permission_Clause$
    </select>

    <resultMap id="RegionMap" type="com.chevron.pms.model.RegionVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region_code" property="regionCode" jdbcType="NVARCHAR"/>
        <result column="region_name" property="regionName" jdbcType="NVARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="region_type" property="regionType" jdbcType="NVARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_leaf" property="isLeaf" jdbcType="BIT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="NVARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="updator" property="updator" jdbcType="NVARCHAR"/>
        <result column="remark" property="remark" jdbcType="NVARCHAR"/>
        <result column="channel_weight" property="channelWeight" jdbcType="INTEGER"/>

        <result column="province_id" property="provinceId" jdbcType="BIGINT"/>
        <result column="city_id" property="cityId" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getCitysSelectedByPartnerId" parameterType="map" resultMap="RegionMap">
        select r2.*, a1.channel_weight from (
        select distinct rp1.channel_weight, r1.parent_id city_id, rp1.partner_id
        from wx_t_region_partner rp1 left join wx_t_region r1 on r1.id=rp1.region_id
        where rp1.partner_id=#{id,jdbcType=BIGINT}
        ) a1 left join wx_t_region r2 on r2.id=a1.city_id
        where not exists (select 1 from wx_t_region ir1 left join wx_t_region_partner irp1 on ir1.id=irp1.region_id and
        irp1.channel_weight=a1.channel_weight and irp1.partner_id=a1.partner_id where ir1.parent_id=a1.city_id and
        irp1.id is null)
        <!-- SELECT id, region_code, region_name, parent_id, region_type, sort, status, is_leaf, create_time,
            creator, update_time, updator, remark FROM wx_t_region WHERE id IN (
              SELECT region1.parent_id FROM
                  (SELECT count(*) AS region_count, t.parent_id FROM wx_t_region t WHERE t.parent_id in
                      (SELECT DISTINCT region.parent_id FROM wx_t_region region LEFT JOIN
                        wx_t_region_partner rp on rp.region_id = region.region_code WHERE rp.partner_id = #{id,jdbcType=BIGINT}
                    ) GROUP BY t.parent_id
                ) region1 ,
                (SELECT count(rp.region_id) AS region_count_city_partner , region.parent_id FROM wx_t_region region LEFT JOIN
                     wx_t_region_partner rp on rp.region_id = region.region_code WHERE rp.partner_id = #{id,jdbcType=BIGINT}
                     GROUP BY region.parent_id
                ) region2 where region2.parent_id = region1.parent_id AND region2.region_count_city_partner = region1.region_count
            ) -->
    </select>
    <select id="selectDealerByParams" resultMap="BaseResultMap">
        SELECT *
        FROM wx_t_organization o
        LEFT JOIN wx_t_partner_o2o_enterprise e
        ON o.id = e.partner_id
        WHERE o.status = 1
        AND e.partner_property = 'Delo Distributor'
    </select>


    <resultMap id="SpRebateMap" type="com.sys.quartz.model.SpRebateInfo">
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="partner_name" property="spName" jdbcType="NVARCHAR"/>
        <result column="scan_rebate" property="scanRebate" jdbcType="NUMERIC"/>
        <result column="scan_number" property="scanNumber" jdbcType="INTEGER"/>
        <result column="lader_rebate" property="laderRebate" jdbcType="NUMERIC"/>
        <result column="lader_number" property="laderNumber" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getSpScanRebate" parameterType="map" resultMap="SpRebateMap">
        SELECT partner_id,partner_name, transaction_amount scan_rebate , capacity scan_number FROM
        (select sum(vd.total_amount) transaction_amount, wp.partner_id, o.organization_name partner_name,
        sum(convert(int, p.capacity)) capacity
        from wx_t_mechanic_qrcode mq left join wx_t_verification_detail vd on mq.qr_code=vd.qr_code
        join wx_t_workshop_partner wp on wp.workshop_id=mq.workshop_id and wp.relation_type='trade'
        join wx_t_organization o on o.id=wp.partner_id
        join wx_t_product p on p.sku=mq.sku
        JOIN wx_t_partner_o2o_enterprise t_p_o2o ON o.id = t_p_o2o.partner_id
        WHERE (t_p_o2o.partner_property = 'Delo Distributor'
        OR (t_p_o2o.partner_property='NORMAL' <!-- AND o.create_time>='2018-01-01' -->))
        <if test="partnerId!=null">
            AND o.id = #{partnerId}
        </if>
        AND mq.creation_time&gt;=#{startDate}
        and mq.creation_time&lt;#{endDate}
        AND o.status=1
        group by wp.partner_id,o.organization_name) T WHERE transaction_amount!=0
    </select>


    <select id="getSpLaderRebate" parameterType="map" resultMap="SpRebateMap">
        SELECT partner_id, partner_name, total_amount lader_rebate, liter_count lader_number FROM
        (SELECT DISTINCT o.id partner_id,o.organization_name
        partner_name,sum(t_b.transaction_amount)total_amount,sum(t_b.liter_count)liter_count
        FROM wx_t_partner_bill_detail t_b
        JOIN wx_t_organization o ON t_b.partner_id = o.id
        WHERE t_b.transaction_source = 'LADDERPRICE_REBATE'
        <if test="partnerId!=null">
            AND o.id = #{partnerId}
        </if>
        AND t_b.transaction_time&gt;=#{startDate}
        AND t_b.transaction_time&lt;#{endDate}
        AND o.status=1
        GROUP BY o.id,o.organization_name) T
    </select>


    <resultMap id="SpEmailMap" type="com.sys.quartz.model.SpEmailInfo">
        <result column="email" property="email" jdbcType="NVARCHAR"/>
        <result column="role_descript" property="roleDescript" jdbcType="NVARCHAR"/>
        <result column="organization_name" property="partnerName" jdbcType="NVARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getSenderEmail" parameterType="map" resultMap="SpEmailMap">
        SELECT DISTINCT tt_user.email,
        tt_role.role_descript,
        tt_user.user_id,
        t_org.id partner_id,
        t_org.organization_name
        FROM wx_t_user tt_user
        JOIN wx_t_userrole t_userrole ON tt_user.user_id = t_userrole.user_id
        JOIN wx_t_role tt_role ON tt_role.role_id = t_userrole.role_id
        JOIN wx_t_organization t_org ON t_org.id = tt_user.org_id
        WHERE tt_role.ch_role_name = 'Service_Partner_Manager'
        AND tt_user.email IS NOT NULL
        AND tt_user.email!=''
        ORDER BY t_org.organization_name
    </select>

    <select id="getCCEmail" parameterType="map" resultMap="SpEmailMap">
        select distinct o.id partner_id,
        o.organization_name,
        u.email
        from wx_t_organization o
        left join wx_t_partner_o2o_enterprise pe on o.id = pe.partner_id
        left join wx_t_partner_responsible pr on pr.partner_id = o.id
        left join wx_t_partner_responsible_main prm
        on prm.id = pr.responsible_main_id and prm.fun_flag = 'order_confirm'
        left join wx_t_user u on u.user_id = prm.user_id
        JOIN wx_t_userrole t_userrole ON u.user_id = t_userrole.user_id
        JOIN wx_t_role tt_role
        ON tt_role.role_id = t_userrole.role_id AND tt_role.ch_role_name = 'Chevron_SAP_CSR'
        where o.type = 1
        and prm.id is not NULL
        AND pe.partner_property = 'NORMAL'
        AND u.email IS NOT NULL
        order by o.id
    </select>

    <select id="getFinalCCEmail" parameterType="map" resultType="String">
        SELECT DISTINCT dic_item_name
        FROM wx_t_dic_item
        WHERE dic_type_code LIKE '%' + #{finalEmailType} + '%'
    </select>


    <resultMap id="SpRebateNewMap" type="com.sys.quartz.model.SpRebateNew">
        <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
        <result column="partner_name" property="partnerName" jdbcType="NVARCHAR"/>
        <result column="rebate_amount" property="rebateAmount" jdbcType="NUMERIC"/>
        <result column="liter_count" property="literCount" jdbcType="INTEGER"/>
        <result column="transaction_source" property="transactionSource" jdbcType="NVARCHAR"/>
    </resultMap>

    <select id="getSpRebate" parameterType="map" resultMap="SpRebateNewMap">
        SELECT partner_id, partner_name, total_amount rebate_amount, liter_count,transaction_source FROM
        (SELECT DISTINCT o.id partner_id,o.organization_name
        partner_name,sum(t_b.transaction_amount)total_amount,sum(t_b.liter_count)liter_count,t_b.transaction_source
        FROM wx_t_partner_bill_detail t_b
        JOIN wx_t_organization o ON t_b.partner_id = o.id
        JOIN wx_t_partner_o2o_enterprise t_p_o2o ON o.id = t_p_o2o.partner_id
        WHERE (t_b.transaction_source = 'LADDERPRICE_REBATE' OR t_b.transaction_source = 'OILVERIFICATION_REBATE')
        <!--  AND (t_p_o2o.partner_property = 'Delo Distributor' OR (t_p_o2o.partner_property='NORMAL' AND o.create_time>='2018-01-01' )) -->
        AND t_p_o2o.partner_property='NORMAL'
        <!-- AND t_b.liter_count IS NOT NULL -->
        AND t_b.liter_count!=0
        AND o.status=1
        <if test="partnerId!=null">
            AND o.id = #{partnerId}
        </if>
        <if test="startDate!=null">
            AND t_b.transaction_time&gt;=#{startDate}
        </if>
        <if test="endDate!=null">
            AND t_b.transaction_time&lt;#{endDate}
        </if>
        <if test="queryDate!=null">
            AND t_b.year_month=#{queryDate}
        </if>
        GROUP BY o.id,o.organization_name,t_b.transaction_source) T
    </select>

    <select id="selectBiCustomersByParams" resultMap="PartnerView" parameterType="map">
        select distinct top ${limit} crss.customer_name_cn name, crss.distributor_id, (select max(c.customer_code) from
        [PP_MID].[dbo].[syn_dw_to_pp_customer] c where c.distributor_id_pp=crss.distributor_id) sap_code
        from [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] crss
        where 1=1
        <!-- <if test="loginCai != null">
            and (not exists (select 1 from dw_customer_region_sales_supervisor_rel crss1 where crss1.sales_cai=#{loginCai} or crss1.suppervisor_cai=#{loginCai})
            or (crss.sales_cai=#{loginCai} or crss.suppervisor_cai=#{loginCai}))
        </if> -->
        <if test="excludePartner == true">
            and (
                not exists (select 1 from wx_t_partner_o2o_enterprise pe left join wx_t_organization o on o.id=pe.partner_id where pe.distributor_id=crss.distributor_id)
                or crss.region = 'Industrial North'
                or crss.region = 'Industrial South'
            )
        </if>
        <if test="keyword != null and keyword != ''">
            and (crss.customer_name_cn like '%' + #{keyword} + '%' or
            left(dbo.f_GetPyToAboutHanyu(crss.customer_name_cn),500) LIKE '%' + #{keyword} + '%')
        </if>
    </select>


    <select id="queryNewPartnerForCtrl" resultMap="PartnerView"
            parameterType="com.chevron.pms.model.NewPartnerCtrlParam">
        select
        t.*, t1.enterprise_code, t1.enterprise_name, t1.distributor_id,
        t1.partner_property,t1.sap_code,t1.ship_to_code,t1.material_stock,t.organization_name as partner_name
        <if test="sellInFlag">
            ,(select distinct sales.region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales sales where 1=1 and
            sales.distributor_id=t1.distributor_id and sales.product_channel
            in(select t.dic_item_code from wx_t_dic_item t where 1=1 and t.status=1 and
            t.dic_type_code='sellIn.salesChannel')
            and sales.sales_name_cn!='NA') as region
        </if>
        from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        where t.type=1 and t.status=1
        <choose>
            <when test="partnerId != null">
                and t.id=#{partnerId,jdbcType=BIGINT}
            </when>
            <when test="distributorId != null">
                and t1.distributor_id=#{distributorId,jdbcType=BIGINT}
            </when>
            <when test="salesId != null">
                and exists (select 1 from wx_t_user u1 left join dw_access_control_customer_org_sales cs1 on
                u1.cai=cs1.sales_cai
                where u1.user_id=#{salesId,jdbcType=BIGINT} and cs1.distributor_id=t1.distributor_id
                <if test="channelWeight != null">
                    and cs1.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
            <when test="salesCai != null and salesCai != ''">
                and exists (select 1 from dw_access_control_customer_org_sales crss where
                crss.distributor_id=t1.distributor_id and crss.sales_cai=#{salesCai,jdbcType=NVARCHAR}
                <if test="channelWeight != null">
                    and crss.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
        </choose>
        <choose>
            <when test="spResource">
                $Permission_Clause$
            </when>
            <otherwise>
                and exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$)
            </otherwise>
        </choose>
        <if test="sellInFlag">
            and exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales crss where 1=1
            and crss.distributor_id = t1.distributor_id and sales_name_cn !='NA'
            and crss.product_channel in(select t.dic_item_code from wx_t_dic_item t where 1=1 and t.status=1 and
            t.dic_type_code='sellIn.salesChannel')
            <!-- <if test="salesChannels!=null and salesChannels!=''">
                and crss.product_channel= #{salesChannels}
            </if> -->
            <if test="region!=null and region!=''">
                and crss.region=#{region}
            </if>
            <if test="customerType!=null and customerType!=''">
                and crss.customer_type=#{customerType}
            </if>
            )
        </if>
        <if test="onlyTarget">
            and exists (select 1 from dw_access_control_customer_org_sales crss where
            crss.distributor_id=t1.distributor_id
            <if test="salesChannels!=null and salesChannels!=''">
                and crss.product_channel= #{salesChannels}
            </if>
            )
        </if>
        <if test="salesChannels!=null and salesChannels!=''">
            <choose>
                <when test="salesChannels=='Industrial'">
                    and EXISTS (select 1 from  dw_customer_org_sales org where t1.distributor_id=org.distributor_id and org.channel_weight=8 and org.region like'%Industrial%')
                </when>
                <otherwise>
                    and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=t.id and rp1.channel_weight&amp;(case
                    when #{salesChannels}='commercial' then 2 when #{salesChannels}='Consumer' then 1 else 0 end)>0)
                </otherwise>
            </choose>
        </if>
        <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and
            i1.partner_id=t.id)
        </if>
        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
        <if test="queryField != null">
            and t.organization_name like '%' + #{queryField} + '%'
        </if>
    </select>

    <select id="queryPartnerForProductConfig" resultMap="PartnerView"
            parameterType="com.chevron.pms.model.NewPartnerCtrlParam">
        select distinct
        t.*, t1.enterprise_code, t1.enterprise_name, t1.distributor_id,
        t1.partner_property,t1.sap_code,t1.ship_to_code,t1.material_stock,t.organization_name as partner_name
        ,p.region_name
            ,(select distinct sales.region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales sales where 1=1 and
            sales.distributor_id=t1.distributor_id and sales.region
            in(select t.dic_item_code from wx_t_dic_item t where 1=1 and t.status=1 and
            t.dic_type_code='sellIn.salesRegion')
            and sales.sales_name_cn!='NA') as region
        from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
            left JOIN wx_t_material_address_config mac on mac.partner_id = t.id
            left join wx_t_region d on d.id = mac.ADDRESS_REGION
            left join wx_t_region c on c.id = d.parent_id
            left join wx_t_region p on p.id = c.parent_id
        where t.type=1 and t.status=1
        <choose>
            <when test="partnerId != null">
                and t.id=#{partnerId,jdbcType=BIGINT}
            </when>
            <when test="distributorId != null">
                and t1.distributor_id=#{distributorId,jdbcType=BIGINT}
            </when>
            <when test="salesId != null">
                and exists (select 1 from wx_t_user u1 left join dw_access_control_customer_org_sales cs1 on
                u1.cai=cs1.sales_cai
                where u1.user_id=#{salesId,jdbcType=BIGINT} and cs1.distributor_id=t1.distributor_id
                <if test="channelWeight != null">
                    and cs1.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
            <when test="salesCai != null and salesCai != ''">
                and exists (select 1 from dw_access_control_customer_org_sales crss where
                crss.distributor_id=t1.distributor_id and crss.sales_cai=#{salesCai,jdbcType=NVARCHAR}
                <if test="channelWeight != null">
                    and crss.[channel_weight]&amp;${channelWeight}>0
                </if>
                )
            </when>
        </choose>
        <choose>
            <when test="spResource">
                $Permission_Clause$
            </when>
            <otherwise>
                and exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$)
            </otherwise>
        </choose>
        <if test="sellInFlag">
            and exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales crss where 1=1
            and crss.distributor_id = t1.distributor_id and sales_name_cn !='NA'
            and crss.region in(select t.dic_item_code from wx_t_dic_item t where 1=1 and t.status=1 and
            t.dic_type_code='sellIn.salesRegion')
            <if test="region!=null and region!=''">
                and crss.region=#{region}
            </if>
            <if test="customerType!=null and customerType!=''">
                and crss.customer_type=#{customerType}
            </if>
            )
        </if>
        <if test="onlyTarget">
            and exists (select 1 from dw_access_control_customer_org_sales crss where
            crss.distributor_id=t1.distributor_id
            <if test="salesChannels!=null and salesChannels!=''">
                and crss.product_channel= #{salesChannels}
            </if>
            )
        </if>
        <if test="salesChannels!=null and salesChannels!=''">
            <choose>
                <when test="salesChannels=='Industrial'">
                    and EXISTS (select 1 from  dw_customer_org_sales org where t1.distributor_id=org.distributor_id and org.channel_weight=8 and org.region like'%Industrial%')
                </when>
                <otherwise>
                    and exists (select 1 from wx_t_region_partner rp1 where rp1.partner_id=t.id and rp1.channel_weight&amp;(case
                    when #{salesChannels}='commercial' then 2 when #{salesChannels}='Consumer' then 1 else 0 end)>0)
                </otherwise>
            </choose>
        </if>
        <!-- 省市区-->
        <if test="regionIdList != null">
            and p.id in <foreach item="id" index="index" collection="regionIdList" open="(" separator="," close=")">
            ${id}
        </foreach>
        </if>

        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
        <if test="queryField != null">
            and t.organization_name like '%' + #{queryField} + '%'
        </if>
    </select>

    <select id="getDistributors" resultMap="PartnerView" parameterType="map">
        select
        t.*, t1.enterprise_code, t1.enterprise_name, t1.distributor_id,
        t1.partner_property,t1.sap_code,t1.ship_to_code,t1.material_stock
        from wx_t_organization t left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
        where t.type=1 and t.status=1
        <if test="distributorIdsList != null">
            and t.id in
            <foreach item="distributorId" index="index" collection="distributorIdsList" open="(" separator=","
                     close=")">
                #{distributorId}
            </foreach>
        </if>
    </select>


    <resultMap id="PartnerInfoMap" type="com.sys.organization.model.PartnerInfoVo">
        <result column="partner_id" property="id" jdbcType="BIGINT"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="customer_name_cn" property="organizationName" jdbcType="NUMERIC"/>
        <result column="channel_weight" property="channelWeight" jdbcType="INTEGER"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="distributor_years_of_cooperation" property="distributorYearsOfCooperation"/>
    </resultMap>
    <select id="queryPartnerInfo" resultMap="PartnerInfoMap"
            parameterType="com.sys.organization.model.PartnerQueryParams">
        select t1.distributor_id,t1.customer_name_cn,sum(t1.channel_weight)
        channel_weight,t2.partner_id,ps.distributor_years_of_cooperation from
        (select distributor_id,
        customer_name_cn,
        region,
        sales_cai,
        supervisor_cai,
        sum(case
        when product_channel = 'Commercial' then 1
        when product_channel = 'Consumer' then 2
        else 0 end) as channel_weight
        from PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos1
        where 1=1
        <if test="productChannels != null and productChannels.size > 0">
            and product_channel in
            <foreach item="item" index="index" collection="productChannels" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="indirectFlag != null and indirectFlag == true">
            and product_channel is not null
        </if>
        <if test="excludeChildDistributor != null and excludeChildDistributor == 1">
            and not exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_customer] dwc where
            dwc.distributor_id_pp=cos1.distributor_id and dwc.distributor_id_pp!=dwc.distributor_id)
        </if>
        group by distributor_id, customer_name_cn, region, sales_cai, supervisor_cai) t1
        left join (select distributor_id,partner_id from wx_t_partner_o2o_enterprise group by distributor_id,partner_id)
        t2 on t1.distributor_id = t2.distributor_id
        left join (select distinct distributor_id_pp,distributor_years_of_cooperation from
        [PP_MID].[dbo].[syn_dw_to_pp_customer]) ps
        on ps.distributor_id_pp = t2.distributor_id
        <if test="joinSparkPlan != null">
            <!-- 筛选参加星火计划的经销商 -->
            left join (select distinct sort_numb as distributor_id from wx_t_dic_item t where 1=1 and
            t.dic_type_code='mkt_sp_distributer') spark
            on spark.distributor_id = t1.distributor_id
        </if>
        <where>
            and t2.partner_id is not null and t1.sales_cai is not null and len(t1.sales_cai) > 0 and t1.sales_cai !=
            'NA' and t1.distributor_id > 0
            <!-- 如果使用wx_t_partner_responsible_main控制权限 -->
            <if test="mainFunFlag != null and mainFunFlag == true">
                and EXISTS (select 1 from wx_t_partner_responsible_main a
                left join wx_t_userrole b on b.user_id=a.user_id
                left join wx_t_role c on c.role_id=b.role_id
                where 1 = 1 and a.fun_flag= #{mainFun}
                <if test="mainRoleName != null and mainRoleName != ''">
                    and c.ch_role_name= #{mainRoleName}
                </if>
                <if test="mainRegionFlag != null and mainRegionFlag == true">
                    and a.region_name = t1.region
                </if>
                <if test="userId != null">
                    and a.user_id = #{userId}
                </if>
                )
            </if>
            <if test="flsrFlag != null and flsrFlag == true">
                and t1.sales_cai = #{salesCai}
            </if>
            <if test="asmFlag != null and asmFlag == true">
                and t1.supervisor_cai = #{supervisorCai}
            </if>
            <if test="keyword != null and keyword != ''">
                and t1.customer_name_cn like '%' + #{keyword} + '%'
            </if>
            <if test="partnerId != null">
                and t2.partner_id = #{partnerId}
            </if>
            <if test=" region != null and region != '' ">
                and t1.region = #{region}
            </if>
            <if test="distributorYearsOfCooperation != null">
                and ps.distributor_years_of_cooperation = #{distributorYearsOfCooperation}
            </if>
            <!-- 筛选参加星火计划的经销商 -->
            <if test="joinSparkPlan != null">
                <choose>
                    <when test="joinSparkPlan == true">
                        and spark.distributor_id is not null
                    </when>
                    <when test="joinSparkPlan == false">
                        and spark.distributor_id is null
                    </when>
                </choose>
            </if>
        </where>
        group by t1.distributor_id,t1.customer_name_cn,t2.partner_id,ps.distributor_years_of_cooperation
    </select>

    <select id="getIndirectPartnerChannelWeight" resultType="java.lang.String">
        select t1.product_channel from PP_MID.dbo.syn_dw_to_pp_customer_org_sales t1 left join (select
        distributor_id,partner_id from wx_t_partner_o2o_enterprise group by distributor_id,partner_id) t2
        on t1.distributor_id = t2.distributor_id
        <where>
            <if test="distributorId!=null">
                t1.distributor_id = #{distributorId}
            </if>
            <if test="partnerId!=null">
                t2.partner_id = #{partnerId}
            </if>
            and t1.product_channel is not null
        </where>
        group by t1.product_channel
    </select>
    <select id="getChargeRegion" resultType="java.lang.String">
        select region
        from PP_MID.dbo.syn_dw_to_pp_customer_org_sales
        where (sales_cai = #{cai} or supervisor_cai = #{cai})
        group by region
    </select>

    <select id="getPartnerViewByIds" resultMap="PartnerView">
        select distinct id,organization_name as partner_name from wx_t_organization
        <where>
            <if test="partnerIds!=null and partnerIds.size > 0">
                and id in
                <foreach item="id" index="index" collection="partnerIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <!-- <select id="getChildrenDistributorIds" resultType="long">
        select vtm.[value_before_transform] from [wx_t_value_transform_map] vtm where vtm.[transform_type]='RootDistributorMapping' and vtm.[value_after_transform]=#{distributorId}
    </select> -->


    <select id="getChildPartners" resultMap="PartnerInfoMap"
            parameterType="com.sys.organization.model.PartnerQueryParams">
        select distinct o.organization_name customer_name_cn,t.distributor_id_pp as
        distributor_id,t.partner_id,t.distributor_years_of_cooperation
        from [PP_MID].[dbo].[syn_dw_to_pp_customer] t
        join wx_t_partner_o2o_enterprise pe on pe.distributor_id = t.distributor_id_pp
        left join wx_t_organization o on o.id=pe.partner_id
        where t.distributor_id != t.distributor_id_pp
        <if test="partnerId != null">
            and pe.partner_id = #{partnerId}
        </if>
        <if test="distributorId != null">
            and t.distributor_id = #{distributorId}
        </if>
    </select>

    <resultMap id="DistributorBySapCodeMap" type="com.sys.organization.model.DistributorBySapCodeVo">
        <result column="sap_code" property="sapCode" jdbcType="VARCHAR"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getDistributorBySapCode" resultMap="DistributorBySapCodeMap"
            parameterType="com.sys.organization.model.DistributorBySapCodeParams">
        select c.customer_code sap_code,pe.distributor_id as distributor_id,o.organization_name as distributor_name
        from wx_t_partner_o2o_enterprise pe
        join wx_t_organization o on pe.partner_id=o.id
        join [PP_MID].[dbo].[syn_dw_to_pp_customer] c on pe.distributor_id=c.[distributor_id_pp]
        where o.status=1 $Permission_Clause$
        and exists (select 1 from dw_customer_org_sales dcos where dcos.distributor_id=pe.distributor_id)
        <if test="sapCode != null and sapCode != '' ">
            and (c.customer_code like '%' + #{sapCode} + '%' or c.customer_name_cn like '%' + #{sapCode} + '%')
        </if>
    </select>
    <select id="getDistributorBySapCodeNoPermission" resultMap="DistributorBySapCodeMap"
            parameterType="com.sys.organization.model.DistributorBySapCodeParams">
        select c.customer_code sap_code,pe.distributor_id as distributor_id,o.organization_name as distributor_name
        from wx_t_partner_o2o_enterprise pe
        join wx_t_organization o on pe.partner_id=o.id
        join [PP_MID].[dbo].[syn_dw_to_pp_customer] c on pe.distributor_id=c.[distributor_id_pp]
        where 1=1
        and exists (select 1 from dw_customer_org_sales dcos where dcos.distributor_id=pe.distributor_id)
        <if test="sapCode != null and sapCode != '' ">
            and (c.customer_code like '%' + #{sapCode} + '%' or c.customer_name_cn like '%' + #{sapCode} + '%')
        </if>
    </select>

    <!-- 根据id查询分销商的所属经销商-->
    <select id="queryPartnerById" resultMap="PartnerView" parameterType="long">
    SELECT wto.id, poe.ext_property1,wto.type from wx_t_organization wto
    inner join wx_t_partner_o2o_enterprise poe on wto.id = poe.partner_id
    WHERE wto.id = #{partnerId}
    </select>

    <!-- 获取所传销售负责对于的经销商以及所传经销商的 sold to code-->
    <select id="queryDistributorIds" resultType="map">
        SELECT
        t.partner_id,
        STUFF(
        (
        SELECT DISTINCT ',' + CONVERT(NVARCHAR(MAX), mpsc.sap_code)
        FROM wx_t_partner_o2o_enterprise pee
        INNER JOIN (
        SELECT org.id
        FROM wx_t_organization org
        WHERE org.id = #{partnerId,jdbcType=NVARCHAR}   --传入经销商
        UNION
        SELECT DISTINCT org.id
        FROM wx_t_user u1
        LEFT JOIN dw_customer_org_sales ss ON ss.sales_cai = u1.cai
        LEFT JOIN wx_t_partner_o2o_enterprise pee ON ss.distributor_id = pee.distributor_id
        LEFT JOIN wx_t_organization org ON org.id = pee.partner_id
        WHERE u1.user_id = #{userId,jdbcType=NVARCHAR}  --传入销售 获取经销商
        ) sub ON pee.partner_id = sub.id
        left join [PP_MID].dbo.mid_partner_sale_config mpsc on mpsc.distributor_id = pee.distributor_id
        WHERE pee.distributor_id IS NOT NULL
        FOR XML PATH(''), TYPE
        ).value('.', 'NVARCHAR(MAX)'),
        1, 1, ''
        ) AS sap_codes
        FROM (
        SELECT org.id as partner_id
        FROM wx_t_organization org
        WHERE org.id = #{partnerId,jdbcType=NVARCHAR}
        UNION
        SELECT DISTINCT org.id as partner_id
        FROM wx_t_user u1
        LEFT JOIN dw_customer_org_sales ss ON ss.sales_cai = u1.cai
        LEFT JOIN wx_t_partner_o2o_enterprise pee ON ss.distributor_id = pee.distributor_id
        LEFT JOIN wx_t_organization org ON org.id = pee.partner_id
        WHERE u1.user_id = #{userId,jdbcType=NVARCHAR}
        ) t WHERE t.partner_id  = #{partnerId,jdbcType=NVARCHAR}

    </select>

</mapper>