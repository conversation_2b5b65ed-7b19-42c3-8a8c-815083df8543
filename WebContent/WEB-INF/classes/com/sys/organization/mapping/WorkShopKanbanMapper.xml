<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.organization.dao.WorkShopKanbanMapper" >
    <resultMap id="QueryCustomerMap" type="com.chevron.report.model.QueryCustomerVo">
        <result column="work_shop_id" property="workShopId" jdbcType="NUMERIC"/>
        <result column="work_shop_name" property="workShopName" jdbcType="NUMERIC"/>
        <result column="partner_id" property="partnerId" jdbcType="NUMERIC"/>
        <result column="distributor_id" property="distributorId" jdbcType="NUMERIC"/>
        <result column="partner_name" property="partnerName" jdbcType="NUMERIC"/>
        <result column="dsr_code" property="dsrCode" jdbcType="NUMERIC"/>
        <result column="dsr_name" property="dsrName" jdbcType="NUMERIC"/>
        <result column="end_customer_code" property="endCustomerCode" jdbcType="NUMERIC"/>
    </resultMap>

    <sql id="queryPartnerList">
        select distinct
        dcos.customer_name_cn  as partner_name,
        dcos.distributor_id
        from dw_customer_org_sales dcos
        left join wx_t_partner_o2o_enterprise wtpooe on  wtpooe.distributor_id = dcos.distributor_id
        left join wx_t_work_shop wtws on wtpooe.partner_id = wtws.partner_id
        where dcos.del_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <if test="'FLSR'.equals(userRole)">
            and dcos.sales_cai = #{saleCai,jdbcType=VARCHAR}
        </if>
        <if test="dsrCode != null">
            and wtws.excute_user_id = #{dsrCode,jdbcType=BIGINT}
            and wtws.delete_flag = 0
        </if>
        <if test="distributorId != null">
            and wtpooe.distributor_id = #{distributorId,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null and partnerName != ''">
            and dcos.customer_name_cn like concat('%',#{partnerName,jdbcType=VARCHAR},'%')
        </if>
    </sql>
    <sql id="queryStoreList">
        select distinct
        dcos.customer_name_cn as partner_name,
        wtpooe.distributor_id,
        wtws.id as work_shop_id,
        wtws.work_shop_name,
        wtws.customer_code as end_customer_code
        from dw_customer_org_sales dcos
        left join  wx_t_partner_o2o_enterprise wtpooe on  wtpooe.distributor_id = dcos.distributor_id
        left join wx_t_work_shop wtws on wtpooe.partner_id = wtws.partner_id
        where dcos.del_flag = 0 and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <if test="'FLSR'.equals(userRole)">
            and dcos.sales_cai = #{saleCai,jdbcType=VARCHAR}
        </if>
        <if test="dsrCode != null">
            and wtws.excute_user_id = #{dsrCode,jdbcType=BIGINT}
        </if>
        <if test="endCustomerCode != null and endCustomerCode != ''">
            and wtws.customer_code  = #{endCustomerCode,jdbcType=VARCHAR}
        </if>
        <if test="workShopName != null and workShopName != ''">
            and wtws.work_shop_name like concat('%',#{workShopName,jdbcType=VARCHAR},'%')
        </if>
        <if test="distributorId != null">
            and wtpooe.distributor_id = #{distributorId,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null and partnerName != ''">
            and dcos.customer_name_cn like concat('%',#{partnerName,jdbcType=VARCHAR},'%')
        </if>
    </sql>
    <sql id="queryDsrList">
        select distinct
        wtpooe.partner_id as partner_id,
        dcos.customer_name_cn as partner_name,
        wtws.excute_user_id as dsr_code,
        wtu.ch_name as dsr_name
        from dw_customer_org_sales dcos
        left join  wx_t_partner_o2o_enterprise wtpooe on  wtpooe.distributor_id = dcos.distributor_id
        left join wx_t_work_shop wtws on wtpooe.partner_id = wtws.partner_id
        left join wx_t_user wtu on wtws.excute_user_id  = wtu.user_id
        where dcos.del_flag = 0 and wtws.delete_flag = 0
        and wtws.excute_user_id is not null and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <if test="'FLSR'.equals(userRole)">
            and dcos.sales_cai = #{saleCai,jdbcType=VARCHAR}
        </if>
        <if test="dsrCode != null">
            and wtws.excute_user_id = #{dsrCode,jdbcType=BIGINT}
        </if>
        <if test="dsrName != null and dsrName != ''">
            and wtu.ch_name like concat('%',#{dsrName},'%')
        </if>
        <if test="distributorId != null">
            and wtpooe.distributor_id = #{distributorId,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null and partnerName != ''">
            and dcos.customer_name_cn like concat('%',#{partnerName,jdbcType=VARCHAR},'%')
        </if>
    </sql>

    <sql id="queryStoreInfoByDsr">
        with total_amount as (
        select COALESCE(count( distinct wtws.id),0)as totalAmount
        from wx_t_work_shop wtws
        where wtws.excute_user_id = #{vo.dsrCode}
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        ),
        cooperative_stores as (
        select COALESCE(count(distinct wtws.id),0)as cooperativeStores
        from wx_t_work_shop wtws
        where wtws.excute_user_id = #{vo.dsrCode}
        and wtws.delete_flag = 0
        and wtws.status = 3 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <!--and 门店状态 = 合作--> ),

        potential_stores as (
        select COALESCE(count(distinct wtws.id),0)as potentialStores
        from wx_t_work_shop wtws
        where wtws.excute_user_id = #{vo.dsrCode}
        and wtws.delete_flag = 0
        and wtws.status = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <!--and 门店状态 = 潜在--> ),

        <!--本月新开门店-->
        new_stores_this_month as (
        select COALESCE(count(distinct scan.end_customer_code),0)as newStoresThisMonth
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        where scan.dsr_code = #{vo.dsrCode,jdbcType=BIGINT}
        and scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.monthBegin} and #{vo.monthEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        ),

        <!--本年新开门店-->
        new_stores_this_year as (
        select COALESCE(count(distinct scan.end_customer_code),0)as newStoresThisYear
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        where scan.dsr_code = #{vo.dsrCode,jdbcType=BIGINT}
        and scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.yearBegin} and #{vo.yearEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        )

        select totalAmount, cooperativeStores,potentialStores,newStoresThisMonth,newStoresThisYear
        from total_amount,cooperative_stores,potential_stores,new_stores_this_month,new_stores_this_year
    </sql>

    <sql id="queryStoreInfoByFlsr">
        with total_amount as (
        select COALESCE(count(distinct wtws.id),0)as totalAmount
        from wx_t_work_shop wtws
        left join wx_t_partner_o2o_enterprise wtpo2oe  on wtws.partner_id  = wtpo2oe.partner_id
        left join dw_customer_org_sales dcos on wtpo2oe.distributor_id = dcos.distributor_id
        where dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        ),
        <!--and 门店状态 = 合作-->
        cooperative_stores as (
        select COALESCE(count(distinct wtws.id),0)as cooperativeStores
        from wx_t_work_shop wtws
        left join wx_t_partner_o2o_enterprise wtpo2oe  on wtws.partner_id  = wtpo2oe.partner_id
        left join dw_customer_org_sales dcos on wtpo2oe.distributor_id = dcos.distributor_id
        where dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        and wtws.delete_flag = 0
        and wtws.status = 3 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
       ),
        <!--and 门店状态 = 潜在-->
        potential_stores as (
        select COALESCE(count(distinct wtws.id),0)as potentialStores
        from wx_t_work_shop wtws
        left join wx_t_partner_o2o_enterprise wtpo2oe  on wtws.partner_id  = wtpo2oe.partner_id
        left join dw_customer_org_sales dcos on wtpo2oe.distributor_id = dcos.distributor_id
        where dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        and wtws.delete_flag = 0
        and wtws.status = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        ),

        <!--本月新开门店-->
        new_stores_this_month as (
        select COALESCE(count(distinct scan.end_customer_code),0)as newStoresThisMonth
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        inner join dw_customer_org_sales dcos on scan.distributor_id  = dcos.distributor_id
        where dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR} and dcos.product_channel = scan.product_channel
        and scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.monthBegin} and #{vo.monthEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        ),

        <!--本年新开门店-->
        new_stores_this_year as (
        select COALESCE(count(distinct scan.end_customer_code),0)as newStoresThisYear
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        inner join dw_customer_org_sales dcos on scan.distributor_id  = dcos.distributor_id
        where dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR} and dcos.product_channel = scan.product_channel
        and scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.yearBegin} and #{vo.yearEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        )

        select totalAmount, cooperativeStores,potentialStores,newStoresThisMonth,newStoresThisYear
        from total_amount,cooperative_stores,potential_stores,new_stores_this_month,new_stores_this_year
    </sql>

    <sql id="queryTotal">
        with total_amount as (
        select COALESCE(count(distinct wtws.id),0)as totalAmount
        from wx_t_work_shop wtws
        where wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        ),
        cooperative_stores as (
        select COALESCE(count(distinct wtws.id),0)as cooperativeStores
        from wx_t_work_shop wtws
        where wtws.delete_flag = 0
        and wtws.status = 3 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <!--and 门店状态 = 合作--> ),

        potential_stores as (
        select COALESCE(count(wtws.id),0)as potentialStores
        from wx_t_work_shop wtws
        where wtws.delete_flag = 0
        and wtws.status = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        <!--and 门店状态 = 潜在--> ),

        <!--本月新开门店-->
        new_stores_this_month as (
        select COALESCE(count(distinct scan.end_customer_id),0)as newStoresThisMonth
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        where scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.monthBegin} and #{vo.monthEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        ),

        <!--本年新开门店-->
        new_stores_this_year as (
        select COALESCE(count(distinct scan.end_customer_id),0)as newStoresThisYear
        from [pp_mid].dbo.syn_dw_to_pp_partner_end_customer_scan_dtl scan
        inner join wx_t_work_shop wtws on wtws.customer_code = scan.end_customer_code
        WHERE scan.first_scan_date >= '2024-01-01 00:00:00'
        and scan.first_scan_date between #{vo.yearBegin} and #{vo.yearEnd}
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        )

        select totalAmount, cooperativeStores,potentialStores,newStoresThisMonth,newStoresThisYear
        from total_amount,cooperative_stores,potential_stores,new_stores_this_month,new_stores_this_year

    </sql>




    <select id="getWorkShopCount" resultType="com.chevron.report.model.workShopAmountResp">
        <choose>
            <when test="vo.roleName.equals('DSR')">
                <include refid="queryStoreInfoByDsr"/>
            </when>
            <when test="vo.roleName.equals('FLSR')">
                <include refid="queryStoreInfoByFlsr"/>
            </when>
            <otherwise>
                <include refid="queryTotal"/>
            </otherwise>
        </choose>

    </select>

    <select id="getSellThroughByMonth" resultType="com.chevron.report.model.SellThroughCountByStoreResp">
        SELECT
        concat(year(th.sales_date),'/',month(th.sales_date)) as yearMonth,
        COALESCE(SUM(th.sell_through_volume_l),0) as categoryCount,
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END AS t3Category
        FROM PP_MID.dbo.syn_dw_to_pp_partner_end_customer_sell_through th
        left join PP_MID.dbo.syn_dw_to_pp_partner_chevron_product vd on th.product_rel_id = vd.product_rel_id
        left join dw_customer_org_sales dcos on th.distributor_id  = dcos.distributor_id and dcos.product_channel = vd.product_channel
        left join wx_t_order wto on th.saleNo = wto.order_no
        left join wx_t_work_shop wtws on wto.work_shop_id = wtws.id
        WHERE th.sales_date between '2024-01-01 00:00:00' and #{vo.yearEnd,jdbcType=TIMESTAMP}
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and vd.product_channel in ('Commercial','Consumer')
        <if test="'FLSR'.equals(vo.roleName)">
            and dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        </if>
        <if test="'DSR'.equals(vo.roleName)">
            and wtws.excute_user_id = #{vo.dsrId,jdbcType=BIGINT}
        </if>
        group by
        concat(year(th.sales_date),'/',month(th.sales_date)),
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END
    </select>

    <select id="selectCustomerRelationList" resultMap="QueryCustomerMap">
        <choose>
            <when test="'distributorId'.equals(field)">
                <include refid="queryPartnerList"/>
            </when>
            <when test="'endCustomerCode'.equals(field)">
                <include refid="queryStoreList"/>
            </when>
            <otherwise>
                <include refid="queryDsrList"/>
            </otherwise>
        </choose>
    </select>

    <select id="selectSellThroughByStore" resultType="com.chevron.report.model.SellThroughCountByStoreResp">
        SELECT
        wtws.customer_code as endCustomerCode,
        COALESCE(SUM(th.sell_through_volume_l),0) as categoryCount,
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END AS t3Category
        FROM PP_MID.dbo.syn_dw_to_pp_partner_end_customer_sell_through th
        left join PP_MID.dbo.syn_dw_to_pp_partner_chevron_product vd on th.product_rel_id = vd.product_rel_id
        left join dw_customer_org_sales dcos on th.distributor_id  = dcos.distributor_id and dcos.product_channel = vd.product_channel
        left join wx_t_order wto on th.saleNo = wto.order_no
        left join wx_t_work_shop wtws on wto.work_shop_id = wtws.id
        WHERE th.sales_date between '2024-01-01 00:00:00' and #{yearEnd,jdbcType=TIMESTAMP}
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and vd.product_channel in ('Commercial','Consumer')
        <if test="'FLSR'.equals(roleName)">
            and dcos.sales_cai = #{salesCai,jdbcType=VARCHAR}
        </if>
        <if test="dsrCode != null">
            and wtws.excute_user_id = #{dsrCode,jdbcType=BIGINT}
        </if>
        <if test="distributorId != null">
            and th.distributor_id = #{distributorId,jdbcType=BIGINT}
        </if>
        <if test="endCustomerCode != null">
            and wtws.customer_code = #{endCustomerCode,jdbcType=BIGINT}
        </if>
        <if test="startDate != null">
            and <![CDATA[th.sales_date >= #{startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="endDate != null">
            and <![CDATA[th.sales_date <= #{endDate,jdbcType=TIMESTAMP}]]>
        </if>
        group by
        wtws.customer_code,
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END
    </select>

    <select id="selectChannelCountByStore" resultType="com.chevron.report.model.SellThroughCountByStoreResp">
        SELECT
        wtws.customer_code as endCustomerCode,
        COALESCE(SUM(th.sell_through_volume_l),0) as categoryCount,
        case
        when vd.product_channel = N'Consumer' Then N'乘用车'
        when vd.product_channel = N'Commercial' Then N'商用油'
        else N'其他'
        END AS productChannel
        FROM PP_MID.dbo.syn_dw_to_pp_partner_end_customer_sell_through th
        left join PP_MID.dbo.syn_dw_to_pp_partner_chevron_product vd on th.product_rel_id = vd.product_rel_id
        left join dw_customer_org_sales dcos on th.distributor_id  = dcos.distributor_id and dcos.product_channel = vd.product_channel
        left join wx_t_order wto on th.saleNo = wto.order_no
        left join wx_t_work_shop wtws on wto.work_shop_id = wtws.id
        WHERE th.sales_date between '2024-01-01 00:00:00' and #{yearEnd,jdbcType=TIMESTAMP}
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and vd.product_channel in ('Commercial','Consumer')
        <if test="'FLSR'.equals(roleName)">
            and dcos.sales_cai = #{salesCai,jdbcType=VARCHAR}
        </if>
        <if test="dsrCode != null">
            and wtws.excute_user_id = #{dsrCode,jdbcType=BIGINT}
        </if>
        <if test="distributorId != null">
            and th.distributor_id = #{distributorId,jdbcType=BIGINT}
        </if>
        <if test="endCustomerCode != null">
            and wtws.customer_code = #{endCustomerCode,jdbcType=BIGINT}
        </if>
        <if test="startDate != null">
            and <![CDATA[th.sales_date >= #{startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="endDate != null">
            and <![CDATA[th.sales_date <= #{endDate,jdbcType=TIMESTAMP}]]>
        </if>
        group by
        wtws.customer_code,
        case
        when vd.product_channel = N'Consumer' Then N'乘用车'
        when vd.product_channel = N'Commercial' Then N'商用油'
        else N'其他'
        END
    </select>

    <select id="getCategoryTendency" resultType="com.chevron.report.model.SellThroughCountByStoreResp">
        select
        concat(year(th.sales_date),'/',month(th.sales_date)) as yearMonth,
        COALESCE(SUM(th.sell_through_volume_l),0)  as categoryCount,
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END AS t3Category
        FROM PP_MID.dbo.syn_dw_to_pp_partner_end_customer_sell_through th
        left join PP_MID.dbo.syn_dw_to_pp_partner_chevron_product vd on th.product_rel_id = vd.product_rel_id
        left join dw_customer_org_sales dcos on th.distributor_id  = dcos.distributor_id and dcos.product_channel = vd.product_channel
        left join wx_t_order wto on th.saleNo = wto.order_no
        left join wx_t_work_shop wtws on wto.work_shop_id = wtws.id
        WHERE th.sales_date >= '2024-01-01 00:00:00'
        and th.sales_date >= DATEFROMPARTS(YEAR(GETDATE()), 1, 1)
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and vd.product_channel in ('Commercial','Consumer')
        <if test="'FLSR'.equals(vo.roleName)">
            and dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        </if>
        <if test="vo.dsrCode != null">
            and wtws.excute_user_id = #{vo.dsrCode,jdbcType=BIGINT}
        </if>
        <if test="vo.distributorId != null">
            and th.distributor_id = #{vo.distributorId,jdbcType=BIGINT}
        </if>
        <if test="vo.endCustomerCode != null">
            and wtws.customer_code = #{vo.endCustomerCode,jdbcType=BIGINT}
        </if>
        <if test="vo.startDate != null">
            and <![CDATA[th.sales_date >= #{vo.startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="vo.endDate != null">
           and <![CDATA[th.sales_date <= #{vo.endDate,jdbcType=TIMESTAMP}]]>
        </if>
        group by
        concat(year(th.sales_date),'/',month(th.sales_date)),
        case
        when vd.qhc_type = N'金盾' Then N'金盾'
        when vd.qhc_type = N'银盾' Then N'银盾'
        when vd.qhc_type = N'星盾' Then N'星盾'
        when vd.qhc_type = N'蓝盾' Then N'蓝盾'
        else N'其他'
        END
    </select>

    <select id="getChannelTendency" resultType="com.chevron.report.model.SellThroughCountByStoreResp">
        select
        concat(year(th.sales_date),'/',month(th.sales_date)) as yearMonth,
        COALESCE(SUM(th.sell_through_volume_l),0)  as categoryCount,
        case
        when vd.product_channel = 'Consumer' Then N'乘用车'
        when vd.product_channel = 'Commercial' Then N'商用油'
        else N'其他'
        END   AS productChannel
        FROM PP_MID.dbo.syn_dw_to_pp_partner_end_customer_sell_through th
        left join PP_MID.dbo.syn_dw_to_pp_partner_chevron_product vd on th.product_rel_id = vd.product_rel_id
        left join dw_customer_org_sales dcos on th.distributor_id  = dcos.distributor_id and dcos.product_channel = vd.product_channel
        left join wx_t_order wto on th.saleNo = wto.order_no
        left join wx_t_work_shop wtws on wto.work_shop_id = wtws.id
        WHERE th.sales_date >= '2024-01-01 00:00:00'
        and th.sales_date >= DATEFROMPARTS(YEAR(GETDATE()), 1, 1)
        and wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and vd.product_channel in ('Commercial','Consumer')
        <if test="'FLSR'.equals(vo.roleName)">
            and dcos.sales_cai = #{vo.salesCai,jdbcType=VARCHAR}
        </if>
        <if test="vo.dsrCode != null">
            and wtws.excute_user_id = #{vo.dsrCode,jdbcType=BIGINT}
        </if>
        <if test="vo.distributorId != null">
            and th.distributor_id = #{vo.distributorId,jdbcType=BIGINT}
        </if>
        <if test="vo.endCustomerCode != null">
            and wtws.customer_code = #{vo.endCustomerCode,jdbcType=BIGINT}
        </if>
        <if test="vo.startDate != null">
            and <![CDATA[th.sales_date >= #{vo.startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="vo.endDate != null">
            and <![CDATA[th.sales_date <= #{vo.endDate,jdbcType=TIMESTAMP}]]>
        </if>
        group by
        concat(year(th.sales_date),'/',month(th.sales_date)),
        case
        when vd.product_channel = 'Consumer' Then N'乘用车'
        when vd.product_channel = 'Commercial' Then N'商用油'
        else N'其他'
        END
    </select>

    <select id="queryWorkshopInspectionsSummaryData" resultType="com.chevron.report.model.WorkshopInspectionsSummaryDataVo">
        select
            sum(case when inspections_date like concat(#{month}, '%') then dsr_daily_actual_inspections_times else 0 end) as monthTimes,
            sum(case when (inspections_date like concat(#{month}, '%') and dsr_daily_actual_qualified_flag = 1) then 1 else 0 end) as monthDays,
            sum(dsr_daily_actual_inspections_times) as yearTimes,
            sum(case when dsr_daily_actual_qualified_flag = 1 then 1 else 0 end) as yearDays
        from (
            select
                inspections_date,
                dsr_daily_actual_inspections_times,
                dsr_daily_actual_qualified_flag
            from PP_MID.dbo.syn_dw_to_pp_partner_end_customer_inspections sdtppeci
            inner join wx_t_work_shop wtws on wtws.customer_code = sdtppeci.end_customer_code
            where inspections_date >= '2024-01-01 00:00:00' and inspections_date like concat(#{year}, '%')
            and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
            <if test="'DSR'.equals(userRole)">
                and dsr_code = #{dsrCode}
            </if>
            <if test="'FLSR'.equals(userRole)">
                and distributor_id in (select distributor_id from dw_customer_org_sales where sales_cai = #{salesCai})
            </if>
            group by dsr_code, inspections_date, dsr_daily_actual_inspections_times, dsr_daily_actual_qualified_flag
        ) tab
    </select>

    <select id="queryCapScanSummaryDataToMonth" resultType="com.chevron.report.model.CapScanMonthSummaryDataVo">
        select
        format(end_customer_scan_date, 'yyyy-MM') as monthStr,
        sum(unit) as monthUnit,
        sum(case when product_category in (N'金盾',N'银盾',N'星盾',N'蓝盾') then unit else 0 end) as qhcMonthUnit,
        sum(case when product_category = N'金盾' then unit else 0 end) as premiumMonthUnit,
        sum(case when product_category = N'银盾' then unit else 0 end) as valueMonthUnit,
        sum(case when product_category = N'星盾' then unit else 0 end) as ecoMonthUnit,
        sum(case when product_category = N'蓝盾' then unit else 0 end) as entryMonthUnit
        from PP_MID.dbo.syn_dw_to_pp_partner_end_customer_scan_dtl sdtppecsd
        inner join wx_t_work_shop wtws on wtws.customer_code = sdtppecsd.end_customer_code
        inner join dw_customer_org_sales dcos on sdtppecsd.distributor_id = dcos.distributor_id and dcos.product_channel = sdtppecsd.product_channel
        where end_customer_scan_date >= '2024-01-01 00:00:00' and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        and sdtppecsd.product_channel in ('Commercial','Consumer') and (
        <foreach collection="months" item="month" separator=" or " >
                end_customer_scan_date like concat(#{month}, '%')
            </foreach>
        )
        <if test="'DSR'.equals(userRole)">
            and dsr_code = #{dsrCode}
        </if>
        <if test="'FLSR'.equals(userRole)">
            and  dcos.sales_cai = #{salesCai}
        </if>
        group by format(end_customer_scan_date, 'yyyy-MM')
    </select>

    <select id="queryCapScanSummaryDataToYear" resultType="com.chevron.report.model.CapScanYearSummaryDataVo">
        select
        sum(unit) as yearUnit,
        sum(case when product_category in (N'金盾',N'银盾',N'星盾',N'蓝盾') then unit else 0 end) as qhcYearUnit
        from PP_MID.dbo.syn_dw_to_pp_partner_end_customer_scan_dtl sdtppecsd
        inner join wx_t_work_shop wtws on wtws.customer_code = sdtppecsd.end_customer_code
        inner join dw_customer_org_sales dcos on sdtppecsd.distributor_id = dcos.distributor_id and dcos.product_channel = sdtppecsd.product_channel
        where end_customer_scan_date >= '2024-01-01 00:00:00' and end_customer_scan_date like concat(#{year}, '%')
        and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%' and sdtppecsd.product_channel in ('Commercial','Consumer')
        <if test="'DSR'.equals(userRole)">
            and dsr_code = #{dsrCode}
        </if>
        <if test="'FLSR'.equals(userRole)">
            and  dcos.sales_cai = #{salesCai}
        </if>
    </select>

    <select id="queryCapScanTrendData" resultType="com.chevron.report.model.CapScanMonthTrendDataVo">
        select tab.month_str as monthStr, tab.month_unit as monthUnit, tab.month_times as monthTimes from (
            select
                format(end_customer_scan_date, 'MM') as month_str,
                sum(unit) as month_unit,
                sum(end_customer_scan_times) as month_times
            from PP_MID.dbo.syn_dw_to_pp_partner_end_customer_scan_dtl sdtppecsd
            inner join wx_t_work_shop wtws on wtws.customer_code = sdtppecsd.end_customer_code
            inner join dw_customer_org_sales dcos on sdtppecsd.distributor_id = dcos.distributor_id and sdtppecsd.product_channel = dcos.product_channel
            where end_customer_scan_date >= '2024-01-01 00:00:00' and end_customer_scan_date like concat(#{year}, '%')
            and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%' and sdtppecsd.product_channel in ('Commercial','Consumer')
        <if test="'DSR'.equals(userRole)">
                and dsr_code = #{dsrCode}
            </if>
            <if test="'FLSR'.equals(userRole)">
                and  dcos.sales_cai = #{salesCai}
            </if>
            <if test="req.distributorId != null and req.distributorId != ''">
                and sdtppecsd.distributor_id = #{req.distributorId}
            </if>
            <if test="req.dsrCode != null and req.dsrCode != ''">
                and dsr_code = #{req.dsrCode}
            </if>
            <if test="req.endCustomerCode != null and req.endCustomerCode != ''">
                and end_customer_code = #{req.endCustomerCode}
            </if>
            <if test="req.productCategory != null and req.productCategory != ''">
                and product_category = #{req.productCategory}
            </if>
            group by format(end_customer_scan_date, 'MM')
        ) tab
        order by tab.month_str asc
    </select>

    <select id="queryCapScanDetailToWorkshop" resultType="com.chevron.report.model.CapScanDetailToWorkshopDataVo">
        select
        tab.end_customer_name as endCustomerName, tab.total_unit as totalUnit, tab.total_times as totalTimes,
        tab.premium_unit as premiumUnit, tab.value_unit as valueUnit, tab.eco_unit as ecoUnit,
        tab.entry_unit as entryUnit
        from (
        select
        max(end_customer_name) as end_customer_name,
        sum(unit) as total_unit,
        sum(end_customer_scan_times) as total_times,
        sum(case when product_category = N'金盾' then unit else 0 end) as premium_unit,
        sum(case when product_category = N'银盾' then unit else 0 end) as value_unit,
        sum(case when product_category = N'星盾' then unit else 0 end) as eco_unit,
        sum(case when product_category = N'蓝盾' then unit else 0 end) as entry_unit
        from PP_MID.dbo.syn_dw_to_pp_partner_end_customer_scan_dtl sdtppecsd
        inner join wx_t_work_shop wtws on wtws.customer_code = sdtppecsd.end_customer_code
        inner join dw_customer_org_sales dcos on sdtppecsd.distributor_id = dcos.distributor_id and sdtppecsd.product_channel = dcos.product_channel
        where end_customer_scan_date >= '2024-01-01 00:00:00' and wtws.customer_type = 1 and wtws.delete_flag = 0 and wtws.customer_code not like 'V%'
        and sdtppecsd.product_channel in ('Commercial','Consumer')
        <if test="'DSR'.equals(userRole)">
                and dsr_code = #{dsrCode}
            </if>
            <if test="'FLSR'.equals(userRole)">
                and dcos.sales_cai = #{salesCai}
            </if>
            <if test="req.startDate != null">
                and end_customer_scan_date >= #{req.startDate}
            </if>
            <if test="req.endDate != null">
                and end_customer_scan_date &lt;= #{req.endDate}
            </if>
            <if test="req.distributorId != null and req.distributorId != ''">
                and sdtppecsd.distributor_id = #{req.distributorId}
            </if>
            <if test="req.dsrCode != null and req.dsrCode != ''">
                and dsr_code = #{req.dsrCode}
            </if>
            <if test="req.endCustomerCode != null and req.endCustomerCode != ''">
                and end_customer_code = #{req.endCustomerCode}
            </if>
            group by end_customer_code
        ) tab
        <if test="req.sortField != null and req.sortField != '' and req.sortDirection != null and req.sortDirection != ''">
        order by ${req.sortField} ${req.sortDirection}
        </if>
    </select>

    <select id="selectWorkShopInfo" resultMap="QueryCustomerMap">
        select distinct
        wtws.customer_code as end_customer_code ,wtws.work_shop_name
        from wx_t_work_shop wtws
        left join wx_t_partner_o2o_enterprise wtpo2oe on wtws.partner_id = wtpo2oe.partner_id
        left join dw_customer_org_sales dcos on wtpo2oe.distributor_id = dcos.distributor_id
        where wtws.delete_flag = 0 and wtws.customer_type = 1 and wtws.customer_code not like 'V%'
        and wtws.customer_code in
        <foreach collection="codeList" separator="," close=")" open="(" item="item">
            #{item,jdbcType=NVARCHAR}
        </foreach>
    </select>
</mapper>