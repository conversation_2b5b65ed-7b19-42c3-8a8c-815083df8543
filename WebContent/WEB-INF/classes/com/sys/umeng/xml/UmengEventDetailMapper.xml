<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sys.umeng.dao.UmengEventDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sys.umeng.model.UmengEventDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="umodel_name" property="umodelName" jdbcType="NVARCHAR" />
    <result column="umodel_event" property="umodelEvent" jdbcType="NVARCHAR" />
    <result column="umodel_oper_type" property="umodelOperType" jdbcType="NVARCHAR" />
    <result column="umodel_oper_name" property="umodelOperName" jdbcType="NVARCHAR" />
    <result column="umodel_oper_id" property="umodelOperId" jdbcType="BIGINT" />
    <result column="umodel_page" property="umodelPage" jdbcType="NVARCHAR" />
    <result column="umodel_date" property="umodelDate" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="umodel_datetime" property="umodelDateTime" jdbcType="TIMESTAMP" />
    
  </resultMap>
  
  
   <!-- 统计所有模块实体 -->
   <resultMap id="CountModelUsedResultMap" type="com.sys.umeng.model.CountModelUsedData" >
    <result column="umodel_name" property="modelName" jdbcType="NVARCHAR" />
    <result column="count_sp" property="countSp" jdbcType="INTEGER" />
    <result column="count_csr" property="countCsr" jdbcType="INTEGER" />
    <result column="count_xs" property="coutXs" jdbcType="INTEGER" />
    <result column="count_qt" property="countQt" jdbcType="INTEGER" />
    <result column="data_flag" property="dataFlag" jdbcType="NVARCHAR" />
  </resultMap>
  
  <!-- 统计具体用户的访问次数 -->
  <resultMap id="CountUmengEventDataUserAccessResultMap" type="com.sys.umeng.model.UmengEventDataAccessUserInfo" >
    <result column="umodel_name" property="modelName" jdbcType="NVARCHAR" />
    <result column="organization_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="umodel_oper_name" property="accessUser" jdbcType="NVARCHAR" />
    <result column="umodel_oper_type" property="userType" jdbcType="NVARCHAR" />
    <result column="access_acount" property="accessCount" jdbcType="INTEGER" />
  </resultMap>
  
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, umodel_name, umodel_event, umodel_oper_type, umodel_oper_name, umodel_oper_id, 
    umodel_page, umodel_date, import_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.umeng.model.UmengEventDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_umeng_event_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_umeng_event_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_umeng_event_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sys.umeng.model.UmengEventDetailExample" >
    delete from wx_t_umeng_event_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sys.umeng.model.UmengEventDetail" >
    insert into wx_t_umeng_event_detail (id, umodel_name, umodel_event, 
      umodel_oper_type, umodel_oper_name, umodel_oper_id, 
      umodel_page, umodel_date, import_time, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{umodelName,jdbcType=NVARCHAR}, #{umodelEvent,jdbcType=NVARCHAR}, 
      #{umodelOperType,jdbcType=NVARCHAR}, #{umodelOperName,jdbcType=NVARCHAR}, #{umodelOperId,jdbcType=BIGINT}, 
      #{umodelPage,jdbcType=NVARCHAR}, #{umodelDate,jdbcType=NVARCHAR}, #{importTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sys.umeng.model.UmengEventDetail" >
    insert into wx_t_umeng_event_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="umodelName != null" >
        umodel_name,
      </if>
      <if test="umodelEvent != null" >
        umodel_event,
      </if>
      <if test="umodelOperType != null" >
        umodel_oper_type,
      </if>
      <if test="umodelOperName != null" >
        umodel_oper_name,
      </if>
      <if test="umodelOperId != null" >
        umodel_oper_id,
      </if>
      <if test="umodelPage != null" >
        umodel_page,
      </if>
      <if test="umodelDate != null" >
        umodel_date,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="umodelName != null" >
        #{umodelName,jdbcType=NVARCHAR},
      </if>
      <if test="umodelEvent != null" >
        #{umodelEvent,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperType != null" >
        #{umodelOperType,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperName != null" >
        #{umodelOperName,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperId != null" >
        #{umodelOperId,jdbcType=BIGINT},
      </if>
      <if test="umodelPage != null" >
        #{umodelPage,jdbcType=NVARCHAR},
      </if>
      <if test="umodelDate != null" >
        #{umodelDate,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_umeng_event_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.umodelName != null" >
        umodel_name = #{record.umodelName,jdbcType=NVARCHAR},
      </if>
      <if test="record.umodelEvent != null" >
        umodel_event = #{record.umodelEvent,jdbcType=NVARCHAR},
      </if>
      <if test="record.umodelOperType != null" >
        umodel_oper_type = #{record.umodelOperType,jdbcType=NVARCHAR},
      </if>
      <if test="record.umodelOperName != null" >
        umodel_oper_name = #{record.umodelOperName,jdbcType=NVARCHAR},
      </if>
      <if test="record.umodelOperId != null" >
        umodel_oper_id = #{record.umodelOperId,jdbcType=BIGINT},
      </if>
      <if test="record.umodelPage != null" >
        umodel_page = #{record.umodelPage,jdbcType=NVARCHAR},
      </if>
      <if test="record.umodelDate != null" >
        umodel_date = #{record.umodelDate,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_umeng_event_detail
    set id = #{record.id,jdbcType=BIGINT},
      umodel_name = #{record.umodelName,jdbcType=NVARCHAR},
      umodel_event = #{record.umodelEvent,jdbcType=NVARCHAR},
      umodel_oper_type = #{record.umodelOperType,jdbcType=NVARCHAR},
      umodel_oper_name = #{record.umodelOperName,jdbcType=NVARCHAR},
      umodel_oper_id = #{record.umodelOperId,jdbcType=BIGINT},
      umodel_page = #{record.umodelPage,jdbcType=NVARCHAR},
      umodel_date = #{record.umodelDate,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sys.umeng.model.UmengEventDetail" >
    update wx_t_umeng_event_detail
    <set >
      <if test="umodelName != null" >
        umodel_name = #{umodelName,jdbcType=NVARCHAR},
      </if>
      <if test="umodelEvent != null" >
        umodel_event = #{umodelEvent,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperType != null" >
        umodel_oper_type = #{umodelOperType,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperName != null" >
        umodel_oper_name = #{umodelOperName,jdbcType=NVARCHAR},
      </if>
      <if test="umodelOperId != null" >
        umodel_oper_id = #{umodelOperId,jdbcType=BIGINT},
      </if>
      <if test="umodelPage != null" >
        umodel_page = #{umodelPage,jdbcType=NVARCHAR},
      </if>
      <if test="umodelDate != null" >
        umodel_date = #{umodelDate,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        import_time = #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sys.umeng.model.UmengEventDetail" >
    update wx_t_umeng_event_detail
    set umodel_name = #{umodelName,jdbcType=NVARCHAR},
      umodel_event = #{umodelEvent,jdbcType=NVARCHAR},
      umodel_oper_type = #{umodelOperType,jdbcType=NVARCHAR},
      umodel_oper_name = #{umodelOperName,jdbcType=NVARCHAR},
      umodel_oper_id = #{umodelOperId,jdbcType=BIGINT},
      umodel_page = #{umodelPage,jdbcType=NVARCHAR},
      umodel_date = #{umodelDate,jdbcType=NVARCHAR},
      import_time = #{importTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <insert id="insertBatch" parameterType="java.util.List">
    insert into wx_t_umeng_event_detail (umodel_name, umodel_event, 
      umodel_oper_type, umodel_oper_name, umodel_oper_id, 
      umodel_page, umodel_date,umodel_datetime, import_time)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.umodelName,jdbcType=NVARCHAR}, 
                #{item.umodelEvent,jdbcType=NVARCHAR}, 
                #{item.umodelOperType,jdbcType=NVARCHAR}, 
                #{item.umodelOperName,jdbcType=NVARCHAR}, 
                #{item.umodelOperId,jdbcType=BIGINT}, 
                #{item.umodelPage,jdbcType=NVARCHAR}, 
                #{item.umodelDate,jdbcType=NVARCHAR}, 
                #{item.umodelDateTime,jdbcType=TIMESTAMP}, 
                #{item.importTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
  </insert>
  
  
  <select id="countAllModelData"   resultMap="CountModelUsedResultMap">
  SELECT DISTINCT umodel_name,
        CASE WHEN umodel_oper_type='SP' THEN
                    count(id) ELSE 0 END count_sp,
		CASE WHEN umodel_oper_type='SPCSR' THEN
		            count(id) ELSE 0 END count_csr,
		CASE WHEN umodel_oper_type='雪佛龙销售' THEN
		            count(id) ELSE 0 END count_xs,
		CASE WHEN umodel_oper_type='其他' THEN
		            count(id) ELSE 0 END count_qt
  FROM wx_t_umeng_event_detail
  GROUP BY umodel_name,umodel_oper_type
</select>

<select id="countModelDataByYear" parameterType="map" resultMap="CountModelUsedResultMap">
 SELECT  DISTINCT umodel_name,data_flag,
 CASE WHEN umodel_oper_type='SP' THEN
                    count(id) ELSE 0 END count_sp,
        CASE WHEN umodel_oper_type='SPCSR' THEN
                    count(id) ELSE 0 END count_csr,
        CASE WHEN umodel_oper_type='雪佛龙销售' THEN
                    count(id) ELSE 0 END count_xs,
        CASE WHEN umodel_oper_type='其他' THEN
                    count(id) ELSE 0 END count_qt
 FROM 
(SELECT SUBSTRING(umodel_date,6,2) data_flag,*  FROM wx_t_umeng_event_detail) tt 
WHERE LEFT (umodel_date,4)=#{year}
GROUP BY umodel_name,data_flag,umodel_oper_type
ORDER BY umodel_name,data_flag
</select>



<select id="countModelDataByMonth" parameterType="map" resultMap="CountModelUsedResultMap">
SELECT  DISTINCT umodel_name,data_flag,
 CASE WHEN umodel_oper_type='SP' THEN
                    count(id) ELSE 0 END count_sp,
        CASE WHEN umodel_oper_type='SPCSR' THEN
                    count(id) ELSE 0 END count_csr,
        CASE WHEN umodel_oper_type='雪佛龙销售' THEN
                    count(id) ELSE 0 END count_xs,
        CASE WHEN umodel_oper_type='其他' THEN
                    count(id) ELSE 0 END count_qt
 FROM 
(SELECT  SUBSTRING(umodel_date,9,2) data_flag,*  FROM wx_t_umeng_event_detail) tt 
WHERE LEFT (umodel_date,4)=#{year} AND SUBSTRING(umodel_date,6,2)=#{month}
GROUP BY umodel_name,data_flag,umodel_oper_type
ORDER BY umodel_name,data_flag
 
</select>


<select id="countUmengEventDataUserAccess" parameterType="map" resultMap="CountUmengEventDataUserAccessResultMap">
SELECT DISTINCT count(t_event_data.umodel_oper_id) access_acount,  
    t_event_data.umodel_oper_name,t_org.organization_name,
    t_event_data.umodel_name,t_event_data.umodel_oper_type
FROM wx_t_umeng_event_detail t_event_data
LEFT JOIN wx_t_user  tt_user
    ON tt_user.user_id = t_event_data.umodel_oper_id
LEFT JOIN wx_t_organization t_org
    ON t_org.id = tt_user.org_id
WHERE t_event_data.umodel_oper_id !=0
    <if test="modelName!=null">
    AND t_event_data.umodel_name = #{modelName}
    </if>
    <if test="year!=null">
    AND LEFT  (t_event_data.umodel_date,4)=#{year}
    </if>
    <if test="month!=null">
    AND SUBSTRING(umodel_date,6,2)=#{month}
    </if>
    <if test="selectData!=null">
    AND SUBSTRING(umodel_date,9,2)=#{selectData}
    </if>
GROUP BY t_event_data.umodel_oper_id,t_event_data.umodel_oper_name,
    t_org.organization_name,t_event_data.umodel_name,t_event_data.umodel_oper_type
ORDER BY t_event_data.umodel_oper_type

 
</select>


 
</mapper>