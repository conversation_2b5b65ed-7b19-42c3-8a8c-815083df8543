<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.dic.dao.WxSystemConfigMapper">
    <resultMap id="BaseResultMap" type="com.sys.dic.model.WxSystemConfig">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="value" jdbcType="NVARCHAR" property="value"/>
        <result column="description" jdbcType="NVARCHAR" property="description"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, name, value, description
  </sql>


    <select id="getConfigItem" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_system_config
        <if test="name != null and name!=''">
            WHERE [name]=#{itemName,jdbcType=NVARCHAR}
        </if>
    </select>
    <select id="getConfig" parameterType="java.lang.String" resultType="java.lang.String">
        select [value]
        from wx_system_config
            WHERE [name]=#{itemName,jdbcType=NVARCHAR}
    </select>

    <select id="getConfigs"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from wx_system_config
        <where>
            <if test="names != null">
                [name] IN
                <foreach collection="names" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>