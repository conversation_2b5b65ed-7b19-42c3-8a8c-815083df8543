<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.workflow.dao.WorkflowExeListMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.workflow.model.WorkflowExeList">
		<result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
		<result column="source_key" property="sourceKey" jdbcType="VARCHAR"/>
		<result column="step_id" property="stepId" jdbcType="BIGINT"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<association property="exeGroup" resultMap="com.sys.workflow.dao.WorkflowPreExeGroupMapper.BaseResultMap"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		source_type,source_key,step_id,remark
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.workflow.model.WorkflowExeList">
		insert into wx_t_workflow_exe_list
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="sourceType != null">
				source_type,
			</if>
			<if test="sourceKey != null">
				source_key,
			</if>
			<if test="stepId != null">
				step_id,
			</if>
			<if test="remark != null">
				remark,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="sourceKey != null">
				#{sourceKey,jdbcType=VARCHAR},
			</if>
			<if test="stepId != null">
				#{stepId,jdbcType=BIGINT},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_workflow_exe_list
		<set>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.workflow.model.WorkflowExeListExample">
		delete from wx_t_workflow_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.workflow.model.WorkflowExeListExample" resultType="int">
		select count(1) from wx_t_workflow_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowExeListExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_workflow_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select wel1.*, t1.condition_sql
		  from wx_t_workflow_exe_list wel1
		  left join wx_t_workflow_pre_exe_group t1 on wel1.source_type='GROUP' and wel1.source_key=t1.id
		 where 1=1
		 <if test="stepId != null">
		 and wel1.step_id=#{stepId, jdbcType=BIGINT}
		 </if>
	</select>
	
	<!-- 根据工作流步骤id获取执行人 -->
	<select id="queryExecutorByStepId" resultType="com.sys.workflow.model.WorkflowExeList" parameterType="long" >
	   SELECT wel.source_type sourceType,wel.step_id stepId, wel.source_key sourceKey, wel.remark, ISNULL(u.ch_name, wpeg.group_name) sourceKeyName 
	     from wx_t_workflow_exe_list wel
         LEFT JOIN wx_t_user u on wel.source_type = 'USER' AND wel.source_key = u.user_id
         LEFT JOIN wx_t_workflow_pre_exe_group wpeg on wel.source_type = 'GROUP' AND wpeg.id = wel.source_key
         where wel.step_id = #{stepId,jdbcType=BIGINT} 
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_workflow_exe_list (source_type, source_key, step_id, remark) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.sourceType, jdbcType=VARCHAR}, #{item.sourceKey, jdbcType=VARCHAR}, #{item.stepId, jdbcType=BIGINT}, #{item.remark, jdbcType=VARCHAR}
			</trim>
		</foreach>
	</insert>
</mapper>
