<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.workflow.dao.WorkflowStepInstanceMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.workflow.model.WorkflowStepInstance">
		<id column="step_instance_id" property="stepInstanceId" jdbcType="BIGINT"/>
		<result column="workflow_instance_id" property="workflowInstanceId" jdbcType="BIGINT"/>
		<result column="step_id" property="stepId" jdbcType="BIGINT"/>
		<result column="step_no" property="stepNo" jdbcType="INTEGER"/>
		<result column="version_no" property="versionNo" jdbcType="BIGINT"/>
		<result column="todo_numb" property="todoNumb" jdbcType="INTEGER"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<association property="workflowStep" column="step_id" resultMap="com.sys.workflow.dao.WorkflowStepMapper.BaseResultMap"/>
		<collection property="worInsExeList" ofType="com.sys.workflow.model.WorInsExeList">
	        <id column="executor" jdbcType="BIGINT" property="executor" />
	        <result column="actual_executor" jdbcType="BIGINT" property="actualExecutor" />
	        <result column="executor_name" jdbcType="VARCHAR" property="executorName" />  
	        <result column="executor_email" jdbcType="VARCHAR" property="executorEmail" />  
	        <result column="actual_executor_name" jdbcType="VARCHAR" property="actualExecutorName" />  
			<result column="execute_status" property="executeStatus" jdbcType="INTEGER"/>
	    </collection>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		step_instance_id,workflow_instance_id,step_id,step_no,version_no,todo_numb,enable_flag,create_user_id,create_time,
		update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.sys.workflow.model.WorkflowStepInstance">
		update wx_t_workflow_step_instance set
		where step_instance_id = #{stepInstanceId,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.workflow.model.WorkflowStepInstance">
		update wx_t_workflow_step_instance
		<set>
			<if test="workflowInstanceId != null" >
				workflow_instance_id = #{workflowInstanceId,jdbcType=BIGINT},
			</if>
			<if test="stepId != null" >
				step_id = #{stepId,jdbcType=BIGINT},
			</if>
			<if test="stepNo != null" >
				step_no = #{stepNo,jdbcType=INTEGER},
			</if>
			<if test="versionNo != null" >
				version_no = #{versionNo,jdbcType=BIGINT},
			</if>
			<if test="todoNumb != null" >
				todo_numb = #{todoNumb,jdbcType=INTEGER},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where step_instance_id = #{stepInstanceId,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.workflow.model.WorkflowStepInstanceExample">
    	delete from wx_t_workflow_step_instance
		where step_instance_id = #{stepInstanceId,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.workflow.model.WorkflowStepInstance" useGeneratedKeys="true" keyProperty="stepInstanceId">
		insert into wx_t_workflow_step_instance
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="workflowInstanceId != null">
				workflow_instance_id,
			</if>
			<if test="stepId != null">
				step_id,
			</if>
			<if test="stepNo != null">
				step_no,
			</if>
			<if test="versionNo != null">
				version_no,
			</if>
			<if test="todoNumb != null">
				todo_numb,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="workflowInstanceId != null">
				#{workflowInstanceId,jdbcType=BIGINT},
			</if>
			<if test="stepId != null">
				#{stepId,jdbcType=BIGINT},
			</if>
			<if test="stepNo != null">
				#{stepNo,jdbcType=INTEGER},
			</if>
			<if test="versionNo != null">
				#{versionNo,jdbcType=BIGINT},
			</if>
			<if test="todoNumb != null">
				#{todoNumb,jdbcType=INTEGER},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_workflow_step_instance
		<set>
			<if test="record.workflowInstanceId != null">
				workflow_instance_id = #{record.workflowInstanceId,jdbcType=BIGINT},
			</if>
			<if test="record.stepId != null">
				step_id = #{record.stepId,jdbcType=BIGINT},
			</if>
			<if test="record.stepNo != null">
				step_no = #{record.stepNo,jdbcType=INTEGER},
			</if>
			<if test="record.versionNo != null">
				version_no = #{record.versionNo,jdbcType=BIGINT},
			</if>
			<if test="record.todoNumb != null">
				todo_numb = #{record.todoNumb,jdbcType=INTEGER},
			</if>
			<if test="record.enableFlag != null">
				enable_flag = #{record.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.workflow.model.WorkflowStepInstanceExample">
		delete from wx_t_workflow_step_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.workflow.model.WorkflowStepInstanceExample" resultType="int">
		select count(1) from wx_t_workflow_step_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowStepInstanceExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_workflow_step_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowStepInstanceExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_workflow_step_instance
		where step_instance_id = #{stepInstanceId,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,wsi1.version_no,
			 wsi1.enable_flag, t1.*
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 where 1=1
		 <if test="stepVersionNo != null">
		 and wsi1.version_no=#{stepVersionNo,jdbcType=BIGINT}
		 </if>
		 <if test="startStepNo != null">
		 and wsi1.step_no>=#{startStepNo,jdbcType=INTEGER}
		 </if>
		 <if test="workflowInstanceId != null">
		 and wsi1.workflow_instance_id=#{workflowInstanceId,jdbcType=BIGINT}
		 </if>
		 <if test="stepCode != null and stepCode != ''">
		 and t1.step_code=#{stepCode, jdbcType=VARCHAR}
		 </if>
	</select>

	<!-- 列表查询 -->
	<select id="getWorkflowStepInstances" resultMap="BaseResultMap" parameterType="map">
		select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id and wiel1.execute_status=30 and wi1.status>0
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no and t1.step_code not in ('START', 'END') 
		 <if test="startWorkflowInstanceStatus != null">
		 and wi1.status>=#{startWorkflowInstanceStatus,jdbcType=INTEGER}
		 </if>
		 <if test="workflowCode != null and workflowCode != ''">
		 and wi1.workflow_code=#{workflowCode,jdbcType=VARCHAR}
		 </if>
		 <if test="formKey != null and formKey != ''">
		 and wi1.form_key=#{formKey,jdbcType=VARCHAR}
		 </if>
		 <if test="onlyAcceptStep == 1">
		 and wiel1.execute_status is not null and not exists (select 1 from wx_t_wor_ins_exe_list iwiel1 where iwiel1.step_instance_id=wsi1.step_instance_id and iwiel1.execute_status!=30)
		 </if>
	</select>
	
	<select id="buildWorkflowStepInstances" resultMap="BaseResultMap" parameterType="map">
		select t1.step_id, 1 step_no, 1 step_instance_id, #{versionNo,jdbcType=BIGINT} version_no, 0 todo_numb,
			 1 enable_flag,
			 t1.*
		  from wx_t_workflow_step t1
		 where t1.delete_flag=0 and t1.workflow_code=#{workflowCode, jdbcType=VARCHAR} and t1.step_code='START'
		union all select t1.step_id, 2 step_no, 2 step_instance_id, #{versionNo,jdbcType=BIGINT} version_no, 0 todo_numb,
			 1 enable_flag,
			 t1.*
		  from wx_t_workflow_step t1
		 where t1.delete_flag=0 and t1.workflow_code=#{workflowCode, jdbcType=VARCHAR} and t1.step_code='REQUEST'
		<if test="steps != null">
			<foreach collection="steps" index="index" item="item">
		union all select t1.step_id, ${index} + 3 step_no, ${index} + 3 step_instance_id, #{versionNo,jdbcType=BIGINT} version_no, -1 todo_numb,
			 1 enable_flag,
			 t1.*
		  from wx_t_workflow_step t1
		 where t1.delete_flag=0 and t1.workflow_code=#{workflowCode, jdbcType=VARCHAR} and t1.step_code=#{item, jdbcType=VARCHAR}
			</foreach>
		</if>
		union all select t1.step_id, #{stepSize,jdbcType=INTEGER} + 3 step_no, #{stepSize,jdbcType=INTEGER} + 3 step_instance_id, #{versionNo,jdbcType=BIGINT} version_no, -1 todo_numb,
			 1 enable_flag,
			 t1.*
		  from wx_t_workflow_step t1
		 where t1.delete_flag=0 and t1.workflow_code=#{workflowCode, jdbcType=VARCHAR} and t1.step_code='END'
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_workflow_step_instance (workflow_instance_id, step_id, step_no, version_no, todo_numb, enable_flag, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.workflowInstanceId, jdbcType=BIGINT}, #{item.stepId, jdbcType=BIGINT}, #{item.stepNo, jdbcType=INTEGER}, #{item.versionNo, jdbcType=BIGINT}, #{item.todoNumb, jdbcType=INTEGER}, #{item.enableFlag, jdbcType=INTEGER}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	<update id="updateBatch" parameterType="com.sys.workflow.model.WorkflowStepInstance">
		<foreach collection="records" item="item" index="index" open="" close="" separator=";">
		update wx_t_workflow_step_instance
		<set>
			<if test="item.workflowInstanceId != null" >
				workflow_instance_id = #{item.workflowInstanceId,jdbcType=BIGINT},
			</if>
			<if test="item.stepId != null" >
				step_id = #{item.stepId,jdbcType=BIGINT},
			</if>
			<if test="item.stepNo != null" >
				step_no = #{item.stepNo,jdbcType=INTEGER},
			</if>
			<if test="item.versionNo != null" >
				version_no = #{item.versionNo,jdbcType=BIGINT},
			</if>
			<if test="item.todoNumb != null" >
				todo_numb = #{item.todoNumb,jdbcType=INTEGER},
			</if>
			<if test="item.enableFlag != null" >
				enable_flag = #{item.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="item.createUserId != null" >
				create_user_id = #{item.createUserId,jdbcType=BIGINT},
			</if>
			<if test="item.createTime != null" >
				create_time = #{item.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="item.updateUserId != null" >
				update_user_id = #{item.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="item.updateTime != null" >
				update_time = #{item.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where step_instance_id = #{item.stepInstanceId,jdbcType=BIGINT}
		</foreach>
	</update>
</mapper>
