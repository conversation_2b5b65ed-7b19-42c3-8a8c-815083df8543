<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.workflow.dao.WorInsExeListMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.workflow.model.WorInsExeList">
		<result column="executor" property="executor" jdbcType="BIGINT"/>
		<result column="actual_executor" property="actualExecutor" jdbcType="BIGINT"/>
		<result column="step_instance_id" property="stepInstanceId" jdbcType="BIGINT"/>
		<result column="execute_status" property="executeStatus" jdbcType="INTEGER"/>
		<result column="execute_status_text" property="executeStatusText" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<association property="user" column="executor" resultMap="com.sys.auth.dao.WxTUserMapper.BaseResultMap"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		executor,step_instance_id,actual_executor,execute_status,create_user_id,create_time
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.workflow.model.WorInsExeList">
		insert into wx_t_wor_ins_exe_list
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="executor != null">
				executor,
			</if>
			<if test="stepInstanceId != null">
				step_instance_id,
			</if>
			<if test="actualExecutor != null">
				actual_executor,
			</if>
			<if test="executeStatus != null">
				execute_status,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="executor != null">
				#{executor,jdbcType=BIGINT},
			</if>
			<if test="stepInstanceId != null">
				#{stepInstanceId,jdbcType=BIGINT},
			</if>
			<if test="actualExecutor != null">
				#{actualExecutor,jdbcType=BIGINT},
			</if>
			<if test="executeStatus != null">
				#{executeStatus,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_wor_ins_exe_list
		<set>
			<if test="record.actualExecutor != null">
				actual_executor = #{record.actualExecutor,jdbcType=BIGINT},
			</if>
			<if test="record.executeStatus != null">
				execute_status = #{record.executeStatus,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.workflow.model.WorInsExeListExample">
		delete from wx_t_wor_ins_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.workflow.model.WorInsExeListExample" resultType="int">
		select count(1) from wx_t_wor_ins_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorInsExeListExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_wor_ins_exe_list
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.executor, t1.step_instance_id, t1.actual_executor, t1.execute_status, u.*
		  from wx_t_wor_ins_exe_list t1
		  left join wx_t_user u on u.user_id=t1.executor and u.status=1
		 where 1=1
		 <if test="stepInstanceId != null">
		 and t1.step_instance_id=#{stepInstanceId, jdbcType=BIGINT}
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_wor_ins_exe_list (executor, actual_executor, step_instance_id, execute_status, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.executor, jdbcType=BIGINT}, #{item.actualExecutor, jdbcType=BIGINT}, (select step_instance_id from wx_t_workflow_step_instance wsi1 where wsi1.version_no=${item.workflowStepInstance.versionNo} 
				and wsi1.workflow_instance_id=${item.workflowStepInstance.workflowInstanceId} and wsi1.step_no=${item.workflowStepInstance.stepNo}), #{item.executeStatus, jdbcType=INTEGER}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	
	<delete id="deleteByParams" parameterType="map">
		delete t from wx_t_wor_ins_exe_list t 
		join wx_t_workflow_step_instance wsi1 on t.step_instance_id=wsi1.step_instance_id
		where 1=1
		<if test="workflowInstanceId != null">
		and wsi1.workflow_instance_id=#{workflowInstanceId, jdbcType=BIGINT}
		</if>
		<if test="stepVersionNo != null"></if>
		and wsi1.version_no=#{stepVersionNo,jdbcType=INTEGER}
		<if test="startStepNo != null">
		and wsi1.step_no>=#{startStepNo,jdbcType=INTEGER}
		</if>
	</delete>
</mapper>
