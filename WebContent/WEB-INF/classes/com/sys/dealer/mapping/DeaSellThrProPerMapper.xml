<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.dealer.dao.DeaSellThrProPerMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.dealer.model.DeaSellThrProPer">
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="product_sku" property="productSku" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		partner_id,product_sku
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.dealer.model.DeaSellThrProPer">
		insert into wx_t_dea_sell_thr_pro_per
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="productSku != null">
				product_sku,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="productSku != null">
				#{productSku,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_dea_sell_thr_pro_per
		<set>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.productSku != null">
				product_sku = #{record.productSku,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.dealer.model.DeaSellThrProPerExample">
		delete from wx_t_dea_sell_thr_pro_per
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.dealer.model.DeaSellThrProPerExample" resultType="int">
		select count(1) from wx_t_dea_sell_thr_pro_per
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.dealer.model.DeaSellThrProPerExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_dea_sell_thr_pro_per
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.partner_id, t1.product_sku
		  from wx_t_dea_sell_thr_pro_per t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_dea_sell_thr_pro_per (partner_id, product_sku) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.partnerId, jdbcType=BIGINT}, #{item.productSku, jdbcType=VARCHAR}
			</trim>
		</foreach>
	</insert>

    <resultMap id="PartnerSelectInfo" type="com.sys.organization.model.PartnerSelectInfo">
        <result column="partner_id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="organization_name" property="partnerName"/>
    </resultMap>
    <!-- 列表查询 -->
    <select id="getProductPermissionBySku" resultMap="PartnerSelectInfo">
        select distinct t1.partner_id, t2.organization_name
        from wx_t_dea_sell_thr_pro_per t1
                 left join wx_t_organization t2 on t1.partner_id = t2.id
        where t1.product_sku = #{sku}
    </select>
</mapper>
