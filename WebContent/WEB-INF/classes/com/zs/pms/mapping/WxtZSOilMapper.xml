<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zs.pms.dao.WxtZSOilMapper" >
  <resultMap id="BaseResultMap" type="com.zs.pms.model.WxtZSOil" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="oil_id" property="oilId" jdbcType="NVARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="NVARCHAR" />
    <result column="oil_model" property="oilModel" jdbcType="NVARCHAR" />
    <result column="gasoline_diesel" property="gasolineDiesel" jdbcType="NVARCHAR" />
    <result column="oil_type" property="oilType" jdbcType="NVARCHAR" />
    <result column="viscosity" property="viscosity" jdbcType="NVARCHAR" />
    <result column="oil_grade" property="oilGrade" jdbcType="NVARCHAR" />
    <result column="capacity" property="capacity" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, oil_id, brand_name, oil_model, gasoline_diesel, oil_type, viscosity, oil_grade, 
    capacity, import_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.zs.pms.model.WxtZSOilExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_zs_oil
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.zs.pms.model.WxtZSOilExample" >
    delete from wx_t_zs_oil
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.zs.pms.model.WxtZSOil" >
    insert into wx_t_zs_oil (id, oil_id, brand_name, 
      oil_model, gasoline_diesel, oil_type, 
      viscosity, oil_grade, capacity, 
      import_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{oilId,jdbcType=NVARCHAR}, #{brandName,jdbcType=NVARCHAR}, 
      #{oilModel,jdbcType=NVARCHAR}, #{gasolineDiesel,jdbcType=NVARCHAR}, #{oilType,jdbcType=NVARCHAR}, 
      #{viscosity,jdbcType=NVARCHAR}, #{oilGrade,jdbcType=NVARCHAR}, #{capacity,jdbcType=NVARCHAR}, 
      #{importTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.zs.pms.model.WxtZSOil" >
    insert into wx_t_zs_oil
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="oilId != null" >
        oil_id,
      </if>
      <if test="brandName != null" >
        brand_name,
      </if>
      <if test="oilModel != null" >
        oil_model,
      </if>
      <if test="gasolineDiesel != null" >
        gasoline_diesel,
      </if>
      <if test="oilType != null" >
        oil_type,
      </if>
      <if test="viscosity != null" >
        viscosity,
      </if>
      <if test="oilGrade != null" >
        oil_grade,
      </if>
      <if test="capacity != null" >
        capacity,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="oilId != null" >
        #{oilId,jdbcType=NVARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=NVARCHAR},
      </if>
      <if test="oilModel != null" >
        #{oilModel,jdbcType=NVARCHAR},
      </if>
      <if test="gasolineDiesel != null" >
        #{gasolineDiesel,jdbcType=NVARCHAR},
      </if>
      <if test="oilType != null" >
        #{oilType,jdbcType=NVARCHAR},
      </if>
      <if test="viscosity != null" >
        #{viscosity,jdbcType=NVARCHAR},
      </if>
      <if test="oilGrade != null" >
        #{oilGrade,jdbcType=NVARCHAR},
      </if>
      <if test="capacity != null" >
        #{capacity,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_zs_oil
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.oilId != null" >
        oil_id = #{record.oilId,jdbcType=NVARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=NVARCHAR},
      </if>
      <if test="record.oilModel != null" >
        oil_model = #{record.oilModel,jdbcType=NVARCHAR},
      </if>
      <if test="record.gasolineDiesel != null" >
        gasoline_diesel = #{record.gasolineDiesel,jdbcType=NVARCHAR},
      </if>
      <if test="record.oilType != null" >
        oil_type = #{record.oilType,jdbcType=NVARCHAR},
      </if>
      <if test="record.viscosity != null" >
        viscosity = #{record.viscosity,jdbcType=NVARCHAR},
      </if>
      <if test="record.oilGrade != null" >
        oil_grade = #{record.oilGrade,jdbcType=NVARCHAR},
      </if>
      <if test="record.capacity != null" >
        capacity = #{record.capacity,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_zs_oil
    set id = #{record.id,jdbcType=BIGINT},
      oil_id = #{record.oilId,jdbcType=NVARCHAR},
      brand_name = #{record.brandName,jdbcType=NVARCHAR},
      oil_model = #{record.oilModel,jdbcType=NVARCHAR},
      gasoline_diesel = #{record.gasolineDiesel,jdbcType=NVARCHAR},
      oil_type = #{record.oilType,jdbcType=NVARCHAR},
      viscosity = #{record.viscosity,jdbcType=NVARCHAR},
      oil_grade = #{record.oilGrade,jdbcType=NVARCHAR},
      capacity = #{record.capacity,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>