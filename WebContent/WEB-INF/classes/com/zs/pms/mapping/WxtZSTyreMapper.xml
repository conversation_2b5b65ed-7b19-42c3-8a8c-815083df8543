<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zs.pms.dao.WxtZSTyreMapper" >
  <resultMap id="BaseResultMap" type="com.zs.pms.model.WxtZSTyre" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="tyre_id" property="tyreId" jdbcType="NVARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="NVARCHAR" />
    <result column="model" property="model" jdbcType="NVARCHAR" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="standard" property="standard" jdbcType="NVARCHAR" />
    <result column="deadweightratio" property="deadweightratio" jdbcType="NVARCHAR" />
    <result column="velocityfactor" property="velocityfactor" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, tyre_id, brand_name, model, type, standard, deadweightratio, velocityfactor, 
    import_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.zs.pms.model.WxtZSTyreExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_zs_tyre
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.zs.pms.model.WxtZSTyreExample" >
    delete from wx_t_zs_tyre
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.zs.pms.model.WxtZSTyre" >
    insert into wx_t_zs_tyre (id, tyre_id, 
      brand_name, model, type, 
      standard, deadweightratio, velocityfactor, 
      import_time, update_time)
    values (#{id,jdbcType=BIGINT},#{tyreId,jdbcType=NVARCHAR}, 
      #{brandName,jdbcType=NVARCHAR}, #{model,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, 
      #{standard,jdbcType=NVARCHAR}, #{deadweightratio,jdbcType=NVARCHAR}, #{velocityfactor,jdbcType=NVARCHAR}, 
      #{importTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.zs.pms.model.WxtZSTyre" >
    insert into wx_t_zs_tyre
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tyreId != null" >
        tyre_id,
      </if>
      <if test="brandName != null" >
        brand_name,
      </if>
      <if test="model != null" >
        model,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="standard != null" >
        standard,
      </if>
      <if test="deadweightratio != null" >
        deadweightratio,
      </if>
      <if test="velocityfactor != null" >
        velocityfactor,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tyreId != null" >
        #{tyreId,jdbcType=NVARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=NVARCHAR},
      </if>
      <if test="model != null" >
        #{model,jdbcType=NVARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="standard != null" >
        #{standard,jdbcType=NVARCHAR},
      </if>
      <if test="deadweightratio != null" >
        #{deadweightratio,jdbcType=NVARCHAR},
      </if>
      <if test="velocityfactor != null" >
        #{velocityfactor,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_zs_tyre
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>     
      <if test="record.tyreId != null" >
        tyre_id = #{record.tyreId,jdbcType=NVARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=NVARCHAR},
      </if>
      <if test="record.model != null" >
        model = #{record.model,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.standard != null" >
        standard = #{record.standard,jdbcType=NVARCHAR},
      </if>
      <if test="record.deadweightratio != null" >
        deadweightratio = #{record.deadweightratio,jdbcType=NVARCHAR},
      </if>
      <if test="record.velocityfactor != null" >
        velocityfactor = #{record.velocityfactor,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_zs_tyre
    set id = #{record.id,jdbcType=BIGINT},
      tyre_id = #{record.tyreId,jdbcType=NVARCHAR},
      brand_name = #{record.brandName,jdbcType=NVARCHAR},
      model = #{record.model,jdbcType=NVARCHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      standard = #{record.standard,jdbcType=NVARCHAR},
      deadweightratio = #{record.deadweightratio,jdbcType=NVARCHAR},
      velocityfactor = #{record.velocityfactor,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPra" resultMap="BaseResultMap" parameterType="map" >
    select
    <include refid="Base_Column_List" />
    from wx_t_zs_tyre
    where  1 = 1
    <if test="tyreId != null">
		and tyre_id = #{tyreId}
	</if>
  </select>
  <insert id="insertBatch" parameterType="java.util.List">
     insert into wx_t_zs_tyre (tyre_id, brand_name, model, type, standard, deadweightratio, velocityfactor, 
    import_time
      ) values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.tyreId,jdbcType=NVARCHAR},
			#{item.brandName,jdbcType=NVARCHAR},
			#{item.model,jdbcType=NVARCHAR},
			#{item.type,jdbcType=NVARCHAR},
			#{item.standard,jdbcType=NVARCHAR},
			#{item.deadweightratio,jdbcType=NVARCHAR},
			#{item.velocityfactor,jdbcType=NVARCHAR},
			#{item.importTime,jdbcType=TIMESTAMP}
		</trim>
	 </foreach>
   </insert>
   <update id="updateBatch" parameterType="java.util.List">
	   <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_zs_tyre 
			<set>
				<if test="item.tyreId != null">
					tyre_id = #{item.tyreId,jdbcType=NVARCHAR},
				</if>
				<if test="item.brandName != null">
					brand_name = #{item.brandName,jdbcType=NVARCHAR},
				</if>
				<if test="item.model != null">
					model = #{item.model,jdbcType=NVARCHAR},
				</if>
				<if test="item.type != null">
					type = #{item.type,jdbcType=NVARCHAR},
				</if>
				<if test="item.standard != null">
					standard = #{item.standard,jdbcType=NVARCHAR},
				</if>
				<if test="item.deadweightratio != null">
					deadweightratio = #{item.deadweightratio,jdbcType=NVARCHAR},
				</if>
				<if test="item.velocityfactor != null">
					velocityfactor = #{item.velocityfactor,jdbcType=NVARCHAR},
				</if>
      			<if test="item.updateTime!= null">  
      				update_time = #{item.updateTime,jdbcType=TIMESTAMP},
      			</if>
			</set>
	 		where	id = #{item.id,jdbcType=NVARCHAR}
		</foreach>
	</update>
</mapper>