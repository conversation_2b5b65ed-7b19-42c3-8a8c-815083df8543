<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zs.pms.dao.WxtZSVehicleMaintenanceRulesVoMapper" >
  <resultMap id="BaseResultMap" type="com.zs.pms.model.WxtZSVehicleMaintenanceRulesVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="t_model_id" property="tModelId" jdbcType="NVARCHAR" />
    <result column="t_model_name" property="tModelName" jdbcType="NVARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="NVARCHAR" />
    <result column="factory_name" property="factoryName" jdbcType="NVARCHAR" />
    <result column="vehicle_type" property="vehicleType" jdbcType="NVARCHAR" />
    <result column="vehicle_series" property="vehicleSeries" jdbcType="NVARCHAR" />
    <result column="engine_displacement_l" property="engineDisplacementL" jdbcType="NVARCHAR" />
    <result column="engine" property="engine" jdbcType="NVARCHAR" />
    <result column="parts_name" property="partsName" jdbcType="NVARCHAR" />
    <result column="parts_type" property="partsType" jdbcType="NVARCHAR" />
    <result column="mile" property="mile" jdbcType="BIGINT" />
    <result column="month" property="month" jdbcType="BIGINT" />
    <result column="working_hour" property="workingHour" jdbcType="NVARCHAR" />
    <result column="working_hour_price" property="workingHourPrice" jdbcType="NVARCHAR" />
    <result column="working_hour_total_value" property="workingHourTotalValue" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, t_model_id, t_model_name, brand_name, factory_name, vehicle_type, vehicle_series, 
    engine_displacement_l, engine, parts_name, parts_type, mile, month, working_hour, 
    working_hour_price, working_hour_total_value, import_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.zs.pms.model.WxtZSVehicleMaintenanceRulesVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_zs_vehicle_maintenance_rules
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.zs.pms.model.WxtZSVehicleMaintenanceRulesVoExample" >
    delete from wx_t_zs_vehicle_maintenance_rules
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.zs.pms.model.WxtZSVehicleMaintenanceRulesVo" >
    insert into wx_t_zs_vehicle_maintenance_rules (id, t_model_id, t_model_name, 
      brand_name, factory_name, vehicle_type, 
      vehicle_series, engine_displacement_l, engine, 
      parts_name, parts_type, mile, 
      month, working_hour, working_hour_price, 
      working_hour_total_value, import_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{tModelId,jdbcType=NVARCHAR}, #{tModelName,jdbcType=NVARCHAR}, 
      #{brandName,jdbcType=NVARCHAR}, #{factoryName,jdbcType=NVARCHAR}, #{vehicleType,jdbcType=NVARCHAR}, 
      #{vehicleSeries,jdbcType=NVARCHAR}, #{engineDisplacementL,jdbcType=NVARCHAR}, #{engine,jdbcType=NVARCHAR}, 
      #{partsName,jdbcType=NVARCHAR}, #{partsType,jdbcType=NVARCHAR}, #{mile,jdbcType=BIGINT}, 
      #{month,jdbcType=BIGINT}, #{workingHour,jdbcType=NVARCHAR}, #{workingHourPrice,jdbcType=NVARCHAR}, 
      #{workingHourTotalValue,jdbcType=NVARCHAR}, #{importTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zs.pms.model.WxtZSVehicleMaintenanceRulesVo" >
    insert into wx_t_zs_vehicle_maintenance_rules
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tModelId != null" >
        t_model_id,
      </if>
      <if test="tModelName != null" >
        t_model_name,
      </if>
      <if test="brandName != null" >
        brand_name,
      </if>
      <if test="factoryName != null" >
        factory_name,
      </if>
      <if test="vehicleType != null" >
        vehicle_type,
      </if>
      <if test="vehicleSeries != null" >
        vehicle_series,
      </if>
      <if test="engineDisplacementL != null" >
        engine_displacement_l,
      </if>
      <if test="engine != null" >
        engine,
      </if>
      <if test="partsName != null" >
        parts_name,
      </if>
      <if test="partsType != null" >
        parts_type,
      </if>
      <if test="mile != null" >
        mile,
      </if>
      <if test="month != null" >
        month,
      </if>
      <if test="workingHour != null" >
        working_hour,
      </if>
      <if test="workingHourPrice != null" >
        working_hour_price,
      </if>
      <if test="workingHourTotalValue != null" >
        working_hour_total_value,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tModelId != null" >
        #{tModelId,jdbcType=NVARCHAR},
      </if>
      <if test="tModelName != null" >
        #{tModelName,jdbcType=NVARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=NVARCHAR},
      </if>
      <if test="factoryName != null" >
        #{factoryName,jdbcType=NVARCHAR},
      </if>
      <if test="vehicleType != null" >
        #{vehicleType,jdbcType=NVARCHAR},
      </if>
      <if test="vehicleSeries != null" >
        #{vehicleSeries,jdbcType=NVARCHAR},
      </if>
      <if test="engineDisplacementL != null" >
        #{engineDisplacementL,jdbcType=NVARCHAR},
      </if>
      <if test="engine != null" >
        #{engine,jdbcType=NVARCHAR},
      </if>
      <if test="partsName != null" >
        #{partsName,jdbcType=NVARCHAR},
      </if>
      <if test="partsType != null" >
        #{partsType,jdbcType=NVARCHAR},
      </if>
      <if test="mile != null" >
        #{mile,jdbcType=BIGINT},
      </if>
      <if test="month != null" >
        #{month,jdbcType=BIGINT},
      </if>
      <if test="workingHour != null" >
        #{workingHour,jdbcType=NVARCHAR},
      </if>
      <if test="workingHourPrice != null" >
        #{workingHourPrice,jdbcType=NVARCHAR},
      </if>
      <if test="workingHourTotalValue != null" >
        #{workingHourTotalValue,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_zs_vehicle_maintenance_rules
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tModelId != null" >
        t_model_id = #{record.tModelId,jdbcType=NVARCHAR},
      </if>
      <if test="record.tModelName != null" >
        t_model_name = #{record.tModelName,jdbcType=NVARCHAR},
      </if>
      <if test="record.brandName != null" >
        brand_name = #{record.brandName,jdbcType=NVARCHAR},
      </if>
      <if test="record.factoryName != null" >
        factory_name = #{record.factoryName,jdbcType=NVARCHAR},
      </if>
      <if test="record.vehicleType != null" >
        vehicle_type = #{record.vehicleType,jdbcType=NVARCHAR},
      </if>
      <if test="record.vehicleSeries != null" >
        vehicle_series = #{record.vehicleSeries,jdbcType=NVARCHAR},
      </if>
      <if test="record.engineDisplacementL != null" >
        engine_displacement_l = #{record.engineDisplacementL,jdbcType=NVARCHAR},
      </if>
      <if test="record.engine != null" >
        engine = #{record.engine,jdbcType=NVARCHAR},
      </if>
      <if test="record.partsName != null" >
        parts_name = #{record.partsName,jdbcType=NVARCHAR},
      </if>
      <if test="record.partsType != null" >
        parts_type = #{record.partsType,jdbcType=NVARCHAR},
      </if>
      <if test="record.mile != null" >
        mile = #{record.mile,jdbcType=BIGINT},
      </if>
      <if test="record.month != null" >
        month = #{record.month,jdbcType=BIGINT},
      </if>
      <if test="record.workingHour != null" >
        working_hour = #{record.workingHour,jdbcType=NVARCHAR},
      </if>
      <if test="record.workingHourPrice != null" >
        working_hour_price = #{record.workingHourPrice,jdbcType=NVARCHAR},
      </if>
      <if test="record.workingHourTotalValue != null" >
        working_hour_total_value = #{record.workingHourTotalValue,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_zs_vehicle_maintenance_rules
    set id = #{record.id,jdbcType=BIGINT},
      t_model_id = #{record.tModelId,jdbcType=NVARCHAR},
      t_model_name = #{record.tModelName,jdbcType=NVARCHAR},
      brand_name = #{record.brandName,jdbcType=NVARCHAR},
      factory_name = #{record.factoryName,jdbcType=NVARCHAR},
      vehicle_type = #{record.vehicleType,jdbcType=NVARCHAR},
      vehicle_series = #{record.vehicleSeries,jdbcType=NVARCHAR},
      engine_displacement_l = #{record.engineDisplacementL,jdbcType=NVARCHAR},
      engine = #{record.engine,jdbcType=NVARCHAR},
      parts_name = #{record.partsName,jdbcType=NVARCHAR},
      parts_type = #{record.partsType,jdbcType=NVARCHAR},
      mile = #{record.mile,jdbcType=BIGINT},
      month = #{record.month,jdbcType=BIGINT},
      working_hour = #{record.workingHour,jdbcType=NVARCHAR},
      working_hour_price = #{record.workingHourPrice,jdbcType=NVARCHAR},
      working_hour_total_value = #{record.workingHourTotalValue,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPra" resultMap="BaseResultMap" parameterType="map" >
    select
    <include refid="Base_Column_List" />
    from wx_t_zs_vehicle_maintenance_rules
    where 1 = 1 
    <if test="tModelId != null">
		and t_model_id = #{tModelId}
	</if>
	<if test="partsName != null">
		and parts_name = #{partsName}
	</if>
	<if test="partsType != null">
		and parts_type = #{partsType}
	</if>
	<if test="mile != null">
		and mile = #{mile}
	</if>
	<if test="month != null">
		and month = #{month}
	</if>
  </select>
  <insert id="insertMaintenanceRulesBatch" parameterType="java.util.List">
     insert into wx_t_zs_vehicle_maintenance_rules (t_model_id, t_model_name, brand_name, factory_name, vehicle_type, vehicle_series, 
     engine_displacement_l, engine, parts_name, parts_type, mile, month, working_hour, 
     working_hour_price, working_hour_total_value,import_time
      ) values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.tModelId,jdbcType=NVARCHAR},
		   	#{item.tModelName,jdbcType=NVARCHAR},
			#{item.brandName,jdbcType=NVARCHAR},
			#{item.factoryName,jdbcType=NVARCHAR},
			#{item.vehicleType,jdbcType=NVARCHAR},
			#{item.vehicleSeries,jdbcType=NVARCHAR},
			#{item.engineDisplacementL,jdbcType=NVARCHAR},
			#{item.engine,jdbcType=NVARCHAR},
			#{item.partsName,jdbcType=NVARCHAR},
			#{item.partsType,jdbcType=NVARCHAR},
			#{item.mile,jdbcType=BIGINT},
			#{item.month,jdbcType=BIGINT},
			#{item.workingHour,jdbcType=NVARCHAR},
			#{item.workingHourPrice,jdbcType=NVARCHAR},
			#{item.workingHourTotalValue,jdbcType=NVARCHAR},
			#{item.importTime,jdbcType=TIMESTAMP}
		</trim>
	 </foreach>
   </insert>
   <update id="updateMaintenanceRulesBatch" parameterType="java.util.List">
	   <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_zs_vehicle_maintenance_rules 
			<set>
				<if test="item.tModelId != null">
					t_model_id = #{item.tModelId,jdbcType=NVARCHAR},
				</if>
				<if test="item.tModelName != null">
					t_model_name = #{item.tModelName,jdbcType=NVARCHAR},
				</if>
				<if test="item.brandName != null">
					brand_name = #{item.brandName,jdbcType=NVARCHAR},
				</if>
				<if test="item.factoryName != null">
					factory_name = #{item.factoryName,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleType != null">
					vehicle_type = #{item.vehicleType,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleSeries != null">
					vehicle_series = #{item.vehicleSeries,jdbcType=NVARCHAR},
				</if>
				<if test="item.engineDisplacementL != null">
					engine_displacement_l = #{item.engineDisplacementL,jdbcType=NVARCHAR},
				</if>
				<if test="item.engine != null">
					engine = #{item.engine,jdbcType=NVARCHAR},
				</if>
				<if test="item.partsName != null">
					parts_name = #{item.partsName,jdbcType=NVARCHAR},
				</if>
				<if test="item.partsType != null">
					parts_type = #{item.partsType,jdbcType=NVARCHAR},
				</if>
				<if test="item.mile != null">
					mile = #{item.mile,jdbcType=BIGINT},
				</if>
				<if test="item.month != null">
					month = #{item.month,jdbcType=BIGINT},
				</if>
				<if test="item.workingHour != null">
					working_hour = #{item.workingHour,jdbcType=NVARCHAR},
				</if>
				<if test="item.workingHourPrice != null">
					working_hour_price = #{item.workingHourPrice,jdbcType=NVARCHAR},
				</if>
				<if test="item.workingHourTotalValue != null">
					working_hour_total_value = #{item.workingHourTotalValue,jdbcType=NVARCHAR},
				</if>
      			<if test="item.updateTime!= null">  
      				update_time = #{item.updateTime,jdbcType=TIMESTAMP},
      			</if>
			</set>
	 		where	id = #{item.id,jdbcType=NVARCHAR}
		</foreach>
	</update>
</mapper>