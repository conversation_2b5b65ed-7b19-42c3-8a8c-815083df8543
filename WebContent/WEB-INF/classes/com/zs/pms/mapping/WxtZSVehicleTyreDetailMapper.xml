<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zs.pms.dao.WxtZSVehicleTyreDetailMapper" >
  <resultMap id="BaseResultMap" type="com.zs.pms.model.WxtZSVehicleTyreDetail" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="t_model_id" property="tModelId" jdbcType="NVARCHAR" />
    <result column="front_tyre" property="frontTyre" jdbcType="NVARCHAR" />
    <result column="rear_tyre" property="rearTyre" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, t_model_id, front_tyre, rear_tyre, import_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.zs.pms.model.WxtZSVehicleTyreDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_zs_vehicle_tyre_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.zs.pms.model.WxtZSVehicleTyreDetailExample" >
    delete from wx_t_zs_vehicle_tyre_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.zs.pms.model.WxtZSVehicleTyreDetail" >
    insert into wx_t_zs_vehicle_tyre_detail (id, t_model_id, front_tyre, 
      rear_tyre, import_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{tModelId,jdbcType=NVARCHAR}, #{frontTyre,jdbcType=NVARCHAR}, 
      #{rearTyre,jdbcType=NVARCHAR}, #{importTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zs.pms.model.WxtZSVehicleTyreDetail" >
    insert into wx_t_zs_vehicle_tyre_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tModelId != null" >
        t_model_id,
      </if>
      <if test="frontTyre != null" >
        front_tyre,
      </if>
      <if test="rearTyre != null" >
        rear_tyre,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tModelId != null" >
        #{tModelId,jdbcType=NVARCHAR},
      </if>
      <if test="frontTyre != null" >
        #{frontTyre,jdbcType=NVARCHAR},
      </if>
      <if test="rearTyre != null" >
        #{rearTyre,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_zs_vehicle_tyre_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tModelId != null" >
        t_model_id = #{record.tModelId,jdbcType=NVARCHAR},
      </if>
      <if test="record.frontTyre != null" >
        front_tyre = #{record.frontTyre,jdbcType=NVARCHAR},
      </if>
      <if test="record.rearTyre != null" >
        rear_tyre = #{record.rearTyre,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_zs_vehicle_tyre_detail
    set id = #{record.id,jdbcType=BIGINT},
      t_model_id = #{record.tModelId,jdbcType=NVARCHAR},
      front_tyre = #{record.frontTyre,jdbcType=NVARCHAR},
      rear_tyre = #{record.rearTyre,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByPra" resultMap="BaseResultMap" parameterType="map" >
    select
    <include refid="Base_Column_List" />
    from wx_t_zs_vehicle_tyre_detail
    where 1 = 1 
    <if test="tModelId != null">
		and t_model_id = #{tModelId}
	</if>
  </select>
  <insert id="insertBatch" parameterType="java.util.List">
     insert into wx_t_zs_vehicle_tyre_detail (t_model_id, front_tyre, 
      rear_tyre,import_time
      ) values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.tModelId,jdbcType=NVARCHAR},
			#{item.frontTyre,jdbcType=NVARCHAR},
			#{item.rearTyre,jdbcType=NVARCHAR},
			#{item.importTime,jdbcType=TIMESTAMP}
		</trim>
	 </foreach>
   </insert>
   <update id="updateBatch" parameterType="java.util.List">
	   <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_zs_vehicle_tyre_detail 
			<set>
				<if test="item.tModelId != null">
					t_model_id = #{item.tModelId,jdbcType=NVARCHAR},
				</if>		
				<if test="item.frontTyre != null">
					front_tyre = #{item.frontTyre,jdbcType=NVARCHAR},
				</if>
				<if test="item.rearTyre != null">
					rear_tyre = #{item.rearTyre,jdbcType=NVARCHAR},
				</if>
      			<if test="item.updateTime!= null">  
      				update_time = #{item.updateTime,jdbcType=TIMESTAMP},
      			</if>
			</set>
	 		where	id = #{item.id,jdbcType=NVARCHAR}
		</foreach>
	</update>
</mapper>