<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget2021.dao.DwCustomerOrgSalesMapper">
  <resultMap id="BaseResultMap" type="com.chevron.budget2021.model.DwCustomerOrgSales">
    <!--@mbg.generated-->
    <!--@Table dw_customer_org_sales-->
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="customer_name_cn" jdbcType="VARCHAR" property="customerNameCn" />
    <result column="org_hier_id" jdbcType="BIGINT" property="orgHierId" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="product_channel" jdbcType="VARCHAR" property="productChannel" />
    <result column="sales_cai" jdbcType="VARCHAR" property="salesCai" />
    <result column="sales_name_cn" jdbcType="VARCHAR" property="salesNameCn" />
    <result column="supervisor_cai" jdbcType="VARCHAR" property="supervisorCai" />
    <result column="supervisor_name_cn" jdbcType="VARCHAR" property="supervisorNameCn" />
    <result column="effective_from_date" jdbcType="DATE" property="effectiveFromDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="BIT" property="delFlag" />
    <result column="timestamp" jdbcType="TIMESTAMP" property="timestamp" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="from_source" jdbcType="VARCHAR" property="fromSource" />
    <result column="channel_weight" jdbcType="INTEGER" property="channelWeight" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    distributor_id, customer_name_cn, org_hier_id, region, product_channel, sales_cai, 
    sales_name_cn, supervisor_cai, supervisor_name_cn, effective_from_date, create_time, 
    update_time, del_flag, [timestamp], customer_type, from_source, channel_weight
  </sql>
  <insert id="insert" parameterType="com.chevron.budget2021.model.DwCustomerOrgSales">
    <!--@mbg.generated-->
    insert into dw_customer_org_sales (distributor_id, customer_name_cn, org_hier_id, 
      region, product_channel, sales_cai, 
      sales_name_cn, supervisor_cai, supervisor_name_cn, 
      effective_from_date, create_time, update_time, 
      del_flag, [timestamp], customer_type, 
      from_source, channel_weight)
    values (#{distributorId,jdbcType=BIGINT}, #{customerNameCn,jdbcType=VARCHAR}, #{orgHierId,jdbcType=BIGINT}, 
      #{region,jdbcType=VARCHAR}, #{productChannel,jdbcType=VARCHAR}, #{salesCai,jdbcType=VARCHAR}, 
      #{salesNameCn,jdbcType=VARCHAR}, #{supervisorCai,jdbcType=VARCHAR}, #{supervisorNameCn,jdbcType=VARCHAR}, 
      #{effectiveFromDate,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=BIT}, #{timestamp,jdbcType=TIMESTAMP}, #{customerType,jdbcType=VARCHAR}, 
      #{fromSource,jdbcType=VARCHAR}, #{channelWeight,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.budget2021.model.DwCustomerOrgSales">
    <!--@mbg.generated-->
    insert into dw_customer_org_sales
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="customerNameCn != null">
        customer_name_cn,
      </if>
      <if test="orgHierId != null">
        org_hier_id,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="productChannel != null">
        product_channel,
      </if>
      <if test="salesCai != null">
        sales_cai,
      </if>
      <if test="salesNameCn != null">
        sales_name_cn,
      </if>
      <if test="supervisorCai != null">
        supervisor_cai,
      </if>
      <if test="supervisorNameCn != null">
        supervisor_name_cn,
      </if>
      <if test="effectiveFromDate != null">
        effective_from_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="timestamp != null">
        [timestamp],
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
      <if test="fromSource != null">
        from_source,
      </if>
      <if test="channelWeight != null">
        channel_weight,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="customerNameCn != null">
        #{customerNameCn,jdbcType=VARCHAR},
      </if>
      <if test="orgHierId != null">
        #{orgHierId,jdbcType=BIGINT},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="productChannel != null">
        #{productChannel,jdbcType=VARCHAR},
      </if>
      <if test="salesCai != null">
        #{salesCai,jdbcType=VARCHAR},
      </if>
      <if test="salesNameCn != null">
        #{salesNameCn,jdbcType=VARCHAR},
      </if>
      <if test="supervisorCai != null">
        #{supervisorCai,jdbcType=VARCHAR},
      </if>
      <if test="supervisorNameCn != null">
        #{supervisorNameCn,jdbcType=VARCHAR},
      </if>
      <if test="effectiveFromDate != null">
        #{effectiveFromDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=BIT},
      </if>
      <if test="timestamp != null">
        #{timestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="fromSource != null">
        #{fromSource,jdbcType=VARCHAR},
      </if>
      <if test="channelWeight != null">
        #{channelWeight,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="selectRegionBySalesCai" resultMap="BaseResultMap">
    select region,sales_cai from
    dw_customer_org_sales
    group by region,sales_cai
  </select>
</mapper>