<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget2021.dao.Budget2021Mapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.budget2021.model.Budget2021">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="budget_year" property="budgetYear" jdbcType="INTEGER"/>
		<result column="brand" property="brand" jdbcType="INTEGER"/>
		<result column="budget_type" property="budgetType" jdbcType="INTEGER"/>
		<result column="budget_partner_key" property="budgetPartnerKey" jdbcType="VARCHAR"/>
		<result column="budget_key" property="budgetKey" jdbcType="VARCHAR"/>
		<result column="budget_titile" property="budgetTitile" jdbcType="VARCHAR"/>
		<result column="channel_weight" property="channelWeight" jdbcType="INTEGER"/>
		<result column="region_budget_value" property="regionBudgetValue" jdbcType="NUMERIC"/>
		<result column="mkt_budget_value" property="mktBudgetValue" jdbcType="NUMERIC"/>
		<result column="mkt_pay_value" property="mktPayValue" jdbcType="NUMERIC"/>
		<result column="region_pay_value" property="regionPayValue" jdbcType="NUMERIC"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="item_budget_value" property="itemBudgetValue" jdbcType="NUMERIC"/>
		<result column="item_pay_value" property="itemPayValue" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,budget_year,brand,budget_type,budget_partner_key,budget_key,budget_titile,channel_weight,region_budget_value,
		mkt_budget_value,create_user_id,create_time,update_user_id,update_time,item_budget_value
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.budget2021.model.Budget2021">
		update wx_t_budget2021 set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.budget2021.model.Budget2021">
		update wx_t_budget2021
		<set>
			<if test="budgetYear != null" >
				budget_year = #{budgetYear,jdbcType=INTEGER},
			</if>
			<if test="brand != null" >
				brand = #{brand,jdbcType=INTEGER},
			</if>
			<if test="budgetType != null" >
				budget_type = #{budgetType,jdbcType=INTEGER},
			</if>
			<if test="budgetPartnerKey != null" >
				budget_partner_key = #{budgetPartnerKey,jdbcType=VARCHAR},
			</if>
			<if test="budgetKey != null" >
				budget_key = #{budgetKey,jdbcType=VARCHAR},
			</if>
			<if test="budgetTitile != null" >
				budget_titile = #{budgetTitile,jdbcType=VARCHAR},
			</if>
			<if test="channelWeight != null" >
				channel_weight = #{channelWeight,jdbcType=INTEGER},
			</if>
			<if test="regionBudgetValue != null" >
				region_budget_value = #{regionBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="mktBudgetValue != null" >
				mkt_budget_value = #{mktBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="itemBudgetValue != null" >
				item_budget_value = #{itemBudgetValue,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.budget2021.model.Budget2021Example">
    	delete from wx_t_budget2021
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.budget2021.model.Budget2021" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_budget2021
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="budgetYear != null">
				budget_year,
			</if>
			<if test="brand != null">
				brand,
			</if>
			<if test="budgetType != null">
				budget_type,
			</if>
			<if test="budgetPartnerKey != null">
				budget_partner_key,
			</if>
			<if test="budgetKey != null">
				budget_key,
			</if>
			<if test="budgetTitile != null">
				budget_titile,
			</if>
			<if test="channelWeight != null">
				channel_weight,
			</if>
			<if test="regionBudgetValue != null">
				region_budget_value,
			</if>
			<if test="mktBudgetValue != null">
				mkt_budget_value,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="itemBudgetValue != null">
				item_budget_value,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="budgetYear != null">
				#{budgetYear,jdbcType=INTEGER},
			</if>
			<if test="brand != null">
				#{brand,jdbcType=INTEGER},
			</if>
			<if test="budgetType != null">
				#{budgetType,jdbcType=INTEGER},
			</if>
			<if test="budgetPartnerKey != null">
				#{budgetPartnerKey,jdbcType=VARCHAR},
			</if>
			<if test="budgetKey != null">
				#{budgetKey,jdbcType=VARCHAR},
			</if>
			<if test="budgetTitile != null">
				#{budgetTitile,jdbcType=VARCHAR},
			</if>
			<if test="channelWeight != null">
				#{channelWeight,jdbcType=INTEGER},
			</if>
			<if test="regionBudgetValue != null">
				#{regionBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="mktBudgetValue != null">
				#{mktBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="itemBudgetValue != null">
				#{itemBudgetValue,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_budget2021
		<set>
			<if test="record.budgetYear != null">
				budget_year = #{record.budgetYear,jdbcType=INTEGER},
			</if>
			<if test="record.brand != null">
				brand = #{record.brand,jdbcType=INTEGER},
			</if>
			<if test="record.budgetType != null">
				budget_type = #{record.budgetType,jdbcType=INTEGER},
			</if>
			<if test="record.budgetPartnerKey != null">
				budget_partner_key = #{record.budgetPartnerKey,jdbcType=VARCHAR},
			</if>
			<if test="record.budgetKey != null">
				budget_key = #{record.budgetKey,jdbcType=VARCHAR},
			</if>
			<if test="record.budgetTitile != null">
				budget_titile = #{record.budgetTitile,jdbcType=VARCHAR},
			</if>
			<if test="record.channelWeight != null">
				channel_weight = #{record.channelWeight,jdbcType=INTEGER},
			</if>
			<if test="record.regionBudgetValue != null">
				region_budget_value = #{record.regionBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="record.mktBudgetValue != null">
				mkt_budget_value = #{record.mktBudgetValue,jdbcType=NUMERIC},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.itemBudgetValue != null">
				item_budget_value = #{record.itemBudgetValue,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.budget2021.model.Budget2021Example">
		delete from wx_t_budget2021
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.budget2021.model.Budget2021Example" resultType="int">
		select count(1) from wx_t_budget2021
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.budget2021.model.Budget2021Example">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_budget2021
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.budget2021.model.Budget2021Example">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_budget2021
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.budget_year, t1.brand, t1.budget_type, t1.budget_partner_key, t1.budget_key, t1.budget_titile,
			 t1.channel_weight, t1.region_budget_value, t1.mkt_budget_value, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time, t1.item_budget_value
		  from wx_t_budget2021 t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.budget2021.model.Budget2021Params">
		select t1.id, t1.budget_year, t1.brand, t1.budget_type, t1.budget_partner_key, t1.budget_key, t1.budget_titile,
			 t1.channel_weight, t1.region_budget_value, t1.mkt_budget_value, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time, t1.item_budget_value
		  from wx_t_budget2021 t1
		 where t1.delete_flag=0
	</select>
	
	<sql id="customerRegion">
		SELECT
			region_name budget_key,
			region_name budget_titile
		FROM
			view_customer_region_sales_channel
		where
			bu = 'Indirect' 
			AND region_name IN (SELECT  dic_item_code from wx_t_dic_item wtdi  where dic_type_code  = 'Region.Indirect')
		<if test="salesCai != null">
			and suppervisor_cai  = #{salesCai}
		</if>
		group by region_name 
	</sql>
	
	<sql id="customerSales">
		SELECT
			rsc.sales_cai budget_key,
			rsc.sales_name budget_titile
		FROM
			view_customer_region_sales_channel rsc
			left join dw_sales_role sr on sr.sales_cai = rsc.sales_cai  
		where
			rsc.bu = 'Indirect' 
			<if test="brand != null">
				AND  (rsc.channel_weight &amp; 
				(SELECT  dic_item_desc from wx_t_dic_item where dic_type_code   = 'ChevronBrand' and dic_item_code = #{brand})
				) &gt; 0
			</if>
			AND rsc.region_name IN (SELECT  dic_item_code from wx_t_dic_item wtdi  where dic_type_code  = 'Region.Indirect')
			<if test="budgetTitile != null">
				AND rsc.region_name = #{budgetTitile}
			</if>
			<if test="salesCai != null">
				AND  sr.sales_cai_level like '%' + #{salesCai} +'%'
			</if>
		group by rsc.sales_cai ,rsc.sales_name
	</sql>
	
	<select id="queryByCondition" resultMap="BaseResultMap" parameterType="com.chevron.budget2021.model.Budget2021">
	select distinct tt.* from (
	
	SELECT 
	t1.id, t1.budget_year, t1.brand, t1.budget_type, isnull(t1.budget_key,t.budget_key) budget_key, isnull(t1.budget_titile,t.budget_titile) budget_titile, t1.channel_weight,
			 t1.region_budget_value, t1.mkt_budget_value, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.budget_partner_key,t1.item_budget_value,0 region_pay_value, 0 mkt_pay_value,
			 0 item_pay_value
	 FROM  (
		<choose>
			<when test="budgetFrom == 'region'">
				<include refid="customerRegion"/>
			</when>
			<when test="budgetFrom == 'sales'">
				<include refid="customerSales"/>
			</when>
			<otherwise>
				<include refid="customerSales"/>
			</otherwise>
		</choose>
		) t left join wx_t_budget2021 t1 on t.budget_key = t1.budget_key 
	and t.budget_titile = t1.budget_titile and t1.budget_year = #{budgetYear}
	<if test="brand != null">
		and (t1.brand = #{brand} OR  t1.brand is NULL)
	</if>
	<if test="budgetFrom == 'sales'">
		union all 
		SELECT t1.*,0 region_pay_value, 0 mkt_pay_value, 0 item_pay_value from wx_t_budget2021 t1
		 <trim prefix="WHERE" prefixOverrides="AND |OR "> 
		 
			 AND t1.budget_partner_key  != '0'
			 <if test="salesCai != null">
			 AND EXISTS (SELECT  1 from dw_sales_role dsr where dsr.sales_cai_level  like '%' + #{salesCai} + '%' AND t1.budget_key  = dsr.sales_cai )
			 </if>
			 <if test="brand != null">AND t1.brand = #{brand}</if>
			 <if test="budgetYear != null">AND t1.budget_year= #{budgetYear}</if>
			 <if test="budgetType != null">AND budget_type = #{budgetType}</if>
			 <if test="budgetPartnerKey != null">AND t1.budget_partner_key = #{budgetPartnerKey}</if>
		 </trim>
	</if>
	) tt 
	</select>
	
	
	<select id="queryByTittle" resultMap="BaseResultMap" parameterType="com.chevron.budget2021.model.Budget2021">
	select t1.id, t1.budget_year, t1.brand, t1.budget_type, t1.budget_partner_key, isnull(t1.budget_key,t.budget_key) budget_key, isnull(t1.budget_titile,t.budget_titile) budget_titile,
		t1.channel_weight, t1.region_budget_value, t1.mkt_budget_value,t.region_pay_value, t.mkt_pay_value, t1.create_user_id, t1.create_time,
		t1.update_user_id, t1.update_time, t1.item_budget_value, t.item_pay_value from (SELECT NULL id, NULL budget_year, NULL brand, NULL budget_type,NULL budget_partner_key, 'Titile'as budget_key, '总预算' budget_titile,
		NULL channel_weight, NULL region_budget_value, NULL mkt_budget_value,0 region_pay_value, 0 mkt_pay_value, NULL create_user_id, NULL create_time,
		NULL  update_user_id,NULL update_time, 0 item_pay_value) t left join  wx_t_budget2021 t1 on t.budget_key = t1.budget_key  and (t1.brand = #{brand} or t1.brand is NULL) and t1.budget_year = #{budgetYear}
	</select>
	
	<select id="queryByProcedureParam" resultMap="BaseResultMap" parameterType="com.chevron.report.model.DistributorExpenseParam">
	select sum(t1.region_budget_value) region_budget_value, sum(t1.mkt_budget_value) mkt_budget_value, sum(t1.item_budget_value) item_budget_value
	from wx_t_budget2021 t1
	where t1.budget_year=#{year}
	<if test="brand != null">
	and t1.brand&amp;#{brand}>0
	</if>
	<choose>
		<when test="permissionWeight == 1">
		and t1.budget_type=1
		</when>
		<when test="asm and region != null and region != ''">
		and t1.budget_type=2 and t1.budget_key=#{region}
		</when>
		<when test="asm">
		and t1.budget_type=2 and exists (select 1 from dw_customer_org_sales os where os.supervisor_cai=#{salesCai} and os.region=t1.budget_key)
		</when>
		<otherwise>
		and t1.budget_type=3 and exists (select 1 from dw_sales_role sr left join dw_sales_role srf on srf.sales_cai_level like sr.sales_cai_level + '%' where sr.sales_cai=#{salesCai} and srf.sales_cai=t1.budget_key)
		</otherwise>
	</choose>
	</select>
	
	<select id="queryWithBrandByProcedureParam" resultMap="BaseResultMap" parameterType="com.chevron.report.model.DistributorExpenseParam">
	select sum(t1.region_budget_value) region_budget_value, sum(t1.mkt_budget_value) mkt_budget_value, sum(t1.item_budget_value) item_budget_value, t1.brand
	from wx_t_budget2021 t1
	where t1.budget_year=#{year}
	<if test="brand != null">
	and t1.brand&amp;#{brand}>0
	</if>
	<choose>
		<when test="region != null and region != '' and (asm or permissionWeight == 1)">
		and t1.budget_type=2 and t1.budget_key=#{region}
		</when>
		<when test="permissionWeight == 1">
		and t1.budget_type=1
		</when>
		<when test="asm">
		and t1.budget_type=2 and exists (select 1 from view_customer_region_sales_channel os where os.suppervisor_cai=#{salesCai} and os.region_name=t1.budget_key)
		</when>
		<otherwise>
		and t1.budget_type=3 and exists (select 1 from dw_sales_role sr left join dw_sales_role srf on srf.sales_cai_level like sr.sales_cai_level + '%' where sr.sales_cai=#{salesCai} and srf.sales_cai=t1.budget_key)
		</otherwise>
	</choose>
	group by t1.brand
	</select>


	<insert id="insertBatch" parameterType="map">
		insert into wx_t_budget2021(budget_year, brand, budget_type, budget_key, budget_titile, channel_weight,
		region_budget_value, mkt_budget_value, create_user_id, create_time, update_user_id,
		update_time, budget_partner_key, item_budget_value) values
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.budgetYear, jdbcType=BIGINT}, #{item.brand, jdbcType=INTEGER},
				#{item.budgetType, jdbcType=INTEGER}, #{item.budgetKey, jdbcType=NVARCHAR},
				#{item.budgetTitile, jdbcType=NVARCHAR}, #{item.channelWeight, jdbcType=INTEGER},
				#{item.regionBudgetValue, jdbcType=DECIMAL}, #{item.mktBudgetValue, jdbcType=DECIMAL},
				#{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP},
				#{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP},
				#{item.budgetPartnerKey, jdbcType=NVARCHAR}, #{item.itemBudgetValue, jdbcType=DECIMAL}
			</trim>
		</foreach>
	</insert>

	<update id="updateBatch" parameterType="map">
		<foreach collection="records" index="index" item="item" separator=";">
			update wx_t_budget2021
			<set>
			<if test="item.updateUserId != null">
				update_user_id = #{item.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="item.updateTime != null">
				update_time = #{item.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="item.regionBudgetValue != null">
				region_budget_value = #{item.regionBudgetValue,jdbcType=DOUBLE},
			</if>
			<if test="item.mktBudgetValue != null">
				mkt_budget_value = #{item.mktBudgetValue,jdbcType=DOUBLE},
			</if>
			<if test="item.itemBudgetValue != null">
				item_budget_value = #{item.itemBudgetValue,jdbcType=DOUBLE}
			</if>
			</set>
			where id = #{item.id,jdbcType=BIGINT}
		</foreach>
	</update>
</mapper>
