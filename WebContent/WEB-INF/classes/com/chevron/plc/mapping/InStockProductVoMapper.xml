<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.plc.dao.InStockProductVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.plc.model.InStockProductVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="stock_out_no" property="stockOutNo" jdbcType="NVARCHAR" />
    <result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="expect_in_count" property="expectInCount" jdbcType="BIGINT" />
    <result column="actual_in_count" property="actualInCount" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="pack_units" property="packUnits" jdbcType="NVARCHAR" />
		<result column="pack" property="pack" jdbcType="NUMERIC"/>
		<result column="capacity" property="capacity" jdbcType="NUMERIC"/>
		<result column="total_in_count" property="totalInCount" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id,stock_out_no ,stock_in_no, sku, expect_in_count, actual_in_count, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.plc.model.InStockProductVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_in_stock_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.plc.model.InStockProductVoExample" >
    delete from wx_t_in_stock_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert"  useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.plc.model.InStockProductVo" >
    insert into wx_t_in_stock_product (id, stock_out_no,stock_in_no, sku, 
      expect_in_count, actual_in_count, remark
      )
    values (#{id,jdbcType=BIGINT}, #{stockOutNo,jdbcType=NVARCHAR},#{stockInNo,jdbcType=NVARCHAR}, #{sku,jdbcType=NVARCHAR}, 
      #{expectInCount,jdbcType=BIGINT}, #{actualInCount,jdbcType=BIGINT}, #{remark,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective"  useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.plc.model.InStockProductVo" >
    insert into wx_t_in_stock_product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="stockOutNo != null" >
        stock_out_no,
      </if>
      <if test="stockInNo != null" >
        stock_in_no,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="expectInCount != null" >
        expect_in_count,
      </if>
      <if test="actualInCount != null" >
        actual_in_count,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="stockOutNo != null" >
        #{stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="stockInNo != null" >
        #{stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="expectInCount != null" >
        #{expectInCount,jdbcType=BIGINT},
      </if>
      <if test="actualInCount != null" >
        #{actualInCount,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_in_stock_product
    <set >
      <if test="record.stockOutNo != null" >
        stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockInNo != null" >
        stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.expectInCount != null" >
        expect_in_count = #{record.expectInCount,jdbcType=BIGINT},
      </if>
      <if test="record.actualInCount != null" >
        actual_in_count = #{record.actualInCount,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_in_stock_product
    set 
      stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      sku = #{record.sku,jdbcType=NVARCHAR},
      expect_in_count = #{record.expectInCount,jdbcType=BIGINT},
      actual_in_count = #{record.actualInCount,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <resultMap id="InStockProductDetail" type="com.chevron.plc.model.InStockProductDetaiView" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="stock_out_no" property="stockOutNo" jdbcType="NVARCHAR" />
    <result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="product_category" property="productCategory" jdbcType="NVARCHAR" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="expect_in_count" property="expectInCount" jdbcType="BIGINT" />
    <result column="actual_in_count" property="actualInCount" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <select id="selectInStockProductByStockInNo" parameterType="map" resultMap="InStockProductDetail">
  	SELECT t.id,t.stock_out_no,t.stock_in_no,t.sku,t.expect_in_count,t.actual_in_count,t.remark,
       t1.name AS product_name,
       t1.category as product_category,
       t1.units    
	FROM wx_t_in_stock_product t
	JOIN wx_t_product t1 ON t.sku=t1.sku
	WHERE t.stock_in_no = #{stockInNo}
  </select>
  
  
  <resultMap id="workshopInstockProduct" type="com.chevron.task.model.WorkshopInstockProductResp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="name" property="name" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="sale_price" property="salePrice" jdbcType="NUMERIC" />
    <result column="category" property="category" jdbcType="NVARCHAR" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="expect_in_count" property="expQuantity" jdbcType="BIGINT" />
    <result column="actual_in_count" property="actualQuantity" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="iconid" property="iconId" jdbcType="BIGINT" />
    
   
  </resultMap>
  <select id="selectInStockProductByCondition" parameterType="map" resultMap="workshopInstockProduct">
   SELECT 
	  t_in_s_p.*, 
	  t_p.name, 
	  t_p.price, 
	  t_p.sale_price, 
	  t_p.category, 
	  t_p.brand, 
	  t_p.units,
	  t_att_file.att_id iconid
	FROM 
	  wx_t_in_stock_product t_in_s_p 
	  LEFT JOIN wx_t_product t_p ON t_in_s_p.sku = t_p.sku 
	  inner JOIN wx_att_file t_att_file ON t_p.id=t_att_file.source_id AND source_type=5
	WHERE  
  	  t_in_s_p.stock_in_no = #{inStockNo}
  	ORDER BY t_in_s_p.id
  </select>
  
  
  
  
  <update id="updateWorkshopInstockProductBatch" parameterType="map" >
   <foreach collection="lstWorkshopInstockProduct" item="item" index="index" open="begin" close="end;" separator=";">
    update wx_t_in_stock_product
    <set >
      <if test="item.actualQuantity != null" >
        actual_in_count = #{item.actualQuantity,jdbcType=BIGINT},
      </if>
    </set>
    where 
    	<if test="item.id!=null">
    	id = #{item.id,jdbcType=NVARCHAR}
    	  and
    	</if>
     1=1
    </foreach>
  </update>
  
  <insert id="insertBatch" parameterType="java.util.List">
    insert into wx_t_in_stock_product (stock_out_no,stock_in_no, sku, 
      expect_in_count, actual_in_count, remark
      )
    values  
       <foreach collection="list" index="index" item="item"
           separator=",">
           <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.stockOutNo,jdbcType=NVARCHAR},#{item.stockInNo,jdbcType=NVARCHAR}, #{item.sku,jdbcType=NVARCHAR}, 
                #{item.expectInCount,jdbcType=BIGINT}, #{item.actualInCount,jdbcType=BIGINT}, #{item.remark,jdbcType=NVARCHAR}
           </trim>
       </foreach>
  </insert>
  <select id="buildDistributorInStock" parameterType="map" resultMap="BaseResultMap">
select bsi.[product_code_SAP] sku, p.name product_name,
				di.dic_item_name pack_units,
				sum(convert(int, round(bsi.liters/p.pack, 0))) expect_in_count
				from[PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi 
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
				left join wx_t_product p on p.sku=bsi.[product_code_SAP]
		where bsi.[sales_reference_doc]=#{orderNo} and p.pack>0
		<if test="sku != null and sku != ''">
		and  p.sku=#{sku}
		</if>
		group by bsi.[product_code_SAP], p.name,di.dic_item_name
  </select>
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.stock_out_no, t1.stock_in_no, t1.sku, t1.expect_in_count, t1.actual_in_count, t1.remark, p.name product_name,
		di.dic_item_name pack_units, p.pack, 
		(select sum(iisp1.actual_in_count) from wx_t_in_stock_product iisp1 left join wx_t_in_stock iis1 on iis1.stock_in_no=iisp1.stock_in_no      
			where i1.order_type=iis1.order_type and i1.order_no=iis1.order_no and iis1.status='2' and iisp1.sku=t1.sku) total_in_count    
		  from wx_t_in_stock_product t1
		  left join wx_t_in_stock i1 on i1.stock_in_no=t1.stock_in_no
		  left join wx_t_product p on p.sku=t1.sku
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=t1.sku
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
		 where 1=1
		 <if test="inStockId != null">
		 and i1.id=#{inStockId}
		 </if>
		 <if test="stockInNos != null">
			and t1.stock_in_no in
			<foreach collection="stockInNos" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		 
		 </if>
	</select>
</mapper>