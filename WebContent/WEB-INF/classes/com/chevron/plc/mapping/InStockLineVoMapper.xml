<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.plc.dao.InStockLineVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.plc.model.InStockLineVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    <result column="code" property="code" jdbcType="NVARCHAR" />
    <result column="code_type" property="codeType" jdbcType="NVARCHAR" />
    <result column="scan_time" property="scanTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, stock_in_no, code, code_type, scan_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.plc.model.InStockLineVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_in_stock_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.plc.model.InStockLineVoExample" >
    delete from wx_t_in_stock_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert"  useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.plc.model.InStockLineVo" >
    insert into wx_t_in_stock_line (id, stock_in_no, code, 
      code_type, scan_time, remark
      )
    values (#{id,jdbcType=BIGINT}, #{stockInNo,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, 
      #{codeType,jdbcType=NVARCHAR}, #{scanTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective"   useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.plc.model.InStockLineVo" >
    insert into wx_t_in_stock_line
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="stockInNo != null" >
        stock_in_no,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="codeType != null" >
        code_type,
      </if>
      <if test="scanTime != null" >
        scan_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="stockInNo != null" >
        #{stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="codeType != null" >
        #{codeType,jdbcType=NVARCHAR},
      </if>
      <if test="scanTime != null" >
        #{scanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_in_stock_line
    <set >
      <if test="record.stockInNo != null" >
        stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.codeType != null" >
        code_type = #{record.codeType,jdbcType=NVARCHAR},
      </if>
      <if test="record.scanTime != null" >
        scan_time = #{record.scanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_in_stock_line
    set 
      stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      code_type = #{record.codeType,jdbcType=NVARCHAR},
      scan_time = #{record.scanTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertBatch" parameterType="java.util.List">
     insert into wx_t_in_stock_line
     ( stock_in_no,     
        code,     
        code_type,      
        scan_time,
        remark ) 
     values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.stockInNo,jdbcType=NVARCHAR},
			#{item.code,jdbcType=NVARCHAR},
			#{item.codeType,jdbcType=NVARCHAR},
			#{item.scanTime,jdbcType=TIMESTAMP},
			#{item.remark,jdbcType=NVARCHAR}
		</trim>
	 </foreach>
   </insert>
   <select id="getInStockLineByBottleCode" parameterType="map" resultMap="BaseResultMap">
		SELECT instockline.* FROM wx_t_in_stock_line instockline 
		LEFT JOIN wx_t_in_stock instock ON instock.stock_in_no = instockline.stock_in_no WHERE 1 =1 
		<if test="stockToType != null">
			and instock.stock_to_type = #{stockToType}
		</if>
		<if test="stockTo">
			and instock.stock_to = #{stockTo}
		</if>
		<if test="code!=null">
			and instockline.code = #{code}
		</if>
    </select>  
    <select id="getInStockLineByStackCode" parameterType="map" resultMap="BaseResultMap">
		SELECT instockline.* FROM wx_t_in_stock_line instockline 
		LEFT JOIN wx_t_in_stock instock ON instock.stock_in_no = instockline.stock_in_no 
		LEFT JOIN wx_t_oem_product_packaging_code oppc ON oppc.code1 = instockline.code 
		WHERE 1 =1 
			<if test="stockToType != null">
			and instock.stock_to_type = #{stockToType}
			</if>
			<if test="stockTo">
				and instock.stock_to = #{stockTo}
			</if>
			<if test="code!=null">
				and oppc.code3 = #{code}
			</if>
    </select>
    <select id="getInStockLineByBoxCode" parameterType="map" resultMap="BaseResultMap">
		SELECT instockline.* FROM wx_t_in_stock_line instockline 
		LEFT JOIN wx_t_in_stock instock ON instock.stock_in_no = instockline.stock_in_no 
		LEFT JOIN wx_t_oem_product_packaging_code oppc ON oppc.code1 = instockline.code 
		WHERE 1 =1 
			<if test="stockToType != null">
			and instock.stock_to_type = #{stockToType}
			</if>
			<if test="stockTo">
				and instock.stock_to = #{stockTo}
			</if>
			<if test="code!=null">
				and oppc.code2 = #{code}
			</if>
    </select>
</mapper>