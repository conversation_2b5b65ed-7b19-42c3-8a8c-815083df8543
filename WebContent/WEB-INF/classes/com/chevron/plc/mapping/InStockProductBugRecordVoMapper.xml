<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.plc.dao.InStockProductBugRecordVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.plc.model.InStockProductBugRecordVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
    <result column="workshop_name" property="workshopName" jdbcType="NVARCHAR" />
    <result column="logistics_number" property="logisticsNumber" jdbcType="NVARCHAR" />
    <result column="bug_comment" property="bugComment" jdbcType="NVARCHAR" />
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, partner_name, workshop_id, workshop_name, logistics_number, bug_comment, 
    upload_time, create_time, creator, update_time, updater
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.plc.model.InStockProductBugRecordVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_in_stock_product_bug_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.plc.model.InStockProductBugRecordVoExample" >
    delete from wx_t_in_stock_product_bug_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.plc.model.InStockProductBugRecordVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_in_stock_product_bug_record (id, partner_id, partner_name, 
      workshop_id, workshop_name, logistics_number, 
      bug_comment, upload_time, create_time, 
      creator, update_time, updater
      )
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, 
      #{workshopId,jdbcType=BIGINT}, #{workshopName,jdbcType=NVARCHAR}, #{logisticsNumber,jdbcType=NVARCHAR}, 
      #{bugComment,jdbcType=NVARCHAR}, #{uploadTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.plc.model.InStockProductBugRecordVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_in_stock_product_bug_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerName != null" >
        partner_name,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
      <if test="workshopName != null" >
        workshop_name,
      </if>
      <if test="logisticsNumber != null" >
        logistics_number,
      </if>
      <if test="bugComment != null" >
        bug_comment,
      </if>
      <if test="uploadTime != null" >
        upload_time,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="logisticsNumber != null" >
        #{logisticsNumber,jdbcType=NVARCHAR},
      </if>
      <if test="bugComment != null" >
        #{bugComment,jdbcType=NVARCHAR},
      </if>
      <if test="uploadTime != null" >
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_in_stock_product_bug_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null" >
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.workshopId != null" >
        workshop_id = #{record.workshopId,jdbcType=BIGINT},
      </if>
      <if test="record.workshopName != null" >
        workshop_name = #{record.workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="record.logisticsNumber != null" >
        logistics_number = #{record.logisticsNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.bugComment != null" >
        bug_comment = #{record.bugComment,jdbcType=NVARCHAR},
      </if>
      <if test="record.uploadTime != null" >
        upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null" >
        updater = #{record.updater,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_in_stock_product_bug_record
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      workshop_id = #{record.workshopId,jdbcType=BIGINT},
      workshop_name = #{record.workshopName,jdbcType=NVARCHAR},
      logistics_number = #{record.logisticsNumber,jdbcType=NVARCHAR},
      bug_comment = #{record.bugComment,jdbcType=NVARCHAR},
      upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      updater = #{record.updater,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>