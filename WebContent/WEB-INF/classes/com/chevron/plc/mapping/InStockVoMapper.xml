<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.plc.dao.InStockVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.plc.model.InStockVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    <result column="delivery_number" property="deliveryNumber" jdbcType="NVARCHAR" />
    <result column="stock_out_no" property="stockOutNo" jdbcType="NVARCHAR" />
    <result column="stock_from" property="stockFrom" jdbcType="NVARCHAR" />
    <result column="stock_from_type" property="stockFromType" jdbcType="NVARCHAR" />
    <result column="stock_to" property="stockTo" jdbcType="NVARCHAR" />
    <result column="stock_to_type" property="stockToType" jdbcType="NVARCHAR" />
    <result column="stock_from_orgname" property="stockFromOrgname" jdbcType="NVARCHAR" />
    <result column="stock_from_address" property="stockFromAddress" jdbcType="NVARCHAR" />
    <result column="stock_from_phone" property="stockFromPhone" jdbcType="NVARCHAR" />
    <result column="stock_to_orgname" property="stockToOrgname" jdbcType="NVARCHAR" />
    <result column="stock_to_address" property="stockToAddress" jdbcType="NVARCHAR" />
    <result column="stock_to_phone" property="stockToPhone" jdbcType="NVARCHAR" />
    <result column="stock_to_contact_person" property="stockToContactPerson" jdbcType="NVARCHAR" />
    <result column="storage_location" property="storageLocation" jdbcType="NVARCHAR" />
    <result column="required_arrive_time" property="requiredArriveTime" jdbcType="TIMESTAMP" />
    <result column="transport_mode" property="transportMode" jdbcType="NVARCHAR" />
    <result column="payment_terms" property="paymentTerms" jdbcType="NVARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
    <result column="order_type" property="orderType" jdbcType="NVARCHAR" />
    <result column="order_data" property="orderData" jdbcType="TIMESTAMP" />
    <result column="fob_destination" property="fobDestination" jdbcType="NVARCHAR" />
    <result column="seal_no" property="sealNo" jdbcType="NVARCHAR" />
    <result column="in_time" property="inTime" jdbcType="TIMESTAMP" />
    <result column="scan_gun_no" property="scanGunNo" jdbcType="NVARCHAR" />
    <result column="scan_person_id" property="scanPersonId" jdbcType="NVARCHAR" />
    <result column="scan_person_name" property="scanPersonName" jdbcType="NVARCHAR" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="updater" property="updater" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
    	<result column="store_name" property="storeName" jdbcType="NVARCHAR" />
        <collection property="productList" ofType="com.chevron.plc.model.InStockProductVo">
		    <result column="sku" property="sku" jdbcType="NVARCHAR" />
		    <result column="expect_in_count" property="expectInCount" jdbcType="BIGINT" />
		    <result column="actual_in_count" property="actualInCount" jdbcType="BIGINT" />
			<result column="pack" property="pack" jdbcType="NUMERIC"/>
			<result column="capacity" property="capacity" jdbcType="NUMERIC"/>
        </collection>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, stock_in_no,delivery_number, stock_out_no, stock_from, stock_from_type, stock_to, stock_to_type, 
    stock_from_orgname, stock_from_address, stock_from_phone, stock_to_orgname, stock_to_address, 
    stock_to_phone,stock_to_contact_person, storage_location, required_arrive_time, transport_mode, payment_terms, 
    order_no, order_type, order_data, fob_destination, seal_no, in_time, scan_gun_no, 
    scan_person_id, scan_person_name, status, create_time, update_time, creator, updater, 
    remark,ext_property1,ext_property2,ext_property3,ext_property4,ext_property5
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.plc.model.InStockVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_in_stock
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.plc.model.InStockVoExample" >
    delete from wx_t_in_stock
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.plc.model.InStockVo" >
    insert into wx_t_in_stock (id, stock_in_no,delivery_number, stock_out_no, 
      stock_from, stock_from_type, stock_to, 
      stock_to_type, stock_from_orgname, stock_from_address, 
      stock_from_phone, stock_to_orgname, stock_to_address, 
      stock_to_phone, stock_to_contact_person,storage_location, required_arrive_time, 
      transport_mode, payment_terms, order_no, 
      order_type, order_data, fob_destination, 
      seal_no, in_time, scan_gun_no, 
      scan_person_id, scan_person_name, status, 
      create_time, update_time, creator, 
      updater, remark)
    values (#{id,jdbcType=BIGINT}, #{stockInNo,jdbcType=NVARCHAR},#{deliveryNumber,jdbcType=NVARCHAR}, #{stockOutNo,jdbcType=NVARCHAR}, 
      #{stockFrom,jdbcType=NVARCHAR}, #{stockFromType,jdbcType=NVARCHAR}, #{stockTo,jdbcType=NVARCHAR}, 
      #{stockToType,jdbcType=NVARCHAR}, #{stockFromOrgname,jdbcType=NVARCHAR}, #{stockFromAddress,jdbcType=NVARCHAR}, 
      #{stockFromPhone,jdbcType=NVARCHAR}, #{stockToOrgname,jdbcType=NVARCHAR}, #{stockToAddress,jdbcType=NVARCHAR}, 
      #{stockToPhone,jdbcType=NVARCHAR},#{stockToContactPerson,jdbcType=NVARCHAR}, #{storageLocation,jdbcType=NVARCHAR}, #{requiredArriveTime,jdbcType=TIMESTAMP}, 
      #{transportMode,jdbcType=NVARCHAR}, #{paymentTerms,jdbcType=NVARCHAR}, #{orderNo,jdbcType=NVARCHAR}, 
      #{orderType,jdbcType=NVARCHAR}, #{orderData,jdbcType=TIMESTAMP}, #{fobDestination,jdbcType=NVARCHAR}, 
      #{sealNo,jdbcType=NVARCHAR}, #{inTime,jdbcType=TIMESTAMP}, #{scanGunNo,jdbcType=NVARCHAR}, 
      #{scanPersonId,jdbcType=NVARCHAR}, #{scanPersonName,jdbcType=NVARCHAR}, #{status,jdbcType=NVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=NVARCHAR}, 
      #{updater,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.plc.model.InStockVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_in_stock
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="stockInNo != null" >
        stock_in_no,
      </if>
      <if test="deliveryNumber != null" >
        delivery_number,
      </if>
      <if test="stockOutNo != null" >
        stock_out_no,
      </if>
      <if test="stockFrom != null" >
        stock_from,
      </if>
      <if test="stockFromType != null" >
        stock_from_type,
      </if>
      <if test="stockTo != null" >
        stock_to,
      </if>
      <if test="stockToType != null" >
        stock_to_type,
      </if>
      <if test="stockFromOrgname != null" >
        stock_from_orgname,
      </if>
      <if test="stockFromAddress != null" >
        stock_from_address,
      </if>
      <if test="stockFromPhone != null" >
        stock_from_phone,
      </if>
      <if test="stockToOrgname != null" >
        stock_to_orgname,
      </if>
      <if test="stockToAddress != null" >
        stock_to_address,
      </if>
      <if test="stockToPhone != null" >
        stock_to_phone,
      </if>
      <if test="stockToContactPerson != null" >
        stock_to_contact_person,
      </if>
      <if test="storageLocation != null" >
        storage_location,
      </if>
      <if test="requiredArriveTime != null" >
        required_arrive_time,
      </if>
      <if test="transportMode != null" >
        transport_mode,
      </if>
      <if test="paymentTerms != null" >
        payment_terms,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="orderType != null" >
        order_type,
      </if>
      <if test="orderData != null" >
        order_data,
      </if>
      <if test="fobDestination != null" >
        fob_destination,
      </if>
      <if test="sealNo != null" >
        seal_no,
      </if>
      <if test="inTime != null" >
        in_time,
      </if>
      <if test="scanGunNo != null" >
        scan_gun_no,
      </if>
      <if test="scanPersonId != null" >
        scan_person_id,
      </if>
      <if test="scanPersonName != null" >
        scan_person_name,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="remark != null" >
        remark,
      </if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="stockInNo != null" >
        #{stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryNumber != null" >
        #{deliveryNumber,jdbcType=NVARCHAR},
      </if>
      <if test="stockOutNo != null" >
        #{stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="stockFrom != null" >
        #{stockFrom,jdbcType=NVARCHAR},
      </if>
      <if test="stockFromType != null" >
        #{stockFromType,jdbcType=NVARCHAR},
      </if>
      <if test="stockTo != null" >
        #{stockTo,jdbcType=NVARCHAR},
      </if>
      <if test="stockToType != null" >
        #{stockToType,jdbcType=NVARCHAR},
      </if>
      <if test="stockFromOrgname != null" >
        #{stockFromOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="stockFromAddress != null" >
        #{stockFromAddress,jdbcType=NVARCHAR},
      </if>
      <if test="stockFromPhone != null" >
        #{stockFromPhone,jdbcType=NVARCHAR},
      </if>
      <if test="stockToOrgname != null" >
        #{stockToOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="stockToAddress != null" >
        #{stockToAddress,jdbcType=NVARCHAR},
      </if>
      <if test="stockToPhone != null" >
        #{stockToPhone,jdbcType=NVARCHAR},
      </if>
      <if test="stockToContactPerson != null" >
        #{stockToContactPerson,jdbcType=NVARCHAR},
      </if>
      <if test="storageLocation != null" >
        #{storageLocation,jdbcType=NVARCHAR},
      </if>
      <if test="requiredArriveTime != null" >
        #{requiredArriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transportMode != null" >
        #{transportMode,jdbcType=NVARCHAR},
      </if>
      <if test="paymentTerms != null" >
        #{paymentTerms,jdbcType=NVARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="orderData != null" >
        #{orderData,jdbcType=TIMESTAMP},
      </if>
      <if test="fobDestination != null" >
        #{fobDestination,jdbcType=NVARCHAR},
      </if>
      <if test="sealNo != null" >
        #{sealNo,jdbcType=NVARCHAR},
      </if>
      <if test="inTime != null" >
        #{inTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scanGunNo != null" >
        #{scanGunNo,jdbcType=NVARCHAR},
      </if>
      <if test="scanPersonId != null" >
        #{scanPersonId,jdbcType=NVARCHAR},
      </if>
      <if test="scanPersonName != null" >
        #{scanPersonName,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_in_stock
    <set >
      <if test="record.stockInNo != null" >
        stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.deliveryNumber != null" >
        delivery_number = #{record.deliveryNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockOutNo != null" >
        stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockFrom != null" >
        stock_from = #{record.stockFrom,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockFromType != null" >
        stock_from_type = #{record.stockFromType,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockTo != null" >
        stock_to = #{record.stockTo,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockToType != null" >
        stock_to_type = #{record.stockToType,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockFromOrgname != null" >
        stock_from_orgname = #{record.stockFromOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockFromAddress != null" >
        stock_from_address = #{record.stockFromAddress,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockFromPhone != null" >
        stock_from_phone = #{record.stockFromPhone,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockToOrgname != null" >
        stock_to_orgname = #{record.stockToOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockToAddress != null" >
        stock_to_address = #{record.stockToAddress,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockToPhone != null" >
        stock_to_phone = #{record.stockToPhone,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockToContactPerson != null" >
        stock_to_contact_person = #{record.stockToContactPerson,jdbcType=NVARCHAR},
      </if>
      <if test="record.storageLocation != null" >
        storage_location = #{record.storageLocation,jdbcType=NVARCHAR},
      </if>
      <if test="record.requiredArriveTime != null" >
        required_arrive_time = #{record.requiredArriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.transportMode != null" >
        transport_mode = #{record.transportMode,jdbcType=NVARCHAR},
      </if>
      <if test="record.paymentTerms != null" >
        payment_terms = #{record.paymentTerms,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderType != null" >
        order_type = #{record.orderType,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderData != null" >
        order_data = #{record.orderData,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fobDestination != null" >
        fob_destination = #{record.fobDestination,jdbcType=NVARCHAR},
      </if>
      <if test="record.sealNo != null" >
        seal_no = #{record.sealNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.inTime != null" >
        in_time = #{record.inTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scanGunNo != null" >
        scan_gun_no = #{record.scanGunNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.scanPersonId != null" >
        scan_person_id = #{record.scanPersonId,jdbcType=NVARCHAR},
      </if>
      <if test="record.scanPersonName != null" >
        scan_person_name = #{record.scanPersonName,jdbcType=NVARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.updater != null" >
        updater = #{record.updater,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_in_stock
    set 
      stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      delivery_number = #{record.deliveryNumber,jdbcType=NVARCHAR},
      stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      stock_from = #{record.stockFrom,jdbcType=NVARCHAR},
      stock_from_type = #{record.stockFromType,jdbcType=NVARCHAR},
      stock_to = #{record.stockTo,jdbcType=NVARCHAR},
      stock_to_type = #{record.stockToType,jdbcType=NVARCHAR},
      stock_from_orgname = #{record.stockFromOrgname,jdbcType=NVARCHAR},
      stock_from_address = #{record.stockFromAddress,jdbcType=NVARCHAR},
      stock_from_phone = #{record.stockFromPhone,jdbcType=NVARCHAR},
      stock_to_orgname = #{record.stockToOrgname,jdbcType=NVARCHAR},
      stock_to_address = #{record.stockToAddress,jdbcType=NVARCHAR},
      stock_to_phone = #{record.stockToPhone,jdbcType=NVARCHAR},
      stock_to_contact_person = #{record.stockToContactPerson,jdbcType=NVARCHAR},
      storage_location = #{record.storageLocation,jdbcType=NVARCHAR},
      required_arrive_time = #{record.requiredArriveTime,jdbcType=TIMESTAMP},
      transport_mode = #{record.transportMode,jdbcType=NVARCHAR},
      payment_terms = #{record.paymentTerms,jdbcType=NVARCHAR},
      order_no = #{record.orderNo,jdbcType=NVARCHAR},
      order_type = #{record.orderType,jdbcType=NVARCHAR},
      order_data = #{record.orderData,jdbcType=TIMESTAMP},
      fob_destination = #{record.fobDestination,jdbcType=NVARCHAR},
      seal_no = #{record.sealNo,jdbcType=NVARCHAR},
      in_time = #{record.inTime,jdbcType=TIMESTAMP},
      scan_gun_no = #{record.scanGunNo,jdbcType=NVARCHAR},
      scan_person_id = #{record.scanPersonId,jdbcType=NVARCHAR},
      scan_person_name = #{record.scanPersonName,jdbcType=NVARCHAR},
      status = #{record.status,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      updater = #{record.updater,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="queryInStockByStockInNo" parameterType="map" resultMap="BaseResultMap">
		SELECT
		    <include refid="Base_Column_List" />
		FROM wx_t_in_stock 
		WHERE stock_in_no  = #{stockInNo}
  </select>
  <select id="queryInStockByOrderNo" parameterType="map" resultMap="BaseResultMap">
		SELECT
		    <include refid="Base_Column_List" />
		FROM wx_t_in_stock 
		WHERE order_no  = #{orderNo}
 </select>
  <select id="getInStockByParamsWithPage" parameterType="map" resultMap="BaseResultMap">
	SELECT TOP ${pageCount} * FROM (
		SELECT * ,row_number() over(order by t.create_time desc) as rownumber FROM (
 			SELECT	 <include refid="Base_Column_List" />
				FROM
					wx_t_in_stock instock
				WHERE 
				<if test="stockInNo!=null">
					instock.stock_in_no = #{stockInNo}
				 and
				</if>
				<if test="status!=null">
					instock.status = #{status}
				 and
				</if>
				<if test="stockTo!=null">
					instock.stock_to = #{stockTo}
				 and
				</if>
				1=1 
			)AS t 
		) AS t2 WHERE rownumber>#{startPage, jdbcType=INTEGER} 
    </select>
	<select id="countInStockByParamsWithPage" parameterType="map" resultType="Long">
		SELECT count(1) FROM (
 			SELECT	 DISTINCT (instock.stock_in_no) 
				FROM
					wx_t_in_stock instock
				WHERE 
				<if test="stockInNo!=null">
					instock.stock_in_no = #{stockInNo}
				 and
				</if>
				<if test="status!=null">
					instock.status = #{status}
				 and
				</if>
				<if test="stockTo!=null">
					instock.stock_to = #{stockTo}
				 and
				</if>
				<if test="orderNo">
					instock.order_no = #{orderNo}
				 and
				</if>
				1=1 ) 
		AS t
 	</select>
 	
 	
 	
 <update id="updateInStockByMapParam" parameterType="map">
    update wx_t_in_stock
    set 
      status = #{status,jdbcType=NVARCHAR}
    where 
      <if test="instockNo!=null">
        stock_in_no = #{instockNo,jdbcType=NVARCHAR}
        and
      </if>
    
      1=1
     
 </update>

	<!-- 基础查询条件 -->
	<sql id="base_cond">
		<if test="orderType != null and orderType != ''">
		and t1.order_type=#{orderType}
		</if>
		<if test="orderNo != null and orderNo != ''">
		and t1.order_no=#{orderNo}
		</if>
		<if test="extProperty3 != null and extProperty3 != ''">
		and t1.ext_property3=#{extProperty3}
		</if>
	</sql>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.stock_in_no, t1.delivery_number, t1.stock_out_no, t1.stock_from, t1.stock_from_type, t1.stock_to,
			 t1.stock_to_type, t1.stock_from_orgname, t1.stock_from_address, t1.stock_from_phone, t1.stock_to_orgname,
			 t1.stock_to_address, t1.stock_to_phone, t1.stock_to_contact_person, t1.storage_location, t1.required_arrive_time,
			 t1.transport_mode, t1.payment_terms, t1.order_no, t1.order_type, t1.order_data, t1.fob_destination, t1.seal_no,
			 t1.in_time, t1.scan_gun_no, t1.scan_person_id, t1.scan_person_name, t1.status, t1.create_time, t1.update_time,
			 t1.creator, t1.updater, t1.remark, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.ext_property4,
			 t1.ext_property5
			 ,case when t1.stock_to_type='sp' then (select io1.organization_name from wx_t_organization io1 where io1.id=t1.stock_to) else null end t1.stock_to_orgname
		  from wx_t_in_stock t1
		  left join wx_t_store2021 s1 on t1.order_type='DISTRIBUTOR_DELIVERY_ORDER' and s1.id=t1.ext_property1
		 where 1=1
		 <if test="id != null">
		 and t1.id=#{id}
		 </if>
		 <include refid="base_cond"/>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.plc.model.InStockVoParams">
		select t1.id, t1.stock_in_no, t1.delivery_number, t1.stock_out_no, t1.stock_from, t1.stock_from_type, t1.stock_to,
			 t1.stock_to_type, t1.stock_from_orgname, t1.stock_from_address, t1.stock_from_phone,
			 t1.stock_to_address, t1.stock_to_phone, t1.stock_to_contact_person, t1.storage_location, t1.required_arrive_time,
			 t1.transport_mode, t1.payment_terms, t1.order_no, t1.order_type, t1.order_data, t1.fob_destination, t1.seal_no,
			 t1.in_time, t1.scan_gun_no, t1.scan_person_id, t1.scan_person_name, t1.status, t1.create_time, t1.update_time,
			 t1.creator, t1.updater, t1.remark, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.ext_property4,
			 t1.ext_property5,s1.name store_name
			 ,case when t1.stock_to_type='sp' then (select io1.organization_name from wx_t_organization io1 where io1.id=t1.stock_to) else null end stock_to_orgname
		  from wx_t_in_stock t1
		  left join wx_t_store2021 s1 on t1.order_type='DISTRIBUTOR_DELIVERY_ORDER' and s1.id=t1.ext_property1
		 where 1=1
		 <include refid="base_cond"/>
	</select>
	
	<select id="buildDistributorInStockBySapDelivery" resultMap="BaseResultMap" parameterType="map">
	SELECT mdo1.delivery_no AS id, po1.partner_id AS stock_to, mdo1.delivery_no AS order_no
		, (
			SELECT TOP 1 psc.store_id
			FROM [PP_MID].dbo.mid_partner_sale_config psc
			WHERE psc.id = po1.partner_sale_config_id
				AND psc.date_from &lt;= getdate()
				AND psc.date_to >= getdate()
		) AS ext_property1, sales_reference_doc AS ext_property2, po1.order_no AS ext_property3, mdo1.material AS sku, sum(mdo1.order_quantity) AS expect_in_count
		, sum(mdo1.delivery_quantity) AS actual_in_count, p1.capacity, p1.pack
		from PP_MID.dbo.syn_dw_to_pp_mtd_delivery_order_dtl mdo1
		left join wx_t_product p1 on p1.sku=mdo1.material
		join wx_t_partner_order po1 on mdo1.purchase_order_number=po1.order_no
		where not exists (select 1 from wx_t_in_stock is1 where is1.order_type='DISTRIBUTOR_DELIVERY_ORDER' and is1.order_no=mdo1.delivery_no)
		 <if test="startTime != null">
		 and mdo1.goods_issue_date>=#{startTime}
		 </if>
		 <if test="endTime != null">
		 and mdo1.goods_issue_date&lt;=#{endTime}
		 </if>
		 group by  mdo1.delivery_no, po1.partner_id, po1.partner_sale_config_id,sales_reference_doc, po1.order_no, mdo1.material, p1.capacity, p1.pack
	</select>
	<select id="queryUnmatchDistributorInStock" resultType="Long" parameterType="map">
	select is1.id 
	from wx_t_in_stock is1 
	where is1.order_type='DISTRIBUTOR_DELIVERY_ORDER' and exists (select 1 from wx_t_in_stock_product isp1 
			where isp1.stock_in_no=is1.stock_in_no and isp1.actual_in_count!=
					(select sum(mdo1.delivery_quantity) from PP_MID.dbo.syn_dw_to_pp_mtd_delivery_order_dtl mdo1 
					where mdo1.material=isp1.sku and mdo1.delivery_no=is1.order_no))
		 <if test="startTime != null">
		 and is1.in_time>=#{startTime}
		 </if>
		 <if test="endTime != null">
		 and is1.in_time&lt;=#{endTime}
		 </if>
	</select>
</mapper>