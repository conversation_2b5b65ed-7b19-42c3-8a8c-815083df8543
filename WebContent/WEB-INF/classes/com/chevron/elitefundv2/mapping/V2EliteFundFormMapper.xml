<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elitefundv2.dao.V2EliteFundFormMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.elitefundv2.model.V2EliteFundForm">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="request_no" property="requestNo" jdbcType="VARCHAR"/>
		<result column="fund_type" property="fundType" jdbcType="VARCHAR"/>
		<result column="apply_project" property="applyProject" jdbcType="VARCHAR"/>
		<result column="apply_amount" property="applyAmount" jdbcType="NUMERIC"/>
		<result column="use_fund_amount" property="useFundAmount" jdbcType="NUMERIC"/>
		<result column="use_ivi_amount" property="useIviAmount" jdbcType="NUMERIC"/>
		<result column="use_year_amount" property="useYearAmount" jdbcType="NUMERIC"/>
		<result column="total_invoice_amount" property="totalInvoiceAmount" jdbcType="NUMERIC"/>
		<result column="final_amount" property="finalAmount" jdbcType="NUMERIC"/>
		<result column="actual_amount" property="actualAmount" jdbcType="NUMERIC"/>
		<result column="form_attrs" property="formAttrs" jdbcType="VARCHAR"/>
		<result column="use_remain_invoice" property="useRemainInvoice" jdbcType="NUMERIC"/>
		<result column="unused_invoice" property="unusedInvoice" jdbcType="NUMERIC"/>
		<result column="unused_invoice_info" property="unusedInvoiceInfo" jdbcType="VARCHAR"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="channel_weight" property="channelWeight" jdbcType="INTEGER"/>
		<result column="bu" property="bu" jdbcType="VARCHAR"/>
		<result column="form_status" property="formStatus" jdbcType="INTEGER"/>
		<result column="apply_year" property="applyYear" jdbcType="INTEGER"/>
		<result column="apply_phase" property="applyPhase" jdbcType="VARCHAR"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="form_version" property="formVersion" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
		<result column="check_reject_flag" property="checkRejectFlag" jdbcType="INTEGER"/>
		<association property="workflowInstance" column="form_key" resultMap="com.sys.workflow.dao.WorkflowInstanceMapper.BaseResultMap"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,distributor_id,request_no,fund_type,apply_project,apply_amount,use_fund_amount,use_ivi_amount,use_year_amount,
		total_invoice_amount,final_amount,actual_amount,form_attrs,use_remain_invoice,unused_invoice,unused_invoice_info,
		ext_flag,ext_property1,ext_property2,ext_property3,ext_property4,ext_property5,channel_weight,bu,form_status,
		apply_year,apply_phase,delete_flag,form_version,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.elitefundv2.model.V2EliteFundForm">
		update wx_t_v2_elite_fund_form set
				distributor_id = #{distributorId,jdbcType=BIGINT},
				request_no = #{requestNo,jdbcType=VARCHAR},
				fund_type = #{fundType,jdbcType=VARCHAR},
				bu = #{bu,jdbcType=VARCHAR},
				apply_year = #{applyYear,jdbcType=INTEGER},
				apply_phase = #{applyPhase,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.elitefundv2.model.V2EliteFundForm">
		update wx_t_v2_elite_fund_form
		<set>
			<if test="distributorId != null" >
				distributor_id = #{distributorId,jdbcType=BIGINT},
			</if>
			<if test="requestNo != null" >
				request_no = #{requestNo,jdbcType=VARCHAR},
			</if>
			<if test="fundType != null" >
				fund_type = #{fundType,jdbcType=VARCHAR},
			</if>
			<if test="applyProject != null" >
				apply_project = #{applyProject,jdbcType=VARCHAR},
			</if>
			<if test="applyAmount != null" >
				apply_amount = #{applyAmount,jdbcType=NUMERIC},
			</if>
			<if test="useFundAmount != null" >
				use_fund_amount = #{useFundAmount,jdbcType=NUMERIC},
			</if>
			<if test="useIviAmount != null" >
				use_ivi_amount = #{useIviAmount,jdbcType=NUMERIC},
			</if>
			<if test="useYearAmount != null" >
				use_year_amount = #{useYearAmount,jdbcType=NUMERIC},
			</if>
			<if test="totalInvoiceAmount != null" >
				total_invoice_amount = #{totalInvoiceAmount,jdbcType=NUMERIC},
			</if>
			<if test="finalAmount != null" >
				final_amount = #{finalAmount,jdbcType=NUMERIC},
			</if>
			<if test="actualAmount != null" >
				actual_amount = #{actualAmount,jdbcType=NUMERIC},
			</if>
			<if test="formAttrs != null" >
				form_attrs = #{formAttrs,jdbcType=VARCHAR},
			</if>
			<if test="useRemainInvoice != null" >
				use_remain_invoice = #{useRemainInvoice,jdbcType=NUMERIC},
			</if>
			<if test="unusedInvoice != null" >
				unused_invoice = #{unusedInvoice,jdbcType=NUMERIC},
			</if>
			<if test="unusedInvoiceInfo != null" >
				unused_invoice_info = #{unusedInvoiceInfo,jdbcType=VARCHAR},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="channelWeight != null" >
				channel_weight = #{channelWeight,jdbcType=INTEGER},
			</if>
			<if test="bu != null" >
				bu = #{bu,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null" >
				form_status = #{formStatus,jdbcType=INTEGER},
			</if>
			<if test="applyYear != null" >
				apply_year = #{applyYear,jdbcType=INTEGER},
			</if>
			<if test="applyPhase != null" >
				apply_phase = #{applyPhase,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="formVersion != null" >
				form_version = #{formVersion,jdbcType=BIGINT} + 1,
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="newExtFlag != null" >
				ext_flag = ext_flag | #{newExtFlag,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
			<if test="formVersion != null" >
				and form_version = #{formVersion,jdbcType=BIGINT}
			</if>
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormExample">
    	delete from wx_t_v2_elite_fund_form
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.elitefundv2.model.V2EliteFundForm" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_v2_elite_fund_form
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="requestNo != null">
				request_no,
			</if>
			<if test="fundType != null">
				fund_type,
			</if>
			<if test="applyProject != null">
				apply_project,
			</if>
			<if test="applyAmount != null">
				apply_amount,
			</if>
			<if test="useFundAmount != null">
				use_fund_amount,
			</if>
			<if test="useIviAmount != null">
				use_ivi_amount,
			</if>
			<if test="useYearAmount != null">
				use_year_amount,
			</if>
			<if test="totalInvoiceAmount != null">
				total_invoice_amount,
			</if>
			<if test="finalAmount != null">
				final_amount,
			</if>
			<if test="actualAmount != null">
				actual_amount,
			</if>
			<if test="formAttrs != null">
				form_attrs,
			</if>
			<if test="useRemainInvoice != null">
				use_remain_invoice,
			</if>
			<if test="unusedInvoice != null">
				unused_invoice,
			</if>
			<if test="unusedInvoiceInfo != null">
				unused_invoice_info,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="channelWeight != null">
				channel_weight,
			</if>
			<if test="bu != null">
				bu,
			</if>
			<if test="formStatus != null">
				form_status,
			</if>
			<if test="applyYear != null">
				apply_year,
			</if>
			<if test="applyPhase != null">
				apply_phase,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="formVersion != null">
				form_version,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
			<if test="requestNo != null">
				#{requestNo,jdbcType=VARCHAR},
			</if>
			<if test="fundType != null">
				#{fundType,jdbcType=VARCHAR},
			</if>
			<if test="applyProject != null">
				#{applyProject,jdbcType=VARCHAR},
			</if>
			<if test="applyAmount != null">
				#{applyAmount,jdbcType=NUMERIC},
			</if>
			<if test="useFundAmount != null">
				#{useFundAmount,jdbcType=NUMERIC},
			</if>
			<if test="useIviAmount != null">
				#{useIviAmount,jdbcType=NUMERIC},
			</if>
			<if test="useYearAmount != null">
				#{useYearAmount,jdbcType=NUMERIC},
			</if>
			<if test="totalInvoiceAmount != null">
				#{totalInvoiceAmount,jdbcType=NUMERIC},
			</if>
			<if test="finalAmount != null">
				#{finalAmount,jdbcType=NUMERIC},
			</if>
			<if test="actualAmount != null">
				#{actualAmount,jdbcType=NUMERIC},
			</if>
			<if test="formAttrs != null">
				#{formAttrs,jdbcType=VARCHAR},
			</if>
			<if test="useRemainInvoice != null">
				#{useRemainInvoice,jdbcType=NUMERIC},
			</if>
			<if test="unusedInvoice != null">
				#{unusedInvoice,jdbcType=NUMERIC},
			</if>
			<if test="unusedInvoiceInfo != null">
				#{unusedInvoiceInfo,jdbcType=VARCHAR},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="channelWeight != null">
				#{channelWeight,jdbcType=INTEGER},
			</if>
			<if test="bu != null">
				#{bu,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null">
				#{formStatus,jdbcType=INTEGER},
			</if>
			<if test="applyYear != null">
				#{applyYear,jdbcType=INTEGER},
			</if>
			<if test="applyPhase != null">
				#{applyPhase,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="formVersion != null">
				#{formVersion,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_v2_elite_fund_form
		<set>
			<if test="record.distributorId != null">
				distributor_id = #{record.distributorId,jdbcType=BIGINT},
			</if>
			<if test="record.requestNo != null">
				request_no = #{record.requestNo,jdbcType=VARCHAR},
			</if>
			<if test="record.fundType != null">
				fund_type = #{record.fundType,jdbcType=VARCHAR},
			</if>
			<if test="record.applyProject != null">
				apply_project = #{record.applyProject,jdbcType=VARCHAR},
			</if>
			<if test="record.applyAmount != null">
				apply_amount = #{record.applyAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.useFundAmount != null">
				use_fund_amount = #{record.useFundAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.useIviAmount != null">
				use_ivi_amount = #{record.useIviAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.useYearAmount != null">
				use_year_amount = #{record.useYearAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.totalInvoiceAmount != null">
				total_invoice_amount = #{record.totalInvoiceAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.finalAmount != null">
				final_amount = #{record.finalAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.actualAmount != null">
				actual_amount = #{record.actualAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.formAttrs != null">
				form_attrs = #{record.formAttrs,jdbcType=VARCHAR},
			</if>
			<if test="record.useRemainInvoice != null">
				use_remain_invoice = #{record.useRemainInvoice,jdbcType=NUMERIC},
			</if>
			<if test="record.unusedInvoice != null">
				unused_invoice = #{record.unusedInvoice,jdbcType=NUMERIC},
			</if>
			<if test="record.unusedInvoiceInfo != null">
				unused_invoice_info = #{record.unusedInvoiceInfo,jdbcType=VARCHAR},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="record.channelWeight != null">
				channel_weight = #{record.channelWeight,jdbcType=INTEGER},
			</if>
			<if test="record.bu != null">
				bu = #{record.bu,jdbcType=VARCHAR},
			</if>
			<if test="record.formStatus != null">
				form_status = #{record.formStatus,jdbcType=INTEGER},
			</if>
			<if test="record.applyYear != null">
				apply_year = #{record.applyYear,jdbcType=INTEGER},
			</if>
			<if test="record.applyPhase != null">
				apply_phase = #{record.applyPhase,jdbcType=VARCHAR},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.formVersion != null">
				form_version = #{record.formVersion,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormExample">
		delete from wx_t_v2_elite_fund_form
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormExample" resultType="int">
		select count(1) from wx_t_v2_elite_fund_form
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_v2_elite_fund_form
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_v2_elite_fund_form
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.unused_invoice_info, t1.ext_flag, t1.ext_property1, t1.ext_property2,
			 t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year,
			 t1.apply_phase, t1.delete_flag, t1.form_version, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time, o.organization_name distributor_name
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		 where t1.delete_flag=0
		 <choose>
		 	<when test="id != null">
		 	and t1.id = #{id, jdbcType=BIGINT}
		 	</when>
		 	<otherwise>
				<if test="distributorId != null">
					and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
				</if>
				<if test="requestNo != null and requestNo != ''">
					and t1.request_no like '%' + #{requestNo, jdbcType=VARCHAR} + '%'
				</if>
				<if test="fundType != null and fundType != ''">
					and t1.fund_type = #{fundType, jdbcType=VARCHAR}
				</if>
				<if test="bu != null and bu != ''">
					and t1.bu = #{bu, jdbcType=VARCHAR}
				</if>
				<if test="applyYear != null">
					and t1.apply_year = #{applyYear, jdbcType=INTEGER}
				</if>
				<if test="applyPhase != null and applyPhase != ''">
					and t1.apply_phase = #{applyPhase, jdbcType=VARCHAR}
				</if>
				<if test="formStatus != null">
					and t1.form_status = #{formStatus, jdbcType=INTEGER}
				</if>
		 	</otherwise>
		 </choose>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, o.organization_name distributor_name
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		 where t1.delete_flag=0
		 <include refid="sql_pageCondition"/>
	</select>
	<sql id="sql_pageCondition">
		<if test="distributorId != null">
			and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
		</if>
		<if test="requestNo != null and requestNo != ''">
			and t1.request_no like '%' + #{requestNo, jdbcType=VARCHAR} + '%'
		</if>
		<if test="fundType != null and fundType != ''">
			and t1.fund_type = #{fundType, jdbcType=VARCHAR}
		</if>
		<if test="applyProject != null and applyProject != ''">
			and t1.apply_project = #{applyProject, jdbcType=VARCHAR}
		</if>
		<if test="bu != null and bu != ''">
			and t1.bu = #{bu, jdbcType=VARCHAR}
		</if>
		<if test="applyYear != null">
			and t1.apply_year = #{applyYear, jdbcType=INTEGER}
		</if>
		<if test="applyPhase != null and applyPhase != ''">
			and t1.apply_phase = #{applyPhase, jdbcType=VARCHAR}
		</if>
		<if test="formStatus != null">
			and t1.form_status = #{formStatus, jdbcType=INTEGER}
		</if>
		<if test="fromFormStatus != null">
			and t1.form_status >= #{fromFormStatus, jdbcType=INTEGER}
		</if>
		<if test="endFormStatus != null">
			and t1.form_status &lt; #{endFormStatus, jdbcType=INTEGER}
		</if>
		<if test="requestYear != null">
			and year(t1.create_time) = #{requestYear, jdbcType=INTEGER}
		</if>
		<if test="queryField != null and queryField != ''">
			and ()
		</if>
	</sql>
	<select id="queryTodoForPage" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, o.organization_name distributor_name, 
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryTodoForPage"/>
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryTodoForPage"/>
		 where t1.delete_flag=0
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryTodoForPage"/>
		 <include refid="sql_pageCondition"/>
	</select>
	<select id="queryDoneForPage" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, o.organization_name distributor_name, 
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryDoneForPage"/>
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryDoneForPage"/>
		 where t1.delete_flag=0 
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryDoneForPage"/>
		 <include refid="sql_pageCondition"/>
	</select>
	<select id="queryAllForPage" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, o.organization_name distributor_name, 
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryAllForPage"/>
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryAllForPage"/>
		 where t1.delete_flag=0 
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryAllForPage"/>
		 <include refid="sql_pageCondition"/>
	</select>
	<select id="queryCheckForPage" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, o.organization_name distributor_name, 
			 case when t1.form_status=30 then 1 else 0 end check_reject_flag,
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryAllForPage"/>
		  from wx_t_v2_elite_fund_form t1
		  left join wx_t_partner_o2o_enterprise pe on t1.distributor_id=pe.distributor_id
		  left join wx_t_organization o on pe.partner_id=o.id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryAllForPage"/>
		 where t1.delete_flag=0 
				AND ((t1.form_status >= 30
				AND wf_wi1.status >= 10)
				 or exists (select 1 from wx_t_workflow_step_instance wsi001 
						left join wx_t_workflow_step_history wsh001 on wsh001.step_instance_id=wsi001.step_instance_id
						left join wx_t_workflow_step ws001 on ws001.step_id=wsi001.step_id
						where  wsi001.workflow_instance_id=wf_wi1.flow_instance_id and ws001.step_code='BSL_CHECK' and wsh001.history_id is not null))
		 <include refid="sql_pageCondition"/>
	</select>
	<select id="getWorkflowForm" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.distributor_id, t1.request_no, t1.fund_type, t1.apply_project, t1.apply_amount, t1.use_fund_amount,
			 t1.use_ivi_amount, t1.use_year_amount, t1.total_invoice_amount, t1.final_amount, t1.actual_amount, t1.form_attrs,
			 t1.use_remain_invoice, t1.unused_invoice, t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.channel_weight, t1.bu, t1.form_status, t1.apply_year, t1.apply_phase,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_getWorkflowForm"/>
		  from wx_t_v2_elite_fund_form t1
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_getWorkflowForm"/>
		 where t1.id=#{id,jdbcType=BIGINT}
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_getWorkflowForm"/>
	</select>
	<select id="queryCurrentBatchAppliedDistributor" resultMap="BaseResultMap" parameterType="com.chevron.elitefundv2.model.V2EliteFundFormParams">
	select row_number() OVER(ORDER BY a.distributor_id desc) id, max(o.organization_name) distributor_name, a.distributor_id, sum(a.channel_weight) channel_weight,
	#{applyYear, jdbcType=INTEGER} apply_year, #{applyPhase, jdbcType=VARCHAR} apply_phase
	 from (
	select distinct f.distributor_id, case when f.fund_type='consumer_mkt_fund' then 1 else 2 end channel_weight 
	from wx_t_v2_elite_fund_form f
	left join view_customer_region_sales_channel v on f.distributor_id=v.distributor_id and v.channel_weight&amp;(case when f.fund_type='consumer_mkt_fund' then 9 else 10 end)>0
	left join wx_t_user u on u.cai=v.sales_cai and u.status=1 
	where f.form_status >= 50 and f.delete_flag=0
			and f.apply_year = #{applyYear, jdbcType=INTEGER}
			and f.apply_phase = #{applyPhase, jdbcType=VARCHAR}
			and u.user_id=#{executor, jdbcType=BIGINT}
	) a
	left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=a.distributor_id
	left join wx_t_organization o on o.id=pe.partner_id
	group by a.distributor_id	
	</select>
	<select id="validateForm" resultType="int" parameterType="map">
	select case when (isnull((select sum(f.apply_amount) 
	from wx_t_v2_elite_fund_form f
	where f.distributor_id=#{distributorId, jdbcType=BIGINT} and f.form_status >= 10 and f.delete_flag=0 
	and f.fund_type=#{fundType} and f.apply_project in (
	<foreach collection="projects" index="index" item="item" separator=",">
	'${item}'
	</foreach>)
			and f.apply_year = #{applyYear, jdbcType=INTEGER}), 0) + #{applyAmount}) / (SELECT 
	<choose>
		<when test="fundType == 'consumer_mkt_fund'">
			SUM(isnull(-f.consumer_marketing_fund, 0) - isnull(f.consumer_marketing_fund_adjust, 0) - isnull(f.consumer_ivi_fund, 0) 
				- isnull(f.consumer_ivi_fund_adjust, 0) - isnull(f.consumer_annual_reward, 0)) 
		</when>
		<when test="fundType == 'commerical_mkt_fund'">
			SUM(isnull(-f.commercial_marketing_fund, 0) - isnull(f.commercial_marketing_fund_adjust, 0)  - isnull(f.commercial_ivi_fund, 0) 
				- isnull(f.commercial_ivi_fund_adjust, 0))
		</when>
	</choose>		
	FROM dw_mid_cal_elite_fund f
	WHERE f.year = #{applyYear, jdbcType=INTEGER}
		AND f.distributor_id = #{distributorId, jdbcType=BIGINT}) > 0.25 then 1 else 0 end
	</select>
	<select id="queryUnusedInvoice" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.unused_invoice, t1.unused_invoice_info, t1.form_version
		  from wx_t_v2_elite_fund_form t1
		 where t1.delete_flag=0 and t1.distributor_id=#{distributorId, jdbcType=BIGINT} and t1.form_status>=50
		 and t1.apply_year = #{applyYear, jdbcType=INTEGER} and t1.unused_invoice>0
		 and t1.fund_type = #{fundType, jdbcType=VARCHAR} and t1.apply_project=#{applyProject, jdbcType=VARCHAR}
	</select>
</mapper>
