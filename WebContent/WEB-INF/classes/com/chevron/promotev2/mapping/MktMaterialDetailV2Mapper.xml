<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.promotev2.dao.MktMaterialDetailV2Mapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.promotev2.model.MktMaterialDetailV2">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="resource_id" property="resourceId" jdbcType="BIGINT"/>
		<result column="package_code" property="packageCode" jdbcType="VARCHAR"/>
		<result column="photoId" property="photoid" jdbcType="BIGINT"/>
		<result column="material_name" property="materialName" jdbcType="VARCHAR"/>
		<result column="material_amount" property="materialAmount" jdbcType="INTEGER"/>
		<result column="material_price" property="materialPrice" jdbcType="NUMERIC"/>
		<result column="sort_numb" property="sortNumb" jdbcType="NUMERIC"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
	    <result column="material_total_price" property="materialTotalPrice" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,resource_id,package_code,photoId,material_name,material_amount,material_price,sort_numb,remark,create_user_id,
		create_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2">
		update wx_v2_mkt_material_detail set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2">
		update wx_v2_mkt_material_detail
		<set>
			<if test="resourceId != null" >
				resource_id = #{resourceId,jdbcType=BIGINT},
			</if>
			<if test="packageCode != null" >
				package_code = #{packageCode,jdbcType=VARCHAR},
			</if>
			<if test="photoid != null" >
				photoId = #{photoid,jdbcType=BIGINT},
			</if>
			<if test="materialName != null" >
				material_name = #{materialName,jdbcType=VARCHAR},
			</if>
			<if test="materialAmount != null" >
				material_amount = #{materialAmount,jdbcType=INTEGER},
			</if>
			<if test="materialPrice != null" >
				material_price = #{materialPrice,jdbcType=NUMERIC},
			</if>
			<if test="sortNumb != null" >
				sort_numb = #{sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2Example">
    	delete from wx_v2_mkt_material_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2">
		insert into wx_v2_mkt_material_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="resourceId != null">
				resource_id,
			</if>
			<if test="packageCode != null">
				package_code,
			</if>
			<if test="photoid != null">
				photoId,
			</if>
			<if test="materialName != null">
				material_name,
			</if>
			<if test="materialAmount != null">
				material_amount,
			</if>
			<if test="materialPrice != null">
				material_price,
			</if>
			<if test="sortNumb != null">
				sort_numb,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="resourceId != null">
				#{resourceId,jdbcType=BIGINT},
			</if>
			<if test="packageCode != null">
				#{packageCode,jdbcType=VARCHAR},
			</if>
			<if test="photoid != null">
				#{photoid,jdbcType=BIGINT},
			</if>
			<if test="materialName != null">
				#{materialName,jdbcType=VARCHAR},
			</if>
			<if test="materialAmount != null">
				#{materialAmount,jdbcType=INTEGER},
			</if>
			<if test="materialPrice != null">
				#{materialPrice,jdbcType=NUMERIC},
			</if>
			<if test="sortNumb != null">
				#{sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_v2_mkt_material_detail
		<set>
			<if test="record.resourceId != null">
				resource_id = #{record.resourceId,jdbcType=BIGINT},
			</if>
			<if test="record.packageCode != null">
				package_code = #{record.packageCode,jdbcType=VARCHAR},
			</if>
			<if test="record.photoid != null">
				photoId = #{record.photoid,jdbcType=BIGINT},
			</if>
			<if test="record.materialName != null">
				material_name = #{record.materialName,jdbcType=VARCHAR},
			</if>
			<if test="record.materialAmount != null">
				material_amount = #{record.materialAmount,jdbcType=INTEGER},
			</if>
			<if test="record.materialPrice != null">
				material_price = #{record.materialPrice,jdbcType=NUMERIC},
			</if>
			<if test="record.sortNumb != null">
				sort_numb = #{record.sortNumb,jdbcType=NUMERIC},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2Example">
		delete from wx_v2_mkt_material_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2Example" resultType="int">
		select count(1) from wx_v2_mkt_material_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2Example">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_v2_mkt_material_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.MktMaterialDetailV2Example">
    	select
		<include refid="Base_Column_List"/>
		from wx_v2_mkt_material_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.resource_id, t1.package_code, t1.photoId, t1.material_name, t1.material_amount, t1.material_price,
			 t1.sort_numb, t1.remark, t1.create_user_id, t1.create_time
		  from wx_v2_mkt_material_detail t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_v2_mkt_material_detail ( resource_id, package_code, photoId, material_name, material_amount, material_price, sort_numb, remark, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			 #{item.resourceId, jdbcType=BIGINT}, #{item.packageCode, jdbcType=VARCHAR}, #{item.photoid, jdbcType=BIGINT}, #{item.materialName, jdbcType=VARCHAR}, #{item.materialAmount, jdbcType=INTEGER}, #{item.materialPrice, jdbcType=NUMERIC}, #{item.sortNumb, jdbcType=NUMERIC}, #{item.remark, jdbcType=VARCHAR}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	
	<!-- 批量删除 -->
	<delete id="deleteByMap" parameterType="map">
	    delete from wx_v2_mkt_material_detail
	    where 1=1
	    <if test = "resourceId != null">
	        and resource_id = #{resourceId, jdbcType=BIGINT}
	    </if>
	</delete>
	
	<!-- 按照礼包类型统计礼包总金额 -->
	<select id="getPacksTotalAmount" resultMap="BaseResultMap" parameterType="map">
	    select v2_material_detail.package_code,sum(v2_material_detail.material_price) material_total_price from wx_v2_mkt_material_detail v2_material_detail
         left join wx_v2_mkt_resource v2_resource on v2_material_detail.resource_id=v2_resource.id 
		 where 1=1 and v2_resource.id = #{planId, jdbcType=BIGINT}
		 group by package_code
	</select>
</mapper>
