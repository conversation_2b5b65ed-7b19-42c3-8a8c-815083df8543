<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promotev2.dao.V2PromotePlanMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promotev2.model.V2PromotePlan" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="plan_batch_id" property="planBatchId" jdbcType="BIGINT" />
    <result column="region" property="region" jdbcType="VARCHAR" />
    <result column="last_annual_reference" property="lastAnnualReference" jdbcType="INTEGER" />
    <result column="seminar_packs_count" property="seminarPacksCount" jdbcType="INTEGER" />
    <result column="road_show_activities_materials_count" property="roadShowActivitiesMaterialsCount" jdbcType="INTEGER" />
    <result column="road_show_consumer_packs_count" property="roadShowConsumerPacksCount" jdbcType="INTEGER" />
    <result column="open_shop_packs_count" property="openShopPacksCount" jdbcType="INTEGER" />
    <result column="open_shop_gd_packs_count" property="openShopGdPacksCount" jdbcType="INTEGER" />
    <result column="integral_total_count" property="integralTotalCount" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="venue_meal" property="venueMeal" jdbcType="NUMERIC" />
    <result column="seminar_packs_high_count" property="seminarPacksHighCount" jdbcType="NUMERIC" />
    <result column="store_packs_count" property="storePacksCount" jdbcType="NUMERIC" />
    <result column="advert_packs_count" property="advertPacksCount" jdbcType="NUMERIC" />
    <result column="agriculture_packs_count" property="agriculturePacksCount" jdbcType="NUMERIC" />
    <result column="try_packs_count" property="tryPacksCount" jdbcType="NUMERIC" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, plan_batch_id, region, last_annual_reference, seminar_packs_count, road_show_activities_materials_count, 
    road_show_consumer_packs_count, open_shop_packs_count, open_shop_gd_packs_count, 
    integral_total_count, creator, create_time, remark,venue_meal, seminar_packs_high_count, store_packs_count,
    advert_packs_count, agriculture_packs_count, try_packs_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.V2PromotePlanExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_plan
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_plan
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promotev2.model.V2PromotePlanExample" >
    delete from wx_promote_plan
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promotev2.model.V2PromotePlan" >
    insert into wx_promote_plan (id, plan_batch_id, region, 
      last_annual_reference, seminar_packs_count, 
      road_show_activities_materials_count, road_show_consumer_packs_count, 
      open_shop_packs_count, open_shop_gd_packs_count, 
      integral_total_count, creator, create_time, 
      remark,venue_meal)
    values (#{id,jdbcType=BIGINT}, #{planBatchId,jdbcType=BIGINT}, #{region,jdbcType=VARCHAR}, 
      #{lastAnnualReference,jdbcType=INTEGER}, #{seminarPacksCount,jdbcType=INTEGER}, 
      #{roadShowActivitiesMaterialsCount,jdbcType=INTEGER}, #{roadShowConsumerPacksCount,jdbcType=INTEGER}, 
      #{openShopPacksCount,jdbcType=INTEGER}, #{openShopGdPacksCount,jdbcType=INTEGER}, 
      #{integralTotalCount,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR},#{venueMeal,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.promotev2.model.V2PromotePlan" >
    insert into wx_promote_plan
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="planBatchId != null" >
        plan_batch_id,
      </if>
      <if test="region != null" >
        region,
      </if>
      <if test="lastAnnualReference != null" >
        last_annual_reference,
      </if>
      <if test="seminarPacksCount != null" >
        seminar_packs_count,
      </if>
      <if test="roadShowActivitiesMaterialsCount != null" >
        road_show_activities_materials_count,
      </if>
      <if test="roadShowConsumerPacksCount != null" >
        road_show_consumer_packs_count,
      </if>
      <if test="openShopPacksCount != null" >
        open_shop_packs_count,
      </if>
      <if test="openShopGdPacksCount != null" >
        open_shop_gd_packs_count,
      </if>
      <if test="integralTotalCount != null" >
        integral_total_count,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="venueMeal != null" >
        venue_meal,
      </if>
      <if test="seminarPacksHighCount != null" >
        seminar_packs_high_count,
      </if>
      <if test="storePacksCount != null" >
        store_packs_count,
      </if>
      <if test="advertPacksCount != null" >
        advert_packs_count,
      </if>
      <if test="agriculturePacksCount != null" >
        agriculture_packs_count,
      </if>
      <if test="tryPacksCount != null" >
        try_packs_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="planBatchId != null" >
        #{planBatchId,jdbcType=BIGINT},
      </if>
      <if test="region != null" >
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="lastAnnualReference != null" >
        #{lastAnnualReference,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksCount != null" >
        #{seminarPacksCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActivitiesMaterialsCount != null" >
        #{roadShowActivitiesMaterialsCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksCount != null" >
        #{roadShowConsumerPacksCount,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksCount != null" >
        #{openShopPacksCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksCount != null" >
        #{openShopGdPacksCount,jdbcType=INTEGER},
      </if>
      <if test="integralTotalCount != null" >
        #{integralTotalCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="venueMeal != null" >
        #{venueMeal,jdbcType=NUMERIC},
      </if>
      <if test="seminarPacksHighCount != null" >
        #{seminarPacksHighCount,jdbcType=INTEGER},
      </if>
      <if test="storePacksCount != null" >
        #{storePacksCount,jdbcType=INTEGER},
      </if>
      <if test="advertPacksCount != null" >
        #{advertPacksCount,jdbcType=INTEGER},
      </if>
      <if test="agriculturePacksCount != null" >
        #{agriculturePacksCount,jdbcType=INTEGER},
      </if>
      <if test="tryPacksCount != null" >
        #{tryPacksCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_plan
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.planBatchId != null" >
        plan_batch_id = #{record.planBatchId,jdbcType=BIGINT},
      </if>
      <if test="record.region != null" >
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.lastAnnualReference != null" >
        last_annual_reference = #{record.lastAnnualReference,jdbcType=INTEGER},
      </if>
      <if test="record.seminarPacksCount != null" >
        seminar_packs_count = #{record.seminarPacksCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowActivitiesMaterialsCount != null" >
        road_show_activities_materials_count = #{record.roadShowActivitiesMaterialsCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowConsumerPacksCount != null" >
        road_show_consumer_packs_count = #{record.roadShowConsumerPacksCount,jdbcType=INTEGER},
      </if>
      <if test="record.openShopPacksCount != null" >
        open_shop_packs_count = #{record.openShopPacksCount,jdbcType=INTEGER},
      </if>
      <if test="record.openShopGdPacksCount != null" >
        open_shop_gd_packs_count = #{record.openShopGdPacksCount,jdbcType=INTEGER},
      </if>
      <if test="record.integralTotalCount != null" >
        integral_total_count = #{record.integralTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.venueMeal != null" >
        remark = #{record.venueMeal,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_plan
    set id = #{record.id,jdbcType=BIGINT},
      plan_batch_id = #{record.planBatchId,jdbcType=BIGINT},
      region = #{record.region,jdbcType=VARCHAR},
      last_annual_reference = #{record.lastAnnualReference,jdbcType=INTEGER},
      seminar_packs_count = #{record.seminarPacksCount,jdbcType=INTEGER},
      road_show_activities_materials_count = #{record.roadShowActivitiesMaterialsCount,jdbcType=INTEGER},
      road_show_consumer_packs_count = #{record.roadShowConsumerPacksCount,jdbcType=INTEGER},
      open_shop_packs_count = #{record.openShopPacksCount,jdbcType=INTEGER},
      open_shop_gd_packs_count = #{record.openShopGdPacksCount,jdbcType=INTEGER},
      integral_total_count = #{record.integralTotalCount,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      venue_meal = #{record.venueMeal,jdbcType=NUMERIC}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promotev2.model.V2PromotePlan" >
    update wx_promote_plan
    <set >
      <if test="planBatchId != null" >
        plan_batch_id = #{planBatchId,jdbcType=BIGINT},
      </if>
      <if test="region != null" >
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="lastAnnualReference != null" >
        last_annual_reference = #{lastAnnualReference,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksCount != null" >
        seminar_packs_count = #{seminarPacksCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActivitiesMaterialsCount != null" >
        road_show_activities_materials_count = #{roadShowActivitiesMaterialsCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksCount != null" >
        road_show_consumer_packs_count = #{roadShowConsumerPacksCount,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksCount != null" >
        open_shop_packs_count = #{openShopPacksCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksCount != null" >
        open_shop_gd_packs_count = #{openShopGdPacksCount,jdbcType=INTEGER},
      </if>
      <if test="integralTotalCount != null" >
        integral_total_count = #{integralTotalCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="venueMeal != null" >
        venue_meal = #{venueMeal,jdbcType=NUMERIC},
      </if>
      <if test="seminarPacksHighCount != null" >
        seminar_packs_high_count = #{seminarPacksHighCount,jdbcType=INTEGER},
      </if>
      <if test="storePacksCount != null" >
        store_packs_count = #{storePacksCount,jdbcType=INTEGER},
      </if>
      <if test="advertPacksCount != null" >
        advert_packs_count = #{advertPacksCount,jdbcType=INTEGER},
      </if>
      <if test="agriculturePacksCount != null" >
        agriculture_packs_count = #{agriculturePacksCount,jdbcType=INTEGER},
      </if>
      <if test="tryPacksCount != null" >
        try_packs_count = #{tryPacksCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promotev2.model.V2PromotePlan" >
    update wx_promote_plan
    set plan_batch_id = #{planBatchId,jdbcType=BIGINT},
      region = #{region,jdbcType=VARCHAR},
      last_annual_reference = #{lastAnnualReference,jdbcType=INTEGER},
      seminar_packs_count = #{seminarPacksCount,jdbcType=INTEGER},
      road_show_activities_materials_count = #{roadShowActivitiesMaterialsCount,jdbcType=INTEGER},
      road_show_consumer_packs_count = #{roadShowConsumerPacksCount,jdbcType=INTEGER},
      open_shop_packs_count = #{openShopPacksCount,jdbcType=INTEGER},
      open_shop_gd_packs_count = #{openShopGdPacksCount,jdbcType=INTEGER},
      integral_total_count = #{integralTotalCount,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      venue_meal = #{venueMeal,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <resultMap id="SalesInResultMap" type="com.chevron.promote.model.PromoteLastYearSale" >
      <result column="sales_liters" property="salesLiters" jdbcType="NUMERIC" />
      <result column="region" property="region" jdbcType="VARCHAR" />
  </resultMap>
  <select id="getLastYearSales" parameterType="map" resultMap="SalesInResultMap" >
   SELECT t_region_config.region_alias as  region,sum(dw_sell_in.liters) as sales_liters 
    FROM wx_t_business_region_config t_region_config
    LEFT JOIN dw_base_trans_sell_in dw_sell_in ON t_region_config.region_name = dw_sell_in.region_name
    WHERE
     1=1
    <if test="regionName!=null"> <!-- not use -->
     AND dw_sell_in.region_name LIKE  #{regionName}+'%' 
    </if>
    <if test="regionType!=null">
     AND t_region_config.business_name = #{regionType} 
    </if>
     AND t_region_config.business_name='REGION_PROMOTION'
    <![CDATA[and dw_sell_in.trans_time>= #{queryStartDate}]]> 
    <![CDATA[and dw_sell_in.trans_time< #{queryEndDate}]]> 
    GROUP BY t_region_config.region_alias,t_region_config.sales_channel
  </select>
  
  <insert id="insertBatchPlans" parameterType="java.util.List">
     insert into wx_promote_plan (plan_batch_id, region, 
      last_annual_reference, seminar_packs_count, 
      road_show_activities_materials_count, road_show_consumer_packs_count, 
      open_shop_packs_count, open_shop_gd_packs_count, 
      integral_total_count, creator, create_time, 
      remark,venue_meal,seminar_packs_high_count,store_packs_count,advert_packs_count,agriculture_packs_count,try_packs_count)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.planBatchId,jdbcType=BIGINT}, #{item.region,jdbcType=VARCHAR}, 
                #{item.lastAnnualReference,jdbcType=INTEGER}, #{item.seminarPacksCount,jdbcType=INTEGER}, 
                #{item.roadShowActivitiesMaterialsCount,jdbcType=INTEGER}, #{item.roadShowConsumerPacksCount,jdbcType=INTEGER}, 
                #{item.openShopPacksCount,jdbcType=INTEGER}, #{item.openShopGdPacksCount,jdbcType=INTEGER}, 
                #{item.integralTotalCount,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
                #{item.remark,jdbcType=VARCHAR},#{item.venueMeal,jdbcType=NUMERIC},#{item.seminarPacksHighCount,jdbcType=NUMERIC}
                ,#{item.storePacksCount,jdbcType=NUMERIC},#{item.advertPacksCount,jdbcType=NUMERIC}
                ,#{item.agriculturePacksCount,jdbcType=NUMERIC},#{item.tryPacksCount,jdbcType=NUMERIC}
            </trim>
        </foreach>
   </insert>
  <select id="getPromotePlanLstByBatchId" parameterType="java.lang.Long" resultMap="BaseResultMap" >
      select * from wx_promote_plan t_p_plan
      where t_p_plan.plan_batch_id = #{planBatchId} 
      ORDER BY t_p_plan.region desc
  </select>
  
  <select id="getPromotePlanByRegion" parameterType="map" resultMap="BaseResultMap" >
    SELECT t_plan.* FROM wx_promote_plan t_plan
	LEFT JOIN wx_promote_plan_batch t_plan_batch
	ON t_plan.plan_batch_id  = t_plan_batch.id
	WHERE t_plan.region = #{regionName}
	and  t_plan_batch.batch_status = 1
	<![CDATA[and  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), t_plan_batch.plan_start_time, 23)]]> 
    <![CDATA[and  CONVERT(varchar(100), #{nowdate}, 23) < CONVERT(varchar(100), t_plan_batch.plan_end_time, 23)]]>
    order by t_plan_batch.release_time desc
  </select>
  
   <delete id="deletePlanByBatchId" parameterType="long">
      delete from wx_promote_plan where plan_batch_id=#{planBatchId}
   </delete>
  
  
  
  
  
  <select id="getCustomerRegionChannelCai" parameterType="map" resultType="string" >
    SELECT distinct t_region_config.region_alias FROM 
	dw_region_sales_channel_rel dw_sales_channel
	LEFT JOIN wx_t_user tt_user ON tt_user.cai = dw_sales_channel.channel_manager_cai
	LEFT JOIN wx_t_business_region_config t_region_config ON t_region_config.region_name = dw_sales_channel.region_name AND t_region_config.business_name = 'REGION_PROMOTION'
	WHERE dw_sales_channel.sales_channel_name LIKE #{regionName}+'%' 
	AND tt_user.user_id = #{userId}
  </select>
  
  
  <select id="getCustomerRegionSupervisorCai" parameterType="map" resultType="string" >
    SELECT DISTINCT t_region_config.region_alias 
    FROM wx_t_user tt_user
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_sales ON dw_c_s_sales.suppervisor_cai = tt_user.cai
    LEFT JOIN wx_t_business_region_config t_region_config ON t_region_config.region_name = dw_c_s_sales.region_name AND t_region_config.business_name = 'REGION_PROMOTION'
    WHERE dw_c_s_sales.region_name LIKE #{regionName}+'%' 
    AND tt_user.user_id = #{userId}
  </select>
  
   <select id="getCustomerRegionSalesCai" parameterType="map" resultType="string" >
    SELECT DISTINCT t_region_config.region_alias 
    FROM wx_t_user tt_user
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_sales ON dw_c_s_sales.sales_cai = tt_user.cai
    LEFT JOIN wx_t_business_region_config t_region_config ON t_region_config.region_name = dw_c_s_sales.region_name AND t_region_config.business_name = 'REGION_PROMOTION'
    WHERE dw_c_s_sales.region_name LIKE  #{regionName}+'%' 
    AND tt_user.user_id =  #{userId}
  </select>
  
  <select id="getCustomerMarketingUser"  parameterType="map" resultType="java.lang.Long" >
      <!-- SELECT DISTINCT tt_user.user_id
	  FROM wx_t_user tt_user
	  LEFT JOIN wx_t_organization tt_org ON tt_org.id = tt_user.org_id
	  WHERE tt_org.parent_id = 2
	  AND tt_user.user_id = #{userId} -->
		SELECT DISTINCT  tt_user.user_id FROM wx_t_user tt_user 
		INNER  JOIN wx_t_userrole tt_u_role
		ON tt_user.user_id = tt_u_role.user_id
		INNER JOIN wx_t_role tt_role
		ON tt_role.role_id = tt_u_role.role_id
		WHERE tt_user.user_id  = #{userId}
		AND tt_role.ch_role_name = 'chevron_Marketing'
  </select>
  
  
  
   <resultMap id="CustomerRegionUserResultMap" type="com.chevron.promotev2.model.V2CustomerRegionUser" >
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="login_name" property="loginName" jdbcType="VARCHAR" />
    <result column="ch_name" property="chName" jdbcType="VARCHAR" />
    <result column="channel_region_name" property="channelRegionName" jdbcType="VARCHAR" />
    <result column="supervisor_region_name" property="supervisorRegionName" jdbcType="VARCHAR" />
    <result column="sales_region_name" property="salesRegionName" jdbcType="VARCHAR" />
    <result column="channel_manager_cai" property="channelManagerCai" jdbcType="VARCHAR" />
    <result column="suppervisor_cai" property="supervisorCai" jdbcType="VARCHAR" />
    <result column="sales_cai" property="salesCai" jdbcType="VARCHAR" />
    <result column="cai" property="cai" jdbcType="VARCHAR" />
    <result column="region_name" property="regionName" jdbcType="VARCHAR" />
    <result column="sales_region_en_name" property="salesRegionEnName" jdbcType="VARCHAR" />
  </resultMap>
  
  <select id="getCustomerRegionUserInfo" parameterType="map" resultMap="CustomerRegionUserResultMap" >
    SELECT top 1 tt_user.user_id,tt_user.login_name,tt_user.ch_name,tt_user.cai,tt_user.region_name,
   <!--  t_region_config.region_alias channel_region_name,
    t_region_config1.region_alias supervisor_region_name,
    t_region_config2.region_alias sales_region_name,
    t_region_config2.region_name sales_region_en_name, -->
    dw_sales_channel.channel_manager_cai,
    dw_c_s_supervisor.suppervisor_cai,
    dw_c_s_sales.sales_cai,
    dw_c_s_sales.region_name sales_region_en_name  
    FROM wx_t_user tt_user 
    LEFT JOIN dw_region_sales_channel_rel dw_sales_channel
    ON dw_sales_channel.channel_manager_cai = tt_user.cai
 <!--    LEFT JOIN wx_t_business_region_config t_region_config 
    ON t_region_config.region_name = dw_sales_channel.region_name AND t_region_config.business_name = #{regionType} -->
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_supervisor
    ON dw_c_s_supervisor.suppervisor_cai = tt_user.cai 
  <!--   LEFT JOIN wx_t_business_region_config t_region_config1
    ON  t_region_config1.region_name = dw_c_s_supervisor.region_name AND t_region_config1.business_name = #{regionType} -->
    LEFT JOIN dw_customer_region_sales_supervisor_rel dw_c_s_sales 
    ON dw_c_s_sales.sales_cai = tt_user.cai
    left JOIN wx_t_partner_responsible_main prm on prm.user_id = tt_user.user_id
	left join dw_customer_region_sales_supervisor_rel crss ON crss.sales_cai = prm.sales_cai	
	left JOIN dw_region_sales_channel_rel rsc ON crss.region_name= rsc.region_name
   <!--  LEFT JOIN wx_t_business_region_config t_region_config2
    ON  t_region_config2.region_name = dw_c_s_sales.region_name AND t_region_config2.business_name = #{regionType} -->
    WHERE
    tt_user.user_id = #{userId}
  </select>
  
  
  <select id="getCurrentPlans" parameterType="map" resultMap="BaseResultMap">
    SELECT t_plan.* FROM wx_promote_plan t_plan
	INNER  JOIN wx_promote_plan_batch t_plan_batch
	ON t_plan.plan_batch_id = t_plan_batch.id
	WHERE t_plan_batch.batch_status = 1
	<if test="sourcePlanId!=null">
	and t_plan.id = #{sourcePlanId}
	</if>
  </select>
  
  
</mapper>