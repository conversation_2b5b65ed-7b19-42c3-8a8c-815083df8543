<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promotev2.dao.V2PromoteActivityMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promotev2.model.V2PromoteActivity" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="source_plan_id" property="sourcePlanId" jdbcType="BIGINT" />
    <result column="source_distribution_id" property="sourceDistributionId" jdbcType="BIGINT" />
    <result column="activity_subject" property="activitySubject" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="activity_address" property="activityAddress" jdbcType="VARCHAR" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="activity_organizers" property="activityOrganizers" jdbcType="VARCHAR" />
    <result column="activity_organizers_id" property="activityOrganizersId" jdbcType="BIGINT" />
    <result column="activity_amount" property="activityAmount" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="activity_uuid" property="activityUUId" jdbcType="VARCHAR" />
    <result column="contact_address" property="contactAddress" jdbcType="VARCHAR" />
    <result column="contact_person" property="contactPerson" jdbcType="VARCHAR" />
    <result column="contact_tel" property="contactTel" jdbcType="VARCHAR" />
    <result column="focus_flag" property="focusFlag" jdbcType="VARCHAR" />
    <result column="work_flow_version_no" property="workFlowVersionNo" jdbcType="INTEGER" />
    <result column="activity_type_name" property="activityTypeName" jdbcType="VARCHAR" />
    <result column="submit_time" property="submitTime" jdbcType="TIMESTAMP" />
    <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
    
     <result column="sum_product_liters" property="sumProductLiters" jdbcType="NUMERIC" />
    <result column="sum_product_margins" property="sumProductMargins" jdbcType="NUMERIC" />
    <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    
    
    <result column="activity_main_type_name" property="activityMainTypeName" jdbcType="VARCHAR" />
    <result column="activity_sub_type_name" property="activitySubTypeName" jdbcType="VARCHAR" />
    
    <!-- 活动反馈相关 -->
    <result column="activity_img_ids" property="activityFeedBackImgIds" jdbcType="VARCHAR" />
    <result column="activity_info_feedback" property="activityFeedBackInfo" jdbcType="VARCHAR" />
    <result column="venue_meal" property="venueMeal" jdbcType="NUMERIC" />
    <result column="a14_apply_form" property="a14ApplyForm" jdbcType="VARCHAR" />
    <result column="compliance_invoice" property="complianceInvoice" jdbcType="VARCHAR" />
    <result column="conference_schedule" property="conferenceSchedule" jdbcType="VARCHAR" />
    <result column="check_in_slip" property="checkInSlip" jdbcType="VARCHAR" />
    <result column="conference_photo" property="conferencePhoto" jdbcType="VARCHAR" />
    <result column="hotel_fee_statement" property="hotelFeeStatement" jdbcType="VARCHAR" />
    <result column="tripartite_agreement" property="tripartiteAgreement" jdbcType="VARCHAR" />
    <result column="proof_purchase" property="proofPurchase" jdbcType="VARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    
   <association property="workflowInstance" column="form_key" resultMap="com.sys.workflow.dao.WorkflowInstanceMapper.BaseResultMap"/>
   
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, apply_batch_id, source_plan_id, source_distribution_id, activity_subject, activity_start_time, 
    activity_end_time, activity_address, activity_type, activity_organizers, activity_organizers_id, 
    activity_amount, create_time,creator,creator_id,status, remark,work_flow_version_no,submit_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.V2PromoteActivityExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_v2_promote_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_v2_promote_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_v2_promote_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promotev2.model.V2PromoteActivityExample" >
    delete from wx_v2_promote_activity
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promotev2.model.V2PromoteActivity" >
    insert into wx_v2_promote_activity (id, apply_batch_id, source_plan_id, 
      source_distribution_id, activity_subject, activity_start_time, 
      activity_end_time, activity_address, activity_type, 
      activity_organizers, activity_organizers_id, 
      activity_amount, create_time, release_time,creator,creator_id,status,remark,contact_address,contact_person,contact_tel,focus_flag,work_flow_version_no,submit_time
      )
    values (#{id,jdbcType=BIGINT}, #{applyBatchId,jdbcType=BIGINT}, #{sourcePlanId,jdbcType=BIGINT}, 
      #{sourceDistributionId,jdbcType=BIGINT}, #{activitySubject,jdbcType=VARCHAR}, #{activityStartTime,jdbcType=TIMESTAMP}, 
      #{activityEndTime,jdbcType=TIMESTAMP}, #{activityAddress,jdbcType=VARCHAR}, #{activityType,jdbcType=VARCHAR}, 
      #{activityOrganizers,jdbcType=VARCHAR}, #{activityOrganizersId,jdbcType=BIGINT}, 
      #{activityAmount,jdbcType=NUMERIC}, #{createTime,jdbcType=TIMESTAMP},#{releaseTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=VARCHAR},#{creatorId,jdbcType=BIGINT},#{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR} ,
      #{contactAddress,jdbcType=VARCHAR}, #{contactPerson,jdbcType=VARCHAR}, #{contactTel,jdbcType=VARCHAR}, #{focusFlag,jdbcType=VARCHAR},
      #{workFlowVersionNo,jdbcType=INTEGER}, #{submitTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.promotev2.model.V2PromoteActivity" >
    insert into wx_v2_promote_activity
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="applyBatchId != null" >
        apply_batch_id,
      </if>
      <if test="sourcePlanId != null" >
        source_plan_id,
      </if>
      <if test="sourceDistributionId != null" >
        source_distribution_id,
      </if>
      <if test="activitySubject != null" >
        activity_subject,
      </if>
      <if test="activityStartTime != null" >
        activity_start_time,
      </if>
      <if test="activityEndTime != null" >
        activity_end_time,
      </if>
      <if test="activityAddress != null" >
        activity_address,
      </if>
      <if test="activityType != null" >
        activity_type,
      </if>
      <if test="activityOrganizers != null" >
        activity_organizers,
      </if>
      <if test="activityOrganizersId != null" >
        activity_organizers_id,
      </if>
      <if test="activityAmount != null" >
        activity_amount,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
       <if test="creator != null" >
        creator,
      </if>
       <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="contactAddress != null" >
        contact_address,
      </if>
      <if test="contactPerson != null" >
        contact_person,
      </if>
      <if test="contactTel != null" >
        contact_tel,
      </if>
      <if test="focusFlag != null" >
        focus_flag,
      </if>
       <if test="workFlowVersionNo != null" >
        work_flow_version_no,
      </if>
       <if test="submitTime != null" >
        submit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applyBatchId != null" >
        #{applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="sourcePlanId != null" >
        #{sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="sourceDistributionId != null" >
        #{sourceDistributionId,jdbcType=BIGINT},
      </if>
      <if test="activitySubject != null" >
        #{activitySubject,jdbcType=VARCHAR},
      </if>
      <if test="activityStartTime != null" >
        #{activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityEndTime != null" >
        #{activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityAddress != null" >
        #{activityAddress,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null" >
        #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="activityOrganizers != null" >
        #{activityOrganizers,jdbcType=VARCHAR},
      </if>
      <if test="activityOrganizersId != null" >
        #{activityOrganizersId,jdbcType=BIGINT},
      </if>
      <if test="activityAmount != null" >
        #{activityAmount,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
       <if test="creatorId != null" >
        #{creatorId,jdbcType=BIGINT},
      </if>
       <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null" >
          #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactPerson != null" >
         #{contactPerson,jdbcType=VARCHAR},
      </if>
      <if test="contactTel != null" >
         #{contactTel,jdbcType=VARCHAR},
      </if>
      <if test="focusFlag != null" >
         #{focusFlag,jdbcType=VARCHAR},
      </if>
      <if test="workFlowVersionNo != null" >
         #{workFlowVersionNo,jdbcType=INTEGER},
      </if>
      <if test="submitTime != null" >
         #{submitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_v2_promote_activity
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyBatchId != null" >
        apply_batch_id = #{record.applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="record.sourcePlanId != null" >
        source_plan_id = #{record.sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="record.sourceDistributionId != null" >
        source_distribution_id = #{record.sourceDistributionId,jdbcType=BIGINT},
      </if>
      <if test="record.activitySubject != null" >
        activity_subject = #{record.activitySubject,jdbcType=VARCHAR},
      </if>
      <if test="record.activityStartTime != null" >
        activity_start_time = #{record.activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityEndTime != null" >
        activity_end_time = #{record.activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityAddress != null" >
        activity_address = #{record.activityAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.activityType != null" >
        activity_type = #{record.activityType,jdbcType=VARCHAR},
      </if>
      <if test="record.activityOrganizers != null" >
        activity_organizers = #{record.activityOrganizers,jdbcType=VARCHAR},
      </if>
      <if test="record.activityOrganizersId != null" >
        activity_organizers_id = #{record.activityOrganizersId,jdbcType=BIGINT},
      </if>
      <if test="record.activityAmount != null" >
        activity_amount = #{record.activityAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.releaseTime != null" >
        release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
       <if test="record.creatorId != null" >
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.contactAddress!= null" >
        contact_address = #{record.contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPerson != null" >
        contact_person = #{record.contactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.contactTel != null" >
        contact_tel  = #{record.contactTel,jdbcType=VARCHAR},
      </if>
      <if test="record.focusFlag != null" >
        focus_flag = #{focusFlag,jdbcType=VARCHAR},
      </if>
       <if test="record.workFlowVersionNo != null" >
        work_flow_version_no = #{workFlowVersionNo,jdbcType=INTEGER}
       </if>
       <if test="record.submitTime != null" >
        submit_time = #{submitTime,jdbcType=TIMESTAMP}
       </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_v2_promote_activity
    set id = #{record.id,jdbcType=BIGINT},
      apply_batch_id = #{record.applyBatchId,jdbcType=BIGINT},
      source_plan_id = #{record.sourcePlanId,jdbcType=BIGINT},
      source_distribution_id = #{record.sourceDistributionId,jdbcType=BIGINT},
      activity_subject = #{record.activitySubject,jdbcType=VARCHAR},
      activity_start_time = #{record.activityStartTime,jdbcType=TIMESTAMP},
      activity_end_time = #{record.activityEndTime,jdbcType=TIMESTAMP},
      activity_address = #{record.activityAddress,jdbcType=VARCHAR},
      activity_type = #{record.activityType,jdbcType=VARCHAR},
      activity_organizers = #{record.activityOrganizers,jdbcType=VARCHAR},
      activity_organizers_id = #{record.activityOrganizersId,jdbcType=BIGINT},
      activity_amount = #{record.activityAmount,jdbcType=NUMERIC},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      focus_flag = #{focusFlag,jdbcType=VARCHAR},
      work_flow_version_no = #{workFlowVersionNo,jdbcType=INTEGER},
      submit_time = #{submitTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promotev2.model.V2PromoteActivity" >
    update wx_v2_promote_activity
    <set >
      <if test="applyBatchId != null" >
        apply_batch_id = #{applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="sourcePlanId != null" >
        source_plan_id = #{sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="sourceDistributionId != null" >
        source_distribution_id = #{sourceDistributionId,jdbcType=BIGINT},
      </if>
      <if test="activitySubject != null" >
        activity_subject = #{activitySubject,jdbcType=VARCHAR},
      </if>
      <if test="activityStartTime != null" >
        activity_start_time = #{activityStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityEndTime != null" >
        activity_end_time = #{activityEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityAddress != null" >
        activity_address = #{activityAddress,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="activityOrganizers != null" >
        activity_organizers = #{activityOrganizers,jdbcType=VARCHAR},
      </if>
      <if test="activityOrganizersId != null" >
        activity_organizers_id = #{activityOrganizersId,jdbcType=BIGINT},
      </if>
      <if test="activityAmount != null" >
        activity_amount = #{activityAmount,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="activityUUId != null" >
        activity_uuid = #{activityUUId,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null" >
        contact_address = #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactPerson != null" >
        contact_person = #{contactPerson,jdbcType=VARCHAR},
      </if>
      <if test="contactTel != null" >
        contact_tel = #{contactTel,jdbcType=VARCHAR},
      </if>
      <if test="focusFlag != null" >
        focus_flag = #{focusFlag,jdbcType=VARCHAR},
      </if>
      <if test="workFlowVersionNo != null" >
        work_flow_version_no = #{workFlowVersionNo,jdbcType=INTEGER},
      </if>
      <if test="submitTime != null" >
        submit_time = #{submitTime,jdbcType=TIMESTAMP}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promotev2.model.V2PromoteActivity" >
    update wx_v2_promote_activity
    set apply_batch_id = #{applyBatchId,jdbcType=BIGINT},
      source_plan_id = #{sourcePlanId,jdbcType=BIGINT},
      source_distribution_id = #{sourceDistributionId,jdbcType=BIGINT},
      activity_subject = #{activitySubject,jdbcType=VARCHAR},
      activity_start_time = #{activityStartTime,jdbcType=TIMESTAMP},
      activity_end_time = #{activityEndTime,jdbcType=TIMESTAMP},
      activity_address = #{activityAddress,jdbcType=VARCHAR},
      activity_type = #{activityType,jdbcType=VARCHAR},
      activity_organizers = #{activityOrganizers,jdbcType=VARCHAR},
      activity_organizers_id = #{activityOrganizersId,jdbcType=BIGINT},
      activity_amount = #{activityAmount,jdbcType=NUMERIC},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      release_time = #{releaseTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      focus_flag = #{focusFlag,jdbcType=VARCHAR},
      work_flow_version_no = #{workFlowVersionNo,jdbcType=INTEGER},
      submit_time = #{submitTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <!-- 物料 -->
  <resultMap id="MaterialResultMap" type="com.chevron.material.model.WXTMaterialVo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="MATERIAL_CODE" jdbcType="NVARCHAR" property="materialCode" />
    <result column="SMC_MATERIAL_CODE" jdbcType="NVARCHAR" property="smcMaterialCode" />
    <result column="MATERIAL_TYPE" jdbcType="NVARCHAR" property="materialType" />
    <result column="MATERIAL_TYPE_NAME" property="materialTypeName" jdbcType="NVARCHAR" />
    <result column="MATERIAL_NAME" jdbcType="NVARCHAR" property="materialName" />
    <result column="MATERIAL_NAME_EN" jdbcType="NVARCHAR" property="materialNameEn" />
    <result column="MATERIAL_DESC" jdbcType="NVARCHAR" property="materialDesc" />
    <result column="SMC_MATERIAL_CATEGORY" jdbcType="NVARCHAR" property="smcMaterialCategory" />
    <result column="MATERIAL_SIZEL" jdbcType="INTEGER" property="materialSizel" />
    <result column="MATERIAL_SIZEW" jdbcType="INTEGER" property="materialSizew" />
    <result column="MATERIAL_SIZEH" jdbcType="INTEGER" property="materialSizeh" />
    <result column="MATERIAL_WEIGHT" jdbcType="NUMERIC" property="materialWeight" />
    <result column="MATERIAL_MAKE_PERIOD" jdbcType="INTEGER" property="materialMakePeriod" />
    <result column="MATERIAL_PRODUCTION_PERIOD" jdbcType="INTEGER" property="materialProductionPeriod" />
    <result column="MATERIAL_PICTURE" jdbcType="BIGINT" property="materialPicture" />
    <result column="MATERIAL_IMAGES" jdbcType="NVARCHAR" property="materialImages" />
    <result column="FIRST_MATERIAL_IMAGE" jdbcType="NVARCHAR" property="firstMaterialImage" />
    <result column="MATERIAL_PRICE" jdbcType="NUMERIC" property="materialPrice" />
    <result column="MATERIAL_UNIT" jdbcType="NVARCHAR" property="materialUnit" />
    <result column="MATERIAL_QTY" property="materialQty" jdbcType="NVARCHAR" />
    <result column="STOCK_QTY" property="stockQty" jdbcType="NVARCHAR" />
    <result column="VIRTUAL_STOCK_QTY" property="virtualStockQty" jdbcType="NVARCHAR" />
    <result column="CUST_EMAIL" jdbcType="NVARCHAR" property="custEmail" />
    <result column="SUPPLIER_EMAIL" jdbcType="NVARCHAR" property="supplierEmail" />
    <result column="SELLING_POINT" jdbcType="NVARCHAR" property="sellingPoint" />
    <result column="SELLING_POINT_EN" jdbcType="NVARCHAR" property="sellingPointEn" />
    <result column="STATE" jdbcType="NVARCHAR" property="state" />
    <result column="SMC_STATUS" jdbcType="INTEGER" property="smcStatus" />
    <result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
    <result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
    <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
    <result column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
    <result column="MATERIAL_SKU_PROP_COLOR" jdbcType="NVARCHAR" property="materialSkuPropColor" />
    <result column="MATERIAL_SKU_PROP_SIZE" jdbcType="NVARCHAR" property="materialSkuPropSize" />
    <result column="SMC_STATUS" property="smcStatus" jdbcType="INTEGER" />
    <result column="MATERIAL_SOURCE" jdbcType="NVARCHAR" property="materialSource" />
    <result column="MATERIAL_CATEGORY" jdbcType="NVARCHAR" property="materialCategory" />
    <result column="OVER_LIMIT" jdbcType="BIT" property="overLimit" />
    <result column="IS_LEFTOVER" jdbcType="BIT" property="isLeftover" />
    <result column="IS_PRESALE" jdbcType="BIT" property="isPresale" />
    <result column="SHOW_STOCK_QTY" jdbcType="BIT" property="showStockQty" />
    <result column="POINT_MODE" jdbcType="BIT" property="pointMode" />
    <result column="INVISIBLE_GROUP" jdbcType="NVARCHAR" property="invisibleGroup" />
    <result column="INVISIBLE_DEALER" jdbcType="NVARCHAR" property="invisibleDealer" />
    <result column="WAREHOUSE_ID" property="warehouseId" jdbcType="BIGINT" />
    <result column="WAREHOUSE_NAME" property="warehouseName" jdbcType="NVARCHAR" />
    <result column="POINT_TYPE" jdbcType="NVARCHAR" property="pointType" />
    <result column="MATERIAL_AFTER_SALES_INFO" jdbcType="NVARCHAR" property="materialAfterSalesInfo" />
    <result column="LIMIT_QTY_PER_ORDER" jdbcType="BIGINT" property="limitQtyPerOrder" />
    <result column="LIMIT_QTY_PER_YEAR" jdbcType="BIGINT" property="limitQtyPerYear" />
    <result column="ALLOC_NUM" jdbcType="BIGINT" property="allocNum" />
  </resultMap>
  
  <select id="getMaterialsPurchasePoints" parameterType="map" resultMap="MaterialResultMap" >
    select m.*,di.dic_item_name material_type_name,
    STOCK_QTY = (select sum(mi.STOCK_QTY) from wx_t_material_inventory mi where mi.MATERIAL_ID=m.ID),
    VIRTUAL_STOCK_QTY = (select sum(mi.VIRTUAL_STOCK_QTY) from wx_t_material_inventory mi where mi.MATERIAL_ID=m.ID),
    MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path(''))
    from wx_t_material m
    left join wx_t_dic_item di on di.dic_type_code = 'material.type' and di.dic_item_code = m.material_type
    where m.delete_flag=0
    AND m.POINT_TYPE = 'caltex'
    and m.point_mode = 1
    and m.smc_status = 1
    <![CDATA[and m.MATERIAL_PRICE< #{materialPrice}]]> 
  </select>
  
  <!-- 活动类型 -->
 <resultMap id="ActivityTypeResultMap" type="com.chevron.promote.model.PromoteActivityType">
    <result column="packs_en_type" jdbcType="NVARCHAR" property="typeCode" />
    <result column="packs_type" jdbcType="NVARCHAR" property="typeName" />
 </resultMap>
 <resultMap id="ActivityNameResultMap" type="com.chevron.promote.model.PromoteActivityName">
    <result column="packs_code" jdbcType="NVARCHAR" property="activityCode" />
    <result column="packs_name" jdbcType="NVARCHAR" property="activityName" />
 </resultMap>
 <select id="getActivityTypes" resultMap="ActivityTypeResultMap">
    SELECT DISTINCT  packs_en_type,packs_type  FROM wx_promote_packs_config t_p_packs_config
    where t_p_packs_config.status != '0'
 </select>
  <select id="getActivityNamesByType" parameterType="map"  resultMap="ActivityNameResultMap">
    SELECT DISTINCT t_p_packs_config.packs_code,t_p_packs_config.packs_name   
    FROM wx_promote_packs_config t_p_packs_config
    WHERE t_p_packs_config.packs_en_type = #{activityType}
 </select>
 
 <!-- 查看活动列表，根据批次 -->
 <select id="getActivityLstByBatchId" parameterType="long"  resultMap="BaseResultMap">
    SELECT DISTINCT t_promote_activity.*,
    t_promote_activity_type.dic_item_name activity_main_type_name
    FROM wx_v2_promote_activity t_promote_activity
    LEFT JOIN wx_t_dic_item t_promote_activity_type
    ON t_promote_activity.activity_type = t_promote_activity_type.dic_item_code and t_promote_activity_type.dic_type_code='MtkResourcePackage.packageType'
	WHERE t_promote_activity.apply_batch_id = #{applyBatchId}
 </select>


    <!-- 根据当前用户获取活动反馈列表 -->
  <select id="queryActivityFeedBack" parameterType="map" resultMap="BaseResultMap">
      SELECT 
        DISTINCT
        t_activity.id,
        t_activity.apply_batch_id,
        t_activity.activity_start_time,t_activity.activity_end_time,
        t_activity.activity_address,
        t_activity.create_time,t_activity.release_time,
        t_apply.batch_title activity_subject,
        t_activity.remark,
        t_activity.activity_type,
        t_activity.activity_organizers,
        t_activity.activity_organizers_id partner_id,
        t_activity.status,
        t_activity.contact_address,t_activity.contact_person,t_activity.contact_tel,
        
        t_a_feedback.activity_img_ids,
        t_a_feedback.activity_info_feedback,
        t_a_feedback.venue_meal,
        t_a_feedback.a14_apply_form,
        t_a_feedback.compliance_invoice,
        t_a_feedback.conference_schedule,
        t_a_feedback.check_in_slip,
        t_a_feedback.conference_photo,
        t_a_feedback.hotel_fee_statement,
        t_a_feedback.tripartite_agreement,
        t_a_feedback.proof_purchase
        
        FROM wx_v2_promote_activity t_activity
        Left JOIN wx_promote_activity_feedback t_a_feedback
        ON t_a_feedback.activity_id = t_activity.id
        LEFT JOIN wx_v2_promote_application_batch t_apply
        ON t_activity.apply_batch_id = t_apply.batchid
        LEFT JOIN wx_t_user tt_user
        ON tt_user.org_id = t_activity.activity_organizers_id
        INNER JOIN wx_promote_plan tt_plan
        ON tt_plan.id = t_activity.source_plan_id
        WHERE 
        1=1
        <if test="userId!=null">
        and tt_user.user_id = #{userId}
        </if>
        <if test="activityType!=null">
        and t_activity.activity_type != #{activityType}
        </if>
        <if test="regionName!=null">
        and tt_plan.region= #{regionName}
        </if>
        and t_activity.status in ('5','6', '7')
  </select>
  
  
  <select id="queryActivityFeedBackDetail" parameterType="long" resultMap="BaseResultMap">
      SELECT 
        DISTINCT
        t_activity.id,
        t_activity.apply_batch_id,
        t_activity.activity_start_time,t_activity.activity_end_time,
        t_activity.activity_address,
        t_activity.create_time,t_activity.release_time,
        t_apply.batch_title activity_subject,
        t_activity.remark,
        t_activity.activity_type,
        t_activity.activity_organizers,
        t_activity.activity_organizers_id partner_id,
        t_activity.status,
        
        t_a_feedback.activity_img_ids,
        t_a_feedback.activity_info_feedback,
        t_a_feedback.venue_meal,
        t_a_feedback.a14_apply_form,
        t_a_feedback.compliance_invoice,
        t_a_feedback.conference_schedule,
        t_a_feedback.check_in_slip,
        t_a_feedback.conference_photo,
        t_a_feedback.hotel_fee_statement,
        t_a_feedback.tripartite_agreement,
        t_a_feedback.proof_purchase
        
        FROM wx_v2_promote_activity t_activity
        Left JOIN wx_promote_activity_feedback t_a_feedback
        ON t_a_feedback.activity_id = t_activity.id
        LEFT JOIN wx_v2_promote_application_batch t_apply
        ON t_activity.apply_batch_id = t_apply.batchid
        LEFT JOIN wx_t_user tt_user
        ON tt_user.org_id = t_activity.activity_organizers_id
        INNER JOIN wx_promote_plan tt_plan
        ON tt_plan.id = t_activity.source_plan_id
        WHERE 
        1=1
        and t_activity.id = #{activityId}
        <if test="regionName!=null">
        and tt_plan.region= #{regionName}
        </if>
  </select>
  
  
  <update id="updateActivityByMap" parameterType="map">
        update wx_v2_promote_activity set  
        status = #{status},
        activity_uuid = #{activityUUId}  
        where apply_batch_id = #{applyBatchId}
  </update>
  
  <resultMap id="ResponseActivityInfoBaseResultMap" type="com.chevron.promote.model.ResponseActivityInfoMobile" >
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="activity_type_name" property="activityTypeName" jdbcType="VARCHAR" />
  </resultMap>
  <select id="getPromoteActivityInfoMobile" parameterType="string" resultMap="ResponseActivityInfoBaseResultMap">
		SELECT DISTINCT t_activity.id activity_id,t_activity.apply_batch_id,
		t_packs.packs_type activity_type_name,t_activity.activity_type  FROM wx_v2_promote_activity t_activity
		inner JOIN wx_promote_packs_config t_packs
		ON t_activity.activity_type = t_packs.packs_en_type
		WHERE t_activity.activity_uuid = #{activityUUId}
  </select>
  <resultMap id="PromotePackageCountMap" type="com.chevron.promote.model.vo.PromotePackageCountView" >
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="road_show_gif_package_count_request" property="roadShowGifPackageCountRequest" jdbcType="BIGINT" />
    <result column="road_show_consumer_packs_count_request" property="roadShowConsumerPacksCountRequest" jdbcType="BIGINT" />
    <result column="apply_high_packs_request" property="seminarApplyHighPacksRequest" jdbcType="BIGINT" />
    <result column="apply_packs_count_request" property="seminarApplyPacksCountRequest" jdbcType="BIGINT" />
    <result column="packs_number_request" property="xdPacksNumberRequest" jdbcType="BIGINT" />
    <result column="gd_packs_number_request" property="xdGdPacksNumberRequest" jdbcType="BIGINT" />
    
    <result column="road_show_gif_package_count_confirm" property="roadShowGifPackageCountConfirm" jdbcType="BIGINT" />
    <result column="road_show_consumer_packs_count_confirm" property="roadShowConsumerPacksCountConfirm" jdbcType="BIGINT" />
    <result column="apply_high_packs_confirm" property="seminarApplyHighPacksConfirm" jdbcType="BIGINT" />
    <result column="apply_packs_count_confirm" property="seminarApplyPacksCountConfirm" jdbcType="BIGINT" />
    <result column="packs_number_confirm" property="xdPacksNumberConfirm" jdbcType="BIGINT" />
    <result column="gd_packs_number_confirm" property="xdGdPacksNumberConfirm" jdbcType="BIGINT" />
    
  </resultMap>
  <select id="getPromotePackageCount" parameterType="map" resultMap="PromotePackageCountMap">
		SELECT org.id as partner_id,
			promote_request.road_show_gif_package_count_request,
			promote_request.road_show_consumer_packs_count_request,
			promote_request.apply_high_packs_request,
			promote_request.apply_packs_count_request,
			promote_request.packs_number_request,
			promote_request.gd_packs_number_request,
			promote_confirm.road_show_gif_package_count_confirm,
			promote_confirm.road_show_consumer_packs_count_confirm,
			promote_confirm.apply_high_packs_confirm,
			promote_confirm.apply_packs_count_confirm,
			promote_confirm.packs_number_confirm,
			promote_confirm.gd_packs_number_confirm
		 FROM wx_t_organization org
		LEFT JOIN (
			SELECT pa.activity_organizers_id AS partner_id, sum(pr.road_show_gif_package_count) AS road_show_gif_package_count_request ,
			sum(pr.road_show_consumer_packs_count) AS road_show_consumer_packs_count_request,
			sum(ps.apply_high_packs) AS apply_high_packs_request ,
			sum(ps.apply_packs_count) AS apply_packs_count_request,
			sum(px.packs_number) AS packs_number_request ,
			sum(px.gd_packs_number) AS gd_packs_number_request FROM wx_v2_promote_activity pa
			LEFT JOIN wx_v2_promote_roadshow_activity_detail pr ON pr.activity_id = pa.id
			LEFT JOIN wx_v2_promote_seminar_activity_detail ps ON ps.activity_id = pa.id
			LEFT JOIN wx_v2_promote_xd_open_detail px ON px.activity_id = px.id
			WHERE year(pa.activity_start_time) = #{year} and year(pa.activity_end_time) = #{year}
				and status IN ('1','2') GROUP BY pa.activity_organizers_id
		) promote_request ON promote_request.partner_id = org.id
		LEFT JOIN (
			SELECT pa.activity_organizers_id AS partner_id, sum(pr.road_show_gif_package_count) AS road_show_gif_package_count_confirm ,
			sum(pr.road_show_consumer_packs_count) AS road_show_consumer_packs_count_confirm,
			sum(ps.apply_high_packs) AS apply_high_packs_confirm ,
			sum(ps.apply_packs_count) AS apply_packs_count_confirm,
			sum(px.packs_number) AS packs_number_confirm ,
			sum(px.gd_packs_number) AS gd_packs_number_confirm FROM wx_v2_promote_activity pa
			LEFT JOIN wx_v2_promote_roadshow_activity_detail pr ON pr.activity_id = pa.id
			LEFT JOIN wx_v2_promote_seminar_activity_detail ps ON ps.activity_id = pa.id
			LEFT JOIN wx_v2_promote_xd_open_detail px ON px.activity_id = px.id
			WHERE year(pa.activity_start_time) = #{year} and year(pa.activity_end_time) = #{year}
				and status IN ('3','4','5','6') GROUP BY pa.activity_organizers_id 
		) promote_confirm ON promote_confirm.partner_id = org.id
			where org.id = #{partnerId} and org.type = '1'
  </select>
  
  
<!--   新流程数据查询
 -->  
 
  <select id="getPromoteActivityDraft" parameterType="map" resultMap="BaseResultMap">
     select t_activity.*,
        t_pack_config.dic_item_name activity_type_name
		FROM wx_v2_promote_activity t_activity
		INNER JOIN wx_t_dic_item t_pack_config ON t_activity.activity_type = t_pack_config.dic_item_code and t_pack_config.dic_type_code='MtkResourcePackage.packageType'
        INNER JOIN wx_v2_mkt_resource t_plan
        ON t_plan.id = t_activity.source_plan_id
        <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryDraftForPage"/>
     where 1=1
        <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryDraftForPage"/>
     <if test="queryFiled!=null and queryFiled !=''">
       AND t_activity.activity_organizers LIKE  '%'+#{queryFiled}+'%'
      </if>
      order by t_activity.create_time desc
  </select>
   <select id="getActivityApplyInapproval" parameterType="map" resultMap="BaseResultMap">
    SELECT
        t_activity.*,
         t_pack_config.dic_item_name activity_type_name,t_partner.distributor_id,
      <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryTodoForPage"/>
      FROM wx_v2_promote_activity t_activity
      INNER  JOIN wx_t_dic_item t_pack_config ON t_activity.activity_type = t_pack_config.dic_item_code and t_pack_config.dic_type_code='MtkResourcePackage.packageType'
      left join wx_t_partner_o2o_enterprise t_partner on t_activity.activity_organizers_id=t_partner.partner_id
      <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryTodoForPage"/>
      WHERE
       1=1
      <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryTodoForPage"/>
      <if test="queryFiled!=null and queryFiled !=''">
       AND t_activity.activity_organizers LIKE  '%'+#{queryFiled}+'%'
      </if>
       <if test='startDate != null and startDate != ""'>
            AND t_activity.create_time &gt;=#{startDate, jdbcType=NVARCHAR}
      </if>
      <if test='endDate != null and endDate != ""'>
            AND t_activity.create_time &lt;=#{endDate, jdbcType=NVARCHAR}
       </if>
      order by t_activity.create_time desc
  </select>
  
    <select id="getActivityApplyApproval" parameterType="map" resultMap="BaseResultMap">
    SELECT
       t_activity.*,
        t_pack_config.dic_item_name activity_type_name,t_partner.distributor_id,
	  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryDoneForPage"/>
      FROM wx_v2_promote_activity t_activity
      INNER  JOIN wx_t_dic_item t_pack_config ON t_activity.activity_type = t_pack_config.dic_item_code and t_pack_config.dic_type_code='MtkResourcePackage.packageType'
	  left join wx_t_partner_o2o_enterprise t_partner on t_activity.activity_organizers_id=t_partner.partner_id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryDoneForPage"/>
      WHERE
       1=1
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryDoneForPage"/>
	  <!-- 状态为15的为撤回 -->
     <if test="queryFiled!=null and queryFiled !=''">
       AND t_activity.activity_organizers LIKE  '%'+#{queryFiled}+'%'
      </if>
       <if test='startDate != null and startDate != ""'>
            AND t_activity.create_time &gt;=#{startDate, jdbcType=NVARCHAR}
      </if>
      <if test='endDate != null and endDate != ""'>
            AND t_activity.create_time &lt;=#{endDate, jdbcType=NVARCHAR}
       </if>
      order by t_activity.create_time desc
  </select>
  
   <select id="getAllActivityApplyList" parameterType="com.chevron.promotev2.model.V2PromoteActivityParams" resultMap="BaseResultMap">
    SELECT
       t_activity.*,
        t_pack_config.dic_item_name activity_type_name,t_partner.distributor_id,
	  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryAllForPage"/>
      FROM wx_v2_promote_activity t_activity
      INNER  JOIN wx_t_dic_item t_pack_config ON t_activity.activity_type = t_pack_config.dic_item_code and t_pack_config.dic_type_code='MtkResourcePackage.packageType'
	  left join wx_t_partner_o2o_enterprise t_partner on t_activity.activity_organizers_id=t_partner.partner_id
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryAllForPage"/>
      WHERE 1=1
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryAllForPage" />
     <if test="queryFiled!=null and queryFiled !=''">
       AND t_activity.activity_organizers LIKE  '%'+#{queryFiled}+'%'
      </if>
      <if test='dateFrom != null and dateFrom != ""'>
            AND t_activity.create_time &gt;=#{dateFrom, jdbcType=NVARCHAR}
      </if>
      <if test='dateTo != null and dateTo != ""'>
            AND t_activity.create_time &lt;=#{dateTo, jdbcType=NVARCHAR}
       </if>
  </select>
  
  <select id="getPartnerIdByDistributorId" resultType="java.lang.Long">
		select partner_id from wx_t_partner_o2o_enterprise where distributor_id = #{distributorId}
	</select>
	
	<select id="getProductLitersAndMargins" resultType="map" resultMap="BaseResultMap">
     select ISNULL(SUM(t.liters),0) AS sum_product_liters,ISNULL(SUM(t.margin_rmb),0) AS sum_product_margins from PP_MID.dbo.syn_dw_to_pp_sap_sell_in t where 1=1 
      <if test='distributorId != null'>
            and t.distributor_id=#{distributorId}
      </if>
      <if test='startDate != null'>
             AND t.create_time &gt;=#{startDate}
      </if>
      <if test='endDate != null'>
             AND t.create_time &lt;#{endDate}
      </if>
      <if test="productSapCode !=null">
          and t.product_code_SAP in 
       <foreach item="code" index="index" collection="productSapCode" open="(" separator="," close=")">  
             #{code}  
        </foreach> 
      </if>
	</select>
	
	<select id="getProductCode" resultType="map" resultMap="BaseResultMap">
     select distinct t.product_code_SAP as product_code from PP_MID.dbo.syn_dw_to_pp_sap_sell_in t where 1=1 
      <if test='distributorId != null'>
            and t.distributor_id=#{distributorId}
      </if>
      <if test='startDate != null'>
             AND t.create_time &gt;=#{startDate}
      </if>
      <if test='endDate != null'>
             AND t.create_time &lt;#{endDate}
      </if>
	</select>
	<select id="getDistributorIdByPartnerId" resultType="java.lang.Long">
		select distributor_id from wx_t_partner_o2o_enterprise where partner_id = #{partnerId}
	</select>
</mapper>