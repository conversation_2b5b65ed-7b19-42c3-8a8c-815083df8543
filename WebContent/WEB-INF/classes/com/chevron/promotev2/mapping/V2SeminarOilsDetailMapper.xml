<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.promotev2.dao.V2SeminarOilsDetailMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.promotev2.model.V2SeminarOilsDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="seminar_id" property="seminarId" jdbcType="BIGINT"/>
		<result column="applicant" property="applicant" jdbcType="VARCHAR"/>
		<result column="applicant_date" property="applicantDate" jdbcType="TIMESTAMP"/>
		<result column="department" property="department" jdbcType="VARCHAR"/>
		<result column="warehouse" property="warehouse" jdbcType="VARCHAR"/>
		<result column="glNo" property="glno" jdbcType="VARCHAR"/>
		<result column="customerId" property="customerid" jdbcType="BIGINT"/>
		<result column="sap_code" property="customerSapCode" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
		<result column="responsible_csr" property="responsibleCsr" jdbcType="VARCHAR"/>
		<result column="ship_to_code" property="shipToCode" jdbcType="VARCHAR"/>
		<result column="delivery_dress" property="deliveryDress" jdbcType="VARCHAR"/>
		<result column="yz_product_quantity" property="yzProductQuantity" jdbcType="NUMERIC"/>
		<result column="pt_product_quantity" property="ptProductQuantity" jdbcType="NUMERIC"/>
		<result column="applay_oil_reason" property="applayOilReason" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
		<result column="customer_sap_code" property="customerSapCode" jdbcType="VARCHAR"/>
		<result column="cost_center" property="costCenter" jdbcType="VARCHAR"/>
		<result column="yz_product_code" property="yzProductCode" jdbcType="VARCHAR"/>
		<result column="pt_product_code" property="ptProductCode" jdbcType="VARCHAR"/>
		<result column="yz_product_name" property="yzProductName" jdbcType="VARCHAR"/>
		<result column="pt_product_name" property="ptProductName" jdbcType="VARCHAR"/>
		<result column="yz_product_package" property="yzProductPackage" jdbcType="NUMERIC"/>
		<result column="pt_product_package" property="ptProductPackage" jdbcType="NUMERIC"/>
		<result column="yz_product_cost" property="yzProductCost" jdbcType="NUMERIC"/>
		<result column="pt_product_cost" property="ptProductCost" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,seminar_id,applicant,applicant_date,department,warehouse,glNo,customerId,type,responsible_csr,ship_to_code,
		delivery_dress,yz_product_quantity,pt_product_quantity,applay_oil_reason,create_user_id,create_time,update_user_id,
		update_time,delete_flag,customer_sap_code,cost_center,yz_product_code,pt_product_code,yz_product_name,
		pt_product_name,yz_product_package,pt_product_package,yz_product_cost,pt_product_cost
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetail">
		update wx_v2_seminar_oils_detail set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetail">
		update wx_v2_seminar_oils_detail
		<set>
			<if test="seminarId != null" >
				seminar_id = #{seminarId,jdbcType=BIGINT},
			</if>
			<if test="applicant != null" >
				applicant = #{applicant,jdbcType=VARCHAR},
			</if>
			<if test="applicantDate != null" >
				applicant_date = #{applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="department != null" >
				department = #{department,jdbcType=VARCHAR},
			</if>
			<if test="warehouse != null" >
				warehouse = #{warehouse,jdbcType=VARCHAR},
			</if>
			<if test="glno != null" >
				glNo = #{glno,jdbcType=VARCHAR},
			</if>
			<if test="customerid != null" >
				customerId = #{customerid,jdbcType=BIGINT},
			</if>
			<if test="type != null" >
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="responsibleCsr != null" >
				responsible_csr = #{responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="shipToCode != null" >
				ship_to_code = #{shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDress != null" >
				delivery_dress = #{deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="yzProductQuantity != null" >
				yz_product_quantity = #{yzProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="ptProductQuantity != null" >
				pt_product_quantity = #{ptProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="applayOilReason != null" >
				applay_oil_reason = #{applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="customerSapCode != null" >
				customer_sap_code = #{customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="costCenter != null" >
				cost_center = #{costCenter,jdbcType=VARCHAR},
			</if>
			<if test="yzProductCode != null" >
				yz_product_code = #{yzProductCode,jdbcType=VARCHAR},
			</if>
			<if test="ptProductCode != null" >
				pt_product_code = #{ptProductCode,jdbcType=VARCHAR},
			</if>
			<if test="yzProductName != null" >
				yz_product_name = #{yzProductName,jdbcType=VARCHAR},
			</if>
			<if test="ptProductName != null" >
				pt_product_name = #{ptProductName,jdbcType=VARCHAR},
			</if>
			<if test="yzProductPackage != null" >
				yz_product_package = #{yzProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="ptProductPackage != null" >
				pt_product_package = #{ptProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="yzProductCost != null" >
				yz_product_cost = #{yzProductCost,jdbcType=NUMERIC},
			</if>
			<if test="ptProductCost != null" >
				pt_product_cost = #{ptProductCost,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetailExample">
    	delete from wx_v2_seminar_oils_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetail" useGeneratedKeys="true" keyProperty="id">
		insert into wx_v2_seminar_oils_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="seminarId != null">
				seminar_id,
			</if>
			<if test="applicant != null">
				applicant,
			</if>
			<if test="applicantDate != null">
				applicant_date,
			</if>
			<if test="department != null">
				department,
			</if>
			<if test="warehouse != null">
				warehouse,
			</if>
			<if test="glno != null">
				glNo,
			</if>
			<if test="customerid != null">
				customerId,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="responsibleCsr != null">
				responsible_csr,
			</if>
			<if test="shipToCode != null">
				ship_to_code,
			</if>
			<if test="deliveryDress != null">
				delivery_dress,
			</if>
			<if test="yzProductQuantity != null">
				yz_product_quantity,
			</if>
			<if test="ptProductQuantity != null">
				pt_product_quantity,
			</if>
			<if test="applayOilReason != null">
				applay_oil_reason,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="customerSapCode != null">
				customer_sap_code,
			</if>
			<if test="costCenter != null">
				cost_center,
			</if>
				<if test="yzProductCode != null">
				yz_product_code,
			</if>
			<if test="ptProductCode != null">
				pt_product_code,
			</if>
			<if test="yzProductName != null">
				yz_product_name,
			</if>
			<if test="ptProductName != null">
				pt_product_name,
			</if>
			<if test="yzProductPackage != null">
				yz_product_package,
			</if>
			<if test="ptProductPackage != null">
				pt_product_package,
			</if>
			<if test="yzProductCost != null">
				yz_product_cost,
			</if>
			<if test="ptProductCost != null">
				pt_product_cost,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="seminarId != null">
				#{seminarId,jdbcType=BIGINT},
			</if>
			<if test="applicant != null">
				#{applicant,jdbcType=VARCHAR},
			</if>
			<if test="applicantDate != null">
				#{applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="department != null">
				#{department,jdbcType=VARCHAR},
			</if>
			<if test="warehouse != null">
				#{warehouse,jdbcType=VARCHAR},
			</if>
			<if test="glno != null">
				#{glno,jdbcType=VARCHAR},
			</if>
			<if test="customerid != null">
				#{customerid,jdbcType=BIGINT},
			</if>
			<if test="type != null">
				#{type,jdbcType=VARCHAR},
			</if>
			<if test="responsibleCsr != null">
				#{responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="shipToCode != null">
				#{shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDress != null">
				#{deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="yzProductQuantity != null">
				#{yzProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="ptProductQuantity != null">
				#{ptProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="applayOilReason != null">
				#{applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="customerSapCode != null">
				#{customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="costCenter != null">
				#{costCenter,jdbcType=VARCHAR},
			</if>
			<if test="yzProductCode != null">
				#{yzProductCode,jdbcType=VARCHAR},
			</if>
			<if test="ptProductCode != null">
				#{ptProductCode,jdbcType=VARCHAR},
			</if>
			<if test="yzProductName != null">
				#{yzProductName,jdbcType=VARCHAR},
			</if>
			<if test="ptProductName != null">
				#{ptProductName,jdbcType=VARCHAR},
			</if>
			<if test="yzProductPackage != null">
				#{yzProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="ptProductPackage != null">
				#{ptProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="yzProductCost != null">
				#{yzProductCost,jdbcType=NUMERIC},
			</if>
			<if test="ptProductCost != null">
				#{ptProductCost,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_v2_seminar_oils_detail
		<set>
			<if test="record.seminarId != null">
				seminar_id = #{record.seminarId,jdbcType=BIGINT},
			</if>
			<if test="record.applicant != null">
				applicant = #{record.applicant,jdbcType=VARCHAR},
			</if>
			<if test="record.applicantDate != null">
				applicant_date = #{record.applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="record.department != null">
				department = #{record.department,jdbcType=VARCHAR},
			</if>
			<if test="record.warehouse != null">
				warehouse = #{record.warehouse,jdbcType=VARCHAR},
			</if>
			<if test="record.glno != null">
				glNo = #{record.glno,jdbcType=VARCHAR},
			</if>
			<if test="record.customerid != null">
				customerId = #{record.customerid,jdbcType=BIGINT},
			</if>
			<if test="record.type != null">
				type = #{record.type,jdbcType=VARCHAR},
			</if>
			<if test="record.responsibleCsr != null">
				responsible_csr = #{record.responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="record.shipToCode != null">
				ship_to_code = #{record.shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="record.deliveryDress != null">
				delivery_dress = #{record.deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="record.yzProductQuantity != null">
				yz_product_quantity = #{record.yzProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="record.ptProductQuantity != null">
				pt_product_quantity = #{record.ptProductQuantity,jdbcType=NUMERIC},
			</if>
			<if test="record.applayOilReason != null">
				applay_oil_reason = #{record.applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.customerSapCode != null">
				customer_sap_code = #{record.customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="record.costCenter != null">
				cost_center = #{record.costCenter,jdbcType=VARCHAR},
			</if>
			<if test="record.yzProductCode != null">
				yz_product_code = #{record.yzProductCode,jdbcType=VARCHAR},
			</if>
			<if test="record.ptProductCode != null">
				pt_product_code = #{record.ptProductCode,jdbcType=VARCHAR},
			</if>
			<if test="record.yzProductName != null">
				yz_product_name = #{record.yzProductName,jdbcType=VARCHAR},
			</if>
			<if test="record.ptProductName != null">
				pt_product_name = #{record.ptProductName,jdbcType=VARCHAR},
			</if>
			<if test="record.yzProductPackage != null">
				yz_product_package = #{record.yzProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="record.ptProductPackage != null">
				pt_product_package = #{record.ptProductPackage,jdbcType=NUMERIC},
			</if>
			<if test="record.yzProductCost != null">
				yz_product_cost = #{record.yzProductCost,jdbcType=NUMERIC},
			</if>
			<if test="record.ptProductCost != null">
				pt_product_cost = #{record.ptProductCost,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetailExample">
		delete from wx_v2_seminar_oils_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetailExample" resultType="int">
		select count(1) from wx_v2_seminar_oils_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_v2_seminar_oils_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.promotev2.model.V2SeminarOilsDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_v2_seminar_oils_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
			SELECT
				t1.id,
				t1.seminar_id,
				t1.applicant,
				t1.applicant_date,
				t1.department,
				t1.warehouse,
				t1.glNo,
				t1.customerId,
				t1.type,
				t1.responsible_csr,
				t1.ship_to_code,
				t1.delivery_dress,
				t1.yz_product_quantity,
				t1.pt_product_quantity,
				t1.applay_oil_reason,
				t1.create_user_id,
				t1.create_time,
				t1.update_user_id,
				t1.update_time,
				t1.delete_flag,
				t2.organization_name customerName, 
				t1.customer_sap_code,
				t1.cost_center,
				t1.yz_product_code, t1.pt_product_code, t1.yz_product_name,
			    t1.pt_product_name, t1.yz_product_package, t1.pt_product_package, t1.yz_product_cost, t1.pt_product_cost
			FROM
				wx_v2_seminar_oils_detail t1
				LEFT JOIN wx_t_organization t2 ON t1.customerId = t2.id 
				LEFT JOIN wx_t_partner_o2o_enterprise t3 on t2.id = t3.partner_id
			 where 1=1
			 <if test="seminarId != null">
				and t1.seminar_id = #{seminarId,jdbcType=BIGINT}
			</if>
	</select>
</mapper>
