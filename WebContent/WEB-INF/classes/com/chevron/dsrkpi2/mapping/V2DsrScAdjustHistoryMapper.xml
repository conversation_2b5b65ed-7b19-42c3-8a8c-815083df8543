<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi2.dao.V2DsrScAdjustHistoryMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi2.model.V2DsrScAdjustHistory">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="adjust_year" property="adjustYear" jdbcType="INTEGER"/>
		<result column="adjust_month" property="adjustMonth" jdbcType="INTEGER"/>
		<result column="old_value" property="oldValue" jdbcType="NUMERIC"/>
		<result column="new_value" property="newValue" jdbcType="NUMERIC"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
		<result column="change_type" property="changeType" jdbcType="VARCHAR"/>
		<result column="change_value" property="changeValue" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,region,partner_id,dsr_id,adjust_year,adjust_month,old_value,new_value,create_user_id,create_time,update_user_id,
		update_time,delete_flag
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistory">
		update wx_t_v2_dsr_sc_adjust_history set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistory">
		update wx_t_v2_dsr_sc_adjust_history
		<set>
			<if test="region != null" >
				region = #{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null" >
				dsr_id = #{dsrId,jdbcType=BIGINT},
			</if>
			<if test="adjustYear != null" >
				adjust_year = #{adjustYear,jdbcType=INTEGER},
			</if>
			<if test="adjustMonth != null" >
				adjust_month = #{adjustMonth,jdbcType=INTEGER},
			</if>
			<if test="oldValue != null" >
				old_value = #{oldValue,jdbcType=NUMERIC},
			</if>
			<if test="newValue != null" >
				new_value = #{newValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryExample">
    	delete from wx_t_v2_dsr_sc_adjust_history
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistory" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_v2_dsr_sc_adjust_history
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="region != null">
				region,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="dsrId != null">
				dsr_id,
			</if>
			<if test="adjustYear != null">
				adjust_year,
			</if>
			<if test="adjustMonth != null">
				adjust_month,
			</if>
			<if test="oldValue != null">
				old_value,
			</if>
			<if test="newValue != null">
				new_value,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="region != null">
				#{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null">
				#{dsrId,jdbcType=BIGINT},
			</if>
			<if test="adjustYear != null">
				#{adjustYear,jdbcType=INTEGER},
			</if>
			<if test="adjustMonth != null">
				#{adjustMonth,jdbcType=INTEGER},
			</if>
			<if test="oldValue != null">
				#{oldValue,jdbcType=NUMERIC},
			</if>
			<if test="newValue != null">
				#{newValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_v2_dsr_sc_adjust_history
		<set>
			<if test="record.region != null">
				region = #{record.region,jdbcType=VARCHAR},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.dsrId != null">
				dsr_id = #{record.dsrId,jdbcType=BIGINT},
			</if>
			<if test="record.adjustYear != null">
				adjust_year = #{record.adjustYear,jdbcType=INTEGER},
			</if>
			<if test="record.adjustMonth != null">
				adjust_month = #{record.adjustMonth,jdbcType=INTEGER},
			</if>
			<if test="record.oldValue != null">
				old_value = #{record.oldValue,jdbcType=NUMERIC},
			</if>
			<if test="record.newValue != null">
				new_value = #{record.newValue,jdbcType=NUMERIC},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryExample">
		delete from wx_t_v2_dsr_sc_adjust_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryExample" resultType="int">
		select count(1) from wx_t_v2_dsr_sc_adjust_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_adjust_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_adjust_history
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.region, t1.partner_id, t1.dsr_id, t1.adjust_year, t1.adjust_month, t1.old_value, t1.new_value,
			 t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.delete_flag
		  from wx_t_v2_dsr_sc_adjust_history t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScAdjustHistoryParams">
		select t1.id, t1.region, t1.partner_id, t1.dsr_id, t1.adjust_year, t1.adjust_month, t1.old_value, t1.new_value,
			 t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.delete_flag,
			 t2.ch_name as create_user_name,  (t1.new_value-t1.old_value) as change_value,
			 case when (t1.new_value-t1.old_value) >= 0 then '增加' else '减少' end as change_type
		from wx_t_v2_dsr_sc_adjust_history t1
		left join wx_t_user t2 on t2.user_id=t1.create_user_id
		where t1.delete_flag=0
		<if test="region != null and region != ''">
			and t1.region = #{region, jdbcType=VARCHAR}
		</if>
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="dsrId != null">
			and t1.dsr_id = #{dsrId, jdbcType=BIGINT}
		</if>
		<if test="adjustYear != null">
			and t1.adjust_year = #{adjustYear, jdbcType=INTEGER}
		</if>
		<if test="adjustMonth != null">
			and t1.adjust_month = #{adjustMonth, jdbcType=INTEGER}
		</if>
	</select>
</mapper>
