<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi2.dao.V2DsrScGrantStatusMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi2.model.V2DsrScGrantStatus">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="grant_year" property="grantYear" jdbcType="INTEGER"/>
		<result column="grant_month" property="grantMonth" jdbcType="INTEGER"/>
		<result column="grant_status" property="grantStatus" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,grant_year,grant_month,grant_status,delete_flag,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatus">
		update wx_t_v2_dsr_sc_grant_status set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatus">
		update wx_t_v2_dsr_sc_grant_status
		<set>
			<if test="grantYear != null" >
				grant_year = #{grantYear,jdbcType=INTEGER},
			</if>
			<if test="grantMonth != null" >
				grant_month = #{grantMonth,jdbcType=INTEGER},
			</if>
			<if test="grantStatus != null" >
				grant_status = #{grantStatus,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample">
    	delete from wx_t_v2_dsr_sc_grant_status
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatus" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_v2_dsr_sc_grant_status
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="grantYear != null">
				grant_year,
			</if>
			<if test="grantMonth != null">
				grant_month,
			</if>
			<if test="grantStatus != null">
				grant_status,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="grantYear != null">
				#{grantYear,jdbcType=INTEGER},
			</if>
			<if test="grantMonth != null">
				#{grantMonth,jdbcType=INTEGER},
			</if>
			<if test="grantStatus != null">
				#{grantStatus,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_v2_dsr_sc_grant_status
		<set>
			<if test="record.grantYear != null">
				grant_year = #{record.grantYear,jdbcType=INTEGER},
			</if>
			<if test="record.grantMonth != null">
				grant_month = #{record.grantMonth,jdbcType=INTEGER},
			</if>
			<if test="record.grantStatus != null">
				grant_status = #{record.grantStatus,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample">
		delete from wx_t_v2_dsr_sc_grant_status
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample" resultType="int">
		select count(1) from wx_t_v2_dsr_sc_grant_status
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_grant_status
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_grant_status
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.grant_year, t1.grant_month, t1.grant_status, t1.delete_flag, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time
		  from wx_t_v2_dsr_sc_grant_status t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantStatusParams">
		select t1.id, t1.grant_year, t1.grant_month, t1.grant_status, t1.delete_flag, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time
		  from wx_t_v2_dsr_sc_grant_status t1
		 where t1.delete_flag=0
	</select>
</mapper>
