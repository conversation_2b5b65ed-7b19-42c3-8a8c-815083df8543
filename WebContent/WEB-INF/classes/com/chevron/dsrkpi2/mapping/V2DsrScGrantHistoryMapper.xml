<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi2.dao.V2DsrScGrantHistoryMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi2.model.V2DsrScGrantHistory">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="grant_year" property="grantYear" jdbcType="INTEGER"/>
		<result column="grant_month" property="grantMonth" jdbcType="INTEGER"/>
		<result column="new_customer_actual_ytd" property="newCustomerActualYtd" jdbcType="NUMERIC"/>
		<result column="visit_days_actual_ytd" property="visitDaysActualYtd" jdbcType="NUMERIC"/>
		<result column="sales_volume_actual_ytd" property="salesVolumeActualYtd" jdbcType="NUMERIC"/>
		<result column="hero_product_sales_volume_actual_ytd" property="heroProductSalesVolumeActualYtd" jdbcType="NUMERIC"/>
		<result column="multiple_standard_reaching_rate_ytd" property="multipleStandardReachingRateYtd" jdbcType="NUMERIC"/>
		<result column="new_customer_actual_mtd" property="newCustomerActualMtd" jdbcType="NUMERIC"/>
		<result column="new_customer_target_mtd" property="newCustomerTargetMtd" jdbcType="NUMERIC"/>
		<result column="new_customer_kpi_mtd" property="newCustomerKpiMtd" jdbcType="NUMERIC"/>
		<result column="visit_days_actual_mtd" property="visitDaysActualMtd" jdbcType="NUMERIC"/>
		<result column="visit_days_target_mtd" property="visitDaysTargetMtd" jdbcType="NUMERIC"/>
		<result column="visit_days_kpi_mtd" property="visitDaysKpiMtd" jdbcType="NUMERIC"/>
		<result column="sales_volume_actual_mtd" property="salesVolumeActualMtd" jdbcType="NUMERIC"/>
		<result column="sales_volume_target_mtd" property="salesVolumeTargetMtd" jdbcType="NUMERIC"/>
		<result column="sales_volume_kpi_mtd" property="salesVolumeKpiMtd" jdbcType="NUMERIC"/>
		<result column="hero_product_sales_volume_actual_mtd" property="heroProductSalesVolumeActualMtd" jdbcType="NUMERIC"/>
		<result column="hero_product_sales_volume_target_mtd" property="heroProductSalesVolumeTargetMtd" jdbcType="NUMERIC"/>
		<result column="hero_product_sales_volume_kpi_mtd" property="heroProductSalesVolumeKpiMtd" jdbcType="NUMERIC"/>
		<result column="multiple_standard_reaching_rate_mtd" property="multipleStandardReachingRateMtd" jdbcType="NUMERIC"/>
		<result column="point_paid_target_mtd" property="pointPaidTargetMtd" jdbcType="NUMERIC"/>
		<result column="point_paid_actual_mtd" property="pointPaidActualMtd" jdbcType="NUMERIC"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,region,partner_id,dsr_id,grant_year,grant_month,new_customer_actual_ytd,visit_days_actual_ytd,
		sales_volume_actual_ytd,hero_product_sales_volume_actual_ytd,multiple_standard_reaching_rate_ytd,
		new_customer_actual_mtd,new_customer_target_mtd,new_customer_kpi_mtd,visit_days_actual_mtd,visit_days_target_mtd,
		visit_days_kpi_mtd,sales_volume_actual_mtd,sales_volume_target_mtd,sales_volume_kpi_mtd,
		hero_product_sales_volume_actual_mtd,hero_product_sales_volume_target_mtd,hero_product_sales_volume_kpi_mtd,
		multiple_standard_reaching_rate_mtd,point_paid_target_mtd,point_paid_actual_mtd,delete_flag,create_user_id,
		create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistory">
		update wx_t_v2_dsr_sc_grant_history set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistory">
		update wx_t_v2_dsr_sc_grant_history
		<set>
			<if test="region != null" >
				region = #{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null" >
				dsr_id = #{dsrId,jdbcType=BIGINT},
			</if>
			<if test="grantYear != null" >
				grant_year = #{grantYear,jdbcType=INTEGER},
			</if>
			<if test="grantMonth != null" >
				grant_month = #{grantMonth,jdbcType=INTEGER},
			</if>
			<if test="newCustomerActualYtd != null" >
				new_customer_actual_ytd = #{newCustomerActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysActualYtd != null" >
				visit_days_actual_ytd = #{visitDaysActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeActualYtd != null" >
				sales_volume_actual_ytd = #{salesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeActualYtd != null" >
				hero_product_sales_volume_actual_ytd = #{heroProductSalesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="multipleStandardReachingRateYtd != null" >
				multiple_standard_reaching_rate_ytd = #{multipleStandardReachingRateYtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerActualMtd != null" >
				new_customer_actual_mtd = #{newCustomerActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerTargetMtd != null" >
				new_customer_target_mtd = #{newCustomerTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerKpiMtd != null" >
				new_customer_kpi_mtd = #{newCustomerKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysActualMtd != null" >
				visit_days_actual_mtd = #{visitDaysActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysTargetMtd != null" >
				visit_days_target_mtd = #{visitDaysTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysKpiMtd != null" >
				visit_days_kpi_mtd = #{visitDaysKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeActualMtd != null" >
				sales_volume_actual_mtd = #{salesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeTargetMtd != null" >
				sales_volume_target_mtd = #{salesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeKpiMtd != null" >
				sales_volume_kpi_mtd = #{salesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeActualMtd != null" >
				hero_product_sales_volume_actual_mtd = #{heroProductSalesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeTargetMtd != null" >
				hero_product_sales_volume_target_mtd = #{heroProductSalesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeKpiMtd != null" >
				hero_product_sales_volume_kpi_mtd = #{heroProductSalesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="multipleStandardReachingRateMtd != null" >
				multiple_standard_reaching_rate_mtd = #{multipleStandardReachingRateMtd,jdbcType=NUMERIC},
			</if>
			<if test="pointPaidTargetMtd != null" >
				point_paid_target_mtd = #{pointPaidTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="pointPaidActualMtd != null" >
				point_paid_actual_mtd = #{pointPaidActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryExample">
    	delete from wx_t_v2_dsr_sc_grant_history
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistory" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_v2_dsr_sc_grant_history
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="region != null">
				region,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="dsrId != null">
				dsr_id,
			</if>
			<if test="grantYear != null">
				grant_year,
			</if>
			<if test="grantMonth != null">
				grant_month,
			</if>
			<if test="newCustomerActualYtd != null">
				new_customer_actual_ytd,
			</if>
			<if test="visitDaysActualYtd != null">
				visit_days_actual_ytd,
			</if>
			<if test="salesVolumeActualYtd != null">
				sales_volume_actual_ytd,
			</if>
			<if test="heroProductSalesVolumeActualYtd != null">
				hero_product_sales_volume_actual_ytd,
			</if>
			<if test="multipleStandardReachingRateYtd != null">
				multiple_standard_reaching_rate_ytd,
			</if>
			<if test="newCustomerActualMtd != null">
				new_customer_actual_mtd,
			</if>
			<if test="newCustomerTargetMtd != null">
				new_customer_target_mtd,
			</if>
			<if test="newCustomerKpiMtd != null">
				new_customer_kpi_mtd,
			</if>
			<if test="visitDaysActualMtd != null">
				visit_days_actual_mtd,
			</if>
			<if test="visitDaysTargetMtd != null">
				visit_days_target_mtd,
			</if>
			<if test="visitDaysKpiMtd != null">
				visit_days_kpi_mtd,
			</if>
			<if test="salesVolumeActualMtd != null">
				sales_volume_actual_mtd,
			</if>
			<if test="salesVolumeTargetMtd != null">
				sales_volume_target_mtd,
			</if>
			<if test="salesVolumeKpiMtd != null">
				sales_volume_kpi_mtd,
			</if>
			<if test="heroProductSalesVolumeActualMtd != null">
				hero_product_sales_volume_actual_mtd,
			</if>
			<if test="heroProductSalesVolumeTargetMtd != null">
				hero_product_sales_volume_target_mtd,
			</if>
			<if test="heroProductSalesVolumeKpiMtd != null">
				hero_product_sales_volume_kpi_mtd,
			</if>
			<if test="multipleStandardReachingRateMtd != null">
				multiple_standard_reaching_rate_mtd,
			</if>
			<if test="pointPaidTargetMtd != null">
				point_paid_target_mtd,
			</if>
			<if test="pointPaidActualMtd != null">
				point_paid_actual_mtd,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="region != null">
				#{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null">
				#{dsrId,jdbcType=BIGINT},
			</if>
			<if test="grantYear != null">
				#{grantYear,jdbcType=INTEGER},
			</if>
			<if test="grantMonth != null">
				#{grantMonth,jdbcType=INTEGER},
			</if>
			<if test="newCustomerActualYtd != null">
				#{newCustomerActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysActualYtd != null">
				#{visitDaysActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeActualYtd != null">
				#{salesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeActualYtd != null">
				#{heroProductSalesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="multipleStandardReachingRateYtd != null">
				#{multipleStandardReachingRateYtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerActualMtd != null">
				#{newCustomerActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerTargetMtd != null">
				#{newCustomerTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="newCustomerKpiMtd != null">
				#{newCustomerKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysActualMtd != null">
				#{visitDaysActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysTargetMtd != null">
				#{visitDaysTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="visitDaysKpiMtd != null">
				#{visitDaysKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeActualMtd != null">
				#{salesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeTargetMtd != null">
				#{salesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="salesVolumeKpiMtd != null">
				#{salesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeActualMtd != null">
				#{heroProductSalesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeTargetMtd != null">
				#{heroProductSalesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="heroProductSalesVolumeKpiMtd != null">
				#{heroProductSalesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="multipleStandardReachingRateMtd != null">
				#{multipleStandardReachingRateMtd,jdbcType=NUMERIC},
			</if>
			<if test="pointPaidTargetMtd != null">
				#{pointPaidTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="pointPaidActualMtd != null">
				#{pointPaidActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_v2_dsr_sc_grant_history
		<set>
			<if test="record.region != null">
				region = #{record.region,jdbcType=VARCHAR},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.dsrId != null">
				dsr_id = #{record.dsrId,jdbcType=BIGINT},
			</if>
			<if test="record.grantYear != null">
				grant_year = #{record.grantYear,jdbcType=INTEGER},
			</if>
			<if test="record.grantMonth != null">
				grant_month = #{record.grantMonth,jdbcType=INTEGER},
			</if>
			<if test="record.newCustomerActualYtd != null">
				new_customer_actual_ytd = #{record.newCustomerActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="record.visitDaysActualYtd != null">
				visit_days_actual_ytd = #{record.visitDaysActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="record.salesVolumeActualYtd != null">
				sales_volume_actual_ytd = #{record.salesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="record.heroProductSalesVolumeActualYtd != null">
				hero_product_sales_volume_actual_ytd = #{record.heroProductSalesVolumeActualYtd,jdbcType=NUMERIC},
			</if>
			<if test="record.multipleStandardReachingRateYtd != null">
				multiple_standard_reaching_rate_ytd = #{record.multipleStandardReachingRateYtd,jdbcType=NUMERIC},
			</if>
			<if test="record.newCustomerActualMtd != null">
				new_customer_actual_mtd = #{record.newCustomerActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.newCustomerTargetMtd != null">
				new_customer_target_mtd = #{record.newCustomerTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.newCustomerKpiMtd != null">
				new_customer_kpi_mtd = #{record.newCustomerKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.visitDaysActualMtd != null">
				visit_days_actual_mtd = #{record.visitDaysActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.visitDaysTargetMtd != null">
				visit_days_target_mtd = #{record.visitDaysTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.visitDaysKpiMtd != null">
				visit_days_kpi_mtd = #{record.visitDaysKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.salesVolumeActualMtd != null">
				sales_volume_actual_mtd = #{record.salesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.salesVolumeTargetMtd != null">
				sales_volume_target_mtd = #{record.salesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.salesVolumeKpiMtd != null">
				sales_volume_kpi_mtd = #{record.salesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.heroProductSalesVolumeActualMtd != null">
				hero_product_sales_volume_actual_mtd = #{record.heroProductSalesVolumeActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.heroProductSalesVolumeTargetMtd != null">
				hero_product_sales_volume_target_mtd = #{record.heroProductSalesVolumeTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.heroProductSalesVolumeKpiMtd != null">
				hero_product_sales_volume_kpi_mtd = #{record.heroProductSalesVolumeKpiMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.multipleStandardReachingRateMtd != null">
				multiple_standard_reaching_rate_mtd = #{record.multipleStandardReachingRateMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.pointPaidTargetMtd != null">
				point_paid_target_mtd = #{record.pointPaidTargetMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.pointPaidActualMtd != null">
				point_paid_actual_mtd = #{record.pointPaidActualMtd,jdbcType=NUMERIC},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryExample">
		delete from wx_t_v2_dsr_sc_grant_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryExample" resultType="int">
		select count(1) from wx_t_v2_dsr_sc_grant_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_grant_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_sc_grant_history
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.region, t1.partner_id, t1.dsr_id, t1.grant_year, t1.grant_month, t1.new_customer_actual_ytd,
			 t1.visit_days_actual_ytd, t1.sales_volume_actual_ytd, t1.hero_product_sales_volume_actual_ytd,
			 t1.multiple_standard_reaching_rate_ytd, t1.new_customer_actual_mtd, t1.new_customer_target_mtd,
			 t1.new_customer_kpi_mtd, t1.visit_days_actual_mtd, t1.visit_days_target_mtd, t1.visit_days_kpi_mtd,
			 t1.sales_volume_actual_mtd, t1.sales_volume_target_mtd, t1.sales_volume_kpi_mtd,
			 t1.hero_product_sales_volume_actual_mtd, t1.hero_product_sales_volume_target_mtd,
			 t1.hero_product_sales_volume_kpi_mtd, t1.multiple_standard_reaching_rate_mtd, t1.point_paid_target_mtd,
			 t1.point_paid_actual_mtd, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_v2_dsr_sc_grant_history t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrScGrantHistoryParams">
		select t1.id, t1.region, t1.partner_id, t1.dsr_id, t1.grant_year, t1.grant_month, t1.new_customer_actual_ytd,
			 t1.visit_days_actual_ytd, t1.sales_volume_actual_ytd, t1.hero_product_sales_volume_actual_ytd,
			 t1.multiple_standard_reaching_rate_ytd, t1.new_customer_actual_mtd, t1.new_customer_target_mtd,
			 t1.new_customer_kpi_mtd, t1.visit_days_actual_mtd, t1.visit_days_target_mtd, t1.visit_days_kpi_mtd,
			 t1.sales_volume_actual_mtd, t1.sales_volume_target_mtd, t1.sales_volume_kpi_mtd,
			 t1.hero_product_sales_volume_actual_mtd, t1.hero_product_sales_volume_target_mtd,
			 t1.hero_product_sales_volume_kpi_mtd, t1.multiple_standard_reaching_rate_mtd, t1.point_paid_target_mtd,
			 t1.point_paid_actual_mtd, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_v2_dsr_sc_grant_history t1
		 where t1.delete_flag=0
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_v2_dsr_sc_grant_history (region, partner_id, dsr_id, grant_year, grant_month,
		new_customer_actual_ytd, visit_days_actual_ytd, sales_volume_actual_ytd, hero_product_sales_volume_actual_ytd,
		multiple_standard_reaching_rate_ytd, new_customer_actual_mtd, new_customer_target_mtd, new_customer_kpi_mtd,
		visit_days_actual_mtd, visit_days_target_mtd, visit_days_kpi_mtd, sales_volume_actual_mtd,
		sales_volume_target_mtd, sales_volume_kpi_mtd, hero_product_sales_volume_actual_mtd, hero_product_sales_volume_target_mtd,
		hero_product_sales_volume_kpi_mtd, multiple_standard_reaching_rate_mtd, point_paid_target_mtd, point_paid_actual_mtd,
		delete_flag, create_user_id, create_time, update_user_id, update_time) values
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.region, jdbcType=VARCHAR},
				#{item.partnerId, jdbcType=BIGINT},
				#{item.dsrId, jdbcType=BIGINT},
				#{item.grantYear, jdbcType=INTEGER},
				#{item.grantMonth, jdbcType=INTEGER},
				#{item.newCustomerActualYtd, jdbcType=NUMERIC},
				#{item.visitDaysActualYtd, jdbcType=NUMERIC},
				#{item.salesVolumeActualYtd, jdbcType=NUMERIC},
				#{item.heroProductSalesVolumeActualYtd, jdbcType=NUMERIC},
				#{item.multipleStandardReachingRateYtd, jdbcType=NUMERIC},
				#{item.newCustomerActualMtd, jdbcType=NUMERIC},
				#{item.newCustomerTargetMtd, jdbcType=NUMERIC},
				#{item.newCustomerKpiMtd, jdbcType=NUMERIC},
				#{item.visitDaysActualMtd, jdbcType=NUMERIC},
				#{item.visitDaysTargetMtd, jdbcType=NUMERIC},
				#{item.visitDaysKpiMtd, jdbcType=NUMERIC},
				#{item.salesVolumeActualMtd, jdbcType=NUMERIC},
				#{item.salesVolumeTargetMtd, jdbcType=NUMERIC},
				#{item.salesVolumeKpiMtd, jdbcType=NUMERIC},
				#{item.heroProductSalesVolumeActualMtd, jdbcType=NUMERIC},
				#{item.heroProductSalesVolumeTargetMtd, jdbcType=NUMERIC},
				#{item.heroProductSalesVolumeKpiMtd, jdbcType=NUMERIC},
				#{item.multipleStandardReachingRateMtd, jdbcType=NUMERIC},
				#{item.pointPaidTargetMtd, jdbcType=NUMERIC},
				#{item.pointPaidActualMtd, jdbcType=NUMERIC},
				#{item.deleteFlag, jdbcType=INTEGER},
				#{item.createUserId, jdbcType=BIGINT},
				#{item.createTime, jdbcType=TIMESTAMP},
				#{item.updateUserId, jdbcType=BIGINT},
				#{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
