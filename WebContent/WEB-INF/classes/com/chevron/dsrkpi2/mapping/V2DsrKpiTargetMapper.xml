<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi2.dao.V2DsrKpiTargetMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi2.model.V2DsrKpiTarget">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="effective_year" property="effectiveYear" jdbcType="INTEGER"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="kpi_code" property="kpiCode" jdbcType="VARCHAR"/>
		<result column="kpi_code_text" property="kpiCodeText" jdbcType="VARCHAR"/>
		<result column="effective_month" property="effectiveMonth" jdbcType="INTEGER"/>
		<result column="target_value" property="targetValue" jdbcType="NUMERIC"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="dsr_account" property="dsrAccount" jdbcType="VARCHAR"/>
		<result column="dsr_name" property="dsrName" jdbcType="VARCHAR"/>
		<result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="sale_name" property="saleName" jdbcType="VARCHAR"/>
		<result column="v1" property="v1" jdbcType="NUMERIC"/>
		<result column="v2" property="v2" jdbcType="NUMERIC"/>
		<result column="v3" property="v3" jdbcType="NUMERIC"/>
		<result column="v4" property="v4" jdbcType="NUMERIC"/>
		<result column="v5" property="v5" jdbcType="NUMERIC"/>
		<result column="v6" property="v6" jdbcType="NUMERIC"/>
		<result column="v7" property="v7" jdbcType="NUMERIC"/>
		<result column="v8" property="v8" jdbcType="NUMERIC"/>
		<result column="v9" property="v9" jdbcType="NUMERIC"/>
		<result column="v10" property="v10" jdbcType="NUMERIC"/>
		<result column="v11" property="v11" jdbcType="NUMERIC"/>
		<result column="v12" property="v12" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,effective_year,region,partner_id,dsr_id,kpi_code,effective_month,target_value,ext_property1,ext_property2,
		ext_property3,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTarget">
		update wx_t_v2_dsr_kpi_target set
				kpi_code = #{kpiCode,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTarget">
		update wx_t_v2_dsr_kpi_target
		<set>
			<if test="effectiveYear != null" >
				effective_year = #{effectiveYear,jdbcType=INTEGER},
			</if>
			<if test="region != null" >
				region = #{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null" >
				dsr_id = #{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiCode != null" >
				kpi_code = #{kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="effectiveMonth != null" >
				effective_month = #{effectiveMonth,jdbcType=INTEGER},
			</if>
			<if test="targetValue != null" >
				target_value = #{targetValue,jdbcType=NUMERIC},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetExample">
    	delete from wx_t_v2_dsr_kpi_target
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTarget" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_v2_dsr_kpi_target
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="effectiveYear != null">
				effective_year,
			</if>
			<if test="region != null">
				region,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="dsrId != null">
				dsr_id,
			</if>
			<if test="kpiCode != null">
				kpi_code,
			</if>
			<if test="effectiveMonth != null">
				effective_month,
			</if>
			<if test="targetValue != null">
				target_value,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="effectiveYear != null">
				#{effectiveYear,jdbcType=INTEGER},
			</if>
			<if test="region != null">
				#{region,jdbcType=VARCHAR},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="dsrId != null">
				#{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiCode != null">
				#{kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="effectiveMonth != null">
				#{effectiveMonth,jdbcType=INTEGER},
			</if>
			<if test="targetValue != null">
				#{targetValue,jdbcType=NUMERIC},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_v2_dsr_kpi_target
		<set>
			<if test="record.effectiveYear != null">
				effective_year = #{record.effectiveYear,jdbcType=INTEGER},
			</if>
			<if test="record.region != null">
				region = #{record.region,jdbcType=VARCHAR},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.dsrId != null">
				dsr_id = #{record.dsrId,jdbcType=BIGINT},
			</if>
			<if test="record.kpiCode != null">
				kpi_code = #{record.kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="record.effectiveMonth != null">
				effective_month = #{record.effectiveMonth,jdbcType=INTEGER},
			</if>
			<if test="record.targetValue != null">
				target_value = #{record.targetValue,jdbcType=NUMERIC},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetExample">
		delete from wx_t_v2_dsr_kpi_target
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetExample" resultType="int">
		select count(1) from wx_t_v2_dsr_kpi_target
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_kpi_target
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_v2_dsr_kpi_target
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.effective_year, t1.region, t1.partner_id, t1.dsr_id, t1.kpi_code, t1.effective_month,
			 t1.target_value, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='DsrKpiV2.kpiCode' and di1.dic_item_code=t1.kpi_code) kpi_code_text
		  from wx_t_v2_dsr_kpi_target t1
		 where 1=1
		<if test="kpiCode != null and kpiCode != ''">
			and t1.kpi_code = #{kpiCode, jdbcType=VARCHAR}
		</if>
	</select>

	<!-- 分页查询 -->
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetParams">
		select t1.id, t1.effective_year, t1.region, t1.partner_id, t1.dsr_id, t1.kpi_code, t1.effective_month,
		t1.target_value, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.create_user_id, t1.create_time,
		t1.update_user_id, t1.update_time,
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='DsrKpiV2.kpiCode' and di1.dic_item_code=t1.kpi_code) kpi_code_text
		from wx_t_v2_dsr_kpi_target t1
		where 1=1
		<if test="kpiCode != null and kpiCode != ''">
			and t1.kpi_code = #{kpiCode, jdbcType=VARCHAR}
		</if>
	</select>

	<!-- 分页查询 -->
	<select id="queryForPageGroup" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi2.model.V2DsrKpiTargetParams">
		select
		distinct dktv.*,
		o.organization_name partner_name,
		u.ch_name dsr_name,
		u.login_name dsr_account,
		cos.sales_name_cn sale_name,
		(select di1.dic_item_name+'('+di1.dic_item_desc+')' from wx_t_dic_item di1 where di1.dic_type_code='DsrKpiV2.kpiCode' and di1.dic_item_code=dktv.kpi_code) kpi_code_text
		from
		(SELECT
		partner_id,
		dsr_id,
		kpi_code,
		region,
		effective_year,
		sum(case when effective_month=1 then target_value else 0 end) v1,
		sum(case when effective_month=2 then target_value else 0 end) v2,
		sum(case when effective_month=3 then target_value else 0 end) v3,
		sum(case when effective_month=4 then target_value else 0 end) v4,
		sum(case when effective_month=5 then target_value else 0 end) v5,
		sum(case when effective_month=6 then target_value else 0 end) v6,
		sum(case when effective_month=7 then target_value else 0 end) v7,
		sum(case when effective_month=8 then target_value else 0 end) v8,
		sum(case when effective_month=9 then target_value else 0 end) v9,
		sum(case when effective_month=10 then target_value else 0 end) v10,
		sum(case when effective_month=11 then target_value else 0 end) v11,
		sum(case when effective_month=12 then target_value else 0 end) v12
		from wx_t_v2_dsr_kpi_target
		GROUP BY partner_id,dsr_id,kpi_code,region,effective_year
		) dktv
		left join wx_t_partner_o2o_enterprise pe on pe.partner_id=dktv.partner_id
		left join wx_t_organization o on o.id=dktv.partner_id
		left join dw_customer_org_sales cos on cos.distributor_id = pe.distributor_id
		left join wx_t_user u on u.user_id=dktv.dsr_id
		where 1=1
		and dktv.partner_id != -1
		and dktv.dsr_id != -1
		and dktv.effective_year =2021
		<if test="kpiCode != null and kpiCode != ''">
			and dktv.kpi_code = #{kpiCode, jdbcType=VARCHAR}
		</if>
		<if test="region != null and region != ''">
			and dktv.region = #{region, jdbcType=VARCHAR}
		</if>
		<if test="saleName != null and saleName != ''">
			and cos.sales_name_cn like '%' + #{saleName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="partnerId != null">
			and dktv.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="dsrAccount != null and dsrAccount != ''">
			and u.login_name like '%' + #{dsrAccount, jdbcType=VARCHAR} + '%'
		</if>
		<if test="dsrName != null and dsrName != ''">
			and u.ch_name like '%' + #{dsrName, jdbcType=VARCHAR} + '%'
		</if>
	</select>

	<insert id="insertBatch" parameterType="java.util.List">
		insert into wx_t_v2_dsr_kpi_target (effective_year, region,
		partner_id, dsr_id, kpi_code, effective_month,target_value,
		ext_property1, ext_property2, ext_property3, create_user_id,
		create_time, update_user_id, update_time
		) values
		<foreach collection="list" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.effectiveYear,jdbcType=INTEGER},
				#{item.region,jdbcType=NVARCHAR},
				#{item.partnerId,jdbcType=BIGINT},
				#{item.dsrId,jdbcType=BIGINT},
				#{item.kpiCode,jdbcType=NVARCHAR},
				#{item.effectiveMonth,jdbcType=INTEGER},
				#{item.targetValue,jdbcType=NVARCHAR},
				#{item.extProperty1,jdbcType=NVARCHAR},
				#{item.extProperty2,jdbcType=NVARCHAR},
				#{item.extProperty3,jdbcType=NVARCHAR},
				#{item.createUserId,jdbcType=BIGINT},
				#{item.createTime,jdbcType=TIMESTAMP},
				#{item.updateUserId,jdbcType=BIGINT},
				#{item.updateTime,jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
