<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.OemDeliveryImportLogMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.OemDeliveryImportLog" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="file_name" property="fileName" jdbcType="NVARCHAR" />
    <result column="delivery_id" property="deliveryId" jdbcType="NVARCHAR" />
    <result column="code" property="code" jdbcType="NVARCHAR" />
    <result column="code_type" property="codeType" jdbcType="NVARCHAR" />
    <result column="log_type" property="logType" jdbcType="NVARCHAR" />
    <result column="log_detail" property="logDetail" jdbcType="NVARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, file_name, delivery_id, code, code_type, log_type, log_detail, creation_time, 
    created_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.OemDeliveryImportLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_oem_delivery_import_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.OemDeliveryImportLogExample" >
    delete from wx_t_oem_delivery_import_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.OemDeliveryImportLog" >
    insert into wx_t_oem_delivery_import_log (id, file_name, delivery_id, 
      code, code_type, log_type, 
      log_detail, creation_time, created_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{fileName,jdbcType=NVARCHAR}, #{deliveryId,jdbcType=NVARCHAR}, 
      #{code,jdbcType=NVARCHAR}, #{codeType,jdbcType=NVARCHAR}, #{logType,jdbcType=NVARCHAR}, 
      #{logDetail,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.OemDeliveryImportLog" >
    insert into wx_t_oem_delivery_import_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="deliveryId != null" >
        delivery_id,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="codeType != null" >
        code_type,
      </if>
      <if test="logType != null" >
        log_type,
      </if>
      <if test="logDetail != null" >
        log_detail,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryId != null" >
        #{deliveryId,jdbcType=NVARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="codeType != null" >
        #{codeType,jdbcType=NVARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=NVARCHAR},
      </if>
      <if test="logDetail != null" >
        #{logDetail,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_oem_delivery_import_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null" >
        file_name = #{record.fileName,jdbcType=NVARCHAR},
      </if>
      <if test="record.deliveryId != null" >
        delivery_id = #{record.deliveryId,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.codeType != null" >
        code_type = #{record.codeType,jdbcType=NVARCHAR},
      </if>
      <if test="record.logType != null" >
        log_type = #{record.logType,jdbcType=NVARCHAR},
      </if>
      <if test="record.logDetail != null" >
        log_detail = #{record.logDetail,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_oem_delivery_import_log
    set id = #{record.id,jdbcType=BIGINT},
      file_name = #{record.fileName,jdbcType=NVARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      code_type = #{record.codeType,jdbcType=NVARCHAR},
      log_type = #{record.logType,jdbcType=NVARCHAR},
      log_detail = #{record.logDetail,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>