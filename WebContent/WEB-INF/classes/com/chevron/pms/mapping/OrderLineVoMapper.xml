<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.OrderLineVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.OrderLineVo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="actual_amount" property="actualAmount" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    
    <result column="region_name" property="regionName" jdbcType="NVARCHAR" />
    <result column="warehouse_name" property="warehouseName" jdbcType="NVARCHAR" />
    <result column="index_no" property="indexNo" jdbcType="INTEGER" />
    <result column="oilqrcode" property="oilQrcode" jdbcType="NVARCHAR"/>
    <result column="product_photo" property="productPhoto" jdbcType="NVARCHAR" />
    
    <result column="capacity" property="capacity" jdbcType="NVARCHAR"/>
    <result column="viscosity" property="viscosity" jdbcType="NVARCHAR" />
    <result column="sku_new" property="skuNew" jdbcType="NVARCHAR" />
    <result column="points" property="points" jdbcType="INTEGER" />
    
    
    
  </resultMap>
  <sql id="Base_Column_List" >
    id, order_id, product_id, sku, product_name, amount, units, price, status, create_time, 
    update_time, creator, remark,actual_amount,type
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_order_line
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_order_line
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.OrderLineVo" >
    insert into wx_t_order_line (order_id, product_id, 
      sku, product_name, amount, 
      units, price, status, 
      create_time, update_time, creator, 
      remark,actual_amount,type)
    values ( #{orderId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, 
      #{sku,jdbcType=NVARCHAR}, #{productName,jdbcType=NVARCHAR}, #{amount,jdbcType=INTEGER}, 
      #{units,jdbcType=NVARCHAR}, #{price,jdbcType=NUMERIC}, #{status,jdbcType=NVARCHAR}, 
      getDate(), getDate(), #{creator,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR},#{actualAmount,jdbcType=INTEGER},#{type,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.OrderLineVo" >
    insert into wx_t_order_line
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="productId != null" >
        product_id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="units != null" >
        units,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="actualAmount != null" >
        actual_amount,
      </if>
      <if test="type != null" >
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="units != null" >
        #{units,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="actualAmount != null" >
        #{actualAmount,jdbcType=INTEGER},
      </if>
       <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.OrderLineVo" >
    update wx_t_order_line
    <set >
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        product_name = #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="units != null" >
        units = #{units,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
       <if test="amount != null" >
        actual_amount = #{actualAmount,jdbcType=INTEGER},
      </if>
       <if test="type != null" >
        type = #{type,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.OrderLineVo" >
    update wx_t_order_line
    set order_id = #{orderId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=NVARCHAR},
      product_name = #{productName,jdbcType=NVARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      units = #{units,jdbcType=NVARCHAR},
      price = #{price,jdbcType=NUMERIC},
      status = #{status,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR},
      actual_amount = #{actualAmount,jdbcType=INTEGER},
      type =  #{type,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 可根据条件选择查询 -->
	<select id="queryOrderLinesByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT order_line.product_id,
		       order_line.amount,
		       p.sku,
		       p.name as product_name,
		       p.units,
		       p.sale_price as price,
		       order_line.amount as actual_amount
		FROM wx_t_product p,
		  (SELECT
		     l.product_id,
		     sum(l.amount) AS amount
		   FROM wx_t_order_line l
		   WHERE 1 = 1
		   <if test="orderIdList != null">
		      	and l.order_id IN 
		     	<foreach item="orderId" index="index" collection="orderIdList" open="(" separator="," close=")">
		     		#{orderId}
		     	</foreach>
		   </if>
		   GROUP BY l.product_id) order_line
		WHERE order_line.product_id = p.id
	</select>
	
	<!-- bo.liu -->
	<select id="getOrderLinesByConditionForDDBXOrder" parameterType="map" resultMap="BaseResultMap">
		SELECT DISTINCT tt_order_line.id, tt_order_line.order_id, tt_order_line.product_id, tt_order_line.sku, tt_order_line.amount, 
		tt_order_line.units, tt_order_line.price, tt_order_line.status, tt_order_line.create_time, tt_order_line.update_time, 
		tt_order_line.creator, tt_order_line.remark,tt_order_line.actual_amount,tt_order_line.type,product.viscosity,product.capacity,
		product.name product_name,
		<if test="skuNewType!=null">
		t_v_t_map.value_before_transform sku_new,
		</if>
		(select top 1 af1.att_id from wx_att_file af1 where af1.source_id=product.id and af1.source_type='5' order by af1.create_time asc) product_photo
		FROM wx_t_order_line tt_order_line
		LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
		<if test="skuNewType!=null">
		LEFT JOIN wx_t_value_transform_map t_v_t_map ON t_v_t_map.value_after_transform = tt_order_line.sku 
		</if>
		WHERE 
			<if test="orderId != null">
				tt_order_line.order_id= #{orderId}
			and
			</if>
			 <if test="type != null">
				tt_order_line.type = #{type}
			and
			</if>
			1 = 1
	</select>
	
	 <insert id="insertOrderLineBatch" parameterType="java.util.List">
	     insert into wx_t_order_line (order_id, product_id, 
		      sku, product_name, amount, 
		      units, price, status, 
		      create_time, update_time, creator, 
		      remark,actual_amount,type,oilqrcode,points)
      	 values
	    <foreach collection="list" index="index" item="item"
			separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.orderId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, 
      			#{item.sku,jdbcType=NVARCHAR}, #{item.productName,jdbcType=NVARCHAR}, #{item.amount,jdbcType=INTEGER}, 
     			#{item.units,jdbcType=NVARCHAR}, #{item.price,jdbcType=NUMERIC}, #{item.status,jdbcType=NVARCHAR}, 
      			getDate(), getDate(), #{item.creator,jdbcType=NVARCHAR}, 
      			#{item.remark,jdbcType=NVARCHAR},#{item.actualAmount,jdbcType=INTEGER},#{item.type,jdbcType=NVARCHAR},
      			#{item.oilQrcode,jdbcType=NVARCHAR},#{item.points,jdbcType=INTEGER}
			</trim>
		</foreach>
 	</insert>
	
	
	<!-- 
	正确查询
	<select id="getExportDataByOrderIds"  parameterType="map" resultMap="BaseResultMap">
		SELECT
			ROW_NUMBER() OVER (ORDER BY tt_orderline.sku ASC) AS index_no,tt_orderline.sku ,sum(tt_orderline.amount) AS amount,t_order.region_name AS region_name,tt_ddbx.warehouse_name AS warehouse_name
		FROM
			wx_t_order_line tt_orderline
		LEFT JOIN
			wx_t_order t_order
		ON
			tt_orderline.order_id = t_order.id
		LEFT JOIN 
			wx_t_order_ddbxwarehouse tt_ddbx
		ON
			t_order.region_name = tt_ddbx.region_name
		WHERE
		<if test="needExportOrderIds!=null">
			t_order.id IN
			<foreach item="order_id" index="index" collection="needExportOrderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
			and
		</if>
		1=1
		GROUP
		BY tt_orderline.sku,t_order.region_name,warehouse_name
		
	</select> -->
	<!-- 现在业务全部处理成上海仓库 -->
	<select id="getExportDataByOrderIds"  parameterType="map" resultMap="BaseResultMap">
		SELECT
			ROW_NUMBER() OVER (ORDER BY tt_orderline.sku ASC) AS index_no,tt_orderline.sku ,sum(tt_orderline.amount) AS amount
		FROM
			wx_t_order_line tt_orderline
		LEFT JOIN
			wx_t_order t_order
		ON
			tt_orderline.order_id = t_order.id
		WHERE
		<if test="needExportOrderIds!=null">
			t_order.id IN
			<foreach item="order_id" index="index" collection="needExportOrderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
			and
		</if>
		1=1
		GROUP
		BY tt_orderline.sku
		
	</select>
	
	<delete id="delteBatchOrderLineByIds" parameterType="map">
  	 delete
  	 	from wx_t_order_line
  	 	where
   		 <if test="orderLineIds!=null">
	    	id in
	    	<foreach item="orderLineId" index="index" collection="orderLineIds" open="(" separator="," close=")">  
				 #{orderLineId}  
			</foreach> 
			and
	    </if>
	    1 = 1
   </delete>
   
   <update id="updateBatchOrderLineByIds" parameterType="map">
   		update wx_t_order_line
    		set status = #{status}
    	where
    	<if test="orderLineIds!=null">
	    	id in
	    	<foreach item="orderLineId" index="index" collection="orderLineIds" open="(" separator="," close=")">  
				 #{orderLineId}  
			</foreach> 
			and
	    </if>
	    1 = 1
   
   </update>
	
	<!-- end bo.liu -->
	<update id="confirmAmountAndStatus" parameterType="com.chevron.pms.model.OrderLineVo" >
	    update wx_t_order_line
	    set 
	      amount = #{amount,jdbcType=INTEGER},
	      status = #{status,jdbcType=NVARCHAR},
	      update_time = #{updateTime,jdbcType=TIMESTAMP},
	      actual_amount = #{actualAmount,jdbcType=INTEGER}
	    where id = #{id,jdbcType=BIGINT}
	  </update>
	  
	  
	<select id="getOrderLinesByOrderId"  parameterType="map" resultMap="BaseResultMap">
		SELECT tt_order_line.*, t1.capacity, (select top 1 af1.att_id from wx_att_file af1 where af1.source_id=t1.id and af1.source_type='5' order by af1.create_time asc) product_photo  
		FROM wx_t_order_line tt_order_line 
		left join wx_t_product t1 on t1.sku=tt_order_line.sku
		WHERE order_id = #{orderId,jdbcType=BIGINT}
		
	</select>
	  
	 <select id="gePoOrderLinesByOrderIds"  parameterType="map" resultMap="BaseResultMap">
		SELECT tt_order_line.*,t_a.att_id product_photo  
		FROM wx_t_order_line tt_order_line 
		LEFT JOIN wx_att_file t_a ON t_a.source_id=tt_order_line.product_id
		WHERE
		<if test="orderIds!=null">
			tt_order_line.order_id IN
			<foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
			and
		</if>
		<!-- t_a.uuid IS NOT NULL
		AND -->
		
	        t_a.source_type=5
	 	AND
		1=1
	</select>
	
	
	<delete id="delteBatchOrderLineByOrderIds" parameterType="map">
  	 delete
  	 	from wx_t_order_line
  	 	where 1=1
   		 <if test="orderLineOrderIds!=null">
	    	and order_id in
	    	<foreach item="orderLineOrderId" index="index" collection="orderLineOrderIds" open="(" separator="," close=")">  
				 #{orderLineOrderId}  
			</foreach> 
	    </if>
	    <if test="orderId != null">
	    and order_id=#{orderId}
	    </if>
   </delete>
	<select id="getAllOrderLinesByOrderIds"  parameterType="map" resultMap="BaseResultMap">
		SELECT tt_order_line.*
		FROM wx_t_order_line tt_order_line 
		WHERE
		<if test="orderIds!=null">
			tt_order_line.order_id IN
			<foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
		</if>
	</select>  
	
	<update id="updateAllOrderLineStatusByOrderIds"  parameterType="map">
		update wx_t_order_line  set status = #{status}
		WHERE
		<if test="orderIds!=null">
			order_id IN
			<foreach item="order_id" index="index" collection="orderIds" open="(" separator="," close=")">  
				 #{order_id}  
			</foreach> 
		</if>
	</update>


    <select id="getOrderLinesByOrderStatus" parameterType="com.chevron.pms.model.OrderCondition" resultMap="BaseResultMap">
        SELECT DISTINCT tt_order_line.*,product.viscosity,product.capacity
        FROM wx_t_order_line tt_order_line
        LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
        WHERE
        tt_order_line.order_id in
        (
        SELECT
        DISTINCT tt_order.id
        FROM
        wx_t_order tt_order
        LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
        LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id
        LEFT JOIN wx_t_user u  ON u.user_id = tt_order.creator
        LEFT JOIN wx_t_region r1 ON tt_workshop.region_id = r1.id
        LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
        LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
        WHERE
        1 = 1
        AND tt_order.create_time >= '2020-04-01 00:00:00.000'
        <if test="partnerId != null and partnerId != ''">
            AND tt_par.partner_id = #{partnerId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            AND tt_order.status = #{orderStatus}
        </if>
        <if test="executeUserId">
            AND tt_order.creator = #{executeUserId}
        </if>
        <if test="executeUserName != null and executeUserName != '' ">
            AND u.ch_name like  '%'+ #{executeUserName} +'%'
        </if>

        <if test="dateStart != null">
            and tt_order.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
        </if>
        <if test="dateEnd != null">
            and tt_order.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
        </if>
        <if test="workshopName != null and workshopName != ''">
            AND tt_workshop.work_shop_name like '%'+ #{workshopName} +'%'
        </if>
        <if test="source">
            AND tt_workshop.from_source = #{source}
        </if>
        <if test="prov != null">
            and r3.id = #{prov,jdbcType=BIGINT}
        </if>
        <if test="city != null">
            and r2.id = #{city,jdbcType=BIGINT}
        </if>
        <if test="dist != null">
            and r1.id = #{dist,jdbcType=BIGINT}
        </if>
        )
        and
        1 = 1
    </select>


    <select id="getOrderTotalLitersByOrderStatus" parameterType="com.chevron.pms.model.OrderCondition" resultType="Double" >
        SELECT isnull(SUM(tt_order_line.amount*CONVERT(decimal(9,2), product.capacity)), 0.00) as total_liters
        FROM wx_t_order_line tt_order_line
        LEFT JOIN wx_t_product product ON tt_order_line.sku = product.sku
        WHERE
        tt_order_line.order_id in
        (
        SELECT
        DISTINCT tt_order.id
        FROM
        wx_t_order tt_order
        LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_order.work_shop_id
        LEFT JOIN wx_t_workshop_partner tt_par ON tt_par.workshop_id = tt_order.work_shop_id
        LEFT JOIN wx_t_user u  ON u.user_id = tt_order.creator
        LEFT JOIN wx_t_region r1 ON tt_workshop.region_id = r1.id
        LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
        LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
        WHERE
        1 = 1
        AND tt_order.create_time >= '2020-04-01 00:00:00.000'
        <if test="partnerId != null and partnerId != ''">
            AND tt_par.partner_id = #{partnerId}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            AND tt_order.status = #{orderStatus}
        </if>
        <if test="executeUserId">
            AND tt_order.creator = #{executeUserId}
        </if>
        <if test="executeUserName != null and executeUserName != '' ">
            AND u.ch_name like  '%'+ #{executeUserName} +'%'
        </if>

        <if test="dateStart != null">
            and tt_order.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
        </if>
        <if test="dateEnd != null">
            and tt_order.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
        </if>
        <if test="workshopName != null and workshopName != ''">
            AND tt_workshop.work_shop_name like '%'+ #{workshopName} +'%'
        </if>
        <if test="source">
            AND tt_workshop.from_source = #{source}
        </if>
        <if test="prov != null">
            and r3.id = #{prov,jdbcType=BIGINT}
        </if>
        <if test="city != null">
            and r2.id = #{city,jdbcType=BIGINT}
        </if>
        <if test="dist != null">
            and r1.id = #{dist,jdbcType=BIGINT}
        </if>
        )
        and product.capacity is not null
        and product.capacity != ''
        and
        1 = 1
    </select>
</mapper>