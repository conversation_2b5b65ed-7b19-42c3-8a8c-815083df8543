<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.FeedbackVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.FeedbackVo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="message" property="message" jdbcType="NVARCHAR" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, message, type, create_time, creator, update_time, updator, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.FeedbackVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_feedback
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_feedback
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_feedback
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert"  parameterType="com.chevron.pms.model.FeedbackVo" >
    insert into wx_t_feedback (message, type, 
      create_time, creator, update_time, 
      updator, remark)
    values (#{message,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, 
      getdate(), #{creator,jdbcType=BIGINT}, getdate(), 
      #{updator,jdbcType=BIGINT}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.FeedbackVo" >
    insert into wx_t_feedback
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="message != null" >
        message,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="message != null" >
        #{message,jdbcType=NVARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.FeedbackVo" >
    update wx_t_feedback
    <set >
      <if test="message != null" >
        message = #{message,jdbcType=NVARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.FeedbackVo" >
    update wx_t_feedback
    set message = #{message,jdbcType=NVARCHAR},
      type = #{type,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=BIGINT},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>