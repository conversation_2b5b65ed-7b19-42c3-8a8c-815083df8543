<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.pms.dao.OilVerificationMapper">
<resultMap id="BaseResultMap" type="com.chevron.pms.model.OilVerification">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="qr_code_id" jdbcType="VARCHAR" property="qrCodeId" />
	<result column="capacity" jdbcType="INTEGER" property="capacity" />
	<result column="mechanic_code" jdbcType="VARCHAR" property="mechanicCode" />
	<result column="workshop_id" jdbcType="VARCHAR" property="workshopId" />
	<result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
	<result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="sku" jdbcType="VARCHAR" property="sku"/>
	<result column="product_name" jdbcType="VARCHAR" property="productName" />
	<result column="sp_name" jdbcType="VARCHAR" property="partnerName" />
	<result column="bd_name" jdbcType="VARCHAR" property="bdName" />
	<result column="status" jdbcType="VARCHAR" property="status"/>
	<result column="order_flag" jdbcType="INTEGER" property="orderFlag" />
	<result column="user_wechat_openid" jdbcType="VARCHAR" property="userWechatOpenid" />
	<result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
	<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	<result column="mechanic_name" jdbcType="VARCHAR" property="mechanicName" />
	<result column="employee_type" jdbcType="VARCHAR" property="employeeType"/>
	<result column="workshop_exec_user_name" jdbcType="VARCHAR" property="workshopExecUserName"/>
	<result column="verified_capacity" jdbcType="INTEGER" property="verifiedCapacity"/>
	<result column="per_liter_reward" property="perLiterReward" jdbcType="DOUBLE"/>
	<result column="owner_per_liter_reward" property="ownerPerLiterReward" jdbcType="DOUBLE"/>
	<result column="mechanic_per_liter_reward" property="mechanicPerLiterReward" jdbcType="DOUBLE"/>
	<result column="partner_id" jdbcType="BIGINT" property="partnerId" />
	<result column="distribution_rule_id" jdbcType="BIGINT" property="distributionRuleId" />
	<result column="verification_rule_id" jdbcType="BIGINT" property="verificationRuleId" />
	<result column="organization_name" jdbcType="VARCHAR" property="organizationName" />

	<result column="province_name" jdbcType="VARCHAR" property="provinceName" />
	<result column="city_name" jdbcType="VARCHAR" property="cityName" />
	<result column="region_name" jdbcType="VARCHAR" property="regionName" />
	<result column="workshop_address" jdbcType="VARCHAR" property="workshopAddress" />
</resultMap>

<select id="queryOilDayReportForEmail" parameterType="map" resultMap="BaseResultMap">
select * from (
		SELECT
			1 order_flag,
			sum(v.capacity) capacity,
			max(v.workshop_name) workshop_name,
			v.workshop_id,
			v.sku,
			max(v.product_name) product_name,
			max(o.organization_name) organization_name,
			o.id partner_id
		FROM wx_v_oil_verification v
			JOIN wx_t_workshop_partner wp on v.workshop_id = wp.workshop_id and wp.relation_type='trade'
			JOIN wx_t_organization o on wp.partner_id=o.id
		WHERE not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v.qr_code)
	<if test="partnerInCondition != null">
		and o.id in ${partnerInCondition}
	</if>
			group by v.sku, v.workshop_id, o.id
			union all
		SELECT
			2 order_flag,
			sum(v.capacity) capacity,
			'' workshop_name,
			v.workshop_id,
			v.sku,
			'' product_name,
			'' organization_name,
			o.id partner_id
		FROM wx_v_oil_verification v
			JOIN wx_t_workshop_partner wp on v.workshop_id = wp.workshop_id and wp.relation_type='trade'
			JOIN wx_t_organization o on wp.partner_id=o.id
		WHERE not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v.qr_code)
		and v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
	<if test="partnerInCondition != null">
		and o.id in ${partnerInCondition}
	</if>
			group by v.sku, v.workshop_id, o.id
			union all
		SELECT 3 order_flag,
					sum(v.capacity) capacity,
					'' workshop_name,
					v.workshop_id,
					v.sku,
					'' product_name,
						'' organization_name,
							o.id partner_id
		FROM wx_v_oil_verification v
			JOIN wx_t_workshop_partner wp on v.workshop_id = wp.workshop_id and wp.relation_type='trade'
			JOIN wx_t_organization o on wp.partner_id=o.id
		WHERE not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v.qr_code)
		and v.creation_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo1, jdbcType=TIMESTAMP}
	<if test="partnerInCondition != null">
		and o.id in ${partnerInCondition}
	</if>
			group by v.sku, v.workshop_id, o.id
			) a
			order by a.partner_id, a.workshop_id, a.sku, a.order_flag
</select>

<select id="queryDetailForDailyReportEmail" parameterType="map" resultMap="BaseResultMap">
select distinct l2.id partner_id, t3.id province_id, t2.id city_id, t1.id region_id, ws.id workshop_id, a3.id,
l2.organization_name sp_name, ws.work_shop_name workshop_name,
t1.region_name, ws.work_shop_address workshop_address,
a3.sku,
p.name product_name,
a3.capacity, a3.creation_time
from wx_v_oil_verification a3
	join wx_t_work_shop ws on a3.workshop_id=ws.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type='trade'
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_product p on a3.sku=p.sku
	where 1=1
	<if test="dateFrom3 != null">
	and a3.creation_time &gt;= #{dateFrom3}
	</if>
	<if test="dateTo3 != null">
	and a3.creation_time &lt; #{dateTo3}
	</if>
	<if test="partnerInCondition != null">
		and l2.id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and a3.creation_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
	order by l2.id, t3.id, t2.id, t1.id, ws.id, a3.sku
</select>

<select id="queryOilVerificationList" parameterType="map" resultMap="BaseResultMap">
		SELECT
			v.*,
			o.organization_name,
			o.id partner_id,
			dr.id distribution_rule_id,
			r.id verification_rule_id,
			e.name AS mechanic_name,
			e.employee_type as employee_type,
			(select u.ch_name from wx_t_user u where u.user_id = ws.excute_user_id) as workshop_exec_user_name,
			(select sum(d.capacity) from wx_t_verification_detail d where d.qr_code = v.qr_code) as verified_capacity,
			r.per_liter_reward as per_liter_reward,
			dr.owner_reward as owner_per_liter_reward,
			dr.mechanic_reward as mechanic_per_liter_reward
		FROM wx_v_oil_verification v
			JOIN wx_t_workshop_employee e ON v.mechanic_code = e.code
			join wx_t_work_shop ws on v.workshop_id = ws.id
			LEFT JOIN wx_t_workshop_partner wp on ws.id = wp.workshop_id
			LEFT JOIN wx_t_organization o on wp.partner_id=o.id
			left join wx_v_enabled_distribution_rule dr on dr.workshop_id = v.workshop_id
			left join wx_v_enabled_verification_rule r on r.partner_id = o.id
		WHERE 1 = 1
			and not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v.qr_code)
			<if test="workshopId != null and workshopId != ''">
			and v.workshop_id = #{workshopId}
			</if>
			AND v.creation_time between #{dateFrom, jdbcType=TIMESTAMP}
									and #{dateTo, jdbcType=TIMESTAMP}
			<if test="mechanicCode != null">
			and v.mechanic_code = #{mechanicCode}
			</if>
		<!-- <if test="status != null">
			and v.status = #{status}
			</if> -->
			<if test="partnerId != null">
				and exists (select 1 from wx_t_workshop_partner p where p.workshop_id = v.workshop_id and p.partner_id = #{partnerId})
			</if>
			<if test='userValid == "Y"'>
				and v.user_wechat_openid is not null
				and v.order_flag = 1
			</if>
			<if test='userValid == "N"'>
				and v.user_wechat_openid is not null
				and v.order_flag is null
			</if>
			<if test='closed == "Y"'>
				and exists (select 1 from wx_t_verification_detail d where d.qr_code = v.qr_code)
			</if>
			<if test="workshopName != null">
				and v.workshop_name like #{workshopName}
			</if>
</select>
<select id="querySpOilVerificationExpList" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="BaseResultMap">
		SELECT v1.workshop_id,
			max(v1.workshop_name) workshop_name,
			sum(v1.capacity) capacity,
			max(e.name) AS mechanic_name,
			max(wp1.partner_id) partner_id
		FROM wx_v_oil_verification v1
			left JOIN wx_t_workshop_employee e ON v1.mechanic_code = e.code
			left join wx_t_work_shop ws on v1.workshop_id = ws.id
			join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
		WHERE  not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v1.qr_code) $Permission_Clause$
			<if test="workshopId != null and workshopId != ''">
			and v1.workshop_id = #{workshopId}
			</if>
			<if test="dateFrom != null">
				and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			</if>
			<if test="dateTo != null">
				and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			</if>
			<!-- <if test="mechanicCode != null">
			and v1.mechanic_code = #{mechanicCode}
			</if>
		<if test="status != null">
			and v1.status = #{status}
			</if> -->
			<if test="partnerId != null">
				and wp1.partner_id = #{partnerId}
			</if>
			<if test="workshopName != null and workshopName != ''">
				and v1.workshop_name like '${workshopName}%'
			</if>
			group by v1.workshop_id, e.code
</select>
<select id="querySpOilVerificationReportExpList" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="BaseResultMap">
		SELECT v1.workshop_id,
			max(v1.workshop_name) workshop_name,
			sum(v1.capacity) capacity,
			max(e.name) AS mechanic_name,
			max(wp1.partner_id) partner_id
		FROM wx_v_oil_verification v1
			left JOIN wx_t_workshop_employee e ON v1.mechanic_code = e.code
			left join wx_t_work_shop ws on v1.workshop_id = ws.id
			join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
		WHERE 1 = 1
			and not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v1.qr_code)
			<if test="workshopId != null and workshopId != ''">
			and v1.workshop_id = #{workshopId}
			</if>
			<if test="dateFrom != null">
				and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			</if>
			<if test="dateTo != null">
				and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			</if>
			<!-- <if test="mechanicCode != null">
			and v1.mechanic_code = #{mechanicCode}
			</if>
		<if test="status != null">
			and v1.status = #{status}
			</if> -->
			<if test="partnerId != null">
				and wp1.partner_id = #{partnerId}
			</if>
			<if test="workshopName != null and workshopName != ''">
				and v1.workshop_name like '${workshopName}%'
			</if>
			group by v1.workshop_id, e.code
</select>

<select id="queryOilVerificationListWithAwardRealtime" parameterType="map" resultMap="BaseResultMap">
		SELECT
			v.*,
			e.name AS mechanic_name,
			e.employee_type as employee_type,
			(select u.ch_name from wx_t_user u where u.user_id = ws.excute_user_id) as workshop_exec_user_name,
			pay.capacity as verified_capacity,
			r.per_liter_reward as per_liter_reward,
			dr.owner_reward as owner_per_liter_reward,
			dr.mechanic_reward as mechanic_per_liter_reward,
			p.organization_name sp_name
		FROM wx_v_oil_verification v
			JOIN wx_t_workshop_employee e ON v.mechanic_code = e.code
			join wx_t_work_shop ws on v.workshop_id = ws.id
			join wx_t_mechanic_verification_pay pay on pay.qr_code = v.qr_code
			join wx_v_enabled_distribution_rule dr on dr.workshop_id = v.workshop_id
			join wx_v_enabled_verification_rule r on r.partner_id = dr.partner_id
			join wx_t_organization p on p.id=dr.partner_id
		WHERE 1 = 1
			and exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = v.qr_code)
			<if test="workshopId != null and workshopId != ''">
			and v.workshop_id = #{workshopId}
			</if>
			AND v.creation_time between #{dateFrom, jdbcType=TIMESTAMP}
									and #{dateTo, jdbcType=TIMESTAMP}
			<if test="mechanicCode != null">
			and v.mechanic_code = #{mechanicCode}
			</if>
			<if test="partnerId != null">
				and exists (select 1 from wx_t_workshop_partner p where p.workshop_id = v.workshop_id and p.partner_id = #{partnerId})
			</if>
			<if test="workshopName != null">
				and v.workshop_name like #{workshopName}
			</if>
	order by v.creation_time desc
</select>

<select id="getVerifiedTotalCapacityByMechanic" parameterType="map" resultType="Long">
	SELECT sum(t.capacity) as total
	FROM (
		SELECT v.capacity
		FROM wx_v_oil_verification v JOIN wx_t_mechanic_verification_pay pay ON pay.qr_code = v.qr_code
		WHERE v.mechanic_code = #{mechanicCode} AND v.creation_time BETWEEN #{dateFrom} AND #{dateTo}

		UNION ALL

		SELECT v.capacity
		FROM wx_v_oil_verification v JOIN wx_t_verification_detail d ON d.qr_code = v.qr_code
			JOIN wx_t_verification_batch b ON b.id = d.batch_id
		WHERE b.status = 'APPROVED' AND v.mechanic_code = #{mechanicCode} AND v.creation_time BETWEEN #{dateFrom} AND #{dateTo}

		) t

</select>

<resultMap id="ReportView" type="com.chevron.pms.model.OilVerificationReportView">
	<result column="capacity" jdbcType="INTEGER" property="capacity" />
	<result column="create_date" jdbcType="VARCHAR" property="createDate" />
	<result column="employee_type" jdbcType="VARCHAR" property="employeeType"/>
	<result column="work_shop_name" jdbcType="VARCHAR" property="workshopName"/>
	<result column="emp_name" jdbcType="VARCHAR" property="empName"/>
	<result column="workshop_id" jdbcType="BIGINT" property="workshopId"/>
	<result column="emp_id" jdbcType="BIGINT" property="empId"/>
	<result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
	<result column="partner_id" jdbcType="BIGINT" property="partnerId"/>
	<result column="user_flag" jdbcType="INTEGER" property="userFlag"/>
	<result column="report_time" jdbcType="INTEGER" property="time"/>
	<result column="report_count" jdbcType="INTEGER" property="count"/>
	<result column="report_type" jdbcType="INTEGER" property="reportType"/>
	<result column="report_date" jdbcType="VARCHAR" property="reportDate"/>
	<result column="rank" jdbcType="INTEGER" property="rank"/>
</resultMap>
<!--
select DATEDIFF(day, creation_time, #{dateTo, jdbcType=TIMESTAMP}) report_time, capacity, partner_id, partner_name, 1 report_type
from (
select min(v.creation_time) creation_time, sum(v.capacity) capacity, wp1.partner_id, max(o1.organization_name) partner_name
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on v.workshop_id=wp1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on wp1.partner_id=o1.id
	where v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
group by wp1.partner_id
) a
-->
<select id="queryIndexTDReport" parameterType="map" resultMap="ReportView">
select DATEDIFF(day, CONVERT(datetime, min(create_date), 111), #{dateTo, jdbcType=TIMESTAMP}) + 1 report_time, count(1) report_count, a.partner_id, max(o1.organization_name) partner_name, 1 report_type
from (select create_date, partner_id from
(
select CONVERT(varchar(100), v.creation_time, 111) create_date, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on v.workshop_id=wp1.workshop_id and wp1.relation_type='trade'
	where v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
) b group by create_date, partner_id) a
join wx_t_organization o1 on a.partner_id=o1.id
group by a.partner_id
union all
select ${monthDay} report_time, count(1) report_count, a.partner_id, max(o1.organization_name) partner_name, 2 report_type
from (select create_date, partner_id from
(
select CONVERT(varchar(100), v.creation_time, 111) create_date, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on v.workshop_id=wp1.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
) b group by create_date, partner_id) a
join wx_t_organization o1 on a.partner_id=o1.id
group by a.partner_id
union all
select ${weekDay} report_time, count(1) report_count, a.partner_id, max(o1.organization_name) partner_name, 3 report_type
from (select create_date, partner_id from
(
select CONVERT(varchar(100), v.creation_time, 111) create_date, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on v.workshop_id=wp1.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
									and v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
) b group by create_date, partner_id) a
join wx_t_organization o1 on a.partner_id=o1.id
group by a.partner_id
</select>
<select id="querySpKpiReportByEmp" parameterType="map" resultMap="ReportView">
select sum(v.capacity) capacity, max(e.name) emp_name, e.id emp_id, v.workshop_id, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_employee e on v.mechanic_code = e.code
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	<if test="workshopId != null">
	and v.workshop_id = #{workshopId}
	</if>
group by wp1.partner_id,v.workshop_id,e.id
</select>
<select id="querySpKpiReportByWs" parameterType="map" resultMap="ReportView">
select sum(v.capacity) capacity, max(ws.work_shop_name) work_shop_name, ws.id workshop_id, t_p.id partner_id
from wx_v_oil_verification v
join wx_t_workshop_employee e on v.mechanic_code = e.code
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
left join wx_t_work_shop ws on v.workshop_id = ws.id
left join wx_t_organization t_p on t_p.id=wp1.partner_id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	<if test="partnerId != null">
	and wp1.partner_id = #{partnerId}
	</if>
group by t_p.id,ws.id
</select>
<select id="querySpKpiReportBySp" parameterType="map" resultMap="ReportView">
/*sellout*/
/*核销*/
select sum(a.capacity) capacity, max(t_p.organization_name) partner_name, a.partner_id, a.report_type from
(select wp1.partner_id, v.capacity, 1 report_type
from wx_v_oil_verification v
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
/*大地服务单*/
union all
select 279, convert(float, p.capacity) * ol1.amount, 1 report_type
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where order_type='DA' and source in ('DDBX', 'HZYYFW')
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
union all
/*易修车服务单*/
select 56321, convert(float, p.capacity) * ol1.amount, 1 report_type
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where order_type='DA' and source = 'ODYXC'
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

union all
/*滴滴养车服务单*/
select 56317, convert(float, p.capacity) * ol1.amount, 1 report_type
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where order_type='DA' and source='DDYYFW'
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
/*sellin*/
		union all
/*合伙人采购订单*/
select po1.partner_id, convert(int, round(po1.total_liter_count, 0)) capacity, 2
from  wx_t_partner_order po1
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
where po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
union all
/*大地服务包订单*/
select 279, convert(float, p.capacity) * ol1.amount, 2
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where source in ('DDBX', 'HZYYFW') and service_acount>remaining_service_times
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
/*易养车服务包订单*/
union all select 56321, convert(float, p.capacity) * ol1.amount, 2
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where order_type='DP' and source='ODYXC'
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
/*滴滴服务订单*/
union all select 56317, convert(float, p.capacity) * ol1.amount, 2
from wx_t_order o1 join wx_t_order_line ol1 on ol1.order_id=o1.id
left join wx_t_product p on p.sku=ol1.sku
where order_type='DA' and source='DDYYFW'
and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) a
left join wx_t_organization t_p on t_p.id=a.partner_id
where 1=1 $Permission_Clause$
group by a.partner_id, a.report_type
</select>
<select id="queryOilVerificationReport" parameterType="map" resultMap="ReportView">
select sum(capacity) capacity, create_date, employee_type,user_flag, partner_id from ( select * from (
select wp1.partner_id, CONVERT(varchar(100), v.creation_time, 23) create_date, v.capacity, e.employee_type,
case when v.user_wechat_openid is null then 0 else 1 end user_flag,v.workshop_id
from wx_v_oil_verification v
join wx_t_workshop_employee e on v.mechanic_code = e.code
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
left join wx_t_work_shop ws on v.workshop_id = ws.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
) p0 where 1=1 $Permission_Clause$
) a group by create_date, employee_type,user_flag, partner_id
</select>
<select id="queryOilVerificationReportByYear" parameterType="map" resultMap="ReportView">
select sum(capacity) capacity, create_date, employee_type,user_flag,partner_id from (select * from (
select wp1.partner_id, MONTH(v.creation_time) create_date, v.capacity, e.employee_type, case when v.user_wechat_openid is null then 0 else 1 end user_flag, v.workshop_id
from wx_v_oil_verification v
join wx_t_workshop_employee e on v.mechanic_code = e.code
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
left join wx_t_work_shop ws on v.workshop_id = ws.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
									and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
									) p0 where 1=1 $Permission_Clause$
) a group by create_date, employee_type,user_flag, partner_id
</select>

<resultMap id="SumReportView" type="com.chevron.pms.model.OilVerificationSumReportView">
	<result column="capacity" jdbcType="INTEGER" property="capacity" />
</resultMap>
<select id="queryOilVerificationSumReport" parameterType="map" resultType="Long">
	select sum(v.capacity) capacity
	from wx_v_oil_verification v
	left join wx_t_work_shop ws on v.workshop_id = ws.id
		left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
		left join wx_t_region t1 on ws.region_id=t1.id
		left join wx_t_region t2 on t1.parent_id=t2.id
		where 1=1 $Permission_Clause$
</select>
<select id="queryOilVerificationGridReport" parameterType="map" resultMap="ReportView">
select sum(capacity) capacity, ws.work_shop_name, max(o1.organization_name) partner_name, wp1.partner_id,v.workshop_id
from wx_v_oil_verification v
left join wx_t_work_shop ws on v.workshop_id = ws.id
	left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
	left join wx_t_organization o1 on wp1.partner_id=o1.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
group by ws.work_shop_name,v.workshop_id, wp1.partner_id
</select>
<select id="querySpKpiReportByYear" parameterType="map" resultMap="ReportView">

<!-- 机油核销升数(sell out volume) -->
select sum(capacity) capacity, report_time, 1 report_type from (
	select a1.* from (
		select MONTH(v.creation_time) report_time, v.capacity, wp1.partner_id
		from wx_v_oil_verification v
		left join wx_t_workshop_partner wp1
		on wp1.workshop_id=v.workshop_id
		where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 279/*大地保险和合众服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source in ('DDBX', 'HZYYFW')
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56321/*易修车服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source = 'ODYXC'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56317 /*滴滴养车服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source='DDYYFW'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by report_time

union all

<!-- 合伙人订单升数(sell in volume) -->
select sum(a.capacity) capacity, a.report_time, 2 report_type  from (
	select a1.* from (
		select po1.partner_id, MONTH(po1.create_time) report_time, convert(int, round(po1.total_liter_count, 0)) capacity
		from  wx_t_partner_order po1
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
		where 1=1
			and po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
		union all

		select 279, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*大地保险和合众已服务的服务包订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DP'
			and  source in ('DDBX', 'HZYYFW') and service_acount>remaining_service_times
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select 56321, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*易修车服务包订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DP' and source='ODYXC'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select 56317, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*滴滴养车服务订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DA' and source='DDYYFW'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by a.report_time

union all

<!-- 门店订单升数(sell through volume) -->
select sum(a.capacity) capacity, a.report_time, 3 report_type  from (
	select a1.* from (
		select wp.partner_id, convert(float, p.capacity)*ol.amount capacity,  MONTH(o.create_time) report_time
		from wx_t_order_line ol
		left join wx_t_order o on o.id = ol.order_id and (o.order_type='PA' or o.order_type='A')
		LEFT JOIN wx_t_workshop_partner wp on wp.workshop_id = o.work_shop_id
		left join wx_t_product p on p.id = ol.product_id and p.category ='JY'
		where 1=1
			and p.category ='JY'
			and o.id is not NULL
			and p.id is not NULL
			and (o.order_type='PA' or o.order_type='A')
			and o.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
	) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by a.report_time

</select>
<select id="querySpKpiReportByDay" parameterType="map" resultMap="ReportView">

<!-- 机油核销升数(sell out volume) -->
select sum(capacity) capacity, report_time, 1 report_type from (
	select a1.* from (
		select DAY(v.creation_time) report_time, v.capacity, wp1.partner_id
		from wx_v_oil_verification v
		left join wx_t_workshop_partner wp1
		on wp1.workshop_id=v.workshop_id
		where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select DAY(o1.create_time), convert(float, p.capacity) * ol1.amount, 279/*大地保险和合众服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source in ('DDBX', 'HZYYFW')
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select DAY(o1.create_time), convert(float, p.capacity) * ol1.amount, 56321/*易修车服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source = 'ODYXC'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select DAY(o1.create_time), convert(float, p.capacity) * ol1.amount, 56317 /*滴滴养车服务订单*/
		from wx_t_order o1
		join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where order_type='DA' and source='DDYYFW'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by report_time

union all

<!-- 合伙人订单升数(sell in volume) -->
select sum(a.capacity) capacity, a.report_time, 2 report_type  from (
	select a1.* from (
		select po1.partner_id, DAY(po1.create_time) report_time, convert(int, round(po1.total_liter_count, 0)) capacity
		from  wx_t_partner_order po1
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
		where 1=1
			and po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))

		union all

		select 279, DAY(o1.create_time), convert(float, p.capacity) * ol1.amount /*大地保险和合众已服务的服务包订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DP'
			and  source in ('DDBX', 'HZYYFW') and service_acount>remaining_service_times
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select 56321, DAY(o1.create_time), convert(float, p.capacity) * ol1.amount /*易修车服务包订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DP' and source='ODYXC'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

		union all

		select 56317, DAY(o1.create_time), convert(float, p.capacity) * ol1.amount /*滴滴养车服务订单*/
		from wx_t_order o1 join wx_t_order_line ol1
		on ol1.order_id=o1.id
		left join wx_t_product p
		on p.sku=ol1.sku
		where 1=1
			and order_type='DA' and source='DDYYFW'
			and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by a.report_time

union all

<!-- 门店订单升数(sell through volume) -->
select sum(a.capacity) capacity, a.report_time, 3 report_type  from (
select a1.* from (
	select wp1.partner_id, convert(float, p.capacity)*ol.amount capacity,  DAY(o.create_time) report_time
	from wx_t_order_line ol
	left join wx_t_order o on o.id = ol.order_id and (o.order_type='PA' or o.order_type='A')
	LEFT JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = o.work_shop_id
	left join wx_t_product p on p.id = ol.product_id and p.category ='JY'
	where 1=1
		and p.category ='JY'
		and o.id is not NULL
		and p.id is not NULL
		and (o.order_type='PA' or o.order_type='A')
		and o.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and o.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	) a1
	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a1.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
) a
where 1=1
	$Permission_Clause$
	<if test="partnerId != null">
		and a.partner_id = #{partnerId}
	</if>
group by a.report_time

</select>
<select id="querySpKpiReportByMonth" parameterType="map" resultMap="ReportView">
	select sum(capacity) capacity, report_time from (
	select DAY(v.creation_time) report_time, v.capacity, wp1.partner_id, v.workshop_id,en.new_sp_flag
	from wx_v_oil_verification v
	left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
	LEFT JOIN wx_t_partner_o2o_enterprise en ON en.partner_id=wp1.partner_id
		where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			<if test="partnerId != null">
				and wp1.partner_id = #{partnerId}
			</if>
			<if test="workshopId != null">
				and v.workshop_id=#{workshopId}
			</if>
			<if test="newSp != null">
				and en.new_sp_flag = #{newSp}
			</if>
	) a where 1=1 $Permission_Clause$ group by report_time
</select>
<select id="querySpKpiReportByEmpMonth" parameterType="map" resultMap="ReportView">
	select sum(capacity) capacity, report_time,partner_id, emp_name, emp_id from (
	select DAY(v.creation_time) report_time, v.capacity, wp1.partner_id, e.name + '-' + e.mobile as emp_name, e.id emp_id
	from wx_v_oil_verification v
join wx_t_workshop_employee e on v.mechanic_code = e.code
	left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id
		where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			<if test="workshopId != null">
				and v.workshop_id=#{workshopId}
			</if>
	) a group by report_time, partner_id, emp_name, emp_id
</select>
<select id="querySaleDayReport" parameterType="map" resultMap="ReportView">
select 1 report_type, sum(v.capacity) capacity, max(t_p.organization_name) partner_name, t_p.id partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
join wx_t_organization t_p on t_p.id=wp1.partner_id
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
	<if test="partnerInCondition != null">
		and wp1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and v.creation_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by t_p.id
union all
select 2 report_type, sum(v.capacity) capacity, max(t_p.organization_name) partner_name, t_p.id partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
join wx_t_organization t_p on t_p.id=wp1.partner_id
	where v.creation_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo1, jdbcType=TIMESTAMP}
	<if test="partnerInCondition != null">
		and wp1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and v.creation_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by t_p.id
union all
select 3 report_type, sum(v.capacity) capacity, max(t_p.organization_name) partner_name, t_p.id partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
join wx_t_organization t_p on t_p.id=wp1.partner_id
	where 1=1
	<if test="partnerInCondition != null">
		and wp1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and v.creation_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by t_p.id
union all
select 4 report_type, sum(convert(int, round(po1.total_liter_count, 0))) capacity, max(t_p.organization_name) partner_name, po1.partner_id
from  wx_t_partner_order po1 
join wx_t_organization t_p on t_p.id=po1.partner_id
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
where po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
	<if test="partnerInCondition != null">
		and po1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and po1.create_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by po1.partner_id

union all
select 5 report_type, sum(convert(int, round(po1.total_liter_count, 0))) capacity, max(t_p.organization_name) partner_name, po1.partner_id
from  wx_t_partner_order po1
join wx_t_organization t_p on t_p.id=po1.partner_id
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
where po1.create_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
		and po1.create_time &lt; #{dateTo1, jdbcType=TIMESTAMP}
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
	<if test="partnerInCondition != null">
		and po1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and po1.create_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by po1.partner_id
union all
select 6 report_type, sum(convert(int, round(po1.total_liter_count, 0))) capacity, max(t_p.organization_name) partner_name, po1.partner_id
from  wx_t_partner_order po1 
join wx_t_organization t_p on t_p.id=po1.partner_id
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
	where 1=1
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
	<if test="partnerInCondition != null">
		and po1.partner_id in ${partnerInCondition}
	</if>
	<if test="startTime != null">
	and po1.create_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
group by po1.partner_id
</select>
<select id="querySaleDailyReportForInte" parameterType="map" resultMap="ReportView">
select tt.*, t_p.organization_name partner_name from (
select 1 report_type, xx.capacity, xx.partner_id, row_number() over(order by xx.capacity desc) as rank from (
select sum(v.capacity) capacity, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
group by wp1.partner_id) xx
union all
select 2 report_type, xx.capacity, xx.partner_id, row_number() over(order by xx.capacity desc) as rank from (
select sum(v.capacity) capacity, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo1, jdbcType=TIMESTAMP}
group by wp1.partner_id
) xx
union all
select 3 report_type, xx.capacity, xx.partner_id, row_number() over(order by xx.capacity desc) as rank from (
select sum(v.capacity) capacity, wp1.partner_id
from wx_v_oil_verification v
join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom2, jdbcType=TIMESTAMP}
	and v.creation_time &lt;= #{dateTo2, jdbcType=TIMESTAMP}
group by wp1.partner_id) xx
) tt
join wx_t_organization t_p on t_p.id=tt.partner_id
where 1=1
	<if test="partnerInCondition != null">
		and tt.partner_id in ${partnerInCondition}
	</if>
</select>
<resultMap id="OilVerificationView" type="com.chevron.pms.model.OilVerificationView">
	<result column="capacity" jdbcType="DOUBLE" property="capacity" />
	<result column="workshop_id" jdbcType="VARCHAR" property="workshopId" />
	<result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
	<result column="submitted_capacity" jdbcType="DOUBLE" property="submittedCapacity"/>
	<result column="approved_capacity" jdbcType="DOUBLE" property="approvedCapacity"/>
	<result column="per_liter_reward" property="spPerLiterReward" jdbcType="DOUBLE"/>
	<result column="owner_per_liter_reward" property="ownerPerLiterReward" jdbcType="DOUBLE"/>
	<result column="mechanic_per_liter_reward" property="mechanicPerLiterReward" jdbcType="DOUBLE"/>
	<result column="partner_id" jdbcType="BIGINT" property="partnerId" />
	<result column="distribution_rule_id" jdbcType="BIGINT" property="wsDistributionRuleId" />
	<result column="verification_rule_id" jdbcType="BIGINT" property="spDistributionRuleId" />
	<result column="partner_name" jdbcType="VARCHAR" property="partnerName" />
	<result column="total_reward" property="totalReward" jdbcType="DOUBLE"/>
	<result column="owner_reward" property="ownerReward" jdbcType="DOUBLE"/>
	<result column="mechanic_reward" property="mechanicReward" jdbcType="DOUBLE"/>
	<result column="unsubmit_verification_capacity" jdbcType="DOUBLE" property="unsubmitVerificationCapacity" />
	<result column="unsubmit_o2o_capacity" jdbcType="DOUBLE" property="unsubmitO2oCapacity" />
	<result column="unsubmit_capacity" jdbcType="DOUBLE" property="unsubmitCapacity" />
	<result column="o2o_capacity" jdbcType="DOUBLE" property="o2oCapacity" />
	<result column="disable_capacity" jdbcType="DOUBLE" property="disableCapacity" />
	<result column="unsubmit_o2o_amount" property="unsubmitO2oAmount" jdbcType="DOUBLE"/>
	<result column="unsubmit_verification_amount" property="unsubmitVerificationAmount" jdbcType="DOUBLE"/>
</resultMap>
<!--    <select id="querySpOilVerification" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select v1.workshop_id, v1.capacity, v1.submitted_capacity, v1.approved_capacity, ws1.work_shop_name workshop_name,
o1.organization_name partner_name, o1.id partner_id,
			dr.id distribution_rule_id,
			r.id verification_rule_id,
			(r.per_liter_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as total_reward,
			(dr.owner_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as owner_reward,
			(dr.mechanic_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as mechanic_reward
from (
select mq1.workshop_id, sum(CONVERT(int, p1.capacity)) capacity,
isnull(sum(case when vb1.status='SUBMIT' then vd1.capacity else 0 end), 0) submitted_capacity,
isnull(sum(case when vb1.status='APPROVED' then vd1.capacity else 0 end), 0) approved_capacity
from wx_t_mechanic_qrcode mq1
join wx_t_product p1 on p1.sku=mq1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=mq1.qr_code
left join wx_t_verification_batch vb1 on vb1.id=vd1.batch_id
where not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = mq1.qr_code)
	<if test="dateFrom != null">
		and mq1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and mq1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and mq1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
group by mq1.workshop_id) v1
join wx_t_work_shop ws1 on v1.workshop_id=ws1.id
join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_v_enabled_distribution_rule dr on dr.workshop_id = v1.workshop_id
left join wx_v_enabled_verification_rule r on r.partner_id = o1.id
where v1.capacity>v1.approved_capacity+v1.submitted_capacity $Permission_Clause$
	<if test="workshopName != null and workshopName != ''">
		and ws1.work_shop_name like '${workshopName}%'
	</if>
	<if test="partnerId != null">
		and o1.id = #{partnerId, jdbcType=BIGINT}
	</if>
</select>  -->
<select id="querySpOilVerification" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select c.*, c.unsubmit_verification_capacity * vr.per_liter_reward unsubmit_verification_amount,
vr.per_liter_reward, vr.id verification_rule_id, dr.id distribution_rule_id, o.organization_name partner_name,
ws.work_shop_name workshop_name from (
select sum(b.capacity) capacity, sum(b.unsubmit_verification_capacity) unsubmit_verification_capacity,
sum(b.unsubmit_o2o_capacity) unsubmit_o2o_capacity, sum(b.unsubmit_o2o_amount) unsubmit_o2o_amount, b.partner_id, b.workshop_id from (
select a.*, case when a.unsubmit_o2o_capacity > 0 then a.unsubmit_o2o_capacity * (select vr1.per_liter_reward
		from wx_t_verification_rule vr1
		where vr1.partner_id=a.qrcode_partner_id and vr1.enable_flag='Y') else 0 end unsubmit_o2o_amount from (
select convert(int, p.capacity) capacity, v1.workshop_id, wp1.partner_id, v1.qrcode_partner_id,
case when (v1.scan_type is null or v1.scan_type='VERIFICATION') and (v1.enable_flag is null or v1.enable_flag=1) and
		not exists (select 1 from wx_t_verification_detail vd where vd.qr_code=v1.qr_code)
	then convert(int, p.capacity) else 0 end unsubmit_verification_capacity,
case when v1.scan_type='O2O' and not exists (select 1 from wx_t_verification_detail vd where vd.qr_code=v1.qr_code)
	then convert(int, p.capacity) else 0 end unsubmit_o2o_capacity
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
where 1=1 $Permission_Clause$
	<if test="dateFrom != null">
		and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT}
	</if>
) a where a.unsubmit_o2o_capacity + a.unsubmit_verification_capacity > 0 ) b
group by b.partner_id, b.workshop_id ) c
join wx_t_organization o on o.id=c.partner_id
join wx_t_work_shop ws on ws.id=c.workshop_id
left join wx_t_verification_rule vr on vr.partner_id=c.partner_id and vr.enable_flag='Y'
left join wx_t_ws_distribution_rule dr on dr.workshop_id=c.workshop_id and dr.enable_flag='Y'
where 1=1
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name like '${workshopName}%'
	</if>
</select>
<select id="querySpOilVerificationSum" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select sum(d.capacity) capacity, sum(d.unsubmit_verification_amount) unsubmit_verification_amount,
sum(d.unsubmit_o2o_amount) unsubmit_o2o_amount, sum(d.unsubmit_o2o_capacity) unsubmit_o2o_capacity,
sum(d.unsubmit_verification_capacity) unsubmit_verification_capacity from
(select c.*, c.unsubmit_verification_capacity * vr.per_liter_reward unsubmit_verification_amount from (
select sum(b.capacity) capacity, sum(b.unsubmit_verification_capacity) unsubmit_verification_capacity,
sum(b.unsubmit_o2o_capacity) unsubmit_o2o_capacity, sum(b.unsubmit_o2o_amount) unsubmit_o2o_amount, b.partner_id, b.workshop_id from (
select a.*, case when a.unsubmit_o2o_capacity > 0 then a.unsubmit_o2o_capacity * (select vr1.per_liter_reward
		from wx_t_verification_rule vr1
		where vr1.partner_id=a.qrcode_partner_id and vr1.enable_flag='Y') else 0 end unsubmit_o2o_amount from (
select convert(int, p.capacity) capacity, v1.workshop_id, wp1.partner_id, v1.qrcode_partner_id,
case when (v1.scan_type is null or v1.scan_type='VERIFICATION') and (v1.enable_flag is null or v1.enable_flag=1) and
		not exists (select 1 from wx_t_verification_detail vd where vd.qr_code=v1.qr_code)
	then convert(int, p.capacity) else 0 end unsubmit_verification_capacity,
case when v1.scan_type='O2O' and not exists (select 1 from wx_t_verification_detail vd where vd.qr_code=v1.qr_code)
	then convert(int, p.capacity) else 0 end unsubmit_o2o_capacity
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
where 1=1 $Permission_Clause$
	<if test="dateFrom != null">
		and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT}
	</if>
) a where a.unsubmit_o2o_capacity + a.unsubmit_verification_capacity > 0 ) b
group by b.partner_id, b.workshop_id ) c
join wx_t_organization o on o.id=c.partner_id
join wx_t_work_shop ws on ws.id=c.workshop_id
left join wx_t_verification_rule vr on vr.partner_id=c.partner_id and vr.enable_flag='Y'
where 1=1
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name like '${workshopName}%'
	</if>
	) d
</select>
<select id="querySpOilVerificationForReport" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select c.workshop_id, c.partner_id, sum(c.capacity) capacity, sum(c.approved_capacity) approved_capacity,
sum(c.capacity - c.disable_capacity - c.submitted_capacity - c.approved_capacity) unsubmit_capacity,
sum(c.disable_capacity) disable_capacity, sum(c.o2o_capacity) o2o_capacity, sum(c.submitted_capacity) submitted_capacity,
max(o.organization_name) partner_name, max(ws.work_shop_name) workshop_name from (
select convert(int, p.capacity) capacity, v1.workshop_id, wp1.partner_id,
case when v1.enable_flag=0 then convert(int, p.capacity) else 0 end disable_capacity,
case when exists (select 1 from wx_t_verification_detail vd
left join wx_t_verification_batch vb on vb.id=vd.batch_id where vd.qr_code=v1.qr_code and vb.status='SUBMIT')
	then convert(int, p.capacity) else 0 end submitted_capacity,
case when exists (select 1 from wx_t_verification_detail vd
left join wx_t_verification_batch vb on vb.id=vd.batch_id where vd.qr_code=v1.qr_code and vb.status='APPROVED')
	then convert(int, p.capacity) else 0 end approved_capacity,
case when v1.scan_type='O2O' then convert(int, p.capacity) else 0 end o2o_capacity
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
where 1=1 $Permission_Clause$
	<if test="dateFrom != null">
		and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT}
	</if>
) c
join wx_t_organization o on o.id=c.partner_id
join wx_t_work_shop ws on ws.id=c.workshop_id
where 1=1
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name like '${workshopName}%'
	</if>
group by c.workshop_id, c.partner_id
</select>
<select id="querySpOilVerificationForReportSum" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select sum(d.capacity) capacity, sum(d.approved_capacity) approved_capacity, sum(d.disable_capacity) disable_capacity,
sum(d.capacity - d.disable_capacity - d.submitted_capacity - d.approved_capacity) unsubmit_capacity,
sum(d.o2o_capacity) o2o_capacity, sum(d.submitted_capacity) submitted_capacity from (
select c.workshop_id, c.partner_id, sum(c.capacity) capacity, sum(c.approved_capacity) approved_capacity,
sum(c.disable_capacity) disable_capacity, sum(c.o2o_capacity) o2o_capacity, sum(c.submitted_capacity) submitted_capacity from (
select convert(int, p.capacity) capacity, v1.workshop_id, wp1.partner_id,
case when v1.enable_flag=0 then convert(int, p.capacity) else 0 end disable_capacity,
case when exists (select 1 from wx_t_verification_detail vd
left join wx_t_verification_batch vb on vb.id=vd.batch_id where vd.qr_code=v1.qr_code and vb.status='SUBMIT')
	then convert(int, p.capacity) else 0 end submitted_capacity,
case when exists (select 1 from wx_t_verification_detail vd
left join wx_t_verification_batch vb on vb.id=vd.batch_id where vd.qr_code=v1.qr_code and vb.status='APPROVED')
	then convert(int, p.capacity) else 0 end approved_capacity,
case when v1.scan_type='O2O' then convert(int, p.capacity) else 0 end o2o_capacity
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
where 1=1 $Permission_Clause$
	<if test="dateFrom != null">
		and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT}
	</if>
) c
join wx_t_organization o on o.id=c.partner_id
join wx_t_work_shop ws on ws.id=c.workshop_id
where 1=1
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name like '${workshopName}%'
	</if>
group by c.workshop_id, c.partner_id ) d
</select>
<!--  <select id="querySpOilVerificationSum" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select sum(v1.capacity) capacity, sum(v1.submitted_capacity) submitted_capacity, sum(v1.approved_capacity) approved_capacity,
			sum(r.per_liter_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as total_reward,
			sum(dr.owner_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as owner_reward,
			sum(dr.mechanic_reward * (v1.capacity - v1.submitted_capacity - v1.approved_capacity)) as mechanic_reward
from (
select mq1.workshop_id, sum(CONVERT(int, p1.capacity)) capacity,
isnull(sum(case when vb1.status='SUBMIT' then vd1.capacity else 0 end), 0) submitted_capacity,
isnull(sum(case when vb1.status='APPROVED' then vd1.capacity else 0 end), 0) approved_capacity
from wx_t_mechanic_qrcode mq1
join wx_t_product p1 on p1.sku=mq1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=mq1.qr_code
left join wx_t_verification_batch vb1 on vb1.id=vd1.batch_id
where not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = mq1.qr_code)
	<if test="dateFrom != null">
		and mq1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and mq1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and mq1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
group by mq1.workshop_id) v1
join wx_t_work_shop ws1 on v1.workshop_id=ws1.id
join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_v_enabled_distribution_rule dr on dr.workshop_id = v1.workshop_id
left join wx_v_enabled_verification_rule r on r.partner_id = o1.id
where v1.capacity>v1.approved_capacity+v1.submitted_capacity $Permission_Clause$
	<if test="workshopName != null and workshopName != ''">
		and ws1.work_shop_name like '${workshopName}%'
	</if>
	<if test="partnerId != null">
		and o1.id = #{partnerId, jdbcType=BIGINT}
	</if>
</select>  -->
<select id="queryUnsetRuleVerificationData" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select v1.workshop_id, ws1.work_shop_name workshop_name,
o1.organization_name partner_name, o1.id partner_id,
			dr.id distribution_rule_id,
			r.id verification_rule_id,
			r.per_liter_reward as total_reward
from (
select mq1.workshop_id, sum(CONVERT(int, p1.capacity)) capacity,
isnull(sum(case when vb1.status='SUBMIT' then vd1.capacity else 0 end), 0) submitted_capacity,
isnull(sum(case when vb1.status='APPROVED' then vd1.capacity else 0 end), 0) approved_capacity
from wx_t_mechanic_qrcode mq1
join wx_t_product p1 on p1.sku=mq1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=mq1.qr_code
left join wx_t_verification_batch vb1 on vb1.id=vd1.batch_id
where not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = mq1.qr_code)
	<if test="dateFrom != null">
		and mq1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="dateTo != null">
		and mq1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and mq1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
group by mq1.workshop_id) v1
join wx_t_work_shop ws1 on v1.workshop_id=ws1.id
join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on o1.id=wp1.partner_id
left join wx_v_enabled_distribution_rule dr on dr.workshop_id = v1.workshop_id
left join wx_v_enabled_verification_rule r on r.partner_id = o1.id
where v1.capacity>v1.approved_capacity+v1.submitted_capacity and dr.id is null $Permission_Clause$
	<if test="workshopName != null and workshopName != ''">
		and ws1.work_shop_name like '${workshopName}%'
	</if>
	<if test="partnerId != null">
		and o1.id = #{partnerId, jdbcType=BIGINT}
	</if>
</select>
<select id="querySpOilVerificationReport" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select v1.workshop_id, v1.capacity, v1.submitted_capacity, v1.approved_capacity, ws1.work_shop_name workshop_name,
o1.organization_name partner_name, o1.id partner_id
from (
select mq1.workshop_id, sum(CONVERT(int, p1.capacity)) capacity,
isnull(sum(case when vb1.status='SUBMIT' then vd1.capacity else 0 end), 0) submitted_capacity,
isnull(sum(case when vb1.status='APPROVED' then vd1.capacity else 0 end), 0) approved_capacity
from wx_t_mechanic_qrcode mq1
join wx_t_product p1 on p1.sku=mq1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=mq1.qr_code
left join wx_t_verification_batch vb1 on vb1.id=vd1.batch_id
where not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = mq1.qr_code)
	<if test="dateFrom != null">
		and mq1.creation_time &gt;= '${dateFromStr}'
	</if>
	<if test="dateTo != null">
		and mq1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and mq1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
group by mq1.workshop_id) v1
join wx_t_work_shop ws1 on v1.workshop_id=ws1.id
join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on o1.id=wp1.partner_id
where 1=1 $Permission_Clause$
	<if test="workshopName != null and workshopName != ''">
		and ws1.work_shop_name like '${workshopName}%'
	</if>
	<if test="partnerId != null">
		and o1.id = #{partnerId, jdbcType=BIGINT}
	</if>
</select>
<select id="querySpOilVerificationReportSum" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="OilVerificationView">
select sum(v1.capacity) capacity, sum(v1.submitted_capacity) submitted_capacity, sum(v1.approved_capacity) approved_capacity
from (
select mq1.workshop_id, sum(CONVERT(int, p1.capacity)) capacity,
isnull(sum(case when vb1.status='SUBMIT' then vd1.capacity else 0 end), 0) submitted_capacity,
isnull(sum(case when vb1.status='APPROVED' then vd1.capacity else 0 end), 0) approved_capacity
from wx_t_mechanic_qrcode mq1
join wx_t_product p1 on p1.sku=mq1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=mq1.qr_code
left join wx_t_verification_batch vb1 on vb1.id=vd1.batch_id
where not exists (select 1 from wx_t_mechanic_verification_pay pay where pay.qr_code = mq1.qr_code)
	<if test="dateFrom != null">
		and mq1.creation_time &gt;= '${dateFromStr}'
	</if>
	<if test="dateTo != null">
		and mq1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="workshopId != null">
		and mq1.workshopId = #{workshopId, jdbcType=BIGINT}
	</if>
group by mq1.workshop_id) v1
join wx_t_work_shop ws1 on v1.workshop_id=ws1.id
join wx_t_workshop_partner wp1 on wp1.workshop_id=v1.workshop_id and wp1.relation_type='trade'
join wx_t_organization o1 on o1.id=wp1.partner_id
where 1=1 $Permission_Clause$
	<if test="workshopName != null and workshopName != ''">
		and ws1.work_shop_name like '${workshopName}%'
	</if>
	<if test="partnerId != null">
		and o1.id = #{partnerId, jdbcType=BIGINT}
	</if>
</select>
<select id="queryReportForMonth" parameterType="map" resultMap="ReportView">
select sum(capacity) capacity,partner_id, workshop_id, report_date from (
select substring(CONVERT(varchar(100), v.creation_time, 23), 0, 8) report_date, v.capacity, wp1.partner_id, v.workshop_id
from wx_v_oil_verification v
left join wx_t_workshop_partner wp1 on wp1.workshop_id=v.workshop_id and wp1.relation_type='trade'
	where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		<if test="partnerId != null">
			and wp1.partner_id = #{partnerId}
		</if>
		<if test="workshopId != null">
			and v.workshop_id = #{workshopId}
		</if>

) a group by partner_id, workshop_id, report_date
</select>
<select id="queryScanDetailForEmail" parameterType="map" resultMap="BaseResultMap">
select distinct l2.id, t3.id, t2.id, t1.id, ws.id, a3.id, l2.organization_name, isnull(vtm.value_after_transform, l2.organization_name) sp_name, ws.work_shop_name workshop_name,
t1.region_name, ws.work_shop_address workshop_address,
isnull(vtm1.value_after_transform, a3.sku) sku,
p.name product_name,
a3.capacity, a3.creation_time
from wx_v_oil_verification a3
	join wx_t_work_shop ws on a3.workshop_id=ws.id
	left join wx_t_region t1 on ws.region_id=t1.id
	left join wx_t_region t2 on t1.parent_id=t2.id
	left join wx_t_region t3 on t2.parent_id=t3.id
	join wx_t_workshop_partner l1 on l1.workshop_id=a3.workshop_id and l1.relation_type='trade'
	join wx_t_organization l2 on l1.partner_id=l2.id
	left join wx_t_product p on a3.sku=p.sku
	left join wx_t_value_transform_map vtm on vtm.transform_type='PartnerName' and vtm.value_before_transform=l2.organization_name
	left join wx_t_value_transform_map vtm1 on vtm1.transform_type='ProductSku' and vtm1.value_before_transform=a3.sku
	where 1=1
	<if test="dateFrom != null">
	and a3.creation_time&gt;=#{dateFrom}
	</if>
	<if test="dateTo != null">
	and a3.creation_time&lt;#{dateTo}
	</if>
	<if test="startTime != null">
	and a3.creation_time &gt;= #{startTime, jdbcType=TIMESTAMP}
	</if>
	order by l2.organization_name, t3.id, t2.id, t1.id, ws.id
	</select>
</mapper>