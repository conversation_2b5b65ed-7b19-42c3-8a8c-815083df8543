<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.OemProductPackagingMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.OemProductPackaging" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="NVARCHAR" />
    <result column="product_batch" property="productBatch" jdbcType="NVARCHAR" />
    <result column="product_date" property="productDate" jdbcType="TIMESTAMP" />
    <result column="product_line_name" property="productLineName" jdbcType="NVARCHAR" />
    <result column="product_banci" property="productBanci" jdbcType="NVARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="file_name" property="fileName" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, product_id, product_batch, product_date, product_line_name, product_banci, creation_time, 
    created_by, update_time,file_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.OemProductPackagingExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_oem_product_packaging
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.OemProductPackagingExample" >
    delete from wx_t_oem_product_packaging
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.OemProductPackaging" >
    insert into wx_t_oem_product_packaging (id, product_id, product_batch, 
      product_date, product_line_name, product_banci, 
      creation_time, created_by, update_time,file_name
      )
    values (#{id,jdbcType=BIGINT}, #{productId,jdbcType=NVARCHAR}, #{productBatch,jdbcType=NVARCHAR}, 
      #{productDate,jdbcType=TIMESTAMP}, #{productLineName,jdbcType=NVARCHAR}, #{productBanci,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{fileName,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.OemProductPackaging" >
    insert into wx_t_oem_product_packaging
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="productId != null" >
        product_id,
      </if>
      <if test="productBatch != null" >
        product_batch,
      </if>
      <if test="productDate != null" >
        product_date,
      </if>
      <if test="productLineName != null" >
        product_line_name,
      </if>
      <if test="productBanci != null" >
        product_banci,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=NVARCHAR},
      </if>
      <if test="productBatch != null" >
        #{productBatch,jdbcType=NVARCHAR},
      </if>
      <if test="productDate != null" >
        #{productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="productLineName != null" >
        #{productLineName,jdbcType=NVARCHAR},
      </if>
      <if test="productBanci != null" >
        #{productBanci,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_oem_product_packaging
    <set >
      <!-- <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if> -->
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=NVARCHAR},
      </if>
      <if test="record.productBatch != null" >
        product_batch = #{record.productBatch,jdbcType=NVARCHAR},
      </if>
      <if test="record.productDate != null" >
        product_date = #{record.productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productLineName != null" >
        product_line_name = #{record.productLineName,jdbcType=NVARCHAR},
      </if>
      <if test="record.productBanci != null" >
        product_banci = #{record.productBanci,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fileName != null" >
        file_name = #{record.fileName,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_oem_product_packaging
    set <!-- id = #{record.id,jdbcType=BIGINT}, -->
      product_id = #{record.productId,jdbcType=NVARCHAR},
      product_batch = #{record.productBatch,jdbcType=NVARCHAR},
      product_date = #{record.productDate,jdbcType=TIMESTAMP},
      product_line_name = #{record.productLineName,jdbcType=NVARCHAR},
      product_banci = #{record.productBanci,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      file_name = #{record.fileName,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="countProductPackingByConditionWithPagination" parameterType="com.chevron.pms.model.GenericPaginationQueryParams" resultType="long">
	SELECT count(1) from wx_t_oem_product_packaging t 
	where 1=1
	<if test="queryParamsMap.productId != null and queryParamsMap.productId != '' ">
		and t.product_id = #{queryParamsMap.productId,jdbcType=VARCHAR}
    </if>
    <if test="queryParamsMap.productBatch != null and queryParamsMap.productBatch != ''">
    	and t.product_batch = #{queryParamsMap.productBatch, jdbcType=VARCHAR}
 	</if>
 	<if test="queryParamsMap.productBanci != null and queryParamsMap.productBanci != ''">
    	and t.product_banci = #{queryParamsMap.productBanci, jdbcType=VARCHAR}
 	</if>
  </select>
  <select id="queryProductPackingByConditionWithPagination" parameterType="com.chevron.pms.model.GenericPaginationQueryParams" resultMap="BaseResultMap">
	select 
	<if test="limit != -1">
 		top ${limit}
 	</if>
	<include refid="Base_Column_List" /> 
	from 
		  (
		  	  select 
		  	    <if test="orderBy != null">
		  	    	row_number() over(order by ${orderBy}) as rownumber,
		  	    </if>
		  	  <include refid="Base_Column_List" />
		  	    from wx_t_oem_product_packaging t
			   where 1 = 1
			  <if test="queryParamsMap.productId != null and queryParamsMap.productId != '' ">
				 and t.product_id = #{queryParamsMap.productId,jdbcType=VARCHAR}
			  </if>
			  <if test="queryParamsMap.productBatch != null and queryParamsMap.productBatch != ''">
		     	and t.product_batch = #{queryParamsMap.productBatch, jdbcType=VARCHAR}
		  	  </if>
		  	  <if test="queryParamsMap.productBanci != null and queryParamsMap.productBanci != ''">
			  	and t.product_banci = #{queryParamsMap.productBanci, jdbcType=VARCHAR}
			  </if>
		  ) t 
	 where 1 = 1
	 <if test="start != -1 and orderBy != null">
	     and t.rownumber > #{start, jdbcType=INTEGER} 
	 </if> 	
  </select>
</mapper>