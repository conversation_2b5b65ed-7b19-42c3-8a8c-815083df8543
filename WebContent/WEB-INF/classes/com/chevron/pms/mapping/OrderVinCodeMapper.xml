<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.OrderVinCodeMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.OrderVinCode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vincode" property="vincode" jdbcType="NVARCHAR" />
    <result column="level_id" property="levelId" jdbcType="NVARCHAR" />
    <result column="order_source" property="orderSource" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, vincode, level_id, order_source, create_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.OrderVinCodeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_order_vincode
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_order_vincode
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_order_vincode
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.OrderVinCodeExample" >
    delete from wx_t_order_vincode
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.OrderVinCode" >
    insert into wx_t_order_vincode (id, vincode, level_id, 
      order_source, create_time)
    values (#{id,jdbcType=BIGINT}, #{vincode,jdbcType=NVARCHAR}, #{levelId,jdbcType=NVARCHAR}, 
      #{orderSource,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.OrderVinCode" >
    insert into wx_t_order_vincode
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vincode != null" >
        vincode,
      </if>
      <if test="levelId != null" >
        level_id,
      </if>
      <if test="orderSource != null" >
        order_source,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vincode != null" >
        #{vincode,jdbcType=NVARCHAR},
      </if>
      <if test="levelId != null" >
        #{levelId,jdbcType=NVARCHAR},
      </if>
      <if test="orderSource != null" >
        #{orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_order_vincode
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vincode != null" >
        vincode = #{record.vincode,jdbcType=NVARCHAR},
      </if>
      <if test="record.levelId != null" >
        level_id = #{record.levelId,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderSource != null" >
        order_source = #{record.orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_order_vincode
    set id = #{record.id,jdbcType=BIGINT},
      vincode = #{record.vincode,jdbcType=NVARCHAR},
      level_id = #{record.levelId,jdbcType=NVARCHAR},
      order_source = #{record.orderSource,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.OrderVinCode" >
    update wx_t_order_vincode
    <set >
      <if test="vincode != null" >
        vincode = #{vincode,jdbcType=NVARCHAR},
      </if>
      <if test="levelId != null" >
        level_id = #{levelId,jdbcType=NVARCHAR},
      </if>
      <if test="orderSource != null" >
        order_source = #{orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.OrderVinCode" >
    update wx_t_order_vincode
    set vincode = #{vincode,jdbcType=NVARCHAR},
      level_id = #{levelId,jdbcType=NVARCHAR},
      order_source = #{orderSource,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <select id="getVinCodesByTime" resultType="java.lang.String" parameterType="map" >
    select 
         distinct vincode
    from wx_t_order_vincode
    where
         <![CDATA[  #{startDate} <=  create_time  and ]]>
         <![CDATA[  #{endDate} >  create_time]]>
  </select>
</mapper>