<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.VehicleTypeVoMapper">
	<resultMap id="BaseResultMap" type="com.chevron.pms.model.VehicleTypeVo">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="level_id" property="levelId" jdbcType="NVARCHAR" />
		<result column="factory" property="factory" jdbcType="NVARCHAR" />
		<result column="brand" property="brand" jdbcType="NVARCHAR" />
		<result column="vehicle_series" property="vehicleSeries"
			jdbcType="NVARCHAR" />
		<result column="vehicle_type" property="vehicleType" jdbcType="NVARCHAR" />
		<result column="sales_name" property="salesName" jdbcType="NVARCHAR" />
		<result column="model_year" property="modelYear" jdbcType="INTEGER" />
		<result column="emission_standard" property="emissionStandard"
			jdbcType="NVARCHAR" />
		<result column="vehicle_category" property="vehicleCategory"
			jdbcType="NVARCHAR" />
		<result column="sales_year" property="salesYear" jdbcType="INTEGER" />
		<result column="sales_month" property="salesMonth" jdbcType="INTEGER" />
		<result column="product_year" property="productYear" jdbcType="INTEGER" />
		<result column="stop_production_year" property="stopProductionYear"
			jdbcType="INTEGER" />
		<result column="stop_production_status" property="stopProductionStatus"
			jdbcType="NVARCHAR" />
		<result column="country" property="country" jdbcType="NVARCHAR" />
		<result column="vehicle_kinds" property="vehicleKinds"
			jdbcType="NVARCHAR" />
		<result column="cylinder_volume" property="cylinderVolume"
			jdbcType="NVARCHAR" />
		<result column="displacement" property="displacement" jdbcType="NVARCHAR" />
		<result column="air_intake_form" property="airIntakeForm"
			jdbcType="NVARCHAR" />
		<result column="fuel_type" property="fuelType" jdbcType="NVARCHAR" />
		<result column="fuel_label" property="fuelLabel" jdbcType="NVARCHAR" />
		<result column="maximum_horsepower" property="maximumHorsepower"
			jdbcType="NVARCHAR" />
		<result column="maximum_power" property="maximumPower"
			jdbcType="NVARCHAR" />
		<result column="cylinder_arrangement" property="cylinderArrangement"
			jdbcType="NVARCHAR" />
		<result column="number_of_cylinders" property="numberOfCylinders"
			jdbcType="NVARCHAR" />
		<result column="valves_per_cylinder" property="valvesPerCylinder"
			jdbcType="NVARCHAR" />
		<result column="gearbox_type" property="gearboxType" jdbcType="NVARCHAR" />
		<result column="gearbox_desc" property="gearboxDesc" jdbcType="NVARCHAR" />
		<result column="number_of_gears" property="numberOfGears"
			jdbcType="NVARCHAR" />
		<result column="front_brake_type" property="frontBrakeType"
			jdbcType="NVARCHAR" />
		<result column="back_brake_type" property="backBrakeType"
			jdbcType="NVARCHAR" />
		<result column="assist_type" property="assistType" jdbcType="NVARCHAR" />
		<result column="engine_position" property="enginePosition"
			jdbcType="NVARCHAR" />
		<result column="driving_mode" property="drivingMode" jdbcType="NVARCHAR" />
		<result column="wheel_base" property="wheelBase" jdbcType="NVARCHAR" />
		<result column="door_number" property="doorNumber" jdbcType="NVARCHAR" />
		<result column="number_of_seats" property="numberOfSeats"
			jdbcType="NVARCHAR" />
		<result column="front_tire_size" property="frontTireSize"
			jdbcType="NVARCHAR" />
		<result column="back_tire_size" property="backTireSize"
			jdbcType="NVARCHAR" />
		<result column="front_boss_of_wheel_size" property="frontBossOfWheelSize"
			jdbcType="NVARCHAR" />
		<result column="back_boss_of_wheel_size" property="backBossOfWheelSize"
			jdbcType="NVARCHAR" />
		<result column="wheel_material" property="wheelMaterial"
			jdbcType="NVARCHAR" />
		<result column="spare_tire_specifications" property="spareTireSpecifications"
			jdbcType="NVARCHAR" />
		<result column="electric_skylight" property="electricSkylight"
			jdbcType="NVARCHAR" />
		<result column="panoramic_skylight" property="panoramicSkylight"
			jdbcType="NVARCHAR" />
		<result column="xenon_lamp" property="xenonLamp" jdbcType="NVARCHAR" />
		<result column="front_fog_lamp" property="frontFogLamp"
			jdbcType="NVARCHAR" />
		<result column="rear_wiper" property="rearWiper" jdbcType="NVARCHAR" />
		<result column="air_conditioner" property="airConditioner"
			jdbcType="NVARCHAR" />
		<result column="auto_air_conditioning" property="autoAirConditioning"
			jdbcType="NVARCHAR" />


	</resultMap>
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value}
									and
									#{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem"
										open="(" close=")" separator=",">
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	<sql id="Base_Column_List">
		id, level_id, factory, brand, vehicle_series, vehicle_type,
		sales_name,
		model_year,
		emission_standard, vehicle_category, sales_year,
		sales_month, product_year,
		stop_production_year,
		stop_production_status, country, vehicle_kinds, cylinder_volume,
		displacement,
		air_intake_form,
		fuel_type, fuel_label,
		maximum_horsepower, maximum_power, cylinder_arrangement,
		number_of_cylinders,
		valves_per_cylinder, gearbox_type, gearbox_desc,
		number_of_gears, front_brake_type,
		back_brake_type, assist_type,
		engine_position, driving_mode,
		wheel_base, door_number,
		number_of_seats, front_tire_size, back_tire_size,
		front_boss_of_wheel_size,
		back_boss_of_wheel_size,
		wheel_material,
		spare_tire_specifications, electric_skylight, panoramic_skylight,
		xenon_lamp, front_fog_lamp, rear_wiper, air_conditioner,
		auto_air_conditioning
	</sql>
	<select id="selectByExample" resultMap="BaseResultMap"
		parameterType="com.chevron.pms.model.VehicleTypeVoExample">
		select
		<if test="distinct">
			distinct
		</if>
		'false' as QUERYID,
		<include refid="Base_Column_List" />
		from wx_t_vehicle_type
		<if test="_parameter != null">
			<include refid="Example_Where_Clause" />
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from wx_t_vehicle_type
		where id = #{id,jdbcType=BIGINT}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from
		wx_t_vehicle_type
		where id = #{id,jdbcType=BIGINT}
	</delete>
	<insert id="insert" parameterType="com.chevron.pms.model.VehicleTypeVo">
		insert into wx_t_vehicle_type
		(level_id, factory,
		brand, vehicle_series, vehicle_type,
		sales_name,
		model_year, emission_standard,
		vehicle_category, sales_year,
		sales_month,
		product_year, stop_production_year,
		stop_production_status,
		country, vehicle_kinds, cylinder_volume,
		displacement, air_intake_form, fuel_type,
		fuel_label,
		maximum_horsepower, maximum_power,
		cylinder_arrangement,
		number_of_cylinders,
		valves_per_cylinder, gearbox_type, gearbox_desc,
		number_of_gears, front_brake_type, back_brake_type,
		assist_type,
		engine_position, driving_mode,
		wheel_base, door_number,
		number_of_seats,
		front_tire_size, back_tire_size,
		front_boss_of_wheel_size,
		back_boss_of_wheel_size, wheel_material,
		spare_tire_specifications,
		electric_skylight, panoramic_skylight,
		xenon_lamp, front_fog_lamp, rear_wiper,
		air_conditioner,
		auto_air_conditioning)
		values (#{levelId,jdbcType=NVARCHAR},
		#{factory,jdbcType=NVARCHAR},
		#{brand,jdbcType=NVARCHAR},
		#{vehicleSeries,jdbcType=NVARCHAR},
		#{vehicleType,jdbcType=NVARCHAR},
		#{salesName,jdbcType=NVARCHAR}, #{modelYear,jdbcType=INTEGER},
		#{emissionStandard,jdbcType=NVARCHAR},
		#{vehicleCategory,jdbcType=NVARCHAR}, #{salesYear,jdbcType=INTEGER},
		#{salesMonth,jdbcType=INTEGER},
		#{productYear,jdbcType=INTEGER},
		#{stopProductionYear,jdbcType=INTEGER},
		#{stopProductionStatus,jdbcType=NVARCHAR},
		#{country,jdbcType=NVARCHAR}, #{vehicleKinds,jdbcType=NVARCHAR},
		#{cylinderVolume,jdbcType=NVARCHAR},
		#{displacement,jdbcType=NVARCHAR}, #{airIntakeForm,jdbcType=NVARCHAR},
		#{fuelType,jdbcType=NVARCHAR},
		#{fuelLabel,jdbcType=NVARCHAR},
		#{maximumHorsepower,jdbcType=NVARCHAR},
		#{maximumPower,jdbcType=NVARCHAR},
		#{cylinderArrangement,jdbcType=NVARCHAR},
		#{numberOfCylinders,jdbcType=NVARCHAR},
		#{valvesPerCylinder,jdbcType=NVARCHAR},
		#{gearboxType,jdbcType=NVARCHAR}, #{gearboxDesc,jdbcType=NVARCHAR},
		#{numberOfGears,jdbcType=NVARCHAR},
		#{frontBrakeType,jdbcType=NVARCHAR},
		#{backBrakeType,jdbcType=NVARCHAR},
		#{assistType,jdbcType=NVARCHAR},
		#{enginePosition,jdbcType=NVARCHAR},
		#{drivingMode,jdbcType=NVARCHAR},
		#{wheelBase,jdbcType=NVARCHAR}, #{doorNumber,jdbcType=NVARCHAR},
		#{numberOfSeats,jdbcType=NVARCHAR},
		#{frontTireSize,jdbcType=NVARCHAR}, #{backTireSize,jdbcType=NVARCHAR},
		#{frontBossOfWheelSize,jdbcType=NVARCHAR},
		#{backBossOfWheelSize,jdbcType=NVARCHAR},
		#{wheelMaterial,jdbcType=NVARCHAR},
		#{spareTireSpecifications,jdbcType=NVARCHAR},
		#{electricSkylight,jdbcType=NVARCHAR},
		#{panoramicSkylight,jdbcType=NVARCHAR},
		#{xenonLamp,jdbcType=NVARCHAR}, #{frontFogLamp,jdbcType=NVARCHAR},
		#{rearWiper,jdbcType=NVARCHAR},
		#{airConditioner,jdbcType=NVARCHAR},
		#{autoAirConditioning,jdbcType=NVARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.chevron.pms.model.VehicleTypeVo">
		insert into wx_t_vehicle_type
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="levelId != null">
				level_id,
			</if>
			<if test="factory != null">
				factory,
			</if>
			<if test="brand != null">
				brand,
			</if>
			<if test="vehicleSeries != null">
				vehicle_series,
			</if>
			<if test="vehicleType != null">
				vehicle_type,
			</if>
			<if test="salesName != null">
				sales_name,
			</if>
			<if test="modelYear != null">
				model_year,
			</if>
			<if test="emissionStandard != null">
				emission_standard,
			</if>
			<if test="vehicleCategory != null">
				vehicle_category,
			</if>
			<if test="salesYear != null">
				sales_year,
			</if>
			<if test="salesMonth != null">
				sales_month,
			</if>
			<if test="productYear != null">
				product_year,
			</if>
			<if test="stopProductionYear != null">
				stop_production_year,
			</if>
			<if test="stopProductionStatus != null">
				stop_production_status,
			</if>
			<if test="country != null">
				country,
			</if>
			<if test="vehicleKinds != null">
				vehicle_kinds,
			</if>
			<if test="cylinderVolume != null">
				cylinder_volume,
			</if>
			<if test="displacement != null">
				displacement,
			</if>
			<if test="airIntakeForm != null">
				air_intake_form,
			</if>
			<if test="fuelType != null">
				fuel_type,
			</if>
			<if test="fuelLabel != null">
				fuel_label,
			</if>
			<if test="maximumHorsepower != null">
				maximum_horsepower,
			</if>
			<if test="maximumPower != null">
				maximum_power,
			</if>
			<if test="cylinderArrangement != null">
				cylinder_arrangement,
			</if>
			<if test="numberOfCylinders != null">
				number_of_cylinders,
			</if>
			<if test="valvesPerCylinder != null">
				valves_per_cylinder,
			</if>
			<if test="gearboxType != null">
				gearbox_type,
			</if>
			<if test="gearboxDesc != null">
				gearbox_desc,
			</if>
			<if test="numberOfGears != null">
				number_of_gears,
			</if>
			<if test="frontBrakeType != null">
				front_brake_type,
			</if>
			<if test="backBrakeType != null">
				back_brake_type,
			</if>
			<if test="assistType != null">
				assist_type,
			</if>
			<if test="enginePosition != null">
				engine_position,
			</if>
			<if test="drivingMode != null">
				driving_mode,
			</if>
			<if test="wheelBase != null">
				wheel_base,
			</if>
			<if test="doorNumber != null">
				door_number,
			</if>
			<if test="numberOfSeats != null">
				number_of_seats,
			</if>
			<if test="frontTireSize != null">
				front_tire_size,
			</if>
			<if test="backTireSize != null">
				back_tire_size,
			</if>
			<if test="frontBossOfWheelSize != null">
				front_boss_of_wheel_size,
			</if>
			<if test="backBossOfWheelSize != null">
				back_boss_of_wheel_size,
			</if>
			<if test="wheelMaterial != null">
				wheel_material,
			</if>
			<if test="spareTireSpecifications != null">
				spare_tire_specifications,
			</if>
			<if test="electricSkylight != null">
				electric_skylight,
			</if>
			<if test="panoramicSkylight != null">
				panoramic_skylight,
			</if>
			<if test="xenonLamp != null">
				xenon_lamp,
			</if>
			<if test="frontFogLamp != null">
				front_fog_lamp,
			</if>
			<if test="rearWiper != null">
				rear_wiper,
			</if>
			<if test="airConditioner != null">
				air_conditioner,
			</if>
			<if test="autoAirConditioning != null">
				auto_air_conditioning,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="levelId != null">
				#{levelId,jdbcType=NVARCHAR},
			</if>
			<if test="factory != null">
				#{factory,jdbcType=NVARCHAR},
			</if>
			<if test="brand != null">
				#{brand,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleSeries != null">
				#{vehicleSeries,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleType != null">
				#{vehicleType,jdbcType=NVARCHAR},
			</if>
			<if test="salesName != null">
				#{salesName,jdbcType=NVARCHAR},
			</if>
			<if test="modelYear != null">
				#{modelYear,jdbcType=INTEGER},
			</if>
			<if test="emissionStandard != null">
				#{emissionStandard,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleCategory != null">
				#{vehicleCategory,jdbcType=NVARCHAR},
			</if>
			<if test="salesYear != null">
				#{salesYear,jdbcType=INTEGER},
			</if>
			<if test="salesMonth != null">
				#{salesMonth,jdbcType=INTEGER},
			</if>
			<if test="productYear != null">
				#{productYear,jdbcType=INTEGER},
			</if>
			<if test="stopProductionYear != null">
				#{stopProductionYear,jdbcType=INTEGER},
			</if>
			<if test="stopProductionStatus != null">
				#{stopProductionStatus,jdbcType=NVARCHAR},
			</if>
			<if test="country != null">
				#{country,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleKinds != null">
				#{vehicleKinds,jdbcType=NVARCHAR},
			</if>
			<if test="cylinderVolume != null">
				#{cylinderVolume,jdbcType=NVARCHAR},
			</if>
			<if test="displacement != null">
				#{displacement,jdbcType=NVARCHAR},
			</if>
			<if test="airIntakeForm != null">
				#{airIntakeForm,jdbcType=NVARCHAR},
			</if>
			<if test="fuelType != null">
				#{fuelType,jdbcType=NVARCHAR},
			</if>
			<if test="fuelLabel != null">
				#{fuelLabel,jdbcType=NVARCHAR},
			</if>
			<if test="maximumHorsepower != null">
				#{maximumHorsepower,jdbcType=NVARCHAR},
			</if>
			<if test="maximumPower != null">
				#{maximumPower,jdbcType=NVARCHAR},
			</if>
			<if test="cylinderArrangement != null">
				#{cylinderArrangement,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfCylinders != null">
				#{numberOfCylinders,jdbcType=NVARCHAR},
			</if>
			<if test="valvesPerCylinder != null">
				#{valvesPerCylinder,jdbcType=NVARCHAR},
			</if>
			<if test="gearboxType != null">
				#{gearboxType,jdbcType=NVARCHAR},
			</if>
			<if test="gearboxDesc != null">
				#{gearboxDesc,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfGears != null">
				#{numberOfGears,jdbcType=NVARCHAR},
			</if>
			<if test="frontBrakeType != null">
				#{frontBrakeType,jdbcType=NVARCHAR},
			</if>
			<if test="backBrakeType != null">
				#{backBrakeType,jdbcType=NVARCHAR},
			</if>
			<if test="assistType != null">
				#{assistType,jdbcType=NVARCHAR},
			</if>
			<if test="enginePosition != null">
				#{enginePosition,jdbcType=NVARCHAR},
			</if>
			<if test="drivingMode != null">
				#{drivingMode,jdbcType=NVARCHAR},
			</if>
			<if test="wheelBase != null">
				#{wheelBase,jdbcType=NVARCHAR},
			</if>
			<if test="doorNumber != null">
				#{doorNumber,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfSeats != null">
				#{numberOfSeats,jdbcType=NVARCHAR},
			</if>
			<if test="frontTireSize != null">
				#{frontTireSize,jdbcType=NVARCHAR},
			</if>
			<if test="backTireSize != null">
				#{backTireSize,jdbcType=NVARCHAR},
			</if>
			<if test="frontBossOfWheelSize != null">
				#{frontBossOfWheelSize,jdbcType=NVARCHAR},
			</if>
			<if test="backBossOfWheelSize != null">
				#{backBossOfWheelSize,jdbcType=NVARCHAR},
			</if>
			<if test="wheelMaterial != null">
				#{wheelMaterial,jdbcType=NVARCHAR},
			</if>
			<if test="spareTireSpecifications != null">
				#{spareTireSpecifications,jdbcType=NVARCHAR},
			</if>
			<if test="electricSkylight != null">
				#{electricSkylight,jdbcType=NVARCHAR},
			</if>
			<if test="panoramicSkylight != null">
				#{panoramicSkylight,jdbcType=NVARCHAR},
			</if>
			<if test="xenonLamp != null">
				#{xenonLamp,jdbcType=NVARCHAR},
			</if>
			<if test="frontFogLamp != null">
				#{frontFogLamp,jdbcType=NVARCHAR},
			</if>
			<if test="rearWiper != null">
				#{rearWiper,jdbcType=NVARCHAR},
			</if>
			<if test="airConditioner != null">
				#{airConditioner,jdbcType=NVARCHAR},
			</if>
			<if test="autoAirConditioning != null">
				#{autoAirConditioning,jdbcType=NVARCHAR},
			</if>
		</trim>
	</insert>


	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.VehicleTypeVo">
		update wx_t_vehicle_type
		<set>
			<if test="levelId != null">
				level_id = #{levelId,jdbcType=NVARCHAR},
			</if>
			<if test="factory != null">
				factory = #{factory,jdbcType=NVARCHAR},
			</if>
			<if test="brand != null">
				brand = #{brand,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleSeries != null">
				vehicle_series = #{vehicleSeries,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleType != null">
				vehicle_type = #{vehicleType,jdbcType=NVARCHAR},
			</if>
			<if test="salesName != null">
				sales_name = #{salesName,jdbcType=NVARCHAR},
			</if>
			<if test="modelYear != null">
				model_year = #{modelYear,jdbcType=INTEGER},
			</if>
			<if test="emissionStandard != null">
				emission_standard =
				#{emissionStandard,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleCategory != null">
				vehicle_category = #{vehicleCategory,jdbcType=NVARCHAR},
			</if>
			<if test="salesYear != null">
				sales_year = #{salesYear,jdbcType=INTEGER},
			</if>
			<if test="salesMonth != null">
				sales_month = #{salesMonth,jdbcType=INTEGER},
			</if>
			<if test="productYear != null">
				product_year = #{productYear,jdbcType=INTEGER},
			</if>
			<if test="stopProductionYear != null">
				stop_production_year =
				#{stopProductionYear,jdbcType=INTEGER},
			</if>
			<if test="stopProductionStatus != null">
				stop_production_status =
				#{stopProductionStatus,jdbcType=NVARCHAR},
			</if>
			<if test="country != null">
				country = #{country,jdbcType=NVARCHAR},
			</if>
			<if test="vehicleKinds != null">
				vehicle_kinds = #{vehicleKinds,jdbcType=NVARCHAR},
			</if>
			<if test="cylinderVolume != null">
				cylinder_volume = #{cylinderVolume,jdbcType=NVARCHAR},
			</if>
			<if test="displacement != null">
				displacement = #{displacement,jdbcType=NVARCHAR},
			</if>
			<if test="airIntakeForm != null">
				air_intake_form = #{airIntakeForm,jdbcType=NVARCHAR},
			</if>
			<if test="fuelType != null">
				fuel_type = #{fuelType,jdbcType=NVARCHAR},
			</if>
			<if test="fuelLabel != null">
				fuel_label = #{fuelLabel,jdbcType=NVARCHAR},
			</if>
			<if test="maximumHorsepower != null">
				maximum_horsepower =
				#{maximumHorsepower,jdbcType=NVARCHAR},
			</if>
			<if test="maximumPower != null">
				maximum_power = #{maximumPower,jdbcType=NVARCHAR},
			</if>
			<if test="cylinderArrangement != null">
				cylinder_arrangement =
				#{cylinderArrangement,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfCylinders != null">
				number_of_cylinders =
				#{numberOfCylinders,jdbcType=NVARCHAR},
			</if>
			<if test="valvesPerCylinder != null">
				valves_per_cylinder =
				#{valvesPerCylinder,jdbcType=NVARCHAR},
			</if>
			<if test="gearboxType != null">
				gearbox_type = #{gearboxType,jdbcType=NVARCHAR},
			</if>
			<if test="gearboxDesc != null">
				gearbox_desc = #{gearboxDesc,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfGears != null">
				number_of_gears = #{numberOfGears,jdbcType=NVARCHAR},
			</if>
			<if test="frontBrakeType != null">
				front_brake_type = #{frontBrakeType,jdbcType=NVARCHAR},
			</if>
			<if test="backBrakeType != null">
				back_brake_type = #{backBrakeType,jdbcType=NVARCHAR},
			</if>
			<if test="assistType != null">
				assist_type = #{assistType,jdbcType=NVARCHAR},
			</if>
			<if test="enginePosition != null">
				engine_position = #{enginePosition,jdbcType=NVARCHAR},
			</if>
			<if test="drivingMode != null">
				driving_mode = #{drivingMode,jdbcType=NVARCHAR},
			</if>
			<if test="wheelBase != null">
				wheel_base = #{wheelBase,jdbcType=NVARCHAR},
			</if>
			<if test="doorNumber != null">
				door_number = #{doorNumber,jdbcType=NVARCHAR},
			</if>
			<if test="numberOfSeats != null">
				number_of_seats = #{numberOfSeats,jdbcType=NVARCHAR},
			</if>
			<if test="frontTireSize != null">
				front_tire_size = #{frontTireSize,jdbcType=NVARCHAR},
			</if>
			<if test="backTireSize != null">
				back_tire_size = #{backTireSize,jdbcType=NVARCHAR},
			</if>
			<if test="frontBossOfWheelSize != null">
				front_boss_of_wheel_size =
				#{frontBossOfWheelSize,jdbcType=NVARCHAR},
			</if>
			<if test="backBossOfWheelSize != null">
				back_boss_of_wheel_size =
				#{backBossOfWheelSize,jdbcType=NVARCHAR},
			</if>
			<if test="wheelMaterial != null">
				wheel_material = #{wheelMaterial,jdbcType=NVARCHAR},
			</if>
			<if test="spareTireSpecifications != null">
				spare_tire_specifications =
				#{spareTireSpecifications,jdbcType=NVARCHAR},
			</if>
			<if test="electricSkylight != null">
				electric_skylight =
				#{electricSkylight,jdbcType=NVARCHAR},
			</if>
			<if test="panoramicSkylight != null">
				panoramic_skylight =
				#{panoramicSkylight,jdbcType=NVARCHAR},
			</if>
			<if test="xenonLamp != null">
				xenon_lamp = #{xenonLamp,jdbcType=NVARCHAR},
			</if>
			<if test="frontFogLamp != null">
				front_fog_lamp = #{frontFogLamp,jdbcType=NVARCHAR},
			</if>
			<if test="rearWiper != null">
				rear_wiper = #{rearWiper,jdbcType=NVARCHAR},
			</if>
			<if test="airConditioner != null">
				air_conditioner = #{airConditioner,jdbcType=NVARCHAR},
			</if>
			<if test="autoAirConditioning != null">
				auto_air_conditioning =
				#{autoAirConditioning,jdbcType=NVARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.VehicleTypeVo">
		update
		wx_t_vehicle_type
		set level_id = #{levelId,jdbcType=NVARCHAR},
		factory =
		#{factory,jdbcType=NVARCHAR},
		brand = #{brand,jdbcType=NVARCHAR},
		vehicle_series = #{vehicleSeries,jdbcType=NVARCHAR},
		vehicle_type =
		#{vehicleType,jdbcType=NVARCHAR},
		sales_name =
		#{salesName,jdbcType=NVARCHAR},
		model_year =
		#{modelYear,jdbcType=INTEGER},
		emission_standard =
		#{emissionStandard,jdbcType=NVARCHAR},
		vehicle_category =
		#{vehicleCategory,jdbcType=NVARCHAR},
		sales_year =
		#{salesYear,jdbcType=INTEGER},
		sales_month =
		#{salesMonth,jdbcType=INTEGER},
		product_year =
		#{productYear,jdbcType=INTEGER},
		stop_production_year =
		#{stopProductionYear,jdbcType=INTEGER},
		stop_production_status =
		#{stopProductionStatus,jdbcType=NVARCHAR},
		country =
		#{country,jdbcType=NVARCHAR},
		vehicle_kinds =
		#{vehicleKinds,jdbcType=NVARCHAR},
		cylinder_volume =
		#{cylinderVolume,jdbcType=NVARCHAR},
		displacement =
		#{displacement,jdbcType=NVARCHAR},
		air_intake_form =
		#{airIntakeForm,jdbcType=NVARCHAR},
		fuel_type =
		#{fuelType,jdbcType=NVARCHAR},
		fuel_label =
		#{fuelLabel,jdbcType=NVARCHAR},
		maximum_horsepower =
		#{maximumHorsepower,jdbcType=NVARCHAR},
		maximum_power =
		#{maximumPower,jdbcType=NVARCHAR},
		cylinder_arrangement =
		#{cylinderArrangement,jdbcType=NVARCHAR},
		number_of_cylinders =
		#{numberOfCylinders,jdbcType=NVARCHAR},
		valves_per_cylinder =
		#{valvesPerCylinder,jdbcType=NVARCHAR},
		gearbox_type =
		#{gearboxType,jdbcType=NVARCHAR},
		gearbox_desc =
		#{gearboxDesc,jdbcType=NVARCHAR},
		number_of_gears =
		#{numberOfGears,jdbcType=NVARCHAR},
		front_brake_type =
		#{frontBrakeType,jdbcType=NVARCHAR},
		back_brake_type =
		#{backBrakeType,jdbcType=NVARCHAR},
		assist_type =
		#{assistType,jdbcType=NVARCHAR},
		engine_position =
		#{enginePosition,jdbcType=NVARCHAR},
		driving_mode =
		#{drivingMode,jdbcType=NVARCHAR},
		wheel_base =
		#{wheelBase,jdbcType=NVARCHAR},
		door_number =
		#{doorNumber,jdbcType=NVARCHAR},
		number_of_seats =
		#{numberOfSeats,jdbcType=NVARCHAR},
		front_tire_size =
		#{frontTireSize,jdbcType=NVARCHAR},
		back_tire_size =
		#{backTireSize,jdbcType=NVARCHAR},
		front_boss_of_wheel_size =
		#{frontBossOfWheelSize,jdbcType=NVARCHAR},
		back_boss_of_wheel_size =
		#{backBossOfWheelSize,jdbcType=NVARCHAR},
		wheel_material =
		#{wheelMaterial,jdbcType=NVARCHAR},
		spare_tire_specifications =
		#{spareTireSpecifications,jdbcType=NVARCHAR},
		electric_skylight =
		#{electricSkylight,jdbcType=NVARCHAR},
		panoramic_skylight =
		#{panoramicSkylight,jdbcType=NVARCHAR},
		xenon_lamp =
		#{xenonLamp,jdbcType=NVARCHAR},
		front_fog_lamp =
		#{frontFogLamp,jdbcType=NVARCHAR},
		rear_wiper =
		#{rearWiper,jdbcType=NVARCHAR},
		air_conditioner =
		#{airConditioner,jdbcType=NVARCHAR},
		auto_air_conditioning =
		#{autoAirConditioning,jdbcType=NVARCHAR}
		where id =
		#{id,jdbcType=BIGINT}
	</update>

	<select id="selectDisplacementByVehicleSerial" resultMap="BaseResultMap"
		parameterType="map">
		select distinct displacement, air_intake_form
		from
		wx_t_vehicle_type
		WHERE vehicle_series = #{vehicleSerial}
		and brand =
		#{brand}
	</select>

	<!-- by Ken 2016.08.25 -->
	<!-- 批量插入数据 -->
	<insert id="insertBatch" parameterType="java.util.List">
		insert into wx_t_vehicle_type
		(level_id, factory,
		brand, vehicle_series,
		vehicle_type,
		sales_name,
		model_year, emission_standard,
		vehicle_category, sales_year,
		sales_month,
		product_year,
		stop_production_year,
		stop_production_status,
		country, vehicle_kinds,
		cylinder_volume,
		displacement, air_intake_form, fuel_type,
		fuel_label,
		maximum_horsepower, maximum_power,
		cylinder_arrangement,
		number_of_cylinders,
		valves_per_cylinder, gearbox_type, gearbox_desc,
		number_of_gears, front_brake_type, back_brake_type,
		assist_type,
		engine_position, driving_mode,
		wheel_base, door_number,
		number_of_seats,
		front_tire_size, back_tire_size,
		front_boss_of_wheel_size,
		back_boss_of_wheel_size, wheel_material,
		spare_tire_specifications,
		electric_skylight, panoramic_skylight,
		xenon_lamp, front_fog_lamp, rear_wiper,
		air_conditioner,
		auto_air_conditioning)
		values
		<foreach collection="list" index="index" item="item"
			separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.levelId,jdbcType=NVARCHAR},
				#{item.factory,jdbcType=NVARCHAR},
				#{item.brand,jdbcType=NVARCHAR},
				#{item.vehicleSeries,jdbcType=NVARCHAR},
				#{item.vehicleType,jdbcType=NVARCHAR},
				#{item.salesName,jdbcType=VARCHAR},
				#{item.modelYear,jdbcType=INTEGER},
				#{item.emissionStandard,jdbcType=NVARCHAR},
				#{item.vehicleCategory,jdbcType=NVARCHAR},
				#{item.salesYear,jdbcType=INTEGER},
				#{item.salesMonth,jdbcType=INTEGER},
				#{item.productYear,jdbcType=INTEGER},#{item.stopProductionYear,jdbcType=INTEGER},
				#{item.stopProductionStatus,jdbcType=NVARCHAR},#{item.country,jdbcType=NVARCHAR},
				#{item.vehicleKinds,jdbcType=NVARCHAR},#{item.cylinderVolume,jdbcType=NVARCHAR},
				#{item.displacement,jdbcType=NVARCHAR},#{item.airIntakeForm,jdbcType=NVARCHAR},
				#{item.fuelType,jdbcType=NVARCHAR},#{item.fuelLabel,jdbcType=NVARCHAR},
				#{item.maximumHorsepower,jdbcType=NVARCHAR},#{item.maximumPower,jdbcType=NVARCHAR},
				#{item.cylinderArrangement,jdbcType=NVARCHAR},#{item.numberOfCylinders,jdbcType=NVARCHAR},
				#{item.valvesPerCylinder,jdbcType=NVARCHAR},#{item.gearboxType,jdbcType=NVARCHAR},
				#{item.gearboxDesc,jdbcType=NVARCHAR},#{item.numberOfGears,jdbcType=NVARCHAR},
				#{item.frontBrakeType,jdbcType=NVARCHAR},#{item.backBrakeType,jdbcType=NVARCHAR},
				#{item.assistType,jdbcType=NVARCHAR},#{item.enginePosition,jdbcType=NVARCHAR},
				#{item.drivingMode,jdbcType=NVARCHAR},#{item.wheelBase,jdbcType=NVARCHAR},
				#{item.doorNumber,jdbcType=NVARCHAR},#{item.numberOfSeats,jdbcType=NVARCHAR},
				#{item.frontTireSize,jdbcType=NVARCHAR},#{item.backTireSize,jdbcType=NVARCHAR},
				#{item.frontBossOfWheelSize,jdbcType=NVARCHAR},#{item.backBossOfWheelSize,jdbcType=NVARCHAR},
				#{item.wheelMaterial,jdbcType=NVARCHAR},#{item.spareTireSpecifications,jdbcType=NVARCHAR},
				#{item.electricSkylight,jdbcType=NVARCHAR},#{item.panoramicSkylight,jdbcType=NVARCHAR},
				#{item.xenonLamp,jdbcType=NVARCHAR},#{item.frontFogLamp,jdbcType=NVARCHAR},
				#{item.rearWiper,jdbcType=NVARCHAR},#{item.airConditioner,jdbcType=NVARCHAR},
				#{item.autoAirConditioning,jdbcType=NVARCHAR}
			</trim>
		</foreach>
	</insert>

	<!--批量更新数据 by ken -->
	<update id="updateBatch" parameterType="java.util.List">
	   <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_vehicle_type 
			<set>
				<if test="item.levelId != null">
					level_id = #{item.levelId,jdbcType=NVARCHAR},
				</if>
				<if test="item.factory != null">
					factory = #{item.factory,jdbcType=NVARCHAR},
				</if>
				<if test="item.brand != null">
					brand = #{item.brand ,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleSeries != null">
					vehicle_series = #{item.vehicleSeries,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleType != null">
					vehicle_type = #{item.vehicleType,jdbcType=NVARCHAR},
				</if>
				<if test="item.salesName != null">
					sales_name = #{item.salesName,jdbcType=NVARCHAR},
				</if>
				<if test="item.modelYear != null">
					model_year = #{item.modelYear,jdbcType=INTEGER},
				</if>
				<if test="item.emissionStandard != null">
					emission_standard = #{item.emissionStandard,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleCategory != null">
					vehicle_category = #{item.vehicleCategory,jdbcType=NVARCHAR},
				</if>
				<if test="item.salesYear != null">
					sales_year = #{item.salesYear,jdbcType=INTEGER},
				</if>
				<if test="item.salesMonth != null">
					sales_month = #{item.salesMonth,jdbcType=INTEGER},
				</if>
				<if test="item.productYear != null">
					product_year = #{item.productYear,jdbcType=INTEGER},
				</if>
				<if test="item.stopProductionYear != null">
					stop_production_year = #{item.stopProductionYear,jdbcType=INTEGER},
				</if>
				<if test="item.stopProductionStatus != null">
					stop_production_status = #{item.stopProductionStatus,jdbcType=NVARCHAR},
				</if>
				<if test="item.country != null">
					country = #{item.country,jdbcType=NVARCHAR},
				</if>
				<if test="item.vehicleKinds != null">
					vehicle_kinds = #{item.vehicleKinds,jdbcType=NVARCHAR},
				</if>
				<if test="item.cylinderVolume != null">
					cylinder_volume = #{item.cylinderVolume,jdbcType=NVARCHAR},
				</if>
				<if test="item.displacement != null">
					displacement = #{item.displacement,jdbcType=NVARCHAR},
				</if>
				<if test="item.airIntakeForm != null">
					air_intake_form = #{item.airIntakeForm,jdbcType=NVARCHAR},
				</if>
				<if test="item.fuelType != null">
					fuel_type = #{item.fuelType,jdbcType=NVARCHAR},
				</if>
				<if test="item.fuelLabel != null">
					fuel_label = #{item.fuelLabel,jdbcType=NVARCHAR},
				</if>
				<if test="item.maximumHorsepower != null">
					maximum_horsepower = #{item.maximumHorsepower,jdbcType=NVARCHAR},
				</if>
				<if test="item.maximumPower != null">
					maximum_power = #{item.maximumPower,jdbcType=NVARCHAR},
				</if>
				<if test="item.cylinderArrangement != null">
					cylinder_arrangement = #{item.cylinderArrangement,jdbcType=NVARCHAR},
				</if>
				<if test="item.numberOfCylinders != null">
					number_of_cylinders = #{item.numberOfCylinders,jdbcType=NVARCHAR},
				</if>
				<if test="item.valvesPerCylinder != null">
					valves_per_cylinder = #{item.valvesPerCylinder,jdbcType=NVARCHAR},
				</if>
				<if test="item.gearboxType != null">
					gearbox_type = #{item.gearboxType,jdbcType=NVARCHAR},
				</if>
				<if test="item.gearboxDesc != null">
					gearbox_desc = #{item.gearboxDesc,jdbcType=NVARCHAR},
				</if>
				<if test="item.numberOfGears != null">
					number_of_gears = #{item.numberOfGears,jdbcType=NVARCHAR},
				</if>
				<if test="item.frontBrakeType != null">
					front_brake_type = #{item.frontBrakeType,jdbcType=NVARCHAR},
				</if>
				<if test="item.backBrakeType != null">
					back_brake_type = #{item.backBrakeType,jdbcType=NVARCHAR},
				</if>
				<if test="item.assistType != null">
					assist_type = #{item.assistType,jdbcType=NVARCHAR},
				</if>
				<if test="item.enginePosition != null">
					engine_position = #{item.enginePosition,jdbcType=NVARCHAR},
				</if>
				<if test="item.drivingMode != null">
					driving_mode = #{item.drivingMode,jdbcType=NVARCHAR},
				</if>
				<if test="item.wheelBase != null">
					wheel_base = #{item.wheelBase,jdbcType=NVARCHAR},
				</if>
				<if test="item.doorNumber != null">
					door_number = #{item.doorNumber,jdbcType=NVARCHAR},
				</if>
				<if test="item.numberOfSeats != null">
					number_of_seats = #{item.numberOfSeats,jdbcType=NVARCHAR},
				</if>
				<if test="item.frontTireSize != null">
					front_tire_size = #{item.frontTireSize,jdbcType=NVARCHAR},
				</if>
				<if test="item.backTireSize != null">
					back_tire_size = #{item.backTireSize,jdbcType=NVARCHAR},
				</if>
				<if test="item.frontBossOfWheelSize != null">
					front_boss_of_wheel_size = #{item.frontBossOfWheelSize,jdbcType=NVARCHAR},
				</if>
				<if test="item.backBossOfWheelSize != null">
					back_boss_of_wheel_size = #{item.backBossOfWheelSize,jdbcType=NVARCHAR},
				</if>
				<if test="item.wheelMaterial != null">
					wheel_material = #{item.wheelMaterial,jdbcType=NVARCHAR},
				</if>
				<if test="item.spareTireSpecifications != null">
					spare_tire_specifications = #{item.spareTireSpecifications,jdbcType=NVARCHAR},
				</if>
				<if test="item.electricSkylight != null">
					electric_skylight = #{item.electricSkylight,jdbcType=NVARCHAR},
				</if>
				<if test="item.panoramicSkylight != null">
					panoramic_skylight = #{item.panoramicSkylight,jdbcType=NVARCHAR},
				</if>
				<if test="item.xenonLamp != null">
					xenon_lamp = #{item.xenonLamp,jdbcType=NVARCHAR},
				</if>
				<if test="item.frontFogLamp != null">
					front_fog_lamp = #{item.frontFogLamp,jdbcType=NVARCHAR},
				</if>
				<if test="item.rearWiper != null">
					rear_wiper = #{item.rearWiper,jdbcType=NVARCHAR},
				</if>
				<if test="item.airConditioner != null">
					air_conditioner = #{item.airConditioner,jdbcType=NVARCHAR},
				</if>
				<if test="item.autoAirConditioning != null">
					auto_air_conditioning = #{item.autoAirConditioning,jdbcType=NVARCHAR}
				</if>
			</set>
	 where	level_id = #{item.levelId,jdbcType=NVARCHAR}
		</foreach>
	</update>



	<resultMap id="VehicleSerialMap" type="com.chevron.pms.model.VehicleSeriesVo">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="vehicle_series_name" property="vehicleSeriesName"
			jdbcType="NVARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="creator" property="creator" jdbcType="BIGINT" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="updator" property="updator" jdbcType="BIGINT" />
		<result column="vehicle_brand_id" property="vehicleBrandId"
			jdbcType="BIGINT" />
		<result column="factory" property="factory" jdbcType="NVARCHAR" />
		<result column="brand_name" property="brandName" jdbcType="NVARCHAR" />
	</resultMap>


	<select id="selectVehicleTypeByBrand" resultMap="VehicleSerialMap"
		parameterType="map">
		select distinct vehicle_type as vehicle_series_name,
		factory, brand as
		brand_name
		from wx_t_vehicle_type
		WHERE brand =
		#{brand}
	</select>

	<select id="selectVehicleTypeByKeyword" resultMap="VehicleSerialMap"
		parameterType="map">
		select distinct vehicle_type as vehicle_series_name,
		factory, brand as
		brand_name
		from wx_t_vehicle_type
		WHERE vehicle_type
		like CONCAT('%', #{keyword}, '%' )
	</select>

	<select id="selectModelYearByBrandAndVehicleType" resultMap="BaseResultMap"
		parameterType="map">
		select distinct model_year
		from wx_t_vehicle_type
		where
		vehicle_type = #{vehicleType}
		and brand = #{brand}
	</select>

	<select id="selectModelYearByVehicleAndDisplacement" resultMap="BaseResultMap"
		parameterType="map">
		select distinct model_year
		from wx_t_vehicle_type
		where vehicle_series =
		#{vehicleSerial}
		and displacement = #{displacement}
		and brand = #{brand}
		<choose>
			<when test="airIntakeForm == '自然吸气'.toString()">
				and air_intake_form = #{airIntakeForm}
			</when>
			<when test="airIntakeForm != '自然吸气'.toString()">
				and air_intake_form != '自然吸气'
			</when>
		</choose>
	</select>
	<select id="selectVehicleSaleList" resultMap="BaseResultMap"
		parameterType="map">
		select distinct sales_name, displacement,
		air_intake_form
		from wx_t_vehicle_type
		where vehicle_type =
		#{vehicleType}
		and model_year = #{year}
		and brand = #{brand}
	</select>

</mapper>