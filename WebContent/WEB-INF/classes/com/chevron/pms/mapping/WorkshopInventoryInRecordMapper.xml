<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.WorkshopInventoryInRecordMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.WorkshopInventoryInRecord" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
    <result column="workshop_name" property="workshopName" jdbcType="NVARCHAR" />
    <result column="stock_in_no" property="stockInNo" jdbcType="NVARCHAR" />
    <result column="code" property="code" jdbcType="NVARCHAR" />
    <result column="code_type" property="codeType" jdbcType="NVARCHAR" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, workshop_id, workshop_name, stock_in_no, code, code_type, sku, product_name, 
    create_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.WorkshopInventoryInRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_workshop_inventory_in_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.WorkshopInventoryInRecordExample" >
    delete from wx_t_workshop_inventory_in_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.WorkshopInventoryInRecord" >
    insert into wx_t_workshop_inventory_in_record (id, workshop_id, workshop_name, 
      stock_in_no, code, code_type, 
      sku, product_name, create_time, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT}, #{workshopName,jdbcType=NVARCHAR}, 
      #{stockInNo,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, #{codeType,jdbcType=NVARCHAR}, 
      #{sku,jdbcType=NVARCHAR}, #{productName,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.WorkshopInventoryInRecord" >
    insert into wx_t_workshop_inventory_in_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
      <if test="workshopName != null" >
        workshop_name,
      </if>
      <if test="stockInNo != null" >
        stock_in_no,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="codeType != null" >
        code_type,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="stockInNo != null" >
        #{stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="codeType != null" >
        #{codeType,jdbcType=NVARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_workshop_inventory_in_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workshopId != null" >
        workshop_id = #{record.workshopId,jdbcType=BIGINT},
      </if>
      <if test="record.workshopName != null" >
        workshop_name = #{record.workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockInNo != null" >
        stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.codeType != null" >
        code_type = #{record.codeType,jdbcType=NVARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.productName != null" >
        product_name = #{record.productName,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_workshop_inventory_in_record
    set id = #{record.id,jdbcType=BIGINT},
      workshop_id = #{record.workshopId,jdbcType=BIGINT},
      workshop_name = #{record.workshopName,jdbcType=NVARCHAR},
      stock_in_no = #{record.stockInNo,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      code_type = #{record.codeType,jdbcType=NVARCHAR},
      sku = #{record.sku,jdbcType=NVARCHAR},
      product_name = #{record.productName,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertBatch" parameterType="java.util.List">
     insert into wx_t_workshop_inventory_in_record (workshop_id, workshop_name, 
      stock_in_no, code, code_type, sku,product_name,
      create_time
      ) values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.workshopId,jdbcType=BIGINT},
			#{item.workshopName,jdbcType=NVARCHAR},
			#{item.stockInNo,jdbcType=NVARCHAR},
			#{item.code,jdbcType=NVARCHAR},
			#{item.codeType,jdbcType=NVARCHAR},
			#{item.sku,jdbcType=NVARCHAR},
			#{item.productName,jdbcType=NVARCHAR},
			#{item.createTime,jdbcType=TIMESTAMP}
		</trim>
	 </foreach>
   </insert>
</mapper>