<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.WorkShopStatisticsMapper">
	<resultMap id="BaseResultMap" type="com.chevron.pms.model.WorkShopStatistics">
		<id column="id" property="employeeId" jdbcType="BIGINT" />
		<result column="code" property="employeeCode" jdbcType="VARCHAR" />
		<result column="name" property="employeeName" jdbcType="VARCHAR" />
		<result column="wechat_account" property="employeeWechatAccount"
			jdbcType="VARCHAR" />
		<result column="mobile" property="employeeMobile" jdbcType="VARCHAR" />
		<result column="employee_type" property="employeeType" jdbcType="VARCHAR" />
		<result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
		<result column="work_shop_code" property="workShopCode"
			jdbcType="NVARCHAR" />
		<result column="work_shop_name" property="workShopName"
			jdbcType="VARCHAR" />
		<result column="region_id" property="workShopRegionId"
			jdbcType="BIGINT" />
		<result column="region_name" property="workShopRegionName"
			jdbcType="NVARCHAR" />
		<result column="work_shop_address" property="workShopAddress"
			jdbcType="NVARCHAR" />
		<result column="status" property="workShopStatus" jdbcType="NVARCHAR" />
	</resultMap>

	<select id="getWorkshopEmployeeListByPartnerIdAndOtherPra" parameterType="map"
		resultMap="BaseResultMap">
		select employee.id,employee.code,employee.name,employee.wechat_account,employee.mobile,employee.employee_type,employee.workshop_id,
		workshop.work_shop_code,workshop.work_shop_name,workshop.region_id,workshop.region_name,workshop.work_shop_address,workshop.status
		from
		wx_t_workshop_employee employee
		inner join wx_t_work_shop workshop on  employee.workshop_id  = workshop.id 
		inner join wx_t_workshop_partner tt_workshop_partner on workshop.id = tt_workshop_partner.workshop_id
		where 1 = 1
		<if test="workshopId">
			and employee.workshop_id = #{workshopId,jdbcType=BIGINT}
		</if>
		<if test="employeeType">
			and employee.employee_type = #{employeeType,jdbcType=VARCHAR}
		</if>
		<if test="workShopStatus">
			and workshop.status = #{workShopStatus,jdbcType=VARCHAR}
		</if>
		<if test="partnerId">
			and tt_workshop_partner.partner_id = #{partnerId,jdbcType=VARCHAR}
		</if>
		
		order by workshop.work_shop_name asc
	</select>

</mapper>