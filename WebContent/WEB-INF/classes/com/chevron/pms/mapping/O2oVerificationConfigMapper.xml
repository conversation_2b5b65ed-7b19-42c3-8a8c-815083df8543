<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.pms.dao.O2oVerificationConfigMapper">
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.O2oVerificationConfig">
    <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
    <result column="verification_type" property="verificationType" jdbcType="VARCHAR"/>
    <result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="create_user" property="createUser" jdbcType="BIGINT"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    partner_id,verification_type,enable_flag,create_time,create_user,update_time,update_user
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.O2oVerificationConfigExample">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from wx_t_o2o_verification_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.O2oVerificationConfigExample">
    delete from wx_t_o2o_verification_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.O2oVerificationConfig">
    insert into wx_t_o2o_verification_config (partner_id,verification_type,enable_flag,create_time,create_user,update_time,update_user)
    values (#{partner_id,jdbcType=BIGINT},#{verification_type,jdbcType=VARCHAR},#{enable_flag,jdbcType=INTEGER},#{create_time,jdbcType=TIMESTAMP},
      #{create_user,jdbcType=BIGINT},#{update_time,jdbcType=TIMESTAMP},#{update_user,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.O2oVerificationConfig">
    insert into wx_t_o2o_verification_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="verificationType != null" >
        verification_type,
      </if>
      <if test="enableFlag != null" >
        enable_flag,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="verificationType != null" >
        #{verificationType,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null" >
        #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.pms.model.O2oVerificationConfigExample">
    select count(1) from wx_t_o2o_verification_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_o2o_verification_config
    <set>
      <if test="record.verificationType != null" >
        verification_type = #{record.verificationType,jdbcType=VARCHAR},
      </if>
      <if test="record.enableFlag != null" >
        enable_flag = #{record.enableFlag,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null" >
        create_user = #{record.createUser,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null" >
        update_user = #{record.updateUser,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.O2oVerificationConfigParams">
<![CDATA[
    select t1.partner_id,t1.verification_type,t1.enable_flag,t1.create_time,t1.create_user,t1.update_time,
           t1.update_user
      from wx_t_o2o_verification_config t1
     where 1=1
]]>
  </select>
  <select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
    select t1.partner_id,t1.verification_type,t1.enable_flag,t1.create_time,t1.create_user,t1.update_time,
           t1.update_user, (select o.organization_name from wx_t_organization o where o.id=t1.partner_id) partner_name
      from wx_t_o2o_verification_config t1
     where 1=1
  </select>
</mapper>
