<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.PartnerO2OEnterpriseMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.PartnerO2OEnterprise" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="enterprise_code" property="enterpriseCode" jdbcType="VARCHAR" />
    <result column="enterprise_name" property="enterpriseName" jdbcType="VARCHAR" />
    <result column="partner_property" property="partnerProperty" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="BIGINT" />
    <result column="distributor_id" property="distributorId" jdbcType="BIGINT" />
    <result column="partner_property_label" property="partnerPropertyLabel"/>
	<result column="sap_code" property="sapCode" jdbcType="VARCHAR"/>
		<result column="material_stock" property="materialStock" jdbcType="VARCHAR"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
    <result column="eto_company_id" property="etoCompanyId" jdbcType="INTEGER" />
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
		<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
		<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
		<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, enterprise_code, enterprise_name, creation_time, created_by, last_update_time, 
    last_updated_by,sap_code,material_stock,distributor_id,ext_flag,partner_property_label,eto_company_id,
    ext_property1,ext_property2,ext_property3,ext_property4,ext_property5,ext_property6,ext_property7,
		ext_property8,ext_property9
  </sql>
    <sql id="Base_Column_ListV2" >
        id, partner_id, enterprise_code, enterprise_name, creation_time, created_by, last_update_time,
    last_updated_by,material_stock,distributor_id,ext_flag,partner_property_label,eto_company_id,
    ext_property1,ext_property2,ext_property3,ext_property4,ext_property5,ext_property6,ext_property7,
		ext_property8,ext_property9
    </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.PartnerO2OEnterpriseExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_o2o_enterprise
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
    <select id="selectByExampleV2" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.PartnerO2OEnterpriseExample" >
        SELECT t2.*,ppc.customer_code as sap_code from [PP_MID].dbo.syn_dw_to_pp_customer ppc
        LEFT JOIN
        (select
        <if test="distinct" >
            distinct
        </if>
        'false' as QUERYID,
        <include refid="Base_Column_ListV2" />
        from wx_t_partner_o2o_enterprise)t2
        on t2.distributor_id = ppc.distributor_id_pp
        <if test="_parameter != null" >
            <include refid="Example_Where_Clause" />
        </if>
    </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_partner_o2o_enterprise
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPartnerId" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_partner_o2o_enterprise
    where partner_id = #{partnerId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_partner_o2o_enterprise
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByPartnerId" parameterType="java.lang.Long" >
    delete from wx_t_partner_o2o_enterprise
    where partner_id = #{partnerId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.PartnerO2OEnterprise" >
    insert into wx_t_partner_o2o_enterprise (id, partner_id, enterprise_code, 
      enterprise_name, creation_time, created_by, 
      last_update_time, last_updated_by, partner_property_label)
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{enterpriseCode,jdbcType=VARCHAR}, 
      #{enterpriseName,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}, #{partnerPropertyLabel,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.PartnerO2OEnterprise" >
    insert into wx_t_partner_o2o_enterprise
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="enterpriseCode != null" >
        enterprise_code,
      </if>
      <if test="enterpriseName != null" >
        enterprise_name,
      </if>
      <if test="partnerProperty != null" >
        partner_property,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="lastUpdateTime != null" >
        last_update_time,
      </if>
        <if test="partnerPropertyLabel != null" >
            partner_property_label,
        </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by,
      </if>
			<if test="sapCode != null">
				sap_code,
			</if>
			<if test="materialStock != null">
				material_stock,
			</if>
 			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="extProperty6 != null">
				ext_property6,
			</if>
			<if test="extProperty7 != null">
				ext_property7,
			</if>
			<if test="extProperty8 != null">
				ext_property8,
			</if>
			<if test="extProperty9 != null">
				ext_property9,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="enterpriseCode != null" >
        #{enterpriseCode,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseName != null" >
        #{enterpriseName,jdbcType=VARCHAR},
      </if>
      <if test="partnerProperty != null" >
        #{partnerProperty,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
        <if test="partnerPropertyLabel != null" >
            #{partnerPropertyLabel,jdbcType=BIGINT},
        </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
			<if test="sapCode != null">
				#{sapCode,jdbcType=VARCHAR},
			</if>
			<if test="materialStock != null">
				#{materialStock,jdbcType=VARCHAR},
			</if>
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
 			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null">
				#{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null">
				#{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null">
				#{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null">
				#{extProperty9,jdbcType=VARCHAR},
			</if>
    </trim>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.PartnerO2OEnterprise">
        update wx_t_partner_o2o_enterprise
        <set>
            <if test="partnerId != null">
                partner_id = #{partnerId,jdbcType=BIGINT},
            </if>
            <if test="enterpriseCode != null">
                enterprise_code = #{enterpriseCode,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseName != null">
                enterprise_name = #{enterpriseName,jdbcType=VARCHAR},
            </if>
            <if test="partnerProperty != null">
                partner_property=#{partnerProperty,jdbcType=VARCHAR},
            </if>
            <if test="creationTime != null">
                creation_time = #{creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null">
                last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
            </if>
            <if test="sapCode != null">
                sap_code = #{sapCode,jdbcType=VARCHAR},
            </if>
            <if test="materialStock != null">
                material_stock = #{materialStock,jdbcType=VARCHAR},
            </if>
            <if test="distributorId != null">
                distributor_id = #{distributorId,jdbcType=BIGINT},
            </if>
            <if test="extFlag != null">
                ext_flag = #{extFlag,jdbcType=INTEGER},
            </if>
            <if test="newExtFlag != null">
                ext_flag = ext_flag - (ext_flag &amp; #{newExtFlag,jdbcType=INTEGER}) + #{newExtFlag,jdbcType=INTEGER},
            </if>
            <if test="partnerPropertyLabel != null">
                partner_property_label = #{partnerPropertyLabel,jdbcType=INTEGER},
            </if>
            <if test="etoCompanyId != null">
                eto_company_id = #{etoCompanyId,jdbcType=INTEGER},
            </if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null" >
				ext_property6 = #{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null" >
				ext_property7 = #{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null" >
				ext_property8 = #{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null" >
				ext_property9 = #{extProperty9,jdbcType=VARCHAR},
			</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPartnerIdSelective" parameterType="com.chevron.pms.model.PartnerO2OEnterprise">
        update wx_t_partner_o2o_enterprise
        <set>
            <if test="enterpriseCode != null">
                enterprise_code = #{enterpriseCode,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseName != null">
                enterprise_name = #{enterpriseName,jdbcType=VARCHAR},
            </if>
            <if test="partnerProperty != null">
                partner_property=#{partnerProperty,jdbcType=VARCHAR},
            </if>
            <if test="creationTime != null">
                creation_time = #{creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=BIGINT},
            </if>
            <if test="lastUpdateTime != null">
                last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null">
                last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
            </if>
            <if test="sapCode != null">
                sap_code = #{sapCode,jdbcType=VARCHAR},
            </if>
            <if test="materialStock != null">
                material_stock = #{materialStock,jdbcType=VARCHAR},
            </if>
            <if test="distributorId != null">
                distributor_id = #{distributorId,jdbcType=BIGINT},
            </if>
            <if test="extFlag != null">
                ext_flag = #{extFlag,jdbcType=INTEGER},
            </if>
            <if test="newExtFlag != null">
                ext_flag = ext_flag - (ext_flag &amp; #{newExtFlag,jdbcType=INTEGER}) + #{newExtFlag,jdbcType=INTEGER},
            </if>
            <if test="partnerPropertyLabel != null">
                partner_property_label = #{partnerPropertyLabel,jdbcType=INTEGER},
            </if>
            <if test="etoCompanyId != null">
                eto_company_id = #{etoCompanyId,jdbcType=INTEGER},
            </if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null" >
				ext_property6 = #{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null" >
				ext_property7 = #{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null" >
				ext_property8 = #{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null" >
				ext_property9 = #{extProperty9,jdbcType=VARCHAR},
			</if>
        </set>
        where partner_id = #{partnerId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.PartnerO2OEnterprise">
        update wx_t_partner_o2o_enterprise
        set partner_id             = #{partnerId,jdbcType=BIGINT},
            enterprise_code        = #{enterpriseCode,jdbcType=VARCHAR},
            enterprise_name        = #{enterpriseName,jdbcType=VARCHAR},
            creation_time          = #{creationTime,jdbcType=TIMESTAMP},
            created_by             = #{createdBy,jdbcType=BIGINT},
            last_update_time       = #{lastUpdateTime,jdbcType=TIMESTAMP},
            last_updated_by        = #{lastUpdatedBy,jdbcType=BIGINT},
            partner_property_label = #{partnerPropertyLabel,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectPartnerTargetVolume" resultType="com.chevron.master.model.PartnerOperationTargetVolume">
        SELECT t1.distributor_id as distributorId, t1.total_target as totalTarget, wtpooe.partner_id as partnerId
        from (SELECT sdtpot.distributor_id, SUM(target_volume / 1000) as total_target
              FROM PP_MID.dbo.syn_dw_to_pp_operation_target sdtpot
              WHERE [month] LIKE '2024%'
              group by distributor_id) t1
                 left join wx_t_partner_o2o_enterprise wtpooe on t1.distributor_id = wtpooe.distributor_id
              where wtpooe.partner_id in
            <foreach collection="partnerIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
    </select>
</mapper>