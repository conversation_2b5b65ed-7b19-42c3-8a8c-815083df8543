<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.CapCodeDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.CapCodeDetail" >
    <id column="code_id" property="codeId" jdbcType="BIGINT" />
    <result column="batch_id" property="batchId" jdbcType="BIGINT" />
    <result column="qr_code" property="qrCode" jdbcType="NVARCHAR" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    code_id, batch_id, qr_code, status
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.CapCodeDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_cap_code_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_cap_code_detail
    where code_id = #{codeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_cap_code_detail
    where code_id = #{codeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert"  useGeneratedKeys="true" keyProperty="codeId"  parameterType="com.chevron.pms.model.CapCodeDetail" >
    insert into wx_t_cap_code_detail (code_id, batch_id, qr_code, 
      status)
    values (#{codeId,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, #{qrCode,jdbcType=NVARCHAR}, 
      #{status,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective"  useGeneratedKeys="true" keyProperty="codeId"  parameterType="com.chevron.pms.model.CapCodeDetail" >
    insert into wx_t_cap_code_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="codeId != null" >
        code_id,
      </if>
      <if test="batchId != null" >
        batch_id,
      </if>
      <if test="qrCode != null" >
        qr_code,
      </if>
      <if test="status != null" >
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="codeId != null" >
        #{codeId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null" >
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.CapCodeDetail" >
    update wx_t_cap_code_detail
    <set >
      <if test="batchId != null" >
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="qrCode != null" >
        qr_code = #{qrCode,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=NVARCHAR},
      </if>
    </set>
    where code_id = #{codeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.CapCodeDetail" >
    update wx_t_cap_code_detail
    set batch_id = #{batchId,jdbcType=BIGINT},
      qr_code = #{qrCode,jdbcType=NVARCHAR},
      status = #{status,jdbcType=NVARCHAR}
    where code_id = #{codeId,jdbcType=BIGINT}
  </update>
  <insert id="bulkInsertCapCode" parameterType="List">
  	insert into wx_t_cap_code_detail(batch_id, qr_code)
	<foreach collection="qrCodeDetailList" item="item" index="index" open="(" close=")" separator="UNION ALL">
		select #{item.batchId},#{item.qrCode} 
	</foreach>
  </insert>
</mapper>