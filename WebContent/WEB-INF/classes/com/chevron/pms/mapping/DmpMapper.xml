<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.pms.dao.DmpMapper">
  <update id="synExpense" parameterType="map" statementType="CALLABLE">
    {call pr_syn_data_expense()} 
  </update>
  <update id="synData" parameterType="map" statementType="CALLABLE">
    {call pr_syn_data()} 
  </update>
  <update id="synVerificationClose" parameterType="map" statementType="CALLABLE">
    {call pr_syn_data_verification_close(#{closeDate})} 
  </update>
  <update id="synWorkshopInventory" parameterType="map" statementType="CALLABLE">
    {call pr_syn_data_workshop_inventory(#{tallyDate})} 
  </update>
  <update id="synPartnerInventory" parameterType="map" statementType="CALLABLE">
    {call pr_syn_data_partner_inventory(#{tallyDate})} 
  </update>
  <update id="synBiData" parameterType="map" statementType="CALLABLE">
    {call pr_syn_bi_data()} 
  </update>
</mapper>
