<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.VerificationDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.VerificationDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="batch_id" property="batchId" jdbcType="BIGINT" />
    <result column="qr_code_id" property="qrCodeId" jdbcType="VARCHAR" />
    <result column="qr_code" property="qrCode" jdbcType="VARCHAR" />
    <result column="capacity" property="capacity" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="product_name" property="productName" jdbcType="VARCHAR"/>
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
    <result column="workshop_name" property="workshopName" jdbcType="VARCHAR"/>
    <result column="mechanic_code" property="mechanicCode" jdbcType="VARCHAR" />
    <result column="user_wechat_openid" property="userWechatOpenid" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="BIGINT" />
    <result column="qr_code_capacity" property="qrCodeCapacity" jdbcType="INTEGER"/>
    <result column="scan_time" property="scanTime" jdbcType="TIMESTAMP"/>
    <result column="mechanic_name" property="mechanicName" jdbcType="VARCHAR"/>
    <result column="verified_capacity" property="verifiedCapacity" jdbcType="INTEGER"/>
    <result column="per_liter_reward" property="perLiterReward" jdbcType="DOUBLE"/>
    <result column="owner_per_liter_reward" property="ownerPerLiterReward" jdbcType="DOUBLE"/>
    <result column="mechanic_per_liter_reward" property="mechanicPerLiterReward" jdbcType="DOUBLE"/>
    <result column="owner_allocate_amount" property="ownerAllocateAmount" jdbcType="NUMERIC"/>
    <result column="mechanic_allocate_amount" property="mechanicAllocateAmount" jdbcType="NUMERIC"/>
    <result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
    <result column="distribution_rule_id" property="distributionRuleId" jdbcType="BIGINT"/>
    <result column="red_packets" property="redPackets" jdbcType="NUMERIC"/>
    <result column="sp_allocate_amount" property="spAllocateAmount" jdbcType="NUMERIC"/>
    <result column="total_amount" property="verificationAmount" jdbcType="NUMERIC"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, batch_id, qr_code_id, qr_code, capacity, sku, workshop_id, mechanic_code, user_wechat_openid, 
    creation_time, created_by, last_update_time, last_updated_by,owner_allocate_amount,mechanic_allocate_amount,rule_id,distribution_rule_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.VerificationDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_verification_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_verification_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_verification_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.VerificationDetail" >
    insert into wx_t_verification_detail (id, batch_id, qr_code_id, 
      qr_code, capacity, sku, 
      workshop_id, mechanic_code, user_wechat_openid, 
      creation_time, created_by, last_update_time, 
      last_updated_by)
    values (#{id,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, #{qrCodeId,jdbcType=VARCHAR}, 
      #{qrCode,jdbcType=VARCHAR}, #{capacity,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{workshopId,jdbcType=BIGINT}, #{mechanicCode,jdbcType=VARCHAR}, #{userWechatOpenid,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.VerificationDetail" >
    insert into wx_t_verification_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="batchId != null" >
        batch_id,
      </if>
      <if test="qrCodeId != null" >
        qr_code_id,
      </if>
      <if test="qrCode != null" >
        qr_code,
      </if>
      <if test="capacity != null" >
        capacity,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
      <if test="mechanicCode != null" >
        mechanic_code,
      </if>
      <if test="userWechatOpenid != null" >
        user_wechat_openid,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="lastUpdateTime != null" >
        last_update_time,
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by,
      </if>
      <if test="ownerAllocateAmount != null" >
        owner_allocate_amount,
      </if>
      <if test="mechanicAllocateAmount != null" >
        mechanic_allocate_amount,
      </if>
      <if test="ruleId != null" >
        rule_id,
      </if>
      <if test="distributionRuleId != null" >
        distribution_rule_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchId != null" >
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="qrCodeId != null" >
        #{qrCodeId,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="capacity != null" >
        #{capacity,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="mechanicCode != null" >
        #{mechanicCode,jdbcType=VARCHAR},
      </if>
      <if test="userWechatOpenid != null" >
        #{userWechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="ownerAllocateAmount != null" >
        #{ownerAllocateAmount,jdbcType=NUMERIC},
      </if>
      <if test="mechanicAllocateAmount != null" >
        #{mechanicAllocateAmount,jdbcType=NUMERIC},
      </if>
      <if test="ruleId != null" >
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="distributionRuleId != null" >
        #{distributionRuleId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.VerificationDetail" >
    update wx_t_verification_detail
    <set >
      <if test="batchId != null" >
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="qrCodeId != null" >
        qr_code_id = #{qrCodeId,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null" >
        qr_code = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="capacity != null" >
        capacity = #{capacity,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="workshopId != null" >
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="mechanicCode != null" >
        mechanic_code = #{mechanicCode,jdbcType=VARCHAR},
      </if>
      <if test="userWechatOpenid != null" >
        user_wechat_openid = #{userWechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="ownerAllocateAmount != null" >
        owner_allocate_amount = #{ownerAllocateAmount,jdbcType=NUMERIC},
      </if>
      <if test="mechanicAllocateAmount != null" >
        mechanic_allocate_amount = #{mechanicAllocateAmount,jdbcType=NUMERIC},
      </if>
      <if test="ruleId != null" >
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="distributionRuleId != null" >
        distribution_rule_id = #{distributionRuleId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.VerificationDetail" >
    update wx_t_verification_detail
    set batch_id = #{batchId,jdbcType=BIGINT},
      qr_code_id = #{qrCodeId,jdbcType=VARCHAR},
      qr_code = #{qrCode,jdbcType=VARCHAR},
      capacity = #{capacity,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      workshop_id = #{workshopId,jdbcType=BIGINT},
      mechanic_code = #{mechanicCode,jdbcType=VARCHAR},
      user_wechat_openid = #{userWechatOpenid,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectVerificationDetailByBatchId" parameterType="long" resultMap="BaseResultMap">
  		SELECT
		  d.*,
		  cast(left(p.capacity,1) as INT) AS qr_code_capacity,
		  p.name AS product_name,
		  (select sum(d1.capacity) from wx_t_verification_detail d1 where d1.qr_code = d.qr_code) as verified_capacity,
		  (SELECT mq.creation_time
		  FROM wx_t_mechanic_qrcode mq
		   WHERE mq.qr_code = d.qr_code) AS scan_time,
		  (select we.name from wx_t_workshop_employee we where we.code = d.mechanic_code) as mechanic_name,
		  r.per_liter_reward as per_liter_reward,
          dr.owner_reward as owner_per_liter_reward,
          dr.mechanic_reward as mechanic_per_liter_reward
		FROM wx_t_verification_detail d 
		    join wx_t_verification_batch b on d.batch_id = b.id
            join wx_t_verification_rule r on b.rule_id = r.id
            join wx_t_ws_distribution_rule dr on b.distribution_rule_id = dr.id
		    join wx_t_product p on p.sku = d.sku
		WHERE d.batch_id = #{batchId}
  </select>
  <select id="selectVerificationDetailForCHExport" parameterType="com.chevron.pms.model.ChevronVerificationParams" resultMap="BaseResultMap">
  		SELECT
  			d.batch_id, max(b.partner_id) partner_id,
		  max(ws.work_shop_name) workshop_name,
		  sum(cast(left(p.capacity,1) as INT)) AS qr_code_capacity,
		  max(we.name) as mechanic_name
		FROM wx_t_verification_detail d 
			join wx_t_verification_batch b on b.id=d.batch_id
			JOIN wx_t_work_shop ws ON ws.id = d.workshop_id
		    join wx_t_product p on p.sku = d.sku
		    join wx_t_workshop_employee we on we.code = d.mechanic_code
		WHERE b.status='APPROVED' $Permission_Clause$
		  <if test="dateFrom != null ">
		  	and b.creation_time &gt;=#{dateFrom}
		  </if>
		    <if test="dateTo != null">
		  	and b.creation_time &lt;=#{dateTo}
		  </if>
		  <if test="partnerId != null">
			  and b.partner_id = #{partnerId}
		  </if>
		  <if test="workshopId != null">
		  	  and b.workshop_id = #{workshopId}
		  </if>
		  <if test="workshopName != null and workshopName != ''">
		  	  and ws.work_shop_name like #{workshopName}
		  </if>
		 group by d.batch_id, d.mechanic_code
  </select>
  <select id="selectVerificationDetailForSPHExport" parameterType="com.chevron.pms.model.OilVerificationSpParams" resultMap="BaseResultMap">
  		SELECT
  			d.batch_id, max(b.partner_id) partner_id,
		  max(ws.work_shop_name) workshop_name,
		  sum(cast(left(p.capacity,1) as INT)) AS qr_code_capacity,
		  max(we.name) as mechanic_name
		FROM wx_t_verification_detail d 
			join wx_t_verification_batch b on b.id=d.batch_id
			JOIN wx_t_work_shop ws ON ws.id = d.workshop_id
		    join wx_t_product p on p.sku = d.sku
		    join wx_t_workshop_employee we on we.code = d.mechanic_code
		WHERE 1=1 $Permission_Clause$
		  <if test="dateFrom != null ">
		  	and b.creation_time &gt;=#{dateFrom}
		  </if>
		    <if test="dateTo != null">
		  	and b.creation_time &lt;=#{dateTo}
		  </if>
		  <if test="partnerId != null">
			  and b.partner_id = #{partnerId}
		  </if>
		  <if test="workshopId != null">
		  	  and b.workshop_id = #{workshopId}
		  </if>
		  <if test="workshopName != null and workshopName != ''">
		  	  and ws.work_shop_name like #{workshopName}
		  </if>
		 group by d.batch_id, d.mechanic_code
  </select>
  <insert id="insertBatchForO2oVerification" parameterType="map">
    insert into wx_t_verification_detail (batch_id, qr_code,rule_id,distribution_rule_id, 
      creation_time, created_by, sku, workshop_id,capacity,owner_allocate_amount,mechanic_allocate_amount,total_amount
      )
    values 
    <foreach collection="verificationDetails" index="index" item="item" separator=",">
	   	<trim prefix="(" suffix=")" suffixOverrides=",">
	        #{item.batchId,jdbcType=BIGINT}, #{item.qrCode,jdbcType=VARCHAR}, #{item.ruleId,jdbcType=BIGINT}, #{item.distributionRuleId,jdbcType=BIGINT}, 
      #{item.creationTime,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR}, 
      #{item.workshopId,jdbcType=BIGINT}, (select p.capacity from wx_t_product p where p.sku=#{item.sku,jdbcType=VARCHAR}), 
      0, 0,
      (select convert(int, p.capacity) * #{item.perLiterReward,jdbcType=NUMERIC} from wx_t_product p where p.sku=#{item.sku,jdbcType=VARCHAR})
	   	</trim>
	</foreach>
  </insert>
  <insert id="insertBatchVerification" parameterType="map">
    insert into wx_t_verification_detail (batch_id, qr_code,rule_id,distribution_rule_id, 
      creation_time, created_by, sku, workshop_id,capacity,owner_allocate_amount,mechanic_allocate_amount,total_amount
      )
    <foreach collection="verificationDetails" index="index" item="item" separator=" union all ">
	  select -1, mq.qr_code, vr.id, dr.id, #{item.creationTime,jdbcType=TIMESTAMP}, 
	  #{item.createdBy,jdbcType=BIGINT}, mq.sku, mq.workshop_id, p.capacity,
				dr.owner_reward * convert(int, p.capacity),dr.mechanic_reward * convert(int, p.capacity), vr.per_liter_reward*convert(int, p.capacity)
				from wx_t_mechanic_qrcode mq
				join wx_t_workshop_partner wp on wp.workshop_id=mq.workshop_id and wp.relation_type='trade'
				join wx_t_verification_rule vr on vr.partner_id=wp.partner_id and vr.enable_flag='Y'
				join wx_t_product p on p.sku=mq.sku
				join (select top 1 dr2.* from (
				/*门店SKU分配规则*/select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade' and dr1.workshop_id=wp1.workshop_id
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id and dr1.sku=mq1.sku
				where mq1.qr_code='${item.qrCode}' and dr1.enable_flag='Y'
				union all
				/*SKU分配规则*/select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade'
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id and dr1.sku=mq1.sku
				where mq1.qr_code='${item.qrCode}' and dr1.workshop_id=-1 and dr1.enable_flag='Y'
				union all/*门店分配规则*/
				select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade' and dr1.workshop_id=wp1.workshop_id
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id
				where mq1.qr_code='${item.qrCode}' and dr1.sku is null and dr1.enable_flag='Y'
				union all /*合伙人默认分配规则*/
				select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade'
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id
				where mq1.qr_code='${item.qrCode}' and dr1.workshop_id=-1 and dr1.sku is null and dr1.enable_flag='Y') dr2 ) dr on mq.workshop_id=dr.workshop_id
				where mq.qr_code='${item.qrCode}'
	</foreach>
  </insert>
  <select id="buildO2oVerificationDetails" parameterType="map" resultMap="BaseResultMap">
    <foreach collection="o2oVerificationDetails" index="index" item="item" separator="union all">
SELECT ${index} id, -1 batch_id, #{item.createdBy,jdbcType=BIGINT} create_by, #{item.creationTime,jdbcType=TIMESTAMP} creation_time, 
#{item.qrCode,jdbcType=VARCHAR} qr_code, #{item.sku,jdbcType=VARCHAR} sku, #{item.workshopId,jdbcType=BIGINT} workshop_id, 
0 mechanic_per_liter_reward, 0 owner_per_liter_reward, vr.per_liter_reward, vr.id rule_id, -1 distribution_rule_id
from wx_t_verification_rule vr where vr.enable_flag='Y' and vr.partner_id=#{item.o2oPartner,jdbcType=BIGINT}
	</foreach>
  </select>
  <select id="queryVerificationSumForPage" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="BaseResultMap">
  	select a1.workshop_id id, a1.workshop_id, ws.work_shop_name workshop_name, wp1.partner_id, o.organization_name partner_name,
a1.capacity, a1.mechanic_allocate_amount - isnull(a1.red_packets, 0) mechanic_allocate_amount, 
 a1.owner_allocate_amount owner_allocate_amount, a1.total_amount,
a1.red_packets 
from (
select sum(mq.capacity) capacity, sum(mq.total_amount) total_amount, 
sum(vd.mechanic_allocate_amount) mechanic_allocate_amount, (sum(vd.owner_allocate_amount)) owner_allocate_amount, 
sum(case when vp.id is null then 0 else vd.mechanic_allocate_amount end) red_packets, mq.workshop_id
from(
select convert(int, p.capacity) capacity, 
case when v1.mechanic_code='-1' or v1.update_time is not null then null else v1.qr_code end qr_code,
vd1.total_amount, v1.workshop_id
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=v1.qr_code
where 1=1 $Permission_Clause$
	<if test="dateFromStr != null and dateFromStr != ''">
		and v1.creation_time &gt;= '${dateFromStr}' 
	</if>
	<if test="dateToStr != null and dateToStr != ''">
		and v1.creation_time &lt; dateadd(day, 1, '${dateToStr}') 
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
union all
select 0, v1.qr_code, 0, v1.workshop_id
from wx_t_mechanic_qrcode v1 
where v1.update_time is not null $Permission_Clause$
	<if test="dateFromStr != null and dateFromStr != ''">
		and v1.update_time &gt;= '${dateFromStr}' 
	</if>
	<if test="dateToStr != null and dateToStr != ''">
		and v1.update_time &lt; dateadd(day, 1, '${dateToStr}')
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
) mq
left join wx_t_verification_detail vd on vd.qr_code=mq.qr_code
left join wx_t_mechanic_verification_pay vp on vp.qr_code=mq.qr_code
group by mq.workshop_id) a1 left join wx_t_workshop_partner wp1 on wp1.workshop_id=a1.workshop_id and wp1.relation_type='trade'
join wx_t_work_shop ws on ws.id=a1.workshop_id
join wx_t_organization o on o.id=wp1.partner_id
where 1=1
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT} 
	</if>
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name = '${workshopNameCond}'
	</if>
  </select>
  <select id="queryVerificationSumForPageSum" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="BaseResultMap">
  	select -1 id, sum(a1.capacity) capacity, sum(a1.mechanic_allocate_amount - isnull(a1.red_packets, 0)) mechanic_allocate_amount, 
  	sum(a1.owner_allocate_amount) owner_allocate_amount, sum(a1.total_amount) total_amount,
sum(a1.red_packets) red_packets from (
select sum(mq.capacity) capacity, sum(mq.total_amount) total_amount, 
sum(vd.mechanic_allocate_amount) mechanic_allocate_amount, (sum(vd.owner_allocate_amount)) owner_allocate_amount, 
sum(case when vp.id is null then 0 else vd.mechanic_allocate_amount end) red_packets, mq.workshop_id
from(
select convert(int, p.capacity) capacity, 
case when v1.mechanic_code='-1' or v1.update_time is not null then null else v1.qr_code end qr_code,
vd1.total_amount, v1.workshop_id
from wx_t_mechanic_qrcode v1
left join wx_t_product p on p.sku=v1.sku
left join wx_t_verification_detail vd1 on vd1.qr_code=v1.qr_code
where 1=1 $Permission_Clause$
	<if test="dateFromStr != null and dateFromStr != ''">
		and v1.creation_time &gt;= '${dateFromStr}' 
	</if>
	<if test="dateToStr != null and dateToStr != ''">
		and v1.creation_time &lt; dateadd(day, 1, '${dateToStr}') 
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
union all
select 0, v1.qr_code, 0, v1.workshop_id
from wx_t_mechanic_qrcode v1 
where v1.update_time is not null $Permission_Clause$ 
	<if test="dateFromStr != null and dateFromStr != ''">
		and v1.update_time &gt;= '${dateFromStr}' 
	</if>
	<if test="dateToStr != null and dateToStr != ''">
		and v1.update_time &lt; dateadd(day, 1, '${dateToStr}') 
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
) mq
left join wx_t_verification_detail vd on vd.qr_code=mq.qr_code
left join wx_t_mechanic_verification_pay vp on vp.qr_code=mq.qr_code
group by mq.workshop_id) a1 left join wx_t_workshop_partner wp1 on wp1.workshop_id=a1.workshop_id and wp1.relation_type='trade'
join wx_t_work_shop ws on ws.id=a1.workshop_id
join wx_t_organization o on o.id=wp1.partner_id
where 1=1
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT} 
	</if>
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name = '${workshopNameCond}'
	</if>
  </select>
  <select id="queryVerificationSumByMechanic" parameterType="com.chevron.pms.model.OilVerificationParams" resultMap="BaseResultMap">
  	select a1.workshop_id id, a1.workshop_id, 
a1.capacity, a1.mechanic_allocate_amount - isnull(a1.red_packets, 0) mechanic_allocate_amount, 
a1.red_packets, we.name mechanic_name
from (
select sum(vd.capacity) capacity, sum(vd.mechanic_allocate_amount) mechanic_allocate_amount, sum(case when vp.id is null then 0 else vd.mechanic_allocate_amount end) red_packets, mq.workshop_id, mq.mechanic_code
from(
select v1.qr_code, v1.mechanic_code, v1.workshop_id, v1.sku
 from wx_t_mechanic_qrcode v1 
where v1.mechanic_code != '-1' and v1.update_time is null $Permission_Clause$
	<if test="dateFrom != null">
		and v1.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
	</if>
	<if test="dateTo != null">
		and v1.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP} 
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
union all
select v1.qr_code, v1.mechanic_code, v1.workshop_id, v1.sku
from wx_t_mechanic_qrcode v1 
where v1.update_time is not null $Permission_Clause$ 
	<if test="dateFrom != null">
		and v1.update_time &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
	</if>
	<if test="dateTo != null">
		and v1.update_time &lt; #{dateTo, jdbcType=TIMESTAMP} 
	</if>
	<if test="workshopId != null">
		and v1.workshopId = #{workshopId, jdbcType=BIGINT} 
	</if>
) mq
left join wx_t_verification_detail vd on vd.qr_code=mq.qr_code
left join wx_t_mechanic_verification_pay vp on vp.qr_code=mq.qr_code
group by mq.mechanic_code, mq.workshop_id) a1 left join wx_t_workshop_partner wp1 on wp1.workshop_id=a1.workshop_id and wp1.relation_type='trade'
join wx_t_work_shop ws on ws.id=a1.workshop_id
left join wx_t_workshop_employee we on we.code=a1.mechanic_code
where 1=1
	<if test="partnerId != null">
		and wp1.partner_id = #{partnerId, jdbcType=BIGINT} 
	</if>
	<if test="workshopName != null and workshopName != ''">
		and ws.work_shop_name = '${workshopName}'
	</if>
  </select>
  	<update id="updateDistribution" parameterType="map">
	   <foreach collection="mechanicQrcodes" item="item" index="index" open="begin" close="end;" separator=";">
			update vd set vd.mechanic_allocate_amount=dr.mechanic_reward*vd.capacity,
				vd.distribution_rule_id=dr.id,
				vd.owner_allocate_amount=dr.owner_reward*vd.capacity,
				vd.last_update_time=#{item.updateTime,jdbcType=TIMESTAMP},
				vd.last_updated_by=#{item.updateUser,jdbcType=BIGINT}
				from wx_t_verification_detail vd 
				join wx_t_mechanic_qrcode mq on mq.qr_code=vd.qr_code
				join (select top 1 dr2.* from (
				/*门店SKU分配规则*/select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade' and dr1.workshop_id=wp1.workshop_id
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id and dr1.sku=mq1.sku
				where mq1.qr_code='${item.qrCode}' and dr1.enable_flag='Y'
				union all
				/*SKU分配规则*/select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade'
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id and dr1.sku=mq1.sku
				where mq1.qr_code='${item.qrCode}' and dr1.workshop_id=-1 and dr1.enable_flag='Y'
				union all/*门店分配规则*/
				select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade' and dr1.workshop_id=wp1.workshop_id
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id
				where mq1.qr_code='${item.qrCode}' and dr1.sku is null and dr1.enable_flag='Y'
				union all /*合伙人默认分配规则*/
				select dr1.mechanic_reward, dr1.owner_reward, dr1.partner_id, dr1.id, mq1.workshop_id
				from wx_t_ws_distribution_rule dr1 
				join wx_t_workshop_partner wp1 on wp1.partner_id=dr1.partner_id and wp1.relation_type='trade'
				join wx_t_mechanic_qrcode mq1 on mq1.workshop_id=wp1.workshop_id
				where mq1.qr_code='${item.qrCode}' and dr1.workshop_id=-1 and dr1.sku is null and dr1.enable_flag='Y') dr2 ) dr on mq.workshop_id=dr.workshop_id
				where vd.qr_code='${item.qrCode}'
		</foreach>
	</update>
</mapper>