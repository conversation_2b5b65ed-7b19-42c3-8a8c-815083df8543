<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.WorkshopEmployeeMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.WorkshopEmployee" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="wechat_account" property="wechatAccount" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="workshop_name" property="workshopName" jdbcType="VARCHAR" />
    <result column="employee_type" property="employeeType" jdbcType="VARCHAR" />
    <result column="employee_type_text" property="employeeTypeText" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="BIGINT" />
    <result column="capacity" property="capacity" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
    <result column="partner_id_new" property="partnerIdNew" jdbcType="BIGINT"/>
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
    <result column="workshop_status" property="workshopStatus" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="INTEGER"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="wechat_open_id" property="wechatOpenId" jdbcType="VARCHAR"/>
		<result column="b2b_open_id" property="b2bOpenId" jdbcType="VARCHAR"/>
    <result column="is_has_product" property="isHasProduct" jdbcType="INTEGER"/>
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, code, name, wechat_account, mobile, workshop_id, partner_id, workshop_name, employee_type, 
    creator, creation_time, updator, update_time,version_no,enable_flag,wechat_open_id,b2b_open_id,is_has_product
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.WorkshopEmployeeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_workshop_employee
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_workshop_employee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_workshop_employee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.WorkshopEmployee" >
    insert into wx_t_workshop_employee (id, code, name, 
      wechat_account, mobile, workshop_id, 
      workshop_name, employee_type, creator, 
      creation_time, updator, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{wechatAccount,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{workshopId,jdbcType=BIGINT}, 
      #{workshopName,jdbcType=VARCHAR}, #{employeeType,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.WorkshopEmployee" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_workshop_employee
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="wechatAccount != null" >
        wechat_account,
      </if>
      <if test="mobile != null" >
        mobile,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="workshopName != null" >
        workshop_name,
      </if>
      <if test="employeeType != null" >
        employee_type,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
			<if test="versionNo != null">
				version_no,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="isHasProduct != null">
				is_has_product,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="wechatAccount != null" >
        #{wechatAccount,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        #{workshopName,jdbcType=VARCHAR},
      </if>
      <if test="employeeType != null" >
        #{employeeType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
			<if test="versionNo != null">
				#{versionNo,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="isHasProduct != null">
				#{isHasProduct,jdbcType=INTEGER},
			</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.WorkshopEmployee" >
    update wx_t_workshop_employee
    <set >
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="wechatAccount != null" >
        wechat_account = #{wechatAccount,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="workshopId != null" >
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        workshop_name = #{workshopName,jdbcType=VARCHAR},
      </if>
      <if test="employeeType != null" >
        employee_type = #{employeeType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
			<if test="versionNo != null" >
				version_no = #{versionNo,jdbcType=VARCHAR},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="wechatOpenId != null" >
				wechat_open_id = #{wechatOpenId,jdbcType=VARCHAR},
			</if>
			<if test="b2bOpenId != null" >
				b2b_open_id = #{b2bOpenId,jdbcType=VARCHAR},
			</if>
	  <if test="isHasProduct != null" >
		  is_has_product = #{isHasProduct,jdbcType=INTEGER},
	  </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.WorkshopEmployee" >
    update wx_t_workshop_employee
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      wechat_account = #{wechatAccount,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      workshop_id = #{workshopId,jdbcType=BIGINT},
      workshop_name = #{workshopName,jdbcType=VARCHAR},
      employee_type = #{employeeType,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- add by bo.liu -->
  <select id="getMechanicByWorkshopId" parameterType="map" resultMap="BaseResultMap">
  	select
  		*
			 ,(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='WorkshopEmployee.employeeType' and di1.dic_item_code=we.employee_type) employee_type_text
  	from
  		wx_t_workshop_employee we
  	where
  		workshop_id = #{workshopId,jdbcType=BIGINT}
  		and enable_flag = 1
       /* and employee_type = 'Mechanic'*/
  </select>
  
  <select id="getCountByWorkshopIdAndEmployeeType" parameterType="long" resultMap="BaseResultMap">
  	select
  		count(id)
  	from
  		wx_t_workshop_employee
  	where
  		workshop_id = #{workshopId,jdbcType=BIGINT}
        and employee_type = #{employeeType,jdbcType=VARCHAR}
  </select>
  <!-- end -->
  
  <select id="selectMechanicBySpAndWorkshop" parameterType="map" resultMap="BaseResultMap">
  	SELECT we.*, (select u.ch_name from wx_t_user u where u.user_id = we.creator) as creator_name
      FROM wx_t_workshop_employee we
     WHERE 1 = 1
      <if test="partnerId != null">
        and exists(SELECT 1
                     FROM wx_t_workshop_partner wp
                    WHERE wp.workshop_id = we.workshop_id AND wp.partner_id = #{partnerId})
      </if>
      <if test="dateFrom != null and dateTo != null">
      AND we.creation_time BETWEEN #{dateFrom} AND #{dateTo}
      </if>
      <if test="workshopId != null">
      	and we.workshop_id = #{workshopId}
      </if>
      <if test="workshopName != null">
      	and we.workshop_name like #{workshopName}
      </if>
      order by we.creation_time desc
  </select>
  
  <!-- 含有数据权限控制的,分页处理 -->
   <select id="selectMechanicBySpAndWorkshopNew" parameterType="com.chevron.pms.model.WorkshopEmployeeParams" resultMap="BaseResultMap">
  	 SELECT o.organization_name partner_name, we.id, we.code, we.name, we.wechat_account, we.mobile, we.workshop_id, we.partner_id, we.workshop_name, we.employee_type, 
    isnull(we.updator, we.creator) creator, isnull(we.update_time,we.creation_time) creation_time, we.updator, we.update_time,we.version_no,we.enable_flag, u.ch_name as creator_name/*, (select sum(v.capacity) from wx_v_oil_verification v where v.mechanic_code = we.code and v.workshop_id=we.workshop_id) capacity*/
			 ,(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='WorkshopEmployee.employeeType' and di1.dic_item_code=we.employee_type) employee_type_text,we.is_has_product
      FROM wx_t_workshop_employee we
      LEFT JOIN wx_t_user u on u.user_id = isnull(we.updator, we.creator)
      left join wx_t_work_shop w on w.id=we.workshop_id
      LEFT JOIN wx_t_workshop_partner  t_w_p ON we.workshop_id = t_w_p.workshop_id
      left join wx_t_organization o on o.id=t_w_p.partner_id
     WHERE w.delete_flag=0
      <if test="partnerId != null">
        and exists(SELECT 1
                     FROM wx_t_workshop_partner wp
                    WHERE wp.workshop_id = we.workshop_id AND wp.partner_id = #{partnerId}) 
       </if>
       AND isnull(we.update_time,we.creation_time) BETWEEN #{dateFrom} AND #{dateTo}
        <if test="queryField==null">
	       <if test="workshopId != null">
	      	and we.workshop_id = #{workshopId}
	       </if>
	       <if test="workshopName != null">
	      	and we.workshop_name like #{workshopName}
	       </if>
	       <if test="employeeType != null">
	      	and we.employee_type = #{employeeType}
	       </if>
	       <if test="name != null">
	      	and we.name like #{name}
	       </if>
	       <if test="mobile != null">
	      	and we.mobile like #{mobile}
	       </if>
	       <if test="creatorName != null">
	      	and u.ch_name like #{creatorName}
	      </if>
       </if>
       
       <if test="queryField!=null">
	      	and 
	      	(we.workshop_name like #{queryField}
	      	 or we.name like #{queryField}
			 or  (CASE we.employee_type 
				WHEN 'Owner' THEN '店主'
				WHEN 'Mechanic' THEN '技师'
				ELSE '未知' END) LIKE #{queryField}
			 or we.mobile like #{queryField}  
			 OR EXISTS(
             SELECT 
             1 FROM 
             wx_t_organization wx_t_ogr
             WHERE
             wx_t_ogr.organization_name LIKE #{queryField}
             AND 
             t_w_p.partner_id = wx_t_ogr.id
             )
			 or exists (select 1 from wx_t_user i1 where i1.user_id=we.creator and i1.ch_name like #{queryField})     	 
	      	)
       </if>
  </select>
  
   <select id="countSelectMechanicBySpAndWorkshopNew" parameterType="com.chevron.pms.model.WorkshopEmployeeParams" resultType="Long">
   	 SELECT count(1)
      FROM wx_t_workshop_employee we
      LEFT JOIN wx_t_user u on u.user_id = we.creator
     LEFT JOIN wx_t_workshop_partner  t_w_p ON we.workshop_id = t_w_p.workshop_id
     WHERE 1 = 1
      <if test="partnerId != null">
        and exists(SELECT 1
                     FROM wx_t_workshop_partner wp
                    WHERE wp.workshop_id = we.workshop_id AND wp.partner_id = #{partnerId}) 
       </if>
       AND we.creation_time BETWEEN #{dateFrom} AND #{dateTo}
       <if test="queryField==null">
	       <if test="workshopId != null">
	      	and we.workshop_id = #{workshopId}
	       </if>
	       <if test="workshopName != null">
	      	and we.workshop_name like #{workshopName}
	       </if>
	       <if test="employeeType != null">
	      	and we.employee_type = #{employeeType}
	       </if>
	       <if test="name != null">
	      	and we.name like #{name}
	       </if>
	       <if test="mobile != null">
	      	and we.mobile like #{mobile}
	       </if>
	       <if test="creatorName != null">
	      	and u.ch_name like #{creatorName}
	       </if>
       </if>
       
       <if test="queryField!=null">
	      	and 
	      	(we.workshop_name like #{queryField}
	      	 or we.name like #{queryField}
			 or  (CASE we.employee_type 
				WHEN 'Owner' THEN '店主'
				WHEN 'Mechanic' THEN '技师'
				ELSE '未知' END) LIKE #{queryField}
			 or we.mobile like #{queryField}  
			 OR EXISTS(
             SELECT 
             1 FROM 
             wx_t_organization wx_t_ogr
             WHERE
             wx_t_ogr.organization_name LIKE #{queryField}
             AND 
             t_w_p.partner_id = wx_t_ogr.id
             )
			 or exists (select 1 from wx_t_user i1 where i1.user_id=we.creator and i1.ch_name  like #{queryField}) 	 
	      	)
       </if>
   </select>
   
   <select id="selectByMapParams" resultMap="BaseResultMap" parameterType="map" >
      SELECT wx_t_e.*
      FROM wx_t_workshop_employee wx_t_e
      where  wx_t_e.mobile = #{mobile} 
   </select>
   
   <select id="selectByParams" resultMap="BaseResultMap" parameterType="map" >
      SELECT wx_t_e.id, wx_t_e.code, wx_t_e.name, wx_t_e.wechat_account, wx_t_e.mobile, wx_t_e.workshop_id, wp.partner_id, wx_t_e.workshop_name, wx_t_e.employee_type, 
    wx_t_e.creator, wx_t_e.creation_time, wx_t_e.updator, wx_t_e.update_time,wx_t_e.version_no,wx_t_e.enable_flag,wp.partner_id as partner_id_new,wp.partner_name as partner_name,wx_t_e.is_has_product
      FROM wx_t_workshop_employee wx_t_e
      left join wx_t_workshop_partner wp on wp.workshop_id=wx_t_e.workshop_id and wp.relation_type='trade'
      <choose>
      	<when test="checkExists">
      	where 1!=1
      		<if test="mobile != null">
      or wx_t_e.mobile = #{mobile}
      		</if>
      		<if test="code != null">
      or wx_t_e.code = #{code}
      		</if>
      	</when>
      	<otherwise>
      where  1=1 
	    	<if test="mobile != null">
	  and wx_t_e.mobile = #{mobile}
	    	</if>
      		<if test="code != null">
      and wx_t_e.code = #{code}
      		</if>
      		<if test="versionNo != null">
      		and wx_t_e.version_no=#{versionNo}
      		</if>
      		<if test="enableFlag != null">
      		and wx_t_e.enable_flag=#{enableFlag }
      		</if>
      		<if test="workshopId !=null">
      		and wx_t_e.workshop_id=#{workshopId}
      		</if>
      		<if test="fun == 'schedulePublish'">
      			<if test="assignDistributors != null or assignWorkshops != null">
      			and (1!= 1 
      				<if test="assignDistributors != null">
      				or wp.partner_id in 
	                  <foreach collection="assignDistributors" item="listItem" open="(" close=")" separator="," >
	                    #{listItem}
	                  </foreach>
      				</if>
      				<if test="assignWorkshops != null">
      				or wx_t_e.workshop_id in 
	                  <foreach collection="assignWorkshops" item="listItem" open="(" close=")" separator="," >
	                    #{listItem}
	                  </foreach>
      				</if>
      			)
      			</if>
      		</if>
      	</otherwise>
      </choose>
   </select>
  
    <select id="getEmployeeCodeByWorkshopId" parameterType="map" resultMap="BaseResultMap">
	  select
	  		tt_e.*,tt_w_p.partner_id partner_id_new
	  from
	  		wx_t_workshop_employee tt_e
	  		LEFT JOIN wx_t_workshop_partner tt_w_p
	  		ON tt_w_p.workshop_id = tt_e.workshop_id
      WHERE 
             tt_e.workshop_id =  #{workshopId,jdbcType=BIGINT}	
	  ORDER BY  tt_e.employee_type DESC, tt_e.update_time DESC, tt_e.creation_time DESC
 	 </select>
	<resultMap id="wpEmployeeCountMap" type="com.chevron.pms.model.WorkshopEmployeeCountReportView" >
	    <result column="count" property="count" jdbcType="NVARCHAR" />
	    <result column="employee_type" property="employeeType" jdbcType="NVARCHAR" />
	</resultMap>
	<select id="getWorkshopEmployeeCounts" resultMap="wpEmployeeCountMap" parameterType="com.chevron.pms.model.WorkshopEmployeeAuthPrams">
	  	SELECT count(we.id) AS count,we.employee_type
	      FROM wx_t_workshop_employee we
	      LEFT JOIN wx_t_user u on u.user_id = we.creator
	      LEFT JOIN wx_t_workshop_partner  t_w_p ON we.workshop_id = t_w_p.workshop_id
	     WHERE 1 = 1 $Permission_Clause$
	     	<if test="partnerId != null">
			  	and t_w_p.partner_id = #{partnerId}
			</if>
	     	GROUP BY we.employee_type
	</select>
  
   <select id="selectByEmployeeType" parameterType="map" resultMap="BaseResultMap">
	  	 SELECT tt_e.* FROM wx_t_workshop_employee tt_e LEFT JOIN wx_t_user tt_u ON tt_e.mobile=tt_u.login_name WHERE  
	  	 <if test="type!=null">
	  		 tt_e.employee_type=#{type}	 AND 
	  	 </if>
	  	 tt_u.login_name IS  NULL
 	 </select>
  
   <select id="selectByCode" resultMap="BaseResultMap" parameterType="map" >
      SELECT t.id, t.code, t.name, t.wechat_account, t.mobile, t.workshop_id, t.workshop_name, t.employee_type, 
    t.creator, t.creation_time, t.updator, t.update_time, 
    wp.partner_id, o.organization_name partner_name, w.status workshop_status
      FROM wx_t_workshop_employee t
      join wx_t_workshop_partner wp on wp.workshop_id=t.workshop_id and wp.relation_type='trade'
      join wx_t_organization o on o.id=wp.partner_id
      join wx_t_work_shop w on t.workshop_id=w.id
      where  t.code = #{code} 
   </select>
   
   <delete id="deleteWorkshopEmployee"  parameterType="map">
      delete from wx_t_workshop_employee where mobile=#{merchanicPhone} and  workshop_id=#{workshopId,jdbcType=BIGINT}  
   </delete>
   
  <select id="getEnterpriseQrcodeUrl" parameterType="map" resultType="string">
  	select top 1 dic_item_name from (
select di.dic_item_name from wx_t_dic_item di where di.dic_type_code='WorkshopEmployee.enterpriseQrcode' and di.status=1 
and di.dic_item_code='${partnerId}'
union all 
select di.dic_item_name from wx_t_dic_item di where di.dic_type_code='WorkshopEmployee.enterpriseQrcode' and di.status=1 
and di.dic_item_code='${salesChannel}'
union all 
select di.dic_item_name from wx_t_dic_item di where di.dic_type_code='WorkshopEmployee.enterpriseQrcode' and di.status=1 
and di.dic_item_code='${chevronCode}') a
  </select>
  
  <select id="validateRegisterAward" parameterType="com.chevron.pms.model.WorkshopEmployee" resultType="int">
  	select (case when (select count(1) from wx_t_workshop_employee we
			left join wx_t_point_business pb on pb.related_code=we.code where we.workshop_id=#{workshopId,jdbcType=BIGINT} 
			and pb.EARN_TYPE='MECHANIC_REGISTER' and pb.CREATION_TIME>='2020-05-11') >= 
			(select p.code from wx_t_properties p where p.codetype='B2b.awardPoints.register.maxCountByWorkshop') then 1 else 0 end)
		+ (case when (select ext_flag from wx_t_work_shop where id=#{workshopId,jdbcType=BIGINT})&amp;**********>0 then 2 else 0 end)
  </select>

    <select id="selectEmployees" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.WorkshopEmployeeParams" >
        SELECT distinct t1.id,t1.code,t1.name,t1.wechat_account,t1.mobile,t1.workshop_id,t1.workshop_name,
                        t1.employee_type,t2.partner_id,t1.version_no,t1.enable_flag,
                        t1.wechat_open_id,t1.b2b_open_id,t1.is_has_product
                        FROM wx_t_workshop_employee t1
            inner join wx_t_workshop_partner t2 on t1.workshop_id = t2.workshop_id
        <where>
            and t1.enable_flag = 1
            <if test="workShopIds != null and workShopIds.size > 0">
                and t1.workshop_id in
                <foreach collection="workShopIds" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="partnerId != null">
                and t2.partner_id =  #{partnerId}
            </if>
            <if test="isHasProduct != null and isHasProduct">
                and t1.is_has_product =  1
            </if>
        </where>
    </select>

    <select id="selectAllEmployees" resultMap="BaseResultMap">
        SELECT distinct t1.id,t1.code,t1.name,t1.wechat_account,t1.mobile,t1.workshop_id,t1.workshop_name,
                        t1.employee_type,t2.partner_id,t1.version_no,t1.enable_flag,
                        t1.wechat_open_id,t1.b2b_open_id,t1.is_has_product
        FROM wx_t_workshop_employee t1
                 inner join wx_t_work_shop t2 on t1.workshop_id = t2.id
        WHERE t2.delete_flag =0
    </select>
</mapper>