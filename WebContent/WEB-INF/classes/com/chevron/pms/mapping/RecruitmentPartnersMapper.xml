<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.RecruitmentPartnersMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.RecruitmentPartners" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="org_name" property="orgName" jdbcType="NVARCHAR" />
    <result column="register_uname" property="registerUname" jdbcType="NVARCHAR" />
    <result column="register_phoneno" property="registerPhoneno" jdbcType="NVARCHAR" />
    <result column="register_workshopscale" property="registerWorkshopscale" jdbcType="NVARCHAR" />
    <result column="register_regionname" property="registerRegionname" jdbcType="NVARCHAR" />
    <result column="register_regionid" property="registerRegionid" jdbcType="BIGINT" />
    <result column="register_email" property="registerEmail" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, org_name, register_uname, register_phoneno, register_workshopscale, register_regionname, 
    register_regionid, register_email
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.RecruitmentPartnersExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_recruitment_partners
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_recruitment_partners
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_recruitment_partners
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.pms.model.RecruitmentPartnersExample" >
    delete from wx_t_recruitment_partners
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.pms.model.RecruitmentPartners" >
    insert into wx_t_recruitment_partners (id, org_name, register_uname, 
      register_phoneno, register_workshopscale, 
      register_regionname, register_regionid, register_email
      )
    values (#{id,jdbcType=BIGINT}, #{orgName,jdbcType=NVARCHAR}, #{registerUname,jdbcType=NVARCHAR}, 
      #{registerPhoneno,jdbcType=NVARCHAR}, #{registerWorkshopscale,jdbcType=NVARCHAR}, 
      #{registerRegionname,jdbcType=NVARCHAR}, #{registerRegionid,jdbcType=BIGINT}, #{registerEmail,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.pms.model.RecruitmentPartners" >
    insert into wx_t_recruitment_partners
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
      <if test="registerUname != null" >
        register_uname,
      </if>
      <if test="registerPhoneno != null" >
        register_phoneno,
      </if>
      <if test="registerWorkshopscale != null" >
        register_workshopscale,
      </if>
      <if test="registerRegionname != null" >
        register_regionname,
      </if>
      <if test="registerRegionid != null" >
        register_regionid,
      </if>
      <if test="registerEmail != null" >
        register_email,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=NVARCHAR},
      </if>
      <if test="registerUname != null" >
        #{registerUname,jdbcType=NVARCHAR},
      </if>
      <if test="registerPhoneno != null" >
        #{registerPhoneno,jdbcType=NVARCHAR},
      </if>
      <if test="registerWorkshopscale != null" >
        #{registerWorkshopscale,jdbcType=NVARCHAR},
      </if>
      <if test="registerRegionname != null" >
        #{registerRegionname,jdbcType=NVARCHAR},
      </if>
      <if test="registerRegionid != null" >
        #{registerRegionid,jdbcType=BIGINT},
      </if>
      <if test="registerEmail != null" >
        #{registerEmail,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_recruitment_partners
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null" >
        org_name = #{record.orgName,jdbcType=NVARCHAR},
      </if>
      <if test="record.registerUname != null" >
        register_uname = #{record.registerUname,jdbcType=NVARCHAR},
      </if>
      <if test="record.registerPhoneno != null" >
        register_phoneno = #{record.registerPhoneno,jdbcType=NVARCHAR},
      </if>
      <if test="record.registerWorkshopscale != null" >
        register_workshopscale = #{record.registerWorkshopscale,jdbcType=NVARCHAR},
      </if>
      <if test="record.registerRegionname != null" >
        register_regionname = #{record.registerRegionname,jdbcType=NVARCHAR},
      </if>
      <if test="record.registerRegionid != null" >
        register_regionid = #{record.registerRegionid,jdbcType=BIGINT},
      </if>
      <if test="record.registerEmail != null" >
        register_email = #{record.registerEmail,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_recruitment_partners
    set id = #{record.id,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=NVARCHAR},
      register_uname = #{record.registerUname,jdbcType=NVARCHAR},
      register_phoneno = #{record.registerPhoneno,jdbcType=NVARCHAR},
      register_workshopscale = #{record.registerWorkshopscale,jdbcType=NVARCHAR},
      register_regionname = #{record.registerRegionname,jdbcType=NVARCHAR},
      register_regionid = #{record.registerRegionid,jdbcType=BIGINT},
      register_email = #{record.registerEmail,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.RecruitmentPartners" >
    update wx_t_recruitment_partners
    <set >
      <if test="orgName != null" >
        org_name = #{orgName,jdbcType=NVARCHAR},
      </if>
      <if test="registerUname != null" >
        register_uname = #{registerUname,jdbcType=NVARCHAR},
      </if>
      <if test="registerPhoneno != null" >
        register_phoneno = #{registerPhoneno,jdbcType=NVARCHAR},
      </if>
      <if test="registerWorkshopscale != null" >
        register_workshopscale = #{registerWorkshopscale,jdbcType=NVARCHAR},
      </if>
      <if test="registerRegionname != null" >
        register_regionname = #{registerRegionname,jdbcType=NVARCHAR},
      </if>
      <if test="registerRegionid != null" >
        register_regionid = #{registerRegionid,jdbcType=BIGINT},
      </if>
      <if test="registerEmail != null" >
        register_email = #{registerEmail,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.RecruitmentPartners" >
    update wx_t_recruitment_partners
    set org_name = #{orgName,jdbcType=NVARCHAR},
      register_uname = #{registerUname,jdbcType=NVARCHAR},
      register_phoneno = #{registerPhoneno,jdbcType=NVARCHAR},
      register_workshopscale = #{registerWorkshopscale,jdbcType=NVARCHAR},
      register_regionname = #{registerRegionname,jdbcType=NVARCHAR},
      register_regionid = #{registerRegionid,jdbcType=BIGINT},
      register_email = #{registerEmail,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>