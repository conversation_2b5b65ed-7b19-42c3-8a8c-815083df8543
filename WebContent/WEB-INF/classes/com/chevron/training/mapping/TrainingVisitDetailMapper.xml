<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.training.dao.TrainingVisitDetailMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.training.model.VisitDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="report_id" property="reportId" jdbcType="BIGINT"/>
		<result column="visit_time" property="visitTime" jdbcType="TIMESTAMP"/>
		<result column="details" property="details" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,report_id,visit_time,details
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.training.model.VisitDetail">
		update wx_t_visit_detail set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.training.model.VisitDetail">
		update wx_t_visit_detail
		<set>
			<if test="reportId != null" >
				report_id = #{reportId,jdbcType=BIGINT},
			</if>
			<if test="visitTime != null" >
				visit_time = #{visitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="details != null" >
				details = #{details,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.training.model.VisitDetailExample">
    	delete from wx_t_visit_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.training.model.VisitDetail">
		insert into wx_t_visit_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="reportId != null">
				report_id,
			</if>
			<if test="visitTime != null">
				visit_time,
			</if>
			<if test="details != null">
				details,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="reportId != null">
				#{reportId,jdbcType=BIGINT},
			</if>
			<if test="visitTime != null">
				#{visitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="details != null">
				#{details,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_visit_detail
		<set>
			<if test="record.reportId != null">
				report_id = #{record.reportId,jdbcType=BIGINT},
			</if>
			<if test="record.visitTime != null">
				visit_time = #{record.visitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.details != null">
				details = #{record.details,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.training.model.VisitDetailExample">
		delete from wx_t_visit_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.training.model.VisitDetailExample" resultType="int">
		select count(1) from wx_t_visit_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.training.model.VisitDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_visit_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.training.model.VisitDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_visit_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.report_id, t1.visit_time, t1.details
		  from wx_t_visit_detail t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_visit_detail (report_id, visit_time, details) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.reportId, jdbcType=BIGINT}, #{item.visitTime, jdbcType=TIMESTAMP}, #{item.details, jdbcType=VARCHAR}
			</trim>
		</foreach>
	</insert>

	<delete id="deleteByReportId" parameterType="com.chevron.training.model.VisitDetailExample">
		delete from wx_t_visit_detail where  report_id = #{reportId, jdbcType=BIGINT}
	</delete>
</mapper>
