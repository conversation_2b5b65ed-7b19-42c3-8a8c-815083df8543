<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.training.dao.TrainingAssessmentMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.training.model.TrainingAssessment">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="plan_id" property="planId" jdbcType="BIGINT"/>
		<result column="dsr_name" property="dsrName" jdbcType="BIGINT"/>
		<result column="evaluate" property="evaluate" jdbcType="VARCHAR"/>
		<result column="total_score" property="totalScore" jdbcType="NUMERIC"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="is_commit" property="isCommit" jdbcType="INTEGER" />
	</resultMap>

	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>

	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>

	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,plan_id,dsr_name,evaluate,total_score,create_user_id,create_time,update_user_id,update_time,is_commit
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.training.model.TrainingAssessment">
		update wx_t_training_assessment set
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.training.model.TrainingAssessment">
		update wx_t_training_assessment
		<set>
			<if test="planId != null" >
				plan_id = #{planId,jdbcType=BIGINT},
			</if>
			<if test="dsrName != null and dsrName != ''" >
				dsr_name = #{dsrName,jdbcType=VARCHAR},
			</if>
			<if test="evaluate != null" >
				evaluate = #{evaluate,jdbcType=VARCHAR},
			</if>
			<if test="totalScore != null" >
				total_score = #{totalScore,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test=" isCommit != null">
				is_commit=#{isCommit,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.training.model.TrainingAssessmentExample">
    	delete from wx_t_training_assessment
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.training.model.TrainingAssessment" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_training_assessment
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="planId != null">
				plan_id,
			</if>
			<if test="dsrName != null and dsrName != ''">
				dsr_Name,
			</if>
			<if test="evaluate != null">
				evaluate,
			</if>
			<if test="totalScore != null">
				total_score,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test=" isCommit != null">
				is_commit,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="planId != null">
				#{planId,jdbcType=BIGINT},
			</if>
			<if test="dsrName != null and dsrName != ''">
				#{dsrName,jdbcType=VARCHAR},
			</if>
			<if test="evaluate != null">
				#{evaluate,jdbcType=VARCHAR},
			</if>
			<if test="totalScore != null">
				#{totalScore,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test=" isCommit != null">
				#{isCommit,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_training_assessment
		<set>
			<if test="record.planId != null">
				plan_id = #{record.planId,jdbcType=BIGINT},
			</if>
			<if test="record.dsrName != null and record.dsrName != ''">
				dsr_name = #{record.dsName,jdbcType=BIGINT},
			</if>
			<if test="record.evaluate != null">
				evaluate = #{record.evaluate,jdbcType=VARCHAR},
			</if>
			<if test="record.totalScore != null">
				total_score = #{record.totalScore,jdbcType=NUMERIC},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test=" record.isCommit != null">
				is_commit=#{record.isCommit,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>

	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.training.model.TrainingAssessmentExample">
		delete from wx_t_training_assessment
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>

	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.training.model.TrainingAssessmentExample" resultType="int">
		select count(1) from wx_t_training_assessment
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>

	<update id="commit" parameterType="java.lang.Long" >
		update wx_t_training_assessment set is_commit=1 where plan_id=#{planId,jdbcType=BIGINT}
	</update>

	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.training.model.TrainingAssessmentExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_training_assessment
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.training.model.TrainingAssessmentExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_training_assessment
		where id = #{id,jdbcType=BIGINT}
	</select>

	<select id="selectByPlanId" resultMap="BaseResultMap" parameterType="com.chevron.training.model.TrainingAssessmentExample">
		select
		<include refid="Base_Column_List"/>
		from wx_t_training_assessment
		where plan_id = #{planId,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.plan_id, t1.dsr_NAME, t1.evaluate, t1.total_score, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,is_commit
		  from wx_t_training_assessment t1
		 where 1=1
	</select>

	<!-- 分页查询 -->
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.training.model.TrainingAssessmentParams">
		select t1.id, t1.plan_id, t1.dsr_NAME, t1.evaluate, t1.total_score, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,is_commit
		  from wx_t_training_assessment t1
		 where 1=1
		 <if test="planId != null">
			and t1.plan_id = #{planId, jdbcType=BIGINT}
		 </if>
	</select>
</mapper>
