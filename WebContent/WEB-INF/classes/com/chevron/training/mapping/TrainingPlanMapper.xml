<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.training.dao.TrainingPlanMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.chevron.training.model.Plan">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="curriculum_id" property="curriculumId" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="traning_days" property="traningDays" jdbcType="INTEGER"/>
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
        <result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
        <result column="distributor_conctor" property="distributorConctor" jdbcType="VARCHAR"/>
        <result column="distributor_address" property="distributorAddress" jdbcType="VARCHAR"/>
        <result column="distributor_phone" property="distributorPhone" jdbcType="VARCHAR"/>
        <result column="asm_cai" property="asmCai" jdbcType="VARCHAR"/>
        <result column="asm_name" property="asmName" jdbcType="VARCHAR"/>
        <result column="sales_cai" property="salesCai" jdbcType="VARCHAR"/>
        <result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
        <result column="sales_phone" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="traning_teacher" property="traningTeacher" jdbcType="VARCHAR"/>
        <result column="teacher_phone" property="teacherPhone" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="name" property="curriculumName" jdbcType="VARCHAR"/>
        <result column="survey_commit" property="surveyCommit" jdbcType="INTEGER" />
        <result column="report_commit" property="reportCommit" jdbcType="INTEGER" />
        <result column="assess_commit" property="assessCommit" jdbcType="INTEGER" />
    </resultMap>

    <!-- 查询Example条件 -->
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <!-- 更新操作Example条件 -->
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <!-- 查询表字段集合 -->
    <sql id="Base_Column_List">
		id,curriculum_id,start_time,end_time,traning_days,distributor_id,distributor_name,distributor_conctor,
		distributor_address,distributor_phone,asm_cai,asm_name,sales_cai,sales_name,sales_phone,user_id,traning_teacher,
		teacher_phone,remark,delete_flag,create_user_id,create_time,update_user_id,update_time
	</sql>

    <!-- 修改页面根据主键更新记录编辑字段 -->
    <update id="updateForEditPage" parameterType="com.chevron.training.model.Plan">
		update wx_t_plan set
				start_time = #{startTime,jdbcType=TIMESTAMP},
				distributor_id = #{distributorId,jdbcType=BIGINT},
				asm_cai = #{asmCai,jdbcType=VARCHAR},
				sales_cai = #{salesCai,jdbcType=VARCHAR},
				traning_teacher = #{traningTeacher,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>

    <!-- 根据主键更新记录有值字段 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.chevron.training.model.Plan">
        update wx_t_plan
        <set>
            <if test="curriculumId != null">
                curriculum_id = #{curriculumId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="traningDays != null">
                traning_days = #{traningDays,jdbcType=INTEGER},
            </if>
            <if test="distributorId != null">
                distributor_id = #{distributorId,jdbcType=BIGINT},
            </if>
            <if test="distributorName != null">
                distributor_name = #{distributorName,jdbcType=VARCHAR},
            </if>
            <if test="distributorConctor != null">
                distributor_conctor = #{distributorConctor,jdbcType=VARCHAR},
            </if>
            <if test="distributorAddress != null">
                distributor_address = #{distributorAddress,jdbcType=VARCHAR},
            </if>
            <if test="distributorPhone != null">
                distributor_phone = #{distributorPhone,jdbcType=VARCHAR},
            </if>
            <if test="asmCai != null">
                asm_cai = #{asmCai,jdbcType=VARCHAR},
            </if>
            <if test="asmName != null">
                asm_name = #{asmName,jdbcType=VARCHAR},
            </if>
            <if test="salesCai != null">
                sales_cai = #{salesCai,jdbcType=VARCHAR},
            </if>
            <if test="salesName != null">
                sales_name = #{salesName,jdbcType=VARCHAR},
            </if>
            <if test="salesPhone != null">
                sales_phone = #{salesPhone,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="traningTeacher != null">
                traning_teacher = #{traningTeacher,jdbcType=VARCHAR},
            </if>
            <if test="teacherPhone != null">
                teacher_phone = #{teacherPhone,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主键删除记录 -->
    <delete id="deleteByPrimaryKey" parameterType="com.chevron.training.model.PlanExample">
    	delete from wx_t_plan
		where id = #{id,jdbcType=BIGINT}
	</delete>

    <!-- 插入记录有值字段 -->
    <insert id="insertSelective" parameterType="com.chevron.training.model.Plan" useGeneratedKeys="true"
            keyProperty="id">
        insert into wx_t_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curriculumId != null">
                curriculum_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="traningDays != null">
                traning_days,
            </if>
            <if test="distributorId != null">
                distributor_id,
            </if>
            <if test="distributorName != null">
                distributor_name,
            </if>
            <if test="distributorConctor != null">
                distributor_conctor,
            </if>
            <if test="distributorAddress != null">
                distributor_address,
            </if>
            <if test="distributorPhone != null">
                distributor_phone,
            </if>
            <if test="asmCai != null">
                asm_cai,
            </if>
            <if test="asmName != null">
                asm_name,
            </if>
            <if test="salesCai != null">
                sales_cai,
            </if>
            <if test="salesName != null">
                sales_name,
            </if>
            <if test="salesPhone != null">
                sales_phone,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="traningTeacher != null">
                traning_teacher,
            </if>
            <if test="teacherPhone != null">
                teacher_phone,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curriculumId != null">
                #{curriculumId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="traningDays != null">
                #{traningDays,jdbcType=INTEGER},
            </if>
            <if test="distributorId != null">
                #{distributorId,jdbcType=BIGINT},
            </if>
            <if test="distributorName != null">
                #{distributorName,jdbcType=VARCHAR},
            </if>
            <if test="distributorConctor != null">
                #{distributorConctor,jdbcType=VARCHAR},
            </if>
            <if test="distributorAddress != null">
                #{distributorAddress,jdbcType=VARCHAR},
            </if>
            <if test="distributorPhone != null">
                #{distributorPhone,jdbcType=VARCHAR},
            </if>
            <if test="asmCai != null">
                #{asmCai,jdbcType=VARCHAR},
            </if>
            <if test="asmName != null">
                #{asmName,jdbcType=VARCHAR},
            </if>
            <if test="salesCai != null">
                #{salesCai,jdbcType=VARCHAR},
            </if>
            <if test="salesName != null">
                #{salesName,jdbcType=VARCHAR},
            </if>
            <if test="salesPhone != null">
                #{salesPhone,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="traningTeacher != null">
                #{traningTeacher,jdbcType=VARCHAR},
            </if>
            <if test="teacherPhone != null">
                #{teacherPhone,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 更新满足Example条件的有值字段 -->
    <update id="updateByExampleSelective" parameterType="map">
        update wx_t_plan
        <set>
            <if test="record.curriculumId != null">
                curriculum_id = #{record.curriculumId,jdbcType=BIGINT},
            </if>
            <if test="record.startTime != null">
                start_time = #{record.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.endTime != null">
                end_time = #{record.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.traningDays != null">
                traning_days = #{record.traningDays,jdbcType=INTEGER},
            </if>
            <if test="record.distributorId != null">
                distributor_id = #{record.distributorId,jdbcType=BIGINT},
            </if>
            <if test="record.distributorName != null">
                distributor_name = #{record.distributorName,jdbcType=VARCHAR},
            </if>
            <if test="record.distributorConctor != null">
                distributor_conctor = #{record.distributorConctor,jdbcType=VARCHAR},
            </if>
            <if test="record.distributorAddress != null">
                distributor_address = #{record.distributorAddress,jdbcType=VARCHAR},
            </if>
            <if test="record.distributorPhone != null">
                distributor_phone = #{record.distributorPhone,jdbcType=VARCHAR},
            </if>
            <if test="record.asmCai != null">
                asm_cai = #{record.asmCai,jdbcType=VARCHAR},
            </if>
            <if test="record.asmName != null">
                asm_name = #{record.asmName,jdbcType=VARCHAR},
            </if>
            <if test="record.salesCai != null">
                sales_cai = #{record.salesCai,jdbcType=VARCHAR},
            </if>
            <if test="record.salesName != null">
                sales_name = #{record.salesName,jdbcType=VARCHAR},
            </if>
            <if test="record.salesPhone != null">
                sales_phone = #{record.salesPhone,jdbcType=VARCHAR},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=BIGINT},
            </if>
            <if test="record.traningTeacher != null">
                traning_teacher = #{record.traningTeacher,jdbcType=VARCHAR},
            </if>
            <if test="record.teacherPhone != null">
                teacher_phone = #{record.teacherPhone,jdbcType=VARCHAR},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.deleteFlag != null">
                delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="record.createUserId != null">
                create_user_id = #{record.createUserId,jdbcType=BIGINT},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateUserId != null">
                update_user_id = #{record.updateUserId,jdbcType=BIGINT},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>

    <!-- 删除满足Example条件的记录 -->
    <delete id="deleteByExample" parameterType="com.chevron.training.model.PlanExample">
        delete from wx_t_plan
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>

    <!-- 统计满足Example条件的记录条数 -->
    <select id="countByExample" parameterType="com.chevron.training.model.PlanExample" resultType="int">
        select count(1) from wx_t_plan
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <!-- 查询满足Example条件的记录集合 -->
    <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.training.model.PlanExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from wx_t_plan
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <!-- 根据主键查询记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.training.model.PlanExample">
        select
        <include refid="Base_Column_List"/>
        from wx_t_plan
        where id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 列表查询 -->
    <select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
        select t1.id, t1.curriculum_id, t1.start_time, t1.end_time, t1.traning_days, t1.distributor_id,
        t1.distributor_name,
        t1.distributor_conctor, t1.distributor_address, t1.distributor_phone, t1.asm_cai, t1.asm_name, t1.sales_cai,
        t1.sales_name, t1.sales_phone, t1.user_id, t1.traning_teacher, t1.teacher_phone, t1.remark, t1.delete_flag,
        t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
        from wx_t_plan t1
        where 1=1
        <if test="dateFrom != null">
            and t1.start_time &gt;= #{dateFrom, jdbcType=DATE}
        </if>
        <if test="dateTo != null">
            and t1.start_time &lt; #{dateTo, jdbcType=DATE}
        </if>
        <if test="distributorId != null">
            and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
        </if>
        <if test="asmCai != null and asmCai != ''">
            and t1.asm_cai like '%' + #{asmCai, jdbcType=VARCHAR} + '%'
        </if>
        <if test="salesCai != null and salesCai != ''">
            and t1.sales_cai like '%' + #{salesCai, jdbcType=VARCHAR} + '%'
        </if>
        <if test="traningTeacher != null and traningTeacher != ''">
            and t1.traning_teacher like '%' + #{traningTeacher, jdbcType=VARCHAR} + '%'
        </if>
    </select>

    <!-- 分页查询 -->
    <select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.training.model.PlanParams">
        select t1.id, t1.curriculum_id, t1.start_time, t1.end_time, t1.traning_days, t1.distributor_id,
        t1.distributor_name,
        t1.distributor_conctor, t1.distributor_address, t1.distributor_phone, t1.asm_cai, t1.asm_name, t1.sales_cai,
        t1.sales_name, t1.sales_phone, t1.user_id, t1.traning_teacher, t1.teacher_phone, t1.remark, t1.delete_flag,
        t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,t2.name,
        ISNULL((select is_commit from wx_t_training_survey a where a.plan_id=t1.id),0)  as survey_commit,
        ISNULL((select is_commit from wx_t_training_report b where b.plan_id=t1.id),0) as report_commit,
        ISNULL((select distinct is_commit from wx_t_training_assessment c where c.plan_id=t1.id),0) as assess_commit
        from wx_t_plan t1 left join wx_t_curriculum t2 on t1.curriculum_id=t2.id
        where t1.delete_flag=0 $Permission_Clause$
        <if test="dateFrom != null and dateTo != null ">
            <![CDATA[
		and (
		((t1.start_time >= #{dateFrom, jdbcType=DATE} and t1.start_time <  #{dateTo, jdbcType=DATE})
		      or (t1.end_time >= #{dateFrom, jdbcType=DATE} and t1.end_time < #{dateTo,jdbcType=DATE}))
			or
			(t1.start_time <= #{dateFrom, jdbcType=DATE} and t1.end_time >= #{dateTo, jdbcType=DATE})
			)

		]]>
        </if>

        <if test="distributorId != null">
            and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
        </if>
        <if test="asmCai != null and asmCai != ''">
            and t1.asm_cai like '%' + #{asmCai, jdbcType=VARCHAR} + '%'
        </if>
        <if test="salesCai != null and salesCai != ''">
            and t1.sales_cai like '%' + #{salesCai, jdbcType=VARCHAR} + '%'
        </if>
        <if test="traningTeacher != null and traningTeacher != ''">
            and t1.traning_teacher like '%' + #{traningTeacher, jdbcType=VARCHAR} + '%'
        </if>
        <if test="userId != null ">
            and t1.user_id = #{userId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="map">
        insert into wx_t_plan (
        curriculum_id,
        start_time,
        end_time,
        traning_days,
        distributor_id,
        distributor_name,
        distributor_conctor,
        distributor_address,
        distributor_phone,
        asm_cai,
        asm_name,
        sales_cai,
        sales_name,
        sales_phone,
        user_id,
        traning_teacher,
        teacher_phone,
        remark,
        delete_flag,
        create_user_id,
        create_time) values
        <foreach collection="records" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.curriculumId,jdbcType=BIGINT},
                #{item.startTime,jdbcType=TIMESTAMP},
                #{item.endTime,jdbcType=TIMESTAMP},
                #{item.traningDays,jdbcType=INTEGER},
                #{item.distributorId,jdbcType=BIGINT},
                #{item.distributorName,jdbcType=VARCHAR},
                #{item.distributorConctor,jdbcType=VARCHAR},
                #{item.distributorAddress,jdbcType=VARCHAR},
                #{item.distributorPhone,jdbcType=VARCHAR},
                #{item.asmCai,jdbcType=VARCHAR},
                #{item.asmName,jdbcType=VARCHAR},
                #{item.salesCai,jdbcType=VARCHAR},
                #{item.salesName,jdbcType=VARCHAR},
                #{item.salesPhone,jdbcType=VARCHAR},
                #{item.userId,jdbcType=BIGINT},
                #{item.traningTeacher,jdbcType=VARCHAR},
                #{item.teacherPhone,jdbcType=VARCHAR},
                #{item.remark,jdbcType=VARCHAR},
                #{item.deleteFlag,jdbcType=INTEGER},
                #{item.createUserId,jdbcType=BIGINT},
                #{item.createTime,jdbcType=TIMESTAMP}
            </trim>
        </foreach>
    </insert>
</mapper>
