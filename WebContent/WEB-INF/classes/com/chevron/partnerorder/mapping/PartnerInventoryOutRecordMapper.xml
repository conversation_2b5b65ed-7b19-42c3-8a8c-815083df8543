<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.PartnerInventoryOutRecordMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.PartnerInventoryOutRecord" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="stock_out_no" property="stockOutNo" jdbcType="NVARCHAR" />
    <result column="code" property="code" jdbcType="NVARCHAR" />
    <result column="code_type" property="codeType" jdbcType="NVARCHAR" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, partner_name, stock_out_no, code, code_type, sku, product_name, create_time, 
    remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.PartnerInventoryOutRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_inventory_out_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.PartnerInventoryOutRecordExample" >
    delete from wx_t_partner_inventory_out_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.PartnerInventoryOutRecord" >
    insert into wx_t_partner_inventory_out_record (id, partner_id, partner_name, 
      stock_out_no, code, code_type, 
      sku, product_name, create_time, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, 
      #{stockOutNo,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, #{codeType,jdbcType=NVARCHAR}, 
      #{sku,jdbcType=NVARCHAR}, #{productName,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.PartnerInventoryOutRecord" >
    insert into wx_t_partner_inventory_out_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerName != null" >
        partner_name,
      </if>
      <if test="stockOutNo != null" >
        stock_out_no,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="codeType != null" >
        code_type,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="stockOutNo != null" >
        #{stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="code != null" >
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="codeType != null" >
        #{codeType,jdbcType=NVARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_partner_inventory_out_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null" >
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockOutNo != null" >
        stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=NVARCHAR},
      </if>
      <if test="record.codeType != null" >
        code_type = #{record.codeType,jdbcType=NVARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.productName != null" >
        product_name = #{record.productName,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_partner_inventory_out_record
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      stock_out_no = #{record.stockOutNo,jdbcType=NVARCHAR},
      code = #{record.code,jdbcType=NVARCHAR},
      code_type = #{record.codeType,jdbcType=NVARCHAR},
      sku = #{record.sku,jdbcType=NVARCHAR},
      product_name = #{record.productName,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertBatch" parameterType="java.util.List">
     insert into wx_t_partner_inventory_out_record (partner_id, partner_name, 
      stock_out_no, code, code_type, sku,product_name,
      create_time
      ) values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.partnerId,jdbcType=BIGINT},
			#{item.partnerName,jdbcType=NVARCHAR},
			#{item.stockOutNo,jdbcType=NVARCHAR},
			#{item.code,jdbcType=NVARCHAR},
			#{item.codeType,jdbcType=NVARCHAR},
			#{item.sku,jdbcType=NVARCHAR},
			#{item.productName,jdbcType=NVARCHAR},
			#{item.createTime,jdbcType=TIMESTAMP}
		</trim>
	 </foreach>
   </insert>
  <select id="countRecord" resultType="int" parameterType="map" >
    select count(1)
    from wx_t_partner_inventory_out_record pior1
    where 1=1
    <if test="code != null and code != ''" >
      and code=#{code}
    </if>
    <if test="partnerNames != null" >
    	and exists (select 1 from wx_t_partner_order_address_product ap1 join wx_t_partner_order po1 on po1.id=ap1.partner_order_id join wx_t_organization o on po1.partner_id=o.id where ap1.out_stock_no=pior1.stock_out_no
      <foreach collection="partnerNames" item="item" index="index" open=" and o.organization_name in (" close=")" separator=",">
      '${item}'
      </foreach>
      )
    </if>
  </select>
  <select id="countOnlineRecord" resultType="int" parameterType="map" >
    select (select count(1)
    from wx_t_partner_inventory_out_record pior1
    where code=#{code}
    	and exists (select 1 from wx_t_partner_order_address_product ap1 join wx_t_partner_order po1 on po1.id=ap1.partner_order_id join wx_t_organization o on po1.partner_id=o.id where ap1.out_stock_no=pior1.stock_out_no
      <foreach collection="partnerNames" item="item" index="index" open=" and o.organization_name in (" close=")" separator=",">
      '${item}'
      </foreach>
      )) + (select count(1)
    from wx_t_out_stock_line osl1 left join wx_t_out_stock os1 on os1.stock_out_no=osl1.stock_out_no
    where osl1.code=#{code}
    	and exists (select 1 from wx_t_organization o where os1.stock_to=o.id
      <foreach collection="partnerNames" item="item" index="index" open=" and o.organization_name in (" close=")" separator=",">
      '${item}'
      </foreach>
      ))
  </select>
  <select id="countUnsanableRecord" resultType="int" parameterType="map" >
    select (select count(1)
    from wx_t_partner_inventory_out_record pior1
    where code=#{code}
    	and exists (select 1 from wx_t_partner_order_address_product ap1 join wx_t_partner_order po1 on po1.id=ap1.partner_order_id where ap1.out_stock_no=pior1.stock_out_no and po1.is_scan_code='0'
      )) + (select count(1)
    from wx_t_out_stock_line osl1 left join wx_t_out_stock os1 on os1.stock_out_no=osl1.stock_out_no
    where osl1.code=#{code} and order_type='SP'
    	and exists (select 1 from wx_t_partner_order po1 where po1.order_no=os1.order_no and po1.is_scan_code='0'))
  </select>
</mapper>