<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.PartnerInventoryCheckHistoryVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="updater" property="updater" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sku, product_name, partner_id, partner_name, units, quantity,status, create_time, update_time, 
    creator, updater, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_inventory_check_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryVoExample" >
    delete from wx_t_partner_inventory_check_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_partner_inventory_check_history (id, sku, product_name, 
      partner_id, partner_name, units, 
      quantity,status, create_time, 
      update_time, creator, updater, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=NVARCHAR}, #{productName,jdbcType=NVARCHAR}, 
      #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, #{units,jdbcType=NVARCHAR}, 
      #{quantity,jdbcType=INTEGER}, #{status,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=NVARCHAR}, #{updater,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_partner_inventory_check_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerName != null" >
        partner_name,
      </if>
      <if test="units != null" >
        units,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="units != null" >
        #{units,jdbcType=NVARCHAR},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_partner_inventory_check_history
    <set >
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.productName != null" >
        product_name = #{record.productName,jdbcType=NVARCHAR},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null" >
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.units != null" >
        units = #{record.units,jdbcType=NVARCHAR},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.updater != null" >
        updater = #{record.updater,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_partner_inventory_check_history
    set 
      sku = #{record.sku,jdbcType=NVARCHAR},
      product_name = #{record.productName,jdbcType=NVARCHAR},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      units = #{record.units,jdbcType=NVARCHAR},
      quantity = #{record.quantity,jdbcType=INTEGER},
      status = #{record.status,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      updater = #{record.updater,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="getPartnerInventoryCheckHistoryPagenationByCondition" parameterType="com.chevron.partnerorder.model.PartnerInventoryCheckHistoryCondition" resultMap="BaseResultMap">
		 <!-- 数据权限开启的情况 -->
   		 <if test="isOpernCustomMybatisInterceptor==1">
		  select 
		  	    pich.* 
		  	    from wx_t_partner_inventory_check_history pich
		 	where 1 = 1 
		     <if test="queryType == 1">
				  <if test="partnerId != null">
				  	 AND pich.partner_id = #{partnerId}
				  </if>
				  <if test="sku != null">
				     AND pich.sku = #{sku}
				  </if>		  
			 </if>
		 </if>
	</select>
</mapper>