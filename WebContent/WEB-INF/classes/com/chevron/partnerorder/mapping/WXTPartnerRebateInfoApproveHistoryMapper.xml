<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.WXTPartnerRebateInfoApproveHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.WXTPartnerRebateInfoApproveHistory" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="year_str" property="yearStr" jdbcType="VARCHAR" />
    <result column="month_str" property="monthStr" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="actual_rebate_amount" property="actualRebateAmount" jdbcType="NUMERIC" />
    <result column="actual_deserved_rebate_discount_ratio" property="actualDeservedRebateDiscountRatio" jdbcType="NUMERIC" />
    <result column="actual_deserved_rebate_amount" property="actualDeservedRebateAmount" jdbcType="NUMERIC" />
    <result column="current_month_rebate_amount" property="currentMonthRebateAmount" jdbcType="NUMERIC" />
    <result column="total_rebate_amount" property="totalRebateAmount" jdbcType="NUMERIC" />
    <result column="workshop_is_reach" property="workshopIsReach" jdbcType="VARCHAR" />
    <result column="workshop_reach_ratio" property="workshopReachRatio" jdbcType="NUMERIC" />
    <result column="active_workshop_number" property="activeWorkshopNumber" jdbcType="INTEGER" />
    <result column="workshop_not_reach_discount_amount" property="workshopNotReachDiscountAmount" jdbcType="NUMERIC" />
    <result column="workshop_not_reach_discount_ratio" property="workshopNotReachDiscountRatio" jdbcType="NUMERIC" />
    <result column="sales_is_reach" property="salesIsReach" jdbcType="VARCHAR" />
    <result column="scancode_reach_ratio" property="scancodeReachRatio" jdbcType="NUMERIC" />
    <result column="purchase_liters" property="purchaseLiters" jdbcType="BIGINT" />
    <result column="sales_liters" property="salesLiters" jdbcType="BIGINT" />
    <result column="scan_code_ratio" property="scanCodeRatio" jdbcType="NUMERIC" />
    <result column="scancode_not_reach_discount_amount" property="scancodeNotReachDiscountAmount" jdbcType="NUMERIC" />
    <result column="scancode_not_reach_discount_ratio" property="scancodeNotReachDiscountRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_is_reach" property="fleeingGoodsIsReach" jdbcType="VARCHAR" />
    <result column="fleeing_goods_ratio" property="fleeingGoodsRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_discount_amount" property="fleeingGoodsDiscountAmount" jdbcType="NUMERIC" />
    <result column="fleeing_goods_discount_ratio" property="fleeingGoodsDiscountRatio" jdbcType="NUMERIC" />
    <result column="fleeing_goods_remark" property="fleeingGoodsRemark" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, year_str, month_str, create_time, update_time, actual_rebate_amount, 
    actual_deserved_rebate_discount_ratio, actual_deserved_rebate_amount, current_month_rebate_amount, 
    total_rebate_amount, workshop_is_reach, workshop_reach_ratio, active_workshop_number, 
    workshop_not_reach_discount_amount, workshop_not_reach_discount_ratio, sales_is_reach, 
    scancode_reach_ratio, purchase_liters, sales_liters, scan_code_ratio, scancode_not_reach_discount_amount, 
    scancode_not_reach_discount_ratio, fleeing_goods_is_reach, fleeing_goods_ratio, fleeing_goods_discount_amount, 
    fleeing_goods_discount_ratio, fleeing_goods_remark, remark, status, creator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoApproveHistoryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_rebate_info_approve_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoApproveHistoryExample" >
    delete from wx_t_partner_rebate_info_approve_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoApproveHistory" >
    insert into wx_t_partner_rebate_info_approve_history (id, partner_id, year_str, 
      month_str, create_time, update_time, 
      actual_rebate_amount, actual_deserved_rebate_discount_ratio, 
      actual_deserved_rebate_amount, current_month_rebate_amount, 
      total_rebate_amount, workshop_is_reach, workshop_reach_ratio, 
      active_workshop_number, workshop_not_reach_discount_amount, 
      workshop_not_reach_discount_ratio, sales_is_reach, 
      scancode_reach_ratio, purchase_liters, sales_liters, 
      scan_code_ratio, scancode_not_reach_discount_amount, 
      scancode_not_reach_discount_ratio, fleeing_goods_is_reach, 
      fleeing_goods_ratio, fleeing_goods_discount_amount, 
      fleeing_goods_discount_ratio, fleeing_goods_remark, 
      remark, status, creator
      )
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{yearStr,jdbcType=VARCHAR}, 
      #{monthStr,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{actualRebateAmount,jdbcType=NUMERIC}, #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC}, 
      #{actualDeservedRebateAmount,jdbcType=NUMERIC}, #{currentMonthRebateAmount,jdbcType=NUMERIC}, 
      #{totalRebateAmount,jdbcType=NUMERIC}, #{workshopIsReach,jdbcType=VARCHAR}, #{workshopReachRatio,jdbcType=NUMERIC}, 
      #{activeWorkshopNumber,jdbcType=INTEGER}, #{workshopNotReachDiscountAmount,jdbcType=NUMERIC}, 
      #{workshopNotReachDiscountRatio,jdbcType=NUMERIC}, #{salesIsReach,jdbcType=VARCHAR}, 
      #{scancodeReachRatio,jdbcType=NUMERIC}, #{purchaseLiters,jdbcType=BIGINT}, #{salesLiters,jdbcType=BIGINT}, 
      #{scanCodeRatio,jdbcType=NUMERIC}, #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC}, 
      #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC}, #{fleeingGoodsIsReach,jdbcType=VARCHAR}, 
      #{fleeingGoodsRatio,jdbcType=NUMERIC}, #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC}, 
      #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC}, #{fleeingGoodsRemark,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=NVARCHAR}, #{creator,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.WXTPartnerRebateInfoApproveHistory" >
    insert into wx_t_partner_rebate_info_approve_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="yearStr != null" >
        year_str,
      </if>
      <if test="monthStr != null" >
        month_str,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="actualRebateAmount != null" >
        actual_rebate_amount,
      </if>
      <if test="actualDeservedRebateDiscountRatio != null" >
        actual_deserved_rebate_discount_ratio,
      </if>
      <if test="actualDeservedRebateAmount != null" >
        actual_deserved_rebate_amount,
      </if>
      <if test="currentMonthRebateAmount != null" >
        current_month_rebate_amount,
      </if>
      <if test="totalRebateAmount != null" >
        total_rebate_amount,
      </if>
      <if test="workshopIsReach != null" >
        workshop_is_reach,
      </if>
      <if test="workshopReachRatio != null" >
        workshop_reach_ratio,
      </if>
      <if test="activeWorkshopNumber != null" >
        active_workshop_number,
      </if>
      <if test="workshopNotReachDiscountAmount != null" >
        workshop_not_reach_discount_amount,
      </if>
      <if test="workshopNotReachDiscountRatio != null" >
        workshop_not_reach_discount_ratio,
      </if>
      <if test="salesIsReach != null" >
        sales_is_reach,
      </if>
      <if test="scancodeReachRatio != null" >
        scancode_reach_ratio,
      </if>
      <if test="purchaseLiters != null" >
        purchase_liters,
      </if>
      <if test="salesLiters != null" >
        sales_liters,
      </if>
      <if test="scanCodeRatio != null" >
        scan_code_ratio,
      </if>
      <if test="scancodeNotReachDiscountAmount != null" >
        scancode_not_reach_discount_amount,
      </if>
      <if test="scancodeNotReachDiscountRatio != null" >
        scancode_not_reach_discount_ratio,
      </if>
      <if test="fleeingGoodsIsReach != null" >
        fleeing_goods_is_reach,
      </if>
      <if test="fleeingGoodsRatio != null" >
        fleeing_goods_ratio,
      </if>
      <if test="fleeingGoodsDiscountAmount != null" >
        fleeing_goods_discount_amount,
      </if>
      <if test="fleeingGoodsDiscountRatio != null" >
        fleeing_goods_discount_ratio,
      </if>
      <if test="fleeingGoodsRemark != null" >
        fleeing_goods_remark,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="creator != null" >
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="yearStr != null" >
        #{yearStr,jdbcType=VARCHAR},
      </if>
      <if test="monthStr != null" >
        #{monthStr,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualRebateAmount != null" >
        #{actualRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateDiscountRatio != null" >
        #{actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="actualDeservedRebateAmount != null" >
        #{actualDeservedRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="currentMonthRebateAmount != null" >
        #{currentMonthRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalRebateAmount != null" >
        #{totalRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopIsReach != null" >
        #{workshopIsReach,jdbcType=VARCHAR},
      </if>
      <if test="workshopReachRatio != null" >
        #{workshopReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="activeWorkshopNumber != null" >
        #{activeWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="workshopNotReachDiscountAmount != null" >
        #{workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="workshopNotReachDiscountRatio != null" >
        #{workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="salesIsReach != null" >
        #{salesIsReach,jdbcType=VARCHAR},
      </if>
      <if test="scancodeReachRatio != null" >
        #{scancodeReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="purchaseLiters != null" >
        #{purchaseLiters,jdbcType=BIGINT},
      </if>
      <if test="salesLiters != null" >
        #{salesLiters,jdbcType=BIGINT},
      </if>
      <if test="scanCodeRatio != null" >
        #{scanCodeRatio,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountAmount != null" >
        #{scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="scancodeNotReachDiscountRatio != null" >
        #{scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsIsReach != null" >
        #{fleeingGoodsIsReach,jdbcType=VARCHAR},
      </if>
      <if test="fleeingGoodsRatio != null" >
        #{fleeingGoodsRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountAmount != null" >
        #{fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsDiscountRatio != null" >
        #{fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="fleeingGoodsRemark != null" >
        #{fleeingGoodsRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_partner_rebate_info_approve_history
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.yearStr != null" >
        year_str = #{record.yearStr,jdbcType=VARCHAR},
      </if>
      <if test="record.monthStr != null" >
        month_str = #{record.monthStr,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.actualRebateAmount != null" >
        actual_rebate_amount = #{record.actualRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.actualDeservedRebateDiscountRatio != null" >
        actual_deserved_rebate_discount_ratio = #{record.actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.actualDeservedRebateAmount != null" >
        actual_deserved_rebate_amount = #{record.actualDeservedRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.currentMonthRebateAmount != null" >
        current_month_rebate_amount = #{record.currentMonthRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.totalRebateAmount != null" >
        total_rebate_amount = #{record.totalRebateAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.workshopIsReach != null" >
        workshop_is_reach = #{record.workshopIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.workshopReachRatio != null" >
        workshop_reach_ratio = #{record.workshopReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.activeWorkshopNumber != null" >
        active_workshop_number = #{record.activeWorkshopNumber,jdbcType=INTEGER},
      </if>
      <if test="record.workshopNotReachDiscountAmount != null" >
        workshop_not_reach_discount_amount = #{record.workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.workshopNotReachDiscountRatio != null" >
        workshop_not_reach_discount_ratio = #{record.workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.salesIsReach != null" >
        sales_is_reach = #{record.salesIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.scancodeReachRatio != null" >
        scancode_reach_ratio = #{record.scancodeReachRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.purchaseLiters != null" >
        purchase_liters = #{record.purchaseLiters,jdbcType=BIGINT},
      </if>
      <if test="record.salesLiters != null" >
        sales_liters = #{record.salesLiters,jdbcType=BIGINT},
      </if>
      <if test="record.scanCodeRatio != null" >
        scan_code_ratio = #{record.scanCodeRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.scancodeNotReachDiscountAmount != null" >
        scancode_not_reach_discount_amount = #{record.scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.scancodeNotReachDiscountRatio != null" >
        scancode_not_reach_discount_ratio = #{record.scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsIsReach != null" >
        fleeing_goods_is_reach = #{record.fleeingGoodsIsReach,jdbcType=VARCHAR},
      </if>
      <if test="record.fleeingGoodsRatio != null" >
        fleeing_goods_ratio = #{record.fleeingGoodsRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsDiscountAmount != null" >
        fleeing_goods_discount_amount = #{record.fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsDiscountRatio != null" >
        fleeing_goods_discount_ratio = #{record.fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.fleeingGoodsRemark != null" >
        fleeing_goods_remark = #{record.fleeingGoodsRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_partner_rebate_info_approve_history
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      year_str = #{record.yearStr,jdbcType=VARCHAR},
      month_str = #{record.monthStr,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      actual_rebate_amount = #{record.actualRebateAmount,jdbcType=NUMERIC},
      actual_deserved_rebate_discount_ratio = #{record.actualDeservedRebateDiscountRatio,jdbcType=NUMERIC},
      actual_deserved_rebate_amount = #{record.actualDeservedRebateAmount,jdbcType=NUMERIC},
      current_month_rebate_amount = #{record.currentMonthRebateAmount,jdbcType=NUMERIC},
      total_rebate_amount = #{record.totalRebateAmount,jdbcType=NUMERIC},
      workshop_is_reach = #{record.workshopIsReach,jdbcType=VARCHAR},
      workshop_reach_ratio = #{record.workshopReachRatio,jdbcType=NUMERIC},
      active_workshop_number = #{record.activeWorkshopNumber,jdbcType=INTEGER},
      workshop_not_reach_discount_amount = #{record.workshopNotReachDiscountAmount,jdbcType=NUMERIC},
      workshop_not_reach_discount_ratio = #{record.workshopNotReachDiscountRatio,jdbcType=NUMERIC},
      sales_is_reach = #{record.salesIsReach,jdbcType=VARCHAR},
      scancode_reach_ratio = #{record.scancodeReachRatio,jdbcType=NUMERIC},
      purchase_liters = #{record.purchaseLiters,jdbcType=BIGINT},
      sales_liters = #{record.salesLiters,jdbcType=BIGINT},
      scan_code_ratio = #{record.scanCodeRatio,jdbcType=NUMERIC},
      scancode_not_reach_discount_amount = #{record.scancodeNotReachDiscountAmount,jdbcType=NUMERIC},
      scancode_not_reach_discount_ratio = #{record.scancodeNotReachDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_is_reach = #{record.fleeingGoodsIsReach,jdbcType=VARCHAR},
      fleeing_goods_ratio = #{record.fleeingGoodsRatio,jdbcType=NUMERIC},
      fleeing_goods_discount_amount = #{record.fleeingGoodsDiscountAmount,jdbcType=NUMERIC},
      fleeing_goods_discount_ratio = #{record.fleeingGoodsDiscountRatio,jdbcType=NUMERIC},
      fleeing_goods_remark = #{record.fleeingGoodsRemark,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=NVARCHAR},
      creator = #{record.creator,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>