<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.WxTPartnerSaleConfigMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.WxTPartnerSaleConfig" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="distributor_id" property="distributorId" jdbcType="BIGINT" />
    <result column="planning_customer_name" property="planningCustomerName" jdbcType="VARCHAR" />
    <result column="sap_code" property="sapCode" jdbcType="NVARCHAR" />
    <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
    <result column="region_name" property="regionName" jdbcType="VARCHAR" />
    <result column="channel" property="channel" jdbcType="VARCHAR" />
    <result column="partner_function" property="partnerFunction" jdbcType="VARCHAR" />
    <result column="ship_to_code" property="shipToCode" jdbcType="VARCHAR" />
    <result column="partner_name_with_type" property="partnerNameWithType" jdbcType="VARCHAR" />
    <result column="payment_term" property="paymentTerm" jdbcType="VARCHAR" />
    <result column="sales_org" property="salesOrg" jdbcType="VARCHAR" />
    <result column="sales_cai" property="salesCai" jdbcType="VARCHAR" />
    <result column="price_level" property="priceLevel" jdbcType="VARCHAR" />
    <result column="purpose" property="purpose" jdbcType="VARCHAR" />
    <result column="date_from" property="dateFrom" jdbcType="TIMESTAMP" />
    <result column="date_to" property="dateTo" jdbcType="TIMESTAMP" />
    <result column="quota_type" property="quotaType" jdbcType="VARCHAR" />
    <result column="quota" property="quota" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="BIGINT" />
    <result column="quota_ratio" property="quotaRatio" jdbcType="BIGINT" />
    <result column="is_quota_ratio" property="isQuotaRatio" jdbcType="VARCHAR" />
    <result column="quota_ratio_sales_org" property="quotaRatioSalesOrg" jdbcType="NVARCHAR" />
    <result column="quota_ratio_sales_code" property="quotaRatioSalesCode" jdbcType="NVARCHAR" />
		<result column="dist_id" property="distId" jdbcType="BIGINT"/>
		<result column="address" property="address" jdbcType="VARCHAR"/>
		<result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
		<result column="contact_person_tel" property="contactPersonTel" jdbcType="VARCHAR"/>
		<result column="store_id" property="storeId" jdbcType="BIGINT"/>
 		<result column="store_name" property="storeName" jdbcType="VARCHAR"/>
    
    
    <result column="partner_id" property="partnerId" jdbcType="VARCHAR" />
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
		<result column="province_name" property="provinceName" jdbcType="NVARCHAR" />
		<result column="city_name" property="cityName" jdbcType="NVARCHAR" />
		<result column="province_id" property="provinceId" jdbcType="BIGINT" />
		<result column="city_id" property="cityId" jdbcType="BIGINT" />
	    <result column="dist_name" property="distName" jdbcType="NVARCHAR" />
	    <result column="payment_term_text" property="paymentTermText" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, distributor_id, planning_customer_name, sap_code, customer_name, region_name, 
    channel, partner_function, ship_to_code, partner_name_with_type, payment_term, sales_org, 
    price_level, purpose, date_from, date_to, quota_type, quota, creation_time, created_by, 
    update_time, updated_by, quota_ratio, is_quota_ratio, quota_ratio_sales_org, quota_ratio_sales_code,
    dist_id,address,contact_person,contact_person_tel,store_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.WxTPartnerSaleConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from [PP_MID].dbo.mid_partner_sale_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.WxTPartnerSaleConfigExample" >
    delete from [PP_MID].dbo.mid_partner_sale_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.WxTPartnerSaleConfig" useGeneratedKeys="true" keyProperty="id">
    insert into [PP_MID].dbo.mid_partner_sale_config (id, distributor_id, planning_customer_name, 
      sap_code, customer_name, region_name, 
      channel, partner_function, ship_to_code, 
      partner_name_with_type, payment_term, sales_org, 
      price_level, purpose, date_from, 
      date_to, quota_type, quota, 
      creation_time, created_by, update_time, 
      updated_by, quota_ratio, is_quota_ratio, 
      quota_ratio_sales_org, quota_ratio_sales_code
      )
    values (#{id,jdbcType=BIGINT}, #{distributorId,jdbcType=BIGINT}, #{planningCustomerName,jdbcType=VARCHAR}, 
      #{sapCode,jdbcType=NVARCHAR}, #{customerName,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, 
      #{channel,jdbcType=VARCHAR}, #{partnerFunction,jdbcType=VARCHAR}, #{shipToCode,jdbcType=VARCHAR}, 
      #{partnerNameWithType,jdbcType=VARCHAR}, #{paymentTerm,jdbcType=VARCHAR}, #{salesOrg,jdbcType=VARCHAR}, 
      #{priceLevel,jdbcType=VARCHAR}, #{purpose,jdbcType=VARCHAR}, #{dateFrom,jdbcType=TIMESTAMP}, 
      #{dateTo,jdbcType=TIMESTAMP}, #{quotaType,jdbcType=VARCHAR}, #{quota,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=BIGINT}, #{quotaRatio,jdbcType=BIGINT}, #{isQuotaRatio,jdbcType=NVARCHAR}, 
      #{quotaRatioSalesOrg,jdbcType=NVARCHAR}, #{quotaRatioSalesCode,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.WxTPartnerSaleConfig" useGeneratedKeys="true" keyProperty="id">
    insert into [PP_MID].dbo.mid_partner_sale_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="distributorId != null" >
        distributor_id,
      </if>
      <if test="planningCustomerName != null" >
        planning_customer_name,
      </if>
      <if test="sapCode != null" >
        sap_code,
      </if>
      <if test="customerName != null" >
        customer_name,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="channel != null" >
        channel,
      </if>
      <if test="partnerFunction != null" >
        partner_function,
      </if>
      <if test="shipToCode != null" >
        ship_to_code,
      </if>
      <if test="partnerNameWithType != null" >
        partner_name_with_type,
      </if>
      <if test="paymentTerm != null" >
        payment_term,
      </if>
      <if test="salesOrg != null" >
        sales_org,
      </if>
      <if test="priceLevel != null" >
        price_level,
      </if>
      <if test="purpose != null" >
        purpose,
      </if>
      <if test="dateFrom != null" >
        date_from,
      </if>
      <if test="dateTo != null" >
        date_to,
      </if>
      <if test="quotaType != null" >
        quota_type,
      </if>
      <if test="quota != null" >
        quota,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updatedBy != null" >
        updated_by,
      </if>
      <if test="quotaRatio != null" >
        quota_ratio,
      </if>
      <if test="isQuotaRatio != null" >
        is_quota_ratio,
      </if>
      <if test="quotaRatioSalesOrg != null" >
        quota_ratio_sales_org,
      </if>
      <if test="quotaRatioSalesCode != null" >
        quota_ratio_sales_code,
      </if>
			<if test="distId != null">
				dist_id,
			</if>
			<if test="address != null">
				address,
			</if>
			<if test="contactPerson != null">
				contact_person,
			</if>
			<if test="contactPersonTel != null">
				contact_person_tel,
			</if>
			<if test="storeId != null">
				store_id,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null" >
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="planningCustomerName != null" >
        #{planningCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="sapCode != null" >
        #{sapCode,jdbcType=NVARCHAR},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null" >
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="channel != null" >
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="partnerFunction != null" >
        #{partnerFunction,jdbcType=VARCHAR},
      </if>
      <if test="shipToCode != null" >
        #{shipToCode,jdbcType=VARCHAR},
      </if>
      <if test="partnerNameWithType != null" >
        #{partnerNameWithType,jdbcType=VARCHAR},
      </if>
      <if test="paymentTerm != null" >
        #{paymentTerm,jdbcType=VARCHAR},
      </if>
      <if test="salesOrg != null" >
        #{salesOrg,jdbcType=VARCHAR},
      </if>
      <if test="priceLevel != null" >
        #{priceLevel,jdbcType=VARCHAR},
      </if>
      <if test="purpose != null" >
        #{purpose,jdbcType=VARCHAR},
      </if>
      <if test="dateFrom != null" >
        #{dateFrom,jdbcType=TIMESTAMP},
      </if>
      <if test="dateTo != null" >
        #{dateTo,jdbcType=TIMESTAMP},
      </if>
      <if test="quotaType != null" >
        #{quotaType,jdbcType=VARCHAR},
      </if>
      <if test="quota != null" >
        #{quota,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="quotaRatio != null" >
        #{quotaRatio,jdbcType=BIGINT},
      </if>
      <if test="isQuotaRatio != null" >
        #{isQuotaRatio,jdbcType=NVARCHAR},
      </if>
      <if test="quotaRatioSalesOrg != null" >
        #{quotaRatioSalesOrg,jdbcType=NVARCHAR},
      </if>
      <if test="quotaRatioSalesCode != null" >
        #{quotaRatioSalesCode,jdbcType=NVARCHAR},
      </if>
			<if test="distId != null">
				#{distId,jdbcType=BIGINT},
			</if>
			<if test="address != null">
				#{address,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null">
				#{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="contactPersonTel != null">
				#{contactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="storeId != null">
				#{storeId,jdbcType=BIGINT},
			</if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update [PP_MID].dbo.mid_partner_sale_config
    <set >
      <if test="record.distributorId != null" >
        distributor_id = #{record.distributorId,jdbcType=BIGINT},
      </if>
      <if test="record.planningCustomerName != null" >
        planning_customer_name = #{record.planningCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="record.sapCode != null" >
        sap_code = #{record.sapCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.customerName != null" >
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null" >
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null" >
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerFunction != null" >
        partner_function = #{record.partnerFunction,jdbcType=VARCHAR},
      </if>
      <if test="record.shipToCode != null" >
        ship_to_code = #{record.shipToCode,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerNameWithType != null" >
        partner_name_with_type = #{record.partnerNameWithType,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentTerm != null" >
        payment_term = #{record.paymentTerm,jdbcType=VARCHAR},
      </if>
      <if test="record.salesOrg != null" >
        sales_org = #{record.salesOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.priceLevel != null" >
        price_level = #{record.priceLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.purpose != null" >
        purpose = #{record.purpose,jdbcType=VARCHAR},
      </if>
      <if test="record.dateFrom != null" >
        date_from = #{record.dateFrom,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dateTo != null" >
        date_to = #{record.dateTo,jdbcType=TIMESTAMP},
      </if>
      <if test="record.quotaType != null" >
        quota_type = #{record.quotaType,jdbcType=VARCHAR},
      </if>
      <if test="record.quota != null" >
        quota = #{record.quota,jdbcType=VARCHAR},
      </if>
      <if test="record.creationTime != null" >
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <choose>
      	<when test="record.quotaRatio == -1">
      	quota_ratio = null,
      	</when>
      	<when test="record.quotaRatio != null">
      	quota_ratio = #{record.quotaRatio,jdbcType=BIGINT},
      	</when>
      </choose>
      <if test="record.isQuotaRatio != null" >
        is_quota_ratio = #{record.isQuotaRatio,jdbcType=NVARCHAR},
      </if>
      <if test="record.quotaRatioSalesOrg != null" >
        quota_ratio_sales_org = #{record.quotaRatioSalesOrg,jdbcType=NVARCHAR},
      </if>
      <if test="record.quotaRatioSalesCode != null" >
        quota_ratio_sales_code = #{record.quotaRatioSalesCode,jdbcType=NVARCHAR},
      </if>
			<if test="record.distId != null">
				dist_id = #{record.distId,jdbcType=BIGINT},
			</if>
			<if test="record.address != null">
				address = #{record.address,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPerson != null">
				contact_person = #{record.contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPersonTel != null">
				contact_person_tel = #{record.contactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="record.storeId != null">
				store_id = #{record.storeId,jdbcType=BIGINT},
			</if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update [PP_MID].dbo.mid_partner_sale_config
    set 
	  distributor_id = #{record.distributorId,jdbcType=BIGINT},
      planning_customer_name = #{record.planningCustomerName,jdbcType=VARCHAR},
      sap_code = #{record.sapCode,jdbcType=NVARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      partner_function = #{record.partnerFunction,jdbcType=VARCHAR},
      ship_to_code = #{record.shipToCode,jdbcType=VARCHAR},
      partner_name_with_type = #{record.partnerNameWithType,jdbcType=VARCHAR},
      payment_term = #{record.paymentTerm,jdbcType=VARCHAR},
      sales_org = #{record.salesOrg,jdbcType=VARCHAR},
      price_level = #{record.priceLevel,jdbcType=VARCHAR},
      purpose = #{record.purpose,jdbcType=VARCHAR},
      date_from = #{record.dateFrom,jdbcType=TIMESTAMP},
      date_to = #{record.dateTo,jdbcType=TIMESTAMP},
      quota_type = #{record.quotaType,jdbcType=VARCHAR},
      quota = #{record.quota,jdbcType=VARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      quota_ratio = #{record.quotaRatio,jdbcType=BIGINT},
      is_quota_ratio = #{record.isQuotaRatio,jdbcType=NVARCHAR},
      quota_ratio_sales_org = #{record.quotaRatioSalesOrg,jdbcType=NVARCHAR},
      quota_ratio_sales_code = #{record.quotaRatioSalesCode,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="getPartnerSalesConfigByCondition" resultMap="BaseResultMap"
		parameterType="com.chevron.partnerorder.model.PartnerSaleConfigConditions">
	select *,(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partnersaleconfig.payment.term' and di1.dic_item_code=v.payment_term) payment_term_text from (
	SELECT psc.*,org.organization_name as partner_name,org.id as partner_id,(SELECT top 1 sales_cai from [PP_MID].dbo.syn_dw_to_pp_customer_org_sales where distributor_id = poe.distributor_id ) sales_cai ,
	r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, s1.name store_name
	 FROM [PP_MID].dbo.mid_partner_sale_config psc
	   LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id
    left join wx_t_organization org on org.id = poe.partner_id
	   left join wx_t_store2021 s1 on s1.id=psc.store_id
		    left join wx_t_region r1 on psc.dist_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
	   WHERE org.status=1 
	   <!-- 关键字搜索 -->
		<if test="queryField!=null and queryField != ''">
			and (
			org.organization_name like '%' + #{queryField} +'%'
			)		
		</if>
	   <if test="partnerId!=null">
			and org.id = #{partnerId}		
	   </if>
	   <if test="activeFlag == 1">
	   and (psc.date_from is null or psc.date_from&lt;=getdate()) and (psc.date_to is null or psc.date_to>getdate())
	   and len(psc.payment_term)>0 and len(psc.address)>0 and len(psc.sales_org)>0
	   </if>
	 ) v
  </select>
  <select id="getPartnerSaleConfigByMap" resultMap="BaseResultMap" parameterType="map">
       SELECT psc.*,(SELECT top 1 sales_cai from [PP_MID].dbo.syn_dw_to_pp_customer_org_sales where distributor_id = poe.distributor_id ) sales_cai
       FROM [PP_MID].dbo.mid_partner_sale_config psc
	   LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id  
	   WHERE 1 = 1 
	   <if test="partnerId != null">
	   		and poe.partner_id = #{partnerId} 
	   </if>
	   <if test="currentTime != null">
	   		AND <![CDATA[ psc.date_from <= #{currentTime} and psc.date_to >= #{currentTime}]]>
	   	</if>
	   	<if test="id != null">
	   		and psc.id=#{id}
	   	</if>
	   	<if test="activeFlag == 1">
	   		AND <![CDATA[ (psc.date_from is null or psc.date_from <= getdate()) and (psc.date_to is null or dateadd(day, 1, psc.date_to) > getdate())]]>
	   	</if>
   </select>
   <select id="getPartnerSalesConfigById" resultMap="BaseResultMap"
		parameterType="java.lang.Long">
	SELECT psc.*,org.organization_name as partner_name,org.id as partner_id,
	r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, s1.name store_name
	FROM [PP_MID].dbo.mid_partner_sale_config psc
	   LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = psc.distributor_id 
	   left join wx_t_organization org on org.id = poe.partner_id 
	   left join wx_t_store2021 s1 on s1.id=psc.store_id
		    left join wx_t_region r1 on psc.dist_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
	   WHERE 1 = 1 and psc.id = #{id}	
  </select>
</mapper>