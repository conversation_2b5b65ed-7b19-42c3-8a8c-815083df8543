<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.partnerorder.dao.WXTPartnerOrderAddressProductVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_order_id" property="partnerOrderId" jdbcType="BIGINT" />
    <result column="address_id" property="addressId" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="actual_amount" property="actualAmount" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="discount_fee" property="discountFee" jdbcType="NUMERIC" />
    <result column="discounted_price" property="discountedPrice" jdbcType="NUMERIC" />
    <result column="total_value" property="totalValue" jdbcType="NUMERIC" />
    <result column="discounted_total_value" property="discountedTotalValue" jdbcType="NUMERIC" />
    
    <result column="out_stock_no" property="outStockNo" jdbcType="NVARCHAR" />
    <result column="sap_sale_number" property="sapSaleNumber" jdbcType="NVARCHAR" />
    <result column="delivery_number" property="deliveryNumber" jdbcType="NVARCHAR" />    
    <result column="out_stock_from" property="outStockFrom" jdbcType="NVARCHAR" />
    <result column="out_stock_orgname" property="outStockOrgname" jdbcType="NVARCHAR" />
    <result column="in_stock_no" property="inStockNo" jdbcType="NVARCHAR" />
    <result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP" />
    <result column="estimated_arrival_time" property="estimatedArrivalTime" jdbcType="TIMESTAMP" />
    <result column="delivery_type" property="deliveryType" jdbcType="NVARCHAR" />   
    <result column="address" property="address" jdbcType="NVARCHAR" />
    <result column="receive_user_name" property="contactPerson" jdbcType="NVARCHAR" />
    <result column="receive_phone_no" property="telephone" jdbcType="NVARCHAR" />
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_order_id, address_id, product_id, sku, product_name, amount, units, price, 
    status, create_time, update_time, creator, remark, actual_amount, type, discount_fee, 
    discounted_price, total_value, discounted_total_value,out_stock_no,sap_sale_number,
    delivery_number,out_stock_from,out_stock_orgname,in_stock_no,delivery_time,estimated_arrival_time,delivery_type
    address,receive_user_name,receive_phone_no   
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_partner_order_address_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_partner_order_address_product
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductVoExample" >
    delete from wx_t_partner_order_address_product
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductVo" >
    insert into wx_t_partner_order_address_product (id, partner_order_id, address_id, 
      product_id, sku, product_name, 
      amount, units, price, 
      status, create_time, update_time, 
      creator, remark, actual_amount, 
      type, discount_fee, discounted_price, 
      total_value, discounted_total_value,out_stock_no,sap_sale_number,
      delivery_number,out_stock_from,out_stock_orgname,in_stock_no,
      delivery_time,estimated_arrival_time,delivery_type,
      address,receive_user_name,receive_phone_no      )
    values (#{id,jdbcType=BIGINT}, #{partnerOrderId,jdbcType=BIGINT}, #{addressId,jdbcType=BIGINT}, 
      #{productId,jdbcType=BIGINT}, #{sku,jdbcType=NVARCHAR}, #{productName,jdbcType=NVARCHAR}, 
      #{amount,jdbcType=INTEGER}, #{units,jdbcType=NVARCHAR}, #{price,jdbcType=NUMERIC}, 
      #{status,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR}, #{actualAmount,jdbcType=INTEGER}, 
      #{type,jdbcType=NVARCHAR}, #{discountFee,jdbcType=NUMERIC}, #{discountedPrice,jdbcType=NUMERIC}, 
      #{totalValue,jdbcType=NUMERIC}, #{discountedTotalValue,jdbcType=NUMERIC},
      #{outStockNo,jdbcType=NVARCHAR},
      #{sapSaleNumber,jdbcType=NVARCHAR},
      #{deliveryNumber,jdbcType=NVARCHAR},
      #{outStockFrom,jdbcType=NVARCHAR},
      #{outStockOrgname,jdbcType=NVARCHAR},
      #{inStockNo,jdbcType=NVARCHAR},
      #{deliveryTime,jdbcType=TIMESTAMP},
      #{estimatedArrivalTime,jdbcType=TIMESTAMP},
      #{deliveryType,jdbcType=NVARCHAR},
      #{address,jdbcType=NVARCHAR},
      #{contactPerson,jdbcType=NVARCHAR},
      #{telephone,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductVo" >
    insert into wx_t_partner_order_address_product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerOrderId != null" >
        partner_order_id,
      </if>
      <if test="addressId != null" >
        address_id,
      </if>
      <if test="productId != null" >
        product_id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="units != null" >
        units,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="actualAmount != null" >
        actual_amount,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="discountFee != null" >
        discount_fee,
      </if>
      <if test="discountedPrice != null" >
        discounted_price,
      </if>
      <if test="totalValue != null" >
        total_value,
      </if>
      <if test="discountedTotalValue != null" >
        discounted_total_value,
      </if>
      <if test="outStockNo != null" >
        out_stock_no,
      </if>
      <if test="sapSaleNumber != null" >
        sap_sale_number,
      </if>
      <if test=" deliveryNumber!= null" >
        delivery_number,
      </if>
      <if test=" outStockFrom!= null" >
        out_stock_from,
      </if>
      <if test="outStockOrgname != null" >
        out_stock_orgname,
      </if>
      <if test="inStockNo != null" >
        in_stock_no,
      </if>
      <if test="deliveryTime != null" >
        delivery_time,
      </if>
      <if test=" estimatedArrivalTime!= null" >
        estimated_arrival_time,
      </if>
      <if test="deliveryType != null" >
        delivery_type,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="contactPerson != null" >
        receive_user_name,
      </if>
      <if test="telephone != null" >
        receive_phone_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerOrderId != null" >
        #{partnerOrderId,jdbcType=BIGINT},
      </if>
      <if test="addressId != null" >
        #{addressId,jdbcType=BIGINT},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=NVARCHAR},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=NVARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="units != null" >
        #{units,jdbcType=NVARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="actualAmount != null" >
        #{actualAmount,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="discountFee != null" >
        #{discountFee,jdbcType=NUMERIC},
      </if>
      <if test="discountedPrice != null" >
        #{discountedPrice,jdbcType=NUMERIC},
      </if>
      <if test="totalValue != null" >
        #{totalValue,jdbcType=NUMERIC},
      </if>
      <if test="discountedTotalValue != null" >
        #{discountedTotalValue,jdbcType=NUMERIC},
      </if>
      <if test="outStockNo != null" >
        #{outStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="sapSaleNumber != null" >
        #{sapSaleNumber,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryNumber != null" >
        #{deliveryNumber,jdbcType=NVARCHAR},
      </if>
      <if test="outStockFrom != null" >
        #{outStockFrom,jdbcType=NVARCHAR},
      </if>
      <if test="outStockOrgname != null" >
        #{outStockOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="inStockNo != null" >
        #{inStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="deliveryTime != null" >
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedArrivalTime != null" >
        #{estimatedArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryType != null" >
        #{deliveryType,jdbcType=NVARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=NVARCHAR},
      </if>
      <if test="contactPerson != null" >
        #{contactPerson,jdbcType=NVARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_partner_order_address_product
    <set >
      <if test="record.partnerOrderId != null" >
        partner_order_id = #{record.partnerOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.addressId != null" >
        address_id = #{record.addressId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=NVARCHAR},
      </if>
      <if test="record.productName != null" >
        product_name = #{record.productName,jdbcType=NVARCHAR},
      </if>
      <if test="record.amount != null" >
        amount = #{record.amount,jdbcType=INTEGER},
      </if>
      <if test="record.units != null" >
        units = #{record.units,jdbcType=NVARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=NUMERIC},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
      <if test="record.actualAmount != null" >
        actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.discountFee != null" >
        discount_fee = #{record.discountFee,jdbcType=NUMERIC},
      </if>
      <if test="record.discountedPrice != null" >
        discounted_price = #{record.discountedPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.totalValue != null" >
        total_value = #{record.totalValue,jdbcType=NUMERIC},
      </if>
      <if test="record.discountedTotalValue != null" >
        discounted_total_value = #{record.discountedTotalValue,jdbcType=NUMERIC},
      </if>
      <if test="record.outStockNo != null" >
        out_stock_no = #{record.outStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.sapSaleNumber != null" >
        sap_sale_number = #{record.sapSaleNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.deliveryNumber != null" >
        delivery_number = #{record.deliveryNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.outStockFrom != null" >
        out_stock_from = #{record.outStockFrom,jdbcType=NVARCHAR},
      </if>
      <if test="record.outStockOrgname != null" >
        out_stock_orgname = #{record.outStockOrgname,jdbcType=NVARCHAR},
      </if>
      <if test="record.inStockNo != null" >
        in_stock_no = #{record.inStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.deliveryTime != null" >
        delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estimatedArrivalTime != null" >
        estimated_arrival_time = #{record.estimatedArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryType != null" >
        delivery_type = #{record.deliveryType,jdbcType=NVARCHAR},
      </if>
      <if test="record.address != null" >
        address = #{record.address,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactPerson != null" >
        receive_user_name = #{record.contactPerson,jdbcType=NVARCHAR},
      </if>
      <if test="record.telephone != null" >
        receive_phone_no = #{record.telephone,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_partner_order_address_product
    set
      partner_order_id = #{record.partnerOrderId,jdbcType=BIGINT},
      address_id = #{record.addressId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      sku = #{record.sku,jdbcType=NVARCHAR},
      product_name = #{record.productName,jdbcType=NVARCHAR},
      amount = #{record.amount,jdbcType=INTEGER},
      units = #{record.units,jdbcType=NVARCHAR},
      price = #{record.price,jdbcType=NUMERIC},
      status = #{record.status,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR},
      actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      type = #{record.type,jdbcType=NVARCHAR},
      discount_fee = #{record.discountFee,jdbcType=NUMERIC},
      discounted_price = #{record.discountedPrice,jdbcType=NUMERIC},
      total_value = #{record.totalValue,jdbcType=NUMERIC},
      discounted_total_value = #{record.discountedTotalValue,jdbcType=NUMERIC},
      out_stock_no = #{record.outStockNo,jdbcType=NVARCHAR},
      sap_sale_number = #{record.sapSaleNumber,jdbcType=NVARCHAR},
      delivery_number = #{record.deliveryNumber,jdbcType=NVARCHAR},
      out_stock_from = #{record.outStockFrom,jdbcType=NVARCHAR},
      out_stock_orgname = #{record.outStockOrgname,jdbcType=NVARCHAR},
      in_stock_no = #{record.inStockNo,jdbcType=NVARCHAR},
      delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      estimated_arrival_time = #{record.estimatedArrivalTime,jdbcType=TIMESTAMP},
      delivery_type = #{record.deliveryType,jdbcType=NVARCHAR},
      address = #{record.address,jdbcType=NVARCHAR},
      receive_user_name = #{record.contactPerson,jdbcType=NVARCHAR},
      receive_phone_no = #{record.telephone,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertOrderAddressProductBatch" parameterType="java.util.List">
     insert into wx_t_partner_order_address_product (partner_order_id, address_id,product_id, 
	      sku, product_name, amount, 
	      units, price, status, 
	      create_time, update_time, creator, 
	      remark,actual_amount,type,
	      discount_fee,discounted_price,
      	  total_value,discounted_total_value,out_stock_no,sap_sale_number,
      	  delivery_number,out_stock_from,out_stock_orgname,delivery_time,estimated_arrival_time,delivery_type,
      	  address,receive_user_name,receive_phone_no)
     	 values
     <foreach collection="list" index="index" item="item" separator=",">
		<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.partnerOrderId,jdbcType=BIGINT}, #{item.addressId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, 
     			#{item.sku,jdbcType=NVARCHAR}, #{item.productName,jdbcType=NVARCHAR}, #{item.amount,jdbcType=INTEGER}, 
    			#{item.units,jdbcType=NVARCHAR}, #{item.price,jdbcType=NUMERIC}, #{item.status,jdbcType=NVARCHAR}, 
     			getDate(), getDate(), #{item.creator,jdbcType=NVARCHAR}, 
     			#{item.remark,jdbcType=NVARCHAR},#{item.actualAmount,jdbcType=INTEGER},#{item.type,jdbcType=NVARCHAR},
     			#{item.discountFee,jdbcType=NUMERIC},
      			#{item.discountedPrice,jdbcType=NUMERIC},
      			#{item.totalValue,jdbcType=NUMERIC},
      			#{item.discountedTotalValue,jdbcType=NUMERIC},
      			#{item.outStockNo,jdbcType=NVARCHAR},
      			#{item.sapSaleNumber,jdbcType=NVARCHAR},
      			#{item.deliveryNumber,jdbcType=NVARCHAR},
      			#{item.outStockFrom,jdbcType=NVARCHAR},
      			#{item.outStockOrgname,jdbcType=NVARCHAR},
      			#{item.deliveryTime,jdbcType=TIMESTAMP},
      			#{item.estimatedArrivalTime,jdbcType=TIMESTAMP},
      			#{item.deliveryType,jdbcType=NVARCHAR},
      			#{item.address,jdbcType=NVARCHAR},
      			#{item.contactPerson,jdbcType=NVARCHAR},
      			#{item.telephone,jdbcType=NVARCHAR}     			
		</trim>
	 </foreach>
   </insert>
   <resultMap id="AddressProcutMap" type="com.chevron.partnerorder.model.WXTPartnerOrderAddressProductView" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_order_id" property="partnerOrderId" jdbcType="BIGINT" />
    <result column="address_id" property="addressId" jdbcType="BIGINT" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="units" property="units" jdbcType="NVARCHAR" />
    <result column="price" property="price" jdbcType="NUMERIC" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="actual_amount" property="actualAmount" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="discount_fee" property="discountFee" jdbcType="NUMERIC" />
    <result column="discounted_price" property="discountedPrice" jdbcType="NUMERIC" />
    <result column="total_value" property="totalValue" jdbcType="NUMERIC" />
    <result column="discounted_total_value" property="discountedTotalValue" jdbcType="NUMERIC" />
    <result column="out_stock_no" property="outStockNo" jdbcType="NVARCHAR" />
    <result column="sap_sale_number" property="sapSaleNumber" jdbcType="NVARCHAR" />
    <result column="delivery_number" property="deliveryNumber" jdbcType="NVARCHAR" />    
    <result column="out_stock_from" property="outStockFrom" jdbcType="NVARCHAR" />
    <result column="out_stock_orgname" property="outStockOrgname" jdbcType="NVARCHAR" />
    <result column="in_stock_no" property="inStockNo" jdbcType="NVARCHAR" />
    <result column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP" />
    <result column="estimated_arrival_time" property="estimatedArrivalTime" jdbcType="TIMESTAMP" />
    <result column="delivery_type" property="deliveryType" jdbcType="NVARCHAR" />
    <result column="province_name" property="provinceName" jdbcType="NVARCHAR" />
    <result column="city_name" property="cityName" jdbcType="NVARCHAR" />
    <result column="dist_name" property="distName" jdbcType="NVARCHAR" />
    <result column="address" property="address" jdbcType="NVARCHAR" />
    <result column="postcode" property="postcode" jdbcType="NVARCHAR" />
    <result column="contact_person" property="contactPerson" jdbcType="NVARCHAR" />
    <result column="telephone" property="telephone" jdbcType="NVARCHAR" />
    <result column="mobile" property="mobile" jdbcType="NVARCHAR" />
    <result column="product_photo" property="productPhoto" jdbcType="NVARCHAR" />
    <result column="workshop_address" property="workshopAddress" jdbcType="NVARCHAR" />
    <result column="receive_user_name" property="workshopContactPerson" jdbcType="NVARCHAR" />
    <result column="receive_phone_no" property="workshopTelephone" jdbcType="NVARCHAR" />
  </resultMap>
   <select id="selectByOrderId" parameterType="java.lang.String" resultMap="AddressProcutMap">
 		select
 			t.id,
 			t.partner_order_id,
 			t.address_id,
 			t.product_id,
 			t.sku,
 			t.product_name,
 			t.amount,
 			t.units,
 			t.price,
 			t.status,
 			t.create_time,
 			t.update_time,
 			t.creator,
 			t.remark,
 			t.actual_amount,
 			t.discount_fee,
 			t.discounted_price,
      		t.total_value,
      		t.discounted_total_value,
      		t.out_stock_no,
      		t.sap_sale_number,
      		t.delivery_number,
      		t.out_stock_from,
      		t.out_stock_orgname,
      		t.in_stock_no,
      		t.delivery_time,
      		t.estimated_arrival_time,
      		t.delivery_type,
      		t.address as workshop_address,
      		t.receive_user_name,
      		t.receive_phone_no,
      		product.viscosity,
      		product.capacity,
      		pro.region_name AS province_name, 
      		city.region_name AS city_name, 
      		dist.region_name AS dist_name, 
      		addr.address, 
      		addr.postcode, 
      		addr.contact_person, 
      		addr.telephone, 
      		addr.mobile,
 			(select dic_item_name from wx_t_dic_item where t.type=dic_item_code and dic_type_code = 'product.category') as type,
 			(select top 1 af1.att_id from wx_att_file af1 where af1.source_id=product.id and af1.source_type='5' order by af1.create_time asc) as product_photo
 		from 
 			wx_t_partner_order_address_product t
 		LEFT JOIN wx_t_product product ON t.sku = product.sku
 		LEFT JOIN wx_t_partner_address addr on t.address_id = addr.id
 		LEFT JOIN wx_t_region pro ON pro.region_code = addr.province_code
 		LEFT JOIN wx_t_region city ON city.region_code = addr.city_code
 		LEFT JOIN wx_t_region dist ON dist.region_code = addr.dist_code
 		where
 			t.partner_order_id=#{partnerOrderId,jdbcType=NVARCHAR}
 	</select>
 	
 	
 	 <select id="selectBatchBySalesNumber" parameterType="map" resultMap="BaseResultMap">
 	 select
 			t.id,
 			t.partner_order_id,
 			t.product_id,
 			t.sku,
 			t.product_name,
 			t.amount,
 			t.create_time,
      		t.sap_sale_number
 		from 
 			wx_t_partner_order_address_product t
 		where
 			<if test="sapOrderList!=null" >
		    	t.sap_sale_number in
		    	<foreach item="item" index="index" collection="sapOrderList" open="(" separator="," close=")">
		    		 #{item.salesDocument,jdbcType=NVARCHAR}
		    	</foreach>
		    	and
		   </if>
		   1=1
 	</select>
</mapper>