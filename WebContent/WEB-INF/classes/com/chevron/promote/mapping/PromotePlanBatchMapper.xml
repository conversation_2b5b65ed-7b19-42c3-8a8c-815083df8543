<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromotePlanBatchMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.PromotePlanBatch" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="batch_code" property="batchCode" jdbcType="VARCHAR" />
    <result column="batch_plan_title" property="batchPlanTitle" jdbcType="VARCHAR" />
    <result column="batch_status" property="batchStatus" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="plan_start_time" property="planStartTime" jdbcType="TIMESTAMP" />
    <result column="plan_end_time" property="planEndTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, batch_code, batch_plan_title, batch_status, create_time, release_time, plan_start_time, 
    plan_end_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promote.model.PromotePlanBatchExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_plan_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_plan_batch
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_plan_batch
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promote.model.PromotePlanBatchExample" >
    delete from wx_promote_plan_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promote.model.PromotePlanBatch" >
    insert into wx_promote_plan_batch (id, batch_code, batch_plan_title, 
      batch_status, create_time, release_time, 
      plan_start_time, plan_end_time, remark
      )
    values (#{id,jdbcType=BIGINT}, #{batchCode,jdbcType=VARCHAR}, #{batchPlanTitle,jdbcType=VARCHAR}, 
      #{batchStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{releaseTime,jdbcType=TIMESTAMP}, 
      #{planStartTime,jdbcType=TIMESTAMP}, #{planEndTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.promote.model.PromotePlanBatch" >
    insert into wx_promote_plan_batch
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="batchCode != null" >
        batch_code,
      </if>
      <if test="batchPlanTitle != null" >
        batch_plan_title,
      </if>
      <if test="batchStatus != null" >
        batch_status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
      <if test="planStartTime != null" >
        plan_start_time,
      </if>
      <if test="planEndTime != null" >
        plan_end_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchCode != null" >
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchPlanTitle != null" >
        #{batchPlanTitle,jdbcType=VARCHAR},
      </if>
      <if test="batchStatus != null" >
        #{batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planStartTime != null" >
        #{planStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planEndTime != null" >
        #{planEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_plan_batch
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchCode != null" >
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchPlanTitle != null" >
        batch_plan_title = #{record.batchPlanTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.batchStatus != null" >
        batch_status = #{record.batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.releaseTime != null" >
        release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planStartTime != null" >
        plan_start_time = #{record.planStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planEndTime != null" >
        plan_end_time = #{record.planEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_plan_batch
    set id = #{record.id,jdbcType=BIGINT},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      batch_plan_title = #{record.batchPlanTitle,jdbcType=VARCHAR},
      batch_status = #{record.batchStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      plan_start_time = #{record.planStartTime,jdbcType=TIMESTAMP},
      plan_end_time = #{record.planEndTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.PromotePlanBatch" >
    update wx_promote_plan_batch
    <set >
      <if test="batchCode != null" >
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchPlanTitle != null" >
        batch_plan_title = #{batchPlanTitle,jdbcType=VARCHAR},
      </if>
      <if test="batchStatus != null" >
        batch_status = #{batchStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planStartTime != null" >
        plan_start_time = #{planStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planEndTime != null" >
        plan_end_time = #{planEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promote.model.PromotePlanBatch" >
    update wx_promote_plan_batch
    set batch_code = #{batchCode,jdbcType=VARCHAR},
      batch_plan_title = #{batchPlanTitle,jdbcType=VARCHAR},
      batch_status = #{batchStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      release_time = #{releaseTime,jdbcType=TIMESTAMP},
      plan_start_time = #{planStartTime,jdbcType=TIMESTAMP},
      plan_end_time = #{planEndTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <select id="queryPromotePlanBatchList" parameterType="map" resultMap="BaseResultMap">
    select * from wx_promote_plan_batch t_p_batch
    where 
    t_p_batch.batch_status!='-1'
    <if test="batchId!=null">
    and t_p_batch.id = #{batchId}
    </if>
    <if test="nowdate!=null">
    <![CDATA[and  CONVERT(varchar(100), #{nowdate}, 23) >=  CONVERT(varchar(100), t_p_batch.plan_start_time, 23)]]> 
    <![CDATA[and  CONVERT(varchar(100), #{nowdate}, 23) < CONVERT(varchar(100), t_p_batch.plan_end_time, 23)]]>
    </if>
    order by t_p_batch.release_time desc
  </select>
</mapper>