<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromoteDistributionMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.PromoteDistribution" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="source_plan_id" property="sourcePlanId" jdbcType="BIGINT" />
    <result column="apply_batch_status" property="applyBatchStatus" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="parent_distribution_id" property="parentDistributionId" jdbcType="BIGINT" />
    <result column="distribution_user_id" property="distributionUserId" jdbcType="BIGINT" />
    <result column="distribution_user_name" property="distributionUserName" jdbcType="VARCHAR" />
    <result column="accept_user_id" property="acceptUserId" jdbcType="BIGINT" />
    <result column="accept_user_name" property="acceptUserName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="workshop_count" property="workshopCount" jdbcType="INTEGER" />
    <result column="last_annual_sales" property="lastAnnualSales" jdbcType="INTEGER" />
    <result column="target_annual_sales" property="targetAnnualSales" jdbcType="INTEGER" />
    <result column="open_shop_packs_total_count" property="openShopPacksTotalCount" jdbcType="INTEGER" />
    <result column="open_shop_packs_distribution_count" property="openShopPacksDistributionCount" jdbcType="INTEGER" />
    <result column="open_shop_gd_packs_total_count" property="openShopGdPacksTotalCount" jdbcType="INTEGER" />
    <result column="open_shop_gd_packs_distribution_count" property="openShopGdPacksDistributionCount" jdbcType="INTEGER" />
    <result column="seminar_packs_total_count" property="seminarPacksTotalCount" jdbcType="INTEGER" />
    <result column="seminar_packs_distribution_count" property="seminarPacksDistributionCount" jdbcType="INTEGER" />
    <result column="road_show_activi_packs_total_count" property="roadShowActiviPacksTotalCount" jdbcType="INTEGER" />
    <result column="road_show_activi_packs_distribution_count" property="roadShowActiviPacksDistributionCount" jdbcType="INTEGER" />
    <result column="road_show_consumer_packs_total_count" property="roadShowConsumerPacksTotalCount" jdbcType="INTEGER" />
    <result column="road_show_consumer_packs_distribution_count" property="roadShowConsumerPacksDistributionCount" jdbcType="INTEGER" />
    <result column="points_total_count" property="pointsTotalCount" jdbcType="INTEGER" />
    <result column="points_distribution_count" property="pointsDistributionCount" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="venue_meal_total" property="venueMealTotal" jdbcType="NUMERIC" />
    <result column="venue_meal_distribution" property="venueMealDistribution" jdbcType="NUMERIC" />
    <result column="seminar_high_packs_total_count" property="seminarHighPacksTotalCount" jdbcType="NUMERIC" />
    <result column="seminar_high_packs_distribution_count" property="seminarHighPacksDistributionCount" jdbcType="NUMERIC" />
    <result column="store_packs_total_count" property="storePacksTotalCount" jdbcType="NUMERIC" />
    <result column="store_packs_distribution_count" property="storePacksDistributionCount" jdbcType="NUMERIC" />
    <result column="agriculture_packs_total_count" property="agriculturePacksTotalCount" jdbcType="NUMERIC" />
	<result column="agriculture_packs_distribution_count" property="agriculturePacksDistributionCount" jdbcType="NUMERIC" />
    <result column="try_packs_total_count" property="tryPacksTotalCount" jdbcType="NUMERIC" />
    <result column="try_packs_distribution_count" property="tryPacksDistributionCount" jdbcType="NUMERIC" />
    <result column="advert_packs_total_count" property="advertPacksTotalCount" jdbcType="NUMERIC" />
    <result column="advert_packs_distribution_count" property="advertPacksDistributionCount" jdbcType="NUMERIC" />
    
    
    <result column="version_flag" property="versionFlag" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, apply_batch_id, source_plan_id, apply_batch_status, status, parent_distribution_id, 
    distribution_user_id, distribution_user_name, accept_user_id, accept_user_name, create_time, 
    release_time, last_annual_sales, target_annual_sales, open_shop_packs_total_count, 
    open_shop_packs_distribution_count, open_shop_gd_packs_total_count, open_shop_gd_packs_distribution_count, 
    seminar_packs_total_count, seminar_packs_distribution_count, road_show_activi_packs_total_count, 
    road_show_activi_packs_distribution_count, road_show_consumer_packs_total_count, 
    road_show_consumer_packs_distribution_count, points_total_count, points_distribution_count, 
    remark,venue_meal_total,venue_meal_distribution, seminar_high_packs_total_count, seminar_high_packs_distribution_count,
    store_packs_total_count, store_packs_distribution_count, agriculture_packs_total_count, agriculture_packs_distribution_count,
    try_packs_total_count, try_packs_distribution_count, advert_packs_total_count, advert_packs_distribution_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promote.model.PromoteDistributionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_distribution
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_distribution
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_distribution
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promote.model.PromoteDistributionExample" >
    delete from wx_promote_distribution
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promote.model.PromoteDistribution" >
    insert into wx_promote_distribution (id, apply_batch_id, source_plan_id, 
      apply_batch_status, status, parent_distribution_id, 
      distribution_user_id, distribution_user_name, 
      accept_user_id, accept_user_name, create_time, 
      release_time, last_annual_sales, target_annual_sales, 
      open_shop_packs_total_count, open_shop_packs_distribution_count, 
      open_shop_gd_packs_total_count, open_shop_gd_packs_distribution_count, 
      seminar_packs_total_count, seminar_packs_distribution_count, 
      road_show_activi_packs_total_count, road_show_activi_packs_distribution_count, 
      road_show_consumer_packs_total_count, road_show_consumer_packs_distribution_count, 
      points_total_count, points_distribution_count, 
      remark,venue_meal_total,venue_meal_distribution)
    values (#{id,jdbcType=BIGINT}, #{applyBatchId,jdbcType=BIGINT}, #{sourcePlanId,jdbcType=BIGINT}, 
      #{applyBatchStatus,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{parentDistributionId,jdbcType=BIGINT}, 
      #{distributionUserId,jdbcType=BIGINT}, #{distributionUserName,jdbcType=VARCHAR}, 
      #{acceptUserId,jdbcType=BIGINT}, #{acceptUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{releaseTime,jdbcType=TIMESTAMP}, #{lastAnnualSales,jdbcType=INTEGER}, #{targetAnnualSales,jdbcType=INTEGER}, 
      #{openShopPacksTotalCount,jdbcType=INTEGER}, #{openShopPacksDistributionCount,jdbcType=INTEGER}, 
      #{openShopGdPacksTotalCount,jdbcType=INTEGER}, #{openShopGdPacksDistributionCount,jdbcType=INTEGER}, 
      #{seminarPacksTotalCount,jdbcType=INTEGER}, #{seminarPacksDistributionCount,jdbcType=INTEGER}, 
      #{roadShowActiviPacksTotalCount,jdbcType=INTEGER}, #{roadShowActiviPacksDistributionCount,jdbcType=INTEGER}, 
      #{roadShowConsumerPacksTotalCount,jdbcType=INTEGER}, #{roadShowConsumerPacksDistributionCount,jdbcType=INTEGER}, 
      #{pointsTotalCount,jdbcType=INTEGER}, #{pointsDistributionCount,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR},#{venueMealTotal,jdbcType=NUMERIC},#{venueMealDistribution,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.promote.model.PromoteDistribution" >
    insert into wx_promote_distribution
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="applyBatchId != null" >
        apply_batch_id,
      </if>
      <if test="sourcePlanId != null" >
        source_plan_id,
      </if>
      <if test="applyBatchStatus != null" >
        apply_batch_status,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="parentDistributionId != null" >
        parent_distribution_id,
      </if>
      <if test="distributionUserId != null" >
        distribution_user_id,
      </if>
      <if test="distributionUserName != null" >
        distribution_user_name,
      </if>
      <if test="acceptUserId != null" >
        accept_user_id,
      </if>
      <if test="acceptUserName != null" >
        accept_user_name,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
      <if test="lastAnnualSales != null" >
        last_annual_sales,
      </if>
      <if test="targetAnnualSales != null" >
        target_annual_sales,
      </if>
      <if test="openShopPacksTotalCount != null" >
        open_shop_packs_total_count,
      </if>
      <if test="openShopPacksDistributionCount != null" >
        open_shop_packs_distribution_count,
      </if>
      <if test="openShopGdPacksTotalCount != null" >
        open_shop_gd_packs_total_count,
      </if>
      <if test="openShopGdPacksDistributionCount != null" >
        open_shop_gd_packs_distribution_count,
      </if>
      <if test="seminarPacksTotalCount != null" >
        seminar_packs_total_count,
      </if>
      <if test="seminarPacksDistributionCount != null" >
        seminar_packs_distribution_count,
      </if>
      <if test="roadShowActiviPacksTotalCount != null" >
        road_show_activi_packs_total_count,
      </if>
      <if test="roadShowActiviPacksDistributionCount != null" >
        road_show_activi_packs_distribution_count,
      </if>
      <if test="roadShowConsumerPacksTotalCount != null" >
        road_show_consumer_packs_total_count,
      </if>
      <if test="roadShowConsumerPacksDistributionCount != null" >
        road_show_consumer_packs_distribution_count,
      </if>
      <if test="pointsTotalCount != null" >
        points_total_count,
      </if>
      <if test="pointsDistributionCount != null" >
        points_distribution_count,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="venueMealTotal != null" >
        venue_meal_total,
      </if>
      <if test="venueMealDistribution != null" >
        venue_meal_distribution,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applyBatchId != null" >
        #{applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="sourcePlanId != null" >
        #{sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="applyBatchStatus != null" >
        #{applyBatchStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="parentDistributionId != null" >
        #{parentDistributionId,jdbcType=BIGINT},
      </if>
      <if test="distributionUserId != null" >
        #{distributionUserId,jdbcType=BIGINT},
      </if>
      <if test="distributionUserName != null" >
        #{distributionUserName,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserId != null" >
        #{acceptUserId,jdbcType=BIGINT},
      </if>
      <if test="acceptUserName != null" >
        #{acceptUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAnnualSales != null" >
        #{lastAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="targetAnnualSales != null" >
        #{targetAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksTotalCount != null" >
        #{openShopPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksDistributionCount != null" >
        #{openShopPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksTotalCount != null" >
        #{openShopGdPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksDistributionCount != null" >
        #{openShopGdPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksTotalCount != null" >
        #{seminarPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksDistributionCount != null" >
        #{seminarPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActiviPacksTotalCount != null" >
        #{roadShowActiviPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActiviPacksDistributionCount != null" >
        #{roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksTotalCount != null" >
        #{roadShowConsumerPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksDistributionCount != null" >
        #{roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="pointsTotalCount != null" >
        #{pointsTotalCount,jdbcType=INTEGER},
      </if>
      <if test="pointsDistributionCount != null" >
        #{pointsDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="venueMealTotal != null" >
        #{venueMealTotal,jdbcType=NUMERIC},
      </if>
      <if test="venueMealDistribution != null" >
        #{venueMealDistribution,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_distribution
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyBatchId != null" >
        apply_batch_id = #{record.applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="record.sourcePlanId != null" >
        source_plan_id = #{record.sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="record.applyBatchStatus != null" >
        apply_batch_status = #{record.applyBatchStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.parentDistributionId != null" >
        parent_distribution_id = #{record.parentDistributionId,jdbcType=BIGINT},
      </if>
      <if test="record.distributionUserId != null" >
        distribution_user_id = #{record.distributionUserId,jdbcType=BIGINT},
      </if>
      <if test="record.distributionUserName != null" >
        distribution_user_name = #{record.distributionUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.acceptUserId != null" >
        accept_user_id = #{record.acceptUserId,jdbcType=BIGINT},
      </if>
      <if test="record.acceptUserName != null" >
        accept_user_name = #{record.acceptUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.releaseTime != null" >
        release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAnnualSales != null" >
        last_annual_sales = #{record.lastAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="record.targetAnnualSales != null" >
        target_annual_sales = #{record.targetAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="record.openShopPacksTotalCount != null" >
        open_shop_packs_total_count = #{record.openShopPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.openShopPacksDistributionCount != null" >
        open_shop_packs_distribution_count = #{record.openShopPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.openShopGdPacksTotalCount != null" >
        open_shop_gd_packs_total_count = #{record.openShopGdPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.openShopGdPacksDistributionCount != null" >
        open_shop_gd_packs_distribution_count = #{record.openShopGdPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.seminarPacksTotalCount != null" >
        seminar_packs_total_count = #{record.seminarPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.seminarPacksDistributionCount != null" >
        seminar_packs_distribution_count = #{record.seminarPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowActiviPacksTotalCount != null" >
        road_show_activi_packs_total_count = #{record.roadShowActiviPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowActiviPacksDistributionCount != null" >
        road_show_activi_packs_distribution_count = #{record.roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowConsumerPacksTotalCount != null" >
        road_show_consumer_packs_total_count = #{record.roadShowConsumerPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.roadShowConsumerPacksDistributionCount != null" >
        road_show_consumer_packs_distribution_count = #{record.roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.pointsTotalCount != null" >
        points_total_count = #{record.pointsTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.pointsDistributionCount != null" >
        points_distribution_count = #{record.pointsDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.venueMealTotal != null" >
        venue_meal_total =   #{venueMealTotal,jdbcType=NUMERIC},
      </if>
      <if test="record.venueMealDistribution != null" >
        venue_meal_distribution =   #{venueMealDistribution,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_distribution
    set id = #{record.id,jdbcType=BIGINT},
      apply_batch_id = #{record.applyBatchId,jdbcType=BIGINT},
      source_plan_id = #{record.sourcePlanId,jdbcType=BIGINT},
      apply_batch_status = #{record.applyBatchStatus,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      parent_distribution_id = #{record.parentDistributionId,jdbcType=BIGINT},
      distribution_user_id = #{record.distributionUserId,jdbcType=BIGINT},
      distribution_user_name = #{record.distributionUserName,jdbcType=VARCHAR},
      accept_user_id = #{record.acceptUserId,jdbcType=BIGINT},
      accept_user_name = #{record.acceptUserName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      release_time = #{record.releaseTime,jdbcType=TIMESTAMP},
      last_annual_sales = #{record.lastAnnualSales,jdbcType=INTEGER},
      target_annual_sales = #{record.targetAnnualSales,jdbcType=INTEGER},
      open_shop_packs_total_count = #{record.openShopPacksTotalCount,jdbcType=INTEGER},
      open_shop_packs_distribution_count = #{record.openShopPacksDistributionCount,jdbcType=INTEGER},
      open_shop_gd_packs_total_count = #{record.openShopGdPacksTotalCount,jdbcType=INTEGER},
      open_shop_gd_packs_distribution_count = #{record.openShopGdPacksDistributionCount,jdbcType=INTEGER},
      seminar_packs_total_count = #{record.seminarPacksTotalCount,jdbcType=INTEGER},
      seminar_packs_distribution_count = #{record.seminarPacksDistributionCount,jdbcType=INTEGER},
      road_show_activi_packs_total_count = #{record.roadShowActiviPacksTotalCount,jdbcType=INTEGER},
      road_show_activi_packs_distribution_count = #{record.roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
      road_show_consumer_packs_total_count = #{record.roadShowConsumerPacksTotalCount,jdbcType=INTEGER},
      road_show_consumer_packs_distribution_count = #{record.roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
      points_total_count = #{record.pointsTotalCount,jdbcType=INTEGER},
      points_distribution_count = #{record.pointsDistributionCount,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      venue_meal_total =   #{venueMealTotal,jdbcType=NUMERIC},
      venue_meal_distribution =   #{venueMealDistribution,jdbcType=NUMERIC}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.PromoteDistribution" >
    update wx_promote_distribution
    <set >
      <if test="applyBatchId != null" >
        apply_batch_id = #{applyBatchId,jdbcType=BIGINT},
      </if>
      <if test="sourcePlanId != null" >
        source_plan_id = #{sourcePlanId,jdbcType=BIGINT},
      </if>
      <if test="applyBatchStatus != null" >
        apply_batch_status = #{applyBatchStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="parentDistributionId != null" >
        parent_distribution_id = #{parentDistributionId,jdbcType=BIGINT},
      </if>
      <if test="distributionUserId != null" >
        distribution_user_id = #{distributionUserId,jdbcType=BIGINT},
      </if>
      <if test="distributionUserName != null" >
        distribution_user_name = #{distributionUserName,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserId != null" >
        accept_user_id = #{acceptUserId,jdbcType=BIGINT},
      </if>
      <if test="acceptUserName != null" >
        accept_user_name = #{acceptUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAnnualSales != null" >
        last_annual_sales = #{lastAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="targetAnnualSales != null" >
        target_annual_sales = #{targetAnnualSales,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksTotalCount != null" >
        open_shop_packs_total_count = #{openShopPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="openShopPacksDistributionCount != null" >
        open_shop_packs_distribution_count = #{openShopPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksTotalCount != null" >
        open_shop_gd_packs_total_count = #{openShopGdPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="openShopGdPacksDistributionCount != null" >
        open_shop_gd_packs_distribution_count = #{openShopGdPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksTotalCount != null" >
        seminar_packs_total_count = #{seminarPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="seminarPacksDistributionCount != null" >
        seminar_packs_distribution_count = #{seminarPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActiviPacksTotalCount != null" >
        road_show_activi_packs_total_count = #{roadShowActiviPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowActiviPacksDistributionCount != null" >
        road_show_activi_packs_distribution_count = #{roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksTotalCount != null" >
        road_show_consumer_packs_total_count = #{roadShowConsumerPacksTotalCount,jdbcType=INTEGER},
      </if>
      <if test="roadShowConsumerPacksDistributionCount != null" >
        road_show_consumer_packs_distribution_count = #{roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="pointsTotalCount != null" >
        points_total_count = #{pointsTotalCount,jdbcType=INTEGER},
      </if>
      <if test="pointsDistributionCount != null" >
        points_distribution_count = #{pointsDistributionCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="venueMealTotal != null" >
        venue_meal_total =   #{venueMealTotal,jdbcType=NUMERIC},
      </if>
      <if test="venueMealDistribution != null" >
        venue_meal_distribution =   #{venueMealDistribution,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promote.model.PromoteDistribution" >
    update wx_promote_distribution
    set apply_batch_id = #{applyBatchId,jdbcType=BIGINT},
      source_plan_id = #{sourcePlanId,jdbcType=BIGINT},
      apply_batch_status = #{applyBatchStatus,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      parent_distribution_id = #{parentDistributionId,jdbcType=BIGINT},
      distribution_user_id = #{distributionUserId,jdbcType=BIGINT},
      distribution_user_name = #{distributionUserName,jdbcType=VARCHAR},
      accept_user_id = #{acceptUserId,jdbcType=BIGINT},
      accept_user_name = #{acceptUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      release_time = #{releaseTime,jdbcType=TIMESTAMP},
      last_annual_sales = #{lastAnnualSales,jdbcType=INTEGER},
      target_annual_sales = #{targetAnnualSales,jdbcType=INTEGER},
      open_shop_packs_total_count = #{openShopPacksTotalCount,jdbcType=INTEGER},
      open_shop_packs_distribution_count = #{openShopPacksDistributionCount,jdbcType=INTEGER},
      open_shop_gd_packs_total_count = #{openShopGdPacksTotalCount,jdbcType=INTEGER},
      open_shop_gd_packs_distribution_count = #{openShopGdPacksDistributionCount,jdbcType=INTEGER},
      seminar_packs_total_count = #{seminarPacksTotalCount,jdbcType=INTEGER},
      seminar_packs_distribution_count = #{seminarPacksDistributionCount,jdbcType=INTEGER},
      road_show_activi_packs_total_count = #{roadShowActiviPacksTotalCount,jdbcType=INTEGER},
      road_show_activi_packs_distribution_count = #{roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
      road_show_consumer_packs_total_count = #{roadShowConsumerPacksTotalCount,jdbcType=INTEGER},
      road_show_consumer_packs_distribution_count = #{roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
      points_total_count = #{pointsTotalCount,jdbcType=INTEGER},
      points_distribution_count = #{pointsDistributionCount,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      venue_meal_total =   #{venueMealTotal,jdbcType=NUMERIC},
      venue_meal_distribution =   #{venueMealDistribution,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <select id="countHasDistributPacksData"  parameterType="map" resultMap="BaseResultMap" >
    SELECT sum(open_shop_packs_distribution_count)open_shop_packs_distribution_count, 
	sum(open_shop_gd_packs_distribution_count)open_shop_gd_packs_distribution_count, 
	sum(seminar_packs_distribution_count)seminar_packs_distribution_count, 
	sum(road_show_activi_packs_distribution_count)road_show_activi_packs_distribution_count,
	sum(road_show_consumer_packs_distribution_count)road_show_consumer_packs_distribution_count, 
	sum(points_distribution_count) points_distribution_count,
	sum(venue_meal_distribution)venue_meal_distribution,
	sum(seminar_high_packs_distribution_count)seminar_high_packs_distribution_count,
	sum(store_packs_distribution_count)store_packs_distribution_count,
	sum(agriculture_packs_distribution_count)agriculture_packs_distribution_count,
	sum(try_packs_distribution_count)try_packs_distribution_count,
	sum(advert_packs_distribution_count)advert_packs_distribution_count
	FROM wx_promote_distribution tt_dis
    INNER JOIN wx_promote_application_batch tt_app_batch
    ON tt_dis.apply_batch_id = tt_app_batch.batchid
	WHERE distribution_user_id = #{distributId}
	AND source_plan_id = #{marketinPlanId}
	<if test="status!=null">
        and
         tt_app_batch.approve_status NOT IN 
        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
             #{statu}  
        </foreach> 
    </if>
  </select>
  
  <select id="countHasToatlPacksData"  parameterType="map" resultMap="BaseResultMap" >
    SELECT open_shop_packs_distribution_count, 
	open_shop_gd_packs_distribution_count, 
	seminar_packs_distribution_count, 
	road_show_activi_packs_distribution_count,
	road_show_consumer_packs_distribution_count, 
	points_distribution_count,
	venue_meal_distribution
	,seminar_high_packs_distribution_count
	,store_packs_distribution_count
	,agriculture_packs_distribution_count
	,try_packs_distribution_count
	,advert_packs_distribution_count
	FROM wx_promote_distribution tt_dis
    INNER JOIN wx_promote_application_batch tt_app_batch
    ON tt_dis.apply_batch_id = tt_app_batch.batchid
	WHERE accept_user_id = #{acceptUserId}
	AND source_plan_id = #{marketinPlanId}
	AND accept_user_id != distribution_user_id  <!-- 接收者不等于分配者的去掉 -->
	<if test="status!=null">
        and
         tt_app_batch.approve_status NOT IN 
        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
             #{statu}  
        </foreach> 
    </if>
	ORDER BY release_time DESC
  </select>
  
  
  <resultMap  id="DistributionPreDataResultMap" type="com.chevron.promote.model.PromoteDistributionPreData">
    <id column="id" property="distriButionId" jdbcType="BIGINT" />
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="accept_user_id" property="acceptUserId" jdbcType="BIGINT" />
    <result column="accept_user_name" property="acceptUserName" jdbcType="VARCHAR" />
    <result column="user_status" property="userStatus" jdbcType="INTEGER" />
    <result column="workshop_count" property="workshopCount" jdbcType="INTEGER" />
    <result column="last_annual_sales" property="lastAnnualSales" jdbcType="INTEGER" />
    <result column="target_annual_sales" property="targetAnnualSales" jdbcType="INTEGER" />
    <result column="open_shop_packs_distribution_count" property="openShopPacksDistributionCount" jdbcType="INTEGER" />
    <result column="open_shop_gd_packs_distribution_count" property="openShopGdPacksDistributionCount" jdbcType="INTEGER" />
    <result column="seminar_packs_distribution_count" property="seminarPacksDistributionCount" jdbcType="INTEGER" />
    <result column="road_show_activi_packs_distribution_count" property="roadShowActiviPacksDistributionCount" jdbcType="INTEGER" />
    <result column="road_show_consumer_packs_distribution_count" property="roadShowConsumerPacksDistributionCount" jdbcType="INTEGER" />
    <result column="points_distribution_count" property="pointsDistributionCount" jdbcType="INTEGER" />
    <result column="approve_status" property="approveStatus" jdbcType="VARCHAR" />
    <result column="venue_meal_distribution" property="venueMealDistribution" jdbcType="NUMERIC" />
    <result column="seminar_high_packs_distribution_count" property="seminarHighPacksDistributionCount" jdbcType="NUMERIC" />
    <result column="store_packs_distribution_count" property="storePacksDistributionCount" jdbcType="NUMERIC" />
    <result column="agriculture_packs_distribution_count" property="agriculturePacksDistributionCount" jdbcType="NUMERIC" />
    <result column="try_packs_distribution_count" property="tryPacksDistributionCount" jdbcType="NUMERIC" />
    <result column="advert_packs_distribution_count" property="advertPacksDistributionCount" jdbcType="NUMERIC" />
  </resultMap>
  
  <select id="getPromoteDistributionPreData1"  parameterType="map" resultMap="DistributionPreDataResultMap" >
    SELECT t_promote_distri.id ,t_promote_distri.apply_batch_id ,
	t_promote_distri.accept_user_id ,t_promote_distri.accept_user_name ,
	t_promote_distri.workshop_count , t_promote_distri.last_annual_sales,
	t_promote_distri.target_annual_sales, t_promote_distri.open_shop_packs_distribution_count,
	t_promote_distri.open_shop_gd_packs_distribution_count, t_promote_distri.seminar_packs_distribution_count,
	t_promote_distri.road_show_activi_packs_distribution_count, t_promote_distri.road_show_consumer_packs_distribution_count,
	t_promote_distri.points_distribution_count,t_promote_appl.approve_status,
	t_promote_distri.venue_meal_distribution,
	t_promote_distri.seminar_high_packs_distribution_count,
	t_promote_distri.store_packs_distribution_count,
	t_promote_distri.agriculture_packs_distribution_count,
	t_promote_distri.try_packs_distribution_count,
	t_promote_distri.advert_packs_distribution_count
	FROM wx_promote_distribution t_promote_distri
	LEFT JOIN wx_promote_application_batch t_promote_appl
    ON t_promote_distri.apply_batch_id = t_promote_appl.batchid
	WHERE 
	1=1
	<if test="applyBatchId!=null">
	and t_promote_distri.apply_batch_id = #{applyBatchId}
	</if>
	<if test="distributionUserId!=null">
	and t_promote_distri.distribution_user_id = #{distributionUserId}
	</if>
	<if test="sourcePlanId!=null">
    and t_promote_distri.source_plan_id = #{sourcePlanId}
    </if>
	
  </select>
  
  <select id="getParentDistributionId"  parameterType="map" resultType="long" >
     SELECT  t_promote_distri.id 
     from  wx_promote_distribution t_promote_distri
     where
     1 = 1 
     <if test="acceptId!=null">
     and t_promote_distri.accept_user_id = #{acceptId}
     </if> 
     <if test="sourcePlanId!=null">
     and t_promote_distri.source_plan_id = #{sourcePlanId}
     </if> 
  </select>
  
  
  <insert id="insertBatchPromoteDistribution" parameterType="java.util.List">
      insert into wx_promote_distribution (apply_batch_id, source_plan_id, 
      apply_batch_status, status, parent_distribution_id, 
      distribution_user_id, distribution_user_name, 
      accept_user_id, accept_user_name, create_time, 
      release_time, last_annual_sales, target_annual_sales, 
      open_shop_packs_total_count, open_shop_packs_distribution_count, 
      open_shop_gd_packs_total_count, open_shop_gd_packs_distribution_count, 
      seminar_packs_total_count, seminar_packs_distribution_count, 
      road_show_activi_packs_total_count, road_show_activi_packs_distribution_count, 
      road_show_consumer_packs_total_count, road_show_consumer_packs_distribution_count, 
      points_total_count, points_distribution_count, 
      remark,venue_meal_total,venue_meal_distribution,
      seminar_high_packs_total_count, seminar_high_packs_distribution_count,
      store_packs_total_count, store_packs_distribution_count,
      agriculture_packs_total_count, agriculture_packs_distribution_count,
      try_packs_total_count, try_packs_distribution_count,
      advert_packs_total_count, advert_packs_distribution_count)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
              #{item.applyBatchId,jdbcType=BIGINT}, #{item.sourcePlanId,jdbcType=BIGINT}, 
		      #{item.applyBatchStatus,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.parentDistributionId,jdbcType=BIGINT}, 
		      #{item.distributionUserId,jdbcType=BIGINT}, #{item.distributionUserName,jdbcType=VARCHAR}, 
		      #{item.acceptUserId,jdbcType=BIGINT}, #{item.acceptUserName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
		      #{item.releaseTime,jdbcType=TIMESTAMP}, #{item.lastAnnualSales,jdbcType=INTEGER}, #{item.targetAnnualSales,jdbcType=INTEGER}, 
		      #{item.openShopPacksTotalCount,jdbcType=INTEGER}, #{item.openShopPacksDistributionCount,jdbcType=INTEGER}, 
		      #{item.openShopGdPacksTotalCount,jdbcType=INTEGER}, #{item.openShopGdPacksDistributionCount,jdbcType=INTEGER}, 
		      #{item.seminarPacksTotalCount,jdbcType=INTEGER}, #{item.seminarPacksDistributionCount,jdbcType=INTEGER}, 
		      #{item.roadShowActiviPacksTotalCount,jdbcType=INTEGER}, #{item.roadShowActiviPacksDistributionCount,jdbcType=INTEGER}, 
		      #{item.roadShowConsumerPacksTotalCount,jdbcType=INTEGER}, #{item.roadShowConsumerPacksDistributionCount,jdbcType=INTEGER}, 
		      #{item.pointsTotalCount,jdbcType=INTEGER}, #{item.pointsDistributionCount,jdbcType=INTEGER}, 
		      #{item.remark,jdbcType=VARCHAR}, #{item.venueMealTotal,jdbcType=NUMERIC},#{item.venueMealDistribution,jdbcType=NUMERIC},
		      #{item.seminarHighPacksTotalCount,jdbcType=NUMERIC},#{item.seminarHighPacksDistributionCount,jdbcType=NUMERIC},
		      #{item.storePacksTotalCount,jdbcType=NUMERIC},#{item.storePacksDistributionCount,jdbcType=NUMERIC},
		      #{item.agriculturePacksTotalCount,jdbcType=NUMERIC},#{item.agriculturePacksDistributionCount,jdbcType=NUMERIC},
		      #{item.tryPacksTotalCount,jdbcType=NUMERIC},#{item.tryPacksDistributionCount,jdbcType=NUMERIC},
		      #{item.advertPacksTotalCount,jdbcType=NUMERIC},#{item.advertPacksDistributionCount,jdbcType=NUMERIC}
            </trim>
        </foreach>
   </insert>
   
   <update id="batchUpdatePromoteDistribution"  parameterType="java.util.List">
         <foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
               update wx_promote_distribution
		    <set >
		        release_time = getdate(),
		      <if test="item.applyBatchId != null" >
		        apply_batch_id = #{item.applyBatchId,jdbcType=BIGINT},
		      </if>
		      <if test="item.sourcePlanId != null" >
		        source_plan_id = #{item.sourcePlanId,jdbcType=BIGINT},
		      </if>
		      <if test="item.applyBatchStatus != null" >
		        apply_batch_status = #{item.applyBatchStatus,jdbcType=VARCHAR},
		      </if>
		      <if test="item.status != null" >
		        status = #{item.status,jdbcType=VARCHAR},
		      </if>
		      <if test="item.parentDistributionId != null" >
		        parent_distribution_id = #{item.parentDistributionId,jdbcType=BIGINT},
		      </if>
		      <if test="item.distributionUserId != null" >
		        distribution_user_id = #{item.distributionUserId,jdbcType=BIGINT},
		      </if>
		      <if test="item.distributionUserName != null" >
		        distribution_user_name = #{item.distributionUserName,jdbcType=VARCHAR},
		      </if>
		      <if test="item.acceptUserId != null" >
		        accept_user_id = #{item.acceptUserId,jdbcType=BIGINT},
		      </if>
		      <if test="item.acceptUserName != null" >
		        accept_user_name = #{item.acceptUserName,jdbcType=VARCHAR},
		      </if>
		      <if test="item.createTime != null" >
		        create_time = #{item.createTime,jdbcType=TIMESTAMP},
		      </if>
		      <if test="item.lastAnnualSales != null" >
		        last_annual_sales = #{item.lastAnnualSales,jdbcType=INTEGER},
		      </if>
		      <if test="item.targetAnnualSales != null" >
		        target_annual_sales = #{item.targetAnnualSales,jdbcType=INTEGER},
		      </if>
		      <if test="item.openShopPacksDistributionCount != null" >
		        open_shop_packs_distribution_count = #{item.openShopPacksDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.openShopGdPacksDistributionCount != null" >
		        open_shop_gd_packs_distribution_count = #{item.openShopGdPacksDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.seminarPacksDistributionCount != null" >
		        seminar_packs_distribution_count = #{item.seminarPacksDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.roadShowActiviPacksDistributionCount != null" >
		        road_show_activi_packs_distribution_count = #{item.roadShowActiviPacksDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.roadShowConsumerPacksDistributionCount != null" >
		        road_show_consumer_packs_distribution_count = #{item.roadShowConsumerPacksDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.pointsDistributionCount != null" >
		        points_distribution_count = #{item.pointsDistributionCount,jdbcType=INTEGER},
		      </if>
		      <if test="item.remark != null" >
		        remark = #{item.remark,jdbcType=VARCHAR},
		      </if>
		      <if test="item.venueMealTotal != null" >
		        venue_meal_total = #{item.venueMealTotal,jdbcType=NUMERIC},
		      </if>
		      <if test="item.venueMealDistribution != null" >
		        venue_meal_distribution = #{item.venueMealDistribution,jdbcType=NUMERIC},
		      </if>
		      
		      <if test="item.seminarHighPacksTotalCount != null" >
		        seminar_high_packs_total_count = #{item.seminarHighPacksTotalCount,jdbcType=NUMERIC},
		      </if>
		      <if test="item.seminarHighPacksDistributionCount != null" >
		        seminar_high_packs_distribution_count = #{item.seminarHighPacksDistributionCount,jdbcType=NUMERIC},
		      </if>
		      
		      <if test="item.storePacksTotalCount != null" >
		        store_packs_total_count = #{item.storePacksTotalCount,jdbcType=NUMERIC},
		      </if>
		      <if test="item.storePacksDistributionCount != null" >
		        store_packs_distribution_count = #{item.storePacksDistributionCount,jdbcType=NUMERIC},
		      </if>
		      
		      <if test="item.agriculturePacksTotalCount != null" >
		        agriculture_packs_total_count = #{item.agriculturePacksTotalCount,jdbcType=NUMERIC},
		      </if>
		      <if test="item.agriculturePacksDistributionCount != null" >
		        agriculture_packs_distribution_count = #{item.agriculturePacksDistributionCount,jdbcType=NUMERIC},
		      </if>
		      
		      <if test="item.tryPacksTotalCount != null" >
		        try_packs_total_count = #{item.tryPacksTotalCount,jdbcType=NUMERIC},
		      </if>
		      <if test="item.tryPacksDistributionCount != null" >
		        try_packs_distribution_count = #{item.tryPacksDistributionCount,jdbcType=NUMERIC},
		      </if>
		      
		      <if test="item.advertPacksTotalCount != null" >
		        advert_packs_total_count = #{item.advertPacksTotalCount,jdbcType=NUMERIC},
		      </if>
		      <if test="item.advertPacksDistributionCount != null" >
		        advert_packs_distribution_count = #{item.advertPacksDistributionCount,jdbcType=NUMERIC},
		      </if>
		    </set>
		    where id = #{item.id,jdbcType=BIGINT}
         </foreach>
    </update>
    
    
    <select id="getPromoteDistributionDetailLst" parameterType="map" resultMap="BaseResultMap">
	    SELECT DISTINCT p_distribution.accept_user_name,p_distribution.last_annual_sales,p_distribution.target_annual_sales
	    ,p_distribution.open_shop_packs_distribution_count,p_distribution.open_shop_gd_packs_distribution_count
	    ,p_distribution.road_show_activi_packs_distribution_count,p_distribution.road_show_consumer_packs_distribution_count
	    ,p_distribution.seminar_packs_distribution_count,p_distribution.points_distribution_count
	    ,p_distribution.venue_meal_distribution,p_distribution.seminar_high_packs_distribution_count
			,p_distribution.store_packs_distribution_count
			,p_distribution.agriculture_packs_distribution_count
			,p_distribution.try_packs_distribution_count
			,p_distribution.advert_packs_distribution_count
	    FROM wx_promote_distribution p_distribution
	    WHERE
	     1=1
	     <if test="applyBatchId!=null">
	     and p_distribution.apply_batch_id = #{applyBatchId}
	     </if>
	     <if test="parentDistributionId!=null">
         and p_distribution.id = #{parentDistributionId}
         </if>
    </select>
    
    <select id="getPromoteDistributionDetailLst2" parameterType="map" resultMap="BaseResultMap">
        SELECT DISTINCT p_distribution.id,p_distribution.apply_batch_id,t_apply.approve_status apply_batch_status
            ,t_apply.version_flag
            ,p_distribution.accept_user_name
	        ,p_distribution.last_annual_sales,p_distribution.target_annual_sales,p_distribution.create_time
	        ,p_distribution.open_shop_packs_distribution_count,p_distribution.open_shop_gd_packs_distribution_count
	        ,p_distribution.road_show_activi_packs_distribution_count,p_distribution.road_show_consumer_packs_distribution_count
	        ,p_distribution.seminar_packs_distribution_count,p_distribution.points_distribution_count
	        ,p_distribution.venue_meal_distribution
	        ,p_distribution.seminar_high_packs_distribution_count
			,p_distribution.store_packs_distribution_count
			,p_distribution.agriculture_packs_distribution_count
			,p_distribution.try_packs_distribution_count
			,p_distribution.advert_packs_distribution_count
	        FROM wx_promote_distribution p_distribution
	        INNER JOIN wx_promote_application_batch t_apply
	        ON t_apply.batchid = p_distribution.apply_batch_id
	        INNER JOIN wx_promote_plan t_plan
            ON t_plan.id = p_distribution.source_plan_id
        WHERE
         1=1
         <if test="currentSourcePlanId!=null">
         AND p_distribution.source_plan_id = #{currentSourcePlanId}
         </if>
         <if test="queryFiled!=null">
         AND p_distribution.accept_user_name LIKE '%'+#{queryFiled}+'%'
         </if>
         <if test="applyKey!=null">
          AND t_apply.prokey = #{applyKey}
         </if>
         
         <if test="queryMarketing!=null">
            <if test="status!=null">
                AND t_apply.approve_status = #{status}
            </if>
            <if test="parentDistributionId=null">
                AND p_distribution.parent_distribution_id IS NULL
            </if>
            <if test="applyBatchId!=null">
                AND p_distribution.apply_batch_id = #{applyBatchId}
            </if>
            <if test="regionName!=null">
                AND t_plan.region = #{regionName}
            </if>
         </if>
         
          <if test="queryChannelManager!=null">
            <if test="status!=null">
                AND t_apply.approve_status = #{status}
            </if>
            <if test="distributionId!=null">
                AND p_distribution.parent_distribution_id IS NULL
                AND p_distribution.distribution_user_id = #{distributionId}
            </if>
            <if test="applyBatchId!=null">
                AND p_distribution.apply_batch_id = #{applyBatchId}
            </if>
         </if>
        
        
         <if test="querySupervisor!=null">
            <if test="saveStatus!=null">
                AND (t_apply.approve_status = #{saveStatus} or t_apply.approve_status = #{unapprovedStatus})
            </if>
            <if test="status!=null">
                AND t_apply.approve_status = #{status} 
            </if>
            <if test="distributionId!=null">
                AND p_distribution.distribution_user_id = #{distributionId}
            </if>
             <if test="applyBatchId!=null">
                AND p_distribution.apply_batch_id = #{applyBatchId}
            </if>
         </if>
         
    </select>
    
    
     <select id="getPromoteDistributionDetailLstForMarketingNotOpen" parameterType="map" resultMap="BaseResultMap">
          SELECT DISTINCT p_distribution.id,p_distribution2.apply_batch_id,t_apply.version_flag
            ,t_apply.approve_status apply_batch_status, p_distribution.accept_user_name
            ,p_distribution.last_annual_sales,p_distribution.target_annual_sales,p_distribution.create_time
            ,p_distribution.open_shop_packs_distribution_count,p_distribution.open_shop_gd_packs_distribution_count
            ,p_distribution.road_show_activi_packs_distribution_count,p_distribution.road_show_consumer_packs_distribution_count
            ,p_distribution.seminar_packs_distribution_count,p_distribution.points_distribution_count
            ,p_distribution.venue_meal_distribution
            FROM wx_promote_distribution p_distribution
            LEFT JOIN wx_promote_distribution p_distribution2
            ON p_distribution2.parent_distribution_id = p_distribution.id
            left  JOIN wx_promote_application_batch t_apply
            ON t_apply.batchid = p_distribution2.apply_batch_id
        WHERE
         1=1
         AND p_distribution.parent_distribution_id IS NULL 
         <if test="status!=null">
                AND t_apply.approve_status = #{status}
         </if>
         <if test="applyKey!=null">
                AND t_apply.prokey = #{applyKey}
         </if>
        
     </select>
     
     <select id="getPromoteDistributionDetailLstForMarketingNotOpen2" parameterType="map" resultMap="BaseResultMap">
          SELECT DISTINCT p_distribution2.accept_user_name, p_distribution.id,p_distribution.apply_batch_id,t_apply.approve_status apply_batch_status
            ,t_apply.version_flag
            ,p_distribution.accept_user_name
            ,p_distribution.last_annual_sales,p_distribution.target_annual_sales,p_distribution.create_time
            ,p_distribution.open_shop_packs_distribution_count,p_distribution.open_shop_gd_packs_distribution_count
            ,p_distribution.road_show_activi_packs_distribution_count,p_distribution.road_show_consumer_packs_distribution_count
            ,p_distribution.seminar_packs_distribution_count,p_distribution.points_distribution_count
            ,p_distribution.venue_meal_distribution
            ,p_distribution.seminar_high_packs_distribution_count
			,p_distribution.store_packs_distribution_count
			,p_distribution.agriculture_packs_distribution_count
			,p_distribution.try_packs_distribution_count
			,p_distribution.advert_packs_distribution_count
            FROM wx_promote_distribution p_distribution
            INNER JOIN wx_promote_application_batch t_apply
            ON t_apply.batchid = p_distribution.apply_batch_id
            INNER JOIN wx_promote_distribution p_distribution2
            ON p_distribution.distribution_user_id = p_distribution2.accept_user_id 
            INNER JOIN wx_promote_plan t_plan
            ON t_plan.id = p_distribution2.source_plan_id
        WHERE
         1=1
         <if test="currentSourcePlanId!=null">
            AND p_distribution.source_plan_id =#{currentSourcePlanId}
         </if>
         <if test="queryFiled!=null">
            AND p_distribution.accept_user_name LIKE '%'+#{queryFiled}+'%'
         </if>
         <if test="status!=null">
            AND t_apply.approve_status = #{status}
         </if>
         <if test="applyKey!=null">
            AND t_apply.prokey = #{applyKey}
         </if>
         <if test="regionName!=null">
            AND t_plan.region = #{regionName}
         </if>
     </select>
     
    
    <select id="getPromoteDistributionPreData2"  parameterType="map" resultMap="DistributionPreDataResultMap" >
		select crss.suppervisor_cai user_cai, tt_user.user_id accept_user_id,tt_user.login_name,tt_user.ch_name accept_user_name,tt_user.status user_status, sum(btsi.liters) last_annual_sales 
		from dw_region_sales_channel_rel rsc 
		left join dw_customer_region_sales_supervisor_rel crss on rsc.region_name=crss.region_name
		left join dw_base_trans_sell_in btsi on btsi.sales_channel_name=rsc.sales_channel_name and btsi.customer_name_cn=crss.customer_name_cn
		<![CDATA[and btsi.trans_time>= #{queryStartDate}]]> 
        <![CDATA[and btsi.trans_time< #{queryEndDate}]]> 
		LEFT JOIN wx_t_user tt_user ON tt_user.cai = crss.suppervisor_cai <!-- AND tt_user.user_id IS NOT NULL AND tt_user.status=1 -->
		where rsc.channel_manager_cai=#{cai}
		<!-- and btsi.trans_time>='2017-01-01' and btsi.trans_time<'2018-01-01' -->
		group by crss.suppervisor_cai, tt_user.user_id,tt_user.login_name,tt_user.status,tt_user.ch_name
    </select>
    
    
    
     <select id="getPromoteDistributionPreData3"  parameterType="map" resultMap="DistributionPreDataResultMap" >
      SELECT t_cai_lastannual.*,t_cai_workshop_count.workshop_count FROM 
        (SELECT sales_cai, sum(workshop_count)  workshop_count
		    FROM
		    (select crss.customer_name_cn, crss.sales_cai,
		    (select count(1) from wx_t_work_shop w left join wx_t_workshop_partner wp on w.id=wp.workshop_id 
		    left join wx_t_organization o on o.id=wp.partner_id where o.organization_name=crss.customer_name_cn
		    and w.status='3') workshop_count
		    from dw_customer_region_sales_supervisor_rel crss
		    left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
		    WHERE
		    crss.sales_cai IN (SELECT DISTINCT  crss.sales_cai user_cai
		    from dw_customer_region_sales_supervisor_rel crss 
		    left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
		    LEFT JOIN wx_t_user tt_user ON tt_user.cai = crss.sales_cai
		     WHERE crss.suppervisor_cai=#{cai} <!-- AND tt_user.user_id IS NOT NULL AND tt_user.status=1 -->)
		    ) tmp
		    GROUP BY sales_cai
		)t_cai_workshop_count
		LEFT JOIN 
		(select crss.sales_cai user_cai,tt_user.user_id accept_user_id,tt_user.login_name,tt_user.ch_name accept_user_name,tt_user.status user_status, sum(btsi.liters) last_annual_sales  
		from dw_customer_region_sales_supervisor_rel crss 
		left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
		left join dw_base_trans_sell_in btsi on btsi.sales_channel_name=rsc.sales_channel_name and btsi.customer_name_cn=crss.customer_name_cn 
		<![CDATA[and btsi.trans_time>= #{queryStartDate}]]> 
        <![CDATA[and btsi.trans_time< #{queryEndDate}]]> 
		LEFT JOIN wx_t_user tt_user ON tt_user.cai = crss.sales_cai
		where crss.suppervisor_cai=#{cai}
		group by crss.sales_cai,tt_user.user_id,tt_user.login_name,tt_user.ch_name,tt_user.status) t_cai_lastannual
		ON t_cai_lastannual.user_cai = t_cai_workshop_count.sales_cai
    </select>
    
    
    <select id="getSourcePromoteDistributionBySales" parameterType="map" resultMap="BaseResultMap" >
		SELECT t_distribution.* FROM wx_promote_application_batch t_apply
		LEFT JOIN wx_promote_distribution t_distribution
		ON t_apply.batchid = t_distribution.apply_batch_id
		WHERE t_apply.approve_status = #{status}
		AND t_distribution.source_plan_id = #{sourcePlanId}
		AND t_distribution.accept_user_id = #{acceptUserId}
    </select>
    
    <select id="getCurrentUserPacksInfo" parameterType="map" resultMap="BaseResultMap">
		SELECT t_distri.* FROM wx_promote_distribution t_distri
		LEFT JOIN wx_promote_application_batch t_apply
		ON t_apply.batchid = t_distri.apply_batch_id
		WHERE t_distri.source_plan_id = #{sourcePlanId}
		AND t_distri.accept_user_id = #{userId}
		AND t_apply.approve_status = #{status}
		ORDER BY t_distri.release_time DESC
    
    </select>
    
    
    <select id="countPromoteDistributionInfoForMarketing" parameterType="map" resultType="java.lang.Integer">
        SELECT count(DISTINCT t_app.batchid) 
		FROM wx_promote_application_batch t_app
		INNER  JOIN wx_promote_distribution t_dis
		ON t_dis.apply_batch_id = t_app.batchid
		INNER JOIN wx_promote_plan t_plan
		ON t_plan.id = t_dis.source_plan_id
		INNER JOIN wx_promote_plan_batch t_plan_batch
		ON t_plan_batch.id = t_plan.plan_batch_id
		WHERE 
		t_plan_batch.batch_status = 1
		AND t_app.approve_status = #{status}
		AND t_app.prokey = #{proKey}
		AND t_app.current_step = #{currentStep}
    </select>
    
     <select id="countPromoteDistributionInfoForChannelManager" parameterType="map" resultType="java.lang.Integer">
        SELECT count(DISTINCT t_app.batchid) 
		FROM wx_promote_application_batch t_app
		INNER  JOIN wx_promote_distribution t_dis
		ON t_dis.apply_batch_id = t_app.batchid
		INNER JOIN wx_promote_plan t_plan
		ON t_plan.id = t_dis.source_plan_id
		INNER JOIN wx_promote_plan_batch t_plan_batch
		ON t_plan_batch.id = t_plan.plan_batch_id
		WHERE 
		t_plan_batch.batch_status = 1
		AND t_app.approve_status = #{status}
		AND t_app.prokey = #{proKey}
		AND t_dis.source_plan_id = #{sourcePlanId}
		AND t_dis.distribution_user_id = #{userId}
    </select>
    
    <select id="statisticsPacksUsed" parameterType="map" resultMap="BaseResultMap">
        SELECT sum(t_distribution.open_shop_packs_distribution_count)open_shop_packs_distribution_count,
		sum(t_distribution.open_shop_gd_packs_distribution_count)open_shop_gd_packs_distribution_count,
		sum(t_distribution.road_show_activi_packs_distribution_count)road_show_activi_packs_distribution_count,
		sum(t_distribution.road_show_consumer_packs_distribution_count)road_show_consumer_packs_distribution_count,
		sum(t_distribution.seminar_packs_distribution_count)seminar_packs_distribution_count,
		sum(t_distribution.points_distribution_count)points_distribution_count,
		sum(t_distribution.venue_meal_distribution)venue_meal_distribution,
		sum(t_distribution.seminar_high_packs_distribution_count)seminar_high_packs_distribution_count,
		sum(t_distribution.store_packs_distribution_count)store_packs_distribution_count,
		sum(t_distribution.agriculture_packs_distribution_count)agriculture_packs_distribution_count,
		sum(t_distribution.try_packs_distribution_count)try_packs_distribution_count,
		sum(t_distribution.advert_packs_distribution_count)advert_packs_distribution_count
		FROM wx_promote_distribution t_distribution
		LEFT JOIN wx_promote_application_batch t_aplly
		ON t_aplly.batchid = t_distribution.apply_batch_id
		WHERE 
		1=1
		<if test="channelManager!=null">
		AND t_aplly.approve_status = 1
		</if>
        <if test="supervisor!=null">
        AND t_aplly.approve_status NOT IN 
	        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
	             #{statu}  
	        </foreach> 
        </if>
        
		
		AND t_aplly.prokey = #{prokey}
		<if test="distributionUserId!=null">
		AND t_distribution.distribution_user_id = #{distributionUserId}
		</if>
		<if test="acceptUserId!=null">
        AND t_distribution.accept_user_id = #{acceptUserId}
        </if>
    </select>
    
</mapper>