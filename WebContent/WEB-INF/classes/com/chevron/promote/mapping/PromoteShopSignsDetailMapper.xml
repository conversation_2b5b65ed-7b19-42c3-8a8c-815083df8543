<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromoteShopSignsDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.detail.PromoteShopSignsDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="packs_number" property="packsNumber" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    id, activity_id, activity_type, packs_number, remark
  </sql>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_shop_signs_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_shop_signs_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insertSelective" parameterType="com.chevron.promote.model.detail.PromoteShopSignsDetail" >
    insert into wx_promote_shop_signs_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="activityId != null" >
        activity_id,
      </if>
      <if test="activityType != null" >
        activity_type,
      </if>
      <if test="packsNumber != null" >
        packs_number,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="packsNumber != null" >
        #{packsNumber,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.detail.PromoteShopSignsDetail" >
    update wx_promote_shop_signs_detail
    <set >
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityType != null" >
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="packsNumber != null" >
        packs_number = #{packsNumber,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="getHasApplyPacks" parameterType="map" resultMap="BaseResultMap">
     SELECT sum(t_activity_detail.packs_number)packs_number
     FROM wx_promote_activity t_activity
     INNER  JOIN wx_promote_shop_signs_detail t_activity_detail
     ON t_activity.id = t_activity_detail.activity_id
     INNER JOIN wx_promote_application_batch t_apply
     ON t_apply.batchid = t_activity.apply_batch_id
     WHERE t_activity.source_plan_id = #{sourcePlanId}
     <if test="userId!=null">
     AND t_activity.creator_id = #{userId}
     </if>
     <if test="status!=null">
        and
         t_apply.approve_status NOT IN 
        <foreach item="statu" index="index" collection="status" open="(" separator="," close=")">  
             #{statu}  
        </foreach> 
    </if>
  </select>
  
  <!-- 活动明细表 -->
  <resultMap id="ShopSignsActivityDetailMap" type="com.chevron.promote.model.response.ResponsePromoteShopSignsDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="packs_number" property="packsNumber" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <!-- 公共 -->
    <result column="apply_batch_id" property="applyBatchId" jdbcType="BIGINT" />
    <result column="source_plan_id" property="sourcePlanId" jdbcType="BIGINT" />
    <result column="source_distribution_id" property="sourceDistributionId" jdbcType="BIGINT" />
    <result column="activity_subject" property="activitySubject" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="activity_address" property="activityAddress" jdbcType="VARCHAR" />
    <result column="activity_organizers" property="activityOrganizers" jdbcType="VARCHAR" />
    <result column="activity_organizers_id" property="activityOrganizersId" jdbcType="BIGINT" />
    <result column="activity_amount" property="activityAmount" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="contact_address" property="contactAddress" jdbcType="VARCHAR" />
    <result column="contact_person" property="contactPerson" jdbcType="VARCHAR" />
    <result column="contact_tel" property="contactTel" jdbcType="VARCHAR" />
  </resultMap>
  <select id="getShopSignsActivityDetail" parameterType="long" resultMap="ShopSignsActivityDetailMap">
	SELECT d.*,
	t_promote_activity.apply_batch_id,t_promote_activity.source_plan_id,
	t_promote_activity.source_distribution_id,t_promote_activity.activity_subject,
	t_promote_activity.activity_start_time,t_promote_activity.activity_end_time,
	t_promote_activity.activity_address,
	t_promote_activity.activity_organizers,t_promote_activity.activity_organizers_id,
	t_promote_activity.activity_amount,t_promote_activity.create_time,t_promote_activity.release_time,
	t_promote_activity.contact_address,t_promote_activity.contact_person,t_promote_activity.contact_tel,
	t_apply.creator_id,tt_user.ch_name creator
	FROM wx_promote_activity t_promote_activity
	INNER JOIN wx_promote_shop_signs_detail d
	ON d.activity_id = t_promote_activity.id
	INNER JOIN wx_promote_application_batch t_apply
	ON t_apply.batchid = t_promote_activity.apply_batch_id
	INNER JOIN wx_t_user tt_user
	ON tt_user.user_id = t_apply.creator_id
	WHERE d.activity_id = #{activityId}
  </select>
</mapper>