<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromoteApplicationBatchMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.PromoteApplicationBatch" >
    <id column="batchid" property="batchid" jdbcType="BIGINT" />
    <result column="batch_title" property="batchTitle" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="approve_status" property="approveStatus" jdbcType="NVARCHAR" />
    <result column="prokey" property="prokey" jdbcType="NVARCHAR" />
    <result column="prodef_id" property="prodefId" jdbcType="NVARCHAR" />
    <result column="proinstance_id" property="proinstanceId" jdbcType="NVARCHAR" />
    <result column="version_flag" property="versionFlag" jdbcType="NVARCHAR" />
    <result column="current_step" property="currentStep" jdbcType="NVARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <!-- 计划有关 -->
    <result column="parent_distribution_id" property="parentDistributionId" jdbcType="BIGINT" />
    
    <!-- 活动有关 -->
    <result column="activity_id" property="activityId" jdbcType="VARCHAR" />
    <result column="activity_subject" property="activitySubject" jdbcType="VARCHAR" />
    <result column="activity_start_time" property="activityStartTime" jdbcType="TIMESTAMP" />
    <result column="activity_end_time" property="activityEndTime" jdbcType="TIMESTAMP" />
    <result column="activity_address" property="activityAddress" jdbcType="VARCHAR" />
    <result column="activity_type" property="activityType" jdbcType="VARCHAR" />
    <result column="activity_amount" property="activityAmount" jdbcType="NUMERIC" />
    <result column="activity_organizers" property="activityOrganizers" jdbcType="VARCHAR" />
    
    
    
    <result column="activity_type_name" property="activityTypeName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    batchid, batch_title, create_time, approve_status, prokey, prodef_id, proinstance_id, 
    version_flag,creator_id, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promote.model.PromoteApplicationBatchExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_application_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_application_batch
    where batchid = #{batchid,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_application_batch
    where batchid = #{batchid,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promote.model.PromoteApplicationBatchExample" >
    delete from wx_promote_application_batch
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promote.model.PromoteApplicationBatch" >
    insert into wx_promote_application_batch (batchid, batch_title, create_time, 
      approve_status, prokey, prodef_id, 
      proinstance_id, version_flag,creator_id,current_step, remark
      )
    values (#{batchid,jdbcType=BIGINT}, #{batchTitle,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{approveStatus,jdbcType=NVARCHAR}, #{prokey,jdbcType=NVARCHAR}, #{prodefId,jdbcType=NVARCHAR}, 
      #{proinstanceId,jdbcType=NVARCHAR}, #{versionFlag,jdbcType=NVARCHAR}, #{creatorId,jdbcType=BIGINT},#{currentStep,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="batchid" parameterType="com.chevron.promote.model.PromoteApplicationBatch" >
    insert into wx_promote_application_batch
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="batchid != null" >
        batchid,
      </if>
      <if test="batchTitle != null" >
        batch_title,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="approveStatus != null" >
        approve_status,
      </if>
      <if test="prokey != null" >
        prokey,
      </if>
      <if test="prodefId != null" >
        prodef_id,
      </if>
      <if test="proinstanceId != null" >
        proinstance_id,
      </if>
      <if test="versionFlag != null" >
        version_flag,
      </if>
      <if test="currentStep != null" >
        current_step,
      </if>
      <if test="creatorId != null" >
        creator_id,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="batchid != null" >
        #{batchid,jdbcType=BIGINT},
      </if>
      <if test="batchTitle != null" >
        #{batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveStatus != null" >
        #{approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="prokey != null" >
        #{prokey,jdbcType=NVARCHAR},
      </if>
      <if test="prodefId != null" >
        #{prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="proinstanceId != null" >
        #{proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="versionFlag != null" >
        #{versionFlag,jdbcType=NVARCHAR},
      </if>
      <if test="currentStep != null" >
        #{currentStep,jdbcType=NVARCHAR},
      </if>
      <if test="creatorId != null" >
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_application_batch
    <set >
      <if test="record.batchid != null" >
        batchid = #{record.batchid,jdbcType=BIGINT},
      </if>
      <if test="record.batchTitle != null" >
        batch_title = #{record.batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approveStatus != null" >
        approve_status = #{record.approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="record.prokey != null" >
        prokey = #{record.prokey,jdbcType=NVARCHAR},
      </if>
      <if test="record.prodefId != null" >
        prodef_id = #{record.prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="record.proinstanceId != null" >
        proinstance_id = #{record.proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="record.versionFlag != null" >
        version_flag = #{record.versionFlag,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_application_batch
    set batchid = #{record.batchid,jdbcType=BIGINT},
      batch_title = #{record.batchTitle,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      approve_status = #{record.approveStatus,jdbcType=NVARCHAR},
      prokey = #{record.prokey,jdbcType=NVARCHAR},
      prodef_id = #{record.prodefId,jdbcType=NVARCHAR},
      proinstance_id = #{record.proinstanceId,jdbcType=NVARCHAR},
      version_flag = #{record.versionFlag,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.PromoteApplicationBatch" >
    update wx_promote_application_batch
    <set >
      <if test="batchTitle != null" >
        batch_title = #{batchTitle,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus,jdbcType=NVARCHAR},
      </if>
      <if test="prokey != null" >
        prokey = #{prokey,jdbcType=NVARCHAR},
      </if>
      <if test="prodefId != null" >
        prodef_id = #{prodefId,jdbcType=NVARCHAR},
      </if>
      <if test="proinstanceId != null" >
        proinstance_id = #{proinstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="versionFlag != null" >
        version_flag = #{versionFlag,jdbcType=NVARCHAR},
      </if>
      <if test="currentStep != null" >
        current_step = #{currentStep,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where batchid = #{batchid,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promote.model.PromoteApplicationBatch" >
    update wx_promote_application_batch
    set batch_title = #{batchTitle,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      approve_status = #{approveStatus,jdbcType=NVARCHAR},
      prokey = #{prokey,jdbcType=NVARCHAR},
      prodef_id = #{prodefId,jdbcType=NVARCHAR},
      proinstance_id = #{proinstanceId,jdbcType=NVARCHAR},
      version_flag = #{versionFlag,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR}
    where batchid = #{batchid,jdbcType=BIGINT}
  </update>
  
  <select id="getPromoteApplayLstByMap"  parameterType="map" resultMap="BaseResultMap">
    SELECT DISTINCT  t_p_appl.*,t_p_distr.parent_distribution_id  FROM wx_promote_application_batch t_p_appl
    LEFT JOIN wx_promote_distribution t_p_distr
    ON t_p_appl.batchid = t_p_distr.apply_batch_id
    WHERE 
    1=1
    <if test="distruibutionUserId!=null">
    AND t_p_distr.distribution_user_id = #{distruibutionUserId}
    </if>
    <if test="status!=null">
    AND t_p_appl.approve_status = #{status}
    </if>
    <if test="queryFiled!=null">
    AND t_p_appl.batch_title LIKE  '%'+#{queryFiled}+'%'
    </if>
  </select>
  
   <select id="getPromoteApplayLstForSupervisorByMap"  parameterType="map" resultMap="BaseResultMap">
    SELECT DISTINCT  t_p_appl.* FROM wx_promote_application_batch t_p_appl
    LEFT JOIN wx_promote_distribution t_p_distr
    ON t_p_appl.batchid = t_p_distr.apply_batch_id
    WHERE 
    1=1
    <if test="distruibutionUserId!=null">
    AND t_p_distr.distribution_user_id = #{distruibutionUserId}
    </if>
    <if test="saveStatus!=null">
    AND (t_p_appl.approve_status = #{saveStatus} or t_p_appl.approve_status =#{unapprovedStatus})
    </if>
    <if test="approvingStatus!=null">
    AND t_p_appl.approve_status = #{approvingStatus} 
    </if>
     <if test="approvedStatus!=null">
    AND t_p_appl.approve_status = #{approvedStatus} 
    </if>
    <if test="queryFiled!=null">
    AND t_p_appl.batch_title LIKE  '%'+#{queryFiled}+'%'
    </if>
  </select>
  
  
  <select id="checkPromoteApplyHasApprovedByMap"  parameterType="map" resultMap="BaseResultMap">
    SELECT DISTINCT promote_appl_batch.* FROM wx_promote_application_batch promote_appl_batch
	WHERE promote_appl_batch.batchid = #{bachId}
	AND promote_appl_batch.prokey = #{processKey}
	<![CDATA[AND  promote_appl_batch.version_flag > #{versionFlag}]]> 
  </select>
  
  <select id="getPromoteActivityApplayLstByMap" parameterType="map" resultMap="BaseResultMap">
     select DISTINCT
     promote_appl_batch.batchid,promote_appl_batch.batch_title,
        promote_appl_batch.create_time,
        promote_appl_batch.approve_status,
        promote_appl_batch.prokey,
        promote_appl_batch.version_flag,
        promote_appl_batch.current_step,
        t_activity.id activity_id,
        t_activity.activity_subject,t_activity.activity_start_time,
        t_activity.activity_end_time,t_activity.activity_address,
        t_activity.activity_type,t_pack_config.packs_type activity_type_name,
        t_activity.activity_organizers,
        t_activity.remark,
        t_activity.activity_amount
		FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity
		ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_packs_config t_pack_config
        ON t_pack_config.packs_en_type = t_activity.activity_type
        INNER JOIN wx_promote_plan t_plan
        ON t_plan.id = t_activity.source_plan_id
     where 1=1
     <if test="creatorId!=null">
        and promote_appl_batch.creator_id = #{creatorId}
     </if>
     <if test="pendingStatus!=null">
        and (promote_appl_batch.approve_status = #{saveStatus} or promote_appl_batch.approve_status =#{unapprovedStatus})
     </if>
    <if test="approvingStatus!=null">
        AND promote_appl_batch.approve_status = #{approvingStatus} 
    </if>
    <if test="approvedStatus!=null">
        AND promote_appl_batch.approve_status = #{approvedStatus} 
    </if>
    <if test="queryFiled!=null">
        AND promote_appl_batch.batch_title LIKE  '%'+#{queryFiled}+'%'
    </if>
     <if test="currentStep!=null">
        AND promote_appl_batch.current_step = #{currentStep} 
    </if>
    <if test="applyKey!=null">
        AND promote_appl_batch.prokey = #{applyKey}
     </if>
     <if test="regionName!=null">
        AND t_plan.region = #{regionName}
     </if>
     <if test="sourcePlanId!=null">
        AND t_activity.source_plan_id = #{sourcePlanId}
     </if>
     <if test="orgName != null">
     	AND activity_organizers LIKE '%' + #{orgName} + '%'
     </if>
     order by promote_appl_batch.create_time desc
  </select>
  
  
  <select id="getPromoteActivityApplayLstForSupervisorByMap" parameterType="map" resultMap="BaseResultMap">
      SELECT DISTINCT t_apply_batch.batchid,t_apply_batch.batch_title,
        t_apply_batch.create_time,
        t_apply_batch.approve_status,
        t_apply_batch.prokey,
        t_apply_batch.version_flag,
        t_apply_batch.current_step,
        t_activity.id activity_id,
        t_activity.activity_subject,t_activity.activity_start_time,
        t_activity.activity_end_time,t_activity.activity_address,
        t_activity.activity_type,t_pack_config.packs_type activity_type_name,
        t_activity.activity_organizers,
        t_activity.remark,
        t_activity.activity_amount
      FROM dw_customer_region_sales_supervisor_rel dw_sales
      INNER  JOIN wx_t_user tt_user ON dw_sales.sales_cai = tt_user.cai
      INNER  JOIN wx_promote_application_batch t_apply_batch ON t_apply_batch.creator_id = tt_user.user_id
      INNER  JOIN wx_promote_activity t_activity ON t_apply_batch.batchid = t_activity.apply_batch_id
      INNER JOIN wx_promote_packs_config t_pack_config
      ON t_pack_config.packs_en_type = t_activity.activity_type
      WHERE 
      1=1
      <if test="currentUserCai!=null">
      AND dw_sales.suppervisor_cai = #{currentUserCai}
      </if>
      <if test="approvingStatus!=null">
      AND t_apply_batch.approve_status = #{approvingStatus}
      </if>
      <if test="queryFiled!=null">
       AND t_apply_batch.batch_title LIKE  '%'+#{queryFiled}+'%'
      </if>
     <if test="currentStep!=null">
         AND t_apply_batch.current_step = #{currentStep} 
    </if>
     <if test="applyKey!=null">
          AND t_apply_batch.prokey = #{applyKey}
       </if>
  </select>
  
  
  
   <select id="getActivityApplyListInapprovalByChannelManager" parameterType="map" resultMap="BaseResultMap">
       SELECT DISTINCT t_apply_batch.batchid,t_apply_batch.batch_title,
        t_apply_batch.create_time,
        t_apply_batch.approve_status,
        t_apply_batch.prokey,
        t_apply_batch.version_flag,
        t_apply_batch.current_step,
        t_activity.id activity_id,
        t_activity.activity_subject,t_activity.activity_start_time,
        t_activity.activity_end_time,t_activity.activity_address,
        t_activity.activity_type,t_pack_config.packs_type activity_type_name,
        t_activity.activity_organizers,
        t_activity.remark,
        t_activity.activity_amount
       FROM dw_customer_region_sales_supervisor_rel dw_sales
       INNER JOIN wx_t_user tt_user ON dw_sales.sales_cai = tt_user.cai
       INNER JOIN wx_promote_application_batch t_apply_batch ON t_apply_batch.creator_id = tt_user.user_id
       INNER JOIN dw_region_sales_channel_rel dw_region ON dw_region.region_name = dw_sales.region_name
       INNER JOIN  wx_t_business_region_config t_region_config ON t_region_config.region_name = dw_region.region_name AND t_region_config.business_name = 'REGION_PROMOTION'
       INNER JOIN wx_promote_activity t_activity ON t_apply_batch.batchid = t_activity.apply_batch_id
       INNER JOIN wx_promote_packs_config t_pack_config
       ON t_pack_config.packs_en_type = t_activity.activity_type
       WHERE 
       1=1
       <if test="currentUserCai!=null">
       AND dw_region.channel_manager_cai = #{currentUserCai}  
       </if>
       <if test="businessName!=null">
       AND t_region_config.business_name = #{businessName}
       </if>
       <if test="approvingStatus!=null">
       AND t_apply_batch.approve_status = #{approvingStatus}
       </if>
       <if test="queryFiled!=null">
       AND t_apply_batch.batch_title LIKE  '%'+#{queryFiled}+'%'
       </if>
       <if test="currentStep!=null">
         AND t_apply_batch.current_step = #{currentStep} 
       </if>
       <if test="applyKey!=null">
          AND t_apply_batch.prokey = #{applyKey}
       </if>
  </select>
  
</mapper>