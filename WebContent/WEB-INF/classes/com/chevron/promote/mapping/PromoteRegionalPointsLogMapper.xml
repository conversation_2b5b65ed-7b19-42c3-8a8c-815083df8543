<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.promote.dao.PromoteRegionalPointsLogMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.promote.model.PromoteRegionalPointsLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="activity_id" property="activityId" jdbcType="BIGINT" />
    <result column="activity_detail_id" property="activityDetailId" jdbcType="BIGINT" />
    <result column="business_id" property="businessId" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="points" property="points" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, activity_id, activity_detail_id, business_id, partner_id, points, create_time, 
    remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.promote.model.PromoteRegionalPointsLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_promote_regional_points_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_promote_regional_points_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_promote_regional_points_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.promote.model.PromoteRegionalPointsLogExample" >
    delete from wx_promote_regional_points_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.promote.model.PromoteRegionalPointsLog" >
    insert into wx_promote_regional_points_log (id, activity_id, activity_detail_id, 
      business_id, partner_id, points, 
      create_time, remark)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{activityDetailId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{points,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.promote.model.PromoteRegionalPointsLog" >
    insert into wx_promote_regional_points_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="activityId != null" >
        activity_id,
      </if>
      <if test="activityDetailId != null" >
        activity_detail_id,
      </if>
      <if test="businessId != null" >
        business_id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="points != null" >
        points,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null" >
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityDetailId != null" >
        #{activityDetailId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null" >
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="points != null" >
        #{points,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_promote_regional_points_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null" >
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.activityDetailId != null" >
        activity_detail_id = #{record.activityDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null" >
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.points != null" >
        points = #{record.points,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_promote_regional_points_log
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      activity_detail_id = #{record.activityDetailId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      points = #{record.points,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.promote.model.PromoteRegionalPointsLog" >
    update wx_promote_regional_points_log
    <set >
      <if test="activityId != null" >
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityDetailId != null" >
        activity_detail_id = #{activityDetailId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null" >
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="points != null" >
        points = #{points,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.promote.model.PromoteRegionalPointsLog" >
    update wx_promote_regional_points_log
    set activity_id = #{activityId,jdbcType=BIGINT},
      activity_detail_id = #{activityDetailId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      partner_id = #{partnerId,jdbcType=BIGINT},
      points = #{points,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>