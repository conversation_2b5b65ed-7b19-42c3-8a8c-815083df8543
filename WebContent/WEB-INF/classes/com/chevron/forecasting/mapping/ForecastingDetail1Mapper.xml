<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.forecasting.dao.ForecastingDetail1Mapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.forecasting.model.ForecastingDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="forecasting_id" property="forecastingId" jdbcType="BIGINT"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="customer_name_en" property="customerNameEn" jdbcType="VARCHAR"/>
		<result column="forecasting_value" property="forecastingValue" jdbcType="NUMERIC"/>
		<result column="fixed_value" property="fixedValue" jdbcType="NUMERIC"/>
		<result column="avg_12" property="avg12" jdbcType="NUMERIC"/>
		<result column="value_last_year" property="valueLastYear" jdbcType="NUMERIC"/>
		<result column="month_offset" property="monthOffset" jdbcType="INTEGER"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="product_name_en" property="productNameEn" jdbcType="VARCHAR"/>
		<result column="product_creation_date" property="productCreationDate" jdbcType="DATE"/>
		<result column="grade_abc" property="gradeAbc" jdbcType="VARCHAR"/>
		<result column="grade_abc_text" property="gradeAbcText" jdbcType="VARCHAR"/>
		<result column="forecast_leading_time" property="forecastLeadingTime" jdbcType="INTEGER"/>
		<result column="pack" property="pack" jdbcType="NUMERIC"/>
		<result column="actual_sellin" property="actualSellin" jdbcType="NUMERIC"/>
		<result column="def_forecasting_value" property="defForecastingValue" jdbcType="NUMERIC"/>
		<result column="sales" property="sales" jdbcType="BIGINT"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="approve_id" property="approveId" jdbcType="BIGINT"/>
		<result column="approver_id" property="approverId" jdbcType="BIGINT"/>
		<result column="approver_name" property="approverName" jdbcType="VARCHAR"/>
		<result column="forecast_month" property="forecastMonth" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="BIGINT"/>
		<result column="latest_forecast_value" property="latestForecastValue" jdbcType="NUMERIC"/>
	</resultMap>
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,forecasting_id,sku,customer_name_cn,customer_name_en,forecasting_value,fixed_value,avg_12,value_last_year,
		month_offset,forecast_month,status,def_forecasting_value,forecast_leading_time,latest_forecast_value
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.forecasting.model.ForecastingDetail">
		update wx_t_forecasting_detail1
		<set>
			<if test="forecastingId != null" >
				forecasting_id = #{forecastingId,jdbcType=BIGINT},
			</if>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="customerNameCn != null" >
				customer_name_cn = #{customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="customerNameEn != null" >
				customer_name_en = #{customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="forecastingValue != null" >
				forecasting_value = #{forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="fixedValue != null" >
				fixed_value = #{fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="avg12 != null" >
				avg_12 = #{avg12,jdbcType=NUMERIC},
			</if>
			<if test="valueLastYear != null" >
				value_last_year = #{valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="monthOffset != null" >
				month_offset = #{monthOffset,jdbcType=INTEGER},
			</if>
			<if test="forecastMonth != null" >
				forecast_month = #{forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=BIGINT},
			</if>
			<if test="defForecastingValue != null" >
				def_forecasting_value = #{defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="forecastLeadingTime != null" >
				forecast_leading_time = #{forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="latestForecastValue != null" >
				latest_forecast_value = #{latestForecastValue,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
    	delete from wx_t_forecasting_detail1
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_detail1
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.forecasting.model.ForecastingDetail">
		insert into wx_t_forecasting_detail1
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="forecastingId != null">
				forecasting_id,
			</if>
			<if test="sku != null">
				sku,
			</if>
			<if test="customerNameCn != null">
				customer_name_cn,
			</if>
			<if test="customerNameEn != null">
				customer_name_en,
			</if>
			<if test="forecastingValue != null">
				forecasting_value,
			</if>
			<if test="fixedValue != null">
				fixed_value,
			</if>
			<if test="avg12 != null">
				avg_12,
			</if>
			<if test="valueLastYear != null">
				value_last_year,
			</if>
			<if test="monthOffset != null">
				month_offset,
			</if>
			<if test="forecastMonth != null">
				forecast_month,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="defForecastingValue != null">
				def_forecasting_value,
			</if>
			<if test="forecastLeadingTime != null">
				forecast_leading_time,
			</if>
			<if test="latestForecastValue != null">
				latest_forecast_value,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="forecastingId != null">
				#{forecastingId,jdbcType=BIGINT},
			</if>
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="customerNameCn != null">
				#{customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="customerNameEn != null">
				#{customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="forecastingValue != null">
				#{forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="fixedValue != null">
				#{fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="avg12 != null">
				#{avg12,jdbcType=NUMERIC},
			</if>
			<if test="valueLastYear != null">
				#{valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="monthOffset != null">
				#{monthOffset,jdbcType=INTEGER},
			</if>
			<if test="forecastMonth != null">
				#{forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=BIGINT},
			</if>
			<if test="defForecastingValue != null">
				#{defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="forecastLeadingTime != null">
				#{forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="latestForecastValue != null">
				#{latestForecastValue,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_forecasting_detail1
		<set>
			<if test="record.forecastingId != null">
				forecasting_id = #{record.forecastingId,jdbcType=BIGINT},
			</if>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.customerNameCn != null">
				customer_name_cn = #{record.customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="record.customerNameEn != null">
				customer_name_en = #{record.customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="record.forecastingValue != null">
				forecasting_value = #{record.forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="record.fixedValue != null">
				fixed_value = #{record.fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="record.avg12 != null">
				avg_12 = #{record.avg12,jdbcType=NUMERIC},
			</if>
			<if test="record.valueLastYear != null">
				value_last_year = #{record.valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="record.monthOffset != null">
				month_offset = #{record.monthOffset,jdbcType=INTEGER},
			</if>
			<if test="record.forecastMonth != null">
				forecast_month = #{record.forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=BIGINT},
			</if>
			<if test="record.defForecastingValue != null">
				def_forecasting_value = #{record.defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="record.forecastLeadingTime != null">
				forecast_leading_time = #{record.forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="record.latestForecastValue != null">
				latest_forecast_value = #{record.latestForecastValue,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		delete from wx_t_forecasting_detail1
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		select count(1) from wx_t_forecasting_detail1
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_detail1
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.forecasting_id, t1.sku, t1.customer_name_cn, t1.customer_name_en, t1.forecasting_value,
			 t1.fixed_value, t1.avg_12, t1.value_last_year, t1.month_offset, t1.forecast_month, t1.status,
			 t1.def_forecasting_value, t1.forecast_leading_time, t1.latest_forecast_value
		  from wx_t_forecasting_detail1 t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_forecasting_detail1 (forecasting_id, sku, customer_name_cn, customer_name_en, forecasting_value, fixed_value, avg_12, value_last_year, month_offset, forecast_month, status, def_forecasting_value, forecast_leading_time, latest_forecast_value) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.forecastingId, jdbcType=BIGINT}, #{item.sku, jdbcType=VARCHAR}, #{item.customerNameCn, jdbcType=VARCHAR}, #{item.customerNameEn, jdbcType=VARCHAR}, #{item.forecastingValue, jdbcType=NUMERIC}, #{item.fixedValue, jdbcType=NUMERIC}, #{item.avg12, jdbcType=NUMERIC}, #{item.valueLastYear, jdbcType=NUMERIC}, #{item.monthOffset, jdbcType=INTEGER}, #{item.forecastMonth, jdbcType=VARCHAR}, #{item.status, jdbcType=BIGINT}, #{item.defForecastingValue, jdbcType=NUMERIC}, #{item.forecastLeadingTime, jdbcType=INTEGER}, #{item.latestForecastValue, jdbcType=NUMERIC}
			</trim>
		</foreach>
	</insert>
</mapper>
