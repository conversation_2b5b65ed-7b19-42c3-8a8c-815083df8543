<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.forecasting.dao.ForecastingApproveMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.forecasting.model.ForecastingApprove">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="worksheet_id" property="worksheetId" jdbcType="BIGINT"/>
		<result column="approver" property="approver" jdbcType="BIGINT"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="completed_sales_count" property="completedSalesCount" jdbcType="INTEGER"/>
		<result column="sales_count" property="salesCount" jdbcType="INTEGER"/>
		<result column="submit_time" property="submitTime" jdbcType="TIMESTAMP"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="worksheet_title" property="worksheetTitle" jdbcType="VARCHAR"/>
		<result column="forecasting_month" property="forecastingMonth" jdbcType="DATE"/>
		<result column="record_version" property="recordVersion" jdbcType="BIGINT"/>
		<result column="has_temp_flag" property="hasTempFlag" jdbcType="INTEGER"/>
		<result column="supervisor_name" property="supervisorName" jdbcType="VARCHAR"/>
		<result column="supervisor_mobile" property="supervisorMobile" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="temp_save_finished" property="tempSaveFinished" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,worksheet_id,approver,status,submit_time,create_user_id,create_time,update_user_id,update_time,record_version
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.forecasting.model.ForecastingApprove">
		update wx_t_forecasting_approve
		<set>
			<if test="worksheetId != null" >
				worksheet_id = #{worksheetId,jdbcType=BIGINT},
			</if>
			<if test="approver != null" >
				approver = #{approver,jdbcType=BIGINT},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="submitTime != null" >
				submit_time = #{submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="recordVersion != null" >
				record_version = #{recordVersion,jdbcType=BIGINT},
			</if>
			<if test="tempSaveFinished != null" >
				temp_save_finished = #{tempSaveFinished,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT} and record_version &lt;= #{recordVersion,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.forecasting.model.ForecastingApproveExample">
    	delete from wx_t_forecasting_approve
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingApproveExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_approve
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.forecasting.model.ForecastingApprove" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_forecasting_approve
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="worksheetId != null">
				worksheet_id,
			</if>
			<if test="approver != null">
				approver,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="submitTime != null">
				submit_time,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="recordVersion != null">
				record_version,
			</if>
			<if test="tempSaveFinished != null">
				temp_save_finished,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="worksheetId != null">
				#{worksheetId,jdbcType=BIGINT},
			</if>
			<if test="approver != null">
				#{approver,jdbcType=BIGINT},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="submitTime != null">
				#{submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="recordVersion != null">
				#{recordVersion,jdbcType=BIGINT},
			</if>
			<if test="tempSaveFinished != null">
				#{tempSaveFinished,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_forecasting_approve
		<set>
			<if test="record.worksheetId != null">
				worksheet_id = #{record.worksheetId,jdbcType=BIGINT},
			</if>
			<if test="record.approver != null">
				approver = #{record.approver,jdbcType=BIGINT},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.submitTime != null">
				submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.recordVersion != null">
				record_version = #{record.recordVersion,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.forecasting.model.ForecastingApproveExample">
		delete from wx_t_forecasting_approve
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.forecasting.model.ForecastingApproveExample">
		select count(1) from wx_t_forecasting_approve
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingApproveExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_approve
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.worksheet_id, t1.approver, t1.status, t1.submit_time, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time, t1.record_version
		  from wx_t_forecasting_approve t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingApproveParams">
		select t1.id, fw.id worksheet_id, t1.approver, t1.submit_time, t1.create_user_id, t1.create_time,
			case when t1.status is null then -5 when fw.status=10 then 10 else t1.status end status, t1.temp_save_finished,
			case when t1.status is null or t1.status&lt;5 then (select count(1) from wx_t_forecasting_sales fs
				left join wx_t_user u on u.user_id=fs.sales
				 where fs.worksheet_id=fw.id and fs.status=5
				 and exists (select 1 from dw_customer_region_sales_supervisor_rel crss 
				 where 
			<choose>
				<when test="supervisorCai != null">
	crss.suppervisor_cai=#{supervisorCai, jdbcType=VARCHAR}
				</when>
				<otherwise>
	crss.suppervisor_cai=(select distinct crss2.suppervisor_cai from dw_customer_region_sales_supervisor_rel crss2 left join wx_t_user u2 on crss2.sales_cai=u2.cai
				where u2.user_id=#{salesId})
				</otherwise>
			</choose>
				  and crss.sales_cai=u.cai))
			else null end completed_sales_count,
			case when t1.status is null or t1.status&lt;5 then (select count(1) from (select crss.sales_cai from dw_customer_region_sales_supervisor_rel crss 
			where 
			<choose>
				<when test="supervisorCai != null">
	crss.suppervisor_cai=#{supervisorCai, jdbcType=VARCHAR}
				</when>
				<otherwise>
	crss.suppervisor_cai=(select distinct crss2.suppervisor_cai from dw_customer_region_sales_supervisor_rel crss2 left join wx_t_user u2 on crss2.sales_cai=u2.cai
				where u2.user_id=#{salesId})
				</otherwise>
			</choose>
			 group by crss.sales_cai) a)
			else null end sales_count,
			 t1.update_user_id, t1.update_time, fw.worksheet_title, fw.forecasting_month,
			 case when t1.status&lt;5 and exists (select 1 from wx_t_forecasting_detail fd left join wx_t_forecasting_sales fs on fd.forecasting_id = fs.id where fd.status!=1 and fs.approve_id=t1.id) then 1 else 0 end has_temp_flag
		  from wx_t_forecasting_worksheet fw 
		  left join wx_t_forecasting_approve t1 on t1.worksheet_id=fw.id
		  <if test="supervisorId != null">
		   and t1.approver=#{supervisorId,jdbcType=BIGINT}
		  </if>
		 <if test="salesId != null">
		 	and exists (select 1 from dw_customer_region_sales_supervisor_rel crss2 left join wx_t_user u2 on crss2.sales_cai=u2.cai
				left join wx_t_user u3 on crss2.suppervisor_cai=u3.cai 
				where u2.user_id=#{salesId} and u3.user_id=t1.approver)
		 </if>
		 where fw.delete_flag=0 and (fw.status = 5 or t1.status >= 5)
		 <if test="forecastingMonth != null and forecastingMonth != ''">
		 	and CONVERT(varchar(7), fw.forecasting_month)=#{forecastingMonth}
		 </if>
		 <if test="worksheetId != null">
		 	and fw.id=#{worksheetId}
		 </if>
	</select>
	
	<select id="querySupservisorApproveInfo" resultMap="BaseResultMap" parameterType="map">
		SELECT u.ch_name supervisor_name, u.mobile_tel supervisor_mobile, case when fa.status = 5 then 5 else 0 end status, fa.submit_time, crss.region_name
		FROM (select distinct crss1.suppervisor_cai, crss1.region_name from dw_customer_region_sales_supervisor_rel crss1
			left join dw_region_sales_channel_rel rsc on rsc.region_name=crss1.region_name
			where rsc.sales_channel_name=(select fw.sales_channel from wx_t_forecasting_worksheet fw where fw.id=#{worksheetId})) crss 
			LEFT JOIN wx_t_user u ON u.cai = crss.suppervisor_cai
			left join wx_t_forecasting_approve fa on fa.worksheet_id = #{worksheetId} and fa.approver=u.user_id
		WHERE u.status=1 and not exists (select 1 from wx_t_business_region_config brc where brc.business_name='FORCAST_EXCLUDE_REGION' and crss.region_name=brc.region_name and brc.enable_flag=1)
	</select>
</mapper>
