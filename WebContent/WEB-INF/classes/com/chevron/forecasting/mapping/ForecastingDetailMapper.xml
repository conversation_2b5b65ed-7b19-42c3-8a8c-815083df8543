<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.forecasting.dao.ForecastingDetailMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.forecasting.model.ForecastingDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="forecasting_id" property="forecastingId" jdbcType="BIGINT"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="customer_name_en" property="customerNameEn" jdbcType="VARCHAR"/>
		<result column="forecasting_value" property="forecastingValue" jdbcType="NUMERIC"/>
		<result column="fixed_value" property="fixedValue" jdbcType="NUMERIC"/>
		<result column="avg_12" property="avg12" jdbcType="NUMERIC"/>
		<result column="value_last_year" property="valueLastYear" jdbcType="NUMERIC"/>
		<result column="month_offset" property="monthOffset" jdbcType="INTEGER"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="product_name_en" property="productNameEn" jdbcType="VARCHAR"/>
		<result column="product_creation_date" property="productCreationDate" jdbcType="DATE"/>
		<result column="grade_abc" property="gradeAbc" jdbcType="VARCHAR"/>
		<result column="grade_abc_text" property="gradeAbcText" jdbcType="VARCHAR"/>
		<result column="forecast_leading_time" property="forecastLeadingTime" jdbcType="INTEGER"/>
		<result column="pack" property="pack" jdbcType="NUMERIC"/>
		<result column="actual_sellin" property="actualSellin" jdbcType="NUMERIC"/>
		<result column="def_forecasting_value" property="defForecastingValue" jdbcType="NUMERIC"/>
		<result column="sales" property="sales" jdbcType="BIGINT"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="approve_id" property="approveId" jdbcType="BIGINT"/>
		<result column="approver_id" property="approverId" jdbcType="BIGINT"/>
		<result column="approver_name" property="approverName" jdbcType="VARCHAR"/>
		<result column="forecast_month" property="forecastMonth" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="BIGINT"/>
		<result column="latest_forecast_value" property="latestForecastValue" jdbcType="NUMERIC"/>
	</resultMap>
	<resultMap id="ExportResultMap" type="com.chevron.forecasting.model.ForecastingExportDetail" extends="BaseResultMap">
		<result column="customer_code" jdbcType="NVARCHAR" property="customerCode" />
		<result column="sales" jdbcType="NUMERIC" property="sales" />
		<result column="sap_code" jdbcType="NVARCHAR" property="sapCode" />
		<result column="sales_office" jdbcType="NVARCHAR" property="salesOffice"/>
		<result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
		<result column="sku" jdbcType="VARCHAR" property="sku"/>
		<result column="sku_desc" jdbcType="NVARCHAR" property="skuDesc"/>
		<result column="sales_name" jdbcType="NVARCHAR" property="salesName"/>
		<result column="customer_name_cn" jdbcType="VARCHAR" property="customerNameCn"/>
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="forecasting_value_n0" jdbcType="NUMERIC" property="forecastingValueN0"/>
		<result column="forecasting_value_n1"  jdbcType="NUMERIC" property="forecastingValueN1"/>
		<result column="forecasting_value_n2" jdbcType="NUMERIC" property="forecastingValueN2"/>
		<result column="forecasting_value_n3"  jdbcType="NUMERIC" property="forecastingValueN3"/>
		<result column="forecasting_value_n4"  jdbcType="NUMERIC" property="forecastingValueN4"/>
		<result column="forecasting_value_n5" jdbcType="NUMERIC" property="forecastingValueN5"/>
		<result column="forecasting_pre_value" jdbcType="NUMERIC" property="forecastingPreValueN0"/>
		<result column="forecasting_pre_value_n1" jdbcType="NUMERIC" property="forecastingPreValueN1"/>
		<result column="forecasting_pre_value_n2" jdbcType="NUMERIC" property="forecastingPreValueN2"/>
		<result column="forecasting_pre_value_n3" jdbcType="NUMERIC" property="forecastingPreValueN3"/>
		<result column="forecasting_pre_value_n4" jdbcType="NUMERIC" property="forecastingPreValueN4"/>
		<result column="forecasting_pre_value_n5" jdbcType="NUMERIC" property="forecastingPreValueN5"/>
		<result column="changes_in_l" jdbcType="NUMERIC" property="changesInL" />
		<result column="original_in_l" jdbcType="NUMERIC" property="originalInL" />
	</resultMap>
	<resultMap id="SalesHistoryMap" type="com.chevron.forecasting.model.SalesHistoryRecord">
		<result column="liters" jdbcType="NUMERIC" property="liters" />
		<result column="trans_month" jdbcType="NVARCHAR" property="transMonth" />
		<result column="sales_channel_name" jdbcType="NVARCHAR" property="salesChannelName" />
	</resultMap>
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,forecasting_id,sku,customer_name_cn,customer_name_en,forecasting_value,fixed_value,avg_12,value_last_year,
		month_offset,forecast_month,status,def_forecasting_value,forecast_leading_time,latest_forecast_value
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.forecasting.model.ForecastingDetail">
		update wx_t_forecasting_detail
		<set>
			<if test="forecastingId != null" >
				forecasting_id = #{forecastingId,jdbcType=BIGINT},
			</if>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="customerNameCn != null" >
				customer_name_cn = #{customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="customerNameEn != null" >
				customer_name_en = #{customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="forecastingValue != null" >
				forecasting_value = #{forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="fixedValue != null" >
				fixed_value = #{fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="avg12 != null" >
				avg_12 = #{avg12,jdbcType=NUMERIC},
			</if>
			<if test="valueLastYear != null" >
				value_last_year = #{valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="monthOffset != null" >
				month_offset = #{monthOffset,jdbcType=INTEGER},
			</if>
			<if test="forecastMonth != null" >
				forecast_month = #{forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=BIGINT},
			</if>
			<if test="defForecastingValue != null" >
				def_forecasting_value = #{defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="forecastLeadingTime != null" >
				forecast_leading_time = #{forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="latestForecastValue != null" >
				latest_forecast_value = #{latestForecastValue,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
    	delete from wx_t_forecasting_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.forecasting.model.ForecastingDetail">
		insert into wx_t_forecasting_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="forecastingId != null">
				forecasting_id,
			</if>
			<if test="sku != null">
				sku,
			</if>
			<if test="customerNameCn != null">
				customer_name_cn,
			</if>
			<if test="customerNameEn != null">
				customer_name_en,
			</if>
			<if test="forecastingValue != null">
				forecasting_value,
			</if>
			<if test="fixedValue != null">
				fixed_value,
			</if>
			<if test="avg12 != null">
				avg_12,
			</if>
			<if test="valueLastYear != null">
				value_last_year,
			</if>
			<if test="monthOffset != null">
				month_offset,
			</if>
			<if test="forecastMonth != null">
				forecast_month,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="defForecastingValue != null">
				def_forecasting_value,
			</if>
			<if test="forecastLeadingTime != null">
				forecast_leading_time,
			</if>
			<if test="latestForecastValue != null">
				latest_forecast_value,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="forecastingId != null">
				#{forecastingId,jdbcType=BIGINT},
			</if>
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="customerNameCn != null">
				#{customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="customerNameEn != null">
				#{customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="forecastingValue != null">
				#{forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="fixedValue != null">
				#{fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="avg12 != null">
				#{avg12,jdbcType=NUMERIC},
			</if>
			<if test="valueLastYear != null">
				#{valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="monthOffset != null">
				#{monthOffset,jdbcType=INTEGER},
			</if>
			<if test="forecastMonth != null">
				#{forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=BIGINT},
			</if>
			<if test="defForecastingValue != null">
				#{defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="forecastLeadingTime != null">
				#{forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="latestForecastValue != null">
				#{latestForecastValue,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_forecasting_detail
		<set>
			<if test="record.forecastingId != null">
				forecasting_id = #{record.forecastingId,jdbcType=BIGINT},
			</if>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.customerNameCn != null">
				customer_name_cn = #{record.customerNameCn,jdbcType=VARCHAR},
			</if>
			<if test="record.customerNameEn != null">
				customer_name_en = #{record.customerNameEn,jdbcType=VARCHAR},
			</if>
			<if test="record.forecastingValue != null">
				forecasting_value = #{record.forecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="record.fixedValue != null">
				fixed_value = #{record.fixedValue,jdbcType=NUMERIC},
			</if>
			<if test="record.avg12 != null">
				avg_12 = #{record.avg12,jdbcType=NUMERIC},
			</if>
			<if test="record.valueLastYear != null">
				value_last_year = #{record.valueLastYear,jdbcType=NUMERIC},
			</if>
			<if test="record.monthOffset != null">
				month_offset = #{record.monthOffset,jdbcType=INTEGER},
			</if>
			<if test="record.forecastMonth != null">
				forecast_month = #{record.forecastMonth,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=BIGINT},
			</if>
			<if test="record.defForecastingValue != null">
				def_forecasting_value = #{record.defForecastingValue,jdbcType=NUMERIC},
			</if>
			<if test="record.forecastLeadingTime != null">
				forecast_leading_time = #{record.forecastLeadingTime,jdbcType=INTEGER},
			</if>
			<if test="record.latestForecastValue != null">
				latest_forecast_value = #{record.latestForecastValue,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		delete from wx_t_forecasting_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		select count(1) from wx_t_forecasting_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.forecasting.model.ForecastingDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_forecasting_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.forecasting_id, t1.sku, t1.customer_name_cn, t1.customer_name_en, t1.forecasting_value,
			 t1.fixed_value, t1.avg_12, t1.value_last_year, t1.month_offset, t1.forecast_month, t1.status,
			 t1.def_forecasting_value, t1.forecast_leading_time, t1.latest_forecast_value
		  from wx_t_forecasting_detail t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_forecasting_detail (forecasting_id, sku, customer_name_cn, customer_name_en, forecasting_value, fixed_value, avg_12, value_last_year, month_offset, forecast_month, status, def_forecasting_value, forecast_leading_time, latest_forecast_value) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.forecastingId, jdbcType=BIGINT}, #{item.sku, jdbcType=VARCHAR}, #{item.customerNameCn, jdbcType=VARCHAR}, #{item.customerNameEn, jdbcType=VARCHAR}, #{item.forecastingValue, jdbcType=NUMERIC}, #{item.fixedValue, jdbcType=NUMERIC}, #{item.avg12, jdbcType=NUMERIC}, #{item.valueLastYear, jdbcType=NUMERIC}, #{item.monthOffset, jdbcType=INTEGER}, #{item.forecastMonth, jdbcType=VARCHAR}, #{item.status, jdbcType=BIGINT}, #{item.defForecastingValue, jdbcType=NUMERIC}, #{item.forecastLeadingTime, jdbcType=INTEGER}, #{item.latestForecastValue, jdbcType=NUMERIC}
			</trim>
		</foreach>
	</insert>
	
	<select id="queryForecastCustomer" resultMap="BaseResultMap" parameterType="map">
		select crss.customer_name_cn, crss.customer_name_en FROM dw_customer_region_sales_supervisor_rel crss where crss.sales_cai='${salesCai}'
	</select>
	
	<select id="queryForecastProduct" resultMap="BaseResultMap" parameterType="map">
		SELECT p1.sku, p1.name AS product_name, p1.pack, p1.grade_abc, p1.forecast_leading_time
									, di1.dic_item_name AS grade_abc_text, p1.creation_date as product_creation_date, 
									isnull(p1.name_en, p1.name) as product_name_en
						FROM wx_t_product p1
							LEFT JOIN wx_t_dic_item di1
								ON di1.dic_type_code = 'Product.gradeAbc'
									AND di1.dic_item_code = p1.grade_abc
		<choose>
			<when test="id == null">
						WHERE p1.status=1 and p1.forecast_leading_time>0 and p1.product_channel = (select fw1.sales_channel from wx_t_forecasting_worksheet fw1 where fw1.id=#{worksheetId})
			</when>
			<otherwise>
		where exists (select 1 from wx_t_forecasting_detail fd left join wx_t_forecasting_sales fs on fd.forecasting_id=fs.id where fs.id=#{id} and fd.sku=p1.sku)
			</otherwise>
		</choose>
	</select>

<!-- 新增 -->	
	<select id="queryForecast" resultMap="BaseResultMap" parameterType="map">
	<choose>
		<when test="id == null">
SELECT f4.customer_name_cn, f4.sku, f4.fixed_value
	, f4.forecasting_value
	, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset
	, f4.def_forecasting_value, f4.avg_12, f4.value_last_year, f4.actual_sellin, f4.latest_forecast_value
FROM (
		SELECT f5.avg_12, f5.fixed_value, f5.forecasting_value, f5.forecast_month, f5.forecasting_month
			, f5.sku, f5.customer_name_cn, f5.def_forecasting_value, f5.value_last_year, si.liters AS actual_sellin
			, f5.latest_forecast_value
		FROM (
			SELECT isnull(f2.avg_12, csfm.AVG_Last_12) AS avg_12
				, isnull(f2.customer_name_cn, csfm.Customer_Name_CN) AS customer_name_cn, f2.fixed_value
				, f2.forecasting_value, isnull(f2.sku, csfm.SKU) AS sku
				, isnull(f2.forecast_month, csfm.forcast_month) AS forecast_month, f2.forecasting_month
				, csfm.Forecast_Volume AS def_forecasting_value, isnull(f2.value_last_year, csfm.Same_Period_Last_Year) AS value_last_year
				, f2.latest_forecast_value
			FROM (
				SELECT f3.customer_name_cn, f3.fixed_value, f3.forecasting_value, f3.sku
						, f3.forecast_month, f3.forecasting_month, case when f3.forecast_month &lt; '${forecastMonth}' then f3.value_last_year else null end value_last_year,
						case when f3.forecast_month &lt; '${forecastMonth}' then f3.avg_12 else null end avg_12, f3.latest_forecast_value
				FROM (
				<!-- 销售预测数据未保存过：取当前销售关系经销商的12个月的预测历史数据。但近12月平均和去年同期值只取前6个月,后6个月从预测表中取 -->
					SELECT fd.customer_name_cn, fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.value_last_year, fd.avg_12, fd.latest_forecast_value,
						ROW_NUMBER() OVER (PARTITION BY fd.customer_name_cn, fd.sku, fd.forecast_month ORDER BY fw.forecasting_month DESC) AS rn
					FROM wx_t_forecasting_detail fd
					left join wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
					WHERE fd.forecast_month &gt;= '${startForecastMonth}'
						AND fd.forecast_month &lt;= '${endForecastMonth}'
						and fs.worksheet_id&lt;#{worksheetId} 
						and exists (select 1 from dw_customer_region_sales_supervisor_rel crss where crss.customer_name_cn=fd.customer_name_cn and crss.sales_cai='${salesCai}')
						AND fw.delete_flag=0 and fw.status&gt;5 and fa.status &gt;= 5 AND fs.status &gt;= 5
				) f3
				WHERE f3.rn = 1
			) f2
			<!-- 取预测后6个月BI提供销售预测默认数据 -->
				FULL JOIN dw_customer_sales_forecast_monthly csfm
				ON f2.sku = csfm.SKU
					AND csfm.forcast_month = f2.forecast_month
					AND csfm.Customer_Name_CN = f2.customer_name_cn
					and csfm.forcast_month >= '201811'
			WHERE csfm.Customer_Name_CN IS NULL
				OR (csfm.sales_channel_name = '${salesChannel}'
				AND csfm.forcast_month &gt;= '${forecastMonth}'
				and csfm.current_sales_cai='${salesCai}')
		) f5
		<!-- 关联实际销售数据 -->
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f5.customer_name_cn
				AND si.[trans_month] = f5.forecast_month
				AND si.product_sku = f5.sku
				AND si.sales_channel_name = '${salesChannel}'
	) f4
ORDER BY f4.customer_name_cn, f4.sku
		</when>
		<otherwise>
select f4.customer_name_cn, f4.sku
	, f4.forecast_leading_time, f4.fixed_value
	, f4.forecasting_value,f4.forecast_month,f4.latest_forecast_value
	, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset
	, f4.def_forecasting_value, f4.avg_12, f4.value_last_year, si.liters actual_sellin from (
				SELECT f3.*, ROW_NUMBER() OVER (PARTITION BY f3.customer_name_cn, f3.sku, f3.forecast_month ORDER BY f3.forecasting_month DESC) AS rn from (
				<!-- 销售预测已存在：后6个月预测数据取当前工单 -->
					SELECT fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.avg_12, fd.value_last_year, fd.def_forecasting_value,
						case when fw.status&gt;5 then fd.forecast_leading_time else null end forecast_leading_time, fd.latest_forecast_value
					FROM wx_t_forecasting_detail fd
					left join wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
					WHERE fs.worksheet_id=#{worksheetId} 
						and fs.sales=#{loginUser}
						<choose>
							<when test="detailStatus == 0">
							and fd.status!=1
							</when>
							<otherwise>
							and fd.status=1
							</otherwise>
						</choose>
					union all
					<!-- 前6个月预测取历史数据，根据历史数据的customer和当前工单的customer匹配，预防销售关系变动 -->
					SELECT fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.avg_12, fd.value_last_year, null, null, fd.latest_forecast_value
					FROM wx_t_forecasting_detail fd
					left join wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
					WHERE fd.forecast_month &gt;= '${startForecastMonth}'
						AND fd.forecast_month &lt; '${forecastMonth}'
						and fs.worksheet_id&lt;#{worksheetId} 
						and exists (select 1 from wx_t_forecasting_sales fs1 left join wx_t_forecasting_detail fd1 on fd1.forecasting_id=fs1.id
								where fs.worksheet_id=#{worksheetId} and fs.sales=#{loginUser} and fd1.customer_name_cn=fd.customer_name_cn)
						AND fw.delete_flag=0 and fw.status>5 and fa.status &gt;= 5 AND fs.status &gt;= 5
				) f3
	) f4 
			<!-- 关联预测数据实际销量 -->
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f4.customer_name_cn
				AND si.[trans_month] = f4.forecast_month
				AND si.product_sku = f4.sku
				AND si.sales_channel_name = '${salesChannel}'
	WHERE f4.rn = 1
	order by f4.customer_name_cn, f4.sku, f4.forecast_month desc
		</otherwise>
	</choose>
	</select>
	<select id="queryHistoryForecastProduct" resultMap="BaseResultMap" parameterType="map">
		SELECT p1.sku, p1.name AS product_name, p1.pack, p1.grade_abc, p1.forecast_leading_time
									, di1.dic_item_name AS grade_abc_text, p1.creation_date as product_creation_date, 
									isnull(p1.name_en, p1.name) as product_name_en
						FROM wx_t_product p1
							LEFT JOIN wx_t_dic_item di1
								ON di1.dic_type_code = 'Product.gradeAbc'
									AND di1.dic_item_code = p1.grade_abc
		where exists (select 1 from wx_t_forecasting_detail fd 
		left join wx_t_forecasting_sales fs on fd.forecasting_id=fs.id 
		left join wx_t_forecasting_approve fa ON fa.id = fs.approve_id where fs.worksheet_id=#{worksheetId} and fd.sku=p1.sku
			AND fs.status &gt;= 5
		)
	</select>
	<select id="queryForecastApprove" resultMap="BaseResultMap" parameterType="map">
 	<choose>
		<when test="id == null"><!-- 第一次进入销售预测审批页面，做当前月预测。可用BI销售预测数据 -->
		select f4.*, si.liters actual_sellin, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset from (
				SELECT *, ROW_NUMBER() OVER (PARTITION BY f3.sales, f3.sku, f3.forecast_month, f3.customer_name_cn ORDER BY f3.forecasting_month DESC) AS rn
				FROM (
					SELECT fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.def_forecasting_value, fs.sales, 
						u.ch_name sales_name, null forecast_leading_time, fd.customer_name_cn, fd.customer_name_en, fd.forecasting_id
						, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
					FROM wx_t_forecasting_sales fs
					left join wx_t_forecasting_detail fd ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						left join wx_t_user u on u.user_id=fs.sales
					WHERE fs.worksheet_id=#{worksheetId} 
						AND exists (select 1 from dw_customer_region_sales_supervisor_rel crss where crss.sales_cai=u.cai and crss.suppervisor_cai='${salesCai}')
						AND fs.status &gt;= 5
					<choose>
						<when test="detailStatus == 0">and fd.status!=1</when>
						<otherwise>and fd.status=1</otherwise>
					</choose>
					union all
					SELECT fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.def_forecasting_value, cr.sales, null, null, 
						fd.customer_name_cn, fd.customer_name_en, fd.forecasting_id, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
					FROM wx_t_forecasting_detail fd
					left join wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
						left join (SELECT distinct crss.customer_name_cn, u.user_id sales
							FROM dw_customer_region_sales_supervisor_rel crss
							left join wx_t_user u on u.cai=crss.sales_cai 
							where crss.suppervisor_cai='${salesCai}') cr on cr.customer_name_cn=fd.customer_name_cn
					WHERE fd.forecast_month &gt;= '${startForecastMonth}'
						AND fd.forecast_month &lt; '${forecastMonth}'
						and fs.worksheet_id&lt;#{worksheetId} 
						and fw.sales_channel='${salesChannel}'
						and fd.status=1
						AND fw.delete_flag=0 and fw.status &gt; 5 and fa.status &gt;= 5 AND fs.status &gt;= 5
				) f3
			) f4
			<!-- 关联预测数据实际销量 -->
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f4.customer_name_cn
				AND si.[trans_month] = f4.forecast_month
				AND si.product_sku = f4.sku
				AND si.sales_channel_name = '${salesChannel}'
			 WHERE f4.rn = 1
			order by f4.sales, f4.customer_name_cn, f4.sku, f4.forecast_month desc
		</when>
		<otherwise><!-- 已保存的销售预测审批，包含历史数据，只能用PP保存的预测数据 -->
SELECT f4.*, si.liters actual_sellin
	, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset
FROM (
	SELECT *, ROW_NUMBER() OVER (PARTITION BY f3.customer_name_cn, f3.sales, f3.sku, f3.forecast_month ORDER BY f3.forecasting_month DESC) AS rn
	FROM (
		SELECT fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, fs.sales, u.ch_name AS sales_name, 
			case when fw.status &gt;5 then fd.forecast_leading_time else null end forecast_leading_time, 
			fd.customer_name_cn, fd.customer_name_en, fd.forecasting_id, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
		FROM wx_t_forecasting_sales fs
			LEFT JOIN wx_t_forecasting_detail fd ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_user u ON u.user_id = fs.sales
		WHERE fs.worksheet_id = #{worksheetId}
			AND fs.approve_id=#{id}
			AND fs.status &gt;= 5
			<choose>
				<when test="detailStatus == 0">and fd.status!=1</when>
				<otherwise>and fd.status=1</otherwise>
			</choose>
		UNION ALL
		SELECT fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, cr.sales, NULL, null, fd.customer_name_cn, 
			fd.customer_name_en, fd.forecasting_id, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
		FROM wx_t_forecasting_detail fd
			LEFT JOIN wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
			left join (SELECT distinct fd1.customer_name_cn, fs1.sales
				FROM wx_t_forecasting_detail fd1 
				left join wx_t_forecasting_sales fs1 on fd1.forecasting_id=fs1.id
				where fs1.approve_id=#{id}
				and fd1.status=1) cr on cr.customer_name_cn=fd.customer_name_cn
		WHERE fd.forecast_month &gt;= '${startForecastMonth}'
			AND fd.forecast_month &lt; '${forecastMonth}'
			and fd.status=1
			AND fs.worksheet_id &lt; #{worksheetId}
			AND fw.sales_channel = '${salesChannel}'
			AND cr.sales is not null
			AND fw.delete_flag=0 and fw.status &gt; 5
			AND fa.status &gt;= 5
			AND fs.status &gt;= 5
	) f3
) f4
			<!-- 关联预测数据实际销量 -->
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f4.customer_name_cn
				AND si.[trans_month] = f4.forecast_month
				AND si.product_sku = f4.sku
				AND si.sales_channel_name = '${salesChannel}'
WHERE f4.rn = 1
ORDER BY f4.sales, f4.customer_name_cn, f4.sku, f4.forecast_month DESC
		</otherwise>
	</choose> 	
	</select>
	<select id="queryForecastApproveCustomer" resultMap="BaseResultMap" parameterType="map">
 	<choose>
		<when test="id == null"><!-- 第一次进入销售预测审批页面，做当前月预测。可用BI销售预测数据 -->
		select *, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset, si.liters actual_sellin from (
				SELECT *, ROW_NUMBER() OVER (PARTITION BY f3.customer_name_cn, f3.sku, f3.forecast_month ORDER BY f3.forecasting_month DESC) AS rn
				FROM (
					SELECT fd.forecasting_id, fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.def_forecasting_value, fs.sales, fd.avg_12, fd.value_last_year
					FROM wx_t_forecasting_sales fs
					left join wx_t_forecasting_detail fd ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
					left join wx_t_user u on u.user_id=fs.sales
					WHERE fs.worksheet_id=#{worksheetId} 
						AND exists (select 1 from dw_customer_region_sales_supervisor_rel crss where crss.sales_cai=u.cai and crss.suppervisor_cai='${salesCai}')
						AND fs.status &gt;= 5
			<choose>
				<when test="detailStatus == 0">and fd.status!=1</when>
				<otherwise>and fd.status=1</otherwise>
			</choose>
					union all
					SELECT null, fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku
						, fd.forecast_month, fw.forecasting_month, fd.def_forecasting_value, cr.sales, fd.avg_12, fd.value_last_year
					FROM wx_t_forecasting_detail fd
					left join wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
						LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
						LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
						left join (SELECT distinct crss.customer_name_cn, u.user_id sales
							FROM dw_customer_region_sales_supervisor_rel crss
							left join wx_t_user u on u.cai=crss.sales_cai 
							where crss.suppervisor_cai='${salesCai}') cr on cr.customer_name_cn=fd.customer_name_cn
					WHERE fd.forecast_month &gt;= '${startForecastMonth}'
						AND fd.forecast_month &lt; '${forecastMonth}'
						and fs.worksheet_id&lt;#{worksheetId} 
						and fw.sales_channel='${salesChannel}'
						AND fw.delete_flag=0 and fw.status &gt; 5 and fa.status &gt;= 5 AND fs.status &gt;= 5
				) f3
			) f4 
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
										, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
									FROM dw_base_trans_sell_in btsi1
									GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
						ON si.customer_name_cn = f4.customer_name_cn
							AND si.[trans_month] = f4.forecast_month
							AND si.product_sku = f4.sku
							AND si.sales_channel_name = '${salesChannel}'
			WHERE f4.rn = 1
			and f4.sku=#{sku} and f4.sales=#{sales}
			order by f4.customer_name_cn, f4.forecast_month desc
		</when>
		<otherwise><!-- 已保存的销售预测审批，包含历史数据，只能用PP保存的预测数据 -->
SELECT f4.*
	, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset, si.liters actual_sellin
FROM (
	SELECT *, ROW_NUMBER() OVER (PARTITION BY f3.customer_name_cn, f3.sku, f3.forecast_month ORDER BY f3.forecasting_month DESC) AS rn
	FROM (
		SELECT fd.forecasting_id, fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, fs.sales, fd.avg_12, fd.value_last_year
		FROM wx_t_forecasting_sales fs
			LEFT JOIN wx_t_forecasting_detail fd ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_user u ON u.user_id = fs.sales
		WHERE fs.worksheet_id = #{worksheetId}
			AND fs.approve_id=#{id}
			AND fs.status &gt;= 5
			<choose>
				<when test="detailStatus == 0">and fd.status!=1</when>
				<otherwise>and fd.status=1</otherwise>
			</choose>
		UNION ALL
		SELECT null, fd.customer_name_cn, fd.customer_name_en, fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, cr.sales, fd.avg_12, fd.value_last_year
		FROM wx_t_forecasting_detail fd
			LEFT JOIN wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
			left join (SELECT distinct fd1.customer_name_cn, fs1.sales
				FROM wx_t_forecasting_detail fd1 
				left join wx_t_forecasting_sales fs1 on fd1.forecasting_id=fs1.id
				where fs1.approve_id=#{id}
				and fd1.status=1) cr on cr.customer_name_cn=fd.customer_name_cn
		WHERE fd.forecast_month &gt;= '${startForecastMonth}'
			AND fd.forecast_month &lt; '${forecastMonth}'
			AND fs.worksheet_id &lt; #{worksheetId}
			AND cr.sales is not null
			and fw.sales_channel=(select fw1.sales_channel from wx_t_forecasting_worksheet fw1 where fw1.id=#{worksheetId})
			AND fw.delete_flag=0 and fw.status &gt; 5
			AND fa.status &gt;= 5
			AND fs.status &gt;= 5
	) f3
) f4
LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f4.customer_name_cn
				AND si.[trans_month] = f4.forecast_month
				AND si.product_sku = f4.sku
				AND si.sales_channel_name = (select fw.sales_channel from wx_t_forecasting_worksheet fw where fw.id=#{worksheetId})
WHERE f4.rn = 1
and f4.sku='${sku}' and f4.sales=${sales}
ORDER BY f4.customer_name_cn, f4.forecast_month DESC
		</otherwise>
	</choose> 	
	</select>
	<select id="queryForecastSum" resultMap="BaseResultMap" parameterType="map">
SELECT f4.*, si.liters actual_sellin
	, convert(int, substring(f4.forecast_month, 5, 2)) AS month_offset
FROM (
	SELECT *, ROW_NUMBER() OVER (PARTITION BY f3.customer_name_cn, f3.sales, f3.sku, f3.forecast_month ORDER BY f3.forecasting_month DESC) AS rn
	FROM (
		SELECT fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, fs.sales, u.ch_name AS sales_name, 
			fd.forecast_leading_time, fs.approve_id,
			u1.ch_name approver_name, fa.approver approver_id, fd.customer_name_cn, 
			fd.customer_name_en, fd.forecasting_id, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
		FROM wx_t_forecasting_sales fs
			LEFT JOIN wx_t_forecasting_detail fd ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_user u ON u.user_id = fs.sales
			left join wx_t_forecasting_approve fa on fa.id=fs.approve_id
			left join wx_t_user u1 on u1.user_id=fa.approver
		WHERE fs.worksheet_id = #{worksheetId}
			AND fs.status &gt;= 5
			and fa.status &gt;= 5
			and fd.status=1
		UNION ALL
		SELECT fd.fixed_value, fd.forecasting_value, fd.sku, fd.forecast_month
			, fw.forecasting_month, fd.def_forecasting_value, cr.sales, NULL, null, null, null, cr.approver, fd.customer_name_cn, 
			fd.customer_name_en, fd.forecasting_id, fd.avg_12, fd.value_last_year, fd.latest_forecast_value
		FROM wx_t_forecasting_detail fd
			LEFT JOIN wx_t_forecasting_sales fs ON fd.forecasting_id = fs.id
			LEFT JOIN wx_t_forecasting_worksheet fw ON fs.worksheet_id = fw.id
			LEFT JOIN wx_t_forecasting_approve fa ON fa.id = fs.approve_id
			left join (SELECT distinct fd1.customer_name_cn, fs1.sales, fa1.approver
				FROM wx_t_forecasting_detail fd1 
				left join wx_t_forecasting_sales fs1 on fd1.forecasting_id=fs1.id
				left join wx_t_forecasting_approve fa1 on fa1.id=fs1.approve_id
				where fd1.status=1) cr on cr.customer_name_cn=fd.customer_name_cn
		WHERE fd.forecast_month &gt;= '${startForecastMonth}'
			AND fd.forecast_month &lt; '${forecastMonth}'
			AND fs.worksheet_id &lt; #{worksheetId}
			AND fw.sales_channel = (select fw.sales_channel from wx_t_forecasting_worksheet fw where fw.id=#{worksheetId})
			AND cr.sales is not null
			AND fw.delete_flag=0 and fw.status &gt; 5
			AND fa.status &gt;= 5
			AND fs.status &gt;= 5
	) f3
) f4
			<!-- 关联预测数据实际销量 -->
			LEFT JOIN (SELECT btsi1.product_sku, btsi1.customer_name_cn, SUM(btsi1.liters) AS liters
							, CONVERT(varchar(6), btsi1.trans_time, 112) AS trans_month, btsi1.sales_channel_name
						FROM dw_base_trans_sell_in btsi1
						GROUP BY btsi1.product_sku, btsi1.customer_name_cn, btsi1.sales_channel_name, CONVERT(varchar(6), btsi1.trans_time, 112)) si
			ON si.customer_name_cn = f4.customer_name_cn
				AND si.[trans_month] = f4.forecast_month
				AND si.product_sku = f4.sku
				AND si.sales_channel_name = '${salesChannel}'
WHERE f4.rn = 1
ORDER BY f4.approver_id, f4.sales, f4.customer_name_cn, f4.sku, f4.forecast_month DESC
	</select>
	<select id="queryForecastApproveCustomerForSave" resultMap="BaseResultMap" parameterType="map">
select fd.*, fs.sales from wx_t_forecasting_detail fd
left join wx_t_forecasting_sales fs on fd.forecasting_id=fs.id
where fs.approve_id=#{id}
<choose>
	<when test="detailStatus == 0">
		and fd.status!=1
	</when>
	<otherwise>
		and fd.status=1
	</otherwise>
</choose>
order by fd.sku, fs.sales, fd.month_offset
	</select>
	
	<delete id="deleteByApproveId" parameterType="map">
    	delete fd from wx_t_forecasting_detail fd
		left join wx_t_forecasting_sales fs on fd.forecasting_id=fs.id
		where fs.approve_id=#{approveId}
		<choose>
			<when test="status == 0">
				and fd.status != 1
			</when>
			<when test="status != 1">
				and fd.status = #{status}
			</when>
		</choose>
	</delete>
	<select id="queryDetailsByWorksheetId1" parameterType="map" resultMap="ExportResultMap">
  SELECT
        pa.customer_code,
 		region.sales_office sales_office,
 	    pa.customer_name_en pa_customer_name_en,
 		pa.plant_code,
		fd.forecasting_id forecasting_id,
		fd.sku sku,
		fd.customer_name_cn customer_name_cn,
 		p.name_en sku_desc,
 		p.category category,
 		p.grade_abc grade_abc,
		u.ch_name as sales_name,
		approver_u.ch_name AS approver,
		fd.fixed_value fixed_value,
		fd.forecasting_value forecasting_value,
		fd.customer_name_en customer_name_en,
		fd.forecast_month forecast_month,
		fd.month_offset month_offset,
		ISNULL(fd.fixed_value,0) changes_in_l,
		fd.forecasting_value original_in_l,
		(fd.forecasting_value + ISNULL(fd.fixed_value,0))   * pa.allocate_rate forecasting_value_n0,
 		(fd1.forecasting_value + ISNULL(fd1.fixed_value,0)) * pa.allocate_rate forecasting_value_n1,
 		(fd2.forecasting_value + ISNULL(fd2.fixed_value,0)) * pa.allocate_rate  forecasting_value_n2,
 		(fd3.forecasting_value + ISNULL(fd3.fixed_value,0)) * pa.allocate_rate forecasting_value_n3,
 		(fd4.forecasting_value + ISNULL(fd4.fixed_value,0)) * pa.allocate_rate forecasting_value_n4,
 		(fd5.forecasting_value + ISNULL(fd5.fixed_value,0)) * pa.allocate_rate  forecasting_value_n5,
 		(pre_fd0.forecasting_value + ISNULL(pre_fd0.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n0,
 		(pre_fd1.forecasting_value + ISNULL(pre_fd1.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n1,
 		(pre_fd2.forecasting_value + ISNULL(pre_fd2.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n2,
 		(pre_fd3.forecasting_value + ISNULL(pre_fd3.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n3,
 		(pre_fd4.forecasting_value + ISNULL(pre_fd4.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n4,
 		(pre_fd5.forecasting_value + ISNULL(pre_fd5.fixed_value,0)) * pa.allocate_rate forecasting_pre_value_n5,
 		fs.sales,
 		fs.approve_id,
 		region.sales_cai,
 		u.cai,
 		o.id,
 		en.sap_code,
 		pa.allocate_rate
	FROM
		dbo.wx_t_forecasting_detail fd
	INNER JOIN dbo.wx_t_forecasting_sales fs on
		fd.forecasting_id = fs.id
		AND fs.worksheet_id = #{worksheetId}
		AND fs.status >= 5
 	LEFT JOIN dbo.wx_t_product p on
 		fd.sku = p.sku
	LEFT JOIN dbo.wx_t_user u ON
		fs.sales = u.user_id
	LEFT JOIN dbo.wx_t_user approver_u ON
		fs.approve_id = u.user_id
	LEFT JOIN dbo.wx_t_organization o ON
	    o.organization_name = fd.customer_name_cn
	LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON
	    en.partner_id = o.id
	OUTER APPLY ( SELECT TOP 1  pa.allocate_rate,pa.customer_name_en,pa.customer_code,pa.plant_code
		FROM dbo.wx_t_sale_for_plant_all pa
		where 1= 1
		AND fd.customer_name_en = pa.customer_name_en
		AND pa.allocate_rate IS NOT NULL
		AND pa.allocate_rate != 0
		AND (pa.effective_from_date &gt;= #{effectiveFromDate}
		OR pa.effective_to_date  &lt;= #{effectiveToDate})
		ORDER BY pa.effective_from_date
	) AS pa
 	OUTER APPLY (SELECT TOP 1 crss.region_name sales_office,crss.sales_cai FROM dbo.dw_customer_region_sales_supervisor_rel crss WHERE u.cai = crss.sales_cai ) AS region
 	LEFT JOIN dbo.wx_t_forecasting_detail fd1
 		ON fd.forecasting_id = fd1.forecasting_id AND fd.sku = fd1.sku AND fd.customer_name_cn = fd1.customer_name_cn AND fd1.month_offset = 1 AND fd1.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail fd2
 		ON fd.forecasting_id = fd2.forecasting_id AND fd.sku = fd2.sku AND fd.customer_name_cn = fd2.customer_name_cn AND fd2.month_offset = 2 AND fd2.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail fd3
 		ON fd.forecasting_id = fd3.forecasting_id AND fd.sku = fd3.sku AND fd.customer_name_cn = fd3.customer_name_cn AND fd3.month_offset = 3 AND fd3.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail fd4
 		ON fd.forecasting_id = fd4.forecasting_id AND fd.sku = fd4.sku AND fd.customer_name_cn = fd4.customer_name_cn AND fd4.month_offset = 4 AND fd4.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail fd5
 		ON fd.forecasting_id = fd5.forecasting_id AND fd.sku = fd5.sku AND fd.customer_name_cn = fd5.customer_name_cn AND fd5.month_offset = 5 AND fd5.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd0
 		ON pre_fd0.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd0.sku AND fd.customer_name_cn = pre_fd0.customer_name_cn AND pre_fd0.month_offset = 0 AND pre_fd0.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd1
 		ON pre_fd1.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd1.sku AND fd.customer_name_cn = pre_fd1.customer_name_cn AND pre_fd1.month_offset = 1 AND pre_fd1.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd2
 		ON pre_fd2.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd2.sku AND fd.customer_name_cn = pre_fd2.customer_name_cn AND pre_fd2.month_offset = 2 AND pre_fd2.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd3
 		ON pre_fd3.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd3.sku AND fd.customer_name_cn = pre_fd3.customer_name_cn AND pre_fd3.month_offset = 3 AND pre_fd3.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd4
 		ON pre_fd4.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd4.sku AND fd.customer_name_cn = pre_fd4.customer_name_cn AND pre_fd4.month_offset = 4 AND pre_fd4.status = 1
 	LEFT JOIN dbo.wx_t_forecasting_detail pre_fd5
 		ON pre_fd5.forecasting_id IN (${preForecastingSalesIds}) AND fd.sku = pre_fd5.sku AND fd.customer_name_cn = pre_fd5.customer_name_cn AND pre_fd5.month_offset = 5 AND pre_fd5.status = 1
     WHERE fd.month_offset = 0 AND fd.status = 1 AND pa.allocate_rate IS NOT NULL AND pa.allocate_rate != 0
    ORDER BY fd.customer_name_en,fd.sku
	</select>
	<select id="queryDetailsByWorksheetId" parameterType="map" resultMap="ExportResultMap">
		SELECT
		distinct
		pa.customer_code,
		region.sales_office sales_office,
		pa.customer_name_en pa_customer_name_en,
		ISNULL(pa.plant_code,'0000') plant_code,
		fd.forecasting_id forecasting_id,
		fd.sku sku,
		fd.customer_name_cn customer_name_cn,
		p.name_en sku_desc,
		p.category category,
		p.grade_abc grade_abc,
		u.ch_name as sales_name,
		approver_u.ch_name AS approver,
		fd.fixed_value fixed_value,
		fd.forecasting_value forecasting_value,
		fd.customer_name_en customer_name_en,
		fd.forecast_month forecast_month,
		fd.month_offset month_offset,
	    ISNULL(fd.fixed_value,0) + ISNULL(fd1.fixed_value,0) + ISNULL(fd2.fixed_value,0) + ISNULL(fd3.fixed_value,0) + ISNULL(fd4.fixed_value,0)  + ISNULL(fd5.fixed_value,0)  changes_in_l,
	    ISNULL(fd.forecasting_value,0) + ISNULL(fd1.forecasting_value,0) + ISNULL(fd2.forecasting_value,0) + ISNULL(fd3.forecasting_value,0) + ISNULL(fd4.forecasting_value,0) + ISNULL(fd5.forecasting_value,0) original_in_l,
		( fd.forecasting_value + ISNULL( fd.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n0,
		( fd1.forecasting_value + ISNULL( fd1.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n1,
		( fd2.forecasting_value + ISNULL( fd2.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n2,
		( fd3.forecasting_value + ISNULL( fd3.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n3,
		( fd4.forecasting_value + ISNULL( fd4.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n4,
		( fd5.forecasting_value + ISNULL( fd5.fixed_value,0 )) * ISNULL(pa.allocate_rate,1) forecasting_value_n5,
		fs.sales,
		fs.approve_id,
		region.sales_cai,
		u.cai,
		o.id,
		en.sap_code,
		pa.allocate_rate
		FROM
		dbo.wx_t_forecasting_detail fd
		INNER JOIN dbo.wx_t_forecasting_sales fs on
		fd.forecasting_id = fs.id
		AND fs.worksheet_id = #{worksheetId}
		AND fs.status >= 5
		LEFT JOIN dbo.wx_t_product p on
		fd.sku = p.sku
		LEFT JOIN dbo.wx_t_user u ON
		fs.sales = u.user_id
		LEFT JOIN dbo.wx_t_user approver_u ON
		fs.approve_id = u.user_id
		LEFT JOIN dbo.wx_t_organization o ON
		o.organization_name = fd.customer_name_cn
		LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON
		en.partner_id = o.id
		LEFT JOIN (
		SELECT
		DISTINCT
		pa.allocate_rate,
		pa.customer_name_en,
		pa.customer_code,
		pa.plant_code
		FROM
		dbo.wx_t_sale_for_plant_all pa
		where
		1 = 1
		AND (pa.effective_from_date &gt;= #{effectiveFromDate}
		OR pa.effective_to_date  &lt;= #{effectiveToDate})
		) AS pa ON
		fd.customer_name_en = pa.customer_name_en
		LEFT JOIN (
		SELECT
		DISTINCT crss.region_name sales_office,
		crss.sales_cai
		FROM
		dbo.dw_customer_region_sales_supervisor_rel crss) AS region
		ON u.cai = region.sales_cai
		LEFT JOIN dbo.wx_t_forecasting_detail fd1 ON
		fd.forecasting_id = fd1.forecasting_id
		AND fd.sku = fd1.sku
		AND fd.customer_name_cn = fd1.customer_name_cn
		AND fd1.month_offset = 1
		AND fd1.status = 1
		LEFT JOIN dbo.wx_t_forecasting_detail fd2 ON
		fd.forecasting_id = fd2.forecasting_id
		AND fd.sku = fd2.sku
		AND fd.customer_name_cn = fd2.customer_name_cn
		AND fd2.month_offset = 2
		AND fd2.status = 1
		LEFT JOIN dbo.wx_t_forecasting_detail fd3 ON
		fd.forecasting_id = fd3.forecasting_id
		AND fd.sku = fd3.sku
		AND fd.customer_name_cn = fd3.customer_name_cn
		AND fd3.month_offset = 3
		AND fd3.status = 1
		LEFT JOIN dbo.wx_t_forecasting_detail fd4 ON
		fd.forecasting_id = fd4.forecasting_id
		AND fd.sku = fd4.sku
		AND fd.customer_name_cn = fd4.customer_name_cn
		AND fd4.month_offset = 4
		AND fd4.status = 1
		LEFT JOIN dbo.wx_t_forecasting_detail fd5 ON
		fd.forecasting_id = fd5.forecasting_id
		AND fd.sku = fd5.sku
		AND fd.customer_name_cn = fd5.customer_name_cn
		AND fd5.month_offset = 5
		AND fd5.status = 1
		WHERE
		fd.month_offset = 0
		AND fd.status = 1
		ORDER BY
		fd.customer_name_en,
		fd.sku
	</select>
	<select id="queryForecastCustomerForCtrl" parameterType="map" resultMap="ExportResultMap">
	select distinct top ${limit} crss.customer_name_cn, crss.customer_name_en, sf.customer_code from dw_customer_region_sales_supervisor_rel crss
	left join wx_t_sale_for_plant_all sf on crss.customer_name_en=sf.customer_name_en
	where 1=1
		<choose>
			<when test="userType == 'sales'">
				and crss.sales_cai=#{loginCai}
			</when>
			<when test="userType == 'supervisor'">
				and crss.suppervisor_cai=#{loginCai}
			</when>
		</choose>
		<if test="salesChannel != null">
			and exists (select 1 from dw_region_sales_channel_rel rsc where rsc.region_name=crss.region_name and rsc.sales_channel_name=#{salesChannel})
		</if>
		<if test="keyword != null and keyword != ''">
			and (crss.customer_name_cn like '%' + #{keyword} + '%' or crss.customer_name_en like '%' + #{keyword} + '%' or sf.customer_code like '%' + #{keyword} + '%' or left(dbo.f_GetPyToAboutHanyu(crss.customer_name_cn),500) like '%' + #{keyword} + '%')
		</if>
	</select>
	<select id="querySalesForecastForEmail" parameterType="map" resultMap="ExportResultMap">
select fd.customer_name_cn, fd.customer_name_en, fd.sku, fd.forecast_month, p.name_en product_name_en, p.name product_name, fd.fixed_value, fd.forecasting_value 
from wx_t_forecasting_sales fs
left join wx_t_forecasting_detail fd on fs.id=fd.forecasting_id
left join wx_t_product p on p.sku=fd.sku
where fs.worksheet_id=#{worksheetId} and fs.id=#{id}
and fd.fixed_value>0.0000001
and fd.status=1
order by fd.forecast_month, fd.customer_name_cn, fd.sku
	</select>
	<select id="queryHistorySales" parameterType="map" resultMap="SalesHistoryMap">
				SELECT
					SUM( btsi.liters ) AS liters,
					CONVERT(varchar(6), btsi.trans_time, 112) AS trans_month,
					btsi.sales_channel_name
				FROM
					dbo.dw_base_trans_sell_in btsi
				LEFT JOIN dbo.wx_t_forecasting_worksheet fw ON 	btsi.sales_channel_name = fw.sales_channel
				WHERE
					1 = 1
					<choose>
						<when test="history == false and userType == 'sales'">
							AND EXISTS (SELECT 1 FROM dw_customer_region_sales_supervisor_rel crss
							    WHERE 1=1 AND crss.customer_name_cn = btsi.customer_name_cn AND crss.sales_cai = #{cai} )
						</when>
						<when test="history == false and userType == 'supervisor'">
							AND EXISTS (SELECT 1 FROM dw_customer_region_sales_supervisor_rel crss
								WHERE 1=1 AND crss.customer_name_cn = btsi.customer_name_cn AND crss.suppervisor_cai = #{cai} )
						</when>
						<when test="history == true and userType == 'sales'">
							AND EXISTS (SELECT 1 FROM wx_t_forecasting_sales fs1 LEFT JOIN wx_t_forecasting_detail fd1 ON fd1.forecasting_id=fs1.id
								WHERE 1=1 AND fs1.worksheet_id=#{worksheetId} AND fs1.sales=#{userId} AND fd1.customer_name_cn = btsi.customer_name_cn)
						</when>
						<when test="history == true and userType == 'supervisor'">
							AND EXISTS ( SELECT 1
								FROM wx_t_forecasting_detail fd1
								LEFT JOIN wx_t_forecasting_sales fs1 on fd1.forecasting_id=fs1.id
								LEFT JOIN dbo.wx_t_forecasting_approve fa1 ON fa1.id = fs1.approve_id
								WHERE 1=1
							    AND fs1.worksheet_id=#{worksheetId}
							    AND fd1.customer_name_cn = btsi.customer_name_cn
								AND fa1.approver = #{userId}
								AND fd1.status=1
							    AND fs1.sales is not null )
						</when>
					</choose>
					AND fw.delete_flag=0
		            AND fw.id = #{worksheetId}
					<if test="customerNameCn != null">
						AND btsi.customer_name_cn = #{customerNameCn}
					</if>
					<if test="sku != null">
						AND btsi.product_sku = #{sku}
					</if>
					AND ( btsi.trans_time &gt;= #{startDate} AND btsi.trans_time &lt;  #{endDate} )
				GROUP BY
					btsi.sales_channel_name,
				CONVERT(varchar(6),btsi.trans_time, 112)
				ORDER BY CONVERT(varchar(6),btsi.trans_time, 112)
	</select>
</mapper>
