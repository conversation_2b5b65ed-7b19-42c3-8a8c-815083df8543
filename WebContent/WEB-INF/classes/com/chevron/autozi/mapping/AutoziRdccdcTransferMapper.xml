<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.autozi.dao.AutoziRdccdcTransferMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.autozi.model.AutoziRdccdcTransfer" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="transfer_no" property="transferNo" jdbcType="NVARCHAR" />
    <result column="transfer_date" property="transferDate" jdbcType="TIMESTAMP" />
    <result column="warehouse_from_code" property="warehouseFromCode" jdbcType="NVARCHAR" />
    <result column="warehouse_to_code" property="warehouseToCode" jdbcType="NVARCHAR" />
    <result column="chevron_product_sku" property="chevronProductSku" jdbcType="NVARCHAR" />
    <result column="transfer_quantity" property="transferQuantity" jdbcType="BIGINT" />
    <result column="delivery_cost" property="deliveryCost" jdbcType="NUMERIC" />
    <result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
    <result column="delivery_quantity" property="deliveryQuantity" jdbcType="BIGINT" />
    <result column="receive_quantity" property="receiveQuantity" jdbcType="BIGINT" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="name" property="name" jdbcType="NVARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, transfer_no, transfer_date, warehouse_from_code, warehouse_to_code, chevron_product_sku, 
    transfer_quantity, delivery_cost, total_amount, delivery_quantity, receive_quantity, 
    import_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.autozi.model.AutoziRdccdcTransferExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_autozi_rdccdc_transfer
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByParams" parameterType="com.chevron.autozi.model.TransferOrderParams" resultMap="BaseResultMap">
  	select t.id, transfer_no, transfer_date, warehouse_from_code, warehouse_to_code, chevron_product_sku, 
    transfer_quantity, delivery_cost, total_amount, delivery_quantity, receive_quantity, 
    import_time, p.name 
    from wx_t_autozi_rdccdc_transfer t
    left join wx_t_product p
    on p.sku=t.chevron_product_sku
    <where>
    	<if test="queryType == 2">
    		<if test="queryField!=null and queryField!=''">
	    		OR transfer_no like '%'+#{queryField}+'%' 
	    		or warehouse_from_code like '%'+#{queryField}+'%' 
	    		or warehouse_to_code like '%'+#{queryField}+'%' 
	    		or p.name like '%'+#{queryField}+'%' 
    		</if>
    	</if>
    	<if test="queryType==1">
	    	<if test="transferNo!=null and transferNo!='' ">
	    		AND transfer_no like '%'+#{transferNo}+'%'
	    	</if>
	    	<if test="warehouseFromCode!=null and warehouseFromCode!='' ">
	    		 and warehouse_from_code like '%'+#{warehouseFromCode}+'%'
	    	</if>
	    	<if test="warehouseToCode!=null and warehouseToCode!='' ">
	    		 and warehouse_to_code like '%'+#{warehouseToCode}+'%'
	    	</if>
	    	<if test="transferStartDate!=null">
	    		 and transfer_date &gt;= #{transferStartDate}
	    	</if>
	    	<if test="transferEndDate!=null">
	    		 and transfer_date &lt;= #{transferEndDate}
	    	</if>
    	</if>
    	</where>
    
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.autozi.model.AutoziRdccdcTransferExample" >
    delete from wx_t_autozi_rdccdc_transfer
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.autozi.model.AutoziRdccdcTransfer" >
    insert into wx_t_autozi_rdccdc_transfer (id, transfer_no, transfer_date, 
      warehouse_from_code, warehouse_to_code, chevron_product_sku, 
      transfer_quantity, delivery_cost, total_amount, 
      delivery_quantity, receive_quantity, import_time, 
      remark)
    values (#{id,jdbcType=BIGINT}, #{transferNo,jdbcType=NVARCHAR}, #{transferDate,jdbcType=TIMESTAMP}, 
      #{warehouseFromCode,jdbcType=NVARCHAR}, #{warehouseToCode,jdbcType=NVARCHAR}, #{chevronProductSku,jdbcType=NVARCHAR}, 
      #{transferQuantity,jdbcType=BIGINT}, #{deliveryCost,jdbcType=NUMERIC}, #{totalAmount,jdbcType=NUMERIC}, 
      #{deliveryQuantity,jdbcType=BIGINT}, #{receiveQuantity,jdbcType=BIGINT}, #{importTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.autozi.model.AutoziRdccdcTransfer" >
    insert into wx_t_autozi_rdccdc_transfer
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="transferNo != null" >
        transfer_no,
      </if>
      <if test="transferDate != null" >
        transfer_date,
      </if>
      <if test="warehouseFromCode != null" >
        warehouse_from_code,
      </if>
      <if test="warehouseToCode != null" >
        warehouse_to_code,
      </if>
      <if test="chevronProductSku != null" >
        chevron_product_sku,
      </if>
      <if test="transferQuantity != null" >
        transfer_quantity,
      </if>
      <if test="deliveryCost != null" >
        delivery_cost,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="deliveryQuantity != null" >
        delivery_quantity,
      </if>
      <if test="receiveQuantity != null" >
        receive_quantity,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="transferNo != null" >
        #{transferNo,jdbcType=NVARCHAR},
      </if>
      <if test="transferDate != null" >
        #{transferDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseFromCode != null" >
        #{warehouseFromCode,jdbcType=NVARCHAR},
      </if>
      <if test="warehouseToCode != null" >
        #{warehouseToCode,jdbcType=NVARCHAR},
      </if>
      <if test="chevronProductSku != null" >
        #{chevronProductSku,jdbcType=NVARCHAR},
      </if>
      <if test="transferQuantity != null" >
        #{transferQuantity,jdbcType=BIGINT},
      </if>
      <if test="deliveryCost != null" >
        #{deliveryCost,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="deliveryQuantity != null" >
        #{deliveryQuantity,jdbcType=BIGINT},
      </if>
      <if test="receiveQuantity != null" >
        #{receiveQuantity,jdbcType=BIGINT},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_autozi_rdccdc_transfer
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.transferNo != null" >
        transfer_no = #{record.transferNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.transferDate != null" >
        transfer_date = #{record.transferDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.warehouseFromCode != null" >
        warehouse_from_code = #{record.warehouseFromCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.warehouseToCode != null" >
        warehouse_to_code = #{record.warehouseToCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.chevronProductSku != null" >
        chevron_product_sku = #{record.chevronProductSku,jdbcType=NVARCHAR},
      </if>
      <if test="record.transferQuantity != null" >
        transfer_quantity = #{record.transferQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryCost != null" >
        delivery_cost = #{record.deliveryCost,jdbcType=NUMERIC},
      </if>
      <if test="record.totalAmount != null" >
        total_amount = #{record.totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.deliveryQuantity != null" >
        delivery_quantity = #{record.deliveryQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.receiveQuantity != null" >
        receive_quantity = #{record.receiveQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_autozi_rdccdc_transfer
    set id = #{record.id,jdbcType=BIGINT},
      transfer_no = #{record.transferNo,jdbcType=NVARCHAR},
      transfer_date = #{record.transferDate,jdbcType=TIMESTAMP},
      warehouse_from_code = #{record.warehouseFromCode,jdbcType=NVARCHAR},
      warehouse_to_code = #{record.warehouseToCode,jdbcType=NVARCHAR},
      chevron_product_sku = #{record.chevronProductSku,jdbcType=NVARCHAR},
      transfer_quantity = #{record.transferQuantity,jdbcType=BIGINT},
      delivery_cost = #{record.deliveryCost,jdbcType=NUMERIC},
      total_amount = #{record.totalAmount,jdbcType=NUMERIC},
      delivery_quantity = #{record.deliveryQuantity,jdbcType=BIGINT},
      receive_quantity = #{record.receiveQuantity,jdbcType=BIGINT},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>