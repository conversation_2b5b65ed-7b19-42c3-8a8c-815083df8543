<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.autozi.dao.AutoziCustomerOrderMapper">
	<resultMap id="BaseResultMap" type="com.chevron.autozi.model.AutoziCustomerOrder">
		<result column="id" property="id" jdbcType="BIGINT" />
		<result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
		<result column="garage_name" property="garageName" jdbcType="NVARCHAR" />
		<result column="delivery_warehouse" property="deliveryWarehouse"
			jdbcType="NVARCHAR" />
		<result column="order_book_date" property="orderBookDate"
			jdbcType="TIMESTAMP" />
		<result column="chevron_product_sku" property="chevronProductSku"
			jdbcType="NVARCHAR" />
		<result column="quantity" property="quantity" jdbcType="BIGINT" />
		<result column="sales_price" property="salesPrice" jdbcType="NUMERIC" />
		<result column="sales_amount" property="salesAmount" jdbcType="NUMERIC" />
		<result column="balance_price" property="balancePrice"
			jdbcType="NUMERIC" />
		<result column="balance_amount" property="balanceAmount"
			jdbcType="NUMERIC" />
		<result column="delivery_time" property="deliveryTime"
			jdbcType="TIMESTAMP" />
		<result column="delivery_quantity" property="deliveryQuantity"
			jdbcType="BIGINT" />
		<result column="close_time" property="closeTime" jdbcType="TIMESTAMP" />
		<result column="close_quantity" property="closeQuantity"
			jdbcType="BIGINT" />
		<result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
		<result column="remark" property="remark" jdbcType="NVARCHAR" />
		<result column="name" property="name" jdbcType="NVARCHAR" />
	</resultMap>
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value}
									and
									#{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem"
										open="(" close=")" separator=",">
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria"
				separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value}
									and
									#{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem"
										open="(" close=")" separator=",">
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	<sql id="Base_Column_List">
		id, order_no, garage_name, delivery_warehouse,
		order_book_date,
		chevron_product_sku,
		quantity, sales_price,
		sales_amount, balance_price, balance_amount,
		delivery_time,
		delivery_quantity, close_time, close_quantity, import_time, remark
	</sql>
	<select id="selectByExample" resultMap="BaseResultMap"
		parameterType="com.chevron.autozi.model.AutoziCustomerOrderExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List" />
		from wx_t_autozi_customer_order
		<if test="_parameter != null">
			<include refid="Example_Where_Clause" />
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	<delete id="deleteByExample" parameterType="com.chevron.autozi.model.AutoziCustomerOrderExample">
		delete from wx_t_autozi_customer_order
		<if test="_parameter != null">
			<include refid="Example_Where_Clause" />
		</if>
	</delete>
	<insert id="insert" parameterType="com.chevron.autozi.model.AutoziCustomerOrder">
		insert into
		wx_t_autozi_customer_order (id, order_no, garage_name,
		delivery_warehouse, order_book_date, chevron_product_sku,
		quantity,
		sales_price, sales_amount,
		balance_price, balance_amount,
		delivery_time,
		delivery_quantity, close_time, close_quantity,
		import_time, remark)
		values (#{id,jdbcType=BIGINT},
		#{orderNo,jdbcType=NVARCHAR},
		#{garageName,jdbcType=NVARCHAR},
		#{deliveryWarehouse,jdbcType=NVARCHAR},
		#{orderBookDate,jdbcType=TIMESTAMP},
		#{chevronProductSku,jdbcType=NVARCHAR},
		#{quantity,jdbcType=BIGINT},
		#{salesPrice,jdbcType=NUMERIC}, #{salesAmount,jdbcType=NUMERIC},
		#{balancePrice,jdbcType=NUMERIC}, #{balanceAmount,jdbcType=NUMERIC},
		#{deliveryTime,jdbcType=TIMESTAMP},
		#{deliveryQuantity,jdbcType=BIGINT}, #{closeTime,jdbcType=TIMESTAMP},
		#{closeQuantity,jdbcType=BIGINT},
		#{importTime,jdbcType=TIMESTAMP},
		#{remark,jdbcType=NVARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.chevron.autozi.model.AutoziCustomerOrder">
		insert into wx_t_autozi_customer_order
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="orderNo != null">
				order_no,
			</if>
			<if test="garageName != null">
				garage_name,
			</if>
			<if test="deliveryWarehouse != null">
				delivery_warehouse,
			</if>
			<if test="orderBookDate != null">
				order_book_date,
			</if>
			<if test="chevronProductSku != null">
				chevron_product_sku,
			</if>
			<if test="quantity != null">
				quantity,
			</if>
			<if test="salesPrice != null">
				sales_price,
			</if>
			<if test="salesAmount != null">
				sales_amount,
			</if>
			<if test="balancePrice != null">
				balance_price,
			</if>
			<if test="balanceAmount != null">
				balance_amount,
			</if>
			<if test="deliveryTime != null">
				delivery_time,
			</if>
			<if test="deliveryQuantity != null">
				delivery_quantity,
			</if>
			<if test="closeTime != null">
				close_time,
			</if>
			<if test="closeQuantity != null">
				close_quantity,
			</if>
			<if test="importTime != null">
				import_time,
			</if>
			<if test="remark != null">
				remark,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="orderNo != null">
				#{orderNo,jdbcType=NVARCHAR},
			</if>
			<if test="garageName != null">
				#{garageName,jdbcType=NVARCHAR},
			</if>
			<if test="deliveryWarehouse != null">
				#{deliveryWarehouse,jdbcType=NVARCHAR},
			</if>
			<if test="orderBookDate != null">
				#{orderBookDate,jdbcType=TIMESTAMP},
			</if>
			<if test="chevronProductSku != null">
				#{chevronProductSku,jdbcType=NVARCHAR},
			</if>
			<if test="quantity != null">
				#{quantity,jdbcType=BIGINT},
			</if>
			<if test="salesPrice != null">
				#{salesPrice,jdbcType=NUMERIC},
			</if>
			<if test="salesAmount != null">
				#{salesAmount,jdbcType=NUMERIC},
			</if>
			<if test="balancePrice != null">
				#{balancePrice,jdbcType=NUMERIC},
			</if>
			<if test="balanceAmount != null">
				#{balanceAmount,jdbcType=NUMERIC},
			</if>
			<if test="deliveryTime != null">
				#{deliveryTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deliveryQuantity != null">
				#{deliveryQuantity,jdbcType=BIGINT},
			</if>
			<if test="closeTime != null">
				#{closeTime,jdbcType=TIMESTAMP},
			</if>
			<if test="closeQuantity != null">
				#{closeQuantity,jdbcType=BIGINT},
			</if>
			<if test="importTime != null">
				#{importTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=NVARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_autozi_customer_order
		<set>
			<if test="record.id != null">
				id = #{record.id,jdbcType=BIGINT},
			</if>
			<if test="record.orderNo != null">
				order_no = #{record.orderNo,jdbcType=NVARCHAR},
			</if>
			<if test="record.garageName != null">
				garage_name = #{record.garageName,jdbcType=NVARCHAR},
			</if>
			<if test="record.deliveryWarehouse != null">
				delivery_warehouse =
				#{record.deliveryWarehouse,jdbcType=NVARCHAR},
			</if>
			<if test="record.orderBookDate != null">
				order_book_date =
				#{record.orderBookDate,jdbcType=TIMESTAMP},
			</if>
			<if test="record.chevronProductSku != null">
				chevron_product_sku =
				#{record.chevronProductSku,jdbcType=NVARCHAR},
			</if>
			<if test="record.quantity != null">
				quantity = #{record.quantity,jdbcType=BIGINT},
			</if>
			<if test="record.salesPrice != null">
				sales_price = #{record.salesPrice,jdbcType=NUMERIC},
			</if>
			<if test="record.salesAmount != null">
				sales_amount = #{record.salesAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.balancePrice != null">
				balance_price = #{record.balancePrice,jdbcType=NUMERIC},
			</if>
			<if test="record.balanceAmount != null">
				balance_amount =
				#{record.balanceAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.deliveryTime != null">
				delivery_time =
				#{record.deliveryTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.deliveryQuantity != null">
				delivery_quantity =
				#{record.deliveryQuantity,jdbcType=BIGINT},
			</if>
			<if test="record.closeTime != null">
				close_time = #{record.closeTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.closeQuantity != null">
				close_quantity = #{record.closeQuantity,jdbcType=BIGINT},
			</if>
			<if test="record.importTime != null">
				import_time = #{record.importTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=NVARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause" />
		</if>
	</update>
	<update id="updateByExample" parameterType="map">
		update wx_t_autozi_customer_order
		set id =
		#{record.id,jdbcType=BIGINT},
		order_no =
		#{record.orderNo,jdbcType=NVARCHAR},
		garage_name =
		#{record.garageName,jdbcType=NVARCHAR},
		delivery_warehouse =
		#{record.deliveryWarehouse,jdbcType=NVARCHAR},
		order_book_date =
		#{record.orderBookDate,jdbcType=TIMESTAMP},
		chevron_product_sku =
		#{record.chevronProductSku,jdbcType=NVARCHAR},
		quantity =
		#{record.quantity,jdbcType=BIGINT},
		sales_price =
		#{record.salesPrice,jdbcType=NUMERIC},
		sales_amount =
		#{record.salesAmount,jdbcType=NUMERIC},
		balance_price =
		#{record.balancePrice,jdbcType=NUMERIC},
		balance_amount =
		#{record.balanceAmount,jdbcType=NUMERIC},
		delivery_time =
		#{record.deliveryTime,jdbcType=TIMESTAMP},
		delivery_quantity =
		#{record.deliveryQuantity,jdbcType=BIGINT},
		close_time =
		#{record.closeTime,jdbcType=TIMESTAMP},
		close_quantity =
		#{record.closeQuantity,jdbcType=BIGINT},
		import_time =
		#{record.importTime,jdbcType=TIMESTAMP},
		remark =
		#{record.remark,jdbcType=NVARCHAR}
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause" />
		</if>
	</update>
	<select id="selectByParams" resultMap="BaseResultMap"
		parameterType="com.chevron.autozi.model.CustomerOrderParams">
		SELECT
		order_no,garage_name,order_book_date,chevron_product_sku,quantity,wx_t_product.name
		FROM wx_t_autozi_customer_order t
		LEFT JOIN wx_t_product
		ON
		t.chevron_product_sku = wx_t_product.sku
		WHERE 1=1
		<if test="queryType == 1">
			<if test="orderNo != null and orderNo != ''">
				AND t.order_no LIKE '%' + #{orderNo,jdbcType=NVARCHAR} + '%'
			</if>
			<if test="garageName != null and orderNo != ''">
				AND t.garage_name LIKE '%' + #{garageName,jdbcType=NVARCHAR}+ '%'
			</if>
			<if test="dateFrom != null and dateFrom != ''">
				AND t.order_book_date &gt;= #{dateFrom}
			</if>
			<if test="dateTo != null and dateTo != ''">
				AND t.order_book_date &lt;= #{dateTo}
			</if>
		</if>
		<if test="queryType == 2">
			AND (t.order_no LIKE '%' + #{orderNo,jdbcType=NVARCHAR} + '%'
			OR t.garage_name LIKE '%' + #{garageName,jdbcType=NVARCHAR}+ '%'
			OR t.chevron_product_sku LIKE '%' + #{orderNo,jdbcType=NVARCHAR} + '%'
			OR wx_t_product.name LIKE '%' + #{orderNo,jdbcType=NVARCHAR} + '%')
		</if>
	</select>
</mapper>