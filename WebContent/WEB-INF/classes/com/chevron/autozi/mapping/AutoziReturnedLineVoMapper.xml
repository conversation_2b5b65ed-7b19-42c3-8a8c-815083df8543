<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.autozi.dao.AutoziReturnedLineVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.autozi.model.AutoziReturnedLineVo" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="po_no" property="poNo" jdbcType="NVARCHAR" />
    <result column="po_return_no" property="poReturnNo" jdbcType="NVARCHAR" />
    <result column="product_code" property="productCode" jdbcType="NVARCHAR" />
    <result column="return_quantity" property="returnQuantity" jdbcType="BIGINT" />
    <result column="confirm_quantity" property="confirmQuantity" jdbcType="BIGINT" />
    <result column="purchase_status" property="purchaseStatus" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, po_no, po_return_no, product_code, return_quantity, confirm_quantity, purchase_status, 
    remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.autozi.model.AutoziReturnedLineVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_autozi_returned_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.autozi.model.AutoziReturnedLineVoExample" >
    delete from wx_t_autozi_returned_line
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.autozi.model.AutoziReturnedLineVo" >
    insert into wx_t_autozi_returned_line (id, po_no, po_return_no, 
      product_code, return_quantity, confirm_quantity, 
      purchase_status, remark)
    values (#{id,jdbcType=BIGINT}, #{poNo,jdbcType=NVARCHAR}, #{poReturnNo,jdbcType=NVARCHAR}, 
      #{productCode,jdbcType=NVARCHAR}, #{returnQuantity,jdbcType=BIGINT}, #{confirmQuantity,jdbcType=BIGINT}, 
      #{purchaseStatus,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.autozi.model.AutoziReturnedLineVo" >
    insert into wx_t_autozi_returned_line
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="poNo != null" >
        po_no,
      </if>
      <if test="poReturnNo != null" >
        po_return_no,
      </if>
      <if test="productCode != null" >
        product_code,
      </if>
      <if test="returnQuantity != null" >
        return_quantity,
      </if>
      <if test="confirmQuantity != null" >
        confirm_quantity,
      </if>
      <if test="purchaseStatus != null" >
        purchase_status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="poNo != null" >
        #{poNo,jdbcType=NVARCHAR},
      </if>
      <if test="poReturnNo != null" >
        #{poReturnNo,jdbcType=NVARCHAR},
      </if>
      <if test="productCode != null" >
        #{productCode,jdbcType=NVARCHAR},
      </if>
      <if test="returnQuantity != null" >
        #{returnQuantity,jdbcType=BIGINT},
      </if>
      <if test="confirmQuantity != null" >
        #{confirmQuantity,jdbcType=BIGINT},
      </if>
      <if test="purchaseStatus != null" >
        #{purchaseStatus,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_autozi_returned_line
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.poNo != null" >
        po_no = #{record.poNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.poReturnNo != null" >
        po_return_no = #{record.poReturnNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.productCode != null" >
        product_code = #{record.productCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.returnQuantity != null" >
        return_quantity = #{record.returnQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.confirmQuantity != null" >
        confirm_quantity = #{record.confirmQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseStatus != null" >
        purchase_status = #{record.purchaseStatus,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_autozi_returned_line
    set id = #{record.id,jdbcType=BIGINT},
      po_no = #{record.poNo,jdbcType=NVARCHAR},
      po_return_no = #{record.poReturnNo,jdbcType=NVARCHAR},
      product_code = #{record.productCode,jdbcType=NVARCHAR},
      return_quantity = #{record.returnQuantity,jdbcType=BIGINT},
      confirm_quantity = #{record.confirmQuantity,jdbcType=BIGINT},
      purchase_status = #{record.purchaseStatus,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
   <insert id="insertAutoziReturnLineVoBatch" parameterType="java.util.List">
	     insert into wx_t_autozi_returned_line (po_no, po_return_no, 
		      product_code, return_quantity, purchase_status)
      	 values
	    <foreach collection="list" index="index" item="item"
			separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.poNo,jdbcType=NVARCHAR},
				#{item.poReturnNo,jdbcType=NVARCHAR},
				#{item.productCode,jdbcType=NVARCHAR},
				#{item.returnQuantity,jdbcType=BIGINT},
				#{item.purchaseStatus,jdbcType=NVARCHAR}
			</trim>
		</foreach>
 	</insert>
  
  
  <select id="getAutoziReturnLineVoLstByPoReturnedNo"  parameterType="map" resultMap="BaseResultMap">
    select * from wx_t_autozi_returned_line
    where
     <if test="poheadno!=null and poheadno!=''">
     po_return_no = #{poheadno,jdbcType=NVARCHAR}
     and
     </if>
    1=1
  
  </select>
  
  
  
   <update id="updateBatchAutoziReturnLineVoByPoReturnedNo" parameterType="java.util.List">
   		<foreach collection="list" item="item" index="index" open="begin" close="end;" separator=";">
			update wx_t_autozi_returned_line 
			<set>
				<if test="item.purchaseStatus != null">
					purchase_status = #{item.purchaseStatus,jdbcType=NVARCHAR}
				</if>
			</set>
	 		where	po_return_no = #{item.poReturnNo,jdbcType=NVARCHAR}
		</foreach>
   
   </update>
  
  
</mapper>