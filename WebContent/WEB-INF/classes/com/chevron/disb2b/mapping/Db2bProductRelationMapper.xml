<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.disb2b.dao.Db2bProductRelationMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.disb2b.model.Db2bProductRelation">
		<result column="parent_product_id" property="parentProductId" jdbcType="BIGINT"/>
		<result column="relation_prouct_id" property="relationProuctId" jdbcType="BIGINT"/>
		<result column="amount" property="amount" jdbcType="INTEGER"/>
		<result column="product_name" property="productName" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		parent_product_id,relation_prouct_id,amount
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.disb2b.model.Db2bProductRelation">
		insert into wx_t_db2b_product_relation
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="parentProductId != null">
				parent_product_id,
			</if>
			<if test="relationProuctId != null">
				relation_prouct_id,
			</if>
			<if test="amount != null">
				amount,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="parentProductId != null">
				#{parentProductId,jdbcType=BIGINT},
			</if>
			<if test="relationProuctId != null">
				#{relationProuctId,jdbcType=BIGINT},
			</if>
			<if test="amount != null">
				#{amount,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_db2b_product_relation
		<set>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.disb2b.model.Db2bProductRelationExample">
		delete from wx_t_db2b_product_relation
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.disb2b.model.Db2bProductRelationExample" resultType="int">
		select count(1) from wx_t_db2b_product_relation
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bProductRelationExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_db2b_product_relation
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.parent_product_id, t1.relation_prouct_id,amount,t2.product_name
		  from wx_t_db2b_product_relation t1
		  left join wx_t_db2b_product t2 on t1.relation_prouct_id = t2.id 
		 where 1=1
		 <if test="parentProductId != null">
			and t1.parent_product_id = #{parentProductId,jdbcType=BIGINT}
		</if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_db2b_product_relation (parent_product_id, relation_prouct_id,amount) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.parentProductId, jdbcType=BIGINT}, #{item.relationProuctId, jdbcType=BIGINT}, #{item.amount, jdbcType=INTEGER}
			</trim>
		</foreach>
	</insert>
</mapper>
