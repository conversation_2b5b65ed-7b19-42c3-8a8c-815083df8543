<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.disb2b.dao.Db2bShoppingCarMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.disb2b.model.Db2bShoppingCar">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="product_id" property="productId" jdbcType="BIGINT"/>
		<result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
		<result column="user_key" property="userKey" jdbcType="VARCHAR"/>
		<result column="amount" property="amount" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="product_price" property="productPrice" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,product_id,source_type,user_key,amount,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.disb2b.model.Db2bShoppingCar">
		update wx_t_db2b_shopping_car set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.disb2b.model.Db2bShoppingCar">
		update wx_t_db2b_shopping_car
		<set>
			<if test="productId != null" >
				product_id = #{productId,jdbcType=BIGINT},
			</if>
			<if test="sourceType != null" >
				source_type = #{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="userKey != null" >
				user_key = #{userKey,jdbcType=VARCHAR},
			</if>
			<if test="amount != null" >
				amount = #{amount,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.disb2b.model.Db2bShoppingCarExample">
    	delete from wx_t_db2b_shopping_car
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.disb2b.model.Db2bShoppingCar">
		insert into wx_t_db2b_shopping_car
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="productId != null">
				product_id,
			</if>
			<if test="sourceType != null">
				source_type,
			</if>
			<if test="userKey != null">
				user_key,
			</if>
			<if test="amount != null">
				amount,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="productId != null">
				#{productId,jdbcType=BIGINT},
			</if>
			<if test="sourceType != null">
				#{sourceType,jdbcType=VARCHAR},
			</if>
			<if test="userKey != null">
				#{userKey,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				#{amount,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_db2b_shopping_car
		<set>
			<if test="record.productId != null">
				product_id = #{record.productId,jdbcType=BIGINT},
			</if>
			<if test="record.sourceType != null">
				source_type = #{record.sourceType,jdbcType=VARCHAR},
			</if>
			<if test="record.userKey != null">
				user_key = #{record.userKey,jdbcType=VARCHAR},
			</if>
			<if test="record.amount != null">
				amount = #{record.amount,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.disb2b.model.Db2bShoppingCarExample">
		delete from wx_t_db2b_shopping_car
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.disb2b.model.Db2bShoppingCarExample" resultType="int">
		select count(1) from wx_t_db2b_shopping_car
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bShoppingCarExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_db2b_shopping_car
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bShoppingCarExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_db2b_shopping_car
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.product_id, t1.source_type, t1.user_key, t1.amount, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,t2.product_name,t2.product_price
		  from wx_t_db2b_shopping_car t1 left join wx_t_db2b_product t2 on t1.product_id = t2.id
		 where 1=1
		<if test="userKey != null">
			 and t1.user_key = #{userKey,jdbcType=VARCHAR}
		</if>
		<if test="productId != null">
			 and t1.product_id = #{productId,jdbcType=BIGINT}
		</if>
		<if test="sourceType != null">
			 and t1.source_type = #{sourceType,jdbcType=VARCHAR}
		</if>
		<if test="createUserId !=null">
		     and t1.create_user_id = #{createUserId}
		</if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_db2b_shopping_car (product_id, source_type, user_key, amount, create_user_id, create_time, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.productId, jdbcType=BIGINT}, #{item.sourceType, jdbcType=VARCHAR}, #{item.userKey, jdbcType=VARCHAR}, #{item.amount, jdbcType=INTEGER}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
