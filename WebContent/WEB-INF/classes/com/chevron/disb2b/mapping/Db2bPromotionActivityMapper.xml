<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.disb2b.dao.Db2bPromotionActivityMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.disb2b.model.Db2bPromotionActivity">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="activity_name" property="activityName" jdbcType="VARCHAR"/>
		<result column="activity_level" property="activityLevel" jdbcType="INTEGER"/>
		<result column="start_date" property="startDate" jdbcType="DATE"/>
		<result column="end_date" property="endDate" jdbcType="DATE"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
        <result column="message_send_type" property="messageSendType"/>
        <result column="message_content" property="messageContent" jdbcType="VARCHAR"/>
        <result column="custom_send_time" property="customSendTime" jdbcType="DATE"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,partner_id,activity_name,activity_level,start_date,end_date,enable_flag,delete_flag,create_user_id,create_time,
		update_user_id,update_time,message_send_type,message_content,custom_send_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.disb2b.model.Db2bPromotionActivity">
		update wx_t_db2b_promotion_activity set
				partner_id = #{partnerId,jdbcType=BIGINT},
				activity_name = #{activityName,jdbcType=VARCHAR},
				activity_level = #{activityLevel,INTEGER},
				start_date = #{startDate,jdbcType=DATE},
				end_date = #{endDate,jdbcType=DATE},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.disb2b.model.Db2bPromotionActivity">
		update wx_t_db2b_promotion_activity
		<set>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="activityName != null" >
				activity_name = #{activityName,jdbcType=VARCHAR},
			</if>
			<if test="activityLevel != null" >
				activity_level = #{activityLevel,jdbcType=INTEGER},
			</if>
			<if test="startDate != null" >
				start_date = #{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null" >
				end_date = #{endDate,jdbcType=DATE},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
            <if test="messageSendType != null">
                message_send_type = #{messageSendType,jdbcType=INTEGER},
                <if test="messageSendType == 1">
                    custom_send_time = null,
                </if>
            </if>
            <if test="messageContent != null">
                message_content = #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="customSendTime != null">
                custom_send_time = #{customSendTime,jdbcType=DATE},
            </if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityExample">
    	delete from wx_t_db2b_promotion_activity
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.disb2b.model.Db2bPromotionActivity" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_db2b_promotion_activity
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="activityName != null">
				activity_name,
			</if>
			<if test="activityLevel != null">
				activity_level,
			</if>
			<if test="startDate != null">
				start_date,
			</if>
			<if test="endDate != null">
				end_date,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="enableFlag != null" >
				enable_flag,
			</if>
            <if test="messageSendType != null">
                message_send_type,
            </if>
            <if test="messageContent != null">
                message_content,
            </if>
            <if test="customSendTime != null">
                custom_send_time,
            </if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="activityName != null">
				#{activityName,jdbcType=VARCHAR},
			</if>
			<if test="activityLevel != null">
				#{activityLevel,jdbcType=INTEGER},
			</if>
			<if test="startDate != null">
				#{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null">
				#{endDate,jdbcType=DATE},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
            <if test="messageSendType != null">
                #{messageSendType,jdbcType=INTEGER},
            </if>
            <if test="messageContent != null">
                #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="customSendTime != null">
                #{customSendTime,jdbcType=DATE},
            </if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_db2b_promotion_activity
		<set>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.activityName != null">
				activity_name = #{record.activityName,jdbcType=VARCHAR},
			</if>
			<if test="record.activityLevel != null">
				activity_level = #{record.activityLevel,jdbcType=INTEGER},
			</if>
			<if test="record.startDate != null">
				start_date = #{record.startDate,jdbcType=DATE},
			</if>
			<if test="record.endDate != null">
				end_date = #{record.endDate,jdbcType=DATE},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.enableFlag != null">
			    enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityExample">
		delete from wx_t_db2b_promotion_activity
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityExample" resultType="int">
		select count(1) from wx_t_db2b_promotion_activity
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_db2b_promotion_activity
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_db2b_promotion_activity
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.partner_id, t1.activity_name, t1.activity_level, t1.start_date, t1.end_date, t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time,t1.enable_flag,t1.message_content,t1.message_send_type,t1.custom_send_time
		  from wx_t_db2b_promotion_activity t1
		 where 1=1
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="activityName != null and activityName != ''">
			and t1.activity_name like '%' + #{activityName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="dateFrom != null">
			and t1.start_date &gt;= #{dateFrom, jdbcType=DATE}
		</if>
		<if test="dateTo != null">
			and t1.end_date &lt; #{dateTo, jdbcType=DATE}
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag =#{enableFlag}
		</if>
		<if test="startDate!=null and startDate != ''" >
			and <![CDATA[t1.start_date <= CONVERT(varchar(100), #{startDate}, 23)]]>
		</if>
		<if test="endDate!=null and endDate != ''" >
		    and <![CDATA[t1.end_date >= CONVERT(varchar(100), #{endDate}, 23)]]>
		</if>
		order by activity_level desc
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bPromotionActivityParams">
		select t1.id, t1.partner_id, t1.activity_name, t1.activity_level, t1.start_date, t1.end_date, t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time,t1.enable_flag,isnull(t2.organization_name,'雪佛龙') partner_name,
             t1.message_content,t1.message_send_type,t1.custom_send_time
		  from wx_t_db2b_promotion_activity t1 LEFT JOIN wx_t_organization t2 on t1.partner_id = t2.id
		 where t1.delete_flag=0
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="activityName != null and activityName != ''">
			and t1.activity_name like '%' + #{activityName, jdbcType=VARCHAR} + '%'
		</if>
		<!--  <if test="dateFrom != null">
			and t1.start_date &gt;= #{dateFrom, jdbcType=DATE}
		</if>
		<if test="dateTo != null">
			and t1.start_date &lt; #{dateTo, jdbcType=DATE}
		</if>
		-->
		<if test="queryField != null and queryField != ''">
			and (t1.activity_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
		
		<if test="dateFromStr!=null and dateFromStr != ''" >
			and <![CDATA[t1.start_date>= #{dateFromStr}]]>
		</if>
		<if test="dateToStr!=null and dateToStr != ''" >
			and <![CDATA[t1.end_date<#{dateToStr}]]>
		</if>
		
		
		
		
	</select>
</mapper>
