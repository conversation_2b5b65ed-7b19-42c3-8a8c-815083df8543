<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.disb2b.dao.Db2bProductMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.disb2b.model.Db2bProduct">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="product_type" property="productType" jdbcType="INTEGER"/>
		<result column="product_photo" property="productPhoto" jdbcType="BIGINT"/>
		<result column="product_category1" property="productCategory1" jdbcType="VARCHAR"/>
		<result column="product_category1_text" property="productCategory1Text" jdbcType="VARCHAR"/>
		<result column="product_category2" property="productCategory2" jdbcType="VARCHAR"/>
		<result column="product_category2_text" property="productCategory2Text" jdbcType="VARCHAR"/>
		<result column="product_category3" property="productCategory3" jdbcType="VARCHAR"/>
		<result column="product_category3_text" property="productCategory3Text" jdbcType="VARCHAR"/>
		<result column="product_category4" property="productCategory4" jdbcType="VARCHAR"/>
		<result column="product_category4_text" property="productCategory4Text" jdbcType="VARCHAR"/>
		<result column="product_category5" property="productCategory5" jdbcType="VARCHAR"/>
		<result column="product_category5_text" property="productCategory5Text" jdbcType="VARCHAR"/>
		<result column="product_price" property="productPrice" jdbcType="NUMERIC"/>
		<result column="product_marker" property="productMarker" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="product_desc" property="productDesc" jdbcType="VARCHAR"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="detail_flag" property="detailFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="product_brand" property="productBrand" jdbcType="VARCHAR"/>
		<result column="product_unit" property="productUnit" jdbcType="VARCHAR"/>
		<result column="product_specs" property="productSpecs" jdbcType="VARCHAR"/>
		<result column="product_hd_photo" property="productHdPhoto" jdbcType="BIGINT"/>
		<result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="promotion_product_id" property="promotionProductId" jdbcType="BIGINT"/>
		<result column="promotion_type" property="promotionType" jdbcType="INTEGER"/>
		<result column="amount" property="amount" jdbcType="INTEGER"/>
		<result column="product_promotion_id" property="productPromotionId" jdbcType="BIGINT"/>
		<result column="capacity" property="capacity" jdbcType="VARCHAR"/>
		
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,partner_id,product_name,sku,product_type,product_photo,product_category1,product_category2,product_category3,
		product_category4,product_category5,product_price,product_marker,status,product_desc,ext_property1,ext_property2,
		ext_property3,ext_property4,ext_property5,inventory_quantity,detail_flag,delete_flag,create_user_id,create_time,
		update_user_id,update_time,product_brand,product_unit,product_specs,product_hd_photo
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.disb2b.model.Db2bProduct">
		update wx_t_db2b_product set
				partner_id = #{partnerId,jdbcType=BIGINT},
				product_name = #{productName,jdbcType=VARCHAR},
				sku = #{sku,jdbcType=VARCHAR},
				product_type = #{productType,jdbcType=INTEGER},
				product_photo = #{productPhoto,jdbcType=BIGINT},
				product_category1 = #{productCategory1,jdbcType=VARCHAR},
				product_category2 = #{productCategory2,jdbcType=VARCHAR},
				product_category3 = #{productCategory3,jdbcType=VARCHAR},
				product_category4 = #{productCategory4,jdbcType=VARCHAR},
				product_category5 = #{productCategory5,jdbcType=VARCHAR},
				product_price = #{productPrice,jdbcType=NUMERIC},
				status = #{status,jdbcType=INTEGER},
				product_desc = #{productDesc,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP},
				product_brand = #{productBrand,jdbcType=VARCHAR},
				product_unit = #{productUnit,jdbcType=VARCHAR},
				product_specs = #{productSpecs,jdbcType=VARCHAR},
				product_hd_photo = #{productHdPhoto,jdbcType=BIGINT},
				ext_property1 = #{extProperty1,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.disb2b.model.Db2bProduct">
		update wx_t_db2b_product
		<set>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="productName != null" >
				product_name = #{productName,jdbcType=VARCHAR},
			</if>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="productType != null" >
				product_type = #{productType,jdbcType=INTEGER},
			</if>
			<if test="productPhoto != null" >
				product_photo = #{productPhoto,jdbcType=BIGINT},
			</if>
			<if test="productCategory1 != null" >
				product_category1 = #{productCategory1,jdbcType=VARCHAR},
			</if>
			<if test="productCategory2 != null" >
				product_category2 = #{productCategory2,jdbcType=VARCHAR},
			</if>
			<if test="productCategory3 != null" >
				product_category3 = #{productCategory3,jdbcType=VARCHAR},
			</if>
			<if test="productCategory4 != null" >
				product_category4 = #{productCategory4,jdbcType=VARCHAR},
			</if>
			<if test="productCategory5 != null" >
				product_category5 = #{productCategory5,jdbcType=VARCHAR},
			</if>
			<if test="productPrice != null" >
				product_price = #{productPrice,jdbcType=NUMERIC},
			</if>
			<if test="productMarker != null" >
				product_marker = #{productMarker,jdbcType=INTEGER},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="productDesc != null" >
				product_desc = #{productDesc,jdbcType=VARCHAR},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="detailFlag != null" >
				detail_flag = #{detailFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="productBrand != null" >
				product_brand = #{productBrand,jdbcType=VARCHAR},
			</if>
			<if test="productUnit != null" >
				product_unit = #{productUnit,jdbcType=VARCHAR},
			</if>
			<if test="productSpecs != null" >
				product_specs = #{productSpecs,jdbcType=VARCHAR},
			</if>
			<if test="productHdPhoto != null" >
				product_hd_photo = #{productHdPhoto,jdbcType=BIGINT},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.disb2b.model.Db2bProductExample">
    	delete from wx_t_db2b_product
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.disb2b.model.Db2bProduct" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_db2b_product
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="productName != null">
				product_name,
			</if>
			<if test="sku != null">
				sku,
			</if>
			<if test="productType != null">
				product_type,
			</if>
			<if test="productPhoto != null">
				product_photo,
			</if>
			<if test="productCategory1 != null">
				product_category1,
			</if>
			<if test="productCategory2 != null">
				product_category2,
			</if>
			<if test="productCategory3 != null">
				product_category3,
			</if>
			<if test="productCategory4 != null">
				product_category4,
			</if>
			<if test="productCategory5 != null">
				product_category5,
			</if>
			<if test="productPrice != null">
				product_price,
			</if>
			<if test="productMarker != null">
				product_marker,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="productDesc != null">
				product_desc,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="detailFlag != null">
				detail_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="productBrand != null">
				product_brand,
			</if>
			<if test="productUnit != null">
				product_unit,
			</if>
			<if test="productSpecs != null">
				product_specs,
			</if>
			<if test="productHdPhoto != null">
				product_hd_photo,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="productName != null">
				#{productName,jdbcType=VARCHAR},
			</if>
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="productType != null">
				#{productType,jdbcType=INTEGER},
			</if>
			<if test="productPhoto != null">
				#{productPhoto,jdbcType=BIGINT},
			</if>
			<if test="productCategory1 != null">
				#{productCategory1,jdbcType=VARCHAR},
			</if>
			<if test="productCategory2 != null">
				#{productCategory2,jdbcType=VARCHAR},
			</if>
			<if test="productCategory3 != null">
				#{productCategory3,jdbcType=VARCHAR},
			</if>
			<if test="productCategory4 != null">
				#{productCategory4,jdbcType=VARCHAR},
			</if>
			<if test="productCategory5 != null">
				#{productCategory5,jdbcType=VARCHAR},
			</if>
			<if test="productPrice != null">
				#{productPrice,jdbcType=NUMERIC},
			</if>
			<if test="productMarker != null">
				#{productMarker,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="productDesc != null">
				#{productDesc,jdbcType=VARCHAR},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="detailFlag != null">
				#{detailFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="productBrand != null">
				#{productBrand,jdbcType=VARCHAR},
			</if>
			<if test="productUnit != null">
				#{productUnit,jdbcType=VARCHAR},
			</if>
			<if test="productSpecs != null">
				#{productSpecs,jdbcType=VARCHAR},
			</if>
			<if test="productHdPhoto != null">
				#{productHdPhoto,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_db2b_product
		<set>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.productName != null">
				product_name = #{record.productName,jdbcType=VARCHAR},
			</if>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.productType != null">
				product_type = #{record.productType,jdbcType=INTEGER},
			</if>
			<if test="record.productPhoto != null">
				product_photo = #{record.productPhoto,jdbcType=BIGINT},
			</if>
			<if test="record.productCategory1 != null">
				product_category1 = #{record.productCategory1,jdbcType=VARCHAR},
			</if>
			<if test="record.productCategory2 != null">
				product_category2 = #{record.productCategory2,jdbcType=VARCHAR},
			</if>
			<if test="record.productCategory3 != null">
				product_category3 = #{record.productCategory3,jdbcType=VARCHAR},
			</if>
			<if test="record.productCategory4 != null">
				product_category4 = #{record.productCategory4,jdbcType=VARCHAR},
			</if>
			<if test="record.productCategory5 != null">
				product_category5 = #{record.productCategory5,jdbcType=VARCHAR},
			</if>
			<if test="record.productPrice != null">
				product_price = #{record.productPrice,jdbcType=NUMERIC},
			</if>
			<if test="record.productMarker != null">
				product_marker = #{record.productMarker,jdbcType=INTEGER},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.productDesc != null">
				product_desc = #{record.productDesc,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="record.detailFlag != null">
				detail_flag = #{record.detailFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.productBrand != null">
				product_brand = #{record.productBrand,jdbcType=VARCHAR},
			</if>
			<if test="record.productUnit != null">
				product_unit = #{record.productUnit,jdbcType=VARCHAR},
			</if>
			<if test="record.productSpecs != null">
				product_specs = #{record.productSpecs,jdbcType=VARCHAR},
			</if>
			<if test="record.productHdPhoto != null">
				product_hd_photo = #{record.productHdPhoto,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.disb2b.model.Db2bProductExample">
		delete from wx_t_db2b_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.disb2b.model.Db2bProductExample" resultType="int">
		select count(1) from wx_t_db2b_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bProductExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_db2b_product
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bProductExample">
    	select
		<include refid="Base_Column_List"/>,
		(select t2.organization_name from wx_t_organization t2 where t.partner_id = t2.id) partner_name,
		(select pt.capacity from wx_t_product pt where 1=1 and pt.sku=t.sku) capacity
		from wx_t_db2b_product t
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select 
		<if test="limit != null">
		top ${limit}
		</if>
		t1.id, t1.partner_id, t1.product_name, t1.sku, t1.product_type, t1.product_photo, t1.product_category1,
			 t1.product_category2, t1.product_category3, t1.product_category4, t1.product_category5, t1.product_price,
			 t1.product_marker, t1.status, t1.product_desc, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.detail_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,t1.product_brand, t1.product_unit, t1.product_specs,
			 t1.product_hd_photo,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Db2bProduct.productCategory' and di1.dic_item_code=t1.product_category1) product_category1_text
			<!-- ,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category2) product_category2_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category3) product_category3_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category4) product_category4_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category5) product_category5_text -->
		  from wx_t_db2b_product t1
		 where t1.delete_flag=0 and t1.detail_flag=#{detailFlag, jdbcType=INTEGER}
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="productName != null and productName != ''">
			and t1.product_name like '%' + #{productName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="sku != null and sku != ''">
			and t1.sku like '%' + #{sku, jdbcType=VARCHAR} + '%'
		</if>
		<if test="productCategory1 != null and productCategory1 != ''">
			and t1.product_category1 = #{productCategory1, jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and t1.status = #{status, jdbcType=INTEGER}
		</if>
		<if test="partnerIdList !=null">
		   and t1.partner_id in
		<foreach collection="partnerIdList" item="id" index="index" open="(" close=")" separator=",">
	             #{id}
	     </foreach>
		</if>
		<if test="productTypes != null">
			and t1.product_type != #{productTypes, jdbcType=INTEGER}
		</if>
		<if test="keyWord != null and keyWord != ''">
		and (t1.product_name like '%' + #{keyWord, jdbcType=VARCHAR} + '%' or t1.sku like '%' + #{keyWord, jdbcType=VARCHAR} + '%'
		or t1.ext_property1 like '%' + #{keyWord, jdbcType=VARCHAR} + '%'
		or left(dbo.f_GetPyToAboutHanyu(t1.product_name),500) LIKE '%' + #{keyWord} + '%')
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bProductParams">
		 select t1.id, t1.partner_id, t1.product_name, t1.sku, t1.product_type, t1.product_photo, t1.product_category1,
			 t1.product_category2, t1.product_category3, t1.product_category4, t1.product_category5, t1.product_price,
			 t1.product_marker, t1.status, t1.product_desc, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.detail_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time, t2.organization_name partner_name,t1.product_brand, t1.product_unit, t1.product_specs,
			 t1.product_hd_photo,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Db2bProduct.productCategory' and di1.dic_item_code=t1.product_category1) product_category1_text
			 <!-- 
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category2) product_category2_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category3) product_category3_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category4) product_category4_text
			,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='xxx' and di1.dic_item_code=t1.product_category5) product_category5_text -->
		  from wx_t_db2b_product t1 LEFT JOIN wx_t_organization t2 on t1.partner_id = t2.id
		 where t1.detail_flag=0
		 <choose>
		 	<when test="deleteFlag == 1">
		 	and t1.delete_flag != 0
		 	</when>
		 	<otherwise>
		 	and t1.delete_flag=0
		 	</otherwise>
		 </choose>
		 <choose>
		 	<when test="partnerId != null">
		 	and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		 	</when>
		 	<otherwise>
		 	and t1.partner_id != -1 
		 	</otherwise>
		 </choose>
		<if test="productTypes != null and productTypes.size()>0">
			and t1.product_type in
			<foreach collection="productTypes" index="index" item="item" open="(" separator="," close=")">
				'${item}'
			</foreach>
		</if>
		<if test="productName != null and productName != ''">
			and t1.product_name like '%' + #{productName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="sku != null and sku != ''">
			and t1.sku like '%' + #{sku, jdbcType=VARCHAR} + '%'
		</if>
		<if test="productCategory1 != null and productCategory1 != ''">
			and t1.product_category1 = #{productCategory1, jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and t1.status = #{status, jdbcType=INTEGER}
		</if>
		<if test="extProperty1 != null and extProperty1 != ''">
			and t1.ext_property1 like '%' + #{extProperty1, jdbcType=VARCHAR} + '%'
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.product_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.sku like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
		 <if test="isB2b and chevronProduct">
		  and exists (select 1 from wx_t_product product where 1=1 and t1.sku = product.sku
		<if test="productCategoryList!=null and productCategoryList.size()>0">
		    and product.category in
           <foreach collection="productCategoryList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		<if test="oilTypeList!=null and oilTypeList.size()>0">
		    and product.oil_type in
		    <foreach collection="oilTypeList" index="index" item="item" open="(" separator="," close=")">
              '${item}'
            </foreach>
		</if>
		<if test="oilViscosityList!=null and oilViscosityList.size()>0">
		    and product.viscosity in
		    <foreach collection="oilViscosityList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		<if test="oilCapacityList!=null and oilCapacityList.size()>0">
		    and product.capacity in
		     <foreach collection="oilCapacityList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		 )
		</if>
	</select>
	
	<!-- app分页查询 -->	
	<select id="queryAppForPage" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bProductParams">
		 select t1.id, t1.partner_id, t1.product_name, t1.sku, t1.product_type, t1.product_photo, t1.product_category1,
			 t1.product_category2, t1.product_category3, t1.product_category4, t1.product_category5, t1.product_price,
			 t1.product_marker, t1.status, t1.product_desc, t1.ext_property1, t1.ext_property2, t1.ext_property3,
			 t1.ext_property4, t1.ext_property5, t1.detail_flag, t1.delete_flag, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time, t2.organization_name partner_name,t1.product_brand, t1.product_unit, t1.product_specs,
			 t1.product_hd_photo,
			 case when ( <include refid="queryPromotions"/>)>0 then 1 else 0 end as promote_order
		  from wx_t_db2b_product t1 LEFT JOIN wx_t_organization t2 on t1.partner_id = t2.id
		  where t1.detail_flag=0
		  <choose>
		 	<when test="deleteFlag == 1">
		 	and t1.delete_flag != 0
		 	</when>
		 	<otherwise>
		 	and t1.delete_flag=0
		 	</otherwise>
		 </choose>
		 <choose>
		 	<when test="partnerId != null">
		 	and t1.partner_id = #{partnerId, jdbcType=BIGINT}
		 	</when>
		 	<otherwise>
		 	and t1.partner_id != -1 
		 	</otherwise>
		 </choose>
		<if test="productName != null and productName != ''">
			and (t1.product_name like '%' + #{productName, jdbcType=VARCHAR} + '%' or exists(select 1
			from wx_t_db2b_product_relation t3
			left join wx_t_db2b_product t4 on t3.relation_prouct_id = t4.id
			where t3.parent_product_id = t1.id
			and t4.product_name like '%'+#{productName, jdbcType=VARCHAR}+'%'))
		</if>
		<if test="sku != null and sku != ''">
			and (t1.sku like '%' + #{sku, jdbcType=VARCHAR} + '%' or exists(select 1
			from wx_t_db2b_product_relation t3
			left join wx_t_db2b_product t4 on t3.relation_prouct_id = t4.id
			where t3.parent_product_id = t1.id
			and t4.sku like '%'+#{sku, jdbcType=VARCHAR}+'%'))
		</if>
		<if test="productCategory1 != null and productCategory1 != ''">
			and t1.product_category1 = #{productCategory1, jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and t1.status = #{status, jdbcType=INTEGER}
		</if>
		<if test="extProperty1 != null and extProperty1 != ''">
			and t1.ext_property1 like '%' + #{extProperty1, jdbcType=VARCHAR} + '%'
		</if>
		<if test="queryField != null and queryField != ''">
			and ((t1.product_name like '%' + #{queryField, jdbcType=VARCHAR} + '%' or t1.sku like '%' + #{queryField, jdbcType=VARCHAR} + '%')
			or exists(select 1
			from wx_t_db2b_product_relation t3
			left join wx_t_db2b_product t4 on t3.relation_prouct_id = t4.id
			where t3.parent_product_id = t1.id
			and (t4.sku like '%'+#{queryField, jdbcType=VARCHAR}+'%' or t1.product_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')) )
		</if><!-- 
		<if test="isB2b">
			and t1.product_type!=2 /*其他产品只能用于创建产品包*/
		</if> -->
		 <if test="isB2b and chevronProduct">
		  and exists (select 1 from wx_t_product product where 1=1 and t1.sku = product.sku
		<if test="productCategoryList!=null and productCategoryList.size()>0">
		    and product.category in
           <foreach collection="productCategoryList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		<if test="oilTypeList!=null and oilTypeList.size()>0">
		    and product.oil_type in
		    <foreach collection="oilTypeList" index="index" item="item" open="(" separator="," close=")">
              '${item}'
            </foreach>
		</if>
		<if test="oilViscosityList!=null and oilViscosityList.size()>0">
		    and product.viscosity in
		    <foreach collection="oilViscosityList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		<if test="oilCapacityList!=null and oilCapacityList.size()>0">
		    and product.capacity in
		     <foreach collection="oilCapacityList" index="index" item="item" open="(" separator="," close=")">
             '${item}'
            </foreach>
		</if>
		 )
		</if>
		<if test="isB2b and hasPromotionFilter">
		and  (<include refid="queryPromotions"/>)>0
		</if>
	</select>

	<!-- 查询活动 -->
	<sql id="queryPromotions">
		select count(0) from (
		select promotion.*,
		activity.partner_id,
		activity.start_date,
		activity.end_date,
		db2b_product.id as product_id
		from wx_t_db2b_promotion promotion
		inner join wx_t_db2b_activity activity
		on activity.id = promotion.activity_id
		inner join wx_t_workshop_relationship ship on activity.id=ship.source_key
		left join wx_t_db2b_promotion_product promotionProduct
		on promotionProduct.promotion_id=promotion.id
		left join wx_t_db2b_product db2b_product
		on promotionProduct.product_id = db2b_product.id
		where 1 = 1
		and promotion.delete_flag = 0
		and activity.enable_flag = 1
		and activity.delete_flag = 0
		<if test="workshopId != null">
			and ship.workshop_id = #{workshopId,jdbcType=BIGINT}
		</if>
		<if test="relationType != null and relationType!=''">
			and ship.relation_type = #{relationType,jdbcType=VARCHAR}
		</if>
		)a
		where 1=1
		and a.product_id = t1.id
		<if test="partnerId !=null">
			and a.partner_id = #{partnerId,jdbcType=BIGINT}
		</if>
		<if test="startDate!=null and startDate != ''" >
			and <![CDATA[a.start_date <= CONVERT(varchar(100), #{startDate}, 23)]]>
		</if>
		<if test="endDate!=null and endDate != ''" >
			and <![CDATA[a.end_date >= CONVERT(varchar(100), #{endDate}, 23)]]>
		</if>
		<if test="promotionsList!=null and promotionsList.size()>0">
			and a.promotion_type in
			<foreach collection="promotionsList" index="index" item="item" open="(" separator="," close=")">
				'${item}'
			</foreach>
		</if>
	</sql>

	<select id="getGiftInfosByParams" resultMap="BaseResultMap" parameterType="map">
	     select product.id,product.status,product.delete_flag,product.product_name,t1.amount
	     from wx_t_db2b_gift_product t1
		 left join wx_t_db2b_product product on t1.gift_product_id = product.id
		 where 1=1
		 <if test="shopCarId!=null">
		    and t1.shop_car_id = #{shopCarId,jdbcType=BIGINT}
		 </if>
	</select>
	
	<select id="getOrderLineByParams" resultMap="BaseResultMap" parameterType="map" >
	select t.id,t.partner_id,t.product_name,t.sku,t.product_type,t.product_photo,t.product_category1,t.product_category2,t.product_category3,
		t.product_category4,t.product_category5,t.product_marker,t.status,t.product_desc,t.ext_property1,t.ext_property2,
		t.ext_property3,t.ext_property4,t.ext_property5,t.inventory_quantity,t.detail_flag,t.delete_flag,t.create_user_id,t.create_time,
		t.update_user_id,t.update_time,t.product_brand,t.product_unit,t.product_specs,t.product_hd_photo,
	 orderLine.amount from wx_t_db2b_product t 
     inner join wx_t_db2b_order_line orderLine on t.id = orderLine.product_id
     where 1=1 
     <if test="orderId!=null">
     and orderLine.order_id= #{orderId}
     </if>
     <if test="additionalFlag!=null">
     and orderLine.additional_flag=#{additionalFlag}
     </if>
     <if test="promotionId!=null">
     and orderLine.promotion_id=#{promotionId}
     </if>
     <if test="productId!=null">
     and orderLine.product_id=#{productId}
     </if>
	</select>

	<insert id="insertChildProduct" >
		insert into wx_t_db2b_product ( partner_id
                              , product_name
                              , sku
                              , product_type
                              , product_photo
                              , product_category1
                              , product_category2
                              , product_category3
                              , product_category4
                              , product_category5
                              , product_price
                              , product_marker
                              , status
                              , product_desc
                              , ext_property1
                              , ext_property2
                              , ext_property3
                              , ext_property4
                              , ext_property5
                              , inventory_quantity
                              , detail_flag
                              , delete_flag
                              , create_user_id
                              , create_time
                              , update_user_id
                              , update_time
                              , product_brand
                              , product_unit
                              , product_specs
                              , product_hd_photo)
select o.id partner_id
     , p.product_name
     , p.sku
     , p.product_type
     , p.product_photo
     , p.product_category1
     , p.product_category2
     , p.product_category3
     , p.product_category4
     , p.product_category5
     , p.product_price
     , p.product_marker
     , 20   status
     , p.product_desc
     , p.ext_property1
     , p.ext_property2
     , p.ext_property3
     , p.ext_property4
     , p.ext_property5
     , p.inventory_quantity
     , p.detail_flag
     , p.delete_flag
     , p.create_user_id
     , p.create_time
     , p.update_user_id
     , p.update_time
     , p.product_brand
     , p.product_unit
     , p.product_specs
     , p.product_hd_photo
from wx_t_db2b_product p
         left join wx_t_organization o on o.status = 1
         left join wx_t_partner_o2o_enterprise pe on pe.partner_id = o.id
		<!--  left join wx_t_dealer_business_fun fun on fun.dealer_id = o.id and business_fun_code = 'DIST_B2B' -->
where pe.distributor_id > 0
  and p.partner_id = -1
  and p.delete_flag = 0
  and p.status=20
  <!-- and fun.business_custom_id = -1 -->
  and not exists(select 1 from wx_t_db2b_product p1 where p1.sku = p.sku and p1.partner_id = o.id)
  and exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir 
	left join dw_region_sales_channel_rel bisr on bir.region=bisr.region_name
	where bir.distributor_id=pe.distributor_id and bisr.bu='Indirect')
  <!-- and exists (select 1 from wx_t_dea_sell_thr_pro_per p2 where p2.product_sku = p.sku and p2.partner_id = o.id)
and exists (select 1 from wx_t_user u left join wx_t_userrole ur on ur.user_id=u.user_id
left join wx_t_role r on r.role_id=ur.role_id where u.user_id=ur.user_id
and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST') and (u.type is null or u.type!='1') and u.org_id=o.id) -->
	</insert>
</mapper>
