<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.disb2b.dao.Db2bDistSettingMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.disb2b.model.Db2bDistSetting">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="order_point_switch" property="orderPointSwitch" jdbcType="INTEGER"/>
		<result column="award_point_rate" property="awardPointRate" jdbcType="NUMERIC"/>
		<result column="point_value" property="pointValue" jdbcType="NUMERIC"/>
		<result column="zfb_qrcode_switch" property="zfbQrcodeSwitch" jdbcType="INTEGER"/>
		<result column="zfb_qrcode_id" property="zfbQrcodeId" jdbcType="BIGINT"/>
		<result column="wechat_qrcode_switch" property="wechatQrcodeSwitch" jdbcType="INTEGER"/>
		<result column="wechat_qrcode_id" property="wechatQrcodeId" jdbcType="BIGINT"/>
		<result column="ext_switch" property="extSwitch" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
		<result column="app_id" property="appId" jdbcType="VARCHAR"/>
		<result column="app_secret" property="appSecret" jdbcType="VARCHAR"/>
		<result column="chevron_price_switch" property="chevronPriceSwitch" jdbcType="INTEGER"/>
		<result column="wechat_pay_page_switch" property="wechatPayPageSwitch" jdbcType="INTEGER"/>
		
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
		<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,partner_id,order_point_switch,award_point_rate,point_value,zfb_qrcode_switch,zfb_qrcode_id,wechat_qrcode_switch,
		wechat_qrcode_id,app_id,app_secret,ext_switch,chevron_price_switch,wechat_pay_page_switch,delete_flag,create_user_id,create_time,
		update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.disb2b.model.Db2bDistSetting">
		update wx_t_db2b_dist_setting set
				partner_id = #{partnerId,jdbcType=BIGINT},
				order_point_switch = #{orderPointSwitch,jdbcType=INTEGER},
				award_point_rate = #{awardPointRate,jdbcType=NUMERIC},
				point_value = #{pointValue,jdbcType=NUMERIC},
				zfb_qrcode_switch = #{zfbQrcodeSwitch,jdbcType=INTEGER},
				zfb_qrcode_id = #{zfbQrcodeId,jdbcType=BIGINT},
				wechat_qrcode_switch = #{wechatQrcodeSwitch,jdbcType=INTEGER},
				wechat_qrcode_id = #{wechatQrcodeId,jdbcType=BIGINT},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.disb2b.model.Db2bDistSetting">
		update wx_t_db2b_dist_setting
		<set>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="orderPointSwitch != null" >
				order_point_switch = #{orderPointSwitch,jdbcType=INTEGER},
			</if>
			<if test="awardPointRate != null" >
				award_point_rate = #{awardPointRate,jdbcType=NUMERIC},
			</if>
			<if test="pointValue != null" >
				point_value = #{pointValue,jdbcType=NUMERIC},
			</if>
			<if test="zfbQrcodeSwitch != null" >
				zfb_qrcode_switch = #{zfbQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="zfbQrcodeId != null" >
				zfb_qrcode_id = #{zfbQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="wechatQrcodeSwitch != null" >
				wechat_qrcode_switch = #{wechatQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="wechatQrcodeId != null" >
				wechat_qrcode_id = #{wechatQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="appId != null" >
				app_id = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="appSecret != null" >
				app_secret = #{appSecret,jdbcType=VARCHAR},
			</if>
			<if test="extSwitch != null" >
				ext_switch = #{extSwitch,jdbcType=INTEGER},
			</if>
			<if test="chevronPriceSwitch != null" >
				chevron_price_switch = #{chevronPriceSwitch,jdbcType=INTEGER},
			</if>
			<if test="wechatPayPageSwitch != null" >
				wechat_pay_page_switch = #{wechatPayPageSwitch,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.disb2b.model.Db2bDistSettingExample">
    	delete from wx_t_db2b_dist_setting
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.disb2b.model.Db2bDistSetting">
		insert into wx_t_db2b_dist_setting
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="orderPointSwitch != null">
				order_point_switch,
			</if>
			<if test="awardPointRate != null">
				award_point_rate,
			</if>
			<if test="pointValue != null">
				point_value,
			</if>
			<if test="zfbQrcodeSwitch != null">
				zfb_qrcode_switch,
			</if>
			<if test="zfbQrcodeId != null">
				zfb_qrcode_id,
			</if>
			<if test="wechatQrcodeSwitch != null">
				wechat_qrcode_switch,
			</if>
			<if test="wechatQrcodeId != null">
				wechat_qrcode_id,
			</if>
			<if test="appId != null">
				app_id,
			</if>
			<if test="appSecret != null">
				app_secret,
			</if>
			<if test="extSwitch != null">
				ext_switch,
			</if>
			<if test="chevronPriceSwitch != null">
				chevron_price_switch,
			</if>
			<if test="wechatPayPageSwitch != null">
				wechat_pay_page_switch,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="orderPointSwitch != null">
				#{orderPointSwitch,jdbcType=INTEGER},
			</if>
			<if test="awardPointRate != null">
				#{awardPointRate,jdbcType=NUMERIC},
			</if>
			<if test="pointValue != null">
				#{pointValue,jdbcType=NUMERIC},
			</if>
			<if test="zfbQrcodeSwitch != null">
				#{zfbQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="zfbQrcodeId != null">
				#{zfbQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="wechatQrcodeSwitch != null">
				#{wechatQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="wechatQrcodeId != null">
				#{wechatQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="appId != null">
				#{appId,jdbcType=VARCHAR},
			</if>
			<if test="appSecret != null">
				#{appSecret,jdbcType=VARCHAR},
			</if>
			<if test="extSwitch != null">
				#{extSwitch,jdbcType=INTEGER},
			</if>
			<if test="chevronPriceSwitch != null">
				#{chevronPriceSwitch,jdbcType=INTEGER},
			</if>
			<if test="wechatPayPageSwitch != null">
				#{wechatPayPageSwitch,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_db2b_dist_setting
		<set>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.orderPointSwitch != null">
				order_point_switch = #{record.orderPointSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.awardPointRate != null">
				award_point_rate = #{record.awardPointRate,jdbcType=NUMERIC},
			</if>
			<if test="record.pointValue != null">
				point_value = #{record.pointValue,jdbcType=NUMERIC},
			</if>
			<if test="record.zfbQrcodeSwitch != null">
				zfb_qrcode_switch = #{record.zfbQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.zfbQrcodeId != null">
				zfb_qrcode_id = #{record.zfbQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="record.wechatQrcodeSwitch != null">
				wechat_qrcode_switch = #{record.wechatQrcodeSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.wechatQrcodeId != null">
				wechat_qrcode_id = #{record.wechatQrcodeId,jdbcType=BIGINT},
			</if>
			<if test="record.appId != null">
				app_id = #{record.appId,jdbcType=VARCHAR},
			</if>
			<if test="record.appSecret != null">
				app_secret = #{record.appSecret,jdbcType=VARCHAR},
			</if>
			<if test="record.extSwitch != null">
				ext_switch = #{record.extSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.chevronPriceSwitch != null">
				chevron_price_switch = #{record.chevronPriceSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.wechatPayPageSwitch != null">
				wechat_pay_page_switch = #{record.wechatPayPageSwitch,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.disb2b.model.Db2bDistSettingExample">
		delete from wx_t_db2b_dist_setting
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.disb2b.model.Db2bDistSettingExample" resultType="int">
		select count(1) from wx_t_db2b_dist_setting
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bDistSettingExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_db2b_dist_setting
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bDistSettingExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_db2b_dist_setting
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, o.id partner_id, isnull(t1.order_point_switch, 0) order_point_switch, t1.award_point_rate, t1.point_value, 
		isnull(t1.zfb_qrcode_switch, 0) zfb_qrcode_switch,
			 t1.zfb_qrcode_id, isnull(t1.wechat_qrcode_switch, 0) wechat_qrcode_switch, t1.wechat_qrcode_id, isnull(t1.ext_switch, 0) ext_switch, 
			 t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time, o.organization_name partner_name,
			 t1.app_id, t1.app_secret,isnull(t1.chevron_price_switch, 0) chevron_price_switch,isnull(t1.wechat_pay_page_switch, 0) wechat_pay_page_switch
		  from wx_t_organization o 
		  left join wx_t_db2b_dist_setting t1 on t1.partner_id=o.id
		 where (t1.id is null or t1.delete_flag=0) and o.type=1
		<if test="partnerId != null">
			and o.id = #{partnerId, jdbcType=BIGINT}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.disb2b.model.Db2bDistSettingParams">
		select t1.id, o.id partner_id, isnull(t1.order_point_switch, 0) order_point_switch, t1.award_point_rate, t1.point_value, 
		isnull(t1.zfb_qrcode_switch, 0) zfb_qrcode_switch,
			 t1.zfb_qrcode_id, isnull(t1.wechat_qrcode_switch, 0) wechat_qrcode_switch, t1.wechat_qrcode_id, isnull(t1.ext_switch, 0) ext_switch, 
			 t1.delete_flag, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time, o.organization_name partner_name,
			 t1.app_id, t1.app_secret,isnull(t1.chevron_price_switch, 0) chevron_price_switch,isnull(t1.wechat_pay_page_switch, 0) wechat_pay_page_switch
		  from wx_t_organization o 
		  left join wx_t_db2b_dist_setting t1 on t1.partner_id=o.id
		 where (t1.id is null or t1.delete_flag=0) and o.type=1
		<if test="partnerId != null">
			and o.id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="partnerName != null">
			and o.organization_name like '%'+#{partnerName}+'%'
		</if>
	</select>
</mapper>
