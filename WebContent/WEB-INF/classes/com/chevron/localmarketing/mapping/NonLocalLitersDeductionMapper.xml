<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.localmarketing.dao.NonLocalLitersDeductionMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.localmarketing.model.NonLocalLitersDeduction">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="punish_id" property="punishId" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="quarter" property="quarter" jdbcType="INTEGER"/>
		<result column="liters" property="liters" jdbcType="NUMERIC"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="delay_flag" property="delayFlag" jdbcType="INTEGER"/>
		<result column="status_snapshoot" property="statusSnapshoot" jdbcType="VARCHAR"/>
		<result column="effect_flag" property="effectFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="BIGINT"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="batch_no" property="batchNo" jdbcType="INTEGER"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,punish_id,create_user_id,create_time,sku,quarter,liters,ext_flag,ext_property1,ext_property2,ext_property3,status,
		delay_flag,status_snapshoot,effect_flag,delete_flag,update_user_id,update_time,batch_no
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeduction">
		update wx_t_non_local_liters_deduction set
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeduction">
		update wx_t_non_local_liters_deduction
		<set>
			<if test="punishId != null" >
				punish_id = #{punishId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="quarter != null" >
				quarter = #{quarter,jdbcType=INTEGER},
			</if>
			<if test="liters != null" >
				liters = #{liters,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="delayFlag != null" >
				delay_flag = #{delayFlag,jdbcType=INTEGER},
			</if>
			<if test="statusSnapshoot != null" >
				status_snapshoot = #{statusSnapshoot,jdbcType=VARCHAR},
			</if>
			<if test="effectFlag != null" >
				effect_flag = #{effectFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="batchNo != null" >
				batch_no = #{batchNo,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeductionExample">
    	delete from wx_t_non_local_liters_deduction
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeduction">
		insert into wx_t_non_local_liters_deduction
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="punishId != null">
				punish_id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="sku != null">
				sku,
			</if>
			<if test="quarter != null">
				quarter,
			</if>
			<if test="liters != null">
				liters,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="delayFlag != null">
				delay_flag,
			</if>
			<if test="statusSnapshoot != null">
				status_snapshoot,
			</if>
			<if test="effectFlag != null">
				effect_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="batchNo != null">
				batch_no,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="punishId != null">
				#{punishId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="quarter != null">
				#{quarter,jdbcType=INTEGER},
			</if>
			<if test="liters != null">
				#{liters,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="delayFlag != null">
				#{delayFlag,jdbcType=INTEGER},
			</if>
			<if test="statusSnapshoot != null">
				#{statusSnapshoot,jdbcType=VARCHAR},
			</if>
			<if test="effectFlag != null">
				#{effectFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="batchNo != null">
				#{batchNo,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_non_local_liters_deduction
		<set>
			<if test="record.punishId != null">
				punish_id = #{record.punishId,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.quarter != null">
				quarter = #{record.quarter,jdbcType=INTEGER},
			</if>
			<if test="record.liters != null">
				liters = #{record.liters,jdbcType=NUMERIC},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.delayFlag != null">
				delay_flag = #{record.delayFlag,jdbcType=INTEGER},
			</if>
			<if test="record.statusSnapshoot != null">
				status_snapshoot = #{record.statusSnapshoot,jdbcType=VARCHAR},
			</if>
			<if test="record.effectFlag != null">
				effect_flag = #{record.effectFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=BIGINT},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.batchNo != null">
				batch_no = #{record.batchNo,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeductionExample">
		delete from wx_t_non_local_liters_deduction
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeductionExample" resultType="int">
		select count(1) from wx_t_non_local_liters_deduction
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeductionExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_non_local_liters_deduction
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalLitersDeductionExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_non_local_liters_deduction
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 基础查询条件 -->
	<sql id="base_cond">
		<if test="punishId != null">
		and t1.punish_id=#{punishId,jdbcType=BIGINT}
		</if>
		<if test="extProperty1 != null and extProperty1 != ''">
		and t1.ext_property1=#{extProperty1}
		</if>
	</sql>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.punish_id, t1.create_user_id, t1.create_time, t1.sku, t1.quarter, t1.liters, t1.ext_flag,
			 t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.status, t1.delay_flag, t1.status_snapshoot,
			 t1.effect_flag, t1.delete_flag, t1.update_user_id, t1.update_time, t1.batch_no, p.name product_name, u.ch_name update_user_name
		  from wx_t_non_local_liters_deduction t1
		  left join wx_t_product p on p.sku=t1.sku
		  left join wx_t_user u on u.user_id=t1.update_user_id
		 where t1.delete_flag=0
		 <include refid="base_cond"/>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_non_local_liters_deduction (punish_id, create_user_id, create_time, sku, quarter, liters, ext_flag, ext_property1, ext_property2, ext_property3, status, delay_flag, status_snapshoot, effect_flag, delete_flag, update_user_id, update_time, batch_no) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.punishId, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.sku, jdbcType=VARCHAR}, #{item.quarter, jdbcType=INTEGER}, #{item.liters, jdbcType=NUMERIC}, #{item.extFlag, jdbcType=INTEGER}, #{item.extProperty1, jdbcType=VARCHAR}, #{item.extProperty2, jdbcType=VARCHAR}, #{item.extProperty3, jdbcType=VARCHAR}, #{item.status, jdbcType=INTEGER}, #{item.delayFlag, jdbcType=INTEGER}, #{item.statusSnapshoot, jdbcType=VARCHAR}, #{item.effectFlag, jdbcType=INTEGER}, #{item.deleteFlag, jdbcType=BIGINT}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}, #{item.batchNo, jdbcType=INTEGER}
			</trim>
		</foreach>
	</insert>
</mapper>
