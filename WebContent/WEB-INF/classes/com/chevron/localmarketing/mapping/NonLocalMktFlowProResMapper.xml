<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.localmarketing.dao.NonLocalMktFlowProResMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.localmarketing.model.NonLocalMktFlowProRes">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="flow_id" property="flowId" jdbcType="BIGINT"/>
		<result column="channel" property="channel" jdbcType="VARCHAR"/>
		<result column="setp_no" property="setpNo" jdbcType="VARCHAR"/>
		<result column="local_marketing_status" property="localMarketingStatus" jdbcType="INTEGER"/>
		<result column="handle_info" property="handleInfo" jdbcType="VARCHAR"/>
		<result column="punish_flag" property="punishFlag" jdbcType="INTEGER"/>
		<result column="deduct_bonus" property="deductBonus" jdbcType="NUMERIC"/>
		<result column="other_reasons" property="otherReasons" jdbcType="VARCHAR"/>
		<result column="kpi_result" property="kpiResult" jdbcType="INTEGER"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="attribute1" property="attribute1" jdbcType="VARCHAR"/>
		<result column="attribute2" property="attribute2" jdbcType="VARCHAR"/>
		<result column="attribute3" property="attribute3" jdbcType="VARCHAR"/>
		<result column="attribute4" property="attribute4" jdbcType="VARCHAR"/>
		<result column="attribute5" property="attribute5" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="attribute6" property="attribute6" jdbcType="VARCHAR"/>
		<result column="attribute7" property="attribute7" jdbcType="VARCHAR"/>
		<result column="attribute8" property="attribute8" jdbcType="VARCHAR"/>
		<result column="attribute9" property="attribute9" jdbcType="VARCHAR"/>
		<result column="attribute10" property="attribute10" jdbcType="VARCHAR"/>
		<result column="attribute11" property="attribute11" jdbcType="VARCHAR"/>
		<result column="attribute12" property="attribute12" jdbcType="VARCHAR"/>
		<result column="attribute13" property="attribute13" jdbcType="VARCHAR"/>
		<result column="attribute14" property="attribute14" jdbcType="VARCHAR"/>
		<result column="attribute15" property="attribute15" jdbcType="VARCHAR"/>
		<result column="attribute16" property="attribute16" jdbcType="VARCHAR"/>
		<result column="attribute17" property="attribute17" jdbcType="VARCHAR"/>
		<result column="attribute18" property="attribute18" jdbcType="VARCHAR"/>
		<result column="attribute19" property="attribute19" jdbcType="VARCHAR"/>
		<result column="punish_version" property="punishVersion" jdbcType="INTEGER"/>
		<result column="display_channel" property="displayChannel" jdbcType="VARCHAR"/>
		<result column="auth_channel" property="authChannel" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,flow_id,channel,setp_no,local_marketing_status,handle_info,punish_flag,deduct_bonus,other_reasons,kpi_result,
		ext_flag,attribute1,attribute2,attribute3,attribute4,attribute5,create_user_id,create_time,update_user_id,
		update_time,attribute6,attribute7,attribute8,attribute9,attribute10,attribute11,attribute12,attribute13,attribute14,
		attribute15,attribute16,attribute17,attribute18,attribute19,punish_version,display_channel,auth_channel
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
        <update id="updateForEditPage" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProRes">
		update wx_t_non_local_mkt_flow_pro_res
		set local_marketing_status = #{localMarketingStatus},
		    handle_info = #{handleInfo},
		    punish_flag = #{punishFlag},
		    deduct_bonus = #{deductBonus},
		    other_reasons = #{otherReasons},
		    kpi_result = #{kpiResult},
		    update_user_id = #{updateUserId},
		    update_time = #{updateTime},
            attribute1 = #{attribute1},
            attribute4 = #{attribute4},
            attribute5 = #{attribute5},
            attribute6 = #{attribute6},
            attribute7 = #{attribute7},
            attribute8 = #{attribute8},
            attribute9 = #{attribute9},
            attribute10 = #{attribute10},
            attribute11 = #{attribute11},
            attribute12 = #{attribute12},
            attribute13 = #{attribute13},
            attribute14 = #{attribute14},
            attribute15 = #{attribute15},
            attribute16 = #{attribute16},
            attribute17 = #{attribute17},
            attribute18 = #{attribute18},
            attribute19 = #{attribute19}
		where id = #{id}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProRes">
		update wx_t_non_local_mkt_flow_pro_res
		<set>
			<if test="flowId != null" >
				flow_id = #{flowId,jdbcType=BIGINT},
			</if>
			<if test="channel != null" >
				channel = #{channel,jdbcType=VARCHAR},
			</if>
			<if test="setpNo != null" >
				setp_no = #{setpNo,jdbcType=VARCHAR},
			</if>
			<if test="localMarketingStatus != null" >
				local_marketing_status = #{localMarketingStatus,jdbcType=INTEGER},
			</if>
			<if test="handleInfo != null" >
				handle_info = #{handleInfo,jdbcType=VARCHAR},
			</if>
			<if test="punishFlag != null" >
				punish_flag = #{punishFlag,jdbcType=INTEGER},
			</if>
			<if test="deductBonus != null" >
				deduct_bonus = #{deductBonus,jdbcType=NUMERIC},
			</if>
			<if test="otherReasons != null" >
				other_reasons = #{otherReasons,jdbcType=VARCHAR},
			</if>
			<if test="kpiResult != null" >
				kpi_result = #{kpiResult,jdbcType=INTEGER},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="attribute1 != null" >
				attribute1 = #{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null" >
				attribute2 = #{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null" >
				attribute3 = #{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="attribute4 != null" >
				attribute4 = #{attribute4,jdbcType=VARCHAR},
			</if>
			<if test="attribute5 != null" >
				attribute5 = #{attribute5,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="attribute6 != null" >
				attribute6 = #{attribute6,jdbcType=VARCHAR},
			</if>
			<if test="attribute7 != null" >
				attribute7 = #{attribute7,jdbcType=VARCHAR},
			</if>
			<if test="attribute8 != null" >
				attribute8 = #{attribute8,jdbcType=VARCHAR},
			</if>
			<if test="attribute9 != null" >
				attribute9 = #{attribute9,jdbcType=VARCHAR},
			</if>
			<if test="attribute10 != null" >
				attribute10 = #{attribute10,jdbcType=VARCHAR},
			</if>
			<if test="attribute11 != null" >
				attribute11 = #{attribute11,jdbcType=VARCHAR},
			</if>
			<if test="attribute12 != null" >
				attribute12 = #{attribute12,jdbcType=VARCHAR},
			</if>
			<if test="attribute13 != null" >
				attribute13 = #{attribute13,jdbcType=VARCHAR},
			</if>
			<if test="attribute14 != null" >
				attribute14 = #{attribute14,jdbcType=VARCHAR},
			</if>
			<if test="attribute15 != null" >
				attribute15 = #{attribute15,jdbcType=VARCHAR},
			</if>
			<if test="attribute16 != null" >
				attribute16 = #{attribute16,jdbcType=VARCHAR},
			</if>
			<if test="attribute17 != null" >
				attribute17 = #{attribute17,jdbcType=VARCHAR},
			</if>
			<if test="attribute18 != null" >
				attribute18 = #{attribute18,jdbcType=VARCHAR},
			</if>
			<if test="attribute19 != null" >
				attribute19 = #{attribute19,jdbcType=VARCHAR},
			</if>
			<if test="punishVersion != null" >
				punish_version = #{punishVersion,jdbcType=INTEGER},
			</if>
			<if test="displayChannel != null" >
				display_channel = #{displayChannel,jdbcType=VARCHAR},
			</if>
			<if test="authChannel != null" >
				auth_channel = #{authChannel,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProResExample">
    	delete from wx_t_non_local_mkt_flow_pro_res
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProRes" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_non_local_mkt_flow_pro_res
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="flowId != null">
				flow_id,
			</if>
			<if test="channel != null">
				channel,
			</if>
			<if test="setpNo != null">
				setp_no,
			</if>
			<if test="localMarketingStatus != null">
				local_marketing_status,
			</if>
			<if test="handleInfo != null">
				handle_info,
			</if>
			<if test="punishFlag != null">
				punish_flag,
			</if>
			<if test="deductBonus != null">
				deduct_bonus,
			</if>
			<if test="otherReasons != null">
				other_reasons,
			</if>
			<if test="kpiResult != null">
				kpi_result,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="attribute1 != null">
				attribute1,
			</if>
			<if test="attribute2 != null">
				attribute2,
			</if>
			<if test="attribute3 != null">
				attribute3,
			</if>
			<if test="attribute4 != null">
				attribute4,
			</if>
			<if test="attribute5 != null">
				attribute5,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="attribute6 != null">
				attribute6,
			</if>
			<if test="attribute7 != null">
				attribute7,
			</if>
			<if test="attribute8 != null">
				attribute8,
			</if>
			<if test="attribute9 != null">
				attribute9,
			</if>
			<if test="attribute10 != null">
				attribute10,
			</if>
			<if test="attribute11 != null">
				attribute11,
			</if>
			<if test="attribute12 != null">
				attribute12,
			</if>
			<if test="attribute13 != null">
				attribute13,
			</if>
			<if test="attribute14 != null">
				attribute14,
			</if>
			<if test="attribute15 != null">
				attribute15,
			</if>
			<if test="attribute16 != null">
				attribute16,
			</if>
			<if test="attribute17 != null">
				attribute17,
			</if>
			<if test="attribute18 != null">
				attribute18,
			</if>
			<if test="attribute19 != null">
				attribute19,
			</if>
			<if test="punishVersion != null">
				punish_version,
			</if>
			<if test="displayChannel != null">
				display_channel,
			</if>
			<if test="authChannel != null">
				auth_channel,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="flowId != null">
				#{flowId,jdbcType=BIGINT},
			</if>
			<if test="channel != null">
				#{channel,jdbcType=VARCHAR},
			</if>
			<if test="setpNo != null">
				#{setpNo,jdbcType=VARCHAR},
			</if>
			<if test="localMarketingStatus != null">
				#{localMarketingStatus,jdbcType=INTEGER},
			</if>
			<if test="handleInfo != null">
				#{handleInfo,jdbcType=VARCHAR},
			</if>
			<if test="punishFlag != null">
				#{punishFlag,jdbcType=INTEGER},
			</if>
			<if test="deductBonus != null">
				#{deductBonus,jdbcType=NUMERIC},
			</if>
			<if test="otherReasons != null">
				#{otherReasons,jdbcType=VARCHAR},
			</if>
			<if test="kpiResult != null">
				#{kpiResult,jdbcType=INTEGER},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="attribute1 != null">
				#{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null">
				#{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null">
				#{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="attribute4 != null">
				#{attribute4,jdbcType=VARCHAR},
			</if>
			<if test="attribute5 != null">
				#{attribute5,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="attribute6 != null">
				#{attribute6,jdbcType=VARCHAR},
			</if>
			<if test="attribute7 != null">
				#{attribute7,jdbcType=VARCHAR},
			</if>
			<if test="attribute8 != null">
				#{attribute8,jdbcType=VARCHAR},
			</if>
			<if test="attribute9 != null">
				#{attribute9,jdbcType=VARCHAR},
			</if>
			<if test="attribute10 != null">
				#{attribute10,jdbcType=VARCHAR},
			</if>
			<if test="attribute11 != null">
				#{attribute11,jdbcType=VARCHAR},
			</if>
			<if test="attribute12 != null">
				#{attribute12,jdbcType=VARCHAR},
			</if>
			<if test="attribute13 != null">
				#{attribute13,jdbcType=VARCHAR},
			</if>
			<if test="attribute14 != null">
				#{attribute14,jdbcType=VARCHAR},
			</if>
			<if test="attribute15 != null">
				#{attribute15,jdbcType=VARCHAR},
			</if>
			<if test="attribute16 != null">
				#{attribute16,jdbcType=VARCHAR},
			</if>
			<if test="attribute17 != null">
				#{attribute17,jdbcType=VARCHAR},
			</if>
			<if test="attribute18 != null">
				#{attribute18,jdbcType=VARCHAR},
			</if>
			<if test="attribute19 != null">
				#{attribute19,jdbcType=VARCHAR},
			</if>
			<if test="punishVersion != null">
				#{punishVersion,jdbcType=INTEGER},
			</if>
			<if test="displayChannel != null">
				#{displayChannel,jdbcType=VARCHAR},
			</if>
			<if test="authChannel != null">
				#{authChannel,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_non_local_mkt_flow_pro_res
		<set>
			<if test="record.flowId != null">
				flow_id = #{record.flowId,jdbcType=BIGINT},
			</if>
			<if test="record.channel != null">
				channel = #{record.channel,jdbcType=VARCHAR},
			</if>
			<if test="record.setpNo != null">
				setp_no = #{record.setpNo,jdbcType=VARCHAR},
			</if>
			<if test="record.localMarketingStatus != null">
				local_marketing_status = #{record.localMarketingStatus,jdbcType=INTEGER},
			</if>
			<if test="record.handleInfo != null">
				handle_info = #{record.handleInfo,jdbcType=VARCHAR},
			</if>
			<if test="record.punishFlag != null">
				punish_flag = #{record.punishFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deductBonus != null">
				deduct_bonus = #{record.deductBonus,jdbcType=NUMERIC},
			</if>
			<if test="record.otherReasons != null">
				other_reasons = #{record.otherReasons,jdbcType=VARCHAR},
			</if>
			<if test="record.kpiResult != null">
				kpi_result = #{record.kpiResult,jdbcType=INTEGER},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.attribute1 != null">
				attribute1 = #{record.attribute1,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute2 != null">
				attribute2 = #{record.attribute2,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute3 != null">
				attribute3 = #{record.attribute3,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute4 != null">
				attribute4 = #{record.attribute4,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute5 != null">
				attribute5 = #{record.attribute5,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.attribute6 != null">
				attribute6 = #{record.attribute6,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute7 != null">
				attribute7 = #{record.attribute7,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute8 != null">
				attribute8 = #{record.attribute8,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute9 != null">
				attribute9 = #{record.attribute9,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute10 != null">
				attribute10 = #{record.attribute10,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute11 != null">
				attribute11 = #{record.attribute11,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute12 != null">
				attribute12 = #{record.attribute12,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute13 != null">
				attribute13 = #{record.attribute13,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute14 != null">
				attribute14 = #{record.attribute14,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute15 != null">
				attribute15 = #{record.attribute15,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute16 != null">
				attribute16 = #{record.attribute16,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute17 != null">
				attribute17 = #{record.attribute17,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute18 != null">
				attribute18 = #{record.attribute18,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute19 != null">
				attribute19 = #{record.attribute19,jdbcType=VARCHAR},
			</if>
			<if test="record.punishVersion != null">
				punish_version = #{record.punishVersion,jdbcType=INTEGER},
			</if>
			<if test="record.displayChannel != null">
				display_channel = #{record.displayChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.authChannel != null">
				auth_channel = #{record.authChannel,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProResExample">
		delete from wx_t_non_local_mkt_flow_pro_res
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProResExample" resultType="int">
		select count(1) from wx_t_non_local_mkt_flow_pro_res
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProResExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_non_local_mkt_flow_pro_res
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktFlowProResExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_non_local_mkt_flow_pro_res
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.flow_id, t1.channel, t1.setp_no, t1.local_marketing_status, t1.handle_info, t1.punish_flag,
			 t1.deduct_bonus, t1.other_reasons, t1.kpi_result, t1.ext_flag, t1.attribute1, t1.attribute2, t1.attribute3,
			 t1.attribute4, t1.attribute5, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.attribute6,
			 t1.attribute7, t1.attribute8, t1.attribute9, t1.attribute10, t1.attribute11, t1.attribute12, t1.attribute13,
			 t1.attribute14, t1.attribute15, t1.attribute16, t1.attribute17, t1.attribute18, t1.attribute19, t1.punish_version
		  from wx_t_non_local_mkt_flow_pro_res t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_non_local_mkt_flow_pro_res (flow_id, channel, setp_no, local_marketing_status, handle_info, punish_flag, deduct_bonus, other_reasons, kpi_result, ext_flag, attribute1, attribute2, attribute3, attribute4, attribute5, create_user_id, create_time, update_user_id, update_time, attribute6, attribute7, attribute8, attribute9, attribute10, attribute11, attribute12, attribute13, attribute14, attribute15, attribute16, attribute17, attribute18, attribute19, punish_version) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.flowId, jdbcType=BIGINT}, #{item.channel, jdbcType=VARCHAR}, #{item.setpNo, jdbcType=VARCHAR}, #{item.localMarketingStatus, jdbcType=INTEGER}, #{item.handleInfo, jdbcType=VARCHAR}, #{item.punishFlag, jdbcType=INTEGER}, #{item.deductBonus, jdbcType=NUMERIC}, #{item.otherReasons, jdbcType=VARCHAR}, #{item.kpiResult, jdbcType=INTEGER}, #{item.extFlag, jdbcType=INTEGER}, #{item.attribute1, jdbcType=VARCHAR}, #{item.attribute2, jdbcType=VARCHAR}, #{item.attribute3, jdbcType=VARCHAR}, #{item.attribute4, jdbcType=VARCHAR}, #{item.attribute5, jdbcType=VARCHAR}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}, #{item.attribute6, jdbcType=VARCHAR}, #{item.attribute7, jdbcType=VARCHAR}, #{item.attribute8, jdbcType=VARCHAR}, #{item.attribute9, jdbcType=VARCHAR}, #{item.attribute10, jdbcType=VARCHAR}, #{item.attribute11, jdbcType=VARCHAR}, #{item.attribute12, jdbcType=VARCHAR}, #{item.attribute13, jdbcType=VARCHAR}, #{item.attribute14, jdbcType=VARCHAR}, #{item.attribute15, jdbcType=VARCHAR}, #{item.attribute16, jdbcType=VARCHAR}, #{item.attribute17, jdbcType=VARCHAR}, #{item.attribute18, jdbcType=VARCHAR}, #{item.attribute19, jdbcType=VARCHAR}, #{item.punishVersion, jdbcType=INTEGER}
			</trim>
		</foreach>
	</insert>
</mapper>
