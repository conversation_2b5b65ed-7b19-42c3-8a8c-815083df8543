<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.localmarketing.dao.NonLocalMktEmaileInfoMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.localmarketing.model.NonLocalMktEmaileInfo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="flow_id" property="flowId" jdbcType="BIGINT"/>
		<result column="source_id" property="sourceId" jdbcType="BIGINT"/>
		<result column="from_distributor_id" property="fromDistributorId" jdbcType="BIGINT"/>
		<result column="customer_name_cn" property="customerNameCn" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="channel" property="channel" jdbcType="VARCHAR"/>
		<result column="warn_info" property="warnInfo" jdbcType="VARCHAR"/>
		<result column="warn_month" property="warnMonth" jdbcType="VARCHAR"/>
		<result column="warn_year" property="warnYear" jdbcType="VARCHAR"/>
		<result column="deliver_status" property="deliverStatus" jdbcType="VARCHAR"/>
		<result column="deliver_time" property="deliverTime" jdbcType="TIMESTAMP"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="BIGINT"/>
		<result column="attribute1" property="attribute1" jdbcType="VARCHAR"/>
		<result column="attribute2" property="attribute2" jdbcType="VARCHAR"/>
		<result column="attribute3" property="attribute3" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="asm_comment" property="abmComment" jdbcType="VARCHAR"/>
		<result column="channel_text" property="channelText" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,create_user_id,create_time,update_user_id,update_time,flow_id,source_id,from_distributor_id,region_name,channel,
		warn_info,warn_month,warn_year,deliver_status,deliver_time,ext_flag,attribute1,attribute2,attribute3,delete_flag
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfo">
		update wx_t_non_local_mkt_emaile_info set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfo">
		update wx_t_non_local_mkt_emaile_info
		<set>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="flowId != null" >
				flow_id = #{flowId,jdbcType=BIGINT},
			</if>
			<if test="sourceId != null" >
				source_id = #{sourceId,jdbcType=BIGINT},
			</if>
			<if test="fromDistributorId != null" >
				from_distributor_id = #{fromDistributorId,jdbcType=BIGINT},
			</if>
			<if test="regionName != null" >
				region_name = #{regionName,jdbcType=VARCHAR},
			</if>
			<if test="channel != null" >
				channel = #{channel,jdbcType=VARCHAR},
			</if>
			<if test="warnInfo != null" >
				warn_info = #{warnInfo,jdbcType=VARCHAR},
			</if>
			<if test="warnMonth != null" >
				warn_month = #{warnMonth,jdbcType=VARCHAR},
			</if>
			<if test="warnYear != null" >
				warn_year = #{warnYear,jdbcType=VARCHAR},
			</if>
			<if test="deliverStatus != null" >
				deliver_status = #{deliverStatus,jdbcType=VARCHAR},
			</if>
			<if test="deliverTime != null" >
				deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="attribute1 != null" >
				attribute1 = #{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null" >
				attribute2 = #{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null" >
				attribute3 = #{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=BIGINT},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoExample">
    	delete from wx_t_non_local_mkt_emaile_info
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfo" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_non_local_mkt_emaile_info
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="remark != null">
				remark,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="flowId != null">
				flow_id,
			</if>
			<if test="sourceId != null">
				source_id,
			</if>
			<if test="fromDistributorId != null">
				from_distributor_id,
			</if>
			<if test="regionName != null">
				region_name,
			</if>
			<if test="channel != null">
				channel,
			</if>
			<if test="warnInfo != null">
				warn_info,
			</if>
			<if test="warnMonth != null">
				warn_month,
			</if>
			<if test="warnYear != null">
				warn_year,
			</if>
			<if test="deliverStatus != null">
				deliver_status,
			</if>
			<if test="deliverTime != null">
				deliver_time,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="attribute1 != null">
				attribute1,
			</if>
			<if test="attribute2 != null">
				attribute2,
			</if>
			<if test="attribute3 != null">
				attribute3,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="flowId != null">
				#{flowId,jdbcType=BIGINT},
			</if>
			<if test="sourceId != null">
				#{sourceId,jdbcType=BIGINT},
			</if>
			<if test="fromDistributorId != null">
				#{fromDistributorId,jdbcType=BIGINT},
			</if>
			<if test="regionName != null">
				#{regionName,jdbcType=VARCHAR},
			</if>
			<if test="channel != null">
				#{channel,jdbcType=VARCHAR},
			</if>
			<if test="warnInfo != null">
				#{warnInfo,jdbcType=VARCHAR},
			</if>
			<if test="warnMonth != null">
				#{warnMonth,jdbcType=VARCHAR},
			</if>
			<if test="warnYear != null">
				#{warnYear,jdbcType=VARCHAR},
			</if>
			<if test="deliverStatus != null">
				#{deliverStatus,jdbcType=VARCHAR},
			</if>
			<if test="deliverTime != null">
				#{deliverTime,jdbcType=TIMESTAMP},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="attribute1 != null">
				#{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null">
				#{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null">
				#{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_non_local_mkt_emaile_info
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.flowId != null">
				flow_id = #{record.flowId,jdbcType=BIGINT},
			</if>
			<if test="record.sourceId != null">
				source_id = #{record.sourceId,jdbcType=BIGINT},
			</if>
			<if test="record.fromDistributorId != null">
				from_distributor_id = #{record.fromDistributorId,jdbcType=BIGINT},
			</if>
			<if test="record.regionName != null">
				region_name = #{record.regionName,jdbcType=VARCHAR},
			</if>
			<if test="record.channel != null">
				channel = #{record.channel,jdbcType=VARCHAR},
			</if>
			<if test="record.warnInfo != null">
				warn_info = #{record.warnInfo,jdbcType=VARCHAR},
			</if>
			<if test="record.warnMonth != null">
				warn_month = #{record.warnMonth,jdbcType=VARCHAR},
			</if>
			<if test="record.warnYear != null">
				warn_year = #{record.warnYear,jdbcType=VARCHAR},
			</if>
			<if test="record.deliverStatus != null">
				deliver_status = #{record.deliverStatus,jdbcType=VARCHAR},
			</if>
			<if test="record.deliverTime != null">
				deliver_time = #{record.deliverTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.attribute1 != null">
				attribute1 = #{record.attribute1,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute2 != null">
				attribute2 = #{record.attribute2,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute3 != null">
				attribute3 = #{record.attribute3,jdbcType=VARCHAR},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoExample">
		delete from wx_t_non_local_mkt_emaile_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoExample" resultType="int">
		select count(1) from wx_t_non_local_mkt_emaile_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_non_local_mkt_emaile_info
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_non_local_mkt_emaile_info
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.flow_id, t1.source_id,
			 t1.from_distributor_id, t1.region_name, t1.channel, t1.warn_info, t1.warn_month, t1.warn_year, t1.deliver_status,
			 t1.deliver_time, t1.ext_flag, t1.attribute1, t1.attribute2, t1.attribute3,t1.delete_flag
		  from wx_t_non_local_mkt_emaile_info t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoParams">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.flow_id, t1.source_id,
			 t1.from_distributor_id, t1.region_name, t1.channel, t1.warn_info, t1.warn_month, t1.warn_year, t1.deliver_status,
			 t1.deliver_time, t1.ext_flag, t1.attribute1, t1.attribute2, t1.attribute3,t1.delete_flag
		  from wx_t_non_local_mkt_emaile_info t1
		 where t1.delete_flag=0
	</select>
	
	<select id="getLocalMktEmailInfoByCondition" resultMap="BaseResultMap" parameterType="com.chevron.localmarketing.model.NonLocalMktEmaileInfoVo">
	
		SELECT DISTINCT
			t1.id,t1.remark, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.flow_id, t1.source_id,
			 t1.from_distributor_id, t1.delete_flag,	
			 CASE WHEN t1.from_distributor_id = '45' THEN 'GQ'
			 WHEN t1.from_distributor_id = '-10000' THEN 'OEM'
			 ELSE tr.value_after_transform END AS region_name, t1.channel, t1.warn_info, t1.warn_month, t1.warn_year, t1.deliver_status,
			 t1.deliver_time, t1.ext_flag, t1.attribute1, t1.attribute2, t1.attribute3, diac1.dic_item_name channel_text,
			CASE WHEN t1.from_distributor_id = '45' THEN 'GQ'
			WHEN t1.from_distributor_id = '-10000' THEN 'OEM'
			ELSE cos.customer_name_cn END AS customer_name_cn, r3.handle_info asm_comment
		FROM
			wx_t_non_local_mkt_emaile_info t1
		LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON t1.from_distributor_id = cos.distributor_id
			LEFT JOIN wx_t_value_transform_map tr ON cos.region = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'
		left join wx_t_non_local_mkt_flow_form f on f.id  = t1.flow_id
		left join wx_t_non_local_mkt_flow_pro_res r3 on r3.flow_id = f.id  and r3.setp_no  = 'ABM_CONFIRM'			
	  left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=t1.channel
		WHERE
			1 = 1
			AND t1.delete_flag = 0
			<if test="regionName != null">
				<choose>
					<when test="regionName == 'GQ'">
						AND t1.from_distributor_id = 45
					</when>
					<when test="regionName == 'OEM'">
						AND t1.from_distributor_id = -10000
					</when>
					<otherwise>
					    AND cos.region = #{regionName}
					</otherwise>
				</choose>
			</if>
			<if test="attribute2 != null">AND t1.attribute2 = #{attribute2} </if>
			<if test="fromDistributorId != null">AND t1.from_distributor_id = #{fromDistributorId}</if>
			<if test="channel != null">AND t1.channel = #{channel}</if>
			<if test="deliverStatus != null">AND t1.deliver_status = #{deliverStatus}</if>
			<if test="warnMonth != null">AND t1.warn_month = #{warnMonth}</if>
			<if test="warnYear != null ">AND t1.warn_year = #{warnYear}</if>
			<if test="startDeliverTime != null">AND t1.deliver_time &gt;= #{startDeliverTime}</if>
			<if test="endDeliverTime != null">AND t1.deliver_time &lt; #{endDeliverTime}</if>
			<if test="otherCustomer != null">
				<choose>
					<when test="otherCustomer == 1">
						AND t1.from_distributor_id not in (45,-10000)
					</when>
				</choose>
			</if>
	</select>
	
	<select id="getCCEmailByFlowId" resultType="String" parameterType="long"> 
	select substring((
	   SELECT ';'+xx0.to_email +';' +xx0.from_email FROM (SELECT
			DISTINCT 
			case when (ff.to_distributor_id = -10000 or ff.to_distributor_id  = 45) 
			then ISNULL((SELECT  top 1 dic_item_name FROM  wx_t_dic_item where dic_type_code = 'local_marketing_warning_email_special_asm'),'-') 
			ELSE ISNULL(u.email,'-') END to_email,
			case when (ff.from_distributor_id = -10000 or ff.from_distributor_id  = 45) 
			then ISNULL((SELECT  top 1 dic_item_name FROM  wx_t_dic_item where dic_type_code = 'local_marketing_warning_email_special_asm'),'-') 
			ELSE ISNULL(u1.email,'-') END from_email
			<!-- ISNULL(u.email,'-') as to_email,ISNULL(u1.email,'-') as from_email -->
		FROM
			wx_t_non_local_mkt_flow_form ff 
		LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos ON ff.to_distributor_id = cos.distributor_id
		LEFT JOIN wx_t_user u ON cos.supervisor_cai = u.cai
		LEFT JOIN PP_MID.dbo.syn_dw_to_pp_customer_org_sales cos1 ON ff.from_distributor_id = cos1.distributor_id
		LEFT JOIN wx_t_user u1 ON cos1.supervisor_cai = u1.cai
		WHERE
			ff.id = #{flowId}) xx0 FOR XML PATH,TYPE).value('.','varchar(max)'),2,100000) AS emails
	</select>
    
    <resultMap id="CalendarResultMap" type="com.chevron.localmarketing.model.calendarDate">
		<result column="date" property="date" jdbcType="TIMESTAMP"/>
		<result column="isworkday_cn" property="isWorkDayCn" jdbcType="INTEGER"/>
	</resultMap>
    
    <select id="getTowWeekDay" resultMap="CalendarResultMap" parameterType="map">
    	SELECT
			[date],isworkday_cn
		FROM
			[PP_MID].[dbo].[syn_dw_to_pp_calendar_date]
		WHERE 1=1
		<if test="year != null"> AND	YEAR = #{year}</if>
		<if test="month != null"> AND [month] = #{month}</if>
		<if test="weekName != null ">AND week_name = #{weekName}</if>
		<if test="date != null">AND [date] >= #{date} AND isworkday_cn = 1 </if>
		ORDER BY
			[DATE]
    
    </select>
</mapper>
