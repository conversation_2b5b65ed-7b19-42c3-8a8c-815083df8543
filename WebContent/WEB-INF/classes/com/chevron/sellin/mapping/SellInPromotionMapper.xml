<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.sellin.dao.SellInPromotionMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.sellin.model.SellInPromotion">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
		<result column="start_date" property="startDate" jdbcType="DATE"/>
		<result column="end_date" property="endDate" jdbcType="DATE"/>
		<result column="priority_level" property="priorityLevel" jdbcType="INTEGER"/>
		<result column="enable_flag" property="enableFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="source_id" property="sourceId" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="product_channel" property="productChannel" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="post_year_month_start" property="postStartDate" jdbcType="DATE"/>
		<result column="post_year_month_end" property="postEndDate" jdbcType="DATE"/>
		<result column="att_id" property="attId" jdbcType="BIGINT"/>

		<collection property="promotionRuleList" ofType="com.chevron.sellin.model.PromotionRule" javaType="java.util.List">
        <result column="promotion_id1" jdbcType="BIGINT" property="promotionId" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
        <result column="rule_config" jdbcType="VARCHAR" property="ruleConfig" />
        </collection>
		
		
		<collection property="promotionPartnerList" ofType="com.chevron.sellin.model.PromotionPartner" javaType="java.util.List">
        <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
        <result column="promotion_id" jdbcType="BIGINT" property="promotionId" />
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName" />
        <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
       </collection>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,promotion_name,start_date,end_date,priority_level,enable_flag,delete_flag,source_id,create_user_id,create_time,
		update_user_id,update_time,product_channel,status,post_year_month_start,post_year_month_end
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.sellin.model.SellInPromotion">
		update wx_t_sell_in_promotion set
				promotion_name = #{promotionName,jdbcType=VARCHAR},
				start_date = #{startDate,jdbcType=DATE},
				end_date = #{endDate,jdbcType=DATE},
				priority_level = #{priorityLevel,jdbcType=INTEGER},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.sellin.model.SellInPromotion">
		update wx_t_sell_in_promotion
		<set>
			<if test="promotionName != null" >
				promotion_name = #{promotionName,jdbcType=VARCHAR},
			</if>
			<if test="startDate != null" >
				start_date = #{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null" >
				end_date = #{endDate,jdbcType=DATE},
			</if>
			<if test="priorityLevel != null" >
				priority_level = #{priorityLevel,jdbcType=INTEGER},
			</if>
			<if test="enableFlag != null" >
				enable_flag = #{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="sourceId != null" >
				source_id = #{sourceId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="productChannel != null" >
				product_channel = #{productChannel,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="postStartDate != null" >
				post_year_month_start = #{postStartDate,jdbcType=DATE},
			</if>
			<if test="postEndDate != null" >
				post_year_month_end = #{postEndDate,jdbcType=DATE},
			</if>

		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.sellin.model.SellInPromotionExample">
    	delete from wx_t_sell_in_promotion
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.sellin.model.SellInPromotion" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_sell_in_promotion
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="promotionName != null">
				promotion_name,
			</if>
			<if test="startDate != null">
				start_date,
			</if>
			<if test="endDate != null">
				end_date,
			</if>
			<if test="priorityLevel != null">
				priority_level,
			</if>
			<if test="enableFlag != null">
				enable_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="sourceId != null">
				source_id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="productChannel != null">
				product_channel,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="postStartDate != null">
				post_year_month_start,
			</if>
			<if test="postEndDate != null">
				post_year_month_end,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="promotionName != null">
				#{promotionName,jdbcType=VARCHAR},
			</if>
			<if test="startDate != null">
				#{startDate,jdbcType=DATE},
			</if>
			<if test="endDate != null">
				#{endDate,jdbcType=DATE},
			</if>
			<if test="priorityLevel != null">
				#{priorityLevel,jdbcType=INTEGER},
			</if>
			<if test="enableFlag != null">
				#{enableFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="sourceId != null">
				#{sourceId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="productChannel != null">
				#{productChannel,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="postStartDate != null">
				#{postStartDate,jdbcType=DATE},
			</if>
			<if test="postEndDate != null">
				#{postEndDate,jdbcType=DATE},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_sell_in_promotion
		<set>
			<if test="record.promotionName != null">
				promotion_name = #{record.promotionName,jdbcType=VARCHAR},
			</if>
			<if test="record.startDate != null">
				start_date = #{record.startDate,jdbcType=DATE},
			</if>
			<if test="record.endDate != null">
				end_date = #{record.endDate,jdbcType=DATE},
			</if>
			<if test="record.priorityLevel != null">
				priority_level = #{record.priorityLevel,jdbcType=INTEGER},
			</if>
			<if test="record.enableFlag != null">
				enable_flag = #{record.enableFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.sourceId != null">
				source_id = #{record.sourceId,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.productChannel != null">
				product_channel = #{record.productChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.postStartDate != null">
				post_year_month_start = #{record.postStartDate,jdbcType=DATE},
			</if>
			<if test="record.postEndDate != null">
				post_year_month_end = #{record.postEndDate,jdbcType=DATE},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.sellin.model.SellInPromotionExample">
		delete from wx_t_sell_in_promotion
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.sellin.model.SellInPromotionExample" resultType="int">
		select count(1) from wx_t_sell_in_promotion
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_sell_in_promotion
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionExample">
    	select
<!-- 		<include refid="Base_Column_List"/>
 -->		t.id,t.promotion_name,t.start_date,t.end_date,t.priority_level,t.enable_flag,t.delete_flag,t.source_id
		,t1.partner_id,t1.promotion_id,org.organization_name as partner_name,t.product_channel, t.status,
			 t.post_year_month_start, t.post_year_month_end
		from wx_t_sell_in_promotion t
		left join wx_t_promotion_partner t1 on t.id=t1.promotion_id
        left join wx_t_organization org on t1.partner_id = org.id
		where t.id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.promotion_name, t1.start_date, t1.end_date, t1.priority_level, t1.enable_flag, t1.delete_flag,
			 t1.source_id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.product_channel, t1.status,
			 t1.post_year_month_start, t1.post_year_month_end
		  from wx_t_sell_in_promotion t1
		 where 1=1
		<if test="promotionName != null and promotionName != ''">
			and t1.promotion_name like '%' + #{promotionName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="deleteFlag != null and deleteFlag != '' or deleteFlag == 0">
			and t1.delete_flag = #{deleteFlag}
		</if>
	</select>
	
	<select id="countByParams" resultType="int" parameterType="map">
		select count(1)
		  from wx_t_sell_in_promotion t1
		 where t1.delete_flag=0
		<if test="promotionName != null and promotionName != ''">
			and t1.promotion_name like '%' + #{promotionName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="status != null">
			and t1.status = #{status, jdbcType=INTEGER}
		</if>
		<if test="partnerId != null">
			and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=t1.id)
			or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=#{partnerId, jdbcType=BIGINT} and pp.promotion_id=t1.id)) 
		</if>
		<if test="pricingDate != null">
			and (t1.start_date is null or t1.start_date &lt;= #{pricingDate,jdbcType=DATE}) and (t1.end_date is null or t1.end_date>=#{pricingDate,jdbcType=DATE})
		</if>
		<if test="productChannel != null and productChannel != ''">
			and t1.product_channel=#{productChannel}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionParams">
		select t1.id, t1.promotion_name, t1.start_date, t1.end_date, t1.priority_level, t1.enable_flag, t1.delete_flag,
			 t1.source_id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.product_channel, t1.status,
			 t1.post_year_month_start, t1.post_year_month_end,(select top 1 f.att_id from wx_att_file f where f.source_type='47' and f.source_id=t1.id order by att_id asc) att_id
		  from wx_t_sell_in_promotion t1
		 where t1.delete_flag=0
		<if test="promotionName != null and promotionName != ''">
			and t1.promotion_name like '%' + #{promotionName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="enableFlag != null">
			and t1.enable_flag = #{enableFlag, jdbcType=INTEGER}
		</if>
		<if test="activeFlag == 1">
			and (t1.start_date is null or t1.start_date &lt;= getdate()) and (t1.end_date is null or dateadd(day, 1, t1.end_date)>getdate())
			and t1.enable_flag=1 and t1.status=10
		</if>
		<if test="partnerId != null">
		and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=t1.id)
			or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=#{partnerId, jdbcType=BIGINT} and pp.promotion_id=t1.id)) 
		</if>
	</select>
	
	<insert id="insertPromotionRules" parameterType="java.util.List">
		INSERT INTO wx_t_sell_in_promotion_rule  ( promotion_id, sku, rule_type, rule_config , ext_property1 , ext_property2, ext_property3, ext_property4, ext_property5, ext_property6, ext_property7, ext_property8, ext_property9) 
		VALUES 
		<foreach collection="promotionRuleList" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.promotionId},
				#{item.sku},
				#{item.ruleType},
				#{item.ruleConfig},
				#{item.extProperty1},
				#{item.extProperty2},
				#{item.extProperty3},
				#{item.extProperty4},
				#{item.extProperty5},
				#{item.extProperty6},
				#{item.extProperty7},
				#{item.extProperty8},
				#{item.extProperty9}
			</trim>
		</foreach>
	</insert>
	
	<insert id="insertPromotionPartner" parameterType="java.util.List">
	INSERT INTO wx_t_promotion_partner (partner_id,promotion_id) VALUES
		<foreach collection="PromotionPartnerList" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.partnerId},#{item.promotionId}
			</trim>
		</foreach>
	</insert>
	
	<select id="querySynData" resultMap="BaseResultMap" parameterType="map">
		select tt_p.id,tt_p.source_id,tt_p.promotion_name,tt_p.start_date,tt_p.end_date,tt_p.priority_level,tt_p.enable_flag,tt_p.delete_flag,
		tt_r.promotion_id promotion_id1,tt_r.sku,tt_r.rule_type,tt_r.rule_config, tt_p.product_channel,tt_p.post_year_month_start,
		tt_p.post_year_month_end
		FROM wx_t_sell_in_promotion tt_p
		LEFT JOIN wx_t_sell_in_promotion_rule tt_r ON tt_p.id = tt_r.promotion_id
		where tt_p.delete_flag = 0
		AND tt_p.enable_flag = 1
		and tt_p.status=10
		and not exists (
			select 1 from [PP_MID].cmt.campaign c where c.campaign_code=isnull(tt_p.source_id, tt_p.id)
		)
		<if test="lastSynDate != null and lastSynDate != ''">
			AND tt_p.create_time &gt;= '${lastSynDate}'
		</if>
	</select>
	
	<insert id="insertCampaign"  parameterType="com.chevron.sellin.model.SellInPromotion" useGeneratedKeys="true" keyProperty="campaignId">
		INSERT INTO [PP_MID].cmt.campaign ( [campaign_code], [campaign_name], [campaign_type_id], [effective_from_date], [effective_to_date], [post_year_month_from], [post_year_month_to], [create_by], [create_time], [update_by], [update_time], [active_flag], [priority]) 
		VALUES ( #{id,jdbcType=BIGINT}, #{promotionName,jdbcType=VARCHAR}, #{campaignTypeId,jdbcType=BIGINT}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{postStartDate,jdbcType=DATE}, #{postEndDate,jdbcType=DATE}, '0', GETDATE(), '0', GETDATE(), 
		'Y',
		CASE WHEN #{priorityLevel} is null THEN '1'
		ELSE #{priorityLevel} END 
		);
	</insert>
	
	<insert id="insertCampaignCustomer" parameterType="map">
		INSERT INTO [PP_MID].cmt.campaign_customer_rel([campaign_id], [distributor_id], [product_channel], [create_by], [create_time], [update_by], [update_time], [active_flag]) 
select c.campaign_id, pe.distributor_id, sp.product_channel,'0',GETDATE(), '0', GETDATE(), 'Y'
from wx_t_promotion_partner pp 
left join wx_t_sell_in_promotion sp on pp.promotion_id=sp.id 
left join [PP_MID].cmt.campaign c on c.campaign_code=isnull(sp.source_id, sp.id)
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=pp.partner_id
where sp.enable_flag=1 and sp.delete_flag=0 and c.campaign_id=#{campaignId}
		<if test="lastSynDate != null and lastSynDate != ''">
			AND sp.create_time &gt;= '${lastSynDate}'
		</if>
and not exists (select 1 from [PP_MID].cmt.campaign_customer_rel ccr where ccr.campaign_id=c.campaign_id and ccr.distributor_id=pe.distributor_id)
		<!-- ,<foreach collection="records" item="item" separator="," >
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.campaignId},#{item.distributorId},#{item.productChannel},'0',GETDATE(), '0', GETDATE(), CASE WHEN #{item.enableFlag} = '1'  THEN 'Y' ELSE 'N' END,
			</trim>
			<!- ,
			<trim prefix="(" suffix=")" suffixOverrides=",">
				#{item.campaignId},#{item.distributorId},'Commercial','0',GETDATE(), '0', GETDATE(), CASE WHEN #{item.enableFlag} = '1'  THEN 'Y' ELSE 'N' END,
			</trim> ->
		</foreach> -->
	</insert>
	
	<insert id="insertOffer" parameterType="com.chevron.sellin.model.PromotionOfferVo" useGeneratedKeys="true" keyProperty="offerId">
		INSERT INTO [PP_MID].cmt.offer ( [campaign_id], [what_quantity_type], [what_quantity_from], [what_quantity_to], [quantity], [reward_category_id], [calculation_function], [gift_is_include_in_achieve_rate_calculate], [gift_is_include_in_offer], [below_is_include_in_achieve_rate_calculate], [below_is_include_in_offer], [create_by], [create_time], [update_by], [update_time], [active_flag],[ext_property1]) VALUES
		<trim prefix="(" suffix=")" suffixOverrides=",">
				#{campaignId},
				#{whatQuantityType},
				#{whatQuantityFrom},
				#{whatQuantityTo},
				#{quantity},
				#{rewardCategoryId},
				#{calculationFunction},
				'N','N','N','N','0',GETDATE(), '0', GETDATE(),CASE WHEN #{enableFlag} = '1'  THEN 'Y' ELSE 'N' END,#{extProperty1}
			</trim>
	</insert>
	<insert id="insertofferProduct" parameterType="com.chevron.sellin.model.PromotionRule" > 
	
	INSERT INTO [PP_MID].cmt.offer_product_rel ([offer_id], [product_code], [create_by], [create_time], [update_by], [update_time], [active_flag]) VALUES
	 	<trim prefix="(" suffix=")" suffixOverrides=",">
				#{offerId},
				#{sku},
				'0',GETDATE(), '0', GETDATE(),
				CASE WHEN #{enableFlag} = '1'  THEN 'Y' ELSE 'N' END
			</trim>
	</insert>
	<!-- 
	<select id="getRewardCategoryList" resultType="com.chevron.sellin.model.RewardCategoryVo">
		SELECT reward_category_id rewardCategoryId,reward_category_name rewardCategoryName, ext_property1 sku, ext_property2 pointType, ext_property3 channel FROM [PP_MID].cmt.reward_category where active_flag = 'Y'
		SELECT reward_category_id rewardCategoryId,reward_category_name rewardCategoryName FROM [PP_MID].cmt.reward_category where active_flag = 'Y'
	</select>
	 -->
	 
	<update id="updateCampaignActive" parameterType="map">
	UPDATE c 
		SET active_flag = 'N',update_time = GETDATE()
		from [PP_MID].cmt.campaign c
		WHERE exists (select 1 from wx_t_sell_in_promotion p where isnull(p.source_id, p.id)=c.campaign_code 
		<if test="lastSynDate != null and lastSynDate != ''">
		and isnull(p.update_time, p.create_time)>='${lastSynDate}'
		</if>
		)
		and not exists (select 1 from wx_t_sell_in_promotion p where isnull(p.source_id, p.id)=c.campaign_code and p.delete_flag=0 and p.enable_flag=1)
	</update>
	<select id="getRewardCategoryId" resultType="long" parameterType="com.chevron.sellin.model.RewardCategoryVo">
		SELECT reward_category_id FROM [PP_MID].cmt.reward_category 
		where ext_property1 = #{extProperty1} and ext_property2 = #{extProperty2} and ext_property3 = #{extProperty3}
	</select>
	<insert id="insertRewardCategory" parameterType="com.chevron.sellin.model.RewardCategoryVo" useGeneratedKeys="true" keyProperty="rewardCategoryId"> 
	
	INSERT INTO [PP_MID].[cmt].[reward_category] ([reward_category_type], [reward_category_name], [create_by], [create_time], [update_by], [update_time], [active_flag],
	[ext_property1],[ext_property2], [ext_property3], [ext_property4]) VALUES
	(#{rewardCategoryType}, #{rewardCategoryName},'0',GETDATE(), '0', GETDATE(), 'Y',#{extProperty1},#{extProperty2},#{extProperty3},#{extProperty4})
	</insert>
</mapper>
