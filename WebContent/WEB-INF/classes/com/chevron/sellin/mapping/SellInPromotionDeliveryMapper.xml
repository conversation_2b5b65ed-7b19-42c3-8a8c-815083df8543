<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.sellin.dao.SellInPromotionDeliveryMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.sellin.model.SellInPromotionDelivery">
		<id column="delivery_id" property="deliveryId" jdbcType="BIGINT"/>
		<result column="sales_order" property="salesOrder" jdbcType="VARCHAR"/>
		<result column="post_year_month" property="postYearMonth" jdbcType="TIMESTAMP"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="product_sku" property="productSku" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="pack_units" property="packUnits" jdbcType="VARCHAR"/>
		<result column="liters" property="liters" jdbcType="NUMERIC"/>
		<result column="revenue_rmb" property="revenueRmb" jdbcType="DOUBLE"/>
		<result column="sold_to_code" property="soldToCode" jdbcType="VARCHAR"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		delivery_id,sales_order,post_year_month,distributor_id,product_sku,quantity,pack_units,liters,revenue_rmb,sold_to_code,region,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.sellin.model.SellInPromotionDelivery">
		update wx_t_sell_in_promotion_delivery set
				sales_order = #{salesOrder,jdbcType=VARCHAR},
				post_year_month = #{postYearMonth,jdbcType=TIMESTAMP},
				distributor_id = #{distributorId,jdbcType=BIGINT},
				product_sku = #{productSku,jdbcType=VARCHAR},
				quantity = #{quantity,jdbcType=INTEGER},
				liters = #{liters,jdbcType=NUMERIC},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where delivery_id = #{deliveryId,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.sellin.model.SellInPromotionDelivery">
		update wx_t_sell_in_promotion_delivery
		<set>
			<if test="salesOrder != null" >
				sales_order = #{salesOrder,jdbcType=VARCHAR},
			</if>
			<if test="postYearMonth != null" >
				post_year_month = #{postYearMonth,jdbcType=TIMESTAMP},
			</if>
			<if test="pricingDate != null" >
				pricing_date = #{pricingDate,jdbcType=TIMESTAMP},
			</if>
			<if test="distributorId != null" >
				distributor_id = #{distributorId,jdbcType=BIGINT},
			</if>
			<if test="productSku != null" >
				product_sku = #{productSku,jdbcType=VARCHAR},
			</if>
			<if test="quantity != null" >
				quantity = #{quantity,jdbcType=INTEGER},
			</if>
			<if test="packUnits != null" >
				pack_units = #{packUnits,jdbcType=VARCHAR},
			</if>
			<if test="liters != null" >
				liters = #{liters,jdbcType=NUMERIC},
			</if>
			<if test="revenueRmb != null" >
				revenue_rmb = #{revenueRmb,jdbcType=NUMERIC},
			</if>
			<if test="soldToCode != null" >
				sold_to_code = #{soldToCode,jdbcType=VARCHAR},
			</if>
			<if test="region != null" >
				region = #{region,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where delivery_id = #{deliveryId,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryExample">
    	delete from wx_t_sell_in_promotion_delivery
		where delivery_id = #{deliveryId,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.sellin.model.SellInPromotionDelivery" useGeneratedKeys="true" keyProperty="deliveryId">
		insert into wx_t_sell_in_promotion_delivery
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="salesOrder != null">
				sales_order,
			</if>
			<if test="postYearMonth != null">
				post_year_month,
			</if>
			<if test="pricingDate != null">
				pricing_date,
			</if>
			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="productSku != null">
				product_sku,
			</if>
			<if test="quantity != null">
				quantity,
			</if>
			<if test="packUnits != null">
				pack_units,
			</if>
			<if test="liters != null">
				liters,
			</if>
			<if test="revenueRmb != null">
				revenue_rmb,
			</if>
			<if test="soldToCode != null">
				sold_to_code,
			</if>
			<if test="region != null">
				region,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="salesOrder != null">
				#{salesOrder,jdbcType=VARCHAR},
			</if>
			<if test="postYearMonth != null">
				#{postYearMonth,jdbcType=TIMESTAMP},
			</if>
			<if test="pricingDate != null">
				#{pricingDate,jdbcType=TIMESTAMP},
			</if>
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
			<if test="productSku != null">
				#{productSku,jdbcType=VARCHAR},
			</if>
			<if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
			</if>
			<if test="packUnits != null">
				#{packUnits,jdbcType=VARCHAR},
			</if>
			<if test="liters != null">
				#{liters,jdbcType=NUMERIC},
			</if>
			<if test="revenueRmb != null">
				#{revenueRmb,jdbcType=NUMERIC},
			</if>
			<if test="soldToCode != null">
				#{soldToCode,jdbcType=VARCHAR},
			</if>
			<if test="region != null">
				#{region,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_sell_in_promotion_delivery
		<set>
			<if test="record.salesOrder != null">
				sales_order = #{record.salesOrder,jdbcType=VARCHAR},
			</if>
			<if test="record.postYearMonth != null">
				post_year_month = #{record.postYearMonth,jdbcType=TIMESTAMP},
			</if>
			<if test="record.distributorId != null">
				distributor_id = #{record.distributorId,jdbcType=BIGINT},
			</if>
			<if test="record.productSku != null">
				product_sku = #{record.productSku,jdbcType=VARCHAR},
			</if>
			<if test="record.quantity != null">
				quantity = #{record.quantity,jdbcType=INTEGER},
			</if>
			<if test="record.packUnits != null">
				pack_units = #{record.packUnits,jdbcType=VARCHAR},
			</if>
			<if test="record.liters != null">
				liters = #{record.liters,jdbcType=NUMERIC},
			</if>
			<if test="record.revenueRmb != null">
				revenue_rmb = #{record.revenueRmb,jdbcType=NUMERIC},
			</if>
			<if test="record.soldToCode != null">
				sold_to_code = #{record.soldToCode,jdbcType=VARCHAR},
			</if>
			<if test="record.region != null">
				region = #{record.region,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryExample">
		delete from wx_t_sell_in_promotion_delivery
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryExample" resultType="int">
		select count(1) from wx_t_sell_in_promotion_delivery
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_sell_in_promotion_delivery
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_sell_in_promotion_delivery
		where delivery_id = #{deliveryId,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.delivery_id, t1.sales_order, t1.post_year_month, t1.distributor_id, t1.product_sku, t1.quantity,
			 t1.pack_units, t1.liters, t1.revenue_rmb,t1.sold_to_code, t1.region, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_sell_in_promotion_delivery t1
		 where 1=1
		<if test="salesOrder != null and salesOrder != ''">
			and t1.sales_order like '%' + #{salesOrder, jdbcType=VARCHAR} + '%'
		</if>
		<if test="distributorId != null">
			and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
		</if>
		<if test="productSku != null and productSku != ''">
			and t1.product_sku like '%' + #{productSku, jdbcType=VARCHAR} + '%'
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.sellin.model.SellInPromotionDeliveryParams">
		select t1.delivery_id, t1.sales_order, t1.post_year_month, t1.distributor_id, t1.product_sku, t1.quantity,
			 t1.pack_units, t1.liters,t1.revenue_rmb, t1.sold_to_code, t1.region, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_sell_in_promotion_delivery t1
		  LEFT JOIN wx_t_promotion_delivery_detail dd ON t1.id = dd.source_id
		 where 1=1
		 <if test="awardType != null and awardType">
		  AND dd.award_type = #{awardType} 
		 </if>
		<if test="salesOrder != null and salesOrder != ''">
			and t1.sales_order like '%' + #{salesOrder, jdbcType=VARCHAR} + '%'
		</if>
		<if test="distributorId != null">
			and t1.distributor_id = #{distributorId, jdbcType=BIGINT}
		</if>
		<if test="productSku != null and productSku != ''">
			and t1.product_sku like '%' + #{productSku, jdbcType=VARCHAR} + '%'
		</if>
	</select>
	
	
	<resultMap id="BaseResultByRuleMap" type="com.chevron.sellin.model.SellInPromotionDelivery">
		<id column="delivery_id" property="deliveryId" jdbcType="BIGINT"/>
	<!-- 	<id column="sales_order" property="salesOrder" jdbcType="VARCHAR"/> -->
		<result column="sales_order" property="salesOrder" jdbcType="VARCHAR"/>
		<result column="post_year_month" property="postYearMonth" jdbcType="TIMESTAMP"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="product_sku" property="productSku" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="pack_units" property="packUnits" jdbcType="VARCHAR"/>
		<result column="liters" property="liters" jdbcType="NUMERIC"/>
		<result column="revenue_rmb" property="revenueRmb" jdbcType="NUMERIC"/>
		<result column="sold_to_code" property="soldToCode" jdbcType="VARCHAR"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<collection property="promotionRules" ofType="com.chevron.sellin.model.PromotionRule">
			<id column="id" property="id" jdbcType="BIGINT"/>
			<result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
			<result column="sku" property="sku" jdbcType="VARCHAR"/>
			<result column="rule_type" property="ruleType" jdbcType="VARCHAR"/>
			<result column="rule_config" property="ruleConfig" jdbcType="VARCHAR"/>
			<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
			<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
			<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
			<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
			<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
			<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
			<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
			<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
			<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
		</collection>
	</resultMap>
	
	<select id="getDeliveryAndRuleList" resultMap="BaseResultByRuleMap">
		select distinct bsi.base_trans_sell_in_id delivery_id, bsi.[sales_reference_doc] sales_order, bso.region,bsi.[distributor_id], bsi.[customer_name_cn],
		bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku,bsi.[pricing_date] post_year_month,
		di.dic_item_name pack_units,convert(int, round(bsi.liters/bp.unit, 0)) quantity, bsi.liters,bsi.revenue_rmb,
		pr.* 
		from PP_MID.dbo.syn_dw_to_pp_sap_sell_in bsi 
		left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
		left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
		left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
		left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=bsi.distributor_id
		left join wx_t_sell_in_promotion sp on sp.enable_flag=1 and sp.delete_flag=0
			and (sp.start_date is null or sp.start_date &lt;=bsi.[pricing_date]) and (sp.end_date is null or sp.end_date>=bsi.[pricing_date])
			and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=sp.id)
				or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=pe.partner_id and pp.promotion_id=sp.id))
			and(sp.priority_level is null or  not exists (select 1 from wx_t_sell_in_promotion sp2 
				where (sp2.start_date is null or sp2.start_date &lt;=bsi.[pricing_date]) 
				and (sp2.end_date is null or sp2.end_date>=bsi.[pricing_date]) 
				and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=sp2.id)
					or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=pe.partner_id and pp.promotion_id=sp2.id))
				and sp.priority_level>sp2.priority_level))
		left join wx_t_sell_in_promotion_rule pr on pr.sku=bsi.[product_code_SAP] and pr.promotion_id=sp.id
		where not exists (select 1 from wx_t_sell_in_promotion_delivery pd where bsi.sales_reference_doc=pd.sales_order)
		and bsi.[pricing_date]>='2020-07-01' and bso.bu='Indirect' and bsi.liters>0
	</select>
	
	<select id="buildPromotionInfo" resultMap="BaseResultByRuleMap">
		<foreach collection="deliveries" index="index" item="item" separator=" union all ">
		select distinct #{item.deliveryId,jdbcType=BIGINT} delivery_id, #{item.productSku,jdbcType=VARCHAR} product_sku,
		#{item.quantity,jdbcType=INTEGER} quantity, 
		<choose>
			<when test="item.liters == null">
		(select convert(float, bp.unit) * #{item.quantity,jdbcType=INTEGER}  from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=#{item.productSku,jdbcType=VARCHAR})
			</when>
			<otherwise>
			#{item.liters,jdbcType=NUMERIC}
			</otherwise>
		</choose>
		 liters, #{item.revenueRmb,jdbcType=NUMERIC} revenue_rmb,
		pr.* 
		from wx_t_sell_in_promotion sp 
		left join wx_t_sell_in_promotion_rule pr on pr.sku=#{item.productSku,jdbcType=VARCHAR} and pr.promotion_id=sp.id
		where sp.enable_flag=1 and sp.delete_flag=0 and sp.status=10
			and (sp.start_date is null or sp.start_date &lt;=CONVERT(varchar(12) , getdate(), 23 )) and (sp.end_date is null or sp.end_date>=CONVERT(varchar(12) , getdate(), 23 ))
			and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=sp.id)
				or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=#{item.partnerId,jdbcType=BIGINT} and pp.promotion_id=sp.id))
			and(sp.priority_level is null or  not exists (select 1 from wx_t_sell_in_promotion sp2 
				where (sp2.start_date is null or sp2.start_date &lt;=CONVERT(varchar(12) , getdate(), 23 ))
				and (sp2.end_date is null or sp2.end_date>=CONVERT(varchar(12) , getdate(), 23 ))
				and (not exists (select 1 from wx_t_promotion_partner pp where pp.promotion_id=sp2.id)
					or exists (select 1 from wx_t_promotion_partner pp where pp.partner_id=#{item.partnerId,jdbcType=BIGINT} and pp.promotion_id=sp2.id))
				and sp.priority_level>sp2.priority_level))
		</foreach>
	</select>
  <update id="synSellinPromotion" parameterType="map" statementType="CALLABLE">
    {call pr_syn_sellin_promotion()} 
  </update>
</mapper>
