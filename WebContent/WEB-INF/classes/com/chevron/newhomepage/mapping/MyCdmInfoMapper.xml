<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.newhomepage.dao.MyCdmInfoMapper" >
    <resultMap id="RankInfoMap" type="com.chevron.newhomepage.model.RankInfo">
        <!--private Integer rankSales;-->
        <!--private Integer rankGross;-->
        <!--private Double rankScore;-->
        <!--private Date evaluationMonth;-->
        <result column="rank_sales" jdbcType="INTEGER" property="rankSales" />
        <result column="rank_gross" jdbcType="INTEGER" property="rankGross" />
        <result column="rank_score" jdbcType="NUMERIC" property="rankScore" />
        <result column="evaluation_month" jdbcType="TIMESTAMP" property="evaluationMonth" />
    </resultMap>
    <resultMap id="GrossProfitInfoMap" type="com.chevron.newhomepage.model.GrossProfitInfo">
        <!--private String salesCai;-->
        <!--private Double targetValue;-->
        <!--private Double actualValue;-->
        <!--private String salesChannel;-->
        <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
        <result column="sales_cai" property="salesCai" jdbcType="VARCHAR" />
        <result column="target_value" property="targetValue" jdbcType="NUMERIC" />
        <result column="actual_value" property="actualValue" jdbcType="NUMERIC" />
        <result column="sales_channel" property="salesChannel" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="GrossAndMktValueMap" type="com.chevron.newhomepage.model.GrossAndMktValueInfo">
        <!--private Long partnerId;-->
        <!--private Double grossActualValue;-->
        <!--private Double grossTargetValue;-->
        <!--private Double newShopCost;-->
        <!--private Double seminarCost;-->
        <!--private Double cmdStockPoint;-->
        <!--private Double cdmMaterialPoint;-->
        <!--private Double trainingCost;-->
        <!--private Double mktCostValue;-->
        <!--基金 进货 物料 店招（"门头申请"） 会议补贴（"门头申请"） 培训（来自培训表）-->
        <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
        <result column="new_shop_cost" property="newShopCost" jdbcType="NUMERIC" />
        <result column="seminar_cost" property="seminarCost" jdbcType="NUMERIC" />
        <result column="cmd_stock_point" property="cmdStockPoint" jdbcType="NUMERIC" />
        <result column="cdm_material_point" property="cdmMaterialPoint" jdbcType="NUMERIC" />
        <result column="mkt_cost_value" property="mktCostValue" jdbcType="NUMERIC" />
        <result column="training_cost" property="trainingCost" jdbcType="NUMERIC" />
        <result column="gross_target_value" property="grossTargetValue" jdbcType="NUMERIC" />
        <result column="gross_actual_value" property="grossActualValue" jdbcType="NUMERIC" />
        <result column="total_fund_value" property="totalFundValue" jdbcType="NUMERIC" />
        <result column="promotion_value" property="promotionValue" jdbcType="NUMERIC" />
        <result column="b2b_point_value" property="b2bPointValue" jdbcType="NUMERIC" />

        <result column="training_days" property="trainingDays" jdbcType="NUMERIC" />
        <result column="store_front_count" property="storeFrontCount" jdbcType="NUMERIC" />
        <result column="meeting_count" property="meetingCount" jdbcType="NUMERIC" />
        <!--<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR" />-->
    </resultMap>
    <resultMap id="AsmPerEvaFormResultMap" type="com.chevron.evaluation.model.AsmPerEvaForm">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="program_id" property="programId" jdbcType="BIGINT"/>
        <result column="supervisor_id" property="supervisorId" jdbcType="BIGINT"/>
        <result column="region_name" property="regionName" jdbcType="VARCHAR"/>
        <result column="channel_manager_id" property="channelManagerId" jdbcType="BIGINT"/>
        <result column="volume_target" property="volumeTarget" jdbcType="NUMERIC"/>
        <result column="volume_result" property="volumeResult" jdbcType="NUMERIC"/>
        <result column="cross_margin_target" property="crossMarginTarget" jdbcType="NUMERIC"/>
        <result column="cross_margin_result" property="crossMarginResult" jdbcType="NUMERIC"/>
        <result column="review_flsr_report_score" property="reviewFlsrReportScore" jdbcType="INTEGER"/>
        <result column="meetings_score" property="meetingsScore" jdbcType="NUMERIC"/>
        <result column="management_score" property="managementScore" jdbcType="NUMERIC"/>
        <result column="comments" property="comments" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="status_text" property="statusText" jdbcType="VARCHAR"/>
        <result column="submit_time" property="submitTime" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="volume_weight" property="volumeWeight" jdbcType="INTEGER"/>
        <result column="cross_margin_weight" property="crossMarginWeight" jdbcType="INTEGER"/>
        <result column="review_flsr_report_weight" property="reviewFlsrReportWeight" jdbcType="INTEGER"/>
        <result column="meeting_weight" property="meetingWeight" jdbcType="INTEGER"/>
        <result column="management_weight" property="managementWeight" jdbcType="INTEGER"/>
        <result column="total_final_score" property="totalFinalScore" jdbcType="NUMERIC"/>
        <result column="supervisor_name" property="supervisorName" jdbcType="VARCHAR"/>
        <result column="review_flsr_report_result" property="reviewFlsrReportResult" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="evaluation_month" property="evaluationMonth" jdbcType="DATE"/>
        <result column="supervisor_cai" property="supervisorCai" jdbcType="VARCHAR"/>
        <result column="channel_manager_name" property="channelManagerName" jdbcType="VARCHAR"/>
        <result column="meetings_result" property="meetingsResult" jdbcType="VARCHAR"/>
        <result column="management_result" property="managementResult" jdbcType="VARCHAR"/>
        <result column="review_mf_count" property="reviewMfCount" jdbcType="INTEGER"/>
        <result column="review_mp_count" property="reviewMpCount" jdbcType="INTEGER"/>
        <result column="sales_count" property="salesCount" jdbcType="INTEGER"/>
        <result column="review_mp_rate" property="reviewMpRate" jdbcType="INTEGER"/>
        <result column="f_year" property="year" jdbcType="INTEGER"/>
        <result column="f_quarter" property="quarter" jdbcType="INTEGER"/>
        <result column="f_rank" property="rank" jdbcType="INTEGER"/>
        <result column="avg_score" property="avgScore" jdbcType="NUMERIC"/>
        <result column="rank" property="rank" jdbcType="INTEGER"/>
        <result column="ranking" property="ranking" jdbcType="NUMERIC"/>
        <result column="rank1" property="rank1" jdbcType="INTEGER"/>
        <result column="ranking1" property="ranking1" jdbcType="NUMERIC"/>
        <result column="rank2" property="rank2" jdbcType="INTEGER"/>
        <result column="ranking2" property="ranking2" jdbcType="NUMERIC"/>
        <result column="reject_comment" property="rejectComment" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="PartnerSalesInfoMap" type="com.chevron.partnerorder.model.PartnerSalesInfo" >
        <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
        <result column="partner_name" property="partnerName" jdbcType="VARCHAR" />
        <result column="sell_in" property="sellIn" jdbcType="NUMERIC" />
        <result column="sell_through" property="sellThrough" jdbcType="NUMERIC" />
        <result column="year_str" property="year" jdbcType="INTEGER" />
        <result column="month_str" property="month" jdbcType="INTEGER" />
        <result column="day_str" property="day" jdbcType="INTEGER" />
    </resultMap>
    <resultMap id="WorkShopScanInfoMap" type="com.chevron.newhomepage.model.WorkShopScanInfo">
        <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
        <result column="work_shop_name" property="workShopName" jdbcType="VARCHAR" />
        <result column="sp_name" property="spName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="WorkshopMonthScanInfoMap" type="com.chevron.newhomepage.model.WorkshopMonthScanInfo">
        <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
        <result column="month" property="month" jdbcType="INTEGER" />
        <result column="liters" property="liters" jdbcType="NUMERIC" />
        <result column="product_name" property="productName" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="PartnerBaseResultMap" type="com.sys.organization.model.OrganizationVo" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="distributor_id" property="distributorId" jdbcType="BIGINT" />
        <result column="organization_code" property="organizationCode" jdbcType="NVARCHAR" />
        <result column="customer_name_cn" property="organizationName" jdbcType="NVARCHAR" />
        <result column="organization_name_py" property="organizationNamePy" jdbcType="NVARCHAR" />
        <result column="parent_id" property="parentId" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="creator" property="creator" jdbcType="BIGINT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="updator" property="updator" jdbcType="BIGINT" />
        <result column="esspflag" property="esspflag" jdbcType="INTEGER" />
        <result column="esspflag" property="esspflag" jdbcType="INTEGER" />
        <result column="Customer_Grade" property="customerGrade" jdbcType="VARCHAR" />
        <result column="partner_property_text" property="partnerPropertyText" jdbcType="VARCHAR" />
        <result column="partner_property_level" property="partnerPropertyLevel" jdbcType="INTEGER" />
    </resultMap>
    <resultMap id="DwPpSalesTargetMap" type="com.chevron.newhomepage.model.DwPpSalesTarget">
        <result property="partnerId" column="partner_id" jdbcType="BIGINT" />
        <result property="targetValue" column="target_value" jdbcType="NUMERIC" />
        <result property="month" column="month" jdbcType="INTEGER" />
        <result property="cdmProductCategory" column="cdm_product_category" jdbcType="VARCHAR" />
        <result property="monthStr" column="month_str" jdbcType="VARCHAR" />
        <result property="sellThrough" column="sell_through" jdbcType="NUMERIC" />
    </resultMap>
    <resultMap id="MktApplyCountInfoMap" type="com.chevron.newhomepage.model.MktApplyCountInfo">
        <result property="count" column="count" jdbcType="BIGINT" />
        <result property="mktType" column="mkt_type" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="NewWorkshopInfoMap" type="com.chevron.newhomepage.model.NewWorkshopInfo">
        <result column="new_shop_import_count" property="newShopImportCount" jdbcType="BIGINT" />
        <result column="new_shop_import_cost" property="newShopImportCost" jdbcType="NUMERIC" />
    </resultMap>
    <sql id="queryRankInfoForAsm">
        select top 1 *
        from (select
        xx1.*,
        ROW_NUMBER()
        OVER ( PARTITION BY xx1.program_id, xx1.channel_manager_id
        ORDER BY xx1.ranking DESC )           AS rank_sales,
        xx2.ranking1,
        ROW_NUMBER()
        OVER ( PARTITION BY xx1.program_id, xx1.channel_manager_id
        ORDER BY xx2.ranking1 DESC )          AS rank_gross,
        xx3.total_final_score,
        ROW_NUMBER()
        OVER ( PARTITION BY xx3.program_id, xx1.channel_manager_id
        ORDER BY xx3.total_final_score DESC ) AS rank_score
        from (/*未提交且未关闭数据*/ SELECT
        t1.id,
        ep.id                                                                                       AS program_id,
        u1.user_id                                                                                  AS supervisor_id,
        u2.user_id                                                                                  AS channel_manager_id,
        ep.evaluation_month,
        u1.ch_name                                                                                  AS supervisor_name,
        u2.ch_name                                                                                  AS channel_manager_name,
        ep.sales_channel,
        ep.bu,
        t1.[status],
        (SELECT di1.dic_item_name
        FROM wx_t_dic_item di1
        WHERE di1.dic_type_code = 'AsmPerEvaForm.status' AND di1.dic_item_code =
        t1.[status])                          AS status_text,
        null                                                                                           submit_time,
        (SELECT CASE WHEN SUM(bip.target_volume) = 0
        THEN NULL
        ELSE SUM(bip.actual_volume) / SUM(bip.target_volume) END
        FROM dw_sales_target_actual_performance_by_month bip
        WHERE bip.[month] &gt;= CONVERT(varchar(4), ep.evaluation_month, 112) + '01' AND
        bip.[month] &lt;= CONVERT(varchar(6), ep.evaluation_month, 112) AND bip.supervisor_cai =
        bi.suppervisor_cai) AS ranking
        FROM wx_t_evaluation_program ep LEFT JOIN (SELECT DISTINCT
        rsc.channel_manager_cai,
        crss.suppervisor_cai,
        rsc.sales_channel_name
        FROM
        dw_customer_region_sales_supervisor_rel crss LEFT JOIN
        dw_region_sales_channel_rel rsc
        ON rsc.region_name = crss.region_name
        WHERE
        NOT EXISTS(/*排除既是ASM也是ChannelManager数据*/ SELECT 1
        FROM
        dw_region_sales_channel_rel rsc1
        WHERE
        rsc1.channel_manager_cai
        =
        crss.suppervisor_cai)) bi
        ON bi.sales_channel_name = 'indirect'
        JOIN wx_t_user u1 ON u1.cai = bi.suppervisor_cai AND u1.status = 1
        LEFT JOIN wx_t_user u2 ON u2.cai = bi.channel_manager_cai AND u2.status = 1
        LEFT JOIN wx_t_asm_per_eva_form t1
        ON t1.program_id = ep.id and t1.supervisor_id = u1.user_id
        WHERE ep.program_type = 'Sales_Manager_Performance_Evaluation_Form' AND ep.status >= 5 AND
        ep.status &lt; 100 AND NOT EXISTS(SELECT 1
        FROM wx_t_asm_per_eva_form t1
        WHERE
        t1.program_id = ep.id AND t1.supervisor_id = u1.user_id
        AND t1.status &gt;= 20)
        UNION ALL /*已提交数据*/ SELECT
        t1.id,
        t1.program_id,
        t1.supervisor_id,
        t1.channel_manager_id,
        ep.evaluation_month,
        u1.ch_name                                   AS supervisor_name,
        sup.ch_name                                  AS channel_manager_name,
        ep.sales_channel,
        ep.bu,
        t1.[status],
        (SELECT di1.dic_item_name
        FROM wx_t_dic_item di1
        WHERE di1.dic_type_code = 'AsmPerEvaForm.status' AND
        di1.dic_item_code = t1.[status])      AS status_text,
        t1.submit_time,
        CASE WHEN t1.volume_target = 0
        THEN NULL
        ELSE t1.volume_result / t1.volume_target END AS ranking
        FROM wx_t_asm_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep
        ON t1.program_id = ep.id
        LEFT JOIN wx_t_user sup ON sup.user_id = t1.channel_manager_id
        LEFT JOIN wx_t_user u1 ON u1.user_id = t1.supervisor_id
        WHERE ep.status >= 5 AND t1.status >= 20) xx1 /*毛利完成率数据*/
        left join
        (/*未提交未关闭数据*/
        SELECT
        ep.id                                           AS program_id,
        u1.user_id                                      AS supervisor_id,
        (SELECT CASE WHEN SUM(bip.target_margin) = 0
        THEN NULL
        ELSE SUM(bip.actual_margin_usd) / SUM(bip.target_margin) END
        FROM dw_sales_target_actual_performance_by_month bip
        WHERE bip.[month] &gt;= CONVERT(varchar(4), ep.evaluation_month, 112) + '01' AND
        bip.[month] &lt;= CONVERT(varchar(6), ep.evaluation_month, 112) AND
        bip.supervisor_cai = bi.suppervisor_cai) AS ranking1
        FROM wx_t_evaluation_program ep LEFT JOIN (SELECT DISTINCT
        rsc.channel_manager_cai,
        crss.suppervisor_cai,
        rsc.sales_channel_name
        FROM
        dw_customer_region_sales_supervisor_rel crss LEFT JOIN
        dw_region_sales_channel_rel rsc
        ON rsc.region_name = crss.region_name
        WHERE NOT EXISTS(/*排除既是ASM也是ChannelManager数据*/ SELECT 1
        FROM
        dw_region_sales_channel_rel rsc1
        WHERE
        rsc1.channel_manager_cai
        =
        crss.suppervisor_cai)) bi
        ON bi.sales_channel_name = 'indirect'
        JOIN wx_t_user u1 ON u1.cai = bi.suppervisor_cai AND u1.status = 1
        LEFT JOIN wx_t_user u2 ON u2.cai = bi.channel_manager_cai AND u2.status = 1
        WHERE ep.program_type = 'Sales_Manager_Performance_Evaluation_Form' AND ep.status >= 5 AND
        ep.status &lt; 100 AND NOT EXISTS(SELECT 1
        FROM wx_t_asm_per_eva_form t1
        WHERE
        t1.program_id = ep.id AND t1.supervisor_id = u1.user_id AND
        t1.status &gt;= 20)
        UNION ALL /*已提交数据*/ SELECT
        t1.program_id,
        t1.supervisor_id,
        CASE WHEN t1.cross_margin_target = 0
        THEN NULL
        ELSE t1.cross_margin_result / t1.cross_margin_target END AS ranking1
        FROM wx_t_asm_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep
        ON t1.program_id = ep.id
        LEFT JOIN wx_t_user sup ON sup.user_id = t1.channel_manager_id
        LEFT JOIN wx_t_user u1 ON u1.user_id = t1.supervisor_id
        WHERE ep.status &gt;= 5 AND t1.status &gt;= 20
        ) xx2
        on xx1.program_id = xx2.program_id and xx1.supervisor_id = xx2.supervisor_id
        /*已提交数据的总分*/ left join (SELECT
        t1.program_id,
        t1.supervisor_id,
        t1.total_final_score
        FROM
        wx_t_asm_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep ON t1.program_id = ep.id
        LEFT JOIN wx_t_user sup ON sup.user_id = t1.channel_manager_id
        LEFT JOIN wx_t_user u1 ON u1.user_id = t1.supervisor_id
        WHERE ep.status &gt;= 5 AND t1.status &gt;= 20) xx3
        on xx1.program_id = xx3.program_id and xx1.supervisor_id = xx3.supervisor_id
        where 1 = 1 and xx1.channel_manager_id = #{channelManagerUserId, jdbcType= BIGINT} and xx1.bu = 'CDM' and
        xx1.evaluation_month = #{queryMonth, jdbcType = TIMESTAMP}
        ) aa
        where 1=1 and aa.supervisor_id = #{userId,jdbcType=BIGINT}
    </sql>
    <select id="queryRankInfo" parameterType="map" resultMap="RankInfoMap">
        <choose>
            <when test="isAsm">
                <include refid="queryRankInfoForAsm"></include>
            </when>
            <otherwise>
                select top 1 aa.*
                from (select
                xx1.*,
                ROW_NUMBER()
                OVER ( PARTITION BY xx1.program_id, xx1.supervisor_id
                ORDER BY xx1.ranking DESC )           AS rank_sales,
                xx2.ranking1,
                ROW_NUMBER()
                OVER ( PARTITION BY xx1.program_id, xx1.supervisor_id
                ORDER BY xx2.ranking1 DESC )          AS rank_gross,
                xx3.total_final_score,
                ROW_NUMBER()
                OVER ( PARTITION BY xx3.program_id, xx1.supervisor_id
                ORDER BY xx3.total_final_score DESC ) AS rank_score
                from (/*未提交且未关闭数据*/
                SELECT
                t1.id,
                ep.id                                                                                   AS program_id,
                u1.user_id                                                                              AS sales_id,
                u2.user_id                                                                              AS supervisor_id,
                ep.evaluation_month,
                u1.ch_name                                                                              AS sales_name,
                u2.ch_name                                                                              AS supervisor_name,
                ep.sales_channel,
                ep.bu,
                t1.[status],
                (SELECT di1.dic_item_name
                FROM wx_t_dic_item di1
                WHERE di1.dic_type_code = 'FlsrPerEvaForm.status' AND di1.dic_item_code =
                t1.[status])                     AS status_text,
                null                                                                                       submit_time,
                (SELECT CASE WHEN SUM(bip.target_volume) = 0
                THEN NULL
                ELSE SUM(bip.actual_volume) / SUM(bip.target_volume) END
                FROM dw_sales_target_actual_performance_by_month bip
                WHERE bip.[month] >= CONVERT(varchar(4), ep.evaluation_month, 112) + '01' AND
                bip.[month] &lt;= CONVERT(varchar(6), ep.evaluation_month, 112) AND bip.sales_cai =
                bi.sales_cai)   AS ranking
                FROM wx_t_evaluation_program ep LEFT JOIN (SELECT DISTINCT
                crss.sales_cai,
                crss.suppervisor_cai,
                rsc.sales_channel_name
                FROM
                dw_customer_region_sales_supervisor_rel crss LEFT JOIN
                dw_region_sales_channel_rel rsc
                ON rsc.region_name = crss.region_name
                WHERE NOT EXISTS(/*排除既是FLSR也是ASM数据*/ SELECT 1
                FROM
                dw_customer_region_sales_supervisor_rel crss1
                WHERE
                crss.sales_cai
                =
                crss1.suppervisor_cai
                AND
                crss1.suppervisor_cai
                !=
                crss1.sales_cai)
                AND
                crss.suppervisor_cai != crss.sales_cai) bi
                ON bi.sales_channel_name = 'indirect'
                JOIN wx_t_user u1 ON u1.cai = bi.sales_cai AND u1.status = 1
                LEFT JOIN wx_t_user u2 ON u2.cai = bi.suppervisor_cai AND u2.status = 1
                LEFT JOIN wx_t_flsr_per_eva_form t1 ON t1.program_id = ep.id and t1.sales_id = u1.user_id
                WHERE
                ep.program_type = 'FLSR_Performance_Evaluation_Form' AND ep.status >= 5 AND ep.status &lt; 100
                AND NOT EXISTS(
                SELECT 1
                FROM wx_t_flsr_per_eva_form t1
                WHERE t1.program_id = ep.id AND t1.sales_id = u1.user_id AND t1.status >= 20)
                UNION ALL
                /*已提交数据*/
                SELECT
                t1.id,
                t1.program_id,
                t1.sales_id,
                t1.supervisor_id,
                ep.evaluation_month,
                u1.ch_name                                   AS sales_name,
                sup.ch_name                                  AS supervisor_name,
                ep.sales_channel,
                ep.bu,
                t1.[status],
                (SELECT di1.dic_item_name
                FROM wx_t_dic_item di1
                WHERE di1.dic_type_code = 'FlsrPerEvaForm.status' AND
                di1.dic_item_code = t1.[status])      AS status_text,
                t1.submit_time,
                CASE WHEN t1.volume_target = 0
                THEN NULL
                ELSE t1.volume_result / t1.volume_target END AS ranking
                FROM wx_t_flsr_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep
                ON t1.program_id = ep.id
                LEFT JOIN wx_t_user sup ON sup.user_id = t1.supervisor_id
                LEFT JOIN wx_t_user u1 ON u1.user_id = t1.sales_id
                WHERE ep.status >= 5 AND t1.status >= 20) xx1 /*毛利完成率数据*/ left join
                (/*未提交未关闭数据*/ SELECT
                ep.id                                AS program_id,
                u1.user_id                           AS sales_id,
                (SELECT CASE WHEN SUM(bip.target_margin) = 0
                THEN NULL
                ELSE SUM(bip.actual_margin_usd) / SUM(bip.target_margin) END
                FROM dw_sales_target_actual_performance_by_month bip
                WHERE bip.[month] >= CONVERT(varchar(4), ep.evaluation_month, 112) + '01' AND
                bip.[month] &lt;= CONVERT(varchar(6), ep.evaluation_month, 112) AND
                bip.sales_cai = bi.sales_cai) AS ranking1
                FROM wx_t_evaluation_program ep LEFT JOIN (SELECT DISTINCT
                crss.sales_cai,
                crss.suppervisor_cai,
                rsc.sales_channel_name
                FROM
                dw_customer_region_sales_supervisor_rel crss LEFT JOIN
                dw_region_sales_channel_rel rsc
                ON rsc.region_name = crss.region_name
                WHERE NOT EXISTS(/*排除既是FLSR也是ASM数据*/ SELECT 1
                FROM
                dw_customer_region_sales_supervisor_rel crss1
                WHERE
                crss.sales_cai =
                crss1.suppervisor_cai
                AND
                crss1.suppervisor_cai
                !=
                crss1.sales_cai)
                AND crss.suppervisor_cai != crss.sales_cai) bi
                ON bi.sales_channel_name = 'indirect'
                JOIN wx_t_user u1 ON u1.cai = bi.sales_cai AND u1.status = 1
                LEFT JOIN wx_t_user u2 ON u2.cai = bi.suppervisor_cai AND u2.status = 1
                WHERE
                ep.program_type = 'FLSR_Performance_Evaluation_Form' AND ep.status >= 5 AND ep.status &lt; 100 AND
                NOT EXISTS(SELECT 1
                FROM wx_t_flsr_per_eva_form t1
                WHERE t1.program_id = ep.id AND t1.sales_id = u1.user_id AND t1.status >= 20)
                UNION ALL /*已提交数据*/ SELECT
                t1.program_id,
                t1.sales_id,
                CASE WHEN t1.cross_margin_target = 0
                THEN NULL
                ELSE t1.cross_margin_result / t1.cross_margin_target END AS ranking1
                FROM wx_t_flsr_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep
                ON t1.program_id = ep.id
                LEFT JOIN wx_t_user sup ON sup.user_id = t1.supervisor_id
                LEFT JOIN wx_t_user u1 ON u1.user_id = t1.sales_id
                WHERE ep.status >= 5 AND t1.status >= 20) xx2
                on xx1.program_id = xx2.program_id and xx1.sales_id = xx2.sales_id
                /*已提交数据的总分*/ left join (SELECT
                t1.program_id,
                t1.sales_id,
                t1.total_final_score
                FROM wx_t_flsr_per_eva_form t1 LEFT JOIN wx_t_evaluation_program ep
                ON t1.program_id = ep.id
                LEFT JOIN wx_t_user sup ON sup.user_id = t1.supervisor_id
                LEFT JOIN wx_t_user u1 ON u1.user_id = t1.sales_id
                WHERE ep.status >= 5 AND t1.status >= 20) xx3
                on xx1.program_id = xx3.program_id and xx1.sales_id = xx3.sales_id
                where 1 = 1
                /* and xx1.supervisor_id = '65921' */
                and xx1.evaluation_month = #{queryMonth,jdbcType=TIMESTAMP}) aa
                where aa.sales_id = #{userId,jdbcType=BIGINT}
            </otherwise>
        </choose>
    </select>
    <!--那个myCdm列表“市场费用里面的值”，鼠标移动过去的时候显示的-->
    <select id="queryGrossAndMktCostByPartnerIds" parameterType="map" resultMap="GrossAndMktValueMap">
        select partner_id,isnull(cmd_stock_point,0) + isnull(cdm_material_point,0) + isnull(total_fund_value,0) + isnull(training_cost,0)
        + isnull(seminar_cost,0) + isnull(new_shop_cost,0) + isnull(new_shop_import_cost,0) + isnull(promotion_value,0)      mkt_cost_value,
        isnull(gross_actual_value,0) gross_actual_value,
        isnull(gross_target_value,0) gross_target_value,
        (isnull(new_shop_cost,0) + isnull(new_shop_import_cost,0)) new_shop_cost,
        isnull(b2b_point_value,0) b2b_point_value,
        isnull(seminar_cost,0) seminar_cost,
        isnull(training_cost,0) training_cost,
        isnull(promotion_value,0) promotion_value,
        isnull(cmd_stock_point,0) cmd_stock_point,
        isnull(cdm_material_point, 0) cdm_material_point,
        isnull(total_fund_value,0) total_fund_value
        
            ,isnull(training_days,0) training_days,
            isnull(store_front_count,0) + isnull(new_shop_import_count,0) store_front_count,
            isnull(meeting_count,0) meeting_count
        
        from
        (
        select
        eop.partner_id,
        round(sum(isnull(bip.actual_margin, 0)), 2) gross_actual_value,
        round(sum(isnull(bip.target_margin, 0)), 2)     gross_target_value,
        (select round(sum(isnull(pvd.POINT_VALUE,0)),2) from dbo.wx_t_point_value_detail pvd left join dbo.wx_t_point_account pac on pac.id = pvd.POINT_ACCOUNT_ID
        where pac.POINT_ACCOUNT_OWNER_ID = eop.partner_id and  year(pvd.TRANS_TIME) = #{year} and POINT_TYPE in ('CDM_STOCK_POINT')) cmd_stock_point,
        (select round(sum(isnull(pvd.POINT_VALUE,0)),2) from dbo.wx_t_point_value_detail pvd left join dbo.wx_t_point_account pac on pac.id = pvd.POINT_ACCOUNT_ID
        where pac.POINT_ACCOUNT_OWNER_ID = eop.partner_id and  year(pvd.TRANS_TIME) = #{year} and POINT_TYPE in ('CDM_MATERIAL_POINT')) cdm_material_point,
        (
        select sum(isnull(convert(float,mf.field_value),0))
        from wx_t_mkt_apply ma
        left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
        where 1=1
        and ma.flow_finish = 1
        and (ma.mkt_type='STORE_FRONT' or ma.mkt_type='STORE_FRONT_OFFER' or ma.mkt_type='STORE_FRONT_REIMBURSE')
        and year(ma.create_time) = #{year}
        and ma.partner_id = eop.distributor_id
        )  new_shop_cost,
        (
        select sum(recruitment_amount) new_shop_import_cost
        from wx_t_work_shop w
        inner join wx_t_workshop_partner wp on w.id = wp.workshop_id
        inner join wx_t_organization o on o.id = wp.partner_id
        where recruitment_amount is not null
        and o.id =  eop.partner_id
        ) new_shop_import_cost,
        (
        select count(1)
        from wx_t_work_shop w
        inner join wx_t_workshop_partner wp on w.id = wp.workshop_id
        inner join wx_t_organization o on o.id = wp.partner_id
        where recruitment_amount is not null
        and o.id =  eop.partner_id
        ) new_shop_import_count,
        (
        select sum(pvd.POINT_VALUE)
        from wx_t_point_value_detail pvd inner join wx_t_point_account pac on pac.id = pvd.POINT_ACCOUNT_ID
        inner join wx_t_workshop_employee emp on pac.POINT_ACCOUNT_OWNER_CODE = emp.code
        inner join wx_t_work_shop shop on shop.id = emp.workshop_id
        inner join wx_t_workshop_partner wp on shop.id = wp.workshop_id
        where wp.partner_id = eop.partner_id and pvd.DELETE_FLAG = 0 and POINT_TYPE = 'B2B_POINT' and pac.POINT_ACCOUNT_TYPE = 'TC'
        ) b2b_point_value,
        (
        select  sum(isnull(convert(float,mf.field_value),0))
        from wx_t_mkt_apply ma
        left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
        where 1=1
        and ma.flow_finish = 1
        and (ma.mkt_type='SEMINAR' or ma.mkt_type='SEMINAR_REIMBURSE')
        and year(ma.create_time) = #{year}
        and ma.partner_id = eop.distributor_id
        )  seminar_cost,
        (
        select
        sum(price) price
        from dbo.wx_t_training_info
        where partner_id = eop.partner_id and year = #{year}
        )  training_cost,
        (
           select sum(promotion) pro_value from wx_t_promotion pro
           where pro.partner_id = eop.partner_id and year = #{year}
        ) promotion_value,
        (select round(sum(isnull(actual_CDM_BD_Fund, 0)) + sum(isnull(actual_CDM_IVI_Fund, 0)) +
        sum(isnull(CDM_Marketing_Fund, 0)), 2) * -1 funds_value
        from dbo.dw_pp_customer_fund_quarter_new fq
        where fq.Distributor_id = eop.distributor_id and fq.Year = #{year} ) total_fund_value
        
            ,(
            select
            sum(days) days
            from dbo.wx_t_training_info
            where partner_id = eop.partner_id and year = #{year}
            )   training_days,
            (
            select  isnull(count(1),0)
            from wx_t_mkt_apply ma
            left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
            where 1=1
            and ma.flow_finish = 1
            and (ma.mkt_type='STORE_FRONT_OFFER')
            and year(ma.create_time) = #{year}
            and ma.partner_id = eop.distributor_id
            )  store_front_count,
            (
            select  isnull(count(1),0)
            from wx_t_mkt_apply ma
            left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
            where 1=1
            and ma.flow_finish = 1
            and (ma.mkt_type='SEMINAR')
            and year(ma.create_time) = #{year}
            and ma.partner_id = eop.distributor_id
            )  meeting_count
        
        from dw_sales_target_actual_performance_by_month bip
        left join wx_t_partner_o2o_enterprise eop on bip.distributor_id = eop.distributor_id
        LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = bip.distributor_id and bip.sales_cai = dwrssr.sales_cai
        LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        where 1 = 1
        and dwrsr.sales_channel_name =  #{salesChannel,jdbcType=VARCHAR}
        and bip.month &gt;=   #{beginMonth,jdbcType=NVARCHAR}
        and bip.month &lt;= #{endMonth,jdbcType=NVARCHAR}
        <if test="partnerIds != null">
            and eop.partner_id in (
            <foreach collection="partnerIds" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by eop.partner_id,eop.distributor_id
        ) aa
    </select>
    <select id="queryGrossProfitInfo" parameterType="map" resultMap="GrossProfitInfoMap">
    	<if test="!isChannel">
            select
            #{salesCai,jdbcType=NVARCHAR} sales_cai,
            #{salesChannel,jdbcType=VARCHAR} sales_channel,
            round(sum(bip.actual_margin_usd),2) actual_value,
            round(sum(bip.target_margin),2)  target_value
            from dw_sales_target_actual_performance_by_month bip
            LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = bip.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
            where 1=1
            and dwrsr.sales_channel_name = 'indirect'
            <if test="beginMonth  != null">
            and bip.month &gt;= #{beginMonth,jdbcType=NVARCHAR}
            </if>
            <if test="endMonth != null">
            and bip.month &lt;= #{endMonth,jdbcType=NVARCHAR}
            </if>
            <if test="salesCai != null">
                <choose>
                    <when test="!isChannel">
                        and (bip.sales_cai = #{salesCai,jdbcType=NVARCHAR}
                        or bip.supervisor_cai = #{salesCai,jdbcType=NVARCHAR})
                    </when>
                    <when test="isChannel">
                        and (dwrsr.channel_manager_cai = #{salesCai,jdbcType=NVARCHAR} or dwrsr.bu_manager_cai = #{salesCai,jdbcType=NVARCHAR})
                    </when>
                </choose>
            </if>
    	</if>
    	<if test="isChannel">
    								SELECT
						round( isnull( SUM ( total_info.actual_value ), 0 ), 2 ) actual_value,
						round( isnull( SUM ( total_info.target_value ), 0 ), 2 ) target_value
						FROM
						(
						select
            round(sum(bip.actual_margin_usd),2) actual_value,
            round(sum(bip.target_margin),2)  target_value
            from dw_sales_target_actual_performance_by_month bip
            LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON dwrssr.distributor_id = bip.distributor_id and bip.sales_cai = dwrssr.sales_cai
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
            where 1=1
            and dwrsr.sales_channel_name = 'indirect'
						AND bip.sales != 'NA' 
            <if test="beginMonth  != null">
            and bip.month &gt;= #{beginMonth,jdbcType=NVARCHAR}
            </if>
            <if test="endMonth != null">
            and bip.month &lt;= #{endMonth,jdbcType=NVARCHAR}
            </if>
            <if test="salesCai != null">
                <choose>
                    <when test="!isChannel">
                        and (bip.sales_cai = #{salesCai,jdbcType=NVARCHAR}
                        or bip.supervisor_cai = #{salesCai,jdbcType=NVARCHAR})
                    </when>
                    <when test="isChannel">
                        and (dwrsr.channel_manager_cai = #{salesCai,jdbcType=NVARCHAR} or dwrsr.bu_manager_cai = #{salesCai,jdbcType=NVARCHAR}  OR dwrsr.channel_manager_cai =  #{salesCai,jdbcType=NVARCHAR}  OR dwrsr.bu_manager_cai =  #{salesCai,jdbcType=NVARCHAR}  )
                    </when>
                </choose>
            </if>	
						UNION
						select
            round(sum(bip.actual_margin_usd),2) actual_value,
            round(sum(bip.target_margin),2)  target_value
            from dw_sales_target_actual_performance_by_month bip
            LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON dwrssr.distributor_id = bip.distributor_id and bip.sales = dwrssr.sales_name
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
            where 1=1
            and dwrsr.sales_channel_name = 'indirect'
						AND bip.sales = 'NA' 
            <if test="beginMonth  != null">
            and bip.month &gt;= #{beginMonth,jdbcType=NVARCHAR}
            </if>
            <if test="endMonth != null">
            and bip.month &lt;= #{endMonth,jdbcType=NVARCHAR}
            </if>
            <if test="salesCai != null">
                <choose>
                    <when test="!isChannel">
                        and (bip.sales_cai = #{salesCai,jdbcType=NVARCHAR}
                        or bip.supervisor_cai = #{salesCai,jdbcType=NVARCHAR})
                    </when>
                    <when test="isChannel">
                        and (dwrsr.channel_manager_cai = #{salesCai,jdbcType=NVARCHAR} or dwrsr.bu_manager_cai = #{salesCai,jdbcType=NVARCHAR}  OR dwrsr.channel_manager_cai =  #{salesCai,jdbcType=NVARCHAR}  OR dwrsr.bu_manager_cai =  #{salesCai,jdbcType=NVARCHAR}  )
                    </when>
                </choose>
            </if>	
    	 ) total_info
    	
    	
    	</if>
    </select>
    <select id="queryGrossProfitInfoByPartnerId" parameterType="map" resultMap="GrossProfitInfoMap">
        select
        eop.partner_id,
        #{salesChannel,jdbcType=VARCHAR} sales_channel,
        round(sum(bip.actual_margin_usd),2) actual_value,
        round(sum(bip.target_margin),2)  target_value
        from dw_sales_target_actual_performance_by_month bip
        left join wx_t_partner_o2o_enterprise eop on bip.distributor_id = eop.distributor_id
        left join wx_t_organization o on o.id = eop.partner_id
        where 1=1
        <if test="beginMonth  != null">
            and bip.month &gt;= #{beginMonth,jdbcType=NVARCHAR}
        </if>
        <if test="endMonth != null">
            and bip.month &lt;= #{endMonth,jdbcType=NVARCHAR}
        </if>
        <if test="partnerId != null">
            and o.id = #{partnerId,jdbcType=BIGINT}
        </if>
           and eop.partner_id != 9
        group by eop.partner_id
    </select>
    <!--private String searchType;//1.待审核列表;2.已审核列表-->
    <!--private String supplierFlag;//2.门头报价;3.完工确认-->
    <select id="queryMtkApplyTodoCount" parameterType="map" resultType="java.lang.Long">
            select count(1)
            from (
            select DISTINCT
            ma.id, ma.organization_name, ma.mkt_type, ma.create_by, ma.create_time, ma.current_step, ma.update_time, ma.flow_finish, mfn.status
            from wx_t_mkt_apply ma
            left join wx_t_mkt_flow_node mfn on ma.id = mfn.mkt_apply_id
            left join wx_t_mkt_history h on h.mkt_apply_id = ma.id and h.operator = mfn.operator
            where 1=1
            and mfn.operator = #{userId, jdbcType=BIGINT}
            and ma.current_step != 1
            and ma.current_step = mfn.step
            and ma.flow_finish = 0
              <if test="mktType == 'STORE_FRONT'">
                and (ma.mkt_type='STORE_FRONT' or ma.mkt_type='STORE_FRONT_OFFER' or ma.mkt_type='STORE_FRONT_REIMBURSE')
              </if>
                <if test="mktType == 'SEMINAR'">
                    and (ma.mkt_type='SEMINAR' or ma.mkt_type='SEMINAR_REIMBURSE')
                </if>
            ) aa
    </select>
    <select id="queryEvaluationList" parameterType="map" resultType="java.util.Date">
        SELECT
          distinct ep.evaluation_month
        FROM wx_t_evaluation_program ep
        <if test="programType == 'FLSR_Monthly_Itinerary_Plan'">
        LEFT JOIN wx_t_flsr_mon_iti_plan t1 ON ep.id = t1.program_id
        </if>
        <if test="programType == 'FLSR_Monthly_Report_Form'">
        LEFT JOIN wx_t_flsr_monthly_report_form t1 ON ep.id = t1.form_id
        </if>
          JOIN wx_t_user u1 on u1.user_id = t1.sales_id
          left join wx_t_user u2 on u2.user_id = t1.supervisor_id
        WHERE 1=1
              and ep.delete_flag =0
              and (ep.status >= 5 and t1.status >= 20)
              and ep.evaluation_month in
                 (<foreach collection="evaluationMonths" index="index" item="item" separator=",">
                    #{item}
                   </foreach>)
              <if test="programType != null">
              and ep.program_type = #{programType,jdbcType=VARCHAR}
              </if>
              <if test="salesId != null">
              and t1.sales_id = #{salesId,jdbcType=BIGINT}
              </if>
              <if test="bu != null">
              and ep.bu = #{bu,jdbcType=VARCHAR}
              </if>
        order by ep.evaluation_month desc
    </select>
    <select id="queryEvaluationFormBySupervisor" resultMap="AsmPerEvaFormResultMap" parameterType="map">
        select distinct ep.title, ep.evaluation_month, ep.status from wx_t_evaluation_program ep
        where ep.program_type = 'Sales_Manager_Performance_Evaluation_Form'
        and ((ep.status>=5 and ep.status&lt;100) or exists (select 1 from wx_t_asm_per_eva_form f where f.program_id=ep.id and f.status>=20
        <if test="channelManagerId != null">
            and f.channel_manager_id = #{channelManagerId}
        </if>
        ))
        <if test="month != null and month != ''">
            and ep.evaluation_month=#{month} + '-01'
        </if>
        <if test="salesChannel != null and salesChannel != ''">
            and ep.sales_channel = #{salesChannel}
        </if>
        <if test="bu != null and bu != ''">
            and ep.bu=#{bu}
        </if>
        /**从>=2019-03开始**/
        and ep.evaluation_month >= '2019-03-01 00:00:00'
        order by ep.evaluation_month desc
    </select>
    <select id="queryResponsiblePartnerTotalFund" parameterType="map" resultType="java.math.BigDecimal">
        select round((sum(isnull(fq.actual_CDM_IVI_Fund,0)* -1)
        +  sum(isnull(fq.actual_CDM_BD_Fund,0)*-1) + sum(isnull(fq.actual_CDM_Marketing_Fund,0)*-1)),2) FUND_TOTAL
        from dbo.dw_pp_customer_fund_quarter_new fq
            LEFT JOIN wx_t_partner_o2o_enterprise en ON en.distributor_id = fq.distributor_id
                <if test="cai != null">
                    LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = en.distributor_id
                    LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
                </if>
                  where 1 = 1
                    and en.partner_id != 9
                    <if test="cai != null">
                    and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
                    or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
                        <if test="salesChannel != null and salesChannel != ''">
                            and  dwrsr.sales_channel_name = #{salesChannel}
                        </if>
                    </if>
                    <if test="year">
                    and fq.Year = #{year}
                    </if>
    </select>
    <select id="getPromotionInfo" parameterType="map" resultType="java.lang.Double">
        select isnull(sum(pro.promotion),0) promotion_value
        from wx_t_promotion pro
        inner join wx_t_partner_o2o_enterprise eop on eop.partner_id = pro.partner_id
        <if test="cai != null">
            left join dw_customer_region_sales_supervisor_rel dwrssr on dwrssr.distributor_id = eop.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        </if>
        where 1=1
        <if test="cai != null">
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
        </if>
        <if test="partnerId != null">
            and eop.partner_id = #{partnerId}
        </if>
    </select>
    <!--查询店招或者研讨会对应的申请的次数-->
    <select id="getSeminarOrStoreFrontCountByCai" parameterType="map" resultType="java.lang.Long">
        select count(1)
        from wx_t_mkt_apply ma
        <if test="cai != null">
            left join dw_customer_region_sales_supervisor_rel dwrssr on dwrssr.distributor_id = ma.partner_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        </if>
        left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
        where 1=1
        and ma.flow_finish = 1
        <if test="mktType == 'STORE_FRONT'">
            and (ma.mkt_type='STORE_FRONT_OFFER')
        </if>
        <if test="mktType == 'SEMINAR'">
            and (ma.mkt_type='SEMINAR')
        </if>
        <if test="year">
            and year(ma.create_time) = #{year}
        </if>
        <if test="cai != null">
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
        </if>
    </select>
    <select id="getMktApplyCountByPartnerIdGroupByMktType" parameterType="map" resultMap="MktApplyCountInfoMap">
        select
        count(1) count,
        ma.mkt_type mkt_type
        from wx_t_mkt_apply ma
        left join wx_t_partner_o2o_enterprise eop on ma.partner_id = eop.distributor_id
        where 1=1
        and eop.partner_id = #{partnerId}
        and ma.flow_finish = 1
        <if test="mktType == 'STORE_FRONT'">
            and (ma.mkt_type = 'STORE_FRONT' or ma.mkt_type = 'STORE_FRONT_OFFER')
        </if>
        <if test="mktType == 'SEMINAR'">
            and (ma.mkt_type='SEMINAR' or ma.mkt_type='SEMINAR_REIMBURSE')
        </if>
        and year(ma.create_time) = #{year}
        group by ma.mkt_type
    </select>
    <select id="getNewShopImportCost" parameterType="map" resultMap="NewWorkshopInfoMap">
        select isnull(sum(recruitment_amount),0) new_shop_import_cost,
                count(1) new_shop_import_count
        from wx_t_work_shop w
        inner join wx_t_workshop_partner wp on w.id = wp.workshop_id
        inner join wx_t_partner_o2o_enterprise eop on eop.partner_id = wp.partner_id
        <if test="cai != null">
            left join dw_customer_region_sales_supervisor_rel dwrssr on dwrssr.distributor_id = eop.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        </if>
        where 1=1
        and recruitment_amount is not null
        <if test="cai != null">
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
        </if>
        <if test="partnerId != null">
            and eop.partner_id = #{partnerId}
        </if>
    </select>
    <select id="getUsedFundByCai" parameterType="map" resultType="java.math.BigDecimal">
	 select isnull(sum(convert(float,mf.field_value)),0)
        from wx_t_mkt_apply ma
        <if test="cai != null">
            left join dw_customer_region_sales_supervisor_rel dwrssr on dwrssr.distributor_id = ma.partner_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        </if>
	 left join wx_t_mkt_field mf on ma.id = mf.mkt_apply_id and mf.field_name = 'moneyCost'
	 where 1=1
	     and ma.flow_finish = 1
        <if test="mktType == 'STORE_FRONT'">
            and (ma.mkt_type='STORE_FRONT' or ma.mkt_type='STORE_FRONT_OFFER' or ma.mkt_type='STORE_FRONT_REIMBURSE')
        </if>
        <if test="mktType == 'SEMINAR'">
            and (ma.mkt_type='SEMINAR' or ma.mkt_type='SEMINAR_REIMBURSE')
        </if>
        <if test="year">
            and year(ma.create_time) = #{year}
        </if>
        <if test="cai != null">
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
        </if>
    </select>
    <select id="getPartnerSellThroughInfoByDay" parameterType="map" resultMap="PartnerSalesInfoMap">
        SELECT out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
        , SUM(out_stock_detail.actual_out_count * out_stock_detail.capacity) AS sell_through
        FROM (
        SELECT isnull(osp.actual_out_count, 0) AS actual_out_count
        , convert(float, p1.capacity) AS capacity, t_workshop_partner.partner_id
        , datepart(yy, t_outstock.out_time) AS year_str
        , datepart(mm, t_outstock.out_time) AS month_str
        , datepart(dd, t_outstock.out_time) AS day_str
        FROM wx_t_out_stock_product osp
        LEFT JOIN wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
        LEFT JOIN wx_t_product p1 ON p1.sku = osp.sku
        LEFT JOIN wx_t_order o ON o.order_no = t_outstock.order_no
        LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
        WHERE 1=1
        AND o.order_type != 'DA'
        AND o.order_type != 'DP'
        AND o.order_type != 'SPDA'
        AND o.status IN ('5', '7')
        and t_outstock.out_time &gt;= #{startTime}
        and t_outstock.out_time &lt; #{endTime}
        <if test="partnerId != null">
        AND t_workshop_partner.partner_id = #{partnerId}
        </if>
        ) out_stock_detail
        GROUP BY out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
        order by  out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
    </select>
    <select id="getPartnerSellThroughInfoByMonth" parameterType="map" resultMap="PartnerSalesInfoMap">
        SELECT out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
        , SUM(out_stock_detail.actual_out_count * out_stock_detail.capacity) AS sell_through
        FROM (
        SELECT isnull(osp.actual_out_count, 0) AS actual_out_count
        , convert(float, p1.capacity) AS capacity, t_workshop_partner.partner_id
        , datepart(yy, t_outstock.out_time) AS year_str
        , datepart(mm, t_outstock.out_time) AS month_str
        , datepart(dd, t_outstock.out_time) AS day_str
        FROM wx_t_out_stock_product osp
        LEFT JOIN wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
        LEFT JOIN wx_t_product p1 ON p1.sku = osp.sku
        LEFT JOIN wx_t_order o ON o.order_no = t_outstock.order_no
        LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
        WHERE 1=1
        AND o.order_type != 'DA'
        AND o.order_type != 'DP'
        AND o.order_type != 'SPDA'
        AND o.status IN ('5', '7')
        AND p1.product_property = 'SN Above'
        and t_outstock.out_time &gt;= #{startTime}
        and t_outstock.out_time &lt; #{endTime}
        <if test="partnerId != null">
            AND t_workshop_partner.partner_id = #{partnerId}
        </if>
        ) out_stock_detail
        GROUP BY out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
        order by  out_stock_detail.year_str, out_stock_detail.month_str, out_stock_detail.day_str
    </select>
    <select id="queryNewWorkshopList" parameterType="com.chevron.newhomepage.model.QueryWorkshopScanInfoParam" resultMap="WorkShopScanInfoMap">
        select  distinct
            a.workshop_id workshop_id,
            ws.work_shop_name,
            o.organization_name sp_name,
            a.lt create_time
        from
          (select min(ws.create_time) lt,
              ws.workshop_id from wx_t_workshop_status ws where ws.workshop_with_status=3
        group by ws.workshop_id) a
        left join wx_t_workshop_partner wp on a.workshop_id=wp.workshop_id
        left join wx_t_work_shop ws on a.workshop_id =  ws.id
        left join wx_t_organization o on o.id=wp.partner_id
        left join wx_t_partner_o2o_enterprise poe on poe.partner_id = wp.partner_id
        LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = poe.distributor_id
        LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        where 1=1
          and a.lt  &gt;= #{startTime}
          and a.lt &lt; #{endTime}
          and dwrsr.sales_channel_name = #{salesChannel}
          and wp.partner_id = #{partnerId}
    </select>
    <!--这个是新招店铺-->
    <select id="queryNewWorkshopCount" parameterType="map" resultType="java.lang.Long">
        select  isnull(count(1),0) workshop_count from (select min(ws.create_time) lt,
        ws.workshop_id from wx_t_workshop_status ws  left join wx_t_work_shop shop on ws.workshop_id = shop.id
            where 1=1
                and ws.workshop_with_status = 3
                <if test="onlyRecruitment">
                and shop.shop_recruitment = 1
                </if>
        group by ws.workshop_id) a
        left join wx_t_workshop_partner wp on a.workshop_id=wp.workshop_id
        left join wx_t_organization o on o.id=wp.partner_id
        left join wx_t_partner_o2o_enterprise poe on poe.partner_id = wp.partner_id
        <if test="cai != null">
        LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = poe.distributor_id
        LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        </if>
        where 1=1
        and year(a.lt) = #{year}
        <if test="cai != null">
            <if test="salesChannel != null">
              and dwrsr.sales_channel_name = #{salesChannel}
            </if>
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
                 or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
        </if>
    </select>
    <select id="queryScanInfoByWorkshopAndMonth" parameterType="map" resultMap="WorkshopMonthScanInfoMap">
        select
          t_workshop_partner.workshop_id,
          sum(isnull(osp.actual_out_count, 0) * convert(float, p1.capacity)) liters,
          month(t_outstock.out_time) [month]
        from wx_t_out_stock_product osp
          LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
          left join wx_t_product p1 on p1.sku = osp.sku
          left join wx_t_order o on t_outstock.order_no = o.order_no
          left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
        WHERE 1=1
            and t_workshop_partner.workshop_id in
                (<foreach collection="workshopIds" index="index" item="item" separator=",">
                #{item}
                </foreach>)
          and p1.product_property = 'SN Above'
          and year(t_outstock.out_time) = #{year}
          and o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7')
        group by t_workshop_partner.workshop_id, month(t_outstock.out_time)
    </select>
    <select id="queryScanInfoByWorkshopAndMonthAndSku" parameterType="map" resultMap="WorkshopMonthScanInfoMap">
        select
          t_workshop_partner.workshop_id,
          sum(isnull(osp.actual_out_count, 0) * convert(float, p1.capacity)) liters,
          month(t_outstock.out_time) [month],
          p1.name product_name
        from wx_t_out_stock_product osp
        LEFT join wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
        left join wx_t_product p1 on p1.sku = osp.sku
        left join wx_t_order o on t_outstock.order_no = o.order_no
        left join wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
        WHERE 1=1
          and t_workshop_partner.workshop_id in
            (<foreach collection="workshopIds" index="index" item="item" separator=",">
            #{item}
        </foreach>)
        and p1.product_property = 'SN Above'
        and year(t_outstock.out_time) = #{year}
        and o.order_type != 'DA' and o.order_type != 'DP' and o.order_type != 'SPDA' and o.status in ('5', '7')
        group by t_workshop_partner.workshop_id, month(t_outstock.out_time) ,p1.name
        order by [month] asc
    </select>
    <select id="selPartnersBySalesRelationForPage"  resultMap="PartnerBaseResultMap" parameterType="com.chevron.newhomepage.model.PerfGridSpParam" >
        select
        v.*,t.*,
        (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='partner.partnerProperty' and di1.dic_item_code=poe.partner_property) partner_property_text,
        case when poe.partner_property = 'NORMAL' then 10 else 1 end as partner_property_level,
        left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) organizationNamePy
        <if test="withCustomerGrade">
            ,(select top 1 pcg.grade from dbo.dw_pp_customer_grade pcg where pcg.distributor_id = poe.distributor_id and pcg.sales_channel_name = 'CDM') as Customer_Grade
        </if>
        from
        view_customer_region_sales_channel v
		LEFT JOIN wx_t_partner_o2o_enterprise poe ON v.distributor_id = poe.distributor_id
		left join wx_t_organization t on poe.partner_id = t.id 
        where 1=1
        <!-- t.type=1 and t.status=1 -->
        <!-- and t.id != 9 -->
        <!-- <if test="partnerId != null">
          and t.id = #{partnerId}
        </if> -->
        <!-- <choose>
            <when test="spResource">
                and t.type = 1 and t.status = 1
            </when>
            <when test="includeSpWithNoWs">
                and (not exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id) or exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$))
            </when>
            <otherwise>
                and exists (select 1 from wx_t_workshop_partner wp1 where wp1.partner_id=t.id $Permission_Clause$)
            </otherwise>
        </choose> -->
        <!-- <if test="regionId != null">
            and exists (select 1 from wx_t_region_partner i1 where i1.region_id=#{regionId,jdbcType=BIGINT} and i1.partner_id=t.id)
        </if>
        <if test="permission == 'loginUser' and !chevron">
            and t.id=#{loginUserOrg,jdbcType=BIGINT}
        </if>
        <if test="partnerName != null">
            and (t.organization_name like '%' + #{partnerName} + '%' or left(dbo.f_GetPyToAboutHanyu(t.organization_name),500) LIKE '%' + #{partnerName} + '%')
        </if>
        <if test="salesChannel != null and salesChannel != ''">
            and exists (select 1 from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel crss on pe.distributor_id=crss.distributor_id
            left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
            where pe.partner_id=t.id and rsc.sales_channel_name=#{salesChannel})
        </if> -->
        <if test="chevron">
            <choose>
                <!--如果这查询的这个人有channelManagerCai，需要这样过滤-->
                <when test="channelManagerCai != null and channelManagerCai != ''">
					<!--                     and exists (select 1
                    from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel crss
                    on pe.distributor_id = crss.distributor_id
                    left join dbo.dw_region_sales_channel_rel channel_rel on crss.region_name = channel_rel.region_name
                    where 1 = 1 and pe.partner_id=t.id and (channel_rel.channel_manager_cai = #{channelManagerCai}) -->
                    and v.channel_manager_cai = #{channelManagerCai}
                </when>
                <!--如果这查询的这个人有buManagerCai，需要这样过滤-->
                <when test="buManagerCai != null and buManagerCai != ''">
                    <!-- and exists (select 1
                    from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel crss
                    on pe.distributor_id = crss.distributor_id
                    left join dbo.dw_region_sales_channel_rel channel_rel on crss.region_name = channel_rel.region_name
                    where 1 = 1 and pe.partner_id=t.id and (channel_rel.bu_manager_cai = #{buManagerCai})) -->
                    and v.bu_manager_cai = #{buManagerCai}
                </when>
                <!--如果有supervisorManagerCai，需要这样过滤-->
                <when test="supervisorManagerCai != null and supervisorManagerCai != ''">
                    <!-- and exists (select 1
                    from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel crss
                    on pe.distributor_id = crss.distributor_id
                    where 1 = 1 and pe.partner_id=t.id and crss.suppervisor_cai = #{supervisorManagerCai}) -->
                    and v.suppervisor_cai = #{supervisorManagerCai}
                </when>
                <!--如果有salesCai，需要这样过滤-->
                <when test="salesCai != null and salesCai != ''">
                    <!-- and exists (select 1
                    from wx_t_partner_o2o_enterprise pe left join dw_customer_region_sales_supervisor_rel crss
                    on pe.distributor_id = crss.distributor_id
                    where 1 = 1 and pe.partner_id=t.id and crss.sales_cai = #{salesCai}) -->
                    and v.sales_cai = #{salesCai}
                </when>
            </choose>
            <!--其他的人按照角色来-->
                <!-- and exists(select 1
                from wx_t_partner_o2o_enterprise pe inner join
                dw_customer_region_sales_supervisor_rel crss on pe.distributor_id = crss.distributor_id
                where
                1 = 1
                and pe.partner_id = t.id and pe.distributor_id > 0 and pe.distributor_id not in (
                select dic_item_code
                from wx_t_dic_item
                where dic_type_code = 'qbr.exclude.distributorIds' and status = 1
                )) -->
        </if>
    </select>
    <select id="getTargetQuarterlyByPartnerIds" resultMap="DwPpSalesTargetMap">
        select round(isnull(sum(target_volume), 0),2) target_value,poe.partner_id, #{quarter,jdbcType=INTEGER} quarter
        from dw_pp_sales_target_by_category_by_month t
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = t.distributor_id
        where 1=1
        and poe.partner_id != 9
        and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
        <if test="partnerId != null">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        <if test="partnerIds != null">
            and poe.partner_id in (
            <foreach collection="partnerIds" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="cdmProductCategory != null">
            and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
        </if>
        group by poe.partner_id
    </select>
        <select id="getTargetByPartnerIds" resultMap="DwPpSalesTargetMap">
		SELECT
			round( isnull( SUM ( target_volume ), 0 ), 2 ) target_value,
			poe.partner_id,
			month
		FROM
			dw_pp_sales_target_by_category_by_month t
			LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.distributor_id = t.distributor_id 
		WHERE
			1 = 1 
			AND poe.partner_id != 9 
        <if test="partnerId != null">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        <if test="partnerIds != null and partnerIds.size()>0">
            and poe.partner_id in (
            <foreach collection="partnerIds" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="cdmProductCategory != null">
            and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
        </if>
        group by poe.partner_id,
	month
    </select>
    <select id="getTargetQuarterlyByPartnerIdsAndMonth" parameterType="map" resultMap="DwPpSalesTargetMap">
        select round(isnull(sum(target_volume), 0)*3/7,2) target_value,
        poe.partner_id,
        CAST(SUBSTRING(t.month,1,4) as int) year,
        CAST(SUBSTRING(t.month,5,6) as int) month
        from dw_pp_sales_target_by_category_by_month t
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = t.distributor_id
        where 1=1
        and poe.partner_id != 9
        and month BETWEEN #{startDateStr, jdbcType=NVARCHAR} and #{endDateStr, jdbcType=NVARCHAR}
        <if test="partnerId != null">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        <if test="partnerIds != null and partnerIds.size()>0">
            and poe.partner_id in (
            <foreach collection="partnerIds" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="cdmProductCategory != null">
            and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
        </if>
        group by poe.partner_id,month
    </select>
    <select id="getPartnerSellThroughInfoByMonthAndPartnerId" parameterType="map" resultMap="PartnerSalesInfoMap">
        SELECT out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str
        , SUM(out_stock_detail.actual_out_count * out_stock_detail.capacity) AS sell_through
        FROM (
        SELECT isnull(osp.actual_out_count, 0) AS actual_out_count
        , convert(float, p1.capacity) AS capacity, t_workshop_partner.partner_id
        , datepart(yy, t_outstock.out_time) AS year_str
        , datepart(mm, t_outstock.out_time) AS month_str
        FROM wx_t_out_stock_product osp
        LEFT JOIN wx_t_out_stock t_outstock ON t_outstock.stock_out_no = osp.stock_out_no
        LEFT JOIN wx_t_product p1 ON p1.sku = osp.sku
        LEFT JOIN wx_t_order o ON o.order_no = t_outstock.order_no
        LEFT JOIN wx_t_workshop_partner t_workshop_partner ON t_workshop_partner.workshop_id = o.work_shop_id
        WHERE 1=1
        AND o.order_type != 'DA'
        AND o.order_type != 'DP'
        AND o.order_type != 'SPDA'
        AND o.status IN ('5', '7')
        AND p1.product_property = 'SN Above'
        and t_outstock.out_time &gt;= #{startDate}
        and t_outstock.out_time &lt;= #{endDate}
        <if test="partnerId != null">
            and t_workshop_partner.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        <if test="partnerIds != null and partnerIds.size()>0">
            and t_workshop_partner.partner_id in (
            <foreach collection="partnerIds" index="index" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ) out_stock_detail
        GROUP BY out_stock_detail.partner_id, out_stock_detail.year_str, out_stock_detail.month_str
        order by  out_stock_detail.year_str, out_stock_detail.month_str
    </select>
    <select id="getTotalTargetQuarterlyByCai" parameterType="map" resultType="java.lang.Double">
        select isnull(sum(target_volume), 0) target_value
        from dw_pp_sales_target_by_category_by_month t
        LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = t.distributor_id
        LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
        where 1=1
        and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
        <!--<if test="cdmProductCategory != null">-->
            <!--and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}-->
        <!--</if>-->
        <if test="salesChannel != null and salesChannel != ''">
            and dwrsr.sales_channel_name = 'indirect'
        </if>
        <if test="cdmProductCategory != null">
            and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
        </if>
        <if test="cai != null">
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
        </if>
    </select>
    <select id="getTotalTargetByPartnerId" parameterType="map" resultType="java.lang.Double">
        select round(isnull(sum(target_volume), 0),2) target_value
        from dw_pp_sales_target_by_category_by_month t
        left join wx_t_partner_o2o_enterprise eop on t.distributor_id = eop.distributor_id
        where 1=1
        and t.region like concat(#{salesChannel},'%')
        and eop.partner_id = #{partnerId}
        and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
        <if test="cdmProductCategory != null">
            and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
        </if>
        group by
        eop.partner_id
    </select>
    <select id="getTotalTargetByCaiOrPartnerAndType" parameterType="map" resultMap="DwPpSalesTargetMap">
        <choose>
        <when test="cai != null">
            select round(isnull(sum(target_volume), 0),2) target_value,t.cdm_product_category
            from dw_pp_sales_target_by_category_by_month t
            LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = t.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
            where 1=1
            and t.region like concat(#{salesChannel},'%')
            and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
            <!--<if test="cdmProductCategory != null">-->
            <!--and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}-->
            <!--</if>-->
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
            <if test="cdmProductCategory != null">
                and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
            </if>
              and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
              or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            group by
              t.cdm_product_category
        </when>
        <when test="partnerId != null">
            select round(isnull(sum(target_volume), 0),2) target_value,t.cdm_product_category
              from dw_pp_sales_target_by_category_by_month t
            left join wx_t_partner_o2o_enterprise eop on t.distributor_id = eop.distributor_id
              where 1=1
            and t.region like concat(#{salesChannel},'%')
            and eop.partner_id = #{partnerId}
            and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
            <if test="cdmProductCategory != null">
                and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
            </if>
            group by
              t.cdm_product_category
        </when>
          <otherwise>
              select round(isnull(sum(target_volume), 0),2) target_value,t.cdm_product_category
              from dw_pp_sales_target_by_category_by_month t
              LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = t.distributor_id
              LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
              where 1=1
              and t.region like concat(#{salesChannel},'%')
              and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
              and dwrsr.sales_channel_name = #{salesChannel}
              <if test="cdmProductCategory != null">
                  and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
              </if>
              group by
              t.cdm_product_category
          </otherwise>
        </choose>
    </select>
    <select id="getTotalTargetByCaiOrPartnerAndMonth" parameterType="map" resultMap="DwPpSalesTargetMap">
        <choose>
        <when test="cai != null">
            select round(isnull(sum(target_volume), 0),2) target_value,CAST(SUBSTRING(t.month,1,4) as int) year,CAST(SUBSTRING(t.month,5,6) as int) month
            from dw_pp_sales_target_by_category_by_month t
            LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = t.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
            where 1=1
            and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
            <if test="salesChannel != null and salesChannel != ''">
                and dwrsr.sales_channel_name = #{salesChannel}
            </if>
            <if test="cdmProductCategory != null">
                and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
            </if>
            and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
            or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
            group by
            t.month
        </when>
        <when test="partnerId != null">
            select round(isnull(sum(target_volume), 0),2) target_value,CAST(SUBSTRING(t.month,1,4) as int) year, CAST(SUBSTRING(t.month,5,6) as int) month,eop.partner_id
            from dw_pp_sales_target_by_category_by_month t
            left join wx_t_partner_o2o_enterprise eop on t.distributor_id = eop.distributor_id
            where 1=1
            and eop.partner_id = #{partnerId}
            and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
            <if test="cdmProductCategory != null">
                and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
            </if>
            group by
            t.month,eop.partner_id
        </when>
        <otherwise>
            select round(isnull(sum(target_volume), 0),2) target_value,CAST(SUBSTRING(t.month,1,4) as int)
            year,CAST(SUBSTRING(t.month,5,6) as int) month
            from dw_pp_sales_target_by_category_by_month t
            where 1=1
            and month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
            <if test="cdmProductCategory != null">
                and t.cdm_product_category = #{cdmProductCategory, jdbcType=NVARCHAR}
            </if>
            group by
                t.month
            </otherwise>
        </choose>
    </select>
    <select id="queryChannelManagerId" parameterType="map" resultType="java.lang.Long">
        select top 1 u.user_id
        FROM
        dw_customer_region_sales_supervisor_rel dwrssr
        left join
        dw_region_sales_channel_rel dwrsr ON
        dwrsr.region_name = dwrssr.region_name
        LEFT JOIN wx_t_user u ON u.cai = dwrsr.channel_manager_cai
        where 1 = 1
        and dwrssr.suppervisor_cai = #{suppervisorCai}
    </select>
    <!--flsr x条精英计划兑换待审核-->
    <select id="elitePlanExchangePendingReviewCount" parameterType="map" resultType="java.lang.Long">
        select count(1) from wx_t_rebate_apply_v2 ra WHERE status+0 >= 1
                and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                and operator = #{userId, jdbcType=BIGINT}
                and status ='-1')
    </select>
    <!--ASM x条精英计划基金花费申请待审核-->
    <select id="elitePlanExpensePendingReviewCount" parameterType="map" resultType="java.lang.Long">
        select count(1) from wx_t_rebate_apply_v2 ra WHERE status='1'
                and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                and operator = #{userId, jdbcType=BIGINT}
                and status !='1')
    </select>
    <!-- asm x条精英计划兑换待审核 -->
    <select id="elitePlanExchangePendingReviewCountForAsm" parameterType="map" resultType="java.lang.Long">
        select count(1) from wx_t_rebate_apply_v2 ra WHERE status+0 >= 6 AND current_step=4 and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                and operator = #{userId, jdbcType=BIGINT}
                and status!='1') AND fund_ownership='CDM'
    </select>
    <select id="elitePlanExchangePendingReviewCountForCm" parameterType="map" resultType="java.lang.Long">
        select count(1) from wx_t_rebate_apply_v2 ra WHERE status+0 >= 8 AND current_step=5
                        AND create_by in (SELECT DISTINCT u.user_id
                        FROM dw_customer_region_sales_supervisor_rel crss
                        LEFT JOIN wx_t_user u ON crss.sales_cai = u.cai
                        WHERE sales_cai != suppervisor_cai AND u.status=1 and u.user_id NOT IN (
                        SELECT user_id FROM dw_customer_region_sales_supervisor_rel crss LEFT JOIN wx_t_user u ON
                        crss.suppervisor_cai = u.cai WHERE sales_cai = suppervisor_cai
                        AND crss.region_name in (select region_name from dw_region_sales_channel_rel where sales_channel_name= 'CDM')))
                        AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 6
                        and operator = #{userId, jdbcType=BIGINT})
    </select>
</mapper>