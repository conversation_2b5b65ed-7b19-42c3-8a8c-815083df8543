<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.answerquestion.dao.AnswerLogMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.answerquestion.model.AnswerLog">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="title" property="title" jdbcType="VARCHAR"/>
		<result column="real_answer" property="realAnswer" jdbcType="VARCHAR"/>
		<result column="submit_answer" property="submitAnswer" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="submit_num" property="submitNum" jdbcType="INTEGER"/>
		<result column="login_name" property="loginName" jdbcType="VARCHAR"/>
		<result column="partner" property="partner" jdbcType="VARCHAR"/>
		<result column="login_date" property="loginDate" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,title,real_answer,submit_answer,status,submit_num,login_name,partner,login_date
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.answerquestion.model.AnswerLog">
		update wx_t_answer_log set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.answerquestion.model.AnswerLog">
		update wx_t_answer_log
		<set>
			<if test="title != null" >
				title = #{title,jdbcType=VARCHAR},
			</if>
			<if test="realAnswer != null" >
				real_answer = #{realAnswer,jdbcType=VARCHAR},
			</if>
			<if test="submitAnswer != null" >
				submit_answer = #{submitAnswer,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="submitNum != null" >
				submit_num = #{submitNum,jdbcType=INTEGER},
			</if>
			<if test="loginName != null" >
				login_name = #{loginName,jdbcType=VARCHAR},
			</if>
			<if test="partner != null" >
				partner = #{partner,jdbcType=VARCHAR},
			</if>
			<if test="loginDate != null" >
				login_date = #{loginDate,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.answerquestion.model.AnswerLogExample">
    	delete from wx_t_answer_log
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.answerquestion.model.AnswerLog">
		insert into wx_t_answer_log
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="title != null">
				title,
			</if>
			<if test="realAnswer != null">
				real_answer,
			</if>
			<if test="submitAnswer != null">
				submit_answer,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="submitNum != null">
				submit_num,
			</if>
			<if test="loginName != null">
				login_name,
			</if>
			<if test="partner != null">
				partner,
			</if>
			<if test="loginDate != null">
				login_date,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="title != null">
				#{title,jdbcType=VARCHAR},
			</if>
			<if test="realAnswer != null">
				#{realAnswer,jdbcType=VARCHAR},
			</if>
			<if test="submitAnswer != null">
				#{submitAnswer,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="submitNum != null">
				#{submitNum,jdbcType=INTEGER},
			</if>
			<if test="loginName != null">
				#{loginName,jdbcType=VARCHAR},
			</if>
			<if test="partner != null">
				#{partner,jdbcType=VARCHAR},
			</if>
			<if test="loginDate != null">
				#{loginDate,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_answer_log
		<set>
			<if test="record.title != null">
				title = #{record.title,jdbcType=VARCHAR},
			</if>
			<if test="record.realAnswer != null">
				real_answer = #{record.realAnswer,jdbcType=VARCHAR},
			</if>
			<if test="record.submitAnswer != null">
				submit_answer = #{record.submitAnswer,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.submitNum != null">
				submit_num = #{record.submitNum,jdbcType=INTEGER},
			</if>
			<if test="record.loginName != null">
				login_name = #{record.loginName,jdbcType=VARCHAR},
			</if>
			<if test="record.partner != null">
				partner = #{record.partner,jdbcType=VARCHAR},
			</if>
			<if test="record.loginDate != null">
				login_date = #{record.loginDate,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.answerquestion.model.AnswerLogExample">
		delete from wx_t_answer_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.answerquestion.model.AnswerLogExample" resultType="int">
		select count(1) from wx_t_answer_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 前20000条记录-->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.answerquestion.model.AnswerLogExample">
		select top 20000
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_answer_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.answerquestion.model.AnswerLogExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_answer_log
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.title, t1.real_answer, t1.submit_answer, t1.status, t1.submit_num, t1.login_name, t1.partner,
			 t1.login_date
		  from wx_t_answer_log t1
		 where 1=1
	</select>
</mapper>
