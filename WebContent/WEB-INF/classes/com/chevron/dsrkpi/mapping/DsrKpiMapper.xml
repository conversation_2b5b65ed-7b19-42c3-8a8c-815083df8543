<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi.dao.DsrKpiMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi.model.DsrKpi">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="kpi_code" property="kpiCode" jdbcType="VARCHAR"/>
		<result column="kpi_value" property="kpiValue" jdbcType="NUMERIC"/>
		<result column="award_point" property="awardPoint" jdbcType="NUMERIC"/>
		<result column="effictive_date" property="effictiveDate" jdbcType="DATE"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,dsr_id,kpi_code,kpi_value,award_point,effictive_date,ext_property1,ext_property2,ext_property3,delete_flag,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi.model.DsrKpi">
		update wx_t_dsr_kpi set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi.model.DsrKpi">
		update wx_t_dsr_kpi
		<set>
			<if test="dsrId != null" >
				dsr_id = #{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiCode != null" >
				kpi_code = #{kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="kpiValue != null" >
				kpi_value = #{kpiValue,jdbcType=NUMERIC},
			</if>
			<if test="awardPoint != null" >
				award_point = #{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="effictiveDate != null" >
				effictive_date = #{effictiveDate,jdbcType=DATE},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi.model.DsrKpiExample">
    	delete from wx_t_dsr_kpi
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi.model.DsrKpi">
		insert into wx_t_dsr_kpi
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="dsrId != null">
				dsr_id,
			</if>
			<if test="kpiCode != null">
				kpi_code,
			</if>
			<if test="kpiValue != null">
				kpi_value,
			</if>
			<if test="awardPoint != null">
				award_point,
			</if>
			<if test="effictiveDate != null">
				effictive_date,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="dsrId != null">
				#{dsrId,jdbcType=BIGINT},
			</if>
			<if test="kpiCode != null">
				#{kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="kpiValue != null">
				#{kpiValue,jdbcType=NUMERIC},
			</if>
			<if test="awardPoint != null">
				#{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="effictiveDate != null">
				#{effictiveDate,jdbcType=DATE},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_dsr_kpi
		<set>
			<if test="record.dsrId != null">
				dsr_id = #{record.dsrId,jdbcType=BIGINT},
			</if>
			<if test="record.kpiCode != null">
				kpi_code = #{record.kpiCode,jdbcType=VARCHAR},
			</if>
			<if test="record.kpiValue != null">
				kpi_value = #{record.kpiValue,jdbcType=NUMERIC},
			</if>
			<if test="record.awardPoint != null">
				award_point = #{record.awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="record.effictiveDate != null">
				effictive_date = #{record.effictiveDate,jdbcType=DATE},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
<!-- 	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi.model.DsrKpiExample">
		delete from wx_t_dsr_kpi
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete> -->
	<!-- 逻辑删除 -->
	<update id="deleteByIds" parameterType="List">
	     update wx_t_dsr_kpi set       
	       delete_flag = 1
	       where id in
	       <if test="ids != null">
			    <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
			          #{item, jdbcType=BIGINT}
			     </foreach>
		    </if>
	</update>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi.model.DsrKpiExample" resultType="int">
		select count(1) from wx_t_dsr_kpi
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.DsrKpiExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_dsr_kpi
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.DsrKpiExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_dsr_kpi
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.dsr_id, t1.kpi_code, t1.kpi_value, t1.award_point, t1.effictive_date, t1.ext_property1,
			 t1.ext_property2, t1.ext_property3, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_dsr_kpi t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.DsrKpiParams">
		select t1.id, t1.dsr_id, t1.kpi_code, t1.kpi_value, t1.award_point, t1.effictive_date, t1.ext_property1,
			 t1.ext_property2, t1.ext_property3, t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_dsr_kpi t1
		 where t1.delete_flag=0
	</select>
	
	<select id="queryForKpiMonth" resultType="com.chevron.dsrkpi.model.DsrKpiMonth" parameterType="map">
	     select u1.user_id "suppervisorId", u1.ch_name "suppervisorName", crsc.region_name "区域", 
			  u2.user_id "channelManagerId", u2.ch_name "channelManagerName",
			   o.organization_name "经销商", crsc.sales_name "销售", u.ch_name "业务员"  , dk.*
			   from wx_t_dsr_kpi dk
			   left join wx_t_user u on u.user_id = dk.dsr_id
			    left JOIN wx_t_organization o on u.org_id = o.id and o.type = 1
			    LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
			    LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 > 0
				LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'
			    left join wx_t_user u1 on u1.cai=crsc.suppervisor_cai and u1.status = 1
					left join wx_t_user u2 on u2.cai=crsc.channel_manager_cai and u2.status = 1
			   where 1=1
			   <if test="dsrId != null">
			       and dk.dsr_id = #{dsrId, jdbcType=BIGINT}
			   </if>
			   <if test="fromDate != null">
			      and dk.create_time &gt; #{fromDate,jdbcType=TIMESTAMP}
			   </if>
			   <if test="toDate != null">
			       and dk.create_time &lt; #{toDate, jdbcType=TIMESTAMP}
			   </if>			       
	</select>
	<select id="queryForKpiMonthRankPage" resultType="com.chevron.dsrkpi.model.DsrKpiMonth" parameterType="map">
	      select  ROW_NUMBER() over(partition BY crsc.region_name order by dk.kpi_value), crsc.region_name,dk.dsr_id, dk.kpi_value ,crsc.region_name "区域", 
			   o.organization_name "经销商", crsc.sales_name "销售", u.ch_name "业务员" 
	           from view_customer_region_sales_channel crsc
			   LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'	
	           LEFT JOIN wx_t_partner_o2o_enterprise pe ON crsc.distributor_id = pe.distributor_id
	           LEFT JOIN  wx_t_organization o ON pe.partner_id = o.id
	           LEFT JOIN wx_t_user u on u.org_id = o.id and o.type = 1
	           LEFT JOIN wx_t_dsr_kpi dk on u.user_id = dk.dsr_id
			   where 1=1
			   AND crsc.channel_weight &amp; 2 > 0
			   <if test="fromDate != null">
			      and dk.create_time &gt; #{fromDate,jdbcType=TIMESTAMP}
			   </if>
			   <if test="toDate != null">
			       and dk.create_time &lt; #{toDate, jdbcType=TIMESTAMP}
			   </if>			       
	</select>
	<insert id="insertForeach" parameterType="java.util.List" useGeneratedKeys="false">
    			insert into wx_t_dsr_kpi
    			( dsr_id,kpi_code,kpi_value,award_point,
    			effictive_date,ext_property1,ext_property2,ext_property3,
    			delete_flag,create_user_id,create_time,
    			update_user_id,update_time)
    			values
    			<foreach collection="list" item="item" index="index" separator=",">
    				(
    					#{item.dsrId,jdbcType=BIGINT},
    					#{item.kpiCode,jdbcType=VARCHAR},
    					#{item.kpiValue,jdbcType=NUMERIC},
    					#{item.awardPoint,jdbcType=NUMERIC},
    					#{item.effictiveDate,jdbcType=DATE},
    					#{item.extProperty1,jdbcType=VARCHAR},
    					#{item.extProperty2,jdbcType=VARCHAR},
    					#{item.extProperty3,jdbcType=VARCHAR},
    					#{item.deleteFlag,jdbcType=INTEGER},
    					#{item.createUserId,jdbcType=BIGINT},
    					#{item.createTime,jdbcType=TIMESTAMP},
    					#{item.updateUserId,jdbcType=BIGINT},
    					#{item.updateTime,jdbcType=TIMESTAMP}
    				)
    		     </foreach>		
    </insert>
    
    <select id="select">
         SELECT dk.dsr_id, dk.kpi_value,<!-- crsc.region_name ,crsc.region_name "区域", --> 
         tr.value_after_transform region_name,tr.value_after_transform "区域",
			   o.organization_name "经销商", crsc.sales_name "销售", u.ch_name "业务员" 
        from wx_t_dsr_kpi dk
        LEFT JOIN wx_t_user u on u.user_id = dk.dsr_id
        LEFT JOIN  wx_t_organization o on u.org_id = o.id and o.type = 1
        left JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
        LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 > 0
		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'
			<!-- 	LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = pe.distributor_id			 -->
        where 1=1
    </select>
   
   <resultMap id="pointPersonalMap" type="com.chevron.dsrkpi.model.PointPersonal">
		<result column="dsr_id" property="dsrId" jdbcType="BIGINT"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="asm_name" property="asmName" jdbcType="VARCHAR"/>
		<result column="organization_name" property="organizationName" jdbcType="VARCHAR"/>			
		<result column="sale_name" property="saleName" jdbcType="VARCHAR"/>
		<result column="dsr_name" property="dsrName" jdbcType="VARCHAR"/>
		<result column="points_double" property="pointsDouble" jdbcType="NUMERIC"/>
		<result column="residue_double" property="residueDouble" jdbcType="NUMERIC"/>
	</resultMap>
    <select id="selectPointPersonal" resultMap="pointPersonalMap"  parameterType="com.chevron.dsrkpi.model.DsrKpiParams">
        select  <!-- crss.region_name --> tr.value_after_transform "region_name", crsc.suppervisor_name "asm_name",
			   o.organization_name "organization_name", crsc.sales_name "sale_name", u.ch_name "dsr_name",u.user_id "dsr_id",
			   <include refid="personalPointsDouble"/>              
	           from view_customer_region_sales_channel crsc
			   LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'	
	           LEFT JOIN wx_t_partner_o2o_enterprise pe ON crsc.distributor_id = pe.distributor_id
	           LEFT JOIN  wx_t_organization o ON pe.partner_id = o.id
	           LEFT JOIN wx_t_user u on u.org_id = o.id and o.type = 1	
	           LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
			   where 1=1
			   AND NOT EXISTS (SELECT 1 FROM wx_t_tmm_info tm WHERE tm.dsr_id = u.user_id AND tm.from_source = 2 )
			   AND crsc.channel_weight &amp; 2 > 0
			   AND u.status = 1 
			   <if test="dsrId != null and dsrId != ''">
			   	AND u.user_id = #{dsrId}
			   </if>
			   <!-- and exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' 
                  and vtm1.value_after_transform=rscr.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I']]>, 'CDM')) -->
			<!--    and u.user_id in (
                      SELECT
						dk1.dsr_id
					FROM
						wx_t_dsr_kpi dk1
					WHERE
						dk1.award_point != 0
            ) -->
            <!-- and crss.region_name LIKE <![CDATA['C&I%']]> -->
            <include refid="permissionCondition"/>
            <if test="regionName != null">
             <!--   and  crsc.region_name = #{regionName,jdbcType=VARCHAR} -->
               and  tr.value_after_transform = #{regionName,jdbcType=VARCHAR}
            </if>
            <if test="asmName != null">
               and  crsc.suppervisor_name like '%' + #{asmName,jdbcType=VARCHAR} + '%'
            </if>
            <if test="organizationId != null">
               and  o.id = #{organizationId,jdbcType=BIGINT}
            </if>
            <if test="organizationName != null">
               and  o.organization_name like '%' + #{organizationName,jdbcType=VARCHAR} + '%'
            </if>
            <if test="saleName != null">
               and  crsc.sales_name like '%' + #{saleName,jdbcType=VARCHAR} + '%'
            </if>
    </select>
     <sql id="personalPointsDouble">
            (
	            SELECT
					SUM (pvd.POINT_VALUE) AS LEFT_POINT
				FROM
					dbo.wx_t_point_value_detail pvd
				LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
				WHERE
					pac.POINT_ACCOUNT_TYPE = 'SPBD'
				AND pac.POINT_ACCOUNT_OWNER_ID = u.user_id
				<if test="fromDate != null">
			      and pvd.creation_time &gt; #{fromDate,jdbcType=TIMESTAMP}
			   </if>
			   <if test="toDate != null">
			       and pvd.creation_time &lt; #{toDate, jdbcType=TIMESTAMP}
			   </if> 
	               <!-- select sum(award_point) from wx_t_dsr_kpi dk
	               where dk.dsr_id = u.user_id
	                   and dk.award_point != 0
                      <if test="fromDate != null">
					      and dk.create_time &gt; #{fromDate,jdbcType=TIMESTAMP}
					   </if>
					   <if test="toDate != null">
					       and dk.create_time &lt; #{toDate, jdbcType=TIMESTAMP}
					   </if> -->	   
               ) "points_double",
               (
                 SELECT sum(pvd.POINT_VALUE - pvd.POINT_PAYED ) AS LEFT_POINT
										FROM dbo.wx_t_point_value_detail pvd
										LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
									where pac.POINT_ACCOUNT_TYPE='SPBD' and pac.POINT_ACCOUNT_OWNER_ID=u.user_id
              )  "residue_double"
    </sql>
    <sql id="permissionCondition">
        <choose>
                <when test="(permissionWeight &amp; 1) &gt; 0">
                    and  1=1
                </when>
                <when test="(permissionWeight &amp; 4) &gt; 0">
                    and crsc.channel_manager_cai = '${cai}'
                </when>
                <when test="(permissionWeight &amp; 8) &gt; 0">
                    and crsc.suppervisor_cai = '${cai}'
                </when>
                 <when test="(permissionWeight &amp; 64) &gt; 0">
                   and  dp_001_sr1.sales_cai_level LIKE '%_'+'${cai}'+'%'
                </when>
                <when test="(permissionWeight &amp; 16) &gt; 0">
                   and  crsc.sales_cai = '${cai}'
                </when>
                <when test="(permissionWeight &amp; 32) &gt; 0">
                   and pe.partner_id = #{partnerId,jdbcType=BIGINT}
                </when>
                <otherwise>
                 and 1 != 1
                </otherwise>
            </choose>
    </sql>
    <resultMap id="pointTotalMap" type="com.chevron.dsrkpi.model.PointTotal">
		<!-- <id column="id" property="id" jdbcType="BIGINT"/> -->
		<result column="dsrId" property="dsrId" jdbcType="BIGINT"/>
		<result column="ch_name" property="chName" jdbcType="VARCHAR"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="region_name_ch" property="regionNameCh" jdbcType="VARCHAR"/>
		<result column="customer_name_cn" property="organizationName" jdbcType="VARCHAR"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="asm_name" property="asmName" jdbcType="VARCHAR"/>
		<result column="cm_name" property="cmName" jdbcType="VARCHAR"/>		
		<result column="points_total_double" property="pointsTotalDouble" jdbcType="NUMERIC"/>
		<result column="residue_total_double" property="residueTotalDouble" jdbcType="NUMERIC"/>
	</resultMap>
    <select id="selectPointTotal" resultMap="pointTotalMap"  >
        SELECT
			x.region_name,
			suppervisor_name "asm_name",
			x.suppervisor_cai,
			x.channel_manager_name,
			x.ch_name 'cm_name',
			SUM ("pointsTotalDouble") AS "points_total_double",
			SUM ("residue_total_double") AS "residue_total_double"
			FROM
				(
					SELECT
						<!-- crsc.region_name, -->
						tr.value_after_transform region_name,
						crsc.suppervisor_name,
						crsc.suppervisor_cai,
						crsc.channel_manager_name,
						cm.ch_name,
						(
							<!-- SELECT
								SUM (award_point) AS points
							FROM
								wx_t_dsr_kpi
							WHERE
								wx_t_dsr_kpi.dsr_id = u.user_id -->
							SELECT
								SUM (pvd.POINT_VALUE) AS LEFT_POINT
							FROM
								dbo.wx_t_point_value_detail pvd
							LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
							WHERE
								pac.POINT_ACCOUNT_TYPE = 'SPBD'
							AND pac.POINT_ACCOUNT_OWNER_ID = u.user_id
						) AS "pointsTotalDouble",
						(
							SELECT
								SUM (
									pvd.POINT_VALUE - pvd.POINT_PAYED
								) AS LEFT_POINT
							FROM
								dbo.wx_t_point_value_detail pvd
							LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
							WHERE
								pac.POINT_ACCOUNT_TYPE = 'SPBD'
							AND pac.POINT_ACCOUNT_OWNER_ID = u.user_id
						) AS "residue_total_double"
					FROM view_customer_region_sales_channel crsc
			  		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'	
					LEFT JOIN wx_t_partner_o2o_enterprise pe ON crsc.distributor_id = pe.distributor_id
					LEFT JOIN wx_t_organization o ON pe.partner_id = o.id
					LEFT JOIN wx_t_user u ON u.org_id = o.id
					AND o.type = 1
                    left join wx_t_user cm on cm.cai = crsc.channel_manager_cai 
                    LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0   
					WHERE
					  1=1 
					  AND crsc.channel_weight &amp; 2 > 0
					 <!--  and exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' 
                          and vtm1.value_after_transform=rscr.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I']]>, 'CDM')) -->
					  <!-- and crss.region_name LIKE <![CDATA['C&I%']]> -->
					 <include refid="permissionCondition"/>
					AND u.status = 1
				) x
			GROUP BY
			x.region_name,
			suppervisor_name,
			x.suppervisor_cai,
			x.channel_manager_name,
			x.ch_name
    </select>
    <sql id="totalPointsDouble">
         (select sum(points) from  (select u.user_id, sum(award_point) points  from wx_t_dsr_kpi 
               where wx_t_dsr_kpi.dsr_id in(
                              select u0.user_id
								from wx_t_user  u0
									 left join wx_t_organization o0 on u0.org_id = o0.id
									 left join wx_t_partner_o2o_enterprise pe0 on pe0.partner_id = o0.id
									 left join dw_customer_region_sales_supervisor_rel crss0 on crss0.distributor_id = pe0.distributor_id
							    where crss0.suppervisor_cai = crss.suppervisor_cai                    
                       )
               
               ) t1 ) "pointsTotalDouble"
    </sql>
    
    <select id="personalPointDetail" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.DsrKpiParams">
         SELECT
			dk.id,
			dk.dsr_id,
			dk.kpi_code,
			dk.kpi_value,
			dk.award_point,
			dk.effictive_date,
			dk.ext_property1,
			
			dk.ext_property3,
			dk.delete_flag,
			dk.create_user_id,
			dk.create_time,
			dk.update_user_id,
			dk.update_time,
		  ar.remark "ext_property2"
		FROM
			wx_t_dsr_kpi dk
		  LEFT JOIN wx_t_award_rule ar on ar.kpi_code = dk.kpi_code
		WHERE
		    1=1
		    and dk.award_point != 0
		    <if test="dsrId != null">
		       and dk.dsr_id = #{dsrId, jdbcType=BIGINT}
		    </if>
		    <if test="fromDate != null">
		      and dk.create_time &gt; #{fromDate,jdbcType=TIMESTAMP}
		   </if>
		   <if test="toDate != null">
		       and dk.create_time &lt; #{toDate, jdbcType=TIMESTAMP}
		   </if>	
    </select>
    
    <select id="selecExcelExportToEmailsByYearMonth" resultType="com.chevron.dsrkpi.model.ExcelExportToEmail">
         SELECT
         <!-- crsc.region_name "regionName", -->
			tr.value_after_transform "regionName",
			cu.ch_name "channelManagerName",
		  crsc.channel_manager_cai "channelManagerCai",
		
			crsc.suppervisor_name "suppervisorName",
		  crsc.suppervisor_cai "suppervisorCai",
		
			o.organization_name "organizationName",
			crsc.sales_name "salesName",
		  crsc.sales_cai "salesCai",
			u.ch_name AS "dsrName",
			di.dic_item_name "kpiName",
			p1.left_point "leftPoint",
			p1.all_points "allPoint",
         <!--    pa.all_point "allPoint", -->
            	dk.id,
            	dk.dsr_id "dsrId",
            	dk.kpi_code  "kpiCode",
            	dk.kpi_value  "kpiValue",
            	dk.award_point  "awardPoint",
            	dk.effictive_date  "effictiveDate",
            	dk.ext_property1  "extProperty1",
            	dk.ext_property2  "extProperty2",
            	dk.ext_property3  "extProperty3",
            	dk.delete_flag   "deleteFlag",
            	dk.create_user_id  "createUserId",
            	dk.create_time  "createTime",
            	dk.update_user_id  "updateUserId",
            	dk.update_time  "updateTime"
			
		FROM
			view_customer_region_sales_channel crsc
		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type='ChRegionNameMapping'	
		left join wx_t_user cu on cu.cai = crsc.channel_manager_cai
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON crsc.distributor_id = pe.distributor_id
		LEFT JOIN wx_t_organization o ON pe.partner_id = o.id
		LEFT JOIN wx_t_user u ON u.org_id = o.id
		AND o.type = 1
		AND (u.type IS NULL OR u.type != '1')
		LEFT JOIN wx_t_dsr_kpi dk ON dk.dsr_id = u.user_id
		AND dk.delete_flag = 0 
		LEFT JOIN wx_t_dic_item di ON di.dic_type_code = 'DsrKpi.kpiCode'
		AND di.status = 1
		AND di.dic_item_code = dk.kpi_code
		LEFT JOIN (
			SELECT
				SUM (
					pvd.POINT_VALUE - pvd.POINT_PAYED
				) AS left_point,
				SUM(pvd.POINT_VALUE) AS all_points,
				pac.POINT_ACCOUNT_OWNER_ID
			FROM
				dbo.wx_t_point_value_detail pvd
			LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
			WHERE
				pac.POINT_ACCOUNT_TYPE = 'SPBD'
			GROUP BY
				pac.POINT_ACCOUNT_OWNER_ID
		) p1 ON p1.POINT_ACCOUNT_OWNER_ID = u.user_id
		LEFT JOIN (
			SELECT
				dsr_id,
				SUM (award_point) all_point
			FROM
				wx_t_dsr_kpi
			WHERE
			   1=1
			   <if test="yearMonth != null">
				   AND wx_t_dsr_kpi.ext_property3 = #{yearMonth, jdbcType=VARCHAR}
				</if>
			GROUP BY
				dsr_id
		) pa ON pa.dsr_id = u.user_id
		WHERE
			u.status = 1
			AND crsc.channel_weight &amp; 2 > 0
			<!-- and exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' 
              and vtm1.value_after_transform=rsc.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I']]>, 'CDM')) -->
		<!-- AND rsc.sales_channel_name = <![CDATA['C&I']]> -->
		AND kpi_code IS NOT NULL 
		<if test="yearMonth != null">
		   AND dk.ext_property3 = #{yearMonth, jdbcType=VARCHAR}
		</if>
		ORDER BY
		crsc.channel_manager_cai,
		crsc.suppervisor_cai,
		crsc.sales_cai,
		o.id,
		u.user_id,
		dk.kpi_code,
		di.sort_numb,
		dk.effictive_date
    </select>
    
    <resultMap id="pointsLogMap" type="com.chevron.dsrkpi.model.DsrPointsLogVo">
		
		<result column="modified_value" property="modifiedValue" jdbcType="NUMERIC"/>
		<result column="changeType" property="changeType" jdbcType="VARCHAR"/>
		<result column="comments" property="comments" jdbcType="VARCHAR"/>		
		<result column="trans_time" property="transTime" jdbcType="VARCHAR"/>		
		<result column="creation_time" property="creationTime" jdbcType="TIMESTAMP"/>
	</resultMap>
    
    <select id="queryPointsLog" resultMap="pointsLogMap" parameterType="com.chevron.dsrkpi.model.PointsLogVo">
		<if test="type == 2">
			SELECT
			SUM (
				de.MATERIAL_PRICE * de.APPLICATION_QTY
			) AS modified_value,
			'reduce' changeType,
			'订单:' + a.APPLICATION_CODE AS comments,
			a.CREATION_TIME trans_time,
			a.CREATION_TIME creation_time
		FROM
			dbo.wx_t_material_application a
		LEFT JOIN dbo.wx_t_organization o ON o.id = a.APPLICATION_ORG_ID
		LEFT JOIN dbo.wx_t_material_application_detail de ON de.APPLICATION_ID = a.ID
		WHERE
			1 = 1
		AND a.DELETE_FLAG = 0
		AND a.APPLICATION_TYPE = 'dsr_cio'
		AND a.APPLICATION_PERSON_ID = #{ownerId}
		AND a.APPLICATION_STATUS NOT IN (
			'REJECTED',
			'DRAFT',
			'PENDING_4_URGENT'
		)
		GROUP BY
			a.APPLICATION_CODE,
			a.APPLICATION_TYPE,
			a.APPLICATION_STATUS,
			a.CREATION_TIME,
			a.LAST_UPDATE_TIME,
			a.APPLICATION_ORG_ID,
			a.APPLICATION_PERSON_ID,
			a.id
		UNION ALL
		</if>
			SELECT
				t_log.modified_value,
				CASE
			WHEN t_log.modified_value &gt;= 0 THEN
				'increase'
			ELSE
				'reduce'
			END changeType,
			CASE WHEN t_det.ATTRIBUTE2 IS NOT NULL
			THEN (SELECT dic_item_name FROM wx_t_dic_item where dic_type_code = 'ci.performance.adjust.point' AND dic_item_code = t_det.ATTRIBUTE2)
			ELSE t_log.comments
			END comments,
			CASE WHEN t_det.trans_time IS NULL THEN
			t_det.CREATION_TIME ELSE t_det.trans_time 
			END trans_time,
			t_log.creation_time
		FROM
			wx_t_point_value_detail_log t_log
		LEFT JOIN wx_t_point_account t_acc ON t_log.POINT_ACCOUNT_ID = t_acc.ID
		AND t_acc.POINT_ACCOUNT_TYPE = #{accountType}
		LEFT JOIN wx_t_point_value_detail t_det ON t_det.id = t_log.POINT_VALUE_ID
		WHERE
			1 = 1
		AND t_acc.POINT_ACCOUNT_OWNER_ID = #{ownerId}
		AND t_log.DELETE_FLAG = 0
    </select>
    
    <select id="checkWhetherPoints" resultType="integer">
        select sum(con)
        from (
                 select count(0) as con
                 from wx_t_dsr_kpi
                 where effictive_date = #{date}
                 union all
                 select count(0)  as con
                 from wx_t_ci_dsr_kpi_history
                 where effictive_date = #{date}
             )t
    </select>
    
    <select id="selectPointDouble" resultMap="pointTotalMap" parameterType="java.util.List">
	    SELECT
			pac.POINT_ACCOUNT_OWNER_ID dsrId,
			u.ch_name,
			(
				SELECT
					SUM (pvd.POINT_VALUE)
				FROM
					wx_t_point_value_detail pvd
					LEFT JOIN wx_t_point_account pacc ON pvd.point_account_id = pacc.ID 
				WHERE
				pacc.POINT_ACCOUNT_OWNER_ID = pac.POINT_ACCOUNT_OWNER_ID
				AND pvd.POINT_TYPE = 'DSR_CIO_POINT'
				GROUP BY pacc.POINT_ACCOUNT_OWNER_ID
			) points_total_double,
			SUM (tv.POINT_VALUE) - SUM (POINT_PAYED) residue_total_double
		FROM
			wx_t_point_value_detail tv
		LEFT JOIN wx_t_point_account pac ON tv.point_account_id = pac.ID
		LEFT JOIN wx_t_user u ON u.user_id = pac.POINT_ACCOUNT_OWNER_ID
		WHERE
			pac.POINT_ACCOUNT_OWNER_ID IN 
			<foreach collection="list" item="dsrId" index="index" open="(" close=")" separator=",">
		    				(
		    					#{dsrId}
		    				)
		    </foreach>	
		AND pac.POINT_ACCOUNT_TYPE = 'SPBD'
		GROUP BY
			pac.POINT_ACCOUNT_OWNER_ID,u.ch_name
    </select>
    
    <select id="selectCurDouble" resultMap="pointTotalMap" parameterType="java.util.List">
	    SELECT
			pe.partner_id,
			crsc.region_name,
			tr.value_after_transform region_name_ch,
			crsc.customer_name_cn,
			crsc.sales_name cm_name,
			crsc.suppervisor_name asm_name
		FROM
			wx_t_partner_o2o_enterprise pe
		LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id
		AND crsc.channel_weight &amp; 2 &gt; 0
		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform
		AND tr.transform_type = 'ChRegionNameMapping'
		WHERE
			1 = 1
		AND pe.partner_id IN 
		<foreach collection="list" item="partnerId" index="index" open="(" close=")" separator=",">
		    #{partnerId}
		</foreach>	
    </select>
    
    <select id="selectCurInfoByOrdIds" resultMap="pointTotalMap" parameterType="java.util.List">
    	SELECT
			u.user_id dsrId,
			u.org_id partner_id,
			u.ch_name,
			crsc.region_name,
			tr.value_after_transform region_name_ch,
			crsc.customer_name_cn,
			crsc.sales_name cm_name,
			crsc.suppervisor_name asm_name
		FROM
			wx_t_user u
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
		LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id
		AND crsc.channel_weight  &amp; 2 &gt; 0
		LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform
		AND tr.transform_type = 'ChRegionNameMapping'
		WHERE
			u.user_id IN 
			<foreach collection="list" item="dsrId" index="index" open="(" close=")" separator=",">
			    #{dsrId}
			</foreach>
    
    </select>


    <!-- 结果集映射 -->
    <resultMap id="KpiCalInfo" type="com.chevron.kpi.model.KpiCalInfo">
        <result column="dsr_id" property="dsrId"/>
        <result column="award_point" property="awardPoint"/>
        <result column="month" property="month"/>
        <result column="year" property="year"/>
        <result column="left_point" property="ytdPointBalance"/>
        <result column="partner_id" property="partnerId"/>
        <result column="ch_name" property="dsrName"/>
        <result column="region_name" property="regionName"/>
        <result column="region_name_ch" property="regionCnName"/>
        <result column="customer_name_cn" property="partnerName"/>
        <result column="sales_name" property="salesName"/>
        <result column="sales_cai" property="salesCai"/>
        <result column="suppervisor_name" property="suppervisorName"/>
        <result column="suppervisor_cai" property="suppervisorCai"/>
        <result column="distributor_id" property="distributorId"/>
        <result column="ytd_point" property="ytdPoint"/>
        <result column="adjust_point" property="adjustPoint"/>
        <result column="dic_item_name" property="adjustReason"/>
        <result column="dic_item_code" property="adjustReasonCode"/>


    </resultMap>
    <select id="getHistoryDsrKpiPoint" resultMap="KpiCalInfo">
        select dsr_id,
               year(effictive_date)  "year",
               month(effictive_date) "month",
               sum(award_point) as   award_point
        from wx_t_dsr_kpi
        where year(effictive_date) = #{year}
        group by dsr_id,year(effictive_date), month(effictive_date)
    </select>


    <!--  查询德乐dsr销售的积分剩余  -->
    <select id="getCiDsrLeftPoint" resultMap="KpiCalInfo">
        SELECT pac.POINT_ACCOUNT_OWNER_ID as dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        <if test="year != null and year != ''">
            sum(case when year(pvd.CREATION_TIME) = ${year} then pvd.POINT_VALUE else 0 end) AS ytd_point,
        </if>
        sum(pvd.POINT_VALUE - pvd.POINT_PAYED) AS left_point
        FROM dbo.wx_t_point_value_detail pvd
        LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
        left join wx_t_user u on pac.POINT_ACCOUNT_OWNER_ID = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND
        crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type =
        'ChRegionNameMapping'
        <if test="orgType == 64">
      	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
      	</if>
        where
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_' + #{orgCai,jdbcType=NVARCHAR} + '%'</if>
        and pac.POINT_ACCOUNT_TYPE = 'SPBD' and crsc.region_name is not null
        group by pac.POINT_ACCOUNT_OWNER_ID,
        u.org_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id
    </select>

    <!-- 结果集映射 -->
    <resultMap id="ExportBonus" type="com.chevron.kpi.model.ExportBonus">
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="dsr_id" property="dsrId"/>
        <result column="partner_id" property="partnerId"/>
        <result column="ch_name" property="dsrName"/>
        <result column="region_name" property="regionName"/>
        <result column="region_name_ch" property="regionCnName"/>
        <result column="customer_name_cn" property="partnerName"/>
        <result column="sales_name" property="salesName"/>
        <result column="suppervisor_name" property="suppervisorName"/>
        <result column="distributor_id" property="distributorId"/>
        <result column="newCusBonus" property="newCusBonus"/>
        <result column="visitBonus" property="visitBonus"/>
        <result column="fleetBonus" property="fleetBonus"/>
    </resultMap>

    <!-- 列表查询 -->
    <select id="buildExportBonus" resultMap="ExportBonus" parameterType="map">
        SELECT a.dsr_id,
        u.org_id partner_id,
        u.ch_name,
        month(a.effictive_date) "month",
        year(a.effictive_date) "year",
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        sum(case when a.kpi_code = 'DAYS_OF_MONTH' then a.award_point else 0 end) visitBonus,
        sum(case when a.kpi_code = 'TOP_RANK' then a.award_point else 0 end) newCusBonus,
        sum(case when a.kpi_code = 'AMOUNT_OF_CERTIFICATE' then a.award_point else 0 end) fleetBonus
        FROM dbo.wx_t_dsr_kpi a
        left join wx_t_user u on a.dsr_id = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc
        ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr
        ON crsc.region_name = tr.value_before_transform AND tr.transform_type = 'ChRegionNameMapping'
       	 <if test="orgType == 64">
      	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
      	</if>
        where
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_' + #{orgCai,jdbcType=NVARCHAR} + '%' </if>
        and crsc.region_name is not null
        <if test="year != null">
            and year(a.effictive_date) = #{year}
        </if>
        group by a.dsr_id,
        u.org_id,
        u.ch_name,
        month(a.effictive_date),
        year(a.effictive_date),
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id
    </select>


    <select id="getDsrInfoByParams" resultMap="ExportBonus" parameterType="map">
        SELECT u.user_id dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id
        FROM wx_t_user u
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc
        ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr
        ON crsc.region_name = tr.value_before_transform AND tr.transform_type = 'ChRegionNameMapping'
       	<if test="orgType == 64">
       	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
       	</if>
        where
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_'+ #{orgCai,jdbcType=NVARCHAR} +'%' </if>
        and crsc.region_name is not null
        <if test="dsrs != null and dsrs.size > 0">
            and u.user_id IN
            <foreach collection="dsrs" item="dsrId" index="index" open="(" close=")" separator=",">
                #{dsrId}
            </foreach>
        </if>
    </select>
    
    <resultMap id="userMap" type="com.sys.auth.model.WxTUser" >
	<!--
	WARNING - @mbggenerated
	This element is automatically generated by MyBatis Generator, do not modify.
	This element was generated on Wed Jun 03 10:58:07 CST 2015.
	-->
	<id column="user_id" property="userId" jdbcType="BIGINT" />
	<result column="login_name" property="loginName" jdbcType="VARCHAR" />
	<result column="user_no" property="userNo" jdbcType="VARCHAR" />
	<result column="password" property="password" jdbcType="VARCHAR" />
	<result column="salt" property="salt" jdbcType="VARCHAR" />
	<result column="pwd_lasttime" property="pwdLasttime" jdbcType="TIMESTAMP" />
	<result column="ch_name" property="chName" jdbcType="VARCHAR" />
	<result column="cn_name" property="cnName" jdbcType="VARCHAR" />
	<result column="pinyin" property="pinyin" jdbcType="VARCHAR" />
	<result column="allow_login" property="allowLogin" jdbcType="VARCHAR" />
	<result column="sex" property="sex" jdbcType="VARCHAR" />
	<result column="birthday" property="birthday" jdbcType="TIMESTAMP" />
	<result column="address" property="address" jdbcType="VARCHAR" />
	<result column="email" property="email" jdbcType="VARCHAR" />
	<result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
	<result column="fixed_tel" property="fixedTel" jdbcType="VARCHAR" />
	<result column="photo_id" property="photoId" jdbcType="BIGINT" />
	<result column="org_id" property="orgId" jdbcType="BIGINT" />
	<result column="org_type" property="orgType" jdbcType="INTEGER" />
	<result column="org_name"   property="orgName" jdbcType="VARCHAR" />
	<result column="postion"   property="postion" jdbcType="VARCHAR"  />
	<result column="postion_name"   property="postionName" jdbcType="VARCHAR"  />
	<result column="branch_name"   property="branchName" />
	<result column="branch_id"   property="branchId" />
	<result column="user_intime" property="userIntime" jdbcType="TIMESTAMP" />
	<result column="user_outtime" property="userOuttime" jdbcType="TIMESTAMP" />
	<result column="description" property="description" jdbcType="VARCHAR" />
	<result column="device_id" property="deviceId" jdbcType="VARCHAR" />
	<result column="device_type" property="deviceType" jdbcType="VARCHAR" />
	<result column="receive_msg" property="receiveMsg" jdbcType="INTEGER" />
	<result column="post" property="post" jdbcType="BIGINT" />
	<result column="is_valid" property="isValid" jdbcType="VARCHAR" />
	<result column="xz_time" property="xzTime" jdbcType="TIMESTAMP" />
	<result column="xz_user" property="xzUser" jdbcType="VARCHAR" />
	<result column="xg_time" property="xgTime" jdbcType="TIMESTAMP" />
	<result column="xg_user" property="xgUser" jdbcType="VARCHAR" />
	<result column="status" property="status" jdbcType="INTEGER" />
	<result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
	<result column="ch_role_name"   property="chRoleName" />
	<result column="type" property="type" jdbcType="VARCHAR" />
	<result column="change_pwd_permission" property="changePwdPermission" jdbcType="INTEGER" />
	<result column="reset_flag" property="resetFlag" jdbcType="INTEGER" />
	<result column="cai" property="cai" jdbcType="VARCHAR"/>
	<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
	<!-- 针对C&I marketing region name -->
	<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
	<result column="user_model" property="userModel" jdbcType="VARCHAR"/>
	<result column="default_locale" property="defaultLocale" jdbcType="VARCHAR"/>
</resultMap>
	
	<select id="getUserListByIds" resultMap="userMap" parameterType="map">
		SELECT
			u.*
		FROM
			wx_t_user u
			LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = u.org_id 
			LEFT JOIN  view_customer_region_sales_channel crsc ON crsc.distributor_id = pe.distributor_id 
		WHERE
			1 =1 
			AND crsc.channel_weight &amp; 2 &gt; 0
			<if test="dsrIds != null and dsrIds.size > 0">
            and u.user_id IN
            <foreach collection="dsrIds" item="dsrId" index="index" open="(" close=")" separator=",">
                #{dsrId}
            </foreach>
        </if>
	
	</select>

    <select id="getAdjustPoint" resultMap="KpiCalInfo">
        SELECT
        pac.POINT_ACCOUNT_OWNER_ID as dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        sum(pvd.POINT_VALUE) as adjust_point,
        month(pvd.TRANS_TIME) as month,
        year(pvd.TRANS_TIME) as year
        FROM dbo.wx_t_point_value_detail pvd
        LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
        LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
        left join wx_t_user u on pac.POINT_ACCOUNT_OWNER_ID = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type = 'ChRegionNameMapping'
        <if test="orgType == 64">
      	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
      	</if>
        WHERE
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_' + #{orgCai,jdbcType=NVARCHAR} + '%'</if>
        AND pac.POINT_ACCOUNT_TYPE = 'SPBD'
        AND pb.BUSINESS_TYPE_CODE = 'ADJUST_POINT'
        and crsc.region_name is not null
        <if test="year != null and year != ''">
            and year(pvd.TRANS_TIME) = #{year}
        </if>
        <if test="month != null and month != ''">
            and month(pvd.TRANS_TIME) = #{month}
        </if>
        <if test="months != null and months.size > 0">
            and month(pvd.TRANS_TIME) in
            <foreach collection="months" item="month" open="(" close=")" separator="," >
                #{month}
            </foreach>
        </if>
        group by
        pac.POINT_ACCOUNT_OWNER_ID,
        u.org_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        month(pvd.TRANS_TIME),
        year(pvd.TRANS_TIME)
    </select>

    <select id="getAdjustPointDetails" resultMap="KpiCalInfo">
        SELECT
        pac.POINT_ACCOUNT_OWNER_ID as dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        wtdi.dic_item_name,
        pvd.attribute2 as dic_item_code,
        sum(pvd.POINT_VALUE) as adjust_point,
        month(pvd.TRANS_TIME) as month,
        year(pvd.TRANS_TIME) as year
        FROM dbo.wx_t_point_value_detail pvd
        LEFT JOIN dbo.wx_t_point_business pb ON pvd.BUSINESS_ID = pb.ID
        LEFT JOIN dbo.wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID
        left join wx_t_user u on pac.POINT_ACCOUNT_OWNER_ID = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type = 'ChRegionNameMapping'
        LEFT JOIN wx_t_dic_item wtdi on pvd.attribute2 = wtdi.dic_item_code and wtdi.dic_type_code = 'ci.performance.adjust.point'
        <if test="orgType == 64">
          LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
        </if>
        WHERE
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_'+#{orgCai,jdbcType=NVARCHAR}+'%'</if>
        
        AND pac.POINT_ACCOUNT_TYPE = 'SPBD'
        AND pb.BUSINESS_TYPE_CODE = 'ADJUST_POINT'
        and crsc.region_name is not null
        <if test="year != null and year != ''">
            and year(pvd.TRANS_TIME) = #{year}
        </if>
        <if test="month != null and month != ''">
            and month(pvd.TRANS_TIME) = #{month}
        </if>
        <if test="months != null and months.size > 0">
            and month(pvd.TRANS_TIME) in
            <foreach collection="months" item="month" open="(" close=")" separator="," >
                #{month}
            </foreach>
        </if>
        group by
        pac.POINT_ACCOUNT_OWNER_ID,
        u.org_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.suppervisor_name,
        crsc.distributor_id,
        wtdi.dic_item_name,
        pvd.attribute2,
        month(pvd.TRANS_TIME),
        year(pvd.TRANS_TIME)
    </select>


    <!--获取手动调整的积分-->
    <select id="getPreAdjustPointCollect" resultMap="KpiCalInfo">
        select
        a.dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.sales_cai,
        crsc.suppervisor_name,
        crsc.suppervisor_cai,
        crsc.distributor_id,
        month(a.trans_time) as month,
        year(a.trans_time) as year,
        sum(pre_points) as adjust_point
        from wx_t_pre_adjust_point a
        left join wx_t_user u on a.dsr_id = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc
        ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type =
        'ChRegionNameMapping'
        <if test="orgType == 64">
      	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
      	</if>
        where
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_' + #{orgCai,jdbcType=NVARCHAR} + '%'</if>
        and crsc.region_name is not null
        <if test="year != null and year != ''">
            and year(a.TRANS_TIME) = #{year}
        </if>
        <if test="month != null and month != ''">
            and month(a.TRANS_TIME) = #{month}
        </if>
        <if test="months != null and months.size > 0">
            and month(a.TRANS_TIME) in
            <foreach collection="months" item="month" open="(" close=")" separator="," >
                #{month}
            </foreach>
        </if>
        group by
        a.dsr_id,
        u.org_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.sales_cai,
        crsc.suppervisor_name,
        crsc.suppervisor_cai,
        crsc.distributor_id,
        month(a.trans_time),
        year(a.trans_time)
    </select>

    <!--获取手动调整的积分-->
    <select id="getPreAdjustPoint" resultMap="KpiCalInfo">
        select
        a.dsr_id,
        u.org_id partner_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform region_name_ch,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.sales_cai,
        crsc.suppervisor_name,
        crsc.suppervisor_cai,
        crsc.distributor_id,
        wtdi.dic_item_name,
        a.dic_item_code,
        month(a.trans_time) as month,
        year(a.trans_time) as year,
        sum(pre_points) as adjust_point
        from wx_t_pre_adjust_point a
        left join wx_t_user u on a.dsr_id = u.user_id
        LEFT JOIN wx_t_partner_o2o_enterprise pe ON u.org_id = pe.partner_id
        LEFT JOIN view_customer_region_sales_channel crsc
        ON pe.distributor_id = crsc.distributor_id AND crsc.channel_weight &amp; 2 &gt; 0
        LEFT JOIN wx_t_value_transform_map tr ON crsc.region_name = tr.value_before_transform AND tr.transform_type =
        'ChRegionNameMapping'
        LEFT JOIN wx_t_dic_item wtdi on a.dic_item_code = wtdi.dic_item_code and wtdi.dic_type_code = 'ci.performance.adjust.point'
        <if test="orgType == 64">
      	LEFT JOIN dw_sales_role dp_001_sr1 ON dp_001_sr1.[sales_cai] = crsc.sales_cai
						AND dp_001_sr1.del_flag = 0
      	</if>
        where
        <if test="orgType == 1">1=1</if>
        <if test="orgType == 4">crsc.channel_manager_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 8">crsc.suppervisor_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 16">crsc.sales_cai = #{orgCai,jdbcType=NVARCHAR}</if>
        <if test="orgType == 32">pe.partner_id = #{partnerId,jdbcType=BIGINT}</if>
        <if test="orgType == 64">dp_001_sr1.sales_cai_level LIKE '%_' + #{orgCai,jdbcType=NVARCHAR} + '%'</if>
        and crsc.region_name is not null
        <if test="year != null and year != ''">
            and year(a.TRANS_TIME) = #{year}
        </if>
        <if test="month != null and month != ''">
            and month(a.TRANS_TIME) = #{month}
        </if>
        <if test="months != null and months.size > 0">
            and month(a.TRANS_TIME) in
            <foreach collection="months" item="month" open="(" close=")" separator="," >
                #{month}
            </foreach>
        </if>
        group by
        a.dsr_id,
        u.org_id,
        u.ch_name,
        crsc.region_name,
        tr.value_after_transform,
        crsc.customer_name_cn,
        crsc.sales_name,
        crsc.sales_cai,
        crsc.suppervisor_name,
        crsc.suppervisor_cai,
        crsc.distributor_id,
        wtdi.dic_item_name,
        a.dic_item_code,
        month(a.trans_time),
        year(a.trans_time)
    </select>

</mapper>
