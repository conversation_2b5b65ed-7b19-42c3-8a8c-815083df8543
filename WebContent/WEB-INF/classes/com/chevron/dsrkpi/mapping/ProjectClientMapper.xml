<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.dsrkpi.dao.ProjectClientMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.dsrkpi.model.ProjectClient">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="project_name" property="projectName" jdbcType="VARCHAR"/>
		<result column="photos" property="photos" jdbcType="VARCHAR"/>
		<result column="project_area" property="projectArea" jdbcType="VARCHAR"/>
		<result column="project_region_id" property="projectRegionId" jdbcType="BIGINT"/>
		<result column="project_address" property="projectAddress" jdbcType="VARCHAR"/>
		<result column="project_type" property="projectType" jdbcType="VARCHAR"/>
		<result column="main_eqpmt_type" property="mainEqpmtType" jdbcType="VARCHAR"/>
		<result column="eqpmt_num" property="eqpmtNum" jdbcType="BIGINT"/>
		<result column="monthly_sales" property="monthlySales" jdbcType="BIGINT"/>
		<result column="main_sales_products" property="mainSalesProducts" jdbcType="VARCHAR"/>
		<result column="visiting_record" property="visitingRecord" jdbcType="VARCHAR"/>
		<result column="longitude" property="longitude" jdbcType="NUMERIC"/>
		<result column="latitude" property="latitude" jdbcType="NUMERIC"/>
		<result column="relative_distance" property="relativeDistance" jdbcType="NUMERIC"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="region_id" property="regionId" jdbcType="BIGINT"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="attribute1" property="attribute1" jdbcType="VARCHAR"/>
		<result column="attribute2" property="attribute2" jdbcType="VARCHAR"/>
		<result column="attribute3" property="attribute3" jdbcType="VARCHAR"/>
		<result column="attribute4" property="attribute4" jdbcType="VARCHAR"/>
		<result column="attribute5" property="attribute5" jdbcType="VARCHAR"/>
		<result column="attribute6" property="attribute6" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,create_user_id,create_time,update_user_id,update_time,project_name,photos,project_area,project_region_id,
		project_address,project_type,main_eqpmt_type,eqpmt_num,monthly_sales,main_sales_products,visiting_record,longitude,
		latitude,status,ext_flag,delete_flag,region_id,partner_id,attribute1,attribute2,attribute3,attribute4,attribute5,
		attribute6
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.dsrkpi.model.ProjectClient">
		update wx_t_project_client set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.dsrkpi.model.ProjectClient">
		update wx_t_project_client
		<set>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="projectName != null" >
				project_name = #{projectName,jdbcType=VARCHAR},
			</if>
			<if test="photos != null" >
				photos = #{photos,jdbcType=VARCHAR},
			</if>
			<if test="projectArea != null" >
				project_area = #{projectArea,jdbcType=VARCHAR},
			</if>
			<if test="projectRegionId != null" >
				project_region_id = #{projectRegionId,jdbcType=BIGINT},
			</if>
			<if test="projectAddress != null" >
				project_address = #{projectAddress,jdbcType=VARCHAR},
			</if>
			<if test="projectType != null" >
				project_type = #{projectType,jdbcType=VARCHAR},
			</if>
			<if test="mainEqpmtType != null" >
				main_eqpmt_type = #{mainEqpmtType,jdbcType=VARCHAR},
			</if>
			<if test="eqpmtNum != null" >
				eqpmt_num = #{eqpmtNum,jdbcType=BIGINT},
			</if>
			<if test="monthlySales != null" >
				monthly_sales = #{monthlySales,jdbcType=BIGINT},
			</if>
			<if test="mainSalesProducts != null" >
				main_sales_products = #{mainSalesProducts,jdbcType=VARCHAR},
			</if>
			<if test="visitingRecord != null" >
				visiting_record = #{visitingRecord,jdbcType=VARCHAR},
			</if>
			<if test="longitude != null" >
				longitude = #{longitude,jdbcType=NUMERIC},
			</if>
			<if test="latitude != null" >
				latitude = #{latitude,jdbcType=NUMERIC},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="regionId != null" >
				region_id = #{regionId,jdbcType=BIGINT},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="attribute1 != null" >
				attribute1 = #{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null" >
				attribute2 = #{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null" >
				attribute3 = #{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="attribute4 != null" >
				attribute4 = #{attribute4,jdbcType=VARCHAR},
			</if>
			<if test="attribute5 != null" >
				attribute5 = #{attribute5,jdbcType=VARCHAR},
			</if>
			<if test="attribute6 != null" >
				attribute6 = #{attribute6,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.dsrkpi.model.ProjectClientExample">
    	delete from wx_t_project_client
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.dsrkpi.model.ProjectClient" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_project_client
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="projectName != null">
				project_name,
			</if>
			<if test="photos != null">
				photos,
			</if>
			<if test="projectArea != null">
				project_area,
			</if>
			<if test="projectRegionId != null">
				project_region_id,
			</if>
			<if test="projectAddress != null">
				project_address,
			</if>
			<if test="projectType != null">
				project_type,
			</if>
			<if test="mainEqpmtType != null">
				main_eqpmt_type,
			</if>
			<if test="eqpmtNum != null">
				eqpmt_num,
			</if>
			<if test="monthlySales != null">
				monthly_sales,
			</if>
			<if test="mainSalesProducts != null">
				main_sales_products,
			</if>
			<if test="visitingRecord != null">
				visiting_record,
			</if>
			<if test="longitude != null">
				longitude,
			</if>
			<if test="latitude != null">
				latitude,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="regionId != null">
				region_id,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="attribute1 != null">
				attribute1,
			</if>
			<if test="attribute2 != null">
				attribute2,
			</if>
			<if test="attribute3 != null">
				attribute3,
			</if>
			<if test="attribute4 != null">
				attribute4,
			</if>
			<if test="attribute5 != null">
				attribute5,
			</if>
			<if test="attribute6 != null">
				attribute6,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="projectName != null">
				#{projectName,jdbcType=VARCHAR},
			</if>
			<if test="photos != null">
				#{photos,jdbcType=VARCHAR},
			</if>
			<if test="projectArea != null">
				#{projectArea,jdbcType=VARCHAR},
			</if>
			<if test="projectRegionId != null">
				#{projectRegionId,jdbcType=BIGINT},
			</if>
			<if test="projectAddress != null">
				#{projectAddress,jdbcType=VARCHAR},
			</if>
			<if test="projectType != null">
				#{projectType,jdbcType=VARCHAR},
			</if>
			<if test="mainEqpmtType != null">
				#{mainEqpmtType,jdbcType=VARCHAR},
			</if>
			<if test="eqpmtNum != null">
				#{eqpmtNum,jdbcType=BIGINT},
			</if>
			<if test="monthlySales != null">
				#{monthlySales,jdbcType=BIGINT},
			</if>
			<if test="mainSalesProducts != null">
				#{mainSalesProducts,jdbcType=VARCHAR},
			</if>
			<if test="visitingRecord != null">
				#{visitingRecord,jdbcType=VARCHAR},
			</if>
			<if test="longitude != null">
				#{longitude,jdbcType=NUMERIC},
			</if>
			<if test="latitude != null">
				#{latitude,jdbcType=NUMERIC},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="regionId != null">
				#{regionId,jdbcType=BIGINT},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="attribute1 != null">
				#{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null">
				#{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null">
				#{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="attribute4 != null">
				#{attribute4,jdbcType=VARCHAR},
			</if>
			<if test="attribute5 != null">
				#{attribute5,jdbcType=VARCHAR},
			</if>
			<if test="attribute6 != null">
				#{attribute6,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_project_client
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.projectName != null">
				project_name = #{record.projectName,jdbcType=VARCHAR},
			</if>
			<if test="record.photos != null">
				photos = #{record.photos,jdbcType=VARCHAR},
			</if>
			<if test="record.projectArea != null">
				project_area = #{record.projectArea,jdbcType=VARCHAR},
			</if>
			<if test="record.projectRegionId != null">
				project_region_id = #{record.projectRegionId,jdbcType=BIGINT},
			</if>
			<if test="record.projectAddress != null">
				project_address = #{record.projectAddress,jdbcType=VARCHAR},
			</if>
			<if test="record.projectType != null">
				project_type = #{record.projectType,jdbcType=VARCHAR},
			</if>
			<if test="record.mainEqpmtType != null">
				main_eqpmt_type = #{record.mainEqpmtType,jdbcType=VARCHAR},
			</if>
			<if test="record.eqpmtNum != null">
				eqpmt_num = #{record.eqpmtNum,jdbcType=BIGINT},
			</if>
			<if test="record.monthlySales != null">
				monthly_sales = #{record.monthlySales,jdbcType=BIGINT},
			</if>
			<if test="record.mainSalesProducts != null">
				main_sales_products = #{record.mainSalesProducts,jdbcType=VARCHAR},
			</if>
			<if test="record.visitingRecord != null">
				visiting_record = #{record.visitingRecord,jdbcType=VARCHAR},
			</if>
			<if test="record.longitude != null">
				longitude = #{record.longitude,jdbcType=NUMERIC},
			</if>
			<if test="record.latitude != null">
				latitude = #{record.latitude,jdbcType=NUMERIC},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.regionId != null">
				region_id = #{record.regionId,jdbcType=BIGINT},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.attribute1 != null">
				attribute1 = #{record.attribute1,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute2 != null">
				attribute2 = #{record.attribute2,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute3 != null">
				attribute3 = #{record.attribute3,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute4 != null">
				attribute4 = #{record.attribute4,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute5 != null">
				attribute5 = #{record.attribute5,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute6 != null">
				attribute6 = #{record.attribute6,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.dsrkpi.model.ProjectClientExample">
		delete from wx_t_project_client
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.dsrkpi.model.ProjectClientExample" resultType="int">
		select count(1) from wx_t_project_client
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.ProjectClientExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_project_client
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.ProjectClientExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_project_client
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.project_name, t1.photos,
			 t1.project_area, t1.project_region_id, t1.project_address, t1.project_type, t1.main_eqpmt_type, t1.eqpmt_num,
			 t1.monthly_sales, t1.main_sales_products, t1.visiting_record, t1.longitude, t1.latitude, t1.status, t1.ext_flag,
			 t1.delete_flag, t1.region_id, t1.partner_id, t1.attribute1, t1.attribute2, t1.attribute3, t1.attribute4,
			 t1.attribute5, t1.attribute6
		  from wx_t_project_client t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.dsrkpi.model.ProjectClientParams">
		select 
			<choose>
				<when test="longitude == null">
					null relative_distance,
				</when>
				<otherwise>
					dbo.fun_cal_distance(${longitude}, ${latitude}, t1.longitude, t1.latitude) relative_distance,
				</otherwise>
			</choose>t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.project_name, t1.photos,
			 t1.project_area, t1.project_region_id, t1.project_address, t1.project_type, t1.main_eqpmt_type, t1.eqpmt_num,
			 t1.monthly_sales, t1.main_sales_products, t1.visiting_record, t1.longitude, t1.latitude, t1.status, t1.ext_flag,
			 t1.delete_flag, t1.region_id, t1.partner_id, t1.attribute1, t1.attribute2, t1.attribute3, t1.attribute4,
			 t1.attribute5, t1.attribute6
		  from wx_t_project_client t1
		 where  1 = 1 
		 <if test="deleteFlag != null">
			 and t1.delete_flag=#{deleteFlag}
		 </if>
	  	<if test="amStatus != null">
			<foreach collection="amStatus" item="item" index="index" open=" and t1.status in (" close=")" separator=",">
				'${item}'
			</foreach>
		</if>
		<if test="projectName != null and projectName != ''">
			AND t1.project_name like '%'+#{projectName}+'%'
		</if>
		<if test="partnerId != null">
			and t1.partner_id = #{partnerId}
		</if>
	</select>
</mapper>
