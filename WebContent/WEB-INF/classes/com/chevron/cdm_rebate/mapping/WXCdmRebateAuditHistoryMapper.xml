<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_rebate.dao.WXCdmRebateAuditHistoryMapper">
<resultMap id="BaseResultMap" type="com.chevron.cdm_rebate.model.WXCdmRebateAuditHistory">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="audit_id" jdbcType="BIGINT" property="auditId" />
	<result column="operator" jdbcType="BIGINT" property="operator" />
	<result column="operator_name" jdbcType="NVARCHAR" property="operatorName" />
	<result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
	<result column="step" jdbcType="NVARCHAR" property="step" />
	<result column="step_name" jdbcType="NVARCHAR" property="stepName" />
	<result column="opinion" jdbcType="NVARCHAR" property="opinion" />
	<result column="status" jdbcType="NVARCHAR" property="status" />
</resultMap>

<sql id="Base_Column_List">
	id, audit_id, operator, operator_name, operation_time, step, step_name, opinion, status
</sql>

<insert id="insert" parameterType="com.chevron.cdm_rebate.model.WXCdmRebateAuditHistory" useGeneratedKeys="true" keyProperty="id">
	insert into wx_cdm_rebate_audit_history (
	audit_id, 
	operator,
	operator_name,
	operation_time, 
	step,
	step_name,
	opinion,
	status
	) values (
	#{auditId, jdbcType=BIGINT},
	#{operator, jdbcType=BIGINT},
	#{operatorName, jdbcType=NVARCHAR},
	#{operationTime, jdbcType=TIMESTAMP},
	#{step, jdbcType=NVARCHAR},
	#{stepName, jdbcType=NVARCHAR},
	#{opinion, jdbcType=NVARCHAR},
	#{status, jdbcType=NVARCHAR}
	)
</insert>

<select id="getHistoryList" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_cdm_rebate_audit_history
	where audit_id = #{auditId, jdbcType=BIGINT}
</select>

<select id="getHistoryByUserId" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_cdm_rebate_audit_history
	where audit_id = #{auditId, jdbcType=BIGINT}
	and operator = #{userId, jdbcType=BIGINT}
	and status = #{status, jdbcType=NVARCHAR}
</select>

<update id="updateHistoryStatus">
    update wx_cdm_rebate_audit_history
    <set>
    	<if test="status != null and status != '' ">
			status = #{status, jdbcType=NVARCHAR},
		</if>
	</set>
	where audit_id = #{auditId,jdbcType=BIGINT}
</update>

</mapper>