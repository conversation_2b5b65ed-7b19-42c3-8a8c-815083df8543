<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_rebate.dao.WXCdmRebateSummaryMapper">
<resultMap id="BaseResultMap" type="com.chevron.cdm_rebate.model.WXCdmRebateAudit">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="status" jdbcType="NVARCHAR" property="status" />
	<result column="total_fund" jdbcType="DECIMAL" property="totalFund" />
	<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	<result column="create_by" jdbcType="BIGINT" property="createBy" />
	<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	<result column="update_by" jdbcType="BIGINT" property="updateBy" />
	<result column="create_by_name" jdbcType="NVARCHAR" property="createByName" />
	<result column="region_name" jdbcType="NVARCHAR" property="regionName" />
	<result column="is_summary" jdbcType="NVARCHAR" property="isSummary" />
	<result column="summary_id" jdbcType="BIGINT" property="summaryId" />
</resultMap>

<sql id="Base_Column_List">
	id, status, total_fund, create_time, create_by, update_time, update_by, create_by_name, region_name, is_summary, summary_id
</sql>

<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_cdm_rebate_audit
	where ID = #{id,jdbcType=BIGINT}
</select>

<insert id="insertSelective" parameterType="com.chevron.cdm_rebate.model.WXCdmRebateAudit" useGeneratedKeys="true" keyProperty="id">
    insert into wx_cdm_rebate_audit
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<if test="status != null and status != '' ">
			status,
		</if>
		<if test="totalFund != null">
			total_fund,
		</if>
		<if test="createTime != null">
			create_time,
		</if>
		<if test="createBy != null">
			create_by,
		</if>
		<if test="updateTime != null">
			update_time,
		</if>
		<if test="updateBy != null">
			update_by,
		</if>
		<if test="createByName != null and createByName != '' ">
			create_by_name,
		</if>
		<if test="regionName != null and regionName != '' ">
			region_name,
		</if>
		<if test="isSummary != null and isSummary != '' ">
			is_summary,
		</if>
		<if test="summaryId != null">
			summary_id,
		</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
		<if test="status != null and status != '' ">
			#{status, jdbcType=NVARCHAR},
		</if>
		<if test="totalFund != null">
			#{totalFund, jdbcType=DECIMAL},
		</if>
		<if test="createTime != null">
			#{createTime, jdbcType=TIMESTAMP},
		</if>
		<if test="createBy != null">
			#{createBy, jdbcType=BIGINT},
		</if>
		<if test="updateTime != null">
			#{updateTime, jdbcType=TIMESTAMP},
		</if>
		<if test="updateBy != null">
			#{updateBy, jdbcType=BIGINT},
		</if>
		<if test="createByName != null and createByName != '' ">
			#{createByName, jdbcType=NVARCHAR},
		</if>
		<if test="regionName != null and regionName != '' ">
			#{regionName, jdbcType=NVARCHAR},
		</if>
		<if test="isSummary != null and isSummary != '' ">
			#{isSummary, jdbcType=NVARCHAR},
		</if>
		<if test="summaryId != null">
			#{summaryId, jdbcType=BIGINT},
		</if>
	</trim>
</insert>

<update id="updateSelective" parameterType="com.chevron.cdm_rebate.model.WXCdmRebateAudit">
    update wx_cdm_rebate_audit
    <set>
    	<if test="status != null and status != '' ">
			status = #{status, jdbcType=NVARCHAR},
		</if>
		<if test="totalFund != null">
			total_fund = #{totalFund, jdbcType=DECIMAL},
		</if>
		<if test="updateTime != null">
			update_time = #{updateTime, jdbcType=TIMESTAMP},
		</if>
		<if test="updateBy != null">
			update_by = #{updateBy, jdbcType=BIGINT},
		</if>
		<if test="isSummary != null and isSummary != '' ">
			is_summary = #{isSummary, jdbcType=NVARCHAR},
		</if>
		<if test="summaryId != null">
			summary_id = #{summaryId, jdbcType=BIGINT},
		</if>
	</set>
	where id = #{id,jdbcType=BIGINT}
</update>

<update id="clearSummaryId" parameterType="java.lang.Long">
	update wx_cdm_rebate_audit
	set summary_id = null
	where summary_id = #{summaryId, jdbcType=BIGINT}
</update>

<select id="getSummaryAudit" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_cdm_rebate_audit
	where summary_id = #{summaryId, jdbcType=BIGINT}
	and is_summary = '-1'
</select>

<select id="getRebateAuditByPage" parameterType="com.chevron.cdm_rebate.dto.request.RebateAuditPageRequest" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_cdm_rebate_audit
	where 1=1
	<if test='roleSearchType == "bsNorth" or roleSearchType == "bsSouth" or roleSearchType == "bsSW" '>
		and create_by = #{userId, jdbcType=BIGINT}
	</if>
	<if test='roleSearchType == "channelAudit" '>
		and is_summary in ('0', '-1')
	</if>
	<if test='roleSearchType == "bsmAudit" or roleSearchType == "sdoAudit" or roleSearchType == "abmAudit" '>
		and is_summary in ('0', '1')
	</if>
	<if test="exStatus != '' and exStatus != null ">
		and status != #{exStatus, jdbcType=NVARCHAR}
	</if>
	<if test="statusList != null and statusList != '' ">
		and status in
		<foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
			${item}
		</foreach>
	</if>
	<if test="startStatus != null and startStatus != '' ">
		and status+0 > #{startStatus, jdbcType=NVARCHAR}
	</if>
</select>

</mapper>