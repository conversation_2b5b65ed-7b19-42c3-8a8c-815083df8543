<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.o2oorder.dao.O2OCardToPartnerDetailMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.o2oorder.model.O2OCardToPartnerDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="o2ocard_sendid" property="o2ocardSendid" jdbcType="BIGINT" />
    <result column="o2ocard_batchno" property="o2ocardBatchno" jdbcType="NVARCHAR" />
    <result column="o2ocard_no" property="o2ocardNo" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_active_flag" property="isActiveFlag" jdbcType="NVARCHAR" />
    <result column="card_type" property="cardType" jdbcType="NVARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, o2ocard_sendid, o2ocard_batchno, o2ocard_no, create_time, update_time, is_active_flag, 
    card_type, partner_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetailExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_o2ocard_to_partner_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetailExample" >
    delete from wx_t_o2ocard_to_partner_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetail" >
    insert into wx_t_o2ocard_to_partner_detail (id, o2ocard_sendid, o2ocard_batchno, 
      o2ocard_no, create_time, update_time, 
      is_active_flag, card_type, partner_id
      )
    values (#{id,jdbcType=BIGINT}, #{o2ocardSendid,jdbcType=BIGINT}, #{o2ocardBatchno,jdbcType=NVARCHAR}, 
      #{o2ocardNo,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isActiveFlag,jdbcType=NVARCHAR}, #{cardType,jdbcType=NVARCHAR}, #{partnerId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetail" >
    insert into wx_t_o2ocard_to_partner_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="o2ocardSendid != null" >
        o2ocard_sendid,
      </if>
      <if test="o2ocardBatchno != null" >
        o2ocard_batchno,
      </if>
      <if test="o2ocardNo != null" >
        o2ocard_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isActiveFlag != null" >
        is_active_flag,
      </if>
      <if test="cardType != null" >
        card_type,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="o2ocardSendid != null" >
        #{o2ocardSendid,jdbcType=BIGINT},
      </if>
      <if test="o2ocardBatchno != null" >
        #{o2ocardBatchno,jdbcType=NVARCHAR},
      </if>
      <if test="o2ocardNo != null" >
        #{o2ocardNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isActiveFlag != null" >
        #{isActiveFlag,jdbcType=NVARCHAR},
      </if>
      <if test="cardType != null" >
        #{cardType,jdbcType=NVARCHAR},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_o2ocard_to_partner_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.o2ocardSendid != null" >
        o2ocard_sendid = #{record.o2ocardSendid,jdbcType=BIGINT},
      </if>
      <if test="record.o2ocardBatchno != null" >
        o2ocard_batchno = #{record.o2ocardBatchno,jdbcType=NVARCHAR},
      </if>
      <if test="record.o2ocardNo != null" >
        o2ocard_no = #{record.o2ocardNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isActiveFlag != null" >
        is_active_flag = #{record.isActiveFlag,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardType != null" >
        card_type = #{record.cardType,jdbcType=NVARCHAR},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_o2ocard_to_partner_detail
    set id = #{record.id,jdbcType=BIGINT},
      o2ocard_sendid = #{record.o2ocardSendid,jdbcType=BIGINT},
      o2ocard_batchno = #{record.o2ocardBatchno,jdbcType=NVARCHAR},
      o2ocard_no = #{record.o2ocardNo,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_active_flag = #{record.isActiveFlag,jdbcType=NVARCHAR},
      card_type = #{record.cardType,jdbcType=NVARCHAR},
      partner_id = #{record.partnerId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetail" >
    update wx_t_o2ocard_to_partner_detail
    <set >
      <if test="o2ocardSendid != null" >
        o2ocard_sendid = #{o2ocardSendid,jdbcType=BIGINT},
      </if>
      <if test="o2ocardBatchno != null" >
        o2ocard_batchno = #{o2ocardBatchno,jdbcType=NVARCHAR},
      </if>
      <if test="o2ocardNo != null" >
        o2ocard_no = #{o2ocardNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isActiveFlag != null" >
        is_active_flag = #{isActiveFlag,jdbcType=NVARCHAR},
      </if>
      <if test="cardType != null" >
        card_type = #{cardType,jdbcType=NVARCHAR},
      </if>
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerDetail" >
    update wx_t_o2ocard_to_partner_detail
    set o2ocard_sendid = #{o2ocardSendid,jdbcType=BIGINT},
      o2ocard_batchno = #{o2ocardBatchno,jdbcType=NVARCHAR},
      o2ocard_no = #{o2ocardNo,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_active_flag = #{isActiveFlag,jdbcType=NVARCHAR},
      card_type = #{cardType,jdbcType=NVARCHAR},
      partner_id = #{partnerId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  
  <insert id="batchInsert" parameterType="java.util.List" >
   insert into wx_t_o2ocard_to_partner_detail (o2ocard_sendid, o2ocard_batchno, 
      o2ocard_no, create_time, update_time, 
      is_active_flag, card_type, partner_id
      )
    values 
    <foreach collection="list" index="index" item="item" separator=",">
	   	<trim prefix="(" suffix=")" suffixOverrides=",">
	  #{item.o2ocardSendid,jdbcType=BIGINT}, #{item.o2ocardBatchno,jdbcType=NVARCHAR}, 
      #{item.o2ocardNo,jdbcType=NVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
      #{item.isActiveFlag,jdbcType=NVARCHAR}, #{item.cardType,jdbcType=NVARCHAR}, #{item.partnerId,jdbcType=BIGINT}
	   	</trim>
	</foreach>
  </insert> 
  
   <select id="selectByO2OCardBatchId" resultMap="BaseResultMap" parameterType="map" >
   
   	select tt.* from wx_t_o2ocard_to_partner_detail tt
   	where
   		tt.o2ocard_sendid = #{o2ocardBatchId,jdbcType=BIGINT}
   
   </select>
  
   <select id="selectByCardNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner_detail
    where o2ocard_no = #{cardno,jdbcType=NVARCHAR}
  </select>
  
  <select id="selectByO2OCardNos" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner_detail
    where 
    <if test="cardnos!=null">
	    	o2ocard_no in
	    	<foreach item="cardno" index="index" collection="cardnos" open="(" separator="," close=")">  
				 #{cardno}  
			</foreach> 
			and
	 </if>
	 1 = 1
  </select>
  
  
  
  
</mapper>