<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.o2oorder.dao.O2OCardTypeInfoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.o2oorder.model.O2OCardTypeInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="card_type" property="cardType" jdbcType="NVARCHAR" />
    <result column="card_service_times" property="cardServiceTimes" jdbcType="NVARCHAR" />
    <result column="card_total_price" property="cardTotalPrice" jdbcType="NVARCHAR" />
    <result column="card_sale_price" property="cardSalePrice" jdbcType="NVARCHAR" />
    <result column="card_discount_price" property="cardDiscountPrice" jdbcType="NVARCHAR" />
    <result column="card_createtime" property="cardCreatetime" jdbcType="TIMESTAMP" />
    <result column="card_invalidtime" property="cardInvalidtime" jdbcType="TIMESTAMP" />
    <result column="card_effectivetime" property="cardEffectivetime" jdbcType="TIMESTAMP" />
    <result column="card_effectivetime" property="cardEffectivetime" jdbcType="TIMESTAMP" />
    <result column="card_desc" property="cardDesc" jdbcType="NVARCHAR" />
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, card_type, card_service_times, card_total_price, card_sale_price, card_discount_price, 
    card_createtime, card_invalidtime, card_effectivetime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardtype_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardtype_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_o2o_cardtype_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfoExample" >
    delete from wx_t_o2o_cardtype_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfo" >
    insert into wx_t_o2o_cardtype_info (id, card_type, card_service_times, 
      card_total_price, card_sale_price, card_discount_price, 
      card_createtime, card_invalidtime, card_effectivetime
      )
    values (#{id,jdbcType=BIGINT}, #{cardType,jdbcType=NVARCHAR}, #{cardServiceTimes,jdbcType=NVARCHAR}, 
      #{cardTotalPrice,jdbcType=NVARCHAR}, #{cardSalePrice,jdbcType=NVARCHAR}, #{cardDiscountPrice,jdbcType=NVARCHAR}, 
      #{cardCreatetime,jdbcType=TIMESTAMP}, #{cardInvalidtime,jdbcType=TIMESTAMP}, #{cardEffectivetime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfo" >
    insert into wx_t_o2o_cardtype_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cardType != null" >
        card_type,
      </if>
      <if test="cardServiceTimes != null" >
        card_service_times,
      </if>
      <if test="cardTotalPrice != null" >
        card_total_price,
      </if>
      <if test="cardSalePrice != null" >
        card_sale_price,
      </if>
      <if test="cardDiscountPrice != null" >
        card_discount_price,
      </if>
      <if test="cardCreatetime != null" >
        card_createtime,
      </if>
      <if test="cardInvalidtime != null" >
        card_invalidtime,
      </if>
      <if test="cardEffectivetime != null" >
        card_effectivetime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cardType != null" >
        #{cardType,jdbcType=NVARCHAR},
      </if>
      <if test="cardServiceTimes != null" >
        #{cardServiceTimes,jdbcType=NVARCHAR},
      </if>
      <if test="cardTotalPrice != null" >
        #{cardTotalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardSalePrice != null" >
        #{cardSalePrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardDiscountPrice != null" >
        #{cardDiscountPrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardCreatetime != null" >
        #{cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardInvalidtime != null" >
        #{cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardEffectivetime != null" >
        #{cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_o2o_cardtype_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardType != null" >
        card_type = #{record.cardType,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardServiceTimes != null" >
        card_service_times = #{record.cardServiceTimes,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardTotalPrice != null" >
        card_total_price = #{record.cardTotalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardSalePrice != null" >
        card_sale_price = #{record.cardSalePrice,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardDiscountPrice != null" >
        card_discount_price = #{record.cardDiscountPrice,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardCreatetime != null" >
        card_createtime = #{record.cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cardInvalidtime != null" >
        card_invalidtime = #{record.cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cardEffectivetime != null" >
        card_effectivetime = #{record.cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_o2o_cardtype_info
    set id = #{record.id,jdbcType=BIGINT},
      card_type = #{record.cardType,jdbcType=NVARCHAR},
      card_service_times = #{record.cardServiceTimes,jdbcType=NVARCHAR},
      card_total_price = #{record.cardTotalPrice,jdbcType=NVARCHAR},
      card_sale_price = #{record.cardSalePrice,jdbcType=NVARCHAR},
      card_discount_price = #{record.cardDiscountPrice,jdbcType=NVARCHAR},
      card_createtime = #{record.cardCreatetime,jdbcType=TIMESTAMP},
      card_invalidtime = #{record.cardInvalidtime,jdbcType=TIMESTAMP},
      card_effectivetime = #{record.cardEffectivetime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfo" >
    update wx_t_o2o_cardtype_info
    <set >
      <if test="cardType != null" >
        card_type = #{cardType,jdbcType=NVARCHAR},
      </if>
      <if test="cardServiceTimes != null" >
        card_service_times = #{cardServiceTimes,jdbcType=NVARCHAR},
      </if>
      <if test="cardTotalPrice != null" >
        card_total_price = #{cardTotalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardSalePrice != null" >
        card_sale_price = #{cardSalePrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardDiscountPrice != null" >
        card_discount_price = #{cardDiscountPrice,jdbcType=NVARCHAR},
      </if>
      <if test="cardCreatetime != null" >
        card_createtime = #{cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardInvalidtime != null" >
        card_invalidtime = #{cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardEffectivetime != null" >
        card_effectivetime = #{cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.o2oorder.model.O2OCardTypeInfo" >
    update wx_t_o2o_cardtype_info
    set card_type = #{cardType,jdbcType=NVARCHAR},
      card_service_times = #{cardServiceTimes,jdbcType=NVARCHAR},
      card_total_price = #{cardTotalPrice,jdbcType=NVARCHAR},
      card_sale_price = #{cardSalePrice,jdbcType=NVARCHAR},
      card_discount_price = #{cardDiscountPrice,jdbcType=NVARCHAR},
      card_createtime = #{cardCreatetime,jdbcType=TIMESTAMP},
      card_invalidtime = #{cardInvalidtime,jdbcType=TIMESTAMP},
      card_effectivetime = #{cardEffectivetime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <select id="selectByCardType" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardtype_info
    where card_type = #{cardType,jdbcType=NVARCHAR}
  </select>
  
</mapper>