<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.o2oorder.dao.O2OCardToPartnerMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.o2oorder.model.O2OCardToPartner" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="card_acount" property="cardAcount" jdbcType="INTEGER" />
    <result column="card_degree_acount" property="cardDegreeAcount" jdbcType="INTEGER" />
    <result column="deposit" property="deposit" jdbcType="NUMERIC" />
    <result column="degree_deposit" property="degreeDeposit" jdbcType="NUMERIC" />
    <result column="batch_no" property="batchNo" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="card_type" property="cardType" jdbcType="NVARCHAR" />
    <result column="card_active_acount" property="cardActiveAcount" jdbcType="INTEGER" />
    <result column="card_desc" property="cardTypeDesc" jdbcType="NVARCHAR" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    
    
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, partner_name, card_acount, card_degree_acount, deposit, degree_deposit, 
    batch_no, create_time, update_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2ocard_to_partner
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_o2ocard_to_partner
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerExample" >
    delete from wx_t_o2ocard_to_partner
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.o2oorder.model.O2OCardToPartner" >
    insert into wx_t_o2ocard_to_partner (id, partner_id, partner_name, 
      card_acount, card_degree_acount, deposit, 
      degree_deposit, batch_no, create_time, 
      update_time, remark)
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, 
      #{cardAcount,jdbcType=INTEGER}, #{cardDegreeAcount,jdbcType=INTEGER}, #{deposit,jdbcType=NUMERIC}, 
      #{degreeDeposit,jdbcType=NUMERIC}, #{batchNo,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.o2oorder.model.O2OCardToPartner" >
    insert into wx_t_o2ocard_to_partner
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerName != null" >
        partner_name,
      </if>
      <if test="cardAcount != null" >
        card_acount,
      </if>
      <if test="cardDegreeAcount != null" >
        card_degree_acount,
      </if>
      <if test="deposit != null" >
        deposit,
      </if>
      <if test="degreeDeposit != null" >
        degree_deposit,
      </if>
      <if test="batchNo != null" >
        batch_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="cardType != null" >
        card_type,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="cardAcount != null" >
        #{cardAcount,jdbcType=INTEGER},
      </if>
      <if test="cardDegreeAcount != null" >
        #{cardDegreeAcount,jdbcType=INTEGER},
      </if>
      <if test="deposit != null" >
        #{deposit,jdbcType=NUMERIC},
      </if>
      <if test="degreeDeposit != null" >
        #{degreeDeposit,jdbcType=NUMERIC},
      </if>
      <if test="batchNo != null" >
        #{batchNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
       <if test="cardType != null" >
        #{cardType,jdbcType=NVARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_o2ocard_to_partner
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null" >
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardAcount != null" >
        card_acount = #{record.cardAcount,jdbcType=INTEGER},
      </if>
      <if test="record.cardDegreeAcount != null" >
        card_degree_acount = #{record.cardDegreeAcount,jdbcType=INTEGER},
      </if>
      <if test="record.deposit != null" >
        deposit = #{record.deposit,jdbcType=NUMERIC},
      </if>
      <if test="record.degreeDeposit != null" >
        degree_deposit = #{record.degreeDeposit,jdbcType=NUMERIC},
      </if>
      <if test="record.batchNo != null" >
        batch_no = #{record.batchNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_o2ocard_to_partner
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      card_acount = #{record.cardAcount,jdbcType=INTEGER},
      card_degree_acount = #{record.cardDegreeAcount,jdbcType=INTEGER},
      deposit = #{record.deposit,jdbcType=NUMERIC},
      degree_deposit = #{record.degreeDeposit,jdbcType=NUMERIC},
      batch_no = #{record.batchNo,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.o2oorder.model.O2OCardToPartner" >
    update wx_t_o2ocard_to_partner
    <set >
      <if test="partnerId != null" >
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        partner_name = #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="cardAcount != null" >
        card_acount = #{cardAcount,jdbcType=INTEGER},
      </if>
      <if test="cardDegreeAcount != null" >
        card_degree_acount = #{cardDegreeAcount,jdbcType=INTEGER},
      </if>
      <if test="deposit != null" >
        deposit = #{deposit,jdbcType=NUMERIC},
      </if>
      <if test="degreeDeposit != null" >
        degree_deposit = #{degreeDeposit,jdbcType=NUMERIC},
      </if>
      <if test="batchNo != null" >
        batch_no = #{batchNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.o2oorder.model.O2OCardToPartner" >
    update wx_t_o2ocard_to_partner
    set partner_id = #{partnerId,jdbcType=BIGINT},
      partner_name = #{partnerName,jdbcType=NVARCHAR},
      card_acount = #{cardAcount,jdbcType=INTEGER},
      card_degree_acount = #{cardDegreeAcount,jdbcType=INTEGER},
      deposit = #{deposit,jdbcType=NUMERIC},
      degree_deposit = #{degreeDeposit,jdbcType=NUMERIC},
      batch_no = #{batchNo,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getO2OCardToPartnerByParams" parameterType="com.chevron.o2oorder.model.O2OCardToPartnerParmas" resultMap="BaseResultMap">
    SELECT DISTINCT  tt.* ,tt3.card_desc,tt.card_acount-tt.card_degree_acount card_active_acount
    	FROM wx_t_o2ocard_to_partner tt 
    	LEFT JOIN wx_t_o2ocard_to_partner_detail tt2 ON tt.id = tt2.o2ocard_sendid
    	LEFT JOIN wx_t_o2o_cardtype_info tt3 ON tt.card_type=tt3.card_type
  	where
  	 <!-- 高级搜索  卡号？？？？-->
     <if test="queryType==1"> 
     	<if test="partnerName!=null and partnerName!='' ">
     	    tt.partner_name  = #{partnerName,jdbcType=NVARCHAR}
     	  and
     	</if>
     	<if test="partnerId!=null and partnerId!='' ">
     	    tt.partner_id  = #{partnerId,jdbcType=NVARCHAR}
     	  and
     	</if>
     	<if test="cardNo!=null and cardNo!='' ">
     	    tt2.o2ocard_no  = #{cardNo,jdbcType=NVARCHAR}
     	  and
     	</if>
     	<if test="isActiveFlag!=null and isActiveFlag!='' ">
     		tt2.is_active_flag = #{isActiveFlag,jdbcType=NVARCHAR}
     	  and
     	</if>
     	<if test="createTime!=null and createTime!='' " >
			SUBSTRING(CONVERT(CHAR(19), tt.create_time, 120),1,10) = #{createTime,jdbcType=NVARCHAR}  
		  and
		</if>
		<if test="updateTime!=null and updateTime!='' " >
			SUBSTRING(CONVERT(CHAR(19), tt2.update_time, 120),1,10) = #{updateTime,jdbcType=NVARCHAR}  
		  and
		</if>
		
     </if>
     
     <!-- 关键字搜索  卡号？？？？ -->
     <if test="queryType==2"> 
     	 <if test="queryField!=null">
     	   (tt.partner_name like '%' + #{queryField} + '%'
     	      or
     	    tt2.o2ocard_no like '%' + #{queryField} + '%'
     	   )
     	   and
     	 </if>
     </if>
     1=1
  </select>
  
   <select id="selectByPrimaryKeyNew" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    	tt.*,tt2.card_desc,tt.card_acount-tt.card_degree_acount card_active_acount
    from wx_t_o2ocard_to_partner tt
    LEFT JOIN
    	wx_t_o2o_cardtype_info tt2
    ON tt.card_type=tt2.card_type
    where tt.id =  #{id,jdbcType=BIGINT}
  </select>
  
  
  
  
  
</mapper>