<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.o2oorder.dao.O2OCardOwnerInfoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.o2oorder.model.O2OCardOwnerInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="memberid" property="memberid" jdbcType="NVARCHAR" />
    <result column="order_card_type" property="orderCardType" jdbcType="NVARCHAR" />
    <result column="card_owner" property="cardOwner" jdbcType="NVARCHAR" />
    <result column="card_owner_phone" property="cardOwnerPhone" jdbcType="NVARCHAR" />
    <result column="card_service_times" property="cardServiceTimes" jdbcType="INTEGER" />
    <result column="card_enable_times" property="cardEnableTimes" jdbcType="INTEGER" />
    <result column="card_createtime" property="cardCreatetime" jdbcType="TIMESTAMP" />
    <result column="card_invalidtime" property="cardInvalidtime" jdbcType="TIMESTAMP" />
    <result column="card_effectivetime" property="cardEffectivetime" jdbcType="TIMESTAMP" />
    <result column="cardno" property="cardNo" jdbcType="NVARCHAR" />
    <result column="creator" property="creator" jdbcType="BIGINT" />
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, memberid, order_card_type, card_owner, card_owner_phone, card_service_times, 
    card_enable_times, card_createtime, card_invalidtime, card_effectivetime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardowner_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardowner_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_o2o_cardowner_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfoExample" >
    delete from wx_t_o2o_cardowner_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfo" >
    insert into wx_t_o2o_cardowner_info (id, memberid, order_card_type, 
      card_owner, card_owner_phone, card_service_times, 
      card_enable_times, card_createtime, card_invalidtime, 
      card_effectivetime)
    values (#{id,jdbcType=BIGINT}, #{memberid,jdbcType=NVARCHAR}, #{orderCardType,jdbcType=NVARCHAR}, 
      #{cardOwner,jdbcType=NVARCHAR}, #{cardOwnerPhone,jdbcType=NVARCHAR}, #{cardServiceTimes,jdbcType=INTEGER}, 
      #{cardEnableTimes,jdbcType=INTEGER}, #{cardCreatetime,jdbcType=TIMESTAMP}, #{cardInvalidtime,jdbcType=TIMESTAMP}, 
      #{cardEffectivetime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfo" >
    insert into wx_t_o2o_cardowner_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="memberid != null" >
        memberid,
      </if>
      <if test="orderCardType != null" >
        order_card_type,
      </if>
      <if test="cardOwner != null" >
        card_owner,
      </if>
      <if test="cardOwnerPhone != null" >
        card_owner_phone,
      </if>
      <if test="cardServiceTimes != null" >
        card_service_times,
      </if>
      <if test="cardEnableTimes != null" >
        card_enable_times,
      </if>
      <if test="cardCreatetime != null" >
        card_createtime,
      </if>
      <if test="cardInvalidtime != null" >
        card_invalidtime,
      </if>
      <if test="cardEffectivetime != null" >
        card_effectivetime,
      </if>
       <if test="cardNo != null" >
        cardno,
      </if>
       <if test="creator != null" >
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="memberid != null" >
        #{memberid,jdbcType=NVARCHAR},
      </if>
      <if test="orderCardType != null" >
        #{orderCardType,jdbcType=NVARCHAR},
      </if>
      <if test="cardOwner != null" >
        #{cardOwner,jdbcType=NVARCHAR},
      </if>
      <if test="cardOwnerPhone != null" >
        #{cardOwnerPhone,jdbcType=NVARCHAR},
      </if>
      <if test="cardServiceTimes != null" >
        #{cardServiceTimes,jdbcType=INTEGER},
      </if>
      <if test="cardEnableTimes != null" >
        #{cardEnableTimes,jdbcType=INTEGER},
      </if>
      <if test="cardCreatetime != null" >
        #{cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardInvalidtime != null" >
        #{cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardEffectivetime != null" >
        #{cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
       <if test="cardNo != null" >
        #{cardNo,jdbcType=NVARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_o2o_cardowner_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberid != null" >
        memberid = #{record.memberid,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderCardType != null" >
        order_card_type = #{record.orderCardType,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardOwner != null" >
        card_owner = #{record.cardOwner,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardOwnerPhone != null" >
        card_owner_phone = #{record.cardOwnerPhone,jdbcType=NVARCHAR},
      </if>
      <if test="record.cardServiceTimes != null" >
        card_service_times = #{record.cardServiceTimes,jdbcType=INTEGER},
      </if>
      <if test="record.cardEnableTimes != null" >
        card_enable_times = #{record.cardEnableTimes,jdbcType=INTEGER},
      </if>
      <if test="record.cardCreatetime != null" >
        card_createtime = #{record.cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cardInvalidtime != null" >
        card_invalidtime = #{record.cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cardEffectivetime != null" >
        card_effectivetime = #{record.cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
       <if test="record.cardNo != null" >
        cardno = #{record.cardNo,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_o2o_cardowner_info
    set id = #{record.id,jdbcType=BIGINT},
      memberid = #{record.memberid,jdbcType=NVARCHAR},
      order_card_type = #{record.orderCardType,jdbcType=NVARCHAR},
      card_owner = #{record.cardOwner,jdbcType=NVARCHAR},
      card_owner_phone = #{record.cardOwnerPhone,jdbcType=NVARCHAR},
      card_service_times = #{record.cardServiceTimes,jdbcType=INTEGER},
      card_enable_times = #{record.cardEnableTimes,jdbcType=INTEGER},
      card_createtime = #{record.cardCreatetime,jdbcType=TIMESTAMP},
      card_invalidtime = #{record.cardInvalidtime,jdbcType=TIMESTAMP},
      card_effectivetime = #{record.cardEffectivetime,jdbcType=TIMESTAMP},
      cardno = #{record.cardNo,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfo" >
    update wx_t_o2o_cardowner_info
    <set >
      <if test="memberid != null" >
        memberid = #{memberid,jdbcType=NVARCHAR},
      </if>
      <if test="orderCardType != null" >
        order_card_type = #{orderCardType,jdbcType=NVARCHAR},
      </if>
      <if test="cardOwner != null" >
        card_owner = #{cardOwner,jdbcType=NVARCHAR},
      </if>
      <if test="cardOwnerPhone != null" >
        card_owner_phone = #{cardOwnerPhone,jdbcType=NVARCHAR},
      </if>
      <if test="cardServiceTimes != null" >
        card_service_times = #{cardServiceTimes,jdbcType=INTEGER},
      </if>
      <if test="cardEnableTimes != null" >
        card_enable_times = #{cardEnableTimes,jdbcType=INTEGER},
      </if>
      <if test="cardCreatetime != null" >
        card_createtime = #{cardCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardInvalidtime != null" >
        card_invalidtime = #{cardInvalidtime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardEffectivetime != null" >
        card_effectivetime = #{cardEffectivetime,jdbcType=TIMESTAMP},
      </if>
       <if test="cardNo != null" >
       	cardno = #{cardNo,jdbcType=NVARCHAR},
       </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.o2oorder.model.O2OCardOwnerInfo" >
    update wx_t_o2o_cardowner_info
    set memberid = #{memberid,jdbcType=NVARCHAR},
      order_card_type = #{orderCardType,jdbcType=NVARCHAR},
      card_owner = #{cardOwner,jdbcType=NVARCHAR},
      card_owner_phone = #{cardOwnerPhone,jdbcType=NVARCHAR},
      card_service_times = #{cardServiceTimes,jdbcType=INTEGER},
      card_enable_times = #{cardEnableTimes,jdbcType=INTEGER},
      card_createtime = #{cardCreatetime,jdbcType=TIMESTAMP},
      card_invalidtime = #{cardInvalidtime,jdbcType=TIMESTAMP},
      card_effectivetime = #{cardEffectivetime,jdbcType=TIMESTAMP},
      cardno = #{cardNo,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="selectByMemberidAndCardNo" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardowner_info
    where memberid = #{memberid,jdbcType=NVARCHAR}
    and cardno = #{cardNo,jdbcType=NVARCHAR}
  </select>
  
  
   <update id="updateByMemberidAndCardNo" parameterType="map" >
    update wx_t_o2o_cardowner_info
    set 
      card_enable_times = #{cardEnableTimes,jdbcType=INTEGER}
    where memberid = #{memberid,jdbcType=NVARCHAR}
    and cardno = #{cardNo,jdbcType=NVARCHAR}
  </update>
  
  <select id="selectByCardNo" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_o2o_cardowner_info
    where cardno = #{cardno_for_memberid,jdbcType=NVARCHAR}
  </select>
  
   <select id="selectByMemberidForCountServiceTimes" resultType="java.lang.Integer" parameterType="map" >
     select 
 		sum(card_enable_times)
    from wx_t_o2o_cardowner_info
    where memberid = #{memberid,jdbcType=NVARCHAR}
  </select>
  
  
  
  
  
</mapper>