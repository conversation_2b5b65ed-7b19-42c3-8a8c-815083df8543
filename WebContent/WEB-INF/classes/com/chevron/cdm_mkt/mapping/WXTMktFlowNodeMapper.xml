<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.WXTMktFlowNodeMapper">
    <resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.WXTMktFlowNode">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="mkt_apply_id" jdbcType="BIGINT" property="mktApplyId"/>
        <result column="operator" jdbcType="BIGINT" property="operator"/>
        <result column="operator_name" jdbcType="NVARCHAR" property="operatorName"/>
        <result column="step" jdbcType="INTEGER" property="step"/>
        <result column="status" jdbcType="NVARCHAR" property="status"/>
        <result column="node_user_role" jdbcType="NVARCHAR" property="nodeUserRole"/>
        <result column="step_name" jdbcType="NVARCHAR" property="stepName"/>
    </resultMap>

    <sql id="Base_Column_List">
id, mkt_apply_id, operator, operator_name, step, status, node_user_role, step_name
</sql>


    <update id="updateStatus" parameterType="com.chevron.cdm_mkt.model.WXTMktFlowNode">
        update wx_t_mkt_flow_node
        <set>
            <if test="status != null and status != ''">
                status = #{status, jdbcType=NVARCHAR},
            </if>
        </set>
        <where>
            mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
            and step = #{step, jdbcType=INTEGER}
            <if test='operator != null and operator != ""'>
                and operator = #{operator, jdbcType=BIGINT}
            </if>
        </where>
    </update>

    <select id="selectFlowNodeList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        ORDER BY step
    </select>

    <select id="selectFlowNodeListAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node h
        <where>
            <if test="ids != null and ids.size() > 0">
                h.mkt_apply_id IN
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                AND status = #{status, jdbcType=NVARCHAR}
            </if>
        </where>
        ORDER BY h.id ASC
    </select>

    <select id="selectNextFlowNode" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select TOP 1
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        AND status='0'
        ORDER BY step
    </select>

    <select id="selectFlowNodeByStep" resultMap="BaseResultMap">
        select TOP 1
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        and step = #{step, jdbcType=NVARCHAR}
        <if test="excludeOperator != null">
        and operator!=#{excludeOperator, jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectFlowNodesByStep" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        and step = #{step, jdbcType=NVARCHAR}
        <if test="status != null and status != ''">
            AND status = #{status, jdbcType=NVARCHAR}
        </if>
        ORDER BY status
    </select>

    <select id="selectFlowNodesByStep2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        and step = #{step, jdbcType=NVARCHAR}
        <if test="status != null and status != ''">
            AND status != #{status, jdbcType=NVARCHAR}
        </if>
        ORDER BY status
    </select>

    <select id="selectFlowNodeByUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_flow_node
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        and operator = #{userId, jdbcType=BIGINT}
    </select>

    <delete id="deleteFlowNodeList" parameterType="java.lang.Long">
    delete from wx_t_mkt_flow_node
    where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
</delete>


</mapper>