<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.WxTEstimatedMonthlySalesMapper">
  <resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.vo.WxTEstimatedMonthlySales">
    <!--@mbg.generated-->
    <!--@Table wx_t_estimated_monthly_sales-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="apply_year" jdbcType="INTEGER" property="applyYear" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="sales_id" jdbcType="VARCHAR" property="salesId" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="qhc_estimated_monthly_sales" jdbcType="DECIMAL" property="qhcEstimatedMonthlySales" />
    <result column="hcx_estimated_monthly_sales" jdbcType="DECIMAL" property="hcxEstimatedMonthlySales" />
    <result column="other_estimated_monthly_sales" jdbcType="DECIMAL" property="otherEstimatedMonthlySales" />
    <result column="total_estimated_monthly_sales" jdbcType="DECIMAL" property="totalEstimatedMonthlySales" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="del_flag" jdbcType="BIT" property="delFlag" />
    <result column="brand" jdbcType="INTEGER" property="brand" />
    <result column="expense_code" jdbcType="VARCHAR" property="expenseCode" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="dl_estimated_monthly_sales" jdbcType="DECIMAL" property="dlEstimatedMonthlySales" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, distributor_id, apply_year, request_no, sales_id, region, customer_id, qhc_estimated_monthly_sales, 
    hcx_estimated_monthly_sales, other_estimated_monthly_sales, total_estimated_monthly_sales, 
    update_time, create_user, del_flag, brand, expense_code, update_user, create_time, 
    dl_estimated_monthly_sales
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from wx_t_estimated_monthly_sales
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from wx_t_estimated_monthly_sales
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.chevron.cdm_mkt.model.vo.WxTEstimatedMonthlySales" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into wx_t_estimated_monthly_sales (distributor_id, apply_year, request_no, 
      sales_id, region, customer_id, 
      qhc_estimated_monthly_sales, hcx_estimated_monthly_sales, 
      other_estimated_monthly_sales, total_estimated_monthly_sales, 
      update_time, create_user, del_flag, 
      brand, expense_code, update_user, 
      create_time, dl_estimated_monthly_sales)
    values (#{distributorId,jdbcType=BIGINT}, #{applyYear,jdbcType=INTEGER}, #{requestNo,jdbcType=VARCHAR}, 
      #{salesId,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}, #{customerId,jdbcType=BIGINT}, 
      #{qhcEstimatedMonthlySales,jdbcType=DECIMAL}, #{hcxEstimatedMonthlySales,jdbcType=DECIMAL}, 
      #{otherEstimatedMonthlySales,jdbcType=DECIMAL}, #{totalEstimatedMonthlySales,jdbcType=DECIMAL}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT}, #{delFlag,jdbcType=BIT}, 
      #{brand,jdbcType=INTEGER}, #{expenseCode,jdbcType=VARCHAR}, #{updateUser,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{dlEstimatedMonthlySales,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.chevron.cdm_mkt.model.vo.WxTEstimatedMonthlySales" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into wx_t_estimated_monthly_sales
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="applyYear != null">
        apply_year,
      </if>
      <if test="requestNo != null">
        request_no,
      </if>
      <if test="salesId != null">
        sales_id,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="qhcEstimatedMonthlySales != null">
        qhc_estimated_monthly_sales,
      </if>
      <if test="hcxEstimatedMonthlySales != null">
        hcx_estimated_monthly_sales,
      </if>
      <if test="otherEstimatedMonthlySales != null">
        other_estimated_monthly_sales,
      </if>
      <if test="totalEstimatedMonthlySales != null">
        total_estimated_monthly_sales,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="expenseCode != null">
        expense_code,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="dlEstimatedMonthlySales != null">
        dl_estimated_monthly_sales,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="applyYear != null">
        #{applyYear,jdbcType=INTEGER},
      </if>
      <if test="requestNo != null">
        #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="salesId != null">
        #{salesId,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="qhcEstimatedMonthlySales != null">
        #{qhcEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="hcxEstimatedMonthlySales != null">
        #{hcxEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="otherEstimatedMonthlySales != null">
        #{otherEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="totalEstimatedMonthlySales != null">
        #{totalEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=BIT},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=INTEGER},
      </if>
      <if test="expenseCode != null">
        #{expenseCode,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dlEstimatedMonthlySales != null">
        #{dlEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.cdm_mkt.model.vo.WxTEstimatedMonthlySales">
    <!--@mbg.generated-->
    update wx_t_estimated_monthly_sales
    <set>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="applyYear != null">
        apply_year = #{applyYear,jdbcType=INTEGER},
      </if>
      <if test="requestNo != null">
        request_no = #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="salesId != null">
        sales_id = #{salesId,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="qhcEstimatedMonthlySales != null">
        qhc_estimated_monthly_sales = #{qhcEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="hcxEstimatedMonthlySales != null">
        hcx_estimated_monthly_sales = #{hcxEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="otherEstimatedMonthlySales != null">
        other_estimated_monthly_sales = #{otherEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="totalEstimatedMonthlySales != null">
        total_estimated_monthly_sales = #{totalEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=BIT},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=INTEGER},
      </if>
      <if test="expenseCode != null">
        expense_code = #{expenseCode,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dlEstimatedMonthlySales != null">
        dl_estimated_monthly_sales = #{dlEstimatedMonthlySales,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.cdm_mkt.model.vo.WxTEstimatedMonthlySales">
    <!--@mbg.generated-->
    update wx_t_estimated_monthly_sales
    set distributor_id = #{distributorId,jdbcType=BIGINT},
      apply_year = #{applyYear,jdbcType=INTEGER},
      request_no = #{requestNo,jdbcType=VARCHAR},
      sales_id = #{salesId,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=BIGINT},
      qhc_estimated_monthly_sales = #{qhcEstimatedMonthlySales,jdbcType=DECIMAL},
      hcx_estimated_monthly_sales = #{hcxEstimatedMonthlySales,jdbcType=DECIMAL},
      other_estimated_monthly_sales = #{otherEstimatedMonthlySales,jdbcType=DECIMAL},
      total_estimated_monthly_sales = #{totalEstimatedMonthlySales,jdbcType=DECIMAL},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=BIT},
      brand = #{brand,jdbcType=INTEGER},
      expense_code = #{expenseCode,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      dl_estimated_monthly_sales = #{dlEstimatedMonthlySales,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_t_estimated_monthly_sales
    where apply_year = #{record.applyYear,jdbcType=INTEGER}
    and brand = #{record.brand,jdbcType=INTEGER}
    and request_no= #{record.requestNo,jdbcType=VARCHAR}
    and expense_code = #{record.expenseType,jdbcType=VARCHAR}
    and del_flag = 0
  </select>
</mapper>