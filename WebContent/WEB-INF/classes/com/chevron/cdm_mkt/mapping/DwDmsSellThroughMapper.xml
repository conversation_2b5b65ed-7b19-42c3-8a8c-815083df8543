<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.DwDmsSellThroughMapper">
	<resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.DwDmsSellThrough">
        <result column="CHEVRON PRODUCT CODE" jdbcType="NVARCHAR" property="chevronProductCode"/>
        <result column="CHEVRON PRODUCT NAME" jdbcType="NVARCHAR" property="chevronProductName"/>
        <result column="CHEVRON PRODUCT NAME CN" jdbcType="NVARCHAR" property="chevronProductNameCn"/>
        <result column="CUSTOMER CODE" jdbcType="NVARCHAR" property="customerCode"/>
        <result column="CUSTOMER NAME" jdbcType="NVARCHAR" property="customerName"/>
        <result column="distributor_id" jdbcType="BIGINT" property="distributorId"/>
        <result column="DMS DATE" jdbcType="NVARCHAR" property="dmsDate"/>
        <result column="DMS ORDER" jdbcType="VARCHAR" property="dmsOrder"/>
        <result column="DMS PRODUCT CODE" jdbcType="NVARCHAR" property="dmsProductCode"/>
        <result column="DMS PRODUCT NAME" jdbcType="NVARCHAR" property="dmsProductName"/>
        <result column="QBR CATEGORY" jdbcType="NVARCHAR" property="qbrGategory"/>
        <result column="SELL THROUGH AMOUNT" jdbcType="NUMERIC" property="sellThroughAmount"/>
        <result column="SELL THROUGH L" jdbcType="NUMERIC" property="sellThroughL"/>
        <result column="T1" jdbcType="VARCHAR" property="t1"/>
        <result column="T2" jdbcType="NVARCHAR" property="t2"/>
        <result column="T3" jdbcType="NVARCHAR" property="t3"/>
        <result column="WORKSHOP CODE" jdbcType="VARCHAR" property="workshopCode"/>
        <result column="WORKSHOP NAME" jdbcType="VARCHAR" property="workshopName"/>
    </resultMap>
    
    <sql id="Base_Column_List">
      [CHEVRON PRODUCT CODE], [CHEVRON PRODUCT NAME], 
      [CHEVRON PRODUCT NAME CN], [CUSTOMER CODE], 
      [CUSTOMER NAME], [distributor_id], [DMS DATE], 
      [DMS ORDER], [DMS PRODUCT CODE], [DMS PRODUCT NAME], 
      [QBR CATEGORY], [SELL THROUGH AMOUNT], [SELL THROUGH L], 
      [T1], [T2], [T3], [WORKSHOP CODE], [WORKSHOP NAME] 
  	</sql>

	<select id="actualSellThroughAverage" parameterType="com.chevron.cdm_mkt.model.DwDmsSellThrough" resultType="java.lang.Double">
	  select avg([SELL THROUGH L]) 
	  from dw_dms_sell_through 
	  where 1 = 1 
	  and [DMS DATE] in 
	  <foreach item="item" index="index" collection="past12Month" open="(" separator="," close=")">
        #{item, jdbcType=NVARCHAR}
  	  </foreach>
  	  and [CHEVRON PRODUCT CODE] = #{chevronProductCode, jdbcType=NVARCHAR}
  	  and ([CUSTOMER CODE] + '/' + [WORKSHOP CODE])  = 
  	   (select dms_key from wx_t_work_shop where id = #{workshopId, jdbcType=BIGINT})
	</select>
	
	<!-- 
	<select id="selectByParams" parameterType="com.chevron.cdm_mkt.model.DwDmsSellThrough" resultMap="BaseResultMap">
	  select <include refid="Base_Column_List"/>
	  from dw_dms_sell_through 
	  where 1 = 1 
	  and [DMS DATE] in 
	  <foreach item="item" index="index" collection="past12Month" open="(" separator="," close=")">
        #{item, jdbcType=NVARCHAR}
  	  </foreach>
  	  and [CHEVRON PRODUCT CODE] = #{chevronProductCode, jdbcType=NVARCHAR}
  	  and [CUSTOMER NAME] = #{customerName, jdbcType=NVARCHAR}
  	  and [WORKSHOP NAME] = #{workshopName, jdbcType=NVARCHAR}
	</select>
 	-->
</mapper>