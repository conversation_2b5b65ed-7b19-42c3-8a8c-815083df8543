<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.WXTMktMaterielMapper">
<resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.WXTMktMateriel">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="mkt_apply_id" jdbcType="BIGINT" property="mktApplyId" />
	<result column="materiel_id" jdbcType="BIGINT" property="materielId" />
	<result column="materiel_type" jdbcType="NVARCHAR" property="materielType" />
</resultMap>

<sql id="Base_Column_List">
id, mkt_apply_id, materiel_id, materiel_type
</sql>


<select id="selectMaterielList" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_mkt_materiel
	where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
</select>

<delete id="deleteMaterielList" parameterType="java.lang.Long" >
    delete from wx_t_mkt_materiel
    where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
</delete>

</mapper>