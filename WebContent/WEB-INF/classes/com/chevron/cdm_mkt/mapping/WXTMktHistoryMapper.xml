<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.WXTMktHistoryMapper">
    <resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.WXTMktHistory">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="mkt_apply_id" jdbcType="BIGINT" property="mktApplyId"/>
        <result column="operator" jdbcType="BIGINT" property="operator"/>
        <result column="operator_name" jdbcType="NVARCHAR" property="operatorName"/>
        <result column="step" jdbcType="INTEGER" property="step"/>
        <result column="audit_status" jdbcType="NVARCHAR" property="auditStatus"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="opinion" jdbcType="NVARCHAR" property="opinion"/>
    </resultMap>

    <sql id="Base_Column_List">
id, mkt_apply_id, operator, operator_name, step, audit_status, audit_time, opinion
</sql>


    <select id="selectMktHistoryList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_history
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
    </select>


    <select id="getAsmAuditHistoryList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_history
        where mkt_apply_id = #{mktApplyId, jdbcType=BIGINT}
        AND operator=#{asmUserId,jdbcType=BIGINT}
        <if test='step != null'>
            AND step = #{step, jdbcType=INTEGER}
        </if>
        ORDER BY id DESC
    </select>


</mapper>