<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.WXTMktAttrsMapper">
    <resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.WXTMktAttrs">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="apply_id" jdbcType="BIGINT" property="applyId"/>
        <result column="volumes" jdbcType="VARCHAR" property="volumes"/>
        <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate"/>
        <result column="is_alert" jdbcType="INTEGER" property="isAlert"/>
        <result column="volume_quarter" jdbcType="DECIMAL" property="volumeQuarter"/>
        <result column="margin_quarter" jdbcType="DECIMAL" property="marginQuarter"/>
        <result column="margin_total" jdbcType="DECIMAL" property="marginTotal"/>
        <result column="return_years" jdbcType="DECIMAL" property="returnYears"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="workshop_province" jdbcType="NVARCHAR" property="workshopProvince" />
        <result column="workshop_city" jdbcType="NVARCHAR" property="workshopCity" />
        <result column="workshop_dist" jdbcType="NVARCHAR" property="workshopDist" />
        <result column="region_alias" jdbcType="NVARCHAR" property="regionAlias" />
    </resultMap>

    <update id="updateByApplyId" parameterType="map">
        update wx_t_mkt_attrs
        <set>
            <if test="record.volumes != null">
                volumes = #{record.volumes,jdbcType=VARCHAR},
            </if>
            <if test="record.expirationDate != null">
                expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isAlert != null">
                is_alert = #{record.isAlert,jdbcType=INTEGER},
            </if>
            <if test="record.volumeQuarter != null">
                volume_quarter = #{record.volumeQuarter,jdbcType=DECIMAL},
            </if>
            <if test="record.marginQuarter != null">
                margin_quarter = #{record.marginQuarter,jdbcType=DECIMAL},
            </if>
            <if test="record.marginTotal != null">
                margin_total = #{record.marginTotal,jdbcType=DECIMAL},
            </if>
            <if test="record.returnYears != null">
                return_years = #{record.returnYears,jdbcType=DECIMAL},
            </if>
            <if test="record.createDate != null">
                create_date = #{record.createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.workshopProvince != null">
                workshop_province = #{record.workshopProvince,jdbcType=NVARCHAR},
            </if>
            <if test="record.workshopCity != null">
                workshop_city = #{record.workshopCity,jdbcType=NVARCHAR},
            </if>
            <if test="record.workshopDist != null">
                workshop_dist = #{record.workshopDist,jdbcType=NVARCHAR},
            </if>
        </set>
        <where>
            <if test="record.applyId != null">
                apply_id = #{record.applyId,jdbcType=BIGINT}
            </if>
        </where>
    </update>

    <sql id="Base_Column_List">
    id, apply_id, volumes, expiration_date, is_alert, volume_quarter, margin_quarter,
    margin_total, return_years, create_date, workshop_province, workshop_city, workshop_dist,region_alias
  </sql>
    <select id="selectByApplyId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select TOP 1 *
        from wx_t_mkt_attrs
        WHERE apply_id = #{applyId,jdbcType=BIGINT}
    </select>

    <select id="getStoreApplyList" resultMap="BaseResultMap">
        select
        t2.*
        from wx_t_mkt_apply t1
        LEFT JOIN wx_t_mkt_attrs t2 ON t2.apply_id=t1.id
        where t1.mkt_type IN ('STORE_FRONT','STORE_IN_STORE')
        AND DATEDIFF(dd,GETDATE(),t2.expiration_date)=30
        <if test='isAlert != null and isAlert != ""'>
            AND t2.is_alert=#{isAlert,jdbcType=INTEGER}
        </if>
    </select>
</mapper>