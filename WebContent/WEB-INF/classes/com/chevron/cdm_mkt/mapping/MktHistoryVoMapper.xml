<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.cdm_mkt.dao.MktHistoryVoMapper">
    <resultMap id="BaseResultMap" type="com.chevron.cdm_mkt.model.vo.MktHistoryVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="mkt_apply_id" jdbcType="BIGINT" property="mktApplyId"/>
        <result column="operator" jdbcType="BIGINT" property="operator"/>
        <result column="operator_name" jdbcType="NVARCHAR" property="operatorName"/>
        <result column="step" jdbcType="INTEGER" property="step"/>
        <result column="audit_status" jdbcType="NVARCHAR" property="auditStatus"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="opinion" jdbcType="NVARCHAR" property="opinion"/>
        <result column="node_user_role" jdbcType="NVARCHAR" property="operatorRoleName"/>
        <result column="step_name" jdbcType="NVARCHAR" property="stepName"/>
    </resultMap>

    <sql id="Base_Column_List">
h.id, h.mkt_apply_id, h.operator, h.operator_name, h.step, h.audit_status, h.audit_time, h.opinion, 
fn.node_user_role, fn.step_name
</sql>

    <select id="selectMktHistoryList" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_history h
        left join wx_t_mkt_flow_node fn on h.mkt_apply_id = fn.mkt_apply_id and h.step = fn.step
        where h.mkt_apply_id = #{applyId, jdbcType=BIGINT}
    </select>

    <select id="selectMktHistoryListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_history h
        left join wx_t_mkt_flow_node fn
        on h.mkt_apply_id = fn.mkt_apply_id and h.step = fn.step AND h.operator=fn.operator
        <where>
            <if test="ids != null and ids.size() > 0">
                h.mkt_apply_id IN
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY h.id ASC
    </select>
    
     <select id="selectMktHistoryListOneByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_mkt_history h
        left join wx_t_mkt_flow_node fn
        on h.mkt_apply_id = fn.mkt_apply_id and h.step = fn.step AND h.operator=fn.operator
        <where>
          h.id in (select s.id from (
            select Max(t.id) as id,t.operator_name from wx_t_mkt_history t
            <where>
             <if test="ids != null and ids.size() > 0">
             and t.mkt_apply_id in 
              <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
              </foreach>
               </if>
             group by t.operator_name)s
             </where>
             )  ORDER BY h.id ASC
        </where>
    </select>

</mapper>