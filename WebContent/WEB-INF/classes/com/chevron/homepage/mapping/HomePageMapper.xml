<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.homepage.dao.HomePageMapper">

	<select id="selectSellinVolume" resultType="map">
<!-- 		SELECT sum(o.total_liter_count) + 0.0 sellinVolume
		FROM wx_t_partner_order o
		where 1=1
		$Permission_Clause$
		-->

		<!-- 所有合伙人的历史累积sellin升数总额， 20171207 Ervin 参照OilVerificationMapper.xml : querySpKpiReportByYear -->
		select sum(a.capacity) sellinVolume from (
			select po1.partner_id, MONTH(po1.create_time) report_time, po1.total_liter_count capacity
			from  wx_t_partner_order po1
			left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
				and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
			where 1=1
				and po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
				and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
			
			union all

			select 279, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*大地保险和合众已服务的服务包订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DP'
				and  source in ('DDBX', 'HZYYFW') and service_acount>remaining_service_times
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select 56321, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*易修车服务包订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DP' and source='ODYXC'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}


			union all

			select 56317, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*滴滴养车服务订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DA' and source='DDYYFW'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) a
		where 1=1
		$Permission_Clause$

	</select>

	<select id="selectSellthroughVolume" resultType="map">
		select SUM (amount * capacity) sellthroughVolume  from (
			SELECT
			ol.amount amount,
			p.capacity capacity,
			o_partner_id =(select top 1 partner_id from wx_t_workshop_partner where workshop_id = o.work_shop_id )
			FROM wx_t_order_line ol
			LEFT JOIN wx_t_order o ON o.id = ol.order_id AND ( o.order_type = 'PA' OR o.order_type = 'A')
			LEFT JOIN wx_t_product p ON p.id = ol.product_id AND p.category = 'JY'
			WHERE p.category = 'JY'
			and o.id is not NULL
			and p.id is not NULL
			AND (o.order_type = 'PA' OR o.order_type = 'A')
			and o.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) temp
		where 1=1
		$Permission_Clause$
	</select>

	<select id="selectSelloutVolume" resultType="map">
		<!-- 机油核销历史累计总升数(sell out volume) 20171207 Ervin 参照OilVerificationMapper.xml : querySpKpiReportByYear -->
		select sum(capacity) selloutVolume from (
			select MONTH(v.creation_time) report_time, v.capacity, wp1.partner_id
			from wx_v_oil_verification v
			left join wx_t_workshop_partner wp1
			on wp1.workshop_id=v.workshop_id
			where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 279/*大地保险和合众服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source in ('DDBX', 'HZYYFW')
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56321/*易修车服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source = 'ODYXC'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56317 /*滴滴养车服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source='DDYYFW'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) a
		where 1=1
			$Permission_Clause$
	</select>

	<select id="selectPercentROI" resultType="map">
		select 1 percentROI;
	</select>

	<select id="selectOilTypeScale" resultType="map">
		select sum(convert(float, a.capacity)) value, a.oil_type name
		from (
		select p.capacity capacity,p.oil_type oil_type,
		p_partner_id =(select top 1 partner_id from wx_t_workshop_partner where workshop_id = v.workshop_id )
		from wx_v_oil_verification v
		LEFT JOIN wx_t_product p
		on v.sku = p.sku
		and p.category = 'JY'
		where 1=1
		and p.category = 'JY'
		<if test="dateFrom != null">
			and v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="dateTo != null">
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		</if>
		) a
		where 1=1
		$Permission_Clause$
		group by a.oil_type
		order by a.oil_type
	</select>
	<select id="selectSkuScale" resultType="map">
		select sum(convert(float, a.capacity)) value, a.sku name
		from (
		select p.capacity capacity, p.sku sku,
		p_partner_id =(select top 1 partner_id from wx_t_workshop_partner where workshop_id = v.workshop_id )
		from wx_v_oil_verification v
		LEFT JOIN
		wx_t_product p
		on v.sku = p.sku and p.category = 'JY'
		where 1=1
		and p.category = 'JY'

		<if test="type != null">
			and p.oil_type = #{type, jdbcType=VARCHAR}
		</if>
		<if test="dateFrom != null">
			and v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="dateTo != null">
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		</if>
		) a
		where 1=1
		$Permission_Clause$
		group by a.sku
		order by value
	</select>
	<select id="selectAreaDistribution" resultType="map">
		select sum(convert(float, a.capacity)) value, a.region region, a.oil_type name
		from (
		select p.capacity capacity,p.oil_type oil_type, r.remark region,
		p_partner_id =(select top 1 partner_id from wx_t_workshop_partner where workshop_id = v.workshop_id )
		from wx_v_oil_verification v
		LEFT JOIN wx_t_product p
		on v.sku = p.sku
		and p.category = 'JY'
		left join wx_t_work_shop w
		on v.workshop_id = w.id
		left join wx_t_region r
		on r.region_type='P'
		and left(r.id, 2) = left(w.region_id,2)
		where 1=1
		and p.category = 'JY'
		and w.region_id is not null
		<if test="type != null">
			and p.oil_type = #{type, jdbcType=VARCHAR}
		</if>
		<if test="dateFrom != null">
			and v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="dateTo != null">
			and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		</if>
		) a
		where 1=1
		$Permission_Clause$
		group by a.region, a.oil_type
		order by a.region, a.oil_type
	</select>

	<select id="selectSunsetWorkshopTotal" resultType="java.lang.Integer">
		select count(1) from (
			select w.id ,
			partner_id = (select top 1 partner_id from wx_t_workshop_partner wp where wp.workshop_id = w.id ),
			status_create_time=(select top 1 ws.create_time from wx_t_workshop_status ws
								where ws.workshop_id = w.id
									and ws.workshop_with_status= '3' and  ws.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}),
			sellout_volume=(
				select sum(convert(float, p.capacity)) from wx_t_mechanic_qrcode mq
				left join wx_t_product p on mq.sku = p.sku and p.category = 'JY'
				where 1=1
				<if test="dateFrom != null">
					and mq.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				</if>
				<if test="dateTo != null">
					and mq.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
				</if>
				and mq.workshop_id = w.id and (mq.enable_flag = 1 or mq.enable_flag is null)
			),
			xd_num=(
				select count(1)
				from wx_task_sub s
				join wx_task_main m on m.task_main_id = s.task_main_id
				where 1=1
				and m.tmb_type_code = 'TT_2_XD'
				<if test="dateFrom != null">
					and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				</if>
				<if test="dateTo != null">
					and s.xg_sj &lt; #{dateTo, jdbcType=TIMESTAMP}
				</if>
				and s.task_status = 4
				and s.org_id = w.id
			)
			from wx_t_work_shop w
			where w.status = '3'
			) temp
		<if test="newSp != null">
		left join wx_t_partner_o2o_enterprise en on temp.partner_id=en.partner_id
		</if>
		where temp.partner_id is not null
		and temp.status_create_time is not null
		and ((
			<if test="selloutVolume != null">
			temp.sellout_volume &lt;=  #{selloutVolume} or temp.sellout_volume is  null
			</if>
			<if test="selloutVolume == null">
			temp.sellout_volume &lt;= 0 or temp.sellout_volume is null
			</if>
			)
			<if test="condition == 'and'">
				or
			</if>
			<if test="condition == 'or'">
				and
			</if>
			<if test="condition == null">
				and
			</if>
			<if test="xdNum != null">
				xd_num &lt;= #{xdNum}
			</if>
			<if test="xdNum == null">
				xd_num &lt;= 0
			</if>
		)
		<if test="newSp != null">
		and en.new_sp_flag=#{newSp,jdbcType=INTEGER}
		</if>
		$Permission_Clause$
	</select>
	<select id="selectActiveWorkshopTotal" resultType="java.lang.Integer">
	select count(1) from (
		select
		w.id,
		partner_id = (select top 1 partner_id from wx_t_workshop_partner wp where wp.workshop_id = w.id ),
		status_create_time=(select top 1 ws.create_time from wx_t_workshop_status ws where ws.workshop_id = w.id and ws.workshop_with_status= '3' )
		from wx_t_work_shop w
		where w.status = '3'
		) temp
		<if test="newSp != null">
		left join wx_t_partner_o2o_enterprise en on temp.partner_id=en.partner_id
		</if>
	where temp.partner_id is not null
		<if test="dateTo != null">
			and temp.status_create_time  &lt; #{dateTo, jdbcType=TIMESTAMP}
		</if>
		and temp.status_create_time is not null
		<if test="newSp != null">
		and en.new_sp_flag=#{newSp,jdbcType=INTEGER}
		</if>
		$Permission_Clause$
	</select>

	<select id="selectTaskRank4Partner" resultType="map">
	select tt.*, l4.organization_name partner_name from (
		select 'LD' view_flag, xx.record_count, xx.partner_id, row_number() over(order by xx.record_count desc) as rank
		from (
			select a.partner_id, count(1) record_count
			from (
				select distinct t.id, t.work_shop_name, t.type, t.region_id, t.work_shop_address, t1.create_time active_time,
				t2.partner_id
				from wx_t_workshop_status t1
				join wx_t_work_shop t on t.id=t1.workshop_id and t.status='3'
				join wx_t_workshop_partner t2 on t2.workshop_id=t.id
				where t1.subtask_id is null and t1.workshop_with_status='3'
				union all
				select t.id, t.work_shop_name, t.type, t.region_id, t.work_shop_address, t1.create_time active_time,
				case when u1.org_id is null then (select top 1 t2.partner_id from wx_t_workshop_partner t2 where t2.workshop_id=t.id)
				else u1.org_id end partner_id
				from wx_t_workshop_status t1
				join wx_t_work_shop t on t.id=t1.workshop_id and t.status='3'
				left join wx_task_sub s1 on s1.task_id=t1.subtask_id
				left join wx_task_main m1 on m1.task_main_id = s1.task_main_id and m1.tmb_type_code = 'TT_2_LD'
				left join wx_t_user u1 on u1.user_id=m1.excute_user_id
				where t1.subtask_id is not null and t1.workshop_with_status='3'
				and not exists (select 1 from wx_t_workshop_status t3 where t3.workshop_id=t1.workshop_id and t3.create_time>t1.create_time)
			) a
			where a.active_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and a.active_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
			group by a.partner_id) xx

		union all

		select 'XD' view_flag, xx.record_count, xx.partner_id, row_number() over(order by xx.record_count desc) as rank from (
			select m.tenant_id partner_id, count(1) record_count
			from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
			where m.tmb_type_code = 'TT_2_XD'
			and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and s.xg_sj &lt;= #{dateTo, jdbcType=TIMESTAMP}
			and s.task_status = 4
			group by m.tenant_id) xx

		union all
		select 'SD' view_flag, xx.record_count, xx.partner_id, row_number() over(order by xx.record_count desc) as rank from (
			select m.tenant_id partner_id, count(1) record_count
			from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
			where m.tmb_type_code = 'TT_2_SD'
			and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and s.xg_sj &lt;= #{dateTo, jdbcType=TIMESTAMP}
			and s.task_status = 4
			group by m.tenant_id) xx
		) tt
		join wx_t_organization l4 on tt.partner_id=l4.id
	where tt.partner_id = #{partnerId, jdbcType=BIGINT}
	</select>

	<select id="selectBDtaskData" resultType="map">
	select tt.*, l4.organization_name partner_name, l5.ch_name user_name from (

		select 'LD' view_flag, xx.record_count, xx.partner_id, xx.exec_user, row_number() over(order by xx.record_count desc) as rank
		from (
			select a.partner_id,a.exec_user, count(1) record_count
			from (
				select distinct t.id, t.work_shop_name, t.type, t.region_id, t.work_shop_address, t1.create_time active_time,t.excute_user_id exec_user,
				t2.partner_id
				from wx_t_workshop_status t1
				join wx_t_work_shop t on t.id=t1.workshop_id and t.status='3'
				join wx_t_workshop_partner t2 on t2.workshop_id=t.id
				where t1.subtask_id is null and t1.workshop_with_status='3'
				union all
				select t.id, t.work_shop_name, t.type, t.region_id, t.work_shop_address, t1.create_time active_time,s1.exec_user,
				case when u1.org_id is null then (select top 1 t2.partner_id from wx_t_workshop_partner t2 where t2.workshop_id=t.id)
				else u1.org_id end partner_id
				from wx_t_workshop_status t1
				join wx_t_work_shop t on t.id=t1.workshop_id and t.status='3'
				left join wx_task_sub s1 on s1.task_id=t1.subtask_id
				left join wx_task_main m1 on m1.task_main_id = s1.task_main_id and m1.tmb_type_code = 'TT_2_LD'
				left join wx_t_user u1 on u1.user_id=m1.excute_user_id
				where t1.subtask_id is not null and t1.workshop_with_status='3'
				and not exists (select 1 from wx_t_workshop_status t3 where t3.workshop_id=t1.workshop_id and t3.create_time>t1.create_time)
				) a
			where a.active_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and a.active_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
			and a.partner_id = #{partnerId, jdbcType=BIGINT}
			group by a.partner_id,a.exec_user
		) xx

		union all
		select 'XD' view_flag, xx.record_count, xx.partner_id, xx.exec_user, row_number() over(order by xx.record_count desc) as rank from (
			select m.tenant_id partner_id,s.exec_user, count(1) record_count
			from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
			where m.tmb_type_code = 'TT_2_XD'
			and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and s.xg_sj &lt;= #{dateTo, jdbcType=TIMESTAMP}
			and s.task_status = 4
			and m.tenant_id = #{partnerId, jdbcType=BIGINT}
			group by m.tenant_id, s.exec_user) xx

		union all
		select 'SD' view_flag, xx.record_count, xx.partner_id, xx.exec_user, row_number() over(order by xx.record_count desc) as rank from (
			select m.tenant_id partner_id,s.exec_user, count(1) record_count
			from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
			where m.tmb_type_code = 'TT_2_SD'
			and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and s.xg_sj &lt;= #{dateTo, jdbcType=TIMESTAMP}
			and s.task_status = 4
			and m.tenant_id = #{partnerId, jdbcType=BIGINT}
			group by m.tenant_id, s.exec_user) xx

		) tt
		join wx_t_organization l4 on tt.partner_id=l4.id
		join wx_t_user l5 on tt.exec_user = l5.user_id
		order by tt.view_flag, tt.rank desc
	</select>

	<!-- 普通合伙人数量 -->
	<select id="countPartner"  resultType="Long" parameterType="com.sys.organization.model.OrganizationParam" >
		select count(1) from wx_t_organization wp1
		LEFT JOIN wx_t_partner_o2o_enterprise t1 ON wp1.id = t1.partner_id
		where  wp1.type = 1 and t1.partner_property = 'NORMAL'
		$Permission_Clause$
	</select>
	<!-- 新合伙人数量 -->
	<select id="countLatestPartner"  resultType="Long" >
		select count(1) from wx_t_organization wp1
		LEFT JOIN wx_t_partner_o2o_enterprise t1 ON wp1.id = t1.partner_id
		where  wp1.type = 1 and t1.partner_property = 'NORMAL' and t1.new_sp_flag=1
		<!-- <if test="latestPartnerLimitDate!=null">
		and wp1.create_time &gt;= #{latestPartnerLimitDate}
		</if> -->
		$Permission_Clause$
	</select>


	<!-- 计算新合伙人sell in -->
	<select id="selectNewSpSellinVolume" resultType="map">
		<!-- 所有合伙人的历史累积sellin升数总额， 20171207 Ervin 参照OilVerificationMapper.xml : querySpKpiReportByYear -->
		select sum(a.capacity) sellinVolume from (
			select po1.partner_id, MONTH(po1.create_time) report_time, po1.total_liter_count capacity
			from  wx_t_partner_order po1
			left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
				and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
			where 1=1
				and po1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and po1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
				and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))

			union all

			select 279, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*大地保险和合众已服务的服务包订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DP'
				and  source in ('DDBX', 'HZYYFW') and service_acount>remaining_service_times
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select 56321, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*易修车服务包订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DP' and source='ODYXC'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}


			union all

			select 56317, MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount /*滴滴养车服务订单*/
			from wx_t_order o1 join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where 1=1
				and order_type='DA' and source='DDYYFW'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) a
		left join wx_t_partner_o2o_enterprise en on a.partner_id=en.partner_id
		where 1=1 and en.new_sp_flag=1
		$Permission_Clause$

	</select>

	<!-- 计算新合伙人sell through -->
	<select id="selectNewSpSellthroughVolume" resultType="map">
		select SUM (amount * capacity) sellthroughVolume  from (
			SELECT
			ol.amount amount,
			p.capacity capacity,
			o_partner_id =(select top 1 partner_id from wx_t_workshop_partner where workshop_id = o.work_shop_id )
			FROM wx_t_order_line ol
			LEFT JOIN wx_t_order o ON o.id = ol.order_id AND ( o.order_type = 'PA' OR o.order_type = 'A')
			LEFT JOIN wx_t_product p ON p.id = ol.product_id AND p.category = 'JY'
			WHERE p.category = 'JY'
			and o.id is not NULL
			and p.id is not NULL
			AND (o.order_type = 'PA' OR o.order_type = 'A')
			and o.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
			and o.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) temp
		left join wx_t_partner_o2o_enterprise en on temp.o_partner_id=en.partner_id
		where 1=1 and en.new_sp_flag=1
		$Permission_Clause$
	</select>

	<!--  计算新合伙人sell out  -->
	<select id="selectNewSpSelloutVolume" resultType="map">
		<!-- 机油核销历史累计总升数(sell out volume) 20171207 Ervin 参照OilVerificationMapper.xml : querySpKpiReportByYear -->
		select sum(capacity) selloutVolume from (
			select MONTH(v.creation_time) report_time, v.capacity, wp1.partner_id
			from wx_v_oil_verification v
			left join wx_t_workshop_partner wp1
			on wp1.workshop_id=v.workshop_id
			where v.creation_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and v.creation_time &lt; #{dateTo, jdbcType=TIMESTAMP}
			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 279/*大地保险和合众服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source in ('DDBX', 'HZYYFW')
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56321/*易修车服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source = 'ODYXC'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}

			union all

			select MONTH(o1.create_time), convert(float, p.capacity) * ol1.amount, 56317 /*滴滴养车服务订单*/
			from wx_t_order o1
			join wx_t_order_line ol1
			on ol1.order_id=o1.id
			left join wx_t_product p
			on p.sku=ol1.sku
			where order_type='DA' and source='DDYYFW'
				and o1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
				and o1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		) a
		left join wx_t_partner_o2o_enterprise en on a.partner_id=en.partner_id
		where 1=1 and en.new_sp_flag=1
			$Permission_Clause$
	</select>
</mapper>