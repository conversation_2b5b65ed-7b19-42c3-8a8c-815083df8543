<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.ActivityAgreeMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.ActivityAgree">
		<result column="user_id" property="userId" jdbcType="VARCHAR"/>
		<result column="activity_id" property="activityId" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		user_id,activity_id,create_user_id,create_time
	</sql>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.ActivityAgree">
		insert into wx_t_activity_agree
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userId != null">
				user_id,
			</if>
			<if test="activityId != null">
				activity_id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userId != null">
				#{userId,jdbcType=VARCHAR},
			</if>
			<if test="activityId != null">
				#{activityId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_activity_agree
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.ActivityAgreeExample">
		delete from wx_t_activity_agree
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.ActivityAgreeExample" resultType="int">
		select count(1) from wx_t_activity_agree
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ActivityAgreeExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_activity_agree
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.user_id, t1.activity_id, t1.create_user_id, t1.create_time
		  from wx_t_activity_agree t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_activity_agree (user_id, activity_id, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.userId, jdbcType=VARCHAR}, #{item.activityId, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
