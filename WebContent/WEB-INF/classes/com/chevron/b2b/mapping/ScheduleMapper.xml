<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.ScheduleMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.Schedule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="job_id" property="jobId" jdbcType="BIGINT"/>
		<result column="schedule_name" property="scheduleName" jdbcType="VARCHAR"/>
		<result column="icon_id" property="iconId" jdbcType="BIGINT"/>
		<result column="banner_id" property="bannerId" jdbcType="BIGINT"/>
		<result column="schedule_type" property="scheduleType" jdbcType="VARCHAR"/>
		<result column="schedule_type_text" property="scheduleTypeText" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="execute_user_type" property="executeUserType" jdbcType="VARCHAR"/>
		<result column="award_point" property="awardPoint" jdbcType="NUMERIC"/>
		<result column="left_steps" property="leftSteps" jdbcType="INTEGER"/>
		<result column="start_time" property="startTime" jdbcType="DATE"/>
		<result column="end_time" property="endTime" jdbcType="DATE"/>
		<result column="schedule_desc" property="scheduleDesc" jdbcType="VARCHAR"/>
		<result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<result column="ext_attr1" property="extAttr1" jdbcType="VARCHAR"/>
		<result column="ext_attr2" property="extAttr2" jdbcType="VARCHAR"/>
		<result column="ext_attr3" property="extAttr3" jdbcType="VARCHAR"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="limit_time" property="limitTime" jdbcType="INTEGER"/>
		<result column="share_flag" property="shareFlag" jdbcType="INTEGER"/>
		<result column="form_bg" property="formBg" jdbcType="BIGINT"/>
		<result column="action_bg" property="actionBg" jdbcType="BIGINT"/>
		<result column="fixed_point_flag" property="fixedPointFlag" jdbcType="INTEGER"/>
		<result column="max_point" property="maxPoint" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,job_id,schedule_name,icon_id,banner_id,schedule_type,status,execute_user_type,award_point,left_steps,start_time,
		end_time,schedule_desc,publish_time,remark,sales_channel,ext_attr1,ext_attr2,
		ext_attr3,delete_flag,create_user_id,create_time,update_user_id,update_time,limit_time,share_flag,form_bg,action_bg,
		fixed_point_flag,max_point
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.b2b.model.Schedule">
		update wx_t_schedule
		<set>
			<if test="jobId != null" >
				job_id = #{jobId,jdbcType=BIGINT},
			</if>
			<if test="scheduleName != null" >
				schedule_name = #{scheduleName,jdbcType=VARCHAR},
			</if>
			<if test="iconId != null" >
				icon_id = #{iconId,jdbcType=BIGINT},
			</if>
			<if test="bannerId != null" >
				banner_id = #{bannerId,jdbcType=BIGINT},
			</if>
			<if test="scheduleType != null" >
				schedule_type = #{scheduleType,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="executeUserType != null" >
				execute_user_type = #{executeUserType,jdbcType=VARCHAR},
			</if>
			<if test="awardPoint != null" >
				award_point = #{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="leftSteps != null" >
				left_steps = #{leftSteps,jdbcType=INTEGER},
			</if>
			<if test="startTime != null" >
				start_time = #{startTime,jdbcType=DATE},
			</if>
			<if test="endTime != null" >
				end_time = #{endTime,jdbcType=DATE},
			</if>
			<if test="scheduleDesc != null" >
				schedule_desc = #{scheduleDesc,jdbcType=VARCHAR},
			</if>
			<if test="publishTime != null" >
				publish_time = #{publishTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null" >
				sales_channel = #{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="extAttr1 != null" >
				ext_attr1 = #{extAttr1,jdbcType=VARCHAR},
			</if>
			<if test="extAttr2 != null" >
				ext_attr2 = #{extAttr2,jdbcType=VARCHAR},
			</if>
			<if test="extAttr3 != null" >
				ext_attr3 = #{extAttr3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="limitTime != null" >
				limit_time = #{limitTime,jdbcType=INTEGER},
			</if>
			<if test="shareFlag != null" >
				share_flag = #{shareFlag,jdbcType=INTEGER},
			</if>
			<if test="formBg != null" >
				form_bg = #{formBg,jdbcType=BIGINT},
			</if>
			<if test="actionBg != null" >
				action_bg = #{actionBg,jdbcType=BIGINT},
			</if>
			<if test="fixedPointFlag != null" >
				fixed_point_flag = #{fixedPointFlag,jdbcType=INTEGER},
			</if>
			<if test="maxPoint != null" >
				max_point = #{maxPoint,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.b2b.model.ScheduleExample">
    	delete from wx_t_schedule
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_schedule
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.Schedule" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_schedule
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="jobId != null">
				job_id,
			</if>
			<if test="scheduleName != null">
				schedule_name,
			</if>
			<if test="iconId != null">
				icon_id,
			</if>
			<if test="bannerId != null">
				banner_id,
			</if>
			<if test="scheduleType != null">
				schedule_type,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="executeUserType != null">
				execute_user_type,
			</if>
			<if test="awardPoint != null">
				award_point,
			</if>
			<if test="leftSteps != null">
				left_steps,
			</if>
			<if test="startTime != null">
				start_time,
			</if>
			<if test="endTime != null">
				end_time,
			</if>
			<if test="scheduleDesc != null">
				schedule_desc,
			</if>
			<if test="publishTime != null">
				publish_time,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="salesChannel != null">
				sales_channel,
			</if>
			<if test="extAttr1 != null">
				ext_attr1,
			</if>
			<if test="extAttr2 != null">
				ext_attr2,
			</if>
			<if test="extAttr3 != null">
				ext_attr3,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="limitTime != null">
				limit_time,
			</if>
			<if test="shareFlag != null">
				share_flag,
			</if>
			<if test="formBg != null">
				form_bg,
			</if>
			<if test="actionBg != null">
				action_bg,
			</if>
			<if test="fixedPointFlag != null">
				fixed_point_flag,
			</if>
			<if test="maxPoint != null">
				max_point,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="jobId != null">
				#{jobId,jdbcType=BIGINT},
			</if>
			<if test="scheduleName != null">
				#{scheduleName,jdbcType=VARCHAR},
			</if>
			<if test="iconId != null">
				#{iconId,jdbcType=BIGINT},
			</if>
			<if test="bannerId != null">
				#{bannerId,jdbcType=BIGINT},
			</if>
			<if test="scheduleType != null">
				#{scheduleType,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="executeUserType != null">
				#{executeUserType,jdbcType=VARCHAR},
			</if>
			<if test="awardPoint != null">
				#{awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="leftSteps != null">
				#{leftSteps,jdbcType=INTEGER},
			</if>
			<if test="startTime != null">
				#{startTime,jdbcType=DATE},
			</if>
			<if test="endTime != null">
				#{endTime,jdbcType=DATE},
			</if>
			<if test="scheduleDesc != null">
				#{scheduleDesc,jdbcType=VARCHAR},
			</if>
			<if test="publishTime != null">
				#{publishTime,jdbcType=TIMESTAMP},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null">
				#{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="extAttr1 != null">
				#{extAttr1,jdbcType=VARCHAR},
			</if>
			<if test="extAttr2 != null">
				#{extAttr2,jdbcType=VARCHAR},
			</if>
			<if test="extAttr3 != null">
				#{extAttr3,jdbcType=VARCHAR},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="limitTime != null">
				#{limitTime,jdbcType=INTEGER},
			</if>
			<if test="shareFlag != null">
				#{shareFlag,jdbcType=INTEGER},
			</if>
			<if test="formBg != null">
				#{formBg,jdbcType=BIGINT},
			</if>
			<if test="actionBg != null">
				#{actionBg,jdbcType=BIGINT},
			</if>
			<if test="fixedPointFlag != null">
				#{fixedPointFlag,jdbcType=INTEGER},
			</if>
			<if test="maxPoint != null">
				#{maxPoint,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_schedule
		<set>
			<if test="record.jobId != null">
				job_id = #{record.jobId,jdbcType=BIGINT},
			</if>
			<if test="record.scheduleName != null">
				schedule_name = #{record.scheduleName,jdbcType=VARCHAR},
			</if>
			<if test="record.iconId != null">
				icon_id = #{record.iconId,jdbcType=BIGINT},
			</if>
			<if test="record.bannerId != null">
				banner_id = #{record.bannerId,jdbcType=BIGINT},
			</if>
			<if test="record.scheduleType != null">
				schedule_type = #{record.scheduleType,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.executeUserType != null">
				execute_user_type = #{record.executeUserType,jdbcType=VARCHAR},
			</if>
			<if test="record.awardPoint != null">
				award_point = #{record.awardPoint,jdbcType=NUMERIC},
			</if>
			<if test="record.leftSteps != null">
				left_steps = #{record.leftSteps,jdbcType=INTEGER},
			</if>
			<if test="record.startTime != null">
				start_time = #{record.startTime,jdbcType=DATE},
			</if>
			<if test="record.endTime != null">
				end_time = #{record.endTime,jdbcType=DATE},
			</if>
			<if test="record.scheduleDesc != null">
				schedule_desc = #{record.scheduleDesc,jdbcType=VARCHAR},
			</if>
			<if test="record.publishTime != null">
				publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.salesChannel != null">
				sales_channel = #{record.salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.extAttr1 != null">
				ext_attr1 = #{record.extAttr1,jdbcType=VARCHAR},
			</if>
			<if test="record.extAttr2 != null">
				ext_attr2 = #{record.extAttr2,jdbcType=VARCHAR},
			</if>
			<if test="record.extAttr3 != null">
				ext_attr3 = #{record.extAttr3,jdbcType=VARCHAR},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.limitTime != null">
				limit_time = #{record.limitTime,jdbcType=INTEGER},
			</if>
			<if test="record.shareFlag != null">
				share_flag = #{record.shareFlag,jdbcType=INTEGER},
			</if>
			<if test="record.formBg != null">
				form_bg = #{record.formBg,jdbcType=BIGINT},
			</if>
			<if test="record.actionBg != null">
				action_bg = #{record.actionBg,jdbcType=BIGINT},
			</if>
			<if test="record.fixedPointFlag != null">
				fixed_point_flag = #{record.fixedPointFlag,jdbcType=INTEGER},
			</if>
			<if test="record.maxPoint != null">
				max_point = #{record.maxPoint,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.ScheduleExample">
		delete from wx_t_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.ScheduleExample">
		select count(1) from wx_t_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_schedule
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.schedule_name, t1.icon_id, t1.banner_id, t1.schedule_type, t1.status, t1.execute_user_type,
			 t1.award_point, t1.left_steps, t1.start_time, t1.end_time, t1.publish_time, t1.remark, t1.sales_channel, t1.ext_attr1, t1.ext_attr2, t1.ext_attr3,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time,
			 t1.limit_time, t1.share_flag, t1.form_bg, t1.action_bg, t1.fixed_point_flag, t1.max_point,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Schedule.scheduleType' and di1.dic_item_code=t1.schedule_type) schedule_type_text
		  from wx_t_schedule t1
		 where 1=1
		<if test="scheduleName != null and scheduleName != ''">
			and t1.schedule_name like '%' + #{scheduleName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="scheduleType != null and scheduleType != ''">
			and t1.schedule_type = #{scheduleType, jdbcType=VARCHAR}
		</if>
		<if test="dateFrom != null">
			and t1.publish_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="dateTo != null">
			and t1.publish_time &lt; #{dateTo, jdbcType=TIMESTAMP}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleParams">
	select xx1.*, case when xx1.job_update_time is not null and xx1.job_update_time>xx1.update_time then xx1.job_update_time else xx1.update_time end update_time1 from (
		select t1.id, t1.schedule_name, t1.icon_id, t1.banner_id, t1.schedule_type, t1.execute_user_type,
			 t1.award_point, t1.left_steps, t1.start_time, t1.end_time, t1.publish_time, t1.remark, t1.sales_channel, t1.ext_attr1, t1.ext_attr2, t1.ext_attr3,
			 t1.delete_flag, t1.create_user_id, t1.create_time, t1.update_user_id, isnull((select max(se.create_time) from wx_t_schedule_entity se where se.schedule_id=t1.id), t1.update_time) update_time,
			 t1.limit_time, t1.share_flag, t1.form_bg, t1.action_bg, t1.fixed_point_flag, t1.max_point, jp.update_time job_update_time,
			 case when t1.status=5 and (exists (select 1 from wx_t_schedule_entity se where se.schedule_id=t1.id and DATEADD(day, 1, se.end_date)>getdate()) 
			 		or (jp.id is not null and (jp.end_date is null or DATEADD(day, 1, jp.end_date)>getdate()))) then 10 
			 	when t1.status=5 then 100
			 	else t1.status end status,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Schedule.scheduleType' and di1.dic_item_code=t1.schedule_type) schedule_type_text
		  from wx_t_schedule t1
		  left join wx_t_job_pool jp on jp.id=t1.job_id and jp.delete_flag=0 and jp.enable_flag=1
		 where t1.delete_flag=0
		<if test="scheduleName != null and scheduleName != ''">
			and t1.schedule_name like '%' + #{scheduleName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="scheduleType != null and scheduleType != ''">
			and t1.schedule_type = #{scheduleType, jdbcType=VARCHAR}
		</if>
		<if test="executeUserType != null and executeUserType != ''">
			and t1.execute_user_type = #{executeUserType, jdbcType=VARCHAR}
		</if>
		<if test="scheduleType != null and scheduleType != ''">
			and t1.schedule_type = #{scheduleType, jdbcType=VARCHAR}
		</if>
		<if test="salesChannel != null and salesChannel != ''">
			and t1.sales_channel = #{salesChannel, jdbcType=VARCHAR}
		</if>
		<if test="publishDateFrom != null">
			and t1.publish_time &gt;= #{publishDateFrom, jdbcType=TIMESTAMP}
		</if>
		<if test="publishDateTo != null">
			and t1.publish_time &lt; #{publishDateTo, jdbcType=TIMESTAMP}
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.schedule_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>) xx1
		where 1=1
		<if test="status != null">
			and xx1.status=#{status,jdbcType=INTEGER}
		</if>
	</select>
</mapper>
