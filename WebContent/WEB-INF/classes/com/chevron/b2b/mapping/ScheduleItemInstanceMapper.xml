<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.b2b.dao.ScheduleItemInstanceMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.b2b.model.ScheduleItemInstance">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="schedule_item_id" property="scheduleItemId" jdbcType="BIGINT"/>
		<result column="schedule_instance_id" property="scheduleInstanceId" jdbcType="BIGINT"/>
		<result column="input_value" property="inputValue" jdbcType="VARCHAR"/>
		
		<result column="caption" property="caption" jdbcType="VARCHAR"/>
		<result column="input_ctrl_config" property="inputCtrlConfig" jdbcType="VARCHAR"/>
		<result column="required_flag" property="requiredFlag" jdbcType="INTEGER"/>
		<result column="ctrl_type" property="ctrlType" jdbcType="VARCHAR"/>
		<result column="input_ctrl_id" property="inputCtrlId" jdbcType="BIGINT"/>
		<result column="ctrl_height" property="ctrlHeight" jdbcType="INTEGER"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,schedule_item_id,schedule_instance_id,input_value
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.b2b.model.ScheduleItemInstance">
		update wx_t_schedule_item_instance
		<set>
			<if test="scheduleItemId != null" >
				schedule_item_id = #{scheduleItemId,jdbcType=BIGINT},
			</if>
			<if test="scheduleInstanceId != null" >
				schedule_instance_id = #{scheduleInstanceId,jdbcType=BIGINT},
			</if>
			<if test="inputValue != null" >
				input_value = #{inputValue,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.b2b.model.ScheduleItemInstanceExample">
    	delete from wx_t_schedule_item_instance
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleItemInstanceExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_schedule_item_instance
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.b2b.model.ScheduleItemInstance">
		insert into wx_t_schedule_item_instance
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="scheduleItemId != null">
				schedule_item_id,
			</if>
			<if test="scheduleInstanceId != null">
				schedule_instance_id,
			</if>
			<if test="inputValue != null">
				input_value,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="scheduleItemId != null">
				#{scheduleItemId,jdbcType=BIGINT},
			</if>
			<if test="scheduleInstanceId != null">
				#{scheduleInstanceId,jdbcType=BIGINT},
			</if>
			<if test="inputValue != null">
				#{inputValue,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_schedule_item_instance
		<set>
			<if test="record.scheduleItemId != null">
				schedule_item_id = #{record.scheduleItemId,jdbcType=BIGINT},
			</if>
			<if test="record.scheduleInstanceId != null">
				schedule_instance_id = #{record.scheduleInstanceId,jdbcType=BIGINT},
			</if>
			<if test="record.inputValue != null">
				input_value = #{record.inputValue,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.b2b.model.ScheduleItemInstanceExample">
		delete from wx_t_schedule_item_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.b2b.model.ScheduleItemInstanceExample">
		select count(1) from wx_t_schedule_item_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.b2b.model.ScheduleItemInstanceExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_schedule_item_instance
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select isnull(t1.id, si.id) id, si.id schedule_item_id, t1.schedule_instance_id, t1.input_value, 
		si.caption, isnull(si.input_ctrl_config, ic.default_config) input_ctrl_config, si.required_flag,
		ic.ctrl_type, si.order_numb, si.input_ctrl_id, si.ctrl_height
		  from wx_t_schedule_item si 
		  left join wx_t_schedule_item_instance t1 on si.id=t1.schedule_item_id
		  left join wx_t_input_ctrl ic on ic.id=si.input_ctrl_id
		 where 1=1
		 <if test="scheduleInstanceId != null">
		 and t1.schedule_instance_id = #{scheduleInstanceId, jdbcType=BIGINT}
		 </if>
		 <if test="scheduleId != null">
		 and si.schedule_id=#{scheduleId, jdbcType=BIGINT}
		 </if>
		 <if test="scheduleEntityId != null">
		 and exists (select 1 from wx_t_schedule_entity se where se.id=#{scheduleEntityId, jdbcType=BIGINT} and se.schedule_id=si.schedule_id)
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_schedule_item_instance (schedule_item_id, schedule_instance_id, input_value) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.scheduleItemId, jdbcType=BIGINT}, #{item.scheduleInstanceId, jdbcType=BIGINT}, #{item.inputValue, jdbcType=VARCHAR}
			</trim>
		</foreach>
	</insert>
	
	<insert id="insertBatchWhenPublish" parameterType="map">
		insert into wx_t_schedule_item_instance (schedule_item_id, schedule_instance_id)
		select sit.id, si.id from wx_t_schedule_instance si
		join wx_t_schedule_item sit on si.schedule_id=sit.schedule_id
		where si.schedule_id=#{scheduleId, jdbcType=BIGINT}
	</insert>
	
	<insert id="insertBatchByScheduleInstance" parameterType="map">
		insert into wx_t_schedule_item_instance (schedule_item_id, schedule_instance_id)
		select sit.id, si.id from wx_t_schedule_instance si
		join wx_t_schedule_entity se on si.schedule_id=se.id
		join wx_t_schedule_item sit on se.schedule_id=sit.schedule_id
		where si.id=#{scheduleInstanceId, jdbcType=BIGINT}
	</insert>
</mapper>
