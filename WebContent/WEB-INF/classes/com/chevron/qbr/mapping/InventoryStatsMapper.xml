<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.qbr.dao.InventoryStatsMapper" >
    <resultMap id="BaseResultMap" type="com.chevron.qbr.model.InventoryValueItem" >
        <result column="Year" property="year" jdbcType="VARCHAR" />
        <result column="Month" property="month" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="liters" property="liters" jdbcType="NUMERIC" />
    </resultMap>

    <select id="queryTotalStat" resultMap="BaseResultMap" parameterType="com.chevron.qbr.model.FundStatsQueryParam">
        <if test="salesChannel == 'CDM'">
            select
            round(sum(inventory.liters),2) liters,
              goods.cdm_product_category product_type,
              month(inventory.check_date) month,
              year(inventory.check_date) year
            from dbo.dw_pp_dms_inventory inventory
            left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = inventory.distributor_id
            left join dbo.dw_pp_dms_goods goods on inventory.goods_id = goods.goods_id
            where 1=1
            AND sales_channel_name = 'CDM'
            and check_date in (
              select max(inventory_date.check_date) from dbo.dw_pp_dms_inventory inventory_date where
                   inventory_date.check_date &gt; #{startDate, jdbcType=TIMESTAMP} and  inventory_date.check_date &lt;= #{endDate, jdbcType=TIMESTAMP}
              group by month(inventory_date.check_date)
            )
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            group by goods.cdm_product_category,month(inventory.check_date),year(inventory.check_date)
        </if>
        <if test="salesChannel != 'CDM'">
            select
            round(sum(inventory.liters),2) liters,
            goods.cio_product_category product_type,
            month(inventory.check_date) month,
            year(inventory.check_date) year
            from dbo.dw_pp_dms_inventory inventory
            left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = inventory.distributor_id
            left join dbo.dw_pp_dms_goods goods on inventory.goods_id = goods.goods_id
            where 1=1
            <![CDATA[ AND sales_channel_name =  'C&I' ]]>
            and check_date in (
               select max(inventory_date.check_date) from dbo.dw_pp_dms_inventory inventory_date where
              inventory_date.check_date &gt; #{startDate, jdbcType=TIMESTAMP} and  inventory_date.check_date &lt;= #{endDate, jdbcType=TIMESTAMP}
              group by month(inventory_date.check_date)
            )
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            group by goods.cio_product_category,month(inventory.check_date),year(inventory.check_date)
        </if>
    </select>
</mapper>