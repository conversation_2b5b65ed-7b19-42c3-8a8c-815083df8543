<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.qbr.dao.WXTQbrCommentVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.qbr.model.WXTQbrCommentVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="target_id" jdbcType="VARCHAR" property="targetId" />
    <result column="content" jdbcType="NVARCHAR" property="content" />
    <result column="comment_by" jdbcType="BIGINT" property="commentBy" />
    <result column="comment_by_person_name" jdbcType="NVARCHAR" property="commentByPersonName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag" />
    <result column="ATTRIBUTE1" jdbcType="VARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="VARCHAR" property="attribute2" />
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <!--<collection column="id" select="com.chevron.qbr.dao.WXTQbrCommentAttVoMapper.selectAttIdByCommentId" property="attIds" />-->
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, target_id, content, comment_by, create_date, deleted_flag, ATTRIBUTE1, ATTRIBUTE2, partner_id
  </sql>
  <select id="selectByExample" parameterType="com.chevron.qbr.model.WXTQbrCommentVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_qbr_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_t_qbr_comment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryComments" parameterType="com.chevron.qbr.model.WXTQbrCommentQueryParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />, u.ch_name comment_by_person_name
    from dbo.wx_t_qbr_comment c
       left join dbo.wx_t_user u on u.user_id = c.comment_by
    where 1=1
       and c.deleted_flag = 0
       and c.target_id = #{targetId}
       and c.partner_id = #{partnerId}
      <if test="year != null">
        and year(c.create_date) = #{year}
      </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_qbr_comment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.qbr.model.WXTQbrCommentVoExample">
    delete from wx_t_qbr_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.qbr.model.WXTQbrCommentVo">
    insert into wx_t_qbr_comment (id, target_id, content, 
      comment_by, create_date, deleted_flag,
      ATTRIBUTE1, ATTRIBUTE2, partner_id)
    values (#{id,jdbcType=BIGINT}, #{targetId,jdbcType=VARCHAR}, #{content,jdbcType=NVARCHAR}, 
      #{commentBy,jdbcType=BIGINT}, #{createDate,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=INTEGER}, 
      #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR},  #{partnerId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.qbr.model.WXTQbrCommentVo">
    insert into wx_t_qbr_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="commentBy != null">
        comment_by,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>
      <if test="partnerId != null">
        partner_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=NVARCHAR},
      </if>
      <if test="commentBy != null">
        #{commentBy,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=INTEGER},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.qbr.model.WXTQbrCommentVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_qbr_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_qbr_comment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.targetId != null">
        target_id = #{record.targetId,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=NVARCHAR},
      </if>
      <if test="record.commentBy != null">
        comment_by = #{record.commentBy,jdbcType=BIGINT},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedFlag != null">
        deleted_flag = #{record.deletedFlag,jdbcType=INTEGER},
      </if>
      <if test="record.attribute1 != null">
        ATTRIBUTE1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null">
        ATTRIBUTE2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_qbr_comment
    set id = #{record.id,jdbcType=BIGINT},
      target_id = #{record.targetId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=NVARCHAR},
      comment_by = #{record.commentBy,jdbcType=BIGINT},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      deleted_flag = #{record.deletedFlag,jdbcType=INTEGER},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=VARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.qbr.model.WXTQbrCommentVo">
    update wx_t_qbr_comment
    <set>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=NVARCHAR},
      </if>
      <if test="commentBy != null">
        comment_by = #{commentBy,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=INTEGER},
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.qbr.model.WXTQbrCommentVo">
    update wx_t_qbr_comment
    set target_id = #{targetId,jdbcType=VARCHAR},
      content = #{content,jdbcType=NVARCHAR},
      comment_by = #{commentBy,jdbcType=BIGINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=INTEGER},
      ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>