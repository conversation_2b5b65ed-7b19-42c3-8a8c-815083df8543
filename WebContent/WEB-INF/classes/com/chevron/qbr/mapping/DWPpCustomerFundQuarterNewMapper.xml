<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.qbr.dao.DWPpCustomerFundQuarterNewMapper">
  <resultMap id="BaseResultMap" type="com.chevron.qbr.model.DWPpCustomerFundQuarterNew">
    <result column="Year" jdbcType="INTEGER" property="year" />
    <result column="Quarter" jdbcType="VARCHAR" property="quarter" />
    <result column="Customer_Name_CN" jdbcType="NVARCHAR" property="customerNameCn" />
    <result column="Distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="Customer_Grade" jdbcType="VARCHAR" property="customerGrade" />
    <result column="Total_Volume" jdbcType="NUMERIC" property="totalVolume" />
    <result column="Total_Fund" jdbcType="NUMERIC" property="totalFund" />
    <result column="CIO_BD_Fund" jdbcType="NUMERIC" property="cioBdFund" />
    <result column="CDM_BD_Fund" jdbcType="NUMERIC" property="cdmBdFund" />
    <result column="CIO_Marketing_Fund" jdbcType="NUMERIC" property="cioMarketingFund" />
    <result column="CDM_Marketing_Fund" jdbcType="NUMERIC" property="cdmMarketingFund" />
    <result column="CIO_IVI_Fund" jdbcType="NUMERIC" property="cioIviFund" />
    <result column="CDM_IVI_Fund" jdbcType="NUMERIC" property="cdmIviFund" />
    <result column="actual_CIO_BD_Fund" jdbcType="NUMERIC" property="actualCioBdFund" />
    <result column="actual_CDM_BD_Fund" jdbcType="NUMERIC" property="actualCdmBdFund" />
    <result column="actual_CIO_Marketing_Fund" jdbcType="NUMERIC" property="actualCioMarketingFund" />
    <result column="actual_CDM_Marketing_Fund" jdbcType="NUMERIC" property="actualCdmMarketingFund" />
    <result column="actual_CIO_IVI_Fund" jdbcType="NUMERIC" property="actualCioIviFund" />
    <result column="actual_CDM_IVI_Fund" jdbcType="NUMERIC" property="actualCdmIviFund" />
    <result column="Is_Elite_Customer" jdbcType="INTEGER" property="isEliteCustomer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    Year, Quarter, Customer_Name_CN, Distributor_id, Customer_Grade, Total_Volume, Total_Fund, 
    CIO_BD_Fund, CDM_BD_Fund, CIO_Marketing_Fund, CDM_Marketing_Fund, CIO_IVI_Fund, CDM_IVI_Fund, 
    actual_CIO_BD_Fund, actual_CDM_BD_Fund, actual_CIO_Marketing_Fund, actual_CDM_Marketing_Fund, 
    actual_CIO_IVI_Fund, actual_CDM_IVI_Fund, Is_Elite_Customer
  </sql>
  <select id="selectByExample" parameterType="com.chevron.qbr.model.DWPpCustomerFundQuarterNewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dw_pp_customer_fund_quarter_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.qbr.model.DWPpCustomerFundQuarterNewExample">
    delete from dw_pp_customer_fund_quarter_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.qbr.model.DWPpCustomerFundQuarterNew">
    insert into dw_pp_customer_fund_quarter_new (Year, Quarter, Customer_Name_CN, 
      Distributor_id, Customer_Grade, Total_Volume, 
      Total_Fund, CIO_BD_Fund, CDM_BD_Fund, 
      CIO_Marketing_Fund, CDM_Marketing_Fund, CIO_IVI_Fund, 
      CDM_IVI_Fund, actual_CIO_BD_Fund, actual_CDM_BD_Fund, 
      actual_CIO_Marketing_Fund, actual_CDM_Marketing_Fund, 
      actual_CIO_IVI_Fund, actual_CDM_IVI_Fund, Is_Elite_Customer
      )
    values (#{year,jdbcType=INTEGER}, #{quarter,jdbcType=VARCHAR}, #{customerNameCn,jdbcType=NVARCHAR}, 
      #{distributorId,jdbcType=BIGINT}, #{customerGrade,jdbcType=VARCHAR}, #{totalVolume,jdbcType=NUMERIC}, 
      #{totalFund,jdbcType=NUMERIC}, #{cioBdFund,jdbcType=NUMERIC}, #{cdmBdFund,jdbcType=NUMERIC}, 
      #{cioMarketingFund,jdbcType=NUMERIC}, #{cdmMarketingFund,jdbcType=NUMERIC}, #{cioIviFund,jdbcType=NUMERIC}, 
      #{cdmIviFund,jdbcType=NUMERIC}, #{actualCioBdFund,jdbcType=NUMERIC}, #{actualCdmBdFund,jdbcType=NUMERIC}, 
      #{actualCioMarketingFund,jdbcType=NUMERIC}, #{actualCdmMarketingFund,jdbcType=NUMERIC}, 
      #{actualCioIviFund,jdbcType=NUMERIC}, #{actualCdmIviFund,jdbcType=NUMERIC}, #{isEliteCustomer,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.qbr.model.DWPpCustomerFundQuarterNew">
    insert into dw_pp_customer_fund_quarter_new
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="year != null">
        Year,
      </if>
      <if test="quarter != null">
        Quarter,
      </if>
      <if test="customerNameCn != null">
        Customer_Name_CN,
      </if>
      <if test="distributorId != null">
        Distributor_id,
      </if>
      <if test="customerGrade != null">
        Customer_Grade,
      </if>
      <if test="totalVolume != null">
        Total_Volume,
      </if>
      <if test="totalFund != null">
        Total_Fund,
      </if>
      <if test="cioBdFund != null">
        CIO_BD_Fund,
      </if>
      <if test="cdmBdFund != null">
        CDM_BD_Fund,
      </if>
      <if test="cioMarketingFund != null">
        CIO_Marketing_Fund,
      </if>
      <if test="cdmMarketingFund != null">
        CDM_Marketing_Fund,
      </if>
      <if test="cioIviFund != null">
        CIO_IVI_Fund,
      </if>
      <if test="cdmIviFund != null">
        CDM_IVI_Fund,
      </if>
      <if test="actualCioBdFund != null">
        actual_CIO_BD_Fund,
      </if>
      <if test="actualCdmBdFund != null">
        actual_CDM_BD_Fund,
      </if>
      <if test="actualCioMarketingFund != null">
        actual_CIO_Marketing_Fund,
      </if>
      <if test="actualCdmMarketingFund != null">
        actual_CDM_Marketing_Fund,
      </if>
      <if test="actualCioIviFund != null">
        actual_CIO_IVI_Fund,
      </if>
      <if test="actualCdmIviFund != null">
        actual_CDM_IVI_Fund,
      </if>
      <if test="isEliteCustomer != null">
        Is_Elite_Customer,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="quarter != null">
        #{quarter,jdbcType=VARCHAR},
      </if>
      <if test="customerNameCn != null">
        #{customerNameCn,jdbcType=NVARCHAR},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="customerGrade != null">
        #{customerGrade,jdbcType=VARCHAR},
      </if>
      <if test="totalVolume != null">
        #{totalVolume,jdbcType=NUMERIC},
      </if>
      <if test="totalFund != null">
        #{totalFund,jdbcType=NUMERIC},
      </if>
      <if test="cioBdFund != null">
        #{cioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="cdmBdFund != null">
        #{cdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="cioMarketingFund != null">
        #{cioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="cdmMarketingFund != null">
        #{cdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="cioIviFund != null">
        #{cioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="cdmIviFund != null">
        #{cdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCioBdFund != null">
        #{actualCioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCdmBdFund != null">
        #{actualCdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCioMarketingFund != null">
        #{actualCioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCdmMarketingFund != null">
        #{actualCdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCioIviFund != null">
        #{actualCioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="actualCdmIviFund != null">
        #{actualCdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="isEliteCustomer != null">
        #{isEliteCustomer,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.qbr.model.DWPpCustomerFundQuarterNewExample" resultType="java.lang.Long">
    select count(*) from dw_pp_customer_fund_quarter_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dw_pp_customer_fund_quarter_new
    <set>
      <if test="record.year != null">
        Year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.quarter != null">
        Quarter = #{record.quarter,jdbcType=VARCHAR},
      </if>
      <if test="record.customerNameCn != null">
        Customer_Name_CN = #{record.customerNameCn,jdbcType=NVARCHAR},
      </if>
      <if test="record.distributorId != null">
        Distributor_id = #{record.distributorId,jdbcType=BIGINT},
      </if>
      <if test="record.customerGrade != null">
        Customer_Grade = #{record.customerGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.totalVolume != null">
        Total_Volume = #{record.totalVolume,jdbcType=NUMERIC},
      </if>
      <if test="record.totalFund != null">
        Total_Fund = #{record.totalFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cioBdFund != null">
        CIO_BD_Fund = #{record.cioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cdmBdFund != null">
        CDM_BD_Fund = #{record.cdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cioMarketingFund != null">
        CIO_Marketing_Fund = #{record.cioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cdmMarketingFund != null">
        CDM_Marketing_Fund = #{record.cdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cioIviFund != null">
        CIO_IVI_Fund = #{record.cioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.cdmIviFund != null">
        CDM_IVI_Fund = #{record.cdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCioBdFund != null">
        actual_CIO_BD_Fund = #{record.actualCioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCdmBdFund != null">
        actual_CDM_BD_Fund = #{record.actualCdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCioMarketingFund != null">
        actual_CIO_Marketing_Fund = #{record.actualCioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCdmMarketingFund != null">
        actual_CDM_Marketing_Fund = #{record.actualCdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCioIviFund != null">
        actual_CIO_IVI_Fund = #{record.actualCioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.actualCdmIviFund != null">
        actual_CDM_IVI_Fund = #{record.actualCdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.isEliteCustomer != null">
        Is_Elite_Customer = #{record.isEliteCustomer,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dw_pp_customer_fund_quarter_new
    set Year = #{record.year,jdbcType=INTEGER},
      Quarter = #{record.quarter,jdbcType=VARCHAR},
      Customer_Name_CN = #{record.customerNameCn,jdbcType=NVARCHAR},
      Distributor_id = #{record.distributorId,jdbcType=BIGINT},
      Customer_Grade = #{record.customerGrade,jdbcType=VARCHAR},
      Total_Volume = #{record.totalVolume,jdbcType=NUMERIC},
      Total_Fund = #{record.totalFund,jdbcType=NUMERIC},
      CIO_BD_Fund = #{record.cioBdFund,jdbcType=NUMERIC},
      CDM_BD_Fund = #{record.cdmBdFund,jdbcType=NUMERIC},
      CIO_Marketing_Fund = #{record.cioMarketingFund,jdbcType=NUMERIC},
      CDM_Marketing_Fund = #{record.cdmMarketingFund,jdbcType=NUMERIC},
      CIO_IVI_Fund = #{record.cioIviFund,jdbcType=NUMERIC},
      CDM_IVI_Fund = #{record.cdmIviFund,jdbcType=NUMERIC},
      actual_CIO_BD_Fund = #{record.actualCioBdFund,jdbcType=NUMERIC},
      actual_CDM_BD_Fund = #{record.actualCdmBdFund,jdbcType=NUMERIC},
      actual_CIO_Marketing_Fund = #{record.actualCioMarketingFund,jdbcType=NUMERIC},
      actual_CDM_Marketing_Fund = #{record.actualCdmMarketingFund,jdbcType=NUMERIC},
      actual_CIO_IVI_Fund = #{record.actualCioIviFund,jdbcType=NUMERIC},
      actual_CDM_IVI_Fund = #{record.actualCdmIviFund,jdbcType=NUMERIC},
      Is_Elite_Customer = #{record.isEliteCustomer,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateActualValueByApprovedInfo" parameterType="map">
    update dw_pp_customer_fund_quarter_new
    <set>
      <if test="rebateCioBdFund != null">
        actual_CIO_BD_Fund = (CIO_BD_Fund + #{rebateCioBdFund,jdbcType=NUMERIC}),
      </if>
      <if test="rebateCdmBdFund != null">
        actual_CDM_BD_Fund = (CDM_BD_Fund + #{rebateCdmBdFund,jdbcType=NUMERIC}),
      </if>
      <if test="rebateCioMarketingFund != null">
        actual_CIO_Marketing_Fund = (CIO_Marketing_Fund + #{rebateCioMarketingFund,jdbcType=NUMERIC}),
      </if>
      <if test="rebateCdmMarketingFund != null">
        actual_CDM_Marketing_Fund = (CDM_Marketing_Fund + #{rebateCdmMarketingFund,jdbcType=NUMERIC}),
      </if>
      <if test="rebateCioIviFund != null">
        actual_CIO_IVI_Fund = (CIO_IVI_Fund + #{rebateCioIviFund,jdbcType=NUMERIC}),
      </if>
      <if test="rebateCdmIviFund != null">
        actual_CDM_IVI_Fund = (CDM_IVI_Fund + #{rebateCdmIviFund,jdbcType=NUMERIC}),
      </if>
    </set>
    where 1=1
    and Distributor_id in (
    select top 1 wx_t_partner_o2o_enterprise.distributor_id from dbo.wx_t_partner_o2o_enterprise where partner_id = #{partnerId}
    ) and Quarter = #{quarter} and Year = #{year}
  </update>
</mapper>