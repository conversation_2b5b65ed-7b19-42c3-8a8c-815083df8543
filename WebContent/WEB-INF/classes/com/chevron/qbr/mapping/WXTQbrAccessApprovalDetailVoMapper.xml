<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.qbr.dao.WXTQbrAccessApprovalDetailVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="quarter" jdbcType="INTEGER" property="quarter" />
    <result column="user_level" jdbcType="VARCHAR" property="userLevel" />
    <result column="rebate_CIO_BD_Fund" jdbcType="NUMERIC" property="rebateCioBdFund" />
    <result column="rebate_CDM_BD_Fund" jdbcType="NUMERIC" property="rebateCdmBdFund" />
    <result column="rebate_CIO_Marketing_Fund" jdbcType="NUMERIC" property="rebateCioMarketingFund" />
    <result column="rebate_CDM_Marketing_Fund" jdbcType="NUMERIC" property="rebateCdmMarketingFund" />
    <result column="rebate_CIO_IVI_Fund" jdbcType="NUMERIC" property="rebateCioIviFund" />
    <result column="rebate_CDM_IVI_Fund" jdbcType="NUMERIC" property="rebateCdmIviFund" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, partner_id, year, quarter, user_level, rebate_CIO_BD_Fund, rebate_CDM_BD_Fund, 
    rebate_CIO_Marketing_Fund, rebate_CDM_Marketing_Fund, rebate_CIO_IVI_Fund, rebate_CDM_IVI_Fund, 
    update_time, create_time, update_by, create_by, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_qbr_access_approval_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_qbr_access_approval_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from wx_t_qbr_access_approval_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVoExample">
    delete from wx_t_qbr_access_approval_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    insert into wx_t_qbr_access_approval_detail (id, partner_id, year, 
      quarter, user_level, rebate_CIO_BD_Fund, 
      rebate_CDM_BD_Fund, rebate_CIO_Marketing_Fund, 
      rebate_CDM_Marketing_Fund, rebate_CIO_IVI_Fund, 
      rebate_CDM_IVI_Fund, update_time, create_time, 
      update_by, create_by, delete_flag
      )
    values (#{id,jdbcType=INTEGER}, #{partnerId,jdbcType=BIGINT}, #{year,jdbcType=INTEGER}, 
      #{quarter,jdbcType=INTEGER}, #{userLevel,jdbcType=VARCHAR}, #{rebateCioBdFund,jdbcType=NUMERIC}, 
      #{rebateCdmBdFund,jdbcType=NUMERIC}, #{rebateCioMarketingFund,jdbcType=NUMERIC}, 
      #{rebateCdmMarketingFund,jdbcType=NUMERIC}, #{rebateCioIviFund,jdbcType=NUMERIC}, 
      #{rebateCdmIviFund,jdbcType=NUMERIC}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{createBy,jdbcType=BIGINT}, #{deleteFlag,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    insert into wx_t_qbr_access_approval_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partnerId != null">
        partner_id,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="quarter != null">
        quarter,
      </if>
      <if test="userLevel != null">
        user_level,
      </if>
      <if test="rebateCioBdFund != null">
        rebate_CIO_BD_Fund,
      </if>
      <if test="rebateCdmBdFund != null">
        rebate_CDM_BD_Fund,
      </if>
      <if test="rebateCioMarketingFund != null">
        rebate_CIO_Marketing_Fund,
      </if>
      <if test="rebateCdmMarketingFund != null">
        rebate_CDM_Marketing_Fund,
      </if>
      <if test="rebateCioIviFund != null">
        rebate_CIO_IVI_Fund,
      </if>
      <if test="rebateCdmIviFund != null">
        rebate_CDM_IVI_Fund,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="quarter != null">
        #{quarter,jdbcType=INTEGER},
      </if>
      <if test="userLevel != null">
        #{userLevel,jdbcType=VARCHAR},
      </if>
      <if test="rebateCioBdFund != null">
        #{rebateCioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmBdFund != null">
        #{rebateCdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCioMarketingFund != null">
        #{rebateCioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmMarketingFund != null">
        #{rebateCdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCioIviFund != null">
        #{rebateCioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmIviFund != null">
        #{rebateCdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_qbr_access_approval_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_qbr_access_approval_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.partnerId != null">
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.quarter != null">
        quarter = #{record.quarter,jdbcType=INTEGER},
      </if>
      <if test="record.userLevel != null">
        user_level = #{record.userLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateCioBdFund != null">
        rebate_CIO_BD_Fund = #{record.rebateCioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.rebateCdmBdFund != null">
        rebate_CDM_BD_Fund = #{record.rebateCdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="record.rebateCioMarketingFund != null">
        rebate_CIO_Marketing_Fund = #{record.rebateCioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.rebateCdmMarketingFund != null">
        rebate_CDM_Marketing_Fund = #{record.rebateCdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="record.rebateCioIviFund != null">
        rebate_CIO_IVI_Fund = #{record.rebateCioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.rebateCdmIviFund != null">
        rebate_CDM_IVI_Fund = #{record.rebateCdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_qbr_access_approval_detail
    set id = #{record.id,jdbcType=INTEGER},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      year = #{record.year,jdbcType=INTEGER},
      quarter = #{record.quarter,jdbcType=INTEGER},
      user_level = #{record.userLevel,jdbcType=VARCHAR},
      rebate_CIO_BD_Fund = #{record.rebateCioBdFund,jdbcType=NUMERIC},
      rebate_CDM_BD_Fund = #{record.rebateCdmBdFund,jdbcType=NUMERIC},
      rebate_CIO_Marketing_Fund = #{record.rebateCioMarketingFund,jdbcType=NUMERIC},
      rebate_CDM_Marketing_Fund = #{record.rebateCdmMarketingFund,jdbcType=NUMERIC},
      rebate_CIO_IVI_Fund = #{record.rebateCioIviFund,jdbcType=NUMERIC},
      rebate_CDM_IVI_Fund = #{record.rebateCdmIviFund,jdbcType=NUMERIC},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    update wx_t_qbr_access_approval_detail
    <set>
      <if test="partnerId != null">
        partner_id = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="quarter != null">
        quarter = #{quarter,jdbcType=INTEGER},
      </if>
      <if test="userLevel != null">
        user_level = #{userLevel,jdbcType=VARCHAR},
      </if>
      <if test="rebateCioBdFund != null">
        rebate_CIO_BD_Fund = #{rebateCioBdFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmBdFund != null">
        rebate_CDM_BD_Fund = #{rebateCdmBdFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCioMarketingFund != null">
        rebate_CIO_Marketing_Fund = #{rebateCioMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmMarketingFund != null">
        rebate_CDM_Marketing_Fund = #{rebateCdmMarketingFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCioIviFund != null">
        rebate_CIO_IVI_Fund = #{rebateCioIviFund,jdbcType=NUMERIC},
      </if>
      <if test="rebateCdmIviFund != null">
        rebate_CDM_IVI_Fund = #{rebateCdmIviFund,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    update wx_t_qbr_access_approval_detail
    set partner_id = #{partnerId,jdbcType=BIGINT},
      year = #{year,jdbcType=INTEGER},
      quarter = #{quarter,jdbcType=INTEGER},
      user_level = #{userLevel,jdbcType=VARCHAR},
      rebate_CIO_BD_Fund = #{rebateCioBdFund,jdbcType=NUMERIC},
      rebate_CDM_BD_Fund = #{rebateCdmBdFund,jdbcType=NUMERIC},
      rebate_CIO_Marketing_Fund = #{rebateCioMarketingFund,jdbcType=NUMERIC},
      rebate_CDM_Marketing_Fund = #{rebateCdmMarketingFund,jdbcType=NUMERIC},
      rebate_CIO_IVI_Fund = #{rebateCioIviFund,jdbcType=NUMERIC},
      rebate_CDM_IVI_Fund = #{rebateCdmIviFund,jdbcType=NUMERIC},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryAccessApprovalDetail" parameterType="map" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    from wx_t_qbr_access_approval_detail
    where 1=1
    and year = #{year}
    and quarter = #{quarter}
    and user_level = #{userLevel}
    and partner_id = #{partnerId}
    and delete_flag = 0
  </select>
  <update id="directlyUpdateRebateFund" parameterType="com.chevron.qbr.model.WXTQbrAccessApprovalDetailVo">
    update wx_t_qbr_access_approval_detail
    set rebate_CIO_BD_Fund = #{rebateCioBdFund,jdbcType=NUMERIC},
      rebate_CDM_BD_Fund = #{rebateCdmBdFund,jdbcType=NUMERIC},
      rebate_CIO_Marketing_Fund = #{rebateCioMarketingFund,jdbcType=NUMERIC},
      rebate_CDM_Marketing_Fund = #{rebateCdmMarketingFund,jdbcType=NUMERIC},
      rebate_CIO_IVI_Fund = #{rebateCioIviFund,jdbcType=NUMERIC},
      rebate_CDM_IVI_Fund = #{rebateCdmIviFund,jdbcType=NUMERIC},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>