<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.qbr.dao.DmsSellinStatsMapper" >
    <resultMap id="BaseResultMap" type="com.chevron.qbr.model.SellinStatsItem" >
        <id column="partner_id" property="partnerId" jdbcType="BIGINT" />
        <result column="enterprise_name" property="enterpriseName" jdbcType="NVARCHAR" />
        <result column="trans_time" property="transTime" jdbcType="TIMESTAMP" />
        <result column="target_value" property="targetValue" jdbcType="NUMERIC" />
        <result column="actual_value" property="actualValue" jdbcType="NUMERIC" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="sales_channel" property="salesChannel" jdbcType="VARCHAR" />
    </resultMap>

    <select id="querySellinStatsByMonth" resultMap="BaseResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        select  round(isnull(sum(liters), 0),2) actual_value,poe.partner_id,round(isnull(total_target_info.total_target,0),2) as target_value,
        convert(date,concat(year(si.trans_time),'-',month(si.trans_time),'-01',' 00:00:00')) trans_time
        from dbo.dw_pp_dms_trans_sell_in si
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = si.distributor_id
        left join
        (select
        sum(customer_target.target_volume) total_target,
        customer_target.distributor_id,
        datepart(year, customer_target.rebate_target_date) year,
        datepart(month, customer_target.rebate_target_date) month
        from dbo.dw_pp_rebate_customer_target customer_target
        where 1=1
        <if test="salesChannel == 'C&amp;I' || salesChannel=='CIO'">
            and customer_target.bu = 'CIO'
        </if>
        <if test="salesChannel == 'CDM'">
            and customer_target.bu = 'CDM'
        </if>
        <if test="salesChannel == null">
            and customer_target.bu in  ('CDM','CIO')
        </if>
        group by customer_target.distributor_id, datepart(year, customer_target.rebate_target_date),
        datepart(month, customer_target.rebate_target_date)
        ) total_target_info
        on total_target_info.distributor_id = si.distributor_id
           and month(si.trans_time) = total_target_info.month
           and year(si.trans_time) = total_target_info.year
        where 1=1
        and si.trans_time between #{startDate, jdbcType=TIMESTAMP} and  #{endDate, jdbcType=TIMESTAMP}
        <if test="salesChannel != null">
        and si.sales_channel_name = #{salesChannel}
        </if>
        <if test="salesChannel == null">
            and si.sales_channel_name in  ('CDM','C&amp;I')
        </if>
        <if test="partnerId != null ">
            and poe.partner_id = #{partnerId, jdbcType=BIGINT}
        </if>
        group by poe.partner_id, poe.enterprise_name, year(si.trans_time), month(si.trans_time),total_target_info.total_target
        order by year(si.trans_time),month(si.trans_time)
    </select>
    <select id="selectTargetValueByMonth" parameterType="com.chevron.qbr.model.StatsQueryParam" resultMap="BaseResultMap">
        select null actual_value,en.partner_id,null enterprise_name,customer_target.rebate_target_date transTime,
        round(isnull(sum(target_volume),0),2) as target_value
        from dbo.dw_pp_rebate_customer_target customer_target
        left join dbo.wx_t_partner_o2o_enterprise en on customer_target.distributor_id = en.distributor_id
        where 1=1
        and year(customer_target.rebate_target_date) = #{year,jdbcType =INTEGER }
        and en.partner_id = #{partnerId, jdbcType=BIGINT}
        <if test="salesChannel == 'C&amp;I' || salesChannel=='CIO'">
            and customer_target.bu = 'CIO'
        </if>
        <if test="salesChannel == 'CDM'">
            and customer_target.bu = 'CDM'
        </if>
        <if test="salesChannel == null">
            and customer_target.bu in  ('CDM','CIO')
        </if>
        group by customer_target.rebate_target_date,en.partner_id
        order by customer_target.rebate_target_date
    </select>
    <select id="querySellinStatsByMonthAndType" resultMap="BaseResultMap" parameterType="com.chevron.qbr.model.StatsQueryParam" >
        <if test="salesChannel == 'CDM'">
        select round(isnull(sum(liters), 0),2) actual_value,poe.partner_id,poe.enterprise_name,
            convert(date,concat(year(si.trans_time),'-',month(si.trans_time),'-01',' 00:00:00')) trans_time,
            0 as target_value,goods.cdm_product_category product_type
        from dbo.dw_pp_dms_trans_sell_in si
        left join dw_pp_dms_goods goods on goods.goods_id = si.goods_id
        left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = si.distributor_id
            where 1=1
            and trans_time between #{startDate, jdbcType=TIMESTAMP} and  #{endDate, jdbcType=TIMESTAMP}
            <if test="salesChannel != null">
                and si.sales_channel_name = #{salesChannel}
            </if>
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            and goods.cdm_product_category in <![CDATA[('Others','SM & Below','SN & Above')]]>
            group by poe.partner_id,poe.enterprise_name,year(si.trans_time),month(si.trans_time),goods.cdm_product_category
            order by trans_time
        </if>
        <if test="salesChannel != 'CDM'">
            select round(isnull(sum(liters), 0),2) actual_value,poe.partner_id,poe.enterprise_name,
            convert(date,concat(year(si.trans_time),'-',month(si.trans_time),'-01',' 00:00:00')) trans_time,
            0 as target_value,goods.cio_product_category product_type
            from dbo.dw_pp_dms_trans_sell_in si
            left join dw_pp_dms_goods goods on goods.goods_id = si.goods_id
            left join wx_t_partner_o2o_enterprise poe on poe.distributor_id = si.distributor_id
            where 1=1
            and trans_time between #{startDate, jdbcType=TIMESTAMP} and  #{endDate, jdbcType=TIMESTAMP}
            <if test="salesChannel != null">
                and si.sales_channel_name = #{salesChannel}
            </if>
            <if test="partnerId != null ">
                and poe.partner_id = #{partnerId, jdbcType=BIGINT}
            </if>
            and goods.cio_product_category in ('液压油','柴机油','Others')
            group by poe.partner_id,poe.enterprise_name,year(si.trans_time),month(si.trans_time),goods.cio_product_category
            order by trans_time
        </if>
    </select>
</mapper>