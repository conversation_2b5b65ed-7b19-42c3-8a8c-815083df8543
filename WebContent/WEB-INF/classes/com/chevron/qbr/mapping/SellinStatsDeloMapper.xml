<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.qbr.dao.SellinStatsDeloMapper">
	<resultMap id="BaseResultMap"
		type="com.chevron.qbr.model.SellinStatsItem">
		<!--private Long partnerId; -->
		<!--private String enterpriseName; -->
		<!--private Date transTime; -->
		<!--private Double targetValue; -->
		<!--private Double actualValue; -->
		<!--private String productType; -->
		<!--private String salesChannel; -->

		<id column="partner_id" property="partnerId" jdbcType="BIGINT" />
		<result column="enterprise_name" property="enterpriseName"
			jdbcType="NVARCHAR" />
		<result column="trans_time" property="transTime"
			jdbcType="TIMESTAMP" />
		<result column="target_value" property="targetValue"
			jdbcType="NUMERIC" />
		<result column="actual_value" property="actualValue"
			jdbcType="NUMERIC" />
		<result column="product_type" property="productType"
			jdbcType="VARCHAR" />
		<result column="sales_channel" property="salesChannel"
			jdbcType="VARCHAR" />
		<result column="sales_channel" property="productName"
			jdbcType="VARCHAR" />
		<result column="product_name" property="productName"
			jdbcType="VARCHAR" />
		<result column="quarter" property="quarter" jdbcType="INTEGER" />
	</resultMap>

	<select id="querySellinStatsByMonth" resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		<if test="!isChannelManager">
				select sum(actual_value) actual_value,
				sum(target_value) target_value,
				trans_time
				from (
				select round(isnull(sum(liters), 0),2) actual_value,
				si.trans_time,
				round(isnull(total_target_info.total_target,0),2) as target_value
				from
				dw_base_trans_sell_in si
				left join wx_t_partner_o2o_enterprise poe on poe.distributor_id =
				si.distributor_id
				<if test="cai != null">
					LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
					dwrssr.distributor_id = si.distributor_id and si.sales_cai =
					dwrssr.sales_cai
					LEFT JOIN dw_region_sales_channel_rel dwrsr ON
					dwrsr.region_name = dwrssr.region_name
				</if>
				left join
				(select
				sum(customer_target.target_volume) total_target,
				customer_target.distributor_id,
				datepart(year, customer_target.rebate_target_date) year,
				datepart(month, customer_target.rebate_target_date) month
				from dbo.dw_pp_rebate_customer_target customer_target
				<if test="cai != null">
					LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
					dwrssr.distributor_id = customer_target.distributor_id
					LEFT JOIN dw_region_sales_channel_rel dwrsr ON
					dwrsr.region_name = dwrssr.region_name
				</if>
				where 1=1
				<if test="cai != null">
					and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
					or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
					#{cai})
					<if test="salesChannel != null">
						and dwrsr.sales_channel_name = #{salesChannel}
					</if>
				</if>
				group by customer_target.distributor_id, datepart(year,
				customer_target.rebate_target_date),
				datepart(month, customer_target.rebate_target_date)
				) total_target_info
				on total_target_info.distributor_id = si.distributor_id
				and month(si.trans_time) = total_target_info.month
				and year(si.trans_time) = total_target_info.year
				where 1=1
				and trans_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate,
				jdbcType=TIMESTAMP}
				<if test="partnerId != null ">
					and poe.partner_id = #{partnerId, jdbcType=BIGINT}
				</if>
				<if test="cai != null">
					and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
					or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
					#{cai})
					<if test="salesChannel != null">
						and dwrsr.sales_channel_name = #{salesChannel}
					</if>
				</if>
				group by
				si.trans_time,total_target_info.total_target
				) aa
				group by trans_time
				order by trans_time
		</if>
		<if test="isChannelManager">
			select sum(actual_value) actual_value,
			sum(target_value) target_value,
			trans_time
			from (
			select round(isnull(sum(liters), 0),2) actual_value,
			si.trans_time,
			round(isnull(total_target_info.total_target,0),2) as target_value
			from
			dw_base_trans_sell_in si
			left join wx_t_partner_o2o_enterprise poe on poe.distributor_id =
			si.distributor_id
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON
				dwrssr.distributor_id = si.distributor_id and si.sales_cai =
				dwrssr.sales_cai
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			left join
			(select
			sum(customer_target.target_volume) total_target,
			customer_target.distributor_id,
			datepart(year, customer_target.rebate_target_date) year,
			datepart(month, customer_target.rebate_target_date) month
			from dbo.dw_pp_rebate_customer_target customer_target
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON
				dwrssr.distributor_id = customer_target.distributor_id
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			where 1=1
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by customer_target.distributor_id, datepart(year,
			customer_target.rebate_target_date),
			datepart(month, customer_target.rebate_target_date)
			) total_target_info
			on total_target_info.distributor_id = si.distributor_id
			and month(si.trans_time) = total_target_info.month
			and year(si.trans_time) = total_target_info.year
			where 1=1
			and trans_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate,
			jdbcType=TIMESTAMP}
			AND si.sales_name_cn != 'NA'
			<if test="partnerId != null ">
				and poe.partner_id = #{partnerId, jdbcType=BIGINT}
			</if>
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by
			si.trans_time,total_target_info.total_target
			UNION
			select round(isnull(sum(liters), 0),2) actual_value,
			si.trans_time,
			round(isnull(total_target_info.total_target,0),2) as target_value
			from
			dw_base_trans_sell_in si
			left join wx_t_partner_o2o_enterprise poe on poe.distributor_id =
			si.distributor_id
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON
				dwrssr.distributor_id = si.distributor_id
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			left join
			(select
			sum(customer_target.target_volume) total_target,
			customer_target.distributor_id,
			datepart(year, customer_target.rebate_target_date) year,
			datepart(month, customer_target.rebate_target_date) month
			from dbo.dw_pp_rebate_customer_target customer_target
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr ON
				dwrssr.distributor_id = customer_target.distributor_id
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			where 1=1
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by customer_target.distributor_id, datepart(year,
			customer_target.rebate_target_date),
			datepart(month, customer_target.rebate_target_date)
			) total_target_info
			on total_target_info.distributor_id = si.distributor_id
			and month(si.trans_time) = total_target_info.month
			and year(si.trans_time) = total_target_info.year
			where 1=1
			and trans_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate,
			jdbcType=TIMESTAMP}
			AND si.sales_name_cn = 'NA'
			<if test="partnerId != null ">
				and poe.partner_id = #{partnerId, jdbcType=BIGINT}
			</if>
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by
			si.trans_time,total_target_info.total_target
			) aa
			group by trans_time
			order by trans_time
		</if>
	</select>
	<!--增加by cai的版本 -->
	<sql id="querySellinStatsByMonthByCai">
		select sum(actual_value) actual_value,
		sum(target_value) target_value,
		trans_time
		from (
		select round(isnull(sum(liters), 0),2) actual_value,
		si.trans_time,
		round(isnull(total_target_info.total_target,0),2) as
		target_value
		from
		[PP_MID].dbo.syn_dw_sap_sell_in si
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
			ON
			dwrssr.distributor_id = si.distributor_id
			LEFT JOIN
			dw_region_sales_channel_rel dwrsr ON
			dwrsr.region_name =
			dwrssr.region_name
		</if>
		left join
		(select
		sum(customer_target.target_volume) total_target,
		customer_target.distributor_id,
		datepart(year,
		customer_target.rebate_target_date) year,
		datepart(month,
		customer_target.rebate_target_date) month
		from
		dbo.dw_pp_rebate_customer_target customer_target
		LEFT JOIN
		dw_customer_region_sales_supervisor_rel dwrssr ON
		dwrssr.distributor_id = customer_target.distributor_id
		LEFT JOIN
		dw_region_sales_channel_rel dwrsr ON
		dwrsr.region_name =
		dwrssr.region_name
		where 1=1
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = 'indirect'
			</if>
		</if>
		group by customer_target.distributor_id, datepart(year,
		customer_target.rebate_target_date),
		datepart(month,
		customer_target.rebate_target_date)
		) total_target_info
		on
		total_target_info.distributor_id = si.distributor_id
		and
		month(si.trans_time) = total_target_info.month
		and year(si.trans_time)
		= total_target_info.year
		where 1=1
		and trans_time between #{startDate,
		jdbcType=TIMESTAMP} and #{endDate,
		jdbcType=TIMESTAMP}
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = 'indirect'
			</if>
		</if>
		group by
		si.trans_time,total_target_info.total_target
		) aa
		group by
		trans_time
		order by trans_time
	</sql>

	<select id="querySellinStatsByMonthAndType"
		resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		select round(isnull(sum(liters), 0),2) actual_value,
		<if test="partnerId != null">
			poe.partner_id,poe.enterprise_name,
		</if>
		si.trans_time,p.ci_category1 product_type
		from
		[PP_MID].dbo.syn_dw_sap_sell_in si
		left join wx_t_product p on p.sku =
		si.product_code_sap
		<if test="partnerId != null">
			left join wx_t_partner_o2o_enterprise poe on
			poe.distributor_id =
			si.distributor_id
		</if>
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
			<if test="partnerId == null">
				dwrssr.distributor_id = si.distributor_id
			</if>
			<if test="partnerId != null">
				dwrssr.distributor_id = poe.distributor_id
			</if>
			LEFT JOIN [PP_MID].dbo.syn_dw_region dwrsr ON
			dwrsr.region_name =
			dwrssr.region_name
		</if>
		where 1=1
		and trans_time between #{startDate, jdbcType=TIMESTAMP} and
		#{endDate,
		jdbcType=TIMESTAMP}
		<if test="partnerId != null ">
			and poe.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = 'indirect'
			</if>
		</if>
		and p.ci_category1 in ('液压油','柴机油','Others')
		group by
		<if test="partnerId != null">
			poe.partner_id,poe.enterprise_name,
		</if>
		si.trans_time,p.ci_category1
		order by trans_time
	</select>
	<select id="selectTargetValueByMonth"
		parameterType="com.chevron.qbr.model.StatsQueryParam"
		resultMap="BaseResultMap">
		select null actual_value,
		<if test="partnerId != null ">
			en.partner_id,null enterprise_name,
		</if>
		customer_target.rebate_target_date transTime,
		round(isnull(sum(target_volume),0),2) as target_value
		from
		[PP_MID].dbo.syn_dw_baseline_target customer_target
		left join
		dbo.wx_t_partner_o2o_enterprise en on
		customer_target.distributor_id =
		en.distributor_id
		left join wx_t_organization o on o.id = en.partner_id
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
			ON
			dwrssr.distributor_id = en.distributor_id
			LEFT JOIN
			[PP_MID].dbo.syn_dw_region dwrsr ON
			dwrsr.region_name =
			dwrssr.region_name
		</if>
		where 1=1
		and en.partner_id != 9
		and o.type = 1 and o.status = 1
		and
		year(customer_target.rebate_target_date) = #{year,jdbcType =INTEGER }
		<if test="partnerId != null ">
			and en.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = 'indirect'
			</if>
		</if>
		group by
		<if test="partnerId != null ">
			en.partner_id,
		</if>
		customer_target.rebate_target_date
		order by
		customer_target.rebate_target_date
	</select>
	<select id="queryTotalSellinStatsByCai"
		resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		select
		isnull(sum(liters), 0) actual_value
		from
		[PP_MID].dbo.syn_dw_sap_sell_in si
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
			ON
			dwrssr.distributor_id = si.distributor_id
			LEFT JOIN
			dw_region_sales_channel_rel dwrsr ON dwrsr.region_name =
			dwrssr.region_name
		</if>
		where 1 = 1
		<if test="cai != null">
			and dwrsr.sales_channel_name = 'indirect'
			and
			(dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
		</if>
		and trans_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate,
		jdbcType=TIMESTAMP}
		and si.sales_channel_name = #{salesChannel}
	</select>
	<select id="querySellinStatsByQuarterByPartnerIds"
		resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		select sum(actual_value) actual_value,
		quarter,
		partner_id
		from (
		select
		round(isnull(sum(liters), 0),2) actual_value,
		poe.partner_id,poe.enterprise_name,
		datepart(quarter, trans_time)
		quarter
		from
		[PP_MID].dbo.syn_dw_sap_sell_in si
		left join
		wx_t_partner_o2o_enterprise poe on poe.distributor_id =
		si.distributor_id
		left join wx_t_organization o on poe.partner_id =
		o.id
		where 1=1
		and poe.partner_id != 9
		and trans_time between
		#{startDate, jdbcType=TIMESTAMP} and #{endDate,
		jdbcType=TIMESTAMP}
		<if test="partnerIds != null ">
			and poe.partner_id in (
			<foreach collection="partnerIds" index="index" item="item"
				separator=",">
				#{item}
			</foreach>
			)
		</if>
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai =
			#{cai}
			or dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
			#{cai})
		</if>
		and o.type = 1 and o.status = 1
		group by
		poe.partner_id,poe.enterprise_name,datepart(quarter, trans_time)
		) aa
		group by quarter,partner_id
		order by quarter,partner_id
	</select>
	<select id="queryTotalSellinStatsByQuarter"
		resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		<if test="!isChannelManager">
			select
			round(isnull(sum(total_info.actual_value),0), 2) actual_value,
			round(isnull(sum(total_info.target_value),0), 2) target_value
			from
			(
			select
			isnull(sum(liters), 0) actual_value,
			isnull(total_target_info.total_target, 0) as target_value
			from
			dw_base_trans_sell_in si
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr
				ON
				dwrssr.distributor_id = si.distributor_id and si.sales_cai =
				dwrssr.sales_cai
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			left join
			(select
			sum(customer_target.target_volume) total_target,
			customer_target.distributor_id,
			datepart(year,
			customer_target.rebate_target_date) year,
			datepart(month,
			customer_target.rebate_target_date) month
			from
			dbo.dw_pp_rebate_customer_target customer_target
			<if test="cai != null">
				LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
				ON
				dwrssr.distributor_id = customer_target.distributor_id
				LEFT JOIN
				dw_region_sales_channel_rel dwrsr ON dwrsr.region_name =
				dwrssr.region_name
			</if>
			where 1 = 1
			<if test="salesChannel != null">
				and customer_target.bu = #{salesChannel}
			</if>
			and datepart(year, customer_target.rebate_target_date) = #{year}
			<if test="quarter != null">
				and datepart(quarter, customer_target.rebate_target_date)
				= #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, customer_target.rebate_target_date)
				&lt;=
				#{maxMonth}
			</if>
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by customer_target.distributor_id, datepart(year,
			customer_target.rebate_target_date),
			datepart(month,
			customer_target.rebate_target_date)
			) total_target_info
			on
			total_target_info.distributor_id = si.distributor_id and
			datepart(month, si.trans_time) = total_target_info.month
			and
			year(si.trans_time) = total_target_info.year
			where 1 = 1
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			<if test="quarter != null">
				and datepart(quarter, trans_time) = #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, trans_time) &lt;= #{maxMonth}
			</if>
			and datepart(year, trans_time) = #{year}
			<if test="salesChannel != null">
				and si.sales_channel_name = #{salesChannel}
			</if>
			group by total_target_info.total_target
			) total_info
		</if>
		<if test="isChannelManager">
			select
			round(isnull(sum(total_info.actual_value),0), 2) actual_value,
			round(isnull(sum(total_info.target_value),0), 2) target_value
			from
			(
			select
			isnull(sum(liters), 0) actual_value,
			isnull(total_target_info.total_target, 0) as target_value
			from
			dw_base_trans_sell_in si
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr
				ON
				dwrssr.distributor_id = si.distributor_id and si.sales_cai =
				dwrssr.sales_cai
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			left join
			(select
			sum(customer_target.target_volume) total_target,
			customer_target.distributor_id,
			datepart(year,
			customer_target.rebate_target_date) year,
			datepart(month,
			customer_target.rebate_target_date) month
			from
			dbo.dw_pp_rebate_customer_target customer_target
			<if test="cai != null">
				LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
				ON
				dwrssr.distributor_id = customer_target.distributor_id
				LEFT JOIN
				dw_region_sales_channel_rel dwrsr ON dwrsr.region_name =
				dwrssr.region_name
			</if>
			where 1 = 1
			<if test="salesChannel != null">
				and customer_target.bu = #{salesChannel}
			</if>
			and datepart(year, customer_target.rebate_target_date) = #{year}
			<if test="quarter != null">
				and datepart(quarter, customer_target.rebate_target_date)
				= #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, customer_target.rebate_target_date)
				&lt;=
				#{maxMonth}
			</if>
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by customer_target.distributor_id, datepart(year,
			customer_target.rebate_target_date),
			datepart(month,
			customer_target.rebate_target_date)
			) total_target_info
			on
			total_target_info.distributor_id = si.distributor_id and
			datepart(month, si.trans_time) = total_target_info.month
			and
			year(si.trans_time) = total_target_info.year
			where 1 = 1
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			<if test="quarter != null">
				and datepart(quarter, trans_time) = #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, trans_time) &lt;= #{maxMonth}
			</if>
			and datepart(year, trans_time) = #{year}
			AND si.sales_name_cn != 'NA'
			<if test="salesChannel != null">
				and si.sales_channel_name = #{salesChannel}
			</if>
			group by total_target_info.total_target
			UNION
			select
			isnull(sum(liters), 0) actual_value,
			isnull(total_target_info.total_target, 0) as target_value
			from
			dw_base_trans_sell_in si
			<if test="cai != null">
				LEFT JOIN [PP_MID].[dbo].customer_sales_region_rel dwrssr
				ON
				dwrssr.distributor_id = si.distributor_id and si.sales_name_cn =
				dwrssr.sales_name
				LEFT JOIN dw_region_sales_channel_rel dwrsr ON
				dwrsr.region_name = dwrssr.region_name
			</if>
			left join
			(select
			sum(customer_target.target_volume) total_target,
			customer_target.distributor_id,
			datepart(year,
			customer_target.rebate_target_date) year,
			datepart(month,
			customer_target.rebate_target_date) month
			from
			dbo.dw_pp_rebate_customer_target customer_target
			<if test="cai != null">
				LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
				ON
				dwrssr.distributor_id = customer_target.distributor_id
				LEFT JOIN
				dw_region_sales_channel_rel dwrsr ON dwrsr.region_name =
				dwrssr.region_name
			</if>
			where 1 = 1
			<if test="salesChannel != null">
				and customer_target.bu = #{salesChannel}
			</if>
			and datepart(year, customer_target.rebate_target_date) = #{year}
			<if test="quarter != null">
				and datepart(quarter, customer_target.rebate_target_date)
				= #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, customer_target.rebate_target_date)
				&lt;=
				#{maxMonth}
			</if>
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			group by customer_target.distributor_id, datepart(year,
			customer_target.rebate_target_date),
			datepart(month,
			customer_target.rebate_target_date)
			) total_target_info
			on
			total_target_info.distributor_id = si.distributor_id and
			datepart(month, si.trans_time) = total_target_info.month
			and
			year(si.trans_time) = total_target_info.year
			where 1 = 1
			<if test="cai != null">
				and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
				or
				dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai =
				#{cai})
				<if test="salesChannel != null">
					and dwrsr.sales_channel_name = #{salesChannel}
				</if>
			</if>
			<if test="quarter != null">
				and datepart(quarter, trans_time) = #{quarter}
			</if>
			<if test="maxMonth != null">
				and datepart(month, trans_time) &lt;= #{maxMonth}
			</if>
			and datepart(year, trans_time) = #{year}
			/*channel
			manager角色，筛选sales_name_cn为NA的经销商*/
			AND si.sales_name_cn = 'NA'
			<if test="salesChannel != null">
				and si.sales_channel_name = #{salesChannel}
			</if>
			group by total_target_info.total_target
			) total_info
		</if>
	</select>
	<select id="queryTotalSellinStatsByPartnerId"
		resultMap="BaseResultMap"
		parameterType="com.chevron.qbr.model.StatsQueryParam">
		select
		<if test="partnerId != null">
			partner_id,enterprise_name,
		</if>
		sum(actual_value) actual_value,
		sum(target_value) target_value
		from (
		select round(isnull(sum(liters), 0),2) actual_value,
		<if test="partnerId != null">
			poe.partner_id,poe.enterprise_name,
		</if>
		round(isnull(total_target_info.total_target,0),2) as target_value
		from
		[PP_MID].dbo.syn_dw_sap_sell_in si
		left join
		wx_t_partner_o2o_enterprise poe on poe.distributor_id =
		si.distributor_id
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
			ON
			dwrssr.distributor_id = poe.distributor_id
			LEFT JOIN
			[PP_MID].dbo.syn_dw_region dwrsr ON
			dwrsr.region_name =
			dwrssr.region_name
		</if>
		left join
		(select
		sum(customer_target.target_volume) total_target,
		customer_target.distributor_id,
		datepart(year,
		customer_target.rebate_target_date) year,
		datepart(month,
		customer_target.rebate_target_date) month
		from
		[PP_MID].dbo.syn_dw_baseline_target customer_target
		left join
		wx_t_partner_o2o_enterprise poe on poe.distributor_id =
		customer_target.distributor_id
		<if test="cai != null">
			LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr
			ON
			dwrssr.distributor_id = poe.distributor_id
			LEFT JOIN
			[PP_MID].dbo.syn_dw_region dwrsr ON
			dwrsr.region_name =
			dwrssr.region_name
		</if>
		where 1=1
		and poe.partner_id != 9
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = #{salesChannel}
			</if>
		</if>
		group by customer_target.distributor_id, datepart(year,
		customer_target.rebate_target_date),
		datepart(month,
		customer_target.rebate_target_date)
		) total_target_info
		on
		total_target_info.distributor_id = si.distributor_id
		and
		month(si.trans_time) = total_target_info.month
		and year(si.trans_time)
		= total_target_info.year
		where 1=1
		and trans_time between #{startDate,
		jdbcType=DATE} and #{endDate,
		jdbcType=TIMESTAMP}
		<if test="salesChannel == null">
			and si.sales_channel_name = #{salesChannel}
		</if>
		<if test="partnerId != null ">
			and poe.partner_id = #{partnerId, jdbcType=BIGINT}
		</if>
		<if test="cai != null">
			and (dwrssr.sales_cai = #{cai} or dwrssr.suppervisor_cai = #{cai}
			or
			dwrsr.channel_manager_cai = #{cai} or dwrsr.bu_manager_cai = #{cai})
			<if test="salesChannel != null">
				and dwrsr.sales_channel_name = #{salesChannel}
			</if>
		</if>
		group by
		<if test="partnerId != null">
			poe.partner_id,poe.enterprise_name,
		</if>
		total_target_info.total_target
		) aa
		<if test="partnerId != null">
			group by partner_id,enterprise_name
		</if>
	</select>
</mapper>