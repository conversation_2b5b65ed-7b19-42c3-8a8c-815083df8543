<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialWarehouseOutDetailVoMapper" >
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialWarehouseOutDetailVo" >
	<result column="WAREHOUSE_OUT_ID" property="warehouseOutId" jdbcType="BIGINT" />
	<result column="MATERIAL_ID" property="materialId" jdbcType="BIGINT" />
	<result column="MATERIAL_SKU_CODE" property="materialSkuCode" jdbcType="NVARCHAR" />
	<result column="APPLICATION_QTY" property="applicationQty" jdbcType="BIGINT" />
	<result column="MATERIAL_PRICE" property="materialPrice" jdbcType="NUMERIC" />
	<result column="WAREHOUSE_ID" property="warehouseId" jdbcType="BIGINT" />
	<result column="COMMENTS" property="comments" jdbcType="NVARCHAR" />
	<result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
	<result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
	<result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
	<result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
	<result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
	<result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
	<result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
	<result column="WAREHOUSE_NAME" jdbcType="BIGINT" property="warehouseName" />

	<!--  附带物料信息 -->
	<result column="MATERIAL_CODE" property="materialCode" jdbcType="NVARCHAR" />
	<result column="SMC_MATERIAL_CODE" property="smcMaterialCode" jdbcType="NVARCHAR" />
	<result column="MATERIAL_TYPE" property="materialType" jdbcType="NVARCHAR" />
	<result column="MATERIAL_TYPE_NAME" property="materialTypeName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_NAME" property="materialName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_DESC" property="materialDesc" jdbcType="NVARCHAR" />
	<result column="MATERIAL_IMAGES" property="materialImages" jdbcType="BIGINT" />
	<result column="MATERIAL_PRICE_NOW" property="materialPriceNow" jdbcType="NUMERIC" />
	<result column="MATERIAL_UNIT" property="materialUnit" jdbcType="NVARCHAR" />
	<result column="MATERIAL_SOURCE" property="materialSource" jdbcType="NVARCHAR" />
	<result column="MATERIAL_CATEGORY" property="materialCategory" jdbcType="NVARCHAR" />
	<result column="OVER_LIMIT" property="overLimit" jdbcType="BIT" />
	<result column="IS_LEFTOVER" property="isLeftover" jdbcType="BIT" />
	<result column="IS_PRESALE" property="isPresale" jdbcType="BIT" />
	<result column="SHOW_STOCK_QTY" property="showStockQty" jdbcType="BIT" />
	<result column="POINT_MODE" property="pointMode" jdbcType="BIT" />
	<!-- 库存信息  -->
	<result column="STOCK_QTY" property="stockQty" jdbcType="NVARCHAR" />
	<result column="VIRTUAL_STOCK_QTY" property="virtualStockQty" jdbcType="NVARCHAR" />
</resultMap>
<sql id="Example_Where_Clause" >
	<where >
	<foreach collection="oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause" >
	<where >
	<foreach collection="example.oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List" >
	WAREHOUSE_OUT_ID, MATERIAL_ID, MATERIAL_SKU_CODE, APPLICATION_QTY, MATERIAL_PRICE,
	WAREHOUSE_ID, COMMENTS, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY
</sql>
<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialWarehouseOutDetailVoExample" >
	select
	<if test="distinct" >
	distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_warehouse_out_detail
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null" >
	order by ${orderByClause}
	</if>
</select>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialWarehouseOutDetailVoExample" >
	delete from wx_t_material_warehouse_out_detail
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialWarehouseOutDetailVo" >
	insert into wx_t_material_warehouse_out_detail (WAREHOUSE_OUT_ID, MATERIAL_ID, MATERIAL_SKU_CODE,
	APPLICATION_QTY, MATERIAL_PRICE, WAREHOUSE_ID,
	COMMENTS, DELETE_FLAG, ATTRIBUTE1,
	ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY)
	values (#{warehouseOutId,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR},
	#{applicationQty,jdbcType=BIGINT}, #{materialPrice,jdbcType=NUMERIC}, #{warehouseId,jdbcType=BIGINT},
	#{comments,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR},
	#{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT},
	#{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialWarehouseOutDetailVo" >
	insert into wx_t_material_warehouse_out_detail
	<trim prefix="(" suffix=")" suffixOverrides="," >
	<if test="warehouseOutId != null" >
		WAREHOUSE_OUT_ID,
	</if>
	<if test="materialId != null" >
		MATERIAL_ID,
	</if>
	<if test="materialSkuCode != null" >
		MATERIAL_SKU_CODE,
	</if>
	<if test="applicationQty != null" >
		APPLICATION_QTY,
	</if>
	<if test="materialPrice != null" >
		MATERIAL_PRICE,
	</if>
	<if test="warehouseId != null" >
		WAREHOUSE_ID,
	</if>
	<if test="comments != null" >
		COMMENTS,
	</if>
	<if test="deleteFlag != null" >
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null" >
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null" >
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null" >
		CREATION_TIME,
	</if>
	<if test="createdBy != null" >
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null" >
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null" >
		LAST_UPDATED_BY,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides="," >
	<if test="warehouseOutId != null" >
		#{warehouseOutId,jdbcType=BIGINT},
	</if>
	<if test="materialId != null" >
		#{materialId,jdbcType=BIGINT},
	</if>
	<if test="materialSkuCode != null" >
		#{materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="applicationQty != null" >
		#{applicationQty,jdbcType=BIGINT},
	</if>
	<if test="materialPrice != null" >
		#{materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="warehouseId != null" >
		#{warehouseId,jdbcType=BIGINT},
	</if>
	<if test="comments != null" >
		#{comments,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null" >
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null" >
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null" >
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null" >
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null" >
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null" >
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null" >
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map" >
	update wx_t_material_warehouse_out_detail
	<set >
	<if test="record.warehouseOutId != null" >
		WAREHOUSE_OUT_ID = #{record.warehouseOutId,jdbcType=BIGINT},
	</if>
	<if test="record.materialId != null" >
		MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	</if>
	<if test="record.materialSkuCode != null" >
		MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.applicationQty != null" >
		APPLICATION_QTY = #{record.applicationQty,jdbcType=BIGINT},
	</if>
	<if test="record.materialPrice != null" >
		MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="record.warehouseId != null" >
		WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
	</if>
	<if test="record.comments != null" >
		COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	</if>
	<if test="record.deleteFlag != null" >
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null" >
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null" >
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null" >
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null" >
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null" >
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null" >
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map" >
	update wx_t_material_warehouse_out_detail
	set WAREHOUSE_OUT_ID = #{record.warehouseOutId,jdbcType=BIGINT},
	MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	APPLICATION_QTY = #{record.applicationQty,jdbcType=BIGINT},
	MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
	COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>

<!-- 查询出库单行信息, 附带物料信息 -->
<select id="selectWithMaterialInfo" resultMap="BaseResultMap" parameterType="java.lang.Long" >
	select
	mad.*, mw.WAREHOUSE_NAME,
	m.MATERIAL_CODE, m.SMC_MATERIAL_CODE, m.MATERIAL_TYPE, m.MATERIAL_NAME, m.MATERIAL_DESC, m.MATERIAL_SOURCE, m.MATERIAL_CATEGORY,m.OVER_LIMIT,
	m.IS_LEFTOVER,m.IS_PRESALE,m.SHOW_STOCK_QTY,m.POINT_MODE,
	MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
	m.MATERIAL_PRICE MATERIAL_PRICE_NOW, m.MATERIAL_UNIT,
	di.DIC_ITEM_NAME MATERIAL_TYPE_NAME,
	STOCK_QTY = (select mi.STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID),
	VIRTUAL_STOCK_QTY = (select mi.VIRTUAL_STOCK_QTY from wx_t_material_inventory mi where mi.MATERIAL_ID=mad.MATERIAL_ID and mi.MATERIAL_SKU_CODE = mad.MATERIAL_SKU_CODE and mi.WAREHOUSE_ID = mad.WAREHOUSE_ID)
	from wx_t_material_warehouse_out_detail mad 
	left join wx_t_material_warehouse_out mw1 on mw1.id=mad.WAREHOUSE_OUT_ID
	left join wx_t_material_application ma1 on ma1.id=mw1.APPLICATION_ID
	left join wx_t_material m on m.ID = mad.MATERIAL_ID and mad.WAREHOUSE_OUT_ID = #{warehouseOutId,jdbcType=BIGINT}
	left join wx_t_material_warehouse mw on mad.warehouse_id = mw.id
	left join wx_t_dic_item di on di.DIC_TYPE_CODE = 'material.type' and di.DIC_ITEM_CODE = m.MATERIAL_TYPE
	where mad.DELETE_FLAG = 0 and ma1.version_no=0
	and mad.WAREHOUSE_OUT_ID = #{warehouseOutId,jdbcType=BIGINT}
	
	union all 
		select
	mad.*, s1.supplier_name WAREHOUSE_NAME,
	m1.MATERIAL_CODE, null SMC_MATERIAL_CODE, md1.material_tag MATERIAL_TYPE, md1.MATERIAL_NAME, md1.MATERIAL_DESC, 
	convert(nvarchar(20),m1.supplier_id) MATERIAL_SOURCE, null MATERIAL_CATEGORY, null OVER_LIMIT,
	0 IS_LEFTOVER,0 IS_PRESALE,null SHOW_STOCK_QTY,1 POINT_MODE,
		convert(varchar(100),(select top 1 af.ATT_ID from  WX_ATT_FILE af where af.SOURCE_ID = m1.id and af.SOURCE_TYPE = '58')) MATERIAL_IMAGES,
	pm1.point_value MATERIAL_PRICE_NOW, md1.MATERIAL_UNIT,
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) MATERIAL_TYPE_NAME,
	i1.current_quantity STOCK_QTY,	i1.current_quantity VIRTUAL_STOCK_QTY
	from wx_t_material_warehouse_out_detail mad 
	left join wx_t_material_warehouse_out mw1 on mw1.id=mad.WAREHOUSE_OUT_ID
	left join wx_t_material_application ma1 on ma1.id=mw1.APPLICATION_ID
	left join wx_t_point_material pm1 on pm1.id=mad.MATERIAL_ID
		  left join wx_t_material2021 m1 on pm1.material_id=m1.id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
	where mad.DELETE_FLAG = 0 and ma1.version_no>=202109
	and mad.WAREHOUSE_OUT_ID = #{warehouseOutId,jdbcType=BIGINT}
</select>
</mapper>