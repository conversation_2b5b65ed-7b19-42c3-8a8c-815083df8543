<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialInventoryLogVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialInventoryLogVo">
    <result column="MATERIAL_ID" jdbcType="BIGINT" property="materialId" />
    <result column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
    <result column="CHANGE_QTY" jdbcType="BIGINT" property="changeQty" />
    <result column="STOCK_TYPE" jdbcType="NVARCHAR" property="stockType" />
    <result column="APPLICATION_CODE" jdbcType="NVARCHAR" property="applicationCode" />
    <result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
    <result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
    <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
    <result column="WAREHOUSE_ID" jdbcType="BIGINT" property="warehouseId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    MATERIAL_ID, MATERIAL_SKU_CODE, CHANGE_QTY, STOCK_TYPE, APPLICATION_CODE, DELETE_FLAG, 
    ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY, 
    WAREHOUSE_ID
  </sql>
  <select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialInventoryLogVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_inventory_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialInventoryLogVoExample">
    delete from wx_t_material_inventory_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialInventoryLogVo">
    insert into wx_t_material_inventory_log (MATERIAL_ID, MATERIAL_SKU_CODE, CHANGE_QTY, 
      STOCK_TYPE, APPLICATION_CODE, DELETE_FLAG, 
      ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
      CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY, 
      WAREHOUSE_ID)
    values (#{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR}, #{changeQty,jdbcType=BIGINT}, 
      #{stockType,jdbcType=NVARCHAR}, #{applicationCode,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, 
      #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}, 
      #{warehouseId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialInventoryLogVo">
    insert into wx_t_material_inventory_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        MATERIAL_ID,
      </if>
      <if test="materialSkuCode != null">
        MATERIAL_SKU_CODE,
      </if>
      <if test="changeQty != null">
        CHANGE_QTY,
      </if>
      <if test="stockType != null">
        STOCK_TYPE,
      </if>
      <if test="applicationCode != null">
        APPLICATION_CODE,
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null">
        CREATION_TIME,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
      <if test="warehouseId != null">
        WAREHOUSE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialSkuCode != null">
        #{materialSkuCode,jdbcType=NVARCHAR},
      </if>
      <if test="changeQty != null">
        #{changeQty,jdbcType=BIGINT},
      </if>
      <if test="stockType != null">
        #{stockType,jdbcType=NVARCHAR},
      </if>
      <if test="applicationCode != null">
        #{applicationCode,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_material_inventory_log
    <set>
      <if test="record.materialId != null">
        MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.materialSkuCode != null">
        MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.changeQty != null">
        CHANGE_QTY = #{record.changeQty,jdbcType=BIGINT},
      </if>
      <if test="record.stockType != null">
        STOCK_TYPE = #{record.stockType,jdbcType=NVARCHAR},
      </if>
      <if test="record.applicationCode != null">
        APPLICATION_CODE = #{record.applicationCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null">
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null">
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null">
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseId != null">
        WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_material_inventory_log
    set MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
      MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
      CHANGE_QTY = #{record.changeQty,jdbcType=BIGINT},
      STOCK_TYPE = #{record.stockType,jdbcType=NVARCHAR},
      APPLICATION_CODE = #{record.applicationCode,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <insert id="insertWithMaterialCode" parameterType="com.chevron.material.model.WXTMaterialInventoryLogVo">
	insert into wx_t_material_inventory_log (MATERIAL_ID, MATERIAL_SKU_CODE, CHANGE_QTY, STOCK_TYPE,
	APPLICATION_CODE, DELETE_FLAG, ATTRIBUTE1,
	ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY,WAREHOUSE_ID)
	values ((select id from wx_t_material where material_code = #{materialCode,jdbcType=NVARCHAR}),#{materialSkuCode,jdbcType=NVARCHAR}, #{changeQty,jdbcType=BIGINT}, #{stockType,jdbcType=NVARCHAR},
	#{applicationCode,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR},
	#{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT},
	#{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT},#{warehouseId,jdbcType=BIGINT})
</insert>
</mapper>