<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.MaterialShippingAddressMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.material.model.MaterialShippingAddress">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="partner_id" property="partnerId" jdbcType="VARCHAR"/>
		<result column="user_id" property="userId" jdbcType="BIGINT"/>
		<result column="province_code" property="provinceCode" jdbcType="VARCHAR"/>
		<result column="city_code" property="cityCode" jdbcType="VARCHAR"/>
		<result column="dist_code" property="distCode" jdbcType="VARCHAR"/>
		<result column="address" property="address" jdbcType="VARCHAR"/>
		<result column="postcode" property="postcode" jdbcType="VARCHAR"/>
		<result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
		<result column="telephone" property="telephone" jdbcType="VARCHAR"/>
		<result column="mobile" property="mobile" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="dist" property="dist" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,partner_id,user_id,source_id,source_type,province_code,city_code,dist_code,address,postcode,contact_person,telephone,mobile,remark,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.material.model.MaterialShippingAddress">
		update wx_t_material_shipping_address set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.MaterialShippingAddress">
		update wx_t_material_shipping_address
		<set>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=VARCHAR},
			</if>
			<if test="userId != null" >
				user_id = #{userId,jdbcType=BIGINT},
			</if>
            <if test="sourceId != null" >
                source_id = #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="sourceType != null" >
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
			<if test="provinceCode != null" >
				province_code = #{provinceCode,jdbcType=VARCHAR},
			</if>
			<if test="cityCode != null" >
				city_code = #{cityCode,jdbcType=VARCHAR},
			</if>
			<if test="distCode != null" >
				dist_code = #{distCode,jdbcType=VARCHAR},
			</if>
			<if test="address != null" >
				address = #{address,jdbcType=VARCHAR},
			</if>
			<if test="postcode != null" >
				postcode = #{postcode,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null" >
				contact_person = #{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="telephone != null" >
				telephone = #{telephone,jdbcType=VARCHAR},
			</if>
			<if test="mobile != null" >
				mobile = #{mobile,jdbcType=VARCHAR},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.material.model.MaterialShippingAddressExample">
    	delete from wx_t_material_shipping_address
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.material.model.MaterialShippingAddress" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_material_shipping_address
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="userId != null">
				user_id,
			</if>
            <if test="sourceId != null" >
                source_id,
            </if>
            <if test="sourceType != null" >
                source_type,
            </if>
			<if test="provinceCode != null">
				province_code,
			</if>
			<if test="cityCode != null">
				city_code,
			</if>
			<if test="distCode != null">
				dist_code,
			</if>
			<if test="address != null">
				address,
			</if>
			<if test="postcode != null">
				postcode,
			</if>
			<if test="contactPerson != null">
				contact_person,
			</if>
			<if test="telephone != null">
				telephone,
			</if>
			<if test="mobile != null">
				mobile,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="partnerId != null">
				#{partnerId,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=BIGINT},
			</if>
            <if test="sourceId != null" >
                #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="sourceType != null" >
                #{sourceType,jdbcType=VARCHAR},
            </if>
			<if test="provinceCode != null">
				#{provinceCode,jdbcType=VARCHAR},
			</if>
			<if test="cityCode != null">
				#{cityCode,jdbcType=VARCHAR},
			</if>
			<if test="distCode != null">
				#{distCode,jdbcType=VARCHAR},
			</if>
			<if test="address != null">
				#{address,jdbcType=VARCHAR},
			</if>
			<if test="postcode != null">
				#{postcode,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null">
				#{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="telephone != null">
				#{telephone,jdbcType=VARCHAR},
			</if>
			<if test="mobile != null">
				#{mobile,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_material_shipping_address
		<set>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=VARCHAR},
			</if>
			<if test="record.userId != null">
				user_id = #{record.userId,jdbcType=BIGINT},
			</if>
            <if test="record.sourceId != null" >
                source_id = #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="record.sourceType != null" >
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
			<if test="record.provinceCode != null">
				province_code = #{record.provinceCode,jdbcType=VARCHAR},
			</if>
			<if test="record.cityCode != null">
				city_code = #{record.cityCode,jdbcType=VARCHAR},
			</if>
			<if test="record.distCode != null">
				dist_code = #{record.distCode,jdbcType=VARCHAR},
			</if>
			<if test="record.address != null">
				address = #{record.address,jdbcType=VARCHAR},
			</if>
			<if test="record.postcode != null">
				postcode = #{record.postcode,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPerson != null">
				contact_person = #{record.contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.telephone != null">
				telephone = #{record.telephone,jdbcType=VARCHAR},
			</if>
			<if test="record.mobile != null">
				mobile = #{record.mobile,jdbcType=VARCHAR},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.material.model.MaterialShippingAddressExample">
		delete from wx_t_material_shipping_address
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.material.model.MaterialShippingAddressExample" resultType="int">
		select count(1) from wx_t_material_shipping_address
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.MaterialShippingAddressExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,a.region_name as province,b.region_name as city,c.region_name as dist,
        t1.id,t1.partner_id,t1.user_id,t1.source_id,t1.source_type,t1.province_code,t1.city_code,t1.dist_code,t1.address,t1.postcode,t1.contact_person,t1.telephone,t1.mobile,t1.remark,
        t1.create_user_id,t1.create_time,t1.update_user_id,t1.update_time
		from wx_t_material_shipping_address t1
		left join wx_t_region a on t1.province_code = a.region_code
        left join wx_t_region b on t1.city_code = b.region_code
        left join wx_t_region c on t1.dist_code = c.region_code
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.material.model.MaterialShippingAddressExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_material_shipping_address
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.partner_id, t1.user_id, t1.source_id,t1.source_type, t1.province_code, t1.city_code, t1.dist_code, t1.address, t1.postcode,
			 t1.contact_person, t1.telephone, t1.mobile, t1.remark, t1.create_user_id, t1.create_time, t1.update_user_id,
			 t1.update_time
		  from wx_t_material_shipping_address t1
		 where 1=1
	</select>
</mapper>
