<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialAddressConfigVoMapper" >
    <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialAddressConfigVo">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="PARTNER_ID" property="partnerId" jdbcType="BIGINT"/>
        <result column="CONTACTS" property="contacts" jdbcType="NVARCHAR"/>
        <result column="CONTACT_NUMBER" property="contactNumber" jdbcType="NVARCHAR"/>
        <result column="ADDRESS_REGION" property="addressRegion" jdbcType="NVARCHAR"/>
        <result column="ADDRESS_DETAIL" property="addressDetail" jdbcType="NVARCHAR"/>
        <result column="STATUS" property="status" jdbcType="NVARCHAR"/>
        <result column="TYPE" property="type" jdbcType="NVARCHAR"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT"/>
        <result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR"/>
        <result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR"/>
        <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT"/>
        <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT"/>
        <result column="province_name" property="provinceName" jdbcType="NVARCHAR"/>
        <result column="city_name" property="cityName" jdbcType="NVARCHAR"/>
        <result column="dist_name" property="distName" jdbcType="NVARCHAR"/>
        <result column="province" property="province" jdbcType="BIGINT"/>
        <result column="city" property="city" jdbcType="BIGINT"/>
        <result column="dist" property="dist" jdbcType="BIGINT"/>
    </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

    <sql id="Query_Example_Where_Clause">
        <where>
            (attribute1 is null or attribute1 = '') and
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, PARTNER_ID, CONTACTS, CONTACT_NUMBER, ADDRESS_REGION, ADDRESS_DETAIL, STATUS, 
    TYPE, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
    LAST_UPDATED_BY,ADDRESS_REGION as dist
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialAddressConfigVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_address_config
    <if test="_parameter != null" >
      <include refid="Query_Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
    <select id="selectByParams" parameterType="com.chevron.pms.model.BaseParams" resultMap="BaseResultMap">
        select t.*,d.region_name as dist_name,c.id as city, c.region_name as city_name,p.id as province,t.ADDRESS_REGION as dist,
        p.region_name as province_name from wx_t_material_address_config t
        left join wx_t_region d on d.id = t.ADDRESS_REGION
        left join wx_t_region c on c.id = d.parent_id
        left join wx_t_region p on p.id = c.parent_id
        <where>
            (t.attribute1 is null or t.attribute1 = '')
            <if test="partnerId != null">
                and t.partner_id = #{partnerId}
            </if>
            <if test="addressId != null">
                and t.id = #{addressId}
            </if>
        </where>
    </select>
    <select id="selectWorkShopList" parameterType="map" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.work_shop_name ADDRESS_REGION,
        work_shop_address ADDRESS_DETAIL,
        contact_person CONTACTS,
        contact_person_tel CONTACT_NUMBER,
        t1.type TYPE,
        wsf.used_flag STATUS
        FROM
        wx_t_work_shop t1
        JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = t1.id
        JOIN wx_t_organization o ON wp1.partner_id = o.id
        LEFT JOIN wx_t_work_shop_flag wsf ON t1.id = wsf.workshop_id
        WHERE
        o.status = 1
        AND t1.status = 3
        <if test="partnerId != null and partnerId != '' " >
            AND wp1.partner_id = #{partnerId,jdbcType=BIGINT}
        </if>
        <if test="workShopName != null and workShopName != ''" >
            and t1.work_shop_name like '%' + #{workShopName,jdbcType=VARCHAR}+ '%'
        </if>
    </select>
    <select id="findWorkShopList" parameterType="map" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.work_shop_name ADDRESS_REGION,
        work_shop_address ADDRESS_DETAIL,
        contact_person CONTACTS,
        contact_person_tel CONTACT_NUMBER,
        t1.type TYPE
        <if test="onceFlag != null and onceFlag == true">
            ,wsf.used_flag STATUS
        </if>
        FROM
        wx_t_work_shop t1
        JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = t1.id
        JOIN wx_t_organization o ON wp1.partner_id = o.id
        LEFT JOIN wx_t_work_shop_flag wsf ON t1.id = wsf.workshop_id
        WHERE
        o.status = 1 and t1.customer_type=1 and (t1.business_weight | (t1.from_source&amp;3))&amp;1>0
        and t1.delete_flag != 1
        <if test="partnerId != null and partnerId != '' " >
            AND wp1.partner_id = #{partnerId,jdbcType=BIGINT}
        </if>
        <if test="workShopName != null and workShopName != ''" >
            and t1.work_shop_name like '%' + #{workShopName,jdbcType=VARCHAR}+ '%'
        </if>
        <choose>
            <when test="potential != null and potential">
                AND t1.status != -3
            </when>
            <otherwise>
                AND t1.status = 3
            </otherwise>
        </choose>
    </select>

  <select id="selectWorkShop" parameterType="map" resultMap="BaseResultMap">
  	SELECT
  		t1.id,
		t1.work_shop_name ADDRESS_REGION,
		work_shop_address ADDRESS_DETAIL,
		contact_person CONTACTS,
		contact_person_tel CONTACT_NUMBER,
		t1.type TYPE,
		wsf.used_flag STATUS
	FROM
	    wx_t_work_shop_flag wsf
		left join wx_t_work_shop t1 ON t1.id = wsf.workshop_id
		JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = t1.id
		JOIN wx_t_organization o ON wp1.partner_id = o.id
	WHERE 1=1
	  <if test="applicationId != null and applicationId != '' " >
       	and wsf.application_id = #{applicationId,jdbcType=BIGINT}
      </if>
  </select>

    <select id="selectWorkShopByShopId" resultMap="BaseResultMap">
        SELECT
        t1.id,
        t1.work_shop_name ADDRESS_REGION,
        t1.work_shop_address ADDRESS_DETAIL,
        t1.contact_person CONTACTS,
        contact_person_tel CONTACT_NUMBER,
        t1.type TYPE
        FROM
        wx_t_work_shop t1
        WHERE 1=1
        <if test="workShopId != null">
            and t1.id = #{workShopId,jdbcType=BIGINT}
        </if>
    </select>
  <insert id="insertWorkshopFlag">
    insert into wx_t_work_shop_flag (APPLICATION_ID, WORKSHOP_ID, USED_FLAG)
    values (#{applicationId}, ${workshopId}, ${usedFlag})
  </insert>
  
   <insert id="updateWorkshopFlag">
    update wx_t_work_shop_flag set WORKSHOP_ID = ${workshopId}, USED_FLAG = ${usedFlag}
    where APPLICATION_ID = ${applicationId}
  </insert>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialAddressConfigVoExample" >
    delete from wx_t_material_address_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialAddressConfigVo" >
    insert into wx_t_material_address_config (ID, PARTNER_ID, CONTACTS, 
      CONTACT_NUMBER, ADDRESS_REGION, ADDRESS_DETAIL, 
      STATUS, TYPE, DELETE_FLAG, 
      ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
      CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
      )
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{contacts,jdbcType=NVARCHAR}, 
      #{contactNumber,jdbcType=NVARCHAR}, #{addressRegion,jdbcType=NVARCHAR}, #{addressDetail,jdbcType=NVARCHAR}, 
      #{status,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, 
      #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialAddressConfigVo" >
    insert into wx_t_material_address_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="partnerId != null" >
        PARTNER_ID,
      </if>
      <if test="contacts != null" >
        CONTACTS,
      </if>
      <if test="contactNumber != null" >
        CONTACT_NUMBER,
      </if>
      <if test="addressRegion != null" >
        ADDRESS_REGION,
      </if>
      <if test="addressDetail != null" >
        ADDRESS_DETAIL,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="contacts != null" >
        #{contacts,jdbcType=NVARCHAR},
      </if>
      <if test="contactNumber != null" >
        #{contactNumber,jdbcType=NVARCHAR},
      </if>
      <if test="addressRegion != null" >
        #{addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="addressDetail != null" >
        #{addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_material_address_config
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        PARTNER_ID = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.contacts != null" >
        CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactNumber != null" >
        CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressRegion != null" >
        ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressDetail != null" >
        ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="record.status != null" >
        STATUS = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null" >
        TYPE = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_material_address_config
    set ID = #{record.id,jdbcType=BIGINT},
      PARTNER_ID = #{record.partnerId,jdbcType=BIGINT},
      CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
      CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
      ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      STATUS = #{record.status,jdbcType=NVARCHAR},
      TYPE = #{record.type,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

    <select id="selectPointTypeAddress" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wx_t_material_address_config
        <where>
            and delete_flag = 0
            and partner_id = #{partnerId}
            <if test="pointType == null">
                and (ATTRIBUTE1 is null or ATTRIBUTE1 = '')
            </if>
            <if test="pointType != null">
                and (ATTRIBUTE1 is null or ATTRIBUTE1 = '' or ATTRIBUTE1 = #{pointType})
            </if>
        </where>
    </select>
</mapper>