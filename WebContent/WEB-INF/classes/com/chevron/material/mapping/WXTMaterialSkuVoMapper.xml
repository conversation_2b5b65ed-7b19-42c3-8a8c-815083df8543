<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialSkuVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialSkuVo">
	<id column="MATERIAL_ID" jdbcType="BIGINT" property="materialId" />
	<id column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
	<result column="MATERIAL_SKU_PROP_COLOR" jdbcType="NVARCHAR" property="materialSkuPropColor" />
	<result column="MATERIAL_SKU_PROP_SIZE" jdbcType="NVARCHAR" property="materialSkuPropSize" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
	<result column="LIMIT_QTY" jdbcType="BIGINT" property="limitQty" />
	<result column="TRADE_QTY" jdbcType="BIGINT" property="tradeQty" />
</resultMap>
<sql id="Example_Where_Clause">
	<where>
	  <foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		  <trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			  <choose>
				<when test="criterion.noValue">
				  and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				  and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				  and ${criterion.condition}
				  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				  </foreach>
				</when>
			  </choose>
			</foreach>
		  </trim>
		</if>
	  </foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	  <foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		  <trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			  <choose>
				<when test="criterion.noValue">
				  and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				  and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				  and ${criterion.condition}
				  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				  </foreach>
				</when>
			  </choose>
			</foreach>
		  </trim>
		</if>
	  </foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	MATERIAL_ID, MATERIAL_SKU_CODE, MATERIAL_SKU_PROP_COLOR, MATERIAL_SKU_PROP_SIZE,
	DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME,
	LAST_UPDATED_BY, LIMIT_QTY, TRADE_QTY
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialSkuVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	  distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_sku
	<if test="_parameter != null">
	  <include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	  order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialSkuVoKey" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_material_sku
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	  and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</select>
<delete id="deleteByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialSkuVoKey">
	delete from wx_t_material_sku
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	  and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialSkuVoExample">
	delete from wx_t_material_sku
	<if test="_parameter != null">
	  <include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialSkuVo">
	insert into wx_t_material_sku (MATERIAL_ID, MATERIAL_SKU_CODE, MATERIAL_SKU_PROP_COLOR,
	  MATERIAL_SKU_PROP_SIZE, DELETE_FLAG, ATTRIBUTE1,
	  ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	  LAST_UPDATE_TIME, LAST_UPDATED_BY, LIMIT_QTY,
	  TRADE_QTY)
	values (#{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR}, #{materialSkuPropColor,jdbcType=NVARCHAR},
	  #{materialSkuPropSize,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR},
	  #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT},
	  #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}, #{limitQty,jdbcType=BIGINT},
	  #{tradeQty,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialSkuVo">
	insert into wx_t_material_sku
	<trim prefix="(" suffix=")" suffixOverrides=",">
	  <if test="materialId != null">
		MATERIAL_ID,
	  </if>
	  <if test="materialSkuCode != null">
		MATERIAL_SKU_CODE,
	  </if>
	  <if test="materialSkuPropColor != null">
		MATERIAL_SKU_PROP_COLOR,
	  </if>
	  <if test="materialSkuPropSize != null">
		MATERIAL_SKU_PROP_SIZE,
	  </if>
	  <if test="deleteFlag != null">
		DELETE_FLAG,
	  </if>
	  <if test="attribute1 != null">
		ATTRIBUTE1,
	  </if>
	  <if test="attribute2 != null">
		ATTRIBUTE2,
	  </if>
	  <if test="creationTime != null">
		CREATION_TIME,
	  </if>
	  <if test="createdBy != null">
		CREATED_BY,
	  </if>
	  <if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	  </if>
	  <if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	  </if>
	  <if test="limitQty != null">
		LIMIT_QTY,
	  </if>
	  <if test="tradeQty != null">
		TRADE_QTY,
	  </if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	  <if test="materialId != null">
		#{materialId,jdbcType=BIGINT},
	  </if>
	  <if test="materialSkuCode != null">
		#{materialSkuCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="materialSkuPropColor != null">
		#{materialSkuPropColor,jdbcType=NVARCHAR},
	  </if>
	  <if test="materialSkuPropSize != null">
		#{materialSkuPropSize,jdbcType=NVARCHAR},
	  </if>
	  <if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	  </if>
	  <if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	  </if>
	  <if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	  </if>
	  <if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	  </if>
	  <if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	  </if>
	  <if test="limitQty != null">
		#{limitQty,jdbcType=BIGINT},
	  </if>
	  <if test="tradeQty != null">
		#{tradeQty,jdbcType=BIGINT},
	  </if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material_sku
	<set>
	  <if test="record.materialId != null">
		MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	  </if>
	  <if test="record.materialSkuCode != null">
		MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.materialSkuPropColor != null">
		MATERIAL_SKU_PROP_COLOR = #{record.materialSkuPropColor,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.materialSkuPropSize != null">
		MATERIAL_SKU_PROP_SIZE = #{record.materialSkuPropSize,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	  </if>
	  <if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	  </if>
	  <if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	  </if>
	  <if test="record.limitQty != null">
		LIMIT_QTY = #{record.limitQty,jdbcType=BIGINT},
	  </if>
	  <if test="record.tradeQty != null">
		TRADE_QTY = #{record.tradeQty,jdbcType=BIGINT},
	  </if>
	</set>
	<if test="_parameter != null">
	  <include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material_sku
	set MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	  MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	  MATERIAL_SKU_PROP_COLOR = #{record.materialSkuPropColor,jdbcType=NVARCHAR},
	  MATERIAL_SKU_PROP_SIZE = #{record.materialSkuPropSize,jdbcType=NVARCHAR},
	  DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	  ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	  ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	  CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	  CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	  LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	  LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	  LIMIT_QTY = #{record.limitQty,jdbcType=BIGINT},
	  TRADE_QTY = #{record.tradeQty,jdbcType=BIGINT}
	<if test="_parameter != null">
	  <include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialSkuVo">
	update wx_t_material_sku
	<set>
	  <if test="materialSkuPropColor != null">
		MATERIAL_SKU_PROP_COLOR = #{materialSkuPropColor,jdbcType=NVARCHAR},
	  </if>
	  <if test="materialSkuPropSize != null">
		MATERIAL_SKU_PROP_SIZE = #{materialSkuPropSize,jdbcType=NVARCHAR},
	  </if>
	  <if test="deleteFlag != null">
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	  </if>
	  <if test="attribute1 != null">
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	  </if>
	  <if test="attribute2 != null">
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	  </if>
	  <if test="creationTime != null">
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="createdBy != null">
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	  </if>
	  <if test="lastUpdateTime != null">
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="lastUpdatedBy != null">
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	  </if>
	  <if test="limitQty != null">
		LIMIT_QTY = #{limitQty,jdbcType=BIGINT},
	  </if>
	  <if test="tradeQty != null">
		TRADE_QTY = #{tradeQty,jdbcType=BIGINT},
	  </if>
	</set>
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	  and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialSkuVo">
	update wx_t_material_sku
	set MATERIAL_SKU_PROP_COLOR = #{materialSkuPropColor,jdbcType=NVARCHAR},
	  MATERIAL_SKU_PROP_SIZE = #{materialSkuPropSize,jdbcType=NVARCHAR},
	  DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	  ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	  ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	  CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	  CREATED_BY = #{createdBy,jdbcType=BIGINT},
	  LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	  LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	  LIMIT_QTY = #{limitQty,jdbcType=BIGINT},
	  TRADE_QTY = #{tradeQty,jdbcType=BIGINT}
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	  and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</update>
</mapper>