<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialInventoryVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialInventoryVo">
	<id column="MATERIAL_ID" jdbcType="BIGINT" property="materialId" />
	<id column="MATERIAL_CODE" jdbcType="NVARCHAR" property="materialCode" />
	<id column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
	<id column="WAREHOUSE_ID" jdbcType="BIGINT" property="warehouseId" />
	<result column="STOCK_QTY" jdbcType="BIGINT" property="stockQty" />
	<result column="VIRTUAL_STOCK_QTY" jdbcType="BIGINT" property="virtualStockQty" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
</resultMap>
<sql id="Example_Where_Clause">
	<where>
	<foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	<foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	MATERIAL_ID, MATERIAL_SKU_CODE, WAREHOUSE_ID, STOCK_QTY, VIRTUAL_STOCK_QTY, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialInventoryVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_inventory
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialInventoryVoKey" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_material_inventory
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialInventoryVoKey">
	delete from wx_t_material_inventory
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialInventoryVoExample">
	delete from wx_t_material_inventory
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	insert into wx_t_material_inventory (MATERIAL_ID, MATERIAL_SKU_CODE, WAREHOUSE_ID,
	STOCK_QTY, VIRTUAL_STOCK_QTY, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME,
	CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
	)
	values (#{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR}, #{warehouseId,jdbcType=BIGINT},
	#{stockQty,jdbcType=BIGINT}, #{virtualStockQty,jdbcType=BIGINT}, #{deleteFlag,jdbcType=BIT},
	#{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP},
	#{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}
	)
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	insert into wx_t_material_inventory
	<trim prefix="(" suffix=")" suffixOverrides=",">
	<if test="materialId != null">
		MATERIAL_ID,
	</if>
	<if test="materialSkuCode != null">
		MATERIAL_SKU_CODE,
	</if>
	<if test="warehouseId != null">
		WAREHOUSE_ID,
	</if>
	<if test="stockQty != null">
		STOCK_QTY,
	</if>
	<if test="virtualStockQty != null">
		VIRTUAL_STOCK_QTY,
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null">
		CREATION_TIME,
	</if>
	<if test="createdBy != null">
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	<if test="materialId != null">
		#{materialId,jdbcType=BIGINT},
	</if>
	<if test="materialSkuCode != null">
		#{materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="warehouseId != null">
		#{warehouseId,jdbcType=BIGINT},
	</if>
	<if test="stockQty != null">
		#{stockQty,jdbcType=BIGINT},
	</if>
	<if test="virtualStockQty != null">
		#{virtualStockQty,jdbcType=BIGINT},
	</if>
	<if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material_inventory
	<set>
	<if test="record.materialId != null">
		MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	</if>
	<if test="record.materialSkuCode != null">
		MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.warehouseId != null">
		WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
	</if>
	<if test="record.stockQty != null">
		STOCK_QTY = #{record.stockQty,jdbcType=BIGINT},
	</if>
	<if test="record.virtualStockQty != null">
		VIRTUAL_STOCK_QTY = #{record.virtualStockQty,jdbcType=BIGINT},
	</if>
	<if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material_inventory
	set MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
	MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
	WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
	STOCK_QTY = #{record.stockQty,jdbcType=BIGINT},
	VIRTUAL_STOCK_QTY = #{record.virtualStockQty,jdbcType=BIGINT},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	update wx_t_material_inventory
	<set>
	<if test="stockQty != null">
		STOCK_QTY = #{stockQty,jdbcType=BIGINT},
	</if>
	<if test="virtualStockQty != null">
		VIRTUAL_STOCK_QTY = #{virtualStockQty,jdbcType=BIGINT},
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	update wx_t_material_inventory
	set STOCK_QTY = #{stockQty,jdbcType=BIGINT},
	VIRTUAL_STOCK_QTY = #{virtualStockQty,jdbcType=BIGINT},
	DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</update>
<update id="updateRealInventory" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	update wx_t_material_inventory
	set STOCK_QTY = STOCK_QTY + #{stockQty,jdbcType=BIGINT},
	<if test="virtualStockQty != null">
        VIRTUAL_STOCK_QTY = VIRTUAL_STOCK_QTY + #{virtualStockQty,jdbcType=BIGINT},
    </if>
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
	where 1=1
	<choose>
	<when test="materialCode != null">
	and MATERIAL_ID = (select m.ID from wx_t_material m where m.MATERIAL_CODE = #{materialCode,jdbcType=NVARCHAR})
	</when>
	<otherwise>
	and MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	</otherwise>
	</choose>
	<if test="materialSkuCode != null">
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
	</if>
</update>
<update id="updateVirtualInventory" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	update wx_t_material_inventory
	set VIRTUAL_STOCK_QTY =  VIRTUAL_STOCK_QTY + #{virtualStockQty,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
	where MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	<if test="materialSkuCode != null">
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	</if>
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</update>
<update id="updateInventorySync" parameterType="com.chevron.material.model.WXTMaterialInventoryVo">
	update wx_t_material_inventory
	set STOCK_QTY = STOCK_QTY + #{stockQty,jdbcType=BIGINT},
	VIRTUAL_STOCK_QTY =  VIRTUAL_STOCK_QTY + #{virtualStockQty,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
	where MATERIAL_ID = (select m.ID from wx_t_material m where m.MATERIAL_CODE = #{materialCode,jdbcType=NVARCHAR})
	<if test="materialSkuCode != null">
	and MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	</if>
	and WAREHOUSE_ID = #{warehouseId,jdbcType=BIGINT}
</update>
<select id="selectSkuInventory4Edit" parameterType="java.lang.Long" resultType="map">
	select
		mi.MATERIAL_ID materialId,
		m.MATERIAL_CODE materialCode,
		m.MATERIAL_SOURCE materialSource,
		mi.MATERIAL_SKU_CODE materialSkuCode,
		ms.MATERIAL_SKU_PROP_COLOR materialSkuPropColor,
		ms.MATERIAL_SKU_PROP_SIZE materialSkuPropSize,
		ms.LIMIT_QTY limitQty,
		ms.TRADE_QTY tradeQty,
		mi.STOCK_QTY stockQty,
		mi.VIRTUAL_STOCK_QTY virtualStockQty,
		mi.LAST_UPDATE_TIME lastUpdateTime,
		mi.LAST_UPDATED_BY lastUpdatedBy,
		mi.WAREHOUSE_ID warehouseId,
		mw.WAREHOUSE_NAME warehouseName
	from wx_t_material_inventory mi
	LEFT JOIN wx_t_material_sku  ms
	on mi.MATERIAL_SKU_CODE = ms.MATERIAL_SKU_CODE
	left join wx_t_material m
	on m.id = mi.MATERIAL_ID
	left join wx_t_material_warehouse mw
	on mw.id = mi.WAREHOUSE_ID
	where mi.DELETE_FLAG = 0
		and ms.DELETE_FLAG = 0
		and mi.MATERIAL_ID =  #{materialId,jdbcType=BIGINT}
</select>
</mapper>