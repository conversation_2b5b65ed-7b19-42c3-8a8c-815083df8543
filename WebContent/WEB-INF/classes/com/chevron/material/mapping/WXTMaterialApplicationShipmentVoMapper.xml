<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationShipmentVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationShipmentVo" >
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="SHIPMENT_CODE" property="shipmentCode" jdbcType="NVARCHAR" />
    <result column="APPLICATION_ID" property="applicationId" jdbcType="BIGINT" />
    <result column="SHIP_TO_WORKSHOP" property="shipToWorkshop" jdbcType="BIT" />
    <result column="WORKSHOP_ID" property="workshopId" jdbcType="BIGINT" />
    <result column="WORKSHOP_NAME" property="workshopName" jdbcType="NVARCHAR" />
    <result column="CONTACTS" property="contacts" jdbcType="NVARCHAR" />
    <result column="CONTACT_NUMBER" property="contactNumber" jdbcType="NVARCHAR" />
    <result column="ADDRESS_REGION" property="addressRegion" jdbcType="NVARCHAR" />
    <result column="ADDRESS_DETAIL" property="addressDetail" jdbcType="NVARCHAR" />
    <result column="POST_COMPANY" property="postCompany" jdbcType="NVARCHAR" />
    <result column="RECIPIENT_DATE" property="recipientDate" jdbcType="TIMESTAMP" />
    <result column="IS_DONE_2_SMC" property="isDone2Smc" jdbcType="BIT" />
    <result column="SMC_ORDER_CODE" property="smcOrderCode" jdbcType="NVARCHAR" />
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
    <result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
    <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
    <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, SHIPMENT_CODE, APPLICATION_ID, SHIP_TO_WORKSHOP, WORKSHOP_ID, WORKSHOP_NAME, 
    CONTACTS, CONTACT_NUMBER, ADDRESS_REGION, ADDRESS_DETAIL, POST_COMPANY, RECIPIENT_DATE, 
    IS_DONE_2_SMC, SMC_ORDER_CODE, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
    CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_application_shipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_material_application_shipment
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_material_application_shipment
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVoExample" >
    delete from wx_t_material_application_shipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVo" >
    insert into wx_t_material_application_shipment (ID, SHIPMENT_CODE, APPLICATION_ID, 
      SHIP_TO_WORKSHOP, WORKSHOP_ID, WORKSHOP_NAME, 
      CONTACTS, CONTACT_NUMBER, ADDRESS_REGION, 
      ADDRESS_DETAIL, POST_COMPANY, RECIPIENT_DATE, 
      IS_DONE_2_SMC, SMC_ORDER_CODE, DELETE_FLAG, 
      ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
      CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
      )
    values (#{id,jdbcType=BIGINT}, #{shipmentCode,jdbcType=NVARCHAR}, #{applicationId,jdbcType=BIGINT}, 
      #{shipToWorkshop,jdbcType=BIT}, #{workshopId,jdbcType=BIGINT}, #{workshopName,jdbcType=NVARCHAR}, 
      #{contacts,jdbcType=NVARCHAR}, #{contactNumber,jdbcType=NVARCHAR}, #{addressRegion,jdbcType=NVARCHAR}, 
      #{addressDetail,jdbcType=NVARCHAR}, #{postCompany,jdbcType=NVARCHAR}, #{recipientDate,jdbcType=TIMESTAMP}, 
      #{isDone2Smc,jdbcType=BIT}, #{smcOrderCode,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, 
      #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVo" >
    insert into wx_t_material_application_shipment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="shipmentCode != null" >
        SHIPMENT_CODE,
      </if>
      <if test="applicationId != null" >
        APPLICATION_ID,
      </if>
      <if test="shipToWorkshop != null" >
        SHIP_TO_WORKSHOP,
      </if>
      <if test="workshopId != null" >
        WORKSHOP_ID,
      </if>
      <if test="workshopName != null" >
        WORKSHOP_NAME,
      </if>
      <if test="contacts != null" >
        CONTACTS,
      </if>
      <if test="contactNumber != null" >
        CONTACT_NUMBER,
      </if>
      <if test="addressRegion != null" >
        ADDRESS_REGION,
      </if>
      <if test="addressDetail != null" >
        ADDRESS_DETAIL,
      </if>
      <if test="postCompany != null" >
        POST_COMPANY,
      </if>
      <if test="recipientDate != null" >
        RECIPIENT_DATE,
      </if>
      <if test="isDone2Smc != null" >
        IS_DONE_2_SMC,
      </if>
      <if test="smcOrderCode != null" >
        SMC_ORDER_CODE,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shipmentCode != null" >
        #{shipmentCode,jdbcType=NVARCHAR},
      </if>
      <if test="applicationId != null" >
        #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="shipToWorkshop != null" >
        #{shipToWorkshop,jdbcType=BIT},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="contacts != null" >
        #{contacts,jdbcType=NVARCHAR},
      </if>
      <if test="contactNumber != null" >
        #{contactNumber,jdbcType=NVARCHAR},
      </if>
      <if test="addressRegion != null" >
        #{addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="addressDetail != null" >
        #{addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="postCompany != null" >
        #{postCompany,jdbcType=NVARCHAR},
      </if>
      <if test="recipientDate != null" >
        #{recipientDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDone2Smc != null" >
        #{isDone2Smc,jdbcType=BIT},
      </if>
      <if test="smcOrderCode != null" >
        #{smcOrderCode,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVoExample" resultType="java.lang.Integer" >
    select count(*) from wx_t_material_application_shipment
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_material_application_shipment
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.shipmentCode != null" >
        SHIPMENT_CODE = #{record.shipmentCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.applicationId != null" >
        APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
      </if>
      <if test="record.shipToWorkshop != null" >
        SHIP_TO_WORKSHOP = #{record.shipToWorkshop,jdbcType=BIT},
      </if>
      <if test="record.workshopId != null" >
        WORKSHOP_ID = #{record.workshopId,jdbcType=BIGINT},
      </if>
      <if test="record.workshopName != null" >
        WORKSHOP_NAME = #{record.workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="record.contacts != null" >
        CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactNumber != null" >
        CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressRegion != null" >
        ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressDetail != null" >
        ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="record.postCompany != null" >
        POST_COMPANY = #{record.postCompany,jdbcType=NVARCHAR},
      </if>
      <if test="record.recipientDate != null" >
        RECIPIENT_DATE = #{record.recipientDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDone2Smc != null" >
        IS_DONE_2_SMC = #{record.isDone2Smc,jdbcType=BIT},
      </if>
      <if test="record.smcOrderCode != null" >
        SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_material_application_shipment
    set ID = #{record.id,jdbcType=BIGINT},
      SHIPMENT_CODE = #{record.shipmentCode,jdbcType=NVARCHAR},
      APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
      SHIP_TO_WORKSHOP = #{record.shipToWorkshop,jdbcType=BIT},
      WORKSHOP_ID = #{record.workshopId,jdbcType=BIGINT},
      WORKSHOP_NAME = #{record.workshopName,jdbcType=NVARCHAR},
      CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
      CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
      ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      POST_COMPANY = #{record.postCompany,jdbcType=NVARCHAR},
      RECIPIENT_DATE = #{record.recipientDate,jdbcType=TIMESTAMP},
      IS_DONE_2_SMC = #{record.isDone2Smc,jdbcType=BIT},
      SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVo" >
    update wx_t_material_application_shipment
    <set >
      <if test="shipmentCode != null" >
        SHIPMENT_CODE = #{shipmentCode,jdbcType=NVARCHAR},
      </if>
      <if test="applicationId != null" >
        APPLICATION_ID = #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="shipToWorkshop != null" >
        SHIP_TO_WORKSHOP = #{shipToWorkshop,jdbcType=BIT},
      </if>
      <if test="workshopId != null" >
        WORKSHOP_ID = #{workshopId,jdbcType=BIGINT},
      </if>
      <if test="workshopName != null" >
        WORKSHOP_NAME = #{workshopName,jdbcType=NVARCHAR},
      </if>
      <if test="contacts != null" >
        CONTACTS = #{contacts,jdbcType=NVARCHAR},
      </if>
      <if test="contactNumber != null" >
        CONTACT_NUMBER = #{contactNumber,jdbcType=NVARCHAR},
      </if>
      <if test="addressRegion != null" >
        ADDRESS_REGION = #{addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="addressDetail != null" >
        ADDRESS_DETAIL = #{addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="postCompany != null" >
        POST_COMPANY = #{postCompany,jdbcType=NVARCHAR},
      </if>
      <if test="recipientDate != null" >
        RECIPIENT_DATE = #{recipientDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDone2Smc != null" >
        IS_DONE_2_SMC = #{isDone2Smc,jdbcType=BIT},
      </if>
      <if test="smcOrderCode != null" >
        SMC_ORDER_CODE = #{smcOrderCode,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationShipmentVo" >
    update wx_t_material_application_shipment
    set SHIPMENT_CODE = #{shipmentCode,jdbcType=NVARCHAR},
      APPLICATION_ID = #{applicationId,jdbcType=BIGINT},
      SHIP_TO_WORKSHOP = #{shipToWorkshop,jdbcType=BIT},
      WORKSHOP_ID = #{workshopId,jdbcType=BIGINT},
      WORKSHOP_NAME = #{workshopName,jdbcType=NVARCHAR},
      CONTACTS = #{contacts,jdbcType=NVARCHAR},
      CONTACT_NUMBER = #{contactNumber,jdbcType=NVARCHAR},
      ADDRESS_REGION = #{addressRegion,jdbcType=NVARCHAR},
      ADDRESS_DETAIL = #{addressDetail,jdbcType=NVARCHAR},
      POST_COMPANY = #{postCompany,jdbcType=NVARCHAR},
      RECIPIENT_DATE = #{recipientDate,jdbcType=TIMESTAMP},
      IS_DONE_2_SMC = #{isDone2Smc,jdbcType=BIT},
      SMC_ORDER_CODE = #{smcOrderCode,jdbcType=NVARCHAR},
      DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>