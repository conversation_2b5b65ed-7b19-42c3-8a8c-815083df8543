<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationLogisticsVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationLogisticsVo">
	<result column="SHIPMENT_CODE" jdbcType="NVARCHAR" property="shipmentCode" />
	<result column="SMC_ORDER_CODE" jdbcType="NVARCHAR" property="smcOrderCode" />
	<result column="EXPRESS_COMPANY" jdbcType="NVARCHAR" property="expressCompany" />
	<result column="EXPRESS_NUMBER" jdbcType="NVARCHAR" property="expressNumber" />
	<result column="TRACKING_TIME" jdbcType="TIMESTAMP" property="trackingTime" />
	<result column="EXPRESS_CONTEXT" jdbcType="NVARCHAR" property="expressContext" />
	<result column="EXPRESS_STATE" jdbcType="NVARCHAR" property="expressState" />
	<result column="EXPRESS_COMMENTS" jdbcType="NVARCHAR" property="expressComments" />
	<result column="EXPRESS_MESSAGE" jdbcType="NVARCHAR" property="expressMessage" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
</resultMap>
<sql id="Example_Where_Clause">
	<where>
	  <foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		  <trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			  <choose>
				<when test="criterion.noValue">
				  and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				  and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				  and ${criterion.condition}
				  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				  </foreach>
				</when>
			  </choose>
			</foreach>
		  </trim>
		</if>
	  </foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	  <foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		  <trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			  <choose>
				<when test="criterion.noValue">
				  and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				  and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				  and ${criterion.condition}
				  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				  </foreach>
				</when>
			  </choose>
			</foreach>
		  </trim>
		</if>
	  </foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	SHIPMENT_CODE, SMC_ORDER_CODE, EXPRESS_COMPANY, EXPRESS_NUMBER, TRACKING_TIME, EXPRESS_CONTEXT,
	EXPRESS_STATE, EXPRESS_COMMENTS, EXPRESS_MESSAGE, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2,
	CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationLogisticsVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	  distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_application_logistics
	<if test="_parameter != null">
	  <include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	  order by ${orderByClause}
	</if>
</select>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationLogisticsVoExample">
	delete from wx_t_material_application_logistics
	<if test="_parameter != null">
	  <include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationLogisticsVo">
	insert into wx_t_material_application_logistics (SHIPMENT_CODE, SMC_ORDER_CODE, EXPRESS_COMPANY,
	  EXPRESS_NUMBER, TRACKING_TIME, EXPRESS_CONTEXT,
	  EXPRESS_STATE, EXPRESS_COMMENTS, EXPRESS_MESSAGE,
	  DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2,
	  CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME,
	  LAST_UPDATED_BY)
	values (#{shipmentCode,jdbcType=NVARCHAR}, #{smcOrderCode,jdbcType=NVARCHAR}, #{expressCompany,jdbcType=NVARCHAR},
	  #{expressNumber,jdbcType=NVARCHAR}, #{trackingTime,jdbcType=TIMESTAMP}, #{expressContext,jdbcType=NVARCHAR},
	  #{expressState,jdbcType=NVARCHAR}, #{expressComments,jdbcType=NVARCHAR}, #{expressMessage,jdbcType=NVARCHAR},
	  #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR},
	  #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP},
	  #{lastUpdatedBy,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialApplicationLogisticsVo">
	insert into wx_t_material_application_logistics
	<trim prefix="(" suffix=")" suffixOverrides=",">
	  <if test="shipmentCode != null">
		SHIPMENT_CODE,
	  </if>
	  <if test="smcOrderCode != null">
		SMC_ORDER_CODE,
	  </if>
	  <if test="expressCompany != null">
		EXPRESS_COMPANY,
	  </if>
	  <if test="expressNumber != null">
		EXPRESS_NUMBER,
	  </if>
	  <if test="trackingTime != null">
		TRACKING_TIME,
	  </if>
	  <if test="expressContext != null">
		EXPRESS_CONTEXT,
	  </if>
	  <if test="expressState != null">
		EXPRESS_STATE,
	  </if>
	  <if test="expressComments != null">
		EXPRESS_COMMENTS,
	  </if>
	  <if test="expressMessage != null">
		EXPRESS_MESSAGE,
	  </if>
	  <if test="deleteFlag != null">
		DELETE_FLAG,
	  </if>
	  <if test="attribute1 != null">
		ATTRIBUTE1,
	  </if>
	  <if test="attribute2 != null">
		ATTRIBUTE2,
	  </if>
	  <if test="creationTime != null">
		CREATION_TIME,
	  </if>
	  <if test="createdBy != null">
		CREATED_BY,
	  </if>
	  <if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	  </if>
	  <if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	  </if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	  <if test="shipmentCode != null">
		#{shipmentCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="smcOrderCode != null">
		#{smcOrderCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="expressCompany != null">
		#{expressCompany,jdbcType=NVARCHAR},
	  </if>
	  <if test="expressNumber != null">
		#{expressNumber,jdbcType=NVARCHAR},
	  </if>
	  <if test="trackingTime != null">
		#{trackingTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="expressContext != null">
		#{expressContext,jdbcType=NVARCHAR},
	  </if>
	  <if test="expressState != null">
		#{expressState,jdbcType=NVARCHAR},
	  </if>
	  <if test="expressComments != null">
		#{expressComments,jdbcType=NVARCHAR},
	  </if>
	  <if test="expressMessage != null">
		#{expressMessage,jdbcType=NVARCHAR},
	  </if>
	  <if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	  </if>
	  <if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	  </if>
	  <if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	  </if>
	  <if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	  </if>
	  <if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	  </if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material_application_logistics
	<set>
	  <if test="record.shipmentCode != null">
		SHIPMENT_CODE = #{record.shipmentCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.smcOrderCode != null">
		SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.expressCompany != null">
		EXPRESS_COMPANY = #{record.expressCompany,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.expressNumber != null">
		EXPRESS_NUMBER = #{record.expressNumber,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.trackingTime != null">
		TRACKING_TIME = #{record.trackingTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="record.expressContext != null">
		EXPRESS_CONTEXT = #{record.expressContext,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.expressState != null">
		EXPRESS_STATE = #{record.expressState,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.expressComments != null">
		EXPRESS_COMMENTS = #{record.expressComments,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.expressMessage != null">
		EXPRESS_MESSAGE = #{record.expressMessage,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	  </if>
	  <if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	  </if>
	  <if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	  </if>
	  <if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	  </if>
	  <if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	  </if>
	</set>
	<if test="_parameter != null">
	  <include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material_application_logistics
	set SHIPMENT_CODE = #{record.shipmentCode,jdbcType=NVARCHAR},
	  SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
	  EXPRESS_COMPANY = #{record.expressCompany,jdbcType=NVARCHAR},
	  EXPRESS_NUMBER = #{record.expressNumber,jdbcType=NVARCHAR},
	  TRACKING_TIME = #{record.trackingTime,jdbcType=TIMESTAMP},
	  EXPRESS_CONTEXT = #{record.expressContext,jdbcType=NVARCHAR},
	  EXPRESS_STATE = #{record.expressState,jdbcType=NVARCHAR},
	  EXPRESS_COMMENTS = #{record.expressComments,jdbcType=NVARCHAR},
	  EXPRESS_MESSAGE = #{record.expressMessage,jdbcType=NVARCHAR},
	  DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	  ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	  ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	  CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	  CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	  LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	  LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
	<if test="_parameter != null">
	  <include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
</mapper>