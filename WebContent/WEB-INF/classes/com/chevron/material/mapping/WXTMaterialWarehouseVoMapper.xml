<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialWarehouseVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialWarehouseVo" >
    <result column="ID" property="id" jdbcType="BIGINT" />
    <result column="WAREHOUSE_NAME" property="warehouseName" jdbcType="NVARCHAR" />
    <result column="MATERIAL_SOURCE" property="materialSource" jdbcType="NVARCHAR" />
    <result column="ADDRESS_REGION" property="addressRegion" jdbcType="NVARCHAR" />
    <result column="ADDRESS_DETAIL" property="addressDetail" jdbcType="NVARCHAR" />
    <result column="CONTACT_NAME" property="contactName" jdbcType="NVARCHAR" />
    <result column="CONTACT_TEL" property="contactTel" jdbcType="NVARCHAR" />
    <result column="CONTACT_FAX" property="contactFax" jdbcType="NVARCHAR" />
    <result column="COMENTS" property="coments" jdbcType="NVARCHAR" />
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
    <result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
    <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
    <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    ID, WAREHOUSE_NAME, MATERIAL_SOURCE, ADDRESS_REGION, ADDRESS_DETAIL, CONTACT_NAME, 
    CONTACT_TEL, CONTACT_FAX, COMENTS, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
    CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialWarehouseVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_warehouse
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialWarehouseVoExample" >
    delete from wx_t_material_warehouse
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialWarehouseVo" >
    insert into wx_t_material_warehouse (ID, WAREHOUSE_NAME, MATERIAL_SOURCE, 
      ADDRESS_REGION, ADDRESS_DETAIL, CONTACT_NAME, 
      CONTACT_TEL, CONTACT_FAX, COMENTS, 
      DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, 
      CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
      LAST_UPDATED_BY)
    values (#{id,jdbcType=BIGINT}, #{warehouseName,jdbcType=NVARCHAR}, #{materialSource,jdbcType=NVARCHAR}, 
      #{addressRegion,jdbcType=NVARCHAR}, #{addressDetail,jdbcType=NVARCHAR}, #{contactName,jdbcType=NVARCHAR}, 
      #{contactTel,jdbcType=NVARCHAR}, #{contactFax,jdbcType=NVARCHAR}, #{coments,jdbcType=NVARCHAR}, 
      #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialWarehouseVo" >
    insert into wx_t_material_warehouse
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="warehouseName != null" >
        WAREHOUSE_NAME,
      </if>
      <if test="materialSource != null" >
        MATERIAL_SOURCE,
      </if>
      <if test="addressRegion != null" >
        ADDRESS_REGION,
      </if>
      <if test="addressDetail != null" >
        ADDRESS_DETAIL,
      </if>
      <if test="contactName != null" >
        CONTACT_NAME,
      </if>
      <if test="contactTel != null" >
        CONTACT_TEL,
      </if>
      <if test="contactFax != null" >
        CONTACT_FAX,
      </if>
      <if test="coments != null" >
        COMENTS,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="warehouseName != null" >
        #{warehouseName,jdbcType=NVARCHAR},
      </if>
      <if test="materialSource != null" >
        #{materialSource,jdbcType=NVARCHAR},
      </if>
      <if test="addressRegion != null" >
        #{addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="addressDetail != null" >
        #{addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=NVARCHAR},
      </if>
      <if test="contactTel != null" >
        #{contactTel,jdbcType=NVARCHAR},
      </if>
      <if test="contactFax != null" >
        #{contactFax,jdbcType=NVARCHAR},
      </if>
      <if test="coments != null" >
        #{coments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_material_warehouse
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseName != null" >
        WAREHOUSE_NAME = #{record.warehouseName,jdbcType=NVARCHAR},
      </if>
      <if test="record.materialSource != null" >
        MATERIAL_SOURCE = #{record.materialSource,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressRegion != null" >
        ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      </if>
      <if test="record.addressDetail != null" >
        ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactName != null" >
        CONTACT_NAME = #{record.contactName,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactTel != null" >
        CONTACT_TEL = #{record.contactTel,jdbcType=NVARCHAR},
      </if>
      <if test="record.contactFax != null" >
        CONTACT_FAX = #{record.contactFax,jdbcType=NVARCHAR},
      </if>
      <if test="record.coments != null" >
        COMENTS = #{record.coments,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_material_warehouse
    set ID = #{record.id,jdbcType=BIGINT},
      WAREHOUSE_NAME = #{record.warehouseName,jdbcType=NVARCHAR},
      MATERIAL_SOURCE = #{record.materialSource,jdbcType=NVARCHAR},
      ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
      ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
      CONTACT_NAME = #{record.contactName,jdbcType=NVARCHAR},
      CONTACT_TEL = #{record.contactTel,jdbcType=NVARCHAR},
      CONTACT_FAX = #{record.contactFax,jdbcType=NVARCHAR},
      COMENTS = #{record.coments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>