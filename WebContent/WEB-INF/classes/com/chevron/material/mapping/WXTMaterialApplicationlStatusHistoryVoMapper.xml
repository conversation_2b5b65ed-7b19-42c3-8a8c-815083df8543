<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationlStatusHistoryVoMapper" >
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVo" >
	<result column="APPLICATION_ID" property="applicationId" jdbcType="BIGINT" />
	<result column="STEP" property="step" jdbcType="NVARCHAR" />
	<result column="ORIGINAL_STATUS" property="originalStatus" jdbcType="NVARCHAR" />
	<result column="CURRENT_STATUS" property="currentStatus" jdbcType="NVARCHAR" />
	<result column="RELATED_INFO" property="relatedInfo" jdbcType="NVARCHAR" />
	<result column="COMENTS" property="coments" jdbcType="NVARCHAR" />
	<result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
	<result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
	<result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
	<result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
	<result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
	<result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
	<result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
	<result column="CREATION_PERSON_NAME" property="creationPersonName" jdbcType="NVARCHAR" />

</resultMap>
<sql id="Example_Where_Clause" >
	<where >
	<foreach collection="oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause" >
	<where >
	<foreach collection="example.oredCriteria" item="criteria" separator="or" >
		<if test="criteria.valid" >
		<trim prefix="(" suffix=")" prefixOverrides="and" >
			<foreach collection="criteria.criteria" item="criterion" >
			<choose >
				<when test="criterion.noValue" >
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue" >
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue" >
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue" >
				and ${criterion.condition}
				<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List" >
	APPLICATION_ID, STEP, ORIGINAL_STATUS, CURRENT_STATUS, RELATED_INFO, COMENTS, DELETE_FLAG,
	ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
</sql>
<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVoExample" >
	select
	<if test="distinct" >
	distinct
	</if>
	'true' as QUERYID,
	h.APPLICATION_ID, h.STEP,h.ORIGINAL_STATUS, h.CURRENT_STATUS, h.RELATED_INFO, h.COMENTS, h.DELETE_FLAG,
	h.ATTRIBUTE1, h.ATTRIBUTE2, h.CREATION_TIME, h.CREATED_BY, h.LAST_UPDATE_TIME, h.LAST_UPDATED_BY, u.CH_NAME CREATION_PERSON_NAME
	from wx_t_material_applicationl_status_history h
	left join wx_t_user u
	on h.CREATED_BY = u.user_id
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null" >
	order by ${orderByClause}
	</if>
</select>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVoExample" >
	delete from wx_t_material_applicationl_status_history
	<if test="_parameter != null" >
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVo" >
	insert into wx_t_material_applicationl_status_history (APPLICATION_ID, STEP, ORIGINAL_STATUS,
	CURRENT_STATUS, RELATED_INFO, COMENTS,
	DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2,
	CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME,
	LAST_UPDATED_BY)
	values (#{applicationId,jdbcType=BIGINT}, #{step,jdbcType=NVARCHAR}, #{originalStatus,jdbcType=NVARCHAR},
	#{currentStatus,jdbcType=NVARCHAR}, #{relatedInfo,jdbcType=NVARCHAR}, #{coments,jdbcType=NVARCHAR},
	#{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR},
	#{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP},
	#{lastUpdatedBy,jdbcType=BIGINT})
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialApplicationlStatusHistoryVo" >
	insert into wx_t_material_applicationl_status_history
	<trim prefix="(" suffix=")" suffixOverrides="," >
	<if test="applicationId != null" >
		APPLICATION_ID,
	</if>
	<if test="step != null" >
		STEP,
	</if>
	<if test="originalStatus != null" >
		ORIGINAL_STATUS,
	</if>
	<if test="currentStatus != null" >
		CURRENT_STATUS,
	</if>
	<if test="relatedInfo != null" >
		RELATED_INFO,
	</if>
	<if test="coments != null" >
		COMENTS,
	</if>
	<if test="deleteFlag != null" >
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null" >
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null" >
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null" >
		CREATION_TIME,
	</if>
	<if test="createdBy != null" >
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null" >
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null" >
		LAST_UPDATED_BY,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides="," >
	<if test="applicationId != null" >
		#{applicationId,jdbcType=BIGINT},
	</if>
	<if test="step != null" >
		#{step,jdbcType=NVARCHAR},
	</if>
	<if test="originalStatus != null" >
		#{originalStatus,jdbcType=NVARCHAR},
	</if>
	<if test="currentStatus != null" >
		#{currentStatus,jdbcType=NVARCHAR},
	</if>
	<if test="relatedInfo != null" >
		#{relatedInfo,jdbcType=NVARCHAR},
	</if>
	<if test="coments != null" >
		#{coments,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null" >
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null" >
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null" >
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null" >
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null" >
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null" >
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null" >
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map" >
	update wx_t_material_applicationl_status_history
	<set >
	<if test="record.applicationId != null" >
		APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
	</if>
	<if test="record.step != null" >
		STEP = #{record.step,jdbcType=NVARCHAR},
	</if>
	<if test="record.originalStatus != null" >
		ORIGINAL_STATUS = #{record.originalStatus,jdbcType=NVARCHAR},
	</if>
	<if test="record.currentStatus != null" >
		CURRENT_STATUS = #{record.currentStatus,jdbcType=NVARCHAR},
	</if>
	<if test="record.relatedInfo != null" >
		RELATED_INFO = #{record.relatedInfo,jdbcType=NVARCHAR},
	</if>
	<if test="record.coments != null" >
		COMENTS = #{record.coments,jdbcType=NVARCHAR},
	</if>
	<if test="record.deleteFlag != null" >
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null" >
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null" >
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null" >
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null" >
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null" >
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null" >
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	</set>
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map" >
	update wx_t_material_applicationl_status_history
	set APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
	STEP = #{record.step,jdbcType=NVARCHAR},
	ORIGINAL_STATUS = #{record.originalStatus,jdbcType=NVARCHAR},
	CURRENT_STATUS = #{record.currentStatus,jdbcType=NVARCHAR},
	RELATED_INFO = #{record.relatedInfo,jdbcType=NVARCHAR},
	COMENTS = #{record.coments,jdbcType=NVARCHAR},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
	<if test="_parameter != null" >
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
</mapper>