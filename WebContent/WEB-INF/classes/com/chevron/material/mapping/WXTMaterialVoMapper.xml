<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialVo">
	<id column="ID" jdbcType="BIGINT" property="id" />
	<result column="MATERIAL_CODE" jdbcType="NVARCHAR" property="materialCode" />
	<result column="SMC_MATERIAL_CODE" jdbcType="NVARCHAR" property="smcMaterialCode" />
	<result column="MATERIAL_TYPE" jdbcType="NVARCHAR" property="materialType" />
	<result column="MATERIAL_TYPE_NAME" property="materialTypeName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_NAME" jdbcType="NVARCHAR" property="materialName" />
	<result column="MATERIAL_NAME_EN" jdbcType="NVARCHAR" property="materialNameEn" />
	<result column="MATERIAL_DESC" jdbcType="NVARCHAR" property="materialDesc" />
	<result column="SMC_MATERIAL_CATEGORY" jdbcType="NVARCHAR" property="smcMaterialCategory" />
	<result column="MATERIAL_SIZEL" jdbcType="INTEGER" property="materialSizel" />
	<result column="MATERIAL_SIZEW" jdbcType="INTEGER" property="materialSizew" />
	<result column="MATERIAL_SIZEH" jdbcType="INTEGER" property="materialSizeh" />
	<result column="ext_flag" jdbcType="INTEGER" property="extFlag" />
	<result column="MATERIAL_WEIGHT" jdbcType="NUMERIC" property="materialWeight" />
	<result column="MATERIAL_MAKE_PERIOD" jdbcType="INTEGER" property="materialMakePeriod" />
	<result column="MATERIAL_PRODUCTION_PERIOD" jdbcType="INTEGER" property="materialProductionPeriod" />
	<result column="MATERIAL_PICTURE" jdbcType="BIGINT" property="materialPicture" />
	<result column="MATERIAL_IMAGES" jdbcType="NVARCHAR" property="materialImages" />
	<result column="FIRST_MATERIAL_IMAGE" jdbcType="NVARCHAR" property="firstMaterialImage" />
	<result column="MATERIAL_PRICE" jdbcType="NUMERIC" property="materialPrice" />
	<result column="MATERIAL_UNIT" jdbcType="NVARCHAR" property="materialUnit" />
	<result column="MATERIAL_QTY" property="materialQty" jdbcType="NVARCHAR" />
	<result column="STOCK_QTY" property="stockQty" jdbcType="NVARCHAR" />
	<result column="VIRTUAL_STOCK_QTY" property="virtualStockQty" jdbcType="NVARCHAR" />
	<result column="CUST_EMAIL" jdbcType="NVARCHAR" property="custEmail" />
	<result column="SUPPLIER_EMAIL" jdbcType="NVARCHAR" property="supplierEmail" />
	<result column="SELLING_POINT" jdbcType="NVARCHAR" property="sellingPoint" />
	<result column="SELLING_POINT_EN" jdbcType="NVARCHAR" property="sellingPointEn" />
	<result column="STATE" jdbcType="NVARCHAR" property="state" />
	<result column="SMC_STATUS" jdbcType="INTEGER" property="smcStatus" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="ATTRIBUTE3" jdbcType="NVARCHAR" property="attribute3" />
    <result column="ATTRIBUTE4" jdbcType="NVARCHAR" property="attribute4" />
    <result column="ATTRIBUTE5" jdbcType="NVARCHAR" property="attribute5" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
	<result column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
	<result column="MATERIAL_SKU_PROP_COLOR" jdbcType="NVARCHAR" property="materialSkuPropColor" />
	<result column="MATERIAL_SKU_PROP_SIZE" jdbcType="NVARCHAR" property="materialSkuPropSize" />
	<result column="SMC_STATUS" property="smcStatus" jdbcType="INTEGER" />
	<result column="MATERIAL_SOURCE" jdbcType="NVARCHAR" property="materialSource" />
	<result column="MATERIAL_CATEGORY" jdbcType="NVARCHAR" property="materialCategory" />
	<result column="OVER_LIMIT" jdbcType="BIT" property="overLimit" />
	<result column="IS_LEFTOVER" jdbcType="BIT" property="isLeftover" />
	<result column="IS_PRESALE" jdbcType="BIT" property="isPresale" />
	<result column="SHOW_STOCK_QTY" jdbcType="BIT" property="showStockQty" />
	<result column="POINT_MODE" jdbcType="BIT" property="pointMode" />
	<result column="INVISIBLE_GROUP" jdbcType="NVARCHAR" property="invisibleGroup" />
	<result column="INVISIBLE_DEALER" jdbcType="NVARCHAR" property="invisibleDealer" />
	<result column="WAREHOUSE_ID" property="warehouseId" jdbcType="BIGINT" />
	<result column="WAREHOUSE_NAME" property="warehouseName" jdbcType="NVARCHAR" />
	<result column="POINT_TYPE" jdbcType="NVARCHAR" property="pointType" />
	<result column="MATERIAL_AFTER_SALES_INFO" jdbcType="NVARCHAR" property="materialAfterSalesInfo" />
	<result column="LIMIT_QTY_PER_ORDER" jdbcType="BIGINT" property="limitQtyPerOrder" />
	<result column="LIMIT_QTY_PER_YEAR" jdbcType="BIGINT" property="limitQtyPerYear" />
	<result column="available_quantity" jdbcType="INTEGER" property="availableQuantity" />
	<result column="version_no" jdbcType="BIGINT" property="versionNo" />
</resultMap>
<!-- 物料申请历史 -->
<resultMap type="com.chevron.material.model.ApplicationHistory" id="AppHisVo">
	<result column="MATERIAL_ID" property="materialId" jdbcType="BIGINT" />
	<result column="MATERIAL_CODE" property="materialCode" jdbcType="NVARCHAR" />
	<result column="MATERIAL_SKU_CODE" property="materialSkuCode" jdbcType="NVARCHAR" />
	<result column="MATERIAL_TYPE_NAME" property="materialTypeName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_NAME" property="materialName" jdbcType="NVARCHAR" />
	<result column="MATERIAL_IMAGES" jdbcType="NVARCHAR" property="materialImages" />
	<result column="MATERIAL_DESC" property="materialDesc" jdbcType="NVARCHAR" />
	<result column="MATERIAL_PICTURE" property="materialPicture" jdbcType="BIGINT" />
	<result column="APPLICATION_QTY" property="applicationQty" jdbcType="BIGINT" />
	<result column="RECEIVED_QTY" property="receivedQty" jdbcType="BIGINT" />
	<result column="SHIPPING_QTY" property="shippingQty" jdbcType="BIGINT" />
	<result column="PENDING_QTY" property="pendingQty" jdbcType="BIGINT" />
	<result column="OUTBOUND_QTY" property="outboundQty" jdbcType="BIGINT" />
	<result column="STOCK_QTY" property="stockQty" jdbcType="BIGINT" />
	<result column="VIRTUAL_STOCK_QTY" property="virtualStockQty" jdbcType="BIGINT" />
</resultMap>

<!-- 物料申请历史详情 -->
<resultMap type="com.chevron.material.model.ApplicationHistoryDetail" id="AppHisDetailVo">
	<result column="APPLICATION_ID" property="applicationId" jdbcType="BIGINT" />
	<result column="MATERIAL_ID" property="materialId" jdbcType="BIGINT" />
	<result column="MATERIAL_SKU_CODE" property="materialSkuCode" jdbcType="NVARCHAR" />
	<result column="APPLICATION_QTY" property="applicationQty" jdbcType="BIGINT" />
	<result column="APPLICATION_PERSON_ID" property="applicationPersonId" jdbcType="BIGINT" />
	<result column="APPLICATION_PERSON_NAME" property="applicationPersonName" jdbcType="NVARCHAR" />
	<result column="APPLICATION_TIME" property="applicationTime" jdbcType="TIMESTAMP" />
	<result column="APPLICATION_ORG_ID" property="applicationOrgId" jdbcType="BIGINT" />
	<result column="APPLICATION_ORG_NAME" property="applicationOrgName" jdbcType="NVARCHAR" />
</resultMap>

<!-- 物料申请历史详情, 按月统计-->
<resultMap type="com.chevron.material.model.ApplicationHistoryDetailMonthly" id="AppHisDetailMonthlyVo">
	<result column="MATERIAL_ID" property="materialId" jdbcType="BIGINT" />
	<result column="APPLICATION_QTY" property="applicationQty" jdbcType="BIGINT" />
	<result column="APPLICATION_MONTH" property="applicationMonth" jdbcType="NVARCHAR" />
	<result column="APPLICATION_ORG_ID" property="applicationOrgId" jdbcType="BIGINT" />
	<result column="APPLICATION_ORG_NAME" property="applicationOrgName" jdbcType="NVARCHAR" />
</resultMap>
<sql id="Example_Where_Clause">
	<where>
	<foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	<foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	ID, MATERIAL_CODE, SMC_MATERIAL_CODE, MATERIAL_TYPE, MATERIAL_NAME, MATERIAL_NAME_EN,
	MATERIAL_DESC, SMC_MATERIAL_CATEGORY, MATERIAL_SIZEL, MATERIAL_SIZEW, MATERIAL_SIZEH,
	MATERIAL_WEIGHT, MATERIAL_MAKE_PERIOD, MATERIAL_PRODUCTION_PERIOD, MATERIAL_PICTURE,
	MATERIAL_PRICE, MATERIAL_UNIT, CUST_EMAIL, SUPPLIER_EMAIL, SELLING_POINT, SELLING_POINT_EN,
	STATE, SMC_STATUS, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2,ATTRIBUTE3, ATTRIBUTE4,ATTRIBUTE5, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY, MATERIAL_SOURCE, MATERIAL_CATEGORY, OVER_LIMIT,
	IS_LEFTOVER, IS_PRESALE, SHOW_STOCK_QTY, POINT_MODE, INVISIBLE_GROUP, INVISIBLE_DEALER,
	POINT_TYPE, MATERIAL_AFTER_SALES_INFO
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_material
	where ID = #{id,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
	delete from wx_t_material
	where ID = #{id,jdbcType=BIGINT}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialVoExample">
	delete from wx_t_material
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialVo" useGeneratedKeys="true" keyProperty="id">
	insert into wx_t_material (ID, MATERIAL_CODE, SMC_MATERIAL_CODE,
	MATERIAL_TYPE, MATERIAL_NAME, MATERIAL_NAME_EN,
	MATERIAL_DESC, SMC_MATERIAL_CATEGORY, MATERIAL_SIZEL,
	MATERIAL_SIZEW, MATERIAL_SIZEH, MATERIAL_WEIGHT,
	MATERIAL_MAKE_PERIOD, MATERIAL_PRODUCTION_PERIOD,
	MATERIAL_PICTURE, MATERIAL_PRICE, MATERIAL_UNIT,
	CUST_EMAIL, SUPPLIER_EMAIL, SELLING_POINT,
	SELLING_POINT_EN, STATE, SMC_STATUS,
	DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2,
	CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME,
	LAST_UPDATED_BY, MATERIAL_SOURCE, MATERIAL_CATEGORY,
	OVER_LIMIT, IS_LEFTOVER, IS_PRESALE,
	SHOW_STOCK_QTY, POINT_MODE, INVISIBLE_GROUP,
	INVISIBLE_DEALER, POINT_TYPE, MATERIAL_AFTER_SALES_INFO
	)
	values (#{id,jdbcType=BIGINT}, #{materialCode,jdbcType=NVARCHAR}, #{smcMaterialCode,jdbcType=NVARCHAR},
	#{materialType,jdbcType=NVARCHAR}, #{materialName,jdbcType=NVARCHAR}, #{materialNameEn,jdbcType=NVARCHAR},
	#{materialDesc,jdbcType=NVARCHAR}, #{smcMaterialCategory,jdbcType=NVARCHAR}, #{materialSizel,jdbcType=INTEGER},
	#{materialSizew,jdbcType=INTEGER}, #{materialSizeh,jdbcType=INTEGER}, #{materialWeight,jdbcType=NUMERIC},
	#{materialMakePeriod,jdbcType=INTEGER}, #{materialProductionPeriod,jdbcType=INTEGER},
	#{materialPicture,jdbcType=BIGINT}, #{materialPrice,jdbcType=NUMERIC}, #{materialUnit,jdbcType=NVARCHAR},
	#{custEmail,jdbcType=NVARCHAR}, #{supplierEmail,jdbcType=NVARCHAR}, #{sellingPoint,jdbcType=NVARCHAR},
	#{sellingPointEn,jdbcType=NVARCHAR}, #{state,jdbcType=NVARCHAR}, #{smcStatus,jdbcType=INTEGER},
	#{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR},
	#{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP},
	#{lastUpdatedBy,jdbcType=BIGINT}, #{materialSource,jdbcType=NVARCHAR}, #{materialCategory,jdbcType=NVARCHAR},
	#{overLimit,jdbcType=BIT}, #{isLeftover,jdbcType=BIT}, #{isPresale,jdbcType=BIT},
	#{showStockQty,jdbcType=BIT}, #{pointMode,jdbcType=BIT}, #{invisibleGroup,jdbcType=NVARCHAR},
	#{invisibleDealer,jdbcType=NVARCHAR}, #{pointType,jdbcType=NVARCHAR}, #{materialAfterSalesInfo,jdbcType=NVARCHAR}
	)
</insert>
<insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialVo" useGeneratedKeys="true" keyProperty="id">
	insert into wx_t_material
	<trim prefix="(" suffix=")" suffixOverrides=",">
	<if test="id != null">
		ID,
	</if>
	<if test="materialCode != null">
		MATERIAL_CODE,
	</if>
	<if test="smcMaterialCode != null">
		SMC_MATERIAL_CODE,
	</if>
	<if test="materialType != null">
		MATERIAL_TYPE,
	</if>
	<if test="materialName != null">
		MATERIAL_NAME,
	</if>
	<if test="materialNameEn != null">
		MATERIAL_NAME_EN,
	</if>
	<if test="materialDesc != null">
		MATERIAL_DESC,
	</if>
	<if test="smcMaterialCategory != null">
		SMC_MATERIAL_CATEGORY,
	</if>
	<if test="materialSizel != null">
		MATERIAL_SIZEL,
	</if>
	<if test="materialSizew != null">
		MATERIAL_SIZEW,
	</if>
	<if test="materialSizeh != null">
		MATERIAL_SIZEH,
	</if>
	<if test="materialWeight != null">
		MATERIAL_WEIGHT,
	</if>
	<if test="materialMakePeriod != null">
		MATERIAL_MAKE_PERIOD,
	</if>
	<if test="materialProductionPeriod != null">
		MATERIAL_PRODUCTION_PERIOD,
	</if>
	<if test="materialPicture != null">
		MATERIAL_PICTURE,
	</if>
	<if test="materialPrice != null">
		MATERIAL_PRICE,
	</if>
	<if test="materialUnit != null">
		MATERIAL_UNIT,
	</if>
	<if test="custEmail != null">
		CUST_EMAIL,
	</if>
	<if test="supplierEmail != null">
		SUPPLIER_EMAIL,
	</if>
	<if test="sellingPoint != null">
		SELLING_POINT,
	</if>
	<if test="sellingPointEn != null">
		SELLING_POINT_EN,
	</if>
	<if test="state != null">
		STATE,
	</if>
	<if test="smcStatus != null">
		SMC_STATUS,
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2,
	</if>
    <if test="attribute3 != null">
        ATTRIBUTE3,
    </if>
    <if test="attribute4 != null">
        ATTRIBUTE4,
    </if>
    <if test="attribute5 != null">
        ATTRIBUTE5,
    </if>
	<if test="creationTime != null">
		CREATION_TIME,
	</if>
	<if test="createdBy != null">
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	</if>
	<if test="materialSource != null">
		MATERIAL_SOURCE,
	</if>
	<if test="materialCategory != null">
		MATERIAL_CATEGORY,
	</if>
	<if test="overLimit != null">
		OVER_LIMIT,
	</if>
	<if test="isLeftover != null">
		IS_LEFTOVER,
	</if>
	<if test="isPresale != null">
		IS_PRESALE,
	</if>
	<if test="showStockQty != null">
		SHOW_STOCK_QTY,
	</if>
	<if test="pointMode != null">
		POINT_MODE,
	</if>
	<if test="invisibleGroup != null">
		INVISIBLE_GROUP,
	</if>
	<if test="invisibleDealer != null">
		INVISIBLE_DEALER,
	</if>
	<if test="pointType != null">
		POINT_TYPE,
	</if>
	<if test="materialAfterSalesInfo != null">
		MATERIAL_AFTER_SALES_INFO,
	</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	<if test="id != null">
		#{id,jdbcType=BIGINT},
	</if>
	<if test="materialCode != null">
		#{materialCode,jdbcType=NVARCHAR},
	</if>
	<if test="smcMaterialCode != null">
		#{smcMaterialCode,jdbcType=NVARCHAR},
	</if>
	<if test="materialType != null">
		#{materialType,jdbcType=NVARCHAR},
	</if>
	<if test="materialName != null">
		#{materialName,jdbcType=NVARCHAR},
	</if>
	<if test="materialNameEn != null">
		#{materialNameEn,jdbcType=NVARCHAR},
	</if>
	<if test="materialDesc != null">
		#{materialDesc,jdbcType=NVARCHAR},
	</if>
	<if test="smcMaterialCategory != null">
		#{smcMaterialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="materialSizel != null">
		#{materialSizel,jdbcType=INTEGER},
	</if>
	<if test="materialSizew != null">
		#{materialSizew,jdbcType=INTEGER},
	</if>
	<if test="materialSizeh != null">
		#{materialSizeh,jdbcType=INTEGER},
	</if>
	<if test="materialWeight != null">
		#{materialWeight,jdbcType=NUMERIC},
	</if>
	<if test="materialMakePeriod != null">
		#{materialMakePeriod,jdbcType=INTEGER},
	</if>
	<if test="materialProductionPeriod != null">
		#{materialProductionPeriod,jdbcType=INTEGER},
	</if>
	<if test="materialPicture != null">
		#{materialPicture,jdbcType=BIGINT},
	</if>
	<if test="materialPrice != null">
		#{materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="materialUnit != null">
		#{materialUnit,jdbcType=NVARCHAR},
	</if>
	<if test="custEmail != null">
		#{custEmail,jdbcType=NVARCHAR},
	</if>
	<if test="supplierEmail != null">
		#{supplierEmail,jdbcType=NVARCHAR},
	</if>
	<if test="sellingPoint != null">
		#{sellingPoint,jdbcType=NVARCHAR},
	</if>
	<if test="sellingPointEn != null">
		#{sellingPointEn,jdbcType=NVARCHAR},
	</if>
	<if test="state != null">
		#{state,jdbcType=NVARCHAR},
	</if>
	<if test="smcStatus != null">
		#{smcStatus,jdbcType=INTEGER},
	</if>
	<if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	</if>
    <if test="attribute3 != null">
        #{attribute3,jdbcType=NVARCHAR},
    </if>
    <if test="attribute4 != null">
        #{attribute4,jdbcType=NVARCHAR},
    </if>
    <if test="attribute5 != null">
        #{attribute5,jdbcType=NVARCHAR},
    </if>
	<if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="materialSource != null">
		#{materialSource,jdbcType=NVARCHAR},
	</if>
	<if test="materialCategory != null">
		#{materialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="overLimit != null">
		#{overLimit,jdbcType=BIT},
	</if>
	<if test="isLeftover != null">
		#{isLeftover,jdbcType=BIT},
	</if>
	<if test="isPresale != null">
		#{isPresale,jdbcType=BIT},
	</if>
	<if test="showStockQty != null">
		#{showStockQty,jdbcType=BIT},
	</if>
	<if test="pointMode != null">
		#{pointMode,jdbcType=BIT},
	</if>
	<if test="invisibleGroup != null">
		#{invisibleGroup,jdbcType=NVARCHAR},
	</if>
	<if test="invisibleDealer != null">
		#{invisibleDealer,jdbcType=NVARCHAR},
	</if>
	<if test="pointType != null">
		#{pointType,jdbcType=NVARCHAR},
	</if>
	<if test="materialAfterSalesInfo != null">
		#{materialAfterSalesInfo,jdbcType=NVARCHAR},
	</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material
	<set>
	<if test="record.id != null">
		ID = #{record.id,jdbcType=BIGINT},
	</if>
	<if test="record.extFlag != null and record.extFlag != null and record.extFlag != 0">
		ext_flag = #{record.extFlag,jdbcType=INTEGER},
	</if>
	<if test="record.extNewFlag != null and record.extNewFlag != null ">
		ext_flag = (ext_flag | #{record.extNewFlag,jdbcType=INTEGER}),
	</if>
	<if test="record.materialCode != null">
		MATERIAL_CODE = #{record.materialCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.smcMaterialCode != null">
		SMC_MATERIAL_CODE = #{record.smcMaterialCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialType != null">
		MATERIAL_TYPE = #{record.materialType,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialName != null">
		MATERIAL_NAME = #{record.materialName,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialNameEn != null">
		MATERIAL_NAME_EN = #{record.materialNameEn,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialDesc != null">
		MATERIAL_DESC = #{record.materialDesc,jdbcType=NVARCHAR},
	</if>
	<if test="record.smcMaterialCategory != null">
		SMC_MATERIAL_CATEGORY = #{record.smcMaterialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialSizel != null">
		MATERIAL_SIZEL = #{record.materialSizel,jdbcType=INTEGER},
	</if>
	<if test="record.materialSizew != null">
		MATERIAL_SIZEW = #{record.materialSizew,jdbcType=INTEGER},
	</if>
	<if test="record.materialSizeh != null">
		MATERIAL_SIZEH = #{record.materialSizeh,jdbcType=INTEGER},
	</if>
	<if test="record.materialWeight != null">
		MATERIAL_WEIGHT = #{record.materialWeight,jdbcType=NUMERIC},
	</if>
	<if test="record.materialMakePeriod != null">
		MATERIAL_MAKE_PERIOD = #{record.materialMakePeriod,jdbcType=INTEGER},
	</if>
	<if test="record.materialProductionPeriod != null">
		MATERIAL_PRODUCTION_PERIOD = #{record.materialProductionPeriod,jdbcType=INTEGER},
	</if>
	<if test="record.materialPicture != null">
		MATERIAL_PICTURE = #{record.materialPicture,jdbcType=BIGINT},
	</if>
	<if test="record.materialPrice != null">
		MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="record.materialUnit != null">
		MATERIAL_UNIT = #{record.materialUnit,jdbcType=NVARCHAR},
	</if>
	<if test="record.custEmail != null">
		CUST_EMAIL = #{record.custEmail,jdbcType=NVARCHAR},
	</if>
	<if test="record.supplierEmail != null">
		SUPPLIER_EMAIL = #{record.supplierEmail,jdbcType=NVARCHAR},
	</if>
	<if test="record.sellingPoint != null">
		SELLING_POINT = #{record.sellingPoint,jdbcType=NVARCHAR},
	</if>
	<if test="record.sellingPointEn != null">
		SELLING_POINT_EN = #{record.sellingPointEn,jdbcType=NVARCHAR},
	</if>
	<if test="record.state != null">
		STATE = #{record.state,jdbcType=NVARCHAR},
	</if>
	<if test="record.smcStatus != null">
		SMC_STATUS = #{record.smcStatus,jdbcType=INTEGER},
	</if>
	<if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
    <if test="record.attribute3 != null">
        ATTRIBUTE3 = #{record.attribute3,jdbcType=NVARCHAR},
    </if>
    <if test="record.attribute4 != null">
        ATTRIBUTE4 = #{record.attribute4,jdbcType=NVARCHAR},
    </if>
    <if test="record.attribute5 != null">
        ATTRIBUTE5 = #{record.attribute5,jdbcType=NVARCHAR},
    </if>
	<if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="record.materialSource != null">
		MATERIAL_SOURCE = #{record.materialSource,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialCategory != null">
		MATERIAL_CATEGORY = #{record.materialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="record.overLimit != null">
		OVER_LIMIT = #{record.overLimit,jdbcType=BIT},
	</if>
	<if test="record.isLeftover != null">
		IS_LEFTOVER = #{record.isLeftover,jdbcType=BIT},
	</if>
	<if test="record.isPresale != null">
		IS_PRESALE = #{record.isPresale,jdbcType=BIT},
	</if>
	<if test="record.showStockQty != null">
		SHOW_STOCK_QTY = #{record.showStockQty,jdbcType=BIT},
	</if>
	<if test="record.pointMode != null">
		POINT_MODE = #{record.pointMode,jdbcType=BIT},
	</if>
	<if test="record.invisibleGroup != null">
		INVISIBLE_GROUP = #{record.invisibleGroup,jdbcType=NVARCHAR},
	</if>
	<if test="record.invisibleDealer != null">
		INVISIBLE_DEALER = #{record.invisibleDealer,jdbcType=NVARCHAR},
	</if>
	<if test="record.pointType != null">
		POINT_TYPE = #{record.pointType,jdbcType=NVARCHAR},
	</if>
	<if test="record.materialAfterSalesInfo != null">
		MATERIAL_AFTER_SALES_INFO = #{record.materialAfterSalesInfo,jdbcType=NVARCHAR},
	</if>
	</set>
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material
	set ID = #{record.id,jdbcType=BIGINT},
	MATERIAL_CODE = #{record.materialCode,jdbcType=NVARCHAR},
	SMC_MATERIAL_CODE = #{record.smcMaterialCode,jdbcType=NVARCHAR},
	MATERIAL_TYPE = #{record.materialType,jdbcType=NVARCHAR},
	MATERIAL_NAME = #{record.materialName,jdbcType=NVARCHAR},
	MATERIAL_NAME_EN = #{record.materialNameEn,jdbcType=NVARCHAR},
	MATERIAL_DESC = #{record.materialDesc,jdbcType=NVARCHAR},
	SMC_MATERIAL_CATEGORY = #{record.smcMaterialCategory,jdbcType=NVARCHAR},
	MATERIAL_SIZEL = #{record.materialSizel,jdbcType=INTEGER},
	MATERIAL_SIZEW = #{record.materialSizew,jdbcType=INTEGER},
	MATERIAL_SIZEH = #{record.materialSizeh,jdbcType=INTEGER},
	MATERIAL_WEIGHT = #{record.materialWeight,jdbcType=NUMERIC},
	MATERIAL_MAKE_PERIOD = #{record.materialMakePeriod,jdbcType=INTEGER},
	MATERIAL_PRODUCTION_PERIOD = #{record.materialProductionPeriod,jdbcType=INTEGER},
	MATERIAL_PICTURE = #{record.materialPicture,jdbcType=BIGINT},
	MATERIAL_PRICE = #{record.materialPrice,jdbcType=NUMERIC},
	MATERIAL_UNIT = #{record.materialUnit,jdbcType=NVARCHAR},
	CUST_EMAIL = #{record.custEmail,jdbcType=NVARCHAR},
	SUPPLIER_EMAIL = #{record.supplierEmail,jdbcType=NVARCHAR},
	SELLING_POINT = #{record.sellingPoint,jdbcType=NVARCHAR},
	SELLING_POINT_EN = #{record.sellingPointEn,jdbcType=NVARCHAR},
	STATE = #{record.state,jdbcType=NVARCHAR},
	SMC_STATUS = #{record.smcStatus,jdbcType=INTEGER},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
    ATTRIBUTE3 = #{record.attribute3,jdbcType=NVARCHAR},
    ATTRIBUTE4 = #{record.attribute4,jdbcType=NVARCHAR},
    ATTRIBUTE5 = #{record.attribute5,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	MATERIAL_SOURCE = #{record.materialSource,jdbcType=NVARCHAR},
	MATERIAL_CATEGORY = #{record.materialCategory,jdbcType=NVARCHAR},
	OVER_LIMIT = #{record.overLimit,jdbcType=BIT},
	IS_LEFTOVER = #{record.isLeftover,jdbcType=BIT},
	IS_PRESALE = #{record.isPresale,jdbcType=BIT},
	SHOW_STOCK_QTY = #{record.showStockQty,jdbcType=BIT},
	POINT_MODE = #{record.pointMode,jdbcType=BIT},
	INVISIBLE_GROUP = #{record.invisibleGroup,jdbcType=NVARCHAR},
	INVISIBLE_DEALER = #{record.invisibleDealer,jdbcType=NVARCHAR},
	POINT_TYPE = #{record.pointType,jdbcType=NVARCHAR},
	MATERIAL_AFTER_SALES_INFO = #{record.materialAfterSalesInfo,jdbcType=NVARCHAR}
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialVo">
	update wx_t_material
	<set>
	<if test="materialCode != null">
		MATERIAL_CODE = #{materialCode,jdbcType=NVARCHAR},
	</if>
	
	<if test="smcMaterialCode != null">
		SMC_MATERIAL_CODE = #{smcMaterialCode,jdbcType=NVARCHAR},
	</if>
	<if test="materialType != null">
		MATERIAL_TYPE = #{materialType,jdbcType=NVARCHAR},
	</if>
	<if test="materialName != null">
		MATERIAL_NAME = #{materialName,jdbcType=NVARCHAR},
	</if>
	<if test="materialNameEn != null">
		MATERIAL_NAME_EN = #{materialNameEn,jdbcType=NVARCHAR},
	</if>
	<if test="materialDesc != null">
		MATERIAL_DESC = #{materialDesc,jdbcType=NVARCHAR},
	</if>
	<if test="smcMaterialCategory != null">
		SMC_MATERIAL_CATEGORY = #{smcMaterialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="materialSizel != null">
		MATERIAL_SIZEL = #{materialSizel,jdbcType=INTEGER},
	</if>
	<if test="materialSizew != null">
		MATERIAL_SIZEW = #{materialSizew,jdbcType=INTEGER},
	</if>
	<if test="materialSizeh != null">
		MATERIAL_SIZEH = #{materialSizeh,jdbcType=INTEGER},
	</if>
	<if test="materialWeight != null">
		MATERIAL_WEIGHT = #{materialWeight,jdbcType=NUMERIC},
	</if>
	<if test="materialMakePeriod != null">
		MATERIAL_MAKE_PERIOD = #{materialMakePeriod,jdbcType=INTEGER},
	</if>
	<if test="materialProductionPeriod != null">
		MATERIAL_PRODUCTION_PERIOD = #{materialProductionPeriod,jdbcType=INTEGER},
	</if>
	<if test="extFlag != null and extFlag != null and extFlag != 0">
		ext_flag = #{extFlag,jdbcType=INTEGER},
	</if>
	<if test="extNewFlag != null and extNewFlag != null ">
		ext_flag = (ext_flag | #{extNewFlag,jdbcType=INTEGER}),
	</if>
	<if test="materialPicture != null">
		MATERIAL_PICTURE = #{materialPicture,jdbcType=BIGINT},
	</if>
	<if test="materialPrice != null">
		MATERIAL_PRICE = #{materialPrice,jdbcType=NUMERIC},
	</if>
	<if test="materialUnit != null">
		MATERIAL_UNIT = #{materialUnit,jdbcType=NVARCHAR},
	</if>
	<if test="custEmail != null">
		CUST_EMAIL = #{custEmail,jdbcType=NVARCHAR},
	</if>
	<if test="supplierEmail != null">
		SUPPLIER_EMAIL = #{supplierEmail,jdbcType=NVARCHAR},
	</if>
	<if test="sellingPoint != null">
		SELLING_POINT = #{sellingPoint,jdbcType=NVARCHAR},
	</if>
	<if test="sellingPointEn != null">
		SELLING_POINT_EN = #{sellingPointEn,jdbcType=NVARCHAR},
	</if>
	<if test="state != null">
		STATE = #{state,jdbcType=NVARCHAR},
	</if>
	<if test="smcStatus != null">
		SMC_STATUS = #{smcStatus,jdbcType=INTEGER},
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	</if>
    <if test="attribute3 != null">
        ATTRIBUTE3 = #{attribute3,jdbcType=NVARCHAR},
    </if>
    <if test="attribute4 != null">
        ATTRIBUTE4 = #{attribute4,jdbcType=NVARCHAR},
    </if>
    <if test="attribute5 != null">
        ATTRIBUTE5 = #{attribute5,jdbcType=NVARCHAR},
    </if>
	<if test="creationTime != null">
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="materialSource != null">
		MATERIAL_SOURCE = #{materialSource,jdbcType=NVARCHAR},
	</if>
	<if test="materialCategory != null">
		MATERIAL_CATEGORY = #{materialCategory,jdbcType=NVARCHAR},
	</if>
	<if test="overLimit != null">
		OVER_LIMIT = #{overLimit,jdbcType=BIT},
	</if>
	<if test="isLeftover != null">
		IS_LEFTOVER = #{isLeftover,jdbcType=BIT},
	</if>
	<if test="isPresale != null">
		IS_PRESALE = #{isPresale,jdbcType=BIT},
	</if>
	<if test="showStockQty != null">
		SHOW_STOCK_QTY = #{showStockQty,jdbcType=BIT},
	</if>
	<if test="pointMode != null">
		POINT_MODE = #{pointMode,jdbcType=BIT},
	</if>
	<if test="invisibleGroup != null">
		INVISIBLE_GROUP = #{invisibleGroup,jdbcType=NVARCHAR},
	</if>
	<if test="invisibleDealer != null">
		INVISIBLE_DEALER = #{invisibleDealer,jdbcType=NVARCHAR},
	</if>
	<if test="pointType != null">
		POINT_TYPE = #{pointType,jdbcType=NVARCHAR},
	</if>
	<if test="materialAfterSalesInfo != null">
		MATERIAL_AFTER_SALES_INFO = #{materialAfterSalesInfo,jdbcType=NVARCHAR},
	</if>
	</set>
	where ID = #{id,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialVo">
	update wx_t_material
	set MATERIAL_CODE = #{materialCode,jdbcType=NVARCHAR},
	SMC_MATERIAL_CODE = #{smcMaterialCode,jdbcType=NVARCHAR},
	MATERIAL_TYPE = #{materialType,jdbcType=NVARCHAR},
	MATERIAL_NAME = #{materialName,jdbcType=NVARCHAR},
	MATERIAL_NAME_EN = #{materialNameEn,jdbcType=NVARCHAR},
	MATERIAL_DESC = #{materialDesc,jdbcType=NVARCHAR},
	SMC_MATERIAL_CATEGORY = #{smcMaterialCategory,jdbcType=NVARCHAR},
	MATERIAL_SIZEL = #{materialSizel,jdbcType=INTEGER},
	MATERIAL_SIZEW = #{materialSizew,jdbcType=INTEGER},
	MATERIAL_SIZEH = #{materialSizeh,jdbcType=INTEGER},
	MATERIAL_WEIGHT = #{materialWeight,jdbcType=NUMERIC},
	MATERIAL_MAKE_PERIOD = #{materialMakePeriod,jdbcType=INTEGER},
	MATERIAL_PRODUCTION_PERIOD = #{materialProductionPeriod,jdbcType=INTEGER},
	MATERIAL_PICTURE = #{materialPicture,jdbcType=BIGINT},
	MATERIAL_PRICE = #{materialPrice,jdbcType=NUMERIC},
	MATERIAL_UNIT = #{materialUnit,jdbcType=NVARCHAR},
	CUST_EMAIL = #{custEmail,jdbcType=NVARCHAR},
	SUPPLIER_EMAIL = #{supplierEmail,jdbcType=NVARCHAR},
	SELLING_POINT = #{sellingPoint,jdbcType=NVARCHAR},
	SELLING_POINT_EN = #{sellingPointEn,jdbcType=NVARCHAR},
	STATE = #{state,jdbcType=NVARCHAR},
	SMC_STATUS = #{smcStatus,jdbcType=INTEGER},
	DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
    ATTRIBUTE3 = #{attribute3,jdbcType=NVARCHAR},
    ATTRIBUTE4 = #{attribute4,jdbcType=NVARCHAR},
    ATTRIBUTE5 = #{attribute5,jdbcType=NVARCHAR},
	CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	MATERIAL_SOURCE = #{materialSource,jdbcType=NVARCHAR},
	MATERIAL_CATEGORY = #{materialCategory,jdbcType=NVARCHAR},
	OVER_LIMIT = #{overLimit,jdbcType=BIT},
	IS_LEFTOVER = #{isLeftover,jdbcType=BIT},
	IS_PRESALE = #{isPresale,jdbcType=BIT},
	SHOW_STOCK_QTY = #{showStockQty,jdbcType=BIT},
	POINT_MODE = #{pointMode,jdbcType=BIT},
	INVISIBLE_GROUP = #{invisibleGroup,jdbcType=NVARCHAR},
	INVISIBLE_DEALER = #{invisibleDealer,jdbcType=NVARCHAR},
	POINT_TYPE = #{pointType,jdbcType=NVARCHAR},
	MATERIAL_AFTER_SALES_INFO = #{materialAfterSalesInfo,jdbcType=NVARCHAR}
	where ID = #{id,jdbcType=BIGINT}
</update>

<!-- 分页查询物料列表 -->
<select id="selectMaterial4Pagination" parameterType="com.chevron.material.model.MaterialParams" resultMap="BaseResultMap">
	<![CDATA[
	select m.*,di.dic_item_name material_type_name,
	STOCK_QTY = (select sum(mi.STOCK_QTY) from wx_t_material_inventory mi where mi.MATERIAL_ID=m.ID and mi.DELETE_FLAG != 1),
	VIRTUAL_STOCK_QTY = (select sum(mi.VIRTUAL_STOCK_QTY) from wx_t_material_inventory mi where mi.MATERIAL_ID=m.ID and mi.DELETE_FLAG != 1),
	MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path(''))
	from wx_t_material m
	left join wx_t_dic_item di on di.dic_type_code = 'material.type' and di.dic_item_code = m.material_type
	where m.delete_flag=0
	]]>
	<if test="materialCode != null" >
	and m.material_code like '%'+#{materialCode}+'%'
	</if>
	<if test="materialType != null" >
	and m.material_type = #{materialType}
	</if>
	<if test="materialName != null" >
	and m.material_name like '%'+#{materialName}+'%'
	</if>
	<if test="materialDesc != null" >
	and m.material_desc like '%'+#{materialDesc}+'%'
	</if>
	<if test="pointMode" >
	and m.point_mode = 1
		<if test="pointType != null">
		and m.point_type = #{pointType}
		</if>
	</if>
	<if test="pointMode == null or pointMode == false">
	and (m.point_mode = 0 or m.point_mode is null)
	</if>

	<if test="is4picking" >
	and m.smc_material_code is not null
	and m.state = 'SYNCED'
	</if>

    <if test="is4picking" >
        and m.smc_material_code is not null
        and m.state = 'SYNCED'
    </if>

    <!-- 只有雪佛龙管理员能看到优惠券的物料信息 -->
    <if test="isChevronAdmin == null or isChevronAdmin == false">
        and m.MATERIAL_TYPE != 'COUPON'
    </if>

	<if test="keyword != null" >
	and (
	di.dic_item_name like '%'+#{keyword}+'%'
	or m.material_code like '%'+#{keyword}+'%'
	or m.material_name like '%'+#{keyword}+'%'
	or m.material_desc like '%'+#{keyword}+'%'
	)
	</if>
</select>
<select id="selectNotEnoughMaterialInventory" parameterType="map" resultMap="BaseResultMap">
	select inv.* from (select m.*,di.dic_item_name material_type_name,
	STOCK_QTY = (select sum(mi.STOCK_QTY) from wx_t_material_inventory mi left join wx_t_material_warehouse w ON w.ID=mi.WAREHOUSE_ID where mi.MATERIAL_ID=m.ID
	<if test="warehouse != null">
	and w.material_source=#{warehouse}
	</if>
	),
	VIRTUAL_STOCK_QTY = (select sum(mi.VIRTUAL_STOCK_QTY) from wx_t_material_inventory mi where mi.MATERIAL_ID=m.ID),
	MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path(''))
	from wx_t_material m
	left join wx_t_dic_item di on di.dic_type_code = 'material.type' and di.dic_item_code = m.material_type
	where m.delete_flag=0
	<if test="pointMode" >
	and m.point_mode = 1
	</if>
	<if test="pointMode == null or pointMode == false">
	and (m.point_mode = 0 or m.point_mode is null)
	</if>
	) inv where 1=1

	<if test="warningNum != null" >
	and inv.stock_qty &lt; #{warningNum}
	</if>
	<if test="pointType != null" >
	and inv.point_type = #{pointType}
	</if>
</select>
<!-- 分页查询物料列表, 用于物料申请时挑选 -->
 <select id="selectMaterialPickingList4Pagination" parameterType="com.chevron.material.model.MaterialParams" resultMap="BaseResultMap">
select t1.id ID, md1.spu MATERIAL_CODE, md1.material_tag [MATERIAL_TYPE], md1.material_name [MATERIAL_NAME],
		md1.material_desc [MATERIAL_DESC],t1.point_value [MATERIAL_PRICE],md1.material_unit [MATERIAL_UNIT],
		s1.supplier_name [MATERIAL_SOURCE], t1.create_time CREATION_TIME,0 IS_LEFTOVER, 0 IS_PRESALE, t1.point_type_id ATTRIBUTE1,
		 m1.material_code MATERIAL_SKU_CODE,m1.supplier_id WAREHOUSE_ID, s1.supplier_name WAREHOUSE_NAME, 
 case when len(md1.material_color) > 0 then md1.material_color else '默认' end MATERIAL_SKU_PROP_COLOR, 
	case when len(md1.material_size)>0 then md1.material_size else '均码' end  MATERIAL_SKU_PROP_SIZE,i1.current_quantity STOCK_QTY,
	i1.current_quantity VIRTUAL_STOCK_QTY, 
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) material_type_name
		,(select top 1 af.ATT_ID from  WX_ATT_FILE af where af.SOURCE_ID = m1.id and af.SOURCE_TYPE = '58') MATERIAL_IMAGES 
		  from wx_t_point_material t1
		  left join wx_t_material2021 m1 on t1.material_id=m1.id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_promotion_point pp1 on pp1.id=t1.point_type_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
		 where t1.delete_flag=0 and m1.del_flag=0 and m1.material_status=1 and md1.del_flag=0 and md1.material_status=1
		 and i1.current_quantity>0
        <if test="materialCode != null">
            and md1.spu like '%'+#{materialCode}+'%'
        </if>
        <if test="materialType != null and materialType != '' ">
            and md1.material_tag = #{materialType}
        </if>
        <if test="materialTypeList != null">
            and md1.material_tag in
            <foreach collection="materialTypeList" item="item" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="materialName != null">
            and md1.material_name like '%'+#{materialName}+'%'
        </if>
        <if test="materialDesc != null">
            and md1.material_desc like '%'+#{materialDesc}+'%'
        </if>
            <if test="pointType != null">
                and (case when pp1.predefine_flag=1 then pp1.point_type else pp1.point_code end) = #{pointType}
            </if>

        <if test="keyword != null and keyword != ''">
            and (
            md1.spu like '%'+#{keyword}+'%'
            or md1.material_name like '%'+#{keyword}+'%'
            or md1.material_desc like '%'+#{keyword}+'%'
            )
        </if>
        <if test="visibleDealer != null">
            and (t1.available_partners is null or t1.available_partners in ('', '-1') or t1.available_partners like '%${visibleDealer}%')
        </if>
		<if test="visibleDealers != null">
			and (t1.available_partners is null or t1.available_partners in ('', '-1') or
			<foreach collection="visibleDealers" item="item" open="(" separator="or" close=")">
				t1.available_partners like '%${item}%'
			</foreach>
			)
		</if>
</select>


    <!-- 分页查询物料列表, 用于物料申请时挑选
    <select id="selectMaterialListByParams" parameterType="com.chevron.material.model.MaterialParams"
            resultMap="BaseResultMap">
        <![CDATA[
        select ma.*
        from (
            select m.*,di.dic_item_name material_type_name,
            MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
            FIRST_MATERIAL_IMAGE = (SELECT IMAGE_ID FROM wx_t_material_image WHERE MATERIAL_ID = m.ID and IMAGE_SEQ = 0)
            from wx_t_material m
            left join wx_t_dic_item di on di.dic_type_code = 'material.type' and di.dic_item_code = m.material_type
            where m.delete_flag=0
            and m.smc_status = 1

    ]]>
        <if test="isAdmin != true">
            and (m.attribute1 is null or m.attribute1 = 'ENABLE' or m.attribute1 = '')
        </if>

        <if test="materialCode != null">
            and m.material_code like '%'+#{materialCode}+'%'
        </if>
        <if test="materialType != null and materialType != '' ">
            and m.material_type = #{materialType}
        </if>
        <if test="materialTypeList != null">
            and m.material_type in
            <foreach collection="materialTypeList" item="item" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="materialName != null">
            and m.material_name like '%'+#{materialName}+'%'
        </if>
        <if test="materialDesc != null">
            and m.material_desc like '%'+#{materialDesc}+'%'
        </if>
        <if test="is4picking">
            and m.smc_material_code is not null
            and m.state = 'SYNCED'
        </if>
        <if test="pointMode">
            and m.POINT_MODE = 1
            <if test="pointType != null">
                and m.point_type = #{pointType}
            </if>
        </if>

        <if test="pointMode != true">
            and (m.POINT_MODE = 0 or m.POINT_MODE is null)
        </if>

        <if test="keyword != null">
            and (
            di.dic_item_name like '%'+#{keyword}+'%'
            or m.material_code like '%'+#{keyword}+'%'
            or m.material_name like '%'+#{keyword}+'%'
            or m.material_desc like '%'+#{keyword}+'%'
            )
        </if>
        <if test="visibleDealer != null">
            and (ISNULL(m.INVISIBLE_DEALER,'') = '' or m.INVISIBLE_DEALER like '%${visibleDealer}%')
        </if>
		<if test="visibleDealers != null">
			and (ISNULL(m.INVISIBLE_DEALER,'') = '' or
			<foreach collection="visibleDealers" item="item" open="(" separator="or" close=")">
				m.INVISIBLE_DEALER like '%${item}%'
			</foreach>
			)
		</if>
        ) ma inner join
        (select s.MATERIAL_ID,sum(VIRTUAL_STOCK_QTY) as VIRTUAL_STOCK_QTY from
        wx_t_material_sku s
        left join wx_t_material_inventory mi
        on mi.MATERIAL_SKU_CODE = s.MATERIAL_SKU_CODE
        left join wx_t_material_warehouse mw
        on mw.ID = mi.WAREHOUSE_ID and s.DELETE_FLAG = 0
        where 1=1
        <if test="pointMode != true">
            and mw.MATERIAL_SOURCE in ('enjoygifts','sunrain')
        </if>
        <if test="pointMode">
            and mw.MATERIAL_SOURCE not in ('enjoygifts','sunrain')
        </if>
        group by s.MATERIAL_ID) t1 on ma.id = t1.MATERIAL_ID
        where ((ma.IS_LEFTOVER = 1 and t1.VIRTUAL_STOCK_QTY > 0 ) or ma.IS_LEFTOVER = 0 or ma.IS_LEFTOVER is null)
    </select> -->

    <select id="selectMaterialListByParams" parameterType="com.chevron.material.model.MaterialParams"
            resultMap="BaseResultMap">
 select max(md1.id) ID, md1.spu MATERIAL_CODE, md1.material_tag [MATERIAL_TYPE], max(md1.material_name) [MATERIAL_NAME],
		max(md1.material_desc) [MATERIAL_DESC],max(t1.point_value) [MATERIAL_PRICE],max(md1.material_unit) [MATERIAL_UNIT],
		max(s1.supplier_name) [MATERIAL_SOURCE], max(t1.create_time) CREATION_TIME,0 IS_LEFTOVER, 0 IS_PRESALE, t1.point_type_id ATTRIBUTE1,
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) material_type_name
		,(select max(af.ATT_ID) from wx_t_material2021_dictionary md2 left join wx_t_material2021 m2 on md2.id=m2.dictionary_id 
				left join WX_ATT_FILE af on af.SOURCE_ID = m2.id and af.SOURCE_TYPE = '58' where md2.spu=md1.spu) MATERIAL_IMAGES 
		  from wx_t_point_material t1
		  left join wx_t_material2021 m1 on t1.material_id=m1.id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_promotion_point pp1 on pp1.id=t1.point_type_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
		 where t1.delete_flag=0 and m1.del_flag=0 and m1.material_status=1 and md1.del_flag=0 and md1.material_status=1
		 and i1.current_quantity>0
        <if test="materialCode != null">
            and md1.spu like '%'+#{materialCode}+'%'
        </if>
        <if test="materialType != null and materialType != '' ">
            and md1.material_tag = #{materialType}
        </if>
        <if test="materialTypeList != null">
            and md1.material_tag in
            <foreach collection="materialTypeList" item="item" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="materialName != null">
            and md1.material_name like '%'+#{materialName}+'%'
        </if>
        <if test="materialDesc != null">
            and md1.material_desc like '%'+#{materialDesc}+'%'
        </if>
            <if test="pointType != null">
                and (case when pp1.predefine_flag=1 then pp1.point_type else pp1.point_code end) = #{pointType}
            </if>

        <if test="keyword != null and keyword != ''">
            and (
            md1.spu like '%'+#{keyword}+'%'
            or md1.material_name like '%'+#{keyword}+'%'
            or md1.material_desc like '%'+#{keyword}+'%'
            )
        </if>
        <if test="visibleDealer != null">
            and (t1.available_partners is null or t1.available_partners in ('', '-1') or t1.available_partners like '%${visibleDealer}%')
        </if>
		<if test="visibleDealers != null">
			and (t1.available_partners is null or t1.available_partners in ('', '-1') or
			<foreach collection="visibleDealers" item="item" open="(" separator="or" close=")">
				t1.available_partners like '%${item}%'
			</foreach>
			)
		</if>
		<if test="retailerId != null">
		and exists (select 1 from wx_t_properties rp1 where rp1.codetype='MaterialPlatform.PP.Retailer.maxPoints' and t1.point_value&lt;convert(float,rp1.code))
		</if>
		 group by md1.spu,md1.material_tag,t1.point_type_id
    </select>

    <!-- 分页查询物料列表, 用于物料申请时挑选 -->
    <select id="selectPromotionGiftListByParams" parameterType="com.chevron.material.model.MaterialParams"
            resultMap="BaseResultMap">
 select max(md1.id) ID, md1.spu MATERIAL_CODE, md1.material_tag [MATERIAL_TYPE], max(md1.material_name) [MATERIAL_NAME],
		max(md1.material_desc) [MATERIAL_DESC],max(t1.point_value) [MATERIAL_PRICE],max(md1.material_unit) [MATERIAL_UNIT],
		max(s1.supplier_name) [MATERIAL_SOURCE], max(t1.create_time) CREATION_TIME,0 IS_LEFTOVER, 0 IS_PRESALE, t1.point_type_id ATTRIBUTE1,
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) material_type_name
		,(select max(af.ATT_ID) from wx_t_material2021_dictionary md2 left join wx_t_material2021 m2 on md2.id=m2.dictionary_id 
				left join WX_ATT_FILE af on af.SOURCE_ID = m2.id and af.SOURCE_TYPE = '58' where md2.spu=md1.spu) MATERIAL_IMAGES,
		t.available_quantity,t.version_no 
        from wx_t_promotion_gift_pool t 
		  left join wx_t_promotion_point pp1 on pp1.point_code=t.point_code
        	left join wx_t_point_material t1 on pp1.id=t1.point_type_id
		  left join wx_t_material2021 m1 on t1.material_id=m1.id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
        where t.point_code = #{pointType} and t.available_quantity > 0 and t.partner_id = #{partnerId}
        and md1.spu=t.material_code and t.material_id=-1
        <if test="materialCode != null">
            and md1.spu like '%'+#{materialCode}+'%'
        </if>
        <if test="materialType != null and materialType != '' ">
            and md1.material_tag = #{materialType}
        </if>
        <if test="materialName != null">
            and md1.material_name like '%'+#{materialName}+'%'
        </if>
        <if test="materialDesc != null">
            and md1.material_desc like '%'+#{materialDesc}+'%'
        </if>
            <if test="pointType != null">
                and pp1.point_code = #{pointType}
            </if>

        <if test="keyword != null and keyword != ''">
            and (
            md1.spu like '%'+#{keyword}+'%'
            or md1.material_name like '%'+#{keyword}+'%'
            or md1.material_desc like '%'+#{keyword}+'%'
            )
        </if>
		 group by md1.spu,md1.material_tag,t1.point_type_id,t.available_quantity,t.version_no
    </select>

    <!-- <select id="selectCouponGiftListByParams" parameterType="com.chevron.material.model.MaterialParams"
            resultMap="BaseResultMap">
        SELECT m.*,
        di.dic_item_name material_type_name,
        MATERIAL_IMAGES =(SELECT convert(varchar(20), IMAGE_ID) + ',' + convert(varchar(20), IMAGE_SEQ) + '|'
        FROM wx_t_material_image
        WHERE MATERIAL_ID = m.ID
        for XML path ('')),
        FIRST_MATERIAL_IMAGE=(SELECT IMAGE_ID FROM wx_t_material_image WHERE MATERIAL_ID = m.ID AND IMAGE_SEQ = 0)
        from
        (
        select c2.material_id
        from wx_t_promotion_gift_pool p
        INNER JOIN wx_t_material m1 ON p.material_id = m1.ID
        inner join wx_t_coupon_option c1 on c1.coupon_id = m1.id
        inner join wx_t_coupon_option_detail c2 on c2.coupon_option_id = c1.id
        where m1.material_type = 'COUPON'
        and p.partner_id = #{partnerId}
        and p.point_code = #{pointType}
        and c1.coupon_id in
        <foreach collection="couponApplyParams" item="coupon" open="(" separator="," close=")">
            '${coupon.couponId}'
        </foreach>
        and c1.id in
        <foreach collection="couponApplyParams" item="coupon" open="(" separator="," close=")">
            <foreach collection="coupon.options" item="optionId" separator=",">
                '${optionId}'
            </foreach>
        </foreach>
        group by c2.material_id
        ) t
        left join wx_t_material m on m.ID = t.material_id
        LEFT JOIN wx_t_dic_item di ON di.dic_type_code = 'material.type' AND di.dic_item_code = m.material_type
        <where>
            <if test="materialType != null and materialType != '' ">
                and m.material_type = #{materialType}
            </if>
            <if test="materialTypeList != null">
                and m.material_type in
                <foreach collection="materialTypeList" item="item" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="materialName != null">
                and m.material_name like '%'+#{materialName}+'%'
            </if>
            <if test="materialDesc != null">
                and m.material_desc like '%'+#{materialDesc}+'%'
            </if>
            <if test="keyword != null">
                and (
                di.dic_item_name like '%'+#{keyword}+'%'
                or m.material_code like '%'+#{keyword}+'%'
                or m.material_name like '%'+#{keyword}+'%'
                or m.material_desc like '%'+#{keyword}+'%'
                )
            </if>
        </where>
    </select> -->

    <!-- 分页查询物料列表, 用于物料申请时挑选 -->
    <select id="selectMaterialInventoryByMaterialCodes" parameterType="map"
            resultMap="BaseResultMap">
 select t1.id ID, md1.spu MATERIAL_CODE, m1.material_code MATERIAL_SKU_CODE,m1.supplier_id WAREHOUSE_ID, s1.supplier_name WAREHOUSE_NAME, 
 case when len(md1.material_color) > 0 then md1.material_color else '默认' end MATERIAL_SKU_PROP_COLOR, 
	case when len(md1.material_size)>0 then md1.material_size else '均码' end  MATERIAL_SKU_PROP_SIZE,i1.current_quantity STOCK_QTY,
	i1.current_quantity VIRTUAL_STOCK_QTY, md1.material_tag [MATERIAL_TYPE], md1.material_name [MATERIAL_NAME]
		  from wx_t_point_material t1
		  left join wx_t_material2021 m1 on t1.material_id=m1.id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
		 where t1.delete_flag=0 and m1.del_flag=0 and m1.material_status=1 and md1.del_flag=0 and md1.material_status=1
		 and i1.current_quantity>0
		 and convert(nvarchar(20),t1.point_type_id)+ '/' + md1.spu in
            <foreach close=")" collection="codes" item="code" open="(" separator=",">
                #{code}
            </foreach>
   </select>

    <!-- 查询物料申请历史 -->
<select id="selectApplicationHistory" resultMap="AppHisVo" parameterType="com.chevron.material.model.MaterialAppHisParams" >
		SELECT
			maq.*,
			m.material_CODE,
			m.material_NAME,
			m.material_PICTURE,
			m.material_DESC,
			MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
			di.dic_item_name material_type_name,
			mi.STOCK_QTY,
			mi.VIRTUAL_STOCK_QTY
		FROM
			(SELECT
				base.material_id,
				base.material_sku_code,
				SUM (base.RECEIVED_QTY) APPLICATION_QTY,
				SUM (base.RECEIVED_QTY) RECEIVED_QTY,
				SUM (base.SHIPPING_QTY) SHIPPING_QTY,
				SUM (base.PENDING_QTY) PENDING_QTY,
				SUM (base.OUTBOUND_QTY) OUTBOUND_QTY
			FROM (select mad.material_id,mad.material_sku_code,
				case when ma.APPLICATION_STATUS IN ('RECEIVED') then mad.APPLICATION_QTY else 0 end RECEIVED_QTY,
				case when ma.APPLICATION_STATUS IN ('SHIPPING') then mad.APPLICATION_QTY else 0 end SHIPPING_QTY,
				case when ma.APPLICATION_STATUS IN ('READY','PENDING_ON_L1', 'PENDING_ON_L2') then mad.APPLICATION_QTY else 0 end PENDING_QTY,
				case when ma.APPLICATION_STATUS IN ('OUTBOUND') then mad.APPLICATION_QTY else 0 end OUTBOUND_QTY
				from wx_t_material_application_detail mad
				LEFT JOIN wx_t_material_application ma ON mad.APPLICATION_ID = ma.ID
				WHERE ma.delete_flag = 0 AND mad.delete_flag = 0
				<if test="partnerId != null" >
					and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
				</if>
				) base
			GROUP BY all material_id,material_sku_code
		) maq
		left join wx_t_material m
		on maq.material_ID = m.ID
		left join wx_t_dic_item di
		on di.dic_type_code = 'material.type'
		and di.dic_item_code = m.material_type
		left join wx_t_material_inventory mi
		on mi.material_sku_code = maq.material_sku_code
		where 1=1
		<if test="materialCode != null" >
		and m.material_code like '%'+#{materialCode}+'%'
		</if>
		<if test="materialType != null" >
		and m.material_type = #{materialType}
		</if>
		<if test="materialName != null" >
		and m.material_name like '%'+#{materialName}+'%'
		</if>
		<if test="materialDesc != null" >
		and m.material_desc like '%'+#{materialDesc}+'%'
		</if>
		<if test="keyword != null" >
			and (
			di.dic_item_name like '%'+#{keyword}+'%'
			or m.material_code like '%'+#{keyword}+'%'
			or m.material_name like '%'+#{keyword}+'%'
			or m.material_desc like '%'+#{keyword}+'%'
			)
		</if>
	</select>

<!-- 查询物料申请历史详情 -->
<select id="selectApplicationHistoryDetail" resultMap="AppHisDetailVo" parameterType="com.chevron.material.model.MaterialAppHisParams" >
	select
		mad.APPLICATION_ID,
		mad.MATERIAL_ID,
		mad.MATERIAL_SKU_CODE,
		mad.APPLICATION_QTY,
		ma.APPLICATION_TIME,
		ma.APPLICATION_ORG_ID,
		ma.APPLICATION_PERSON_ID,
		u.ch_name APPLICATION_PERSON_NAME,
		o.organization_name APPLICATION_ORG_NAME

	from wx_t_material_application_detail mad
	LEFT JOIN wx_t_material_application ma
	on mad.APPLICATION_ID = ma.ID
	<if test="partnerId != null" >
		and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
	</if>
	LEFT JOIN wx_t_organization o
	on ma.APPLICATION_ORG_ID = o.id
	left join wx_t_user u
	on ma.APPLICATION_PERSON_ID = u.user_id
	where 1=1

	<if test='type == null or type =="RECEIVED" or type =="" ' >
	and ma.APPLICATION_STATUS in ('RECEIVED')
	</if>

	<if test='type =="SHIPPING"' >
	and ma.APPLICATION_STATUS in ('SHIPPING')
	</if>

	<if test='type =="OUTBOUND"' >
	and ma.APPLICATION_STATUS in ('OUTBOUND')
	</if>

	<if test='type =="PENDING"' >
	and ma.APPLICATION_STATUS IN ('READY','PENDING_ON_L1', 'PENDING_ON_L2')
	</if>
	and ma.delete_flag = 0
	and mad.delete_flag = 0
	<if test="partnerId != null" >
		and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
	</if>
	and mad.material_ID = #{materialId,jdbcType=BIGINT}
	and mad.material_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
</select>

<!-- 查询物料申请历史详情,按月统计 -->
<select id="selectApplicationHistoryDetailMonthly" resultMap="AppHisDetailMonthlyVo">
	select base.*,
		o.organization_name APPLICATION_ORG_NAME
		from (
			select
				mad.MATERIAL_ID,
				convert(varchar(7),APPLICATION_TIME,120) APPLICATION_MONTH,
				APPLICATION_ORG_ID,
				sum(APPLICATION_QTY) APPLICATION_QTY
			from wx_t_material_application_detail mad
			LEFT JOIN wx_t_material_application ma
			on mad.APPLICATION_ID = ma.ID
			where 1=1
			<if test='type == null or type =="RECEIVED" or type =="" ' >
			and ma.APPLICATION_STATUS in ('RECEIVED')
			</if>

			<if test='type =="SHIPPING"' >
			and ma.APPLICATION_STATUS in ('SHIPPING')
			</if>

			<if test='type =="OUTBOUND"' >
			and ma.APPLICATION_STATUS in ('OUTBOUND')
			</if>

			<if test='type =="PENDING"' >
			and ma.APPLICATION_STATUS IN ('READY','PENDING_ON_L1', 'PENDING_ON_L2')
			</if>
			<if test="partnerId != null" >
			and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
			</if>
			and mad.material_ID = #{materialId,jdbcType=BIGINT}
			and mad.material_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
			and convert(varchar(4),ma.APPLICATION_TIME,120) = #{year,jdbcType=BIGINT}
			group by convert(varchar(7),ma.APPLICATION_TIME,120),MATERIAL_ID, APPLICATION_ORG_ID
			) base
		LEFT JOIN wx_t_organization o
		on base.APPLICATION_ORG_ID = o.id
</select>

<!-- 查询物料申请历史详情之年份列表 -->
<select id="selectApplicationHistoryDetailYear" resultType="java.lang.Long">
	select distinct convert(varchar(4),APPLICATION_TIME,120) year
	from wx_t_material_application_detail mad
	LEFT JOIN wx_t_material_application ma
	on mad.APPLICATION_ID = ma.ID
	where 1=1
	<if test='type == null or type =="RECEIVED" or type =="" ' >
	and ma.APPLICATION_STATUS in ('RECEIVED')
	</if>

	<if test='type =="SHIPPING"' >
	and ma.APPLICATION_STATUS in ('SHIPPING')
	</if>

	<if test='type =="OUTBOUND"' >
	and ma.APPLICATION_STATUS in ('OUTBOUND')
	</if>

	<if test='type =="PENDING"' >
	and ma.APPLICATION_STATUS IN ('READY','PENDING_ON_L1', 'PENDING_ON_L2')
	</if>
	and mad.MATERIAL_ID = #{materialId,jdbcType=BIGINT}
	and mad.material_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR}
	<if test="partnerId != null" >
	and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
	</if>
</select>

<select id="selectPromotionMaterial" parameterType="map" resultMap="BaseResultMap">
 select max(md1.id) ID, md1.spu MATERIAL_CODE, md1.material_tag [MATERIAL_TYPE], max(md1.material_name) [MATERIAL_NAME],
		max(md1.material_desc) [MATERIAL_DESC],max(t1.point_value) [MATERIAL_PRICE],max(md1.material_unit) [MATERIAL_UNIT],
		max(s1.supplier_name) [MATERIAL_SOURCE], max(t1.create_time) CREATION_TIME,0 IS_LEFTOVER, 0 IS_PRESALE, t1.point_type_id ATTRIBUTE1,
		(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='material.type' and di1.dic_item_code=md1.material_tag) material_type_name
		,(select max(af.ATT_ID) from wx_t_material2021_dictionary md2 left join wx_t_material2021 m2 on md2.id=m2.dictionary_id 
				left join WX_ATT_FILE af on af.SOURCE_ID = m2.id and af.SOURCE_TYPE = '58' where md2.spu=md1.spu) MATERIAL_IMAGES
		  from wx_t_point_material t1
		  left join wx_t_material2021 m1 on t1.material_id=m1.id
		  left join wx_t_material2021_dictionary md1 on md1.id=m1.dictionary_id
		  left join wx_t_supplier s1 on s1.id=m1.supplier_id
		  left join wx_t_promotion_point pp1 on pp1.id=t1.point_type_id
		  left join wx_t_inventory2021 i1 on i1.body_type='supplier' and i1.body_key=m1.supplier_id and i1.inventory_type='material' 
		  	and i1.product_key=m1.material_code and i1.delete_flag=0
		 where t1.delete_flag=0 and m1.del_flag=0 and m1.material_status=1 and md1.del_flag=0 and md1.material_status=1
		 and i1.current_quantity>0
        <if test="materialCode != null">
            and md1.spu like '%'+#{materialCode}+'%'
        </if>
        <if test="materialType != null and materialType != '' ">
            and md1.material_tag = #{materialType}
        </if>
        <if test="materialName != null">
            and md1.material_name like '%'+#{materialName}+'%'
        </if>
        <if test="materialDesc != null">
            and md1.material_desc like '%'+#{materialDesc}+'%'
        </if>
            <if test="pointType != null">
                and pp1.point_code = #{pointType}
            </if>

        <if test="keyword != null and keyword != ''">
            and (
            md1.spu like '%'+#{keyword}+'%'
            or md1.material_name like '%'+#{keyword}+'%'
            or md1.material_desc like '%'+#{keyword}+'%'
            )
        </if>
		 group by md1.spu,md1.material_tag,t1.point_type_id
	</select>

    <!-- 分页查询物料列表, 用于物料申请时挑选 -->
    <select id="materialCtrl" parameterType="com.chevron.material.model.MaterialParams"
            resultMap="BaseResultMap">
        <![CDATA[
        select ma.*
        from (
            select m.*,di.dic_item_name material_type_name,
            MATERIAL_IMAGES =(select convert(varchar(20),IMAGE_ID) +',' + convert(varchar(20),IMAGE_SEQ ) + '|' from wx_t_material_image where MATERIAL_ID = m.ID for XML path('')),
            FIRST_MATERIAL_IMAGE = (SELECT IMAGE_ID FROM wx_t_material_image WHERE MATERIAL_ID = m.ID and IMAGE_SEQ = 0)
            from wx_t_material m
            left join wx_t_dic_item di on di.dic_type_code = 'material.type' and di.dic_item_code = m.material_type
            where m.delete_flag=0 and m.point_type = #{pointType} and m.material_type != 'COUPON'
        ]]>
        <if test="materialId != null">
            and m.id = #{materialId}
        </if>
        <if test="materialType != null and materialType != '' ">
            and m.material_type = #{materialType}
        </if>
        <if test="materialType != null and materialType != '' ">
            and m.material_type = #{materialType}
        </if>
        <if test="materialTypeList != null">
            and m.material_type in
            <foreach collection="materialTypeList" item="item" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="keyword != null">
            and (
            di.dic_item_name like '%'+#{keyword}+'%'
            or m.material_code like '%'+#{keyword}+'%'
            or m.material_name like '%'+#{keyword}+'%'
            or m.material_desc like '%'+#{keyword}+'%'
            )
        </if>
        ) ma
    </select>
</mapper>