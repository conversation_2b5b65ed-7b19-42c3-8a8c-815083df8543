<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.material.dao.WXTMaterialWarehouseSupportVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialWarehouseSupportVo" >
    <result column="WAREHOUSE_ID" property="warehouseId" jdbcType="BIGINT" />
    <result column="SUPPORT_ID" property="supportId" jdbcType="BIGINT" />
    <result column="SUPPORT_CODE" property="supportCode" jdbcType="NVARCHAR" />
    <result column="SUPPORT_NAME" property="supportName" jdbcType="NVARCHAR" />
    <result column="SUPPORT_TYPE" property="supportType" jdbcType="NVARCHAR" />
    <result column="COMENTS" property="coments" jdbcType="NVARCHAR" />
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
    <result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
    <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
    <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    WAREHOUSE_ID, SUPPORT_ID, SUPPORT_CODE, SUPPORT_NAME, SUPPORT_TYPE, COMENTS, DELETE_FLAG, 
    ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material.model.WXTMaterialWarehouseSupportVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_warehouse_support
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialWarehouseSupportVoExample" >
    delete from wx_t_material_warehouse_support
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialWarehouseSupportVo" >
    insert into wx_t_material_warehouse_support (WAREHOUSE_ID, SUPPORT_ID, SUPPORT_CODE, 
      SUPPORT_NAME, SUPPORT_TYPE, COMENTS, 
      DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, 
      CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
      LAST_UPDATED_BY)
    values (#{warehouseId,jdbcType=BIGINT}, #{supportId,jdbcType=BIGINT}, #{supportCode,jdbcType=NVARCHAR}, 
      #{supportName,jdbcType=NVARCHAR}, #{supportType,jdbcType=NVARCHAR}, #{coments,jdbcType=NVARCHAR}, 
      #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialWarehouseSupportVo" >
    insert into wx_t_material_warehouse_support
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warehouseId != null" >
        WAREHOUSE_ID,
      </if>
      <if test="supportId != null" >
        SUPPORT_ID,
      </if>
      <if test="supportCode != null" >
        SUPPORT_CODE,
      </if>
      <if test="supportName != null" >
        SUPPORT_NAME,
      </if>
      <if test="supportType != null" >
        SUPPORT_TYPE,
      </if>
      <if test="coments != null" >
        COMENTS,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warehouseId != null" >
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="supportId != null" >
        #{supportId,jdbcType=BIGINT},
      </if>
      <if test="supportCode != null" >
        #{supportCode,jdbcType=NVARCHAR},
      </if>
      <if test="supportName != null" >
        #{supportName,jdbcType=NVARCHAR},
      </if>
      <if test="supportType != null" >
        #{supportType,jdbcType=NVARCHAR},
      </if>
      <if test="coments != null" >
        #{coments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_material_warehouse_support
    <set >
      <if test="record.warehouseId != null" >
        WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
      </if>
      <if test="record.supportId != null" >
        SUPPORT_ID = #{record.supportId,jdbcType=BIGINT},
      </if>
      <if test="record.supportCode != null" >
        SUPPORT_CODE = #{record.supportCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.supportName != null" >
        SUPPORT_NAME = #{record.supportName,jdbcType=NVARCHAR},
      </if>
      <if test="record.supportType != null" >
        SUPPORT_TYPE = #{record.supportType,jdbcType=NVARCHAR},
      </if>
      <if test="record.coments != null" >
        COMENTS = #{record.coments,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_material_warehouse_support
    set WAREHOUSE_ID = #{record.warehouseId,jdbcType=BIGINT},
      SUPPORT_ID = #{record.supportId,jdbcType=BIGINT},
      SUPPORT_CODE = #{record.supportCode,jdbcType=NVARCHAR},
      SUPPORT_NAME = #{record.supportName,jdbcType=NVARCHAR},
      SUPPORT_TYPE = #{record.supportType,jdbcType=NVARCHAR},
      COMENTS = #{record.coments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>