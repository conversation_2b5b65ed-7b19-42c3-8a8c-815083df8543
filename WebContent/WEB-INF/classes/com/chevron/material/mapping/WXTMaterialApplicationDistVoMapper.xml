<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationDistVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationDistVo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="APPLICATION_ID" jdbcType="BIGINT" property="applicationId" />
    <result column="SHIPMENT_ID" jdbcType="BIGINT" property="shipmentId" />
    <result column="MATERIAL_ID" jdbcType="BIGINT" property="materialId" />
    <result column="MATERIAL_SKU_CODE" jdbcType="NVARCHAR" property="materialSkuCode" />
    <result column="MATERIAL_QTY" jdbcType="BIGINT" property="materialQty" />
    <result column="COMMENTS" jdbcType="NVARCHAR" property="comments" />
    <result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
    <result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
    <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID, APPLICATION_ID, SHIPMENT_ID, MATERIAL_ID, MATERIAL_SKU_CODE, MATERIAL_QTY, COMMENTS, 
    DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
    LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_material_application_dist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_material_application_dist
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_material_application_dist
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVoExample">
    delete from wx_t_material_application_dist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVo">
    insert into wx_t_material_application_dist (ID, APPLICATION_ID, SHIPMENT_ID, 
      MATERIAL_ID, MATERIAL_SKU_CODE, MATERIAL_QTY, 
      COMMENTS, DELETE_FLAG, ATTRIBUTE1, 
      ATTRIBUTE2, CREATION_TIME, CREATED_BY, 
      LAST_UPDATE_TIME, LAST_UPDATED_BY)
    values (#{id,jdbcType=BIGINT}, #{applicationId,jdbcType=BIGINT}, #{shipmentId,jdbcType=BIGINT}, 
      #{materialId,jdbcType=BIGINT}, #{materialSkuCode,jdbcType=NVARCHAR}, #{materialQty,jdbcType=BIGINT}, 
      #{comments,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, 
      #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVo">
    insert into wx_t_material_application_dist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applicationId != null">
        APPLICATION_ID,
      </if>
      <if test="shipmentId != null">
        SHIPMENT_ID,
      </if>
      <if test="materialId != null">
        MATERIAL_ID,
      </if>
      <if test="materialSkuCode != null">
        MATERIAL_SKU_CODE,
      </if>
      <if test="materialQty != null">
        MATERIAL_QTY,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null">
        CREATION_TIME,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applicationId != null">
        #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="shipmentId != null">
        #{shipmentId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialSkuCode != null">
        #{materialSkuCode,jdbcType=NVARCHAR},
      </if>
      <if test="materialQty != null">
        #{materialQty,jdbcType=BIGINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVoExample" resultType="java.lang.Integer">
    select count(*) from wx_t_material_application_dist
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_material_application_dist
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applicationId != null">
        APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
      </if>
      <if test="record.shipmentId != null">
        SHIPMENT_ID = #{record.shipmentId,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.materialSkuCode != null">
        MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.materialQty != null">
        MATERIAL_QTY = #{record.materialQty,jdbcType=BIGINT},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null">
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null">
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null">
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_material_application_dist
    set ID = #{record.id,jdbcType=BIGINT},
      APPLICATION_ID = #{record.applicationId,jdbcType=BIGINT},
      SHIPMENT_ID = #{record.shipmentId,jdbcType=BIGINT},
      MATERIAL_ID = #{record.materialId,jdbcType=BIGINT},
      MATERIAL_SKU_CODE = #{record.materialSkuCode,jdbcType=NVARCHAR},
      MATERIAL_QTY = #{record.materialQty,jdbcType=BIGINT},
      COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVo">
    update wx_t_material_application_dist
    <set>
      <if test="applicationId != null">
        APPLICATION_ID = #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="shipmentId != null">
        SHIPMENT_ID = #{shipmentId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        MATERIAL_ID = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="materialSkuCode != null">
        MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR},
      </if>
      <if test="materialQty != null">
        MATERIAL_QTY = #{materialQty,jdbcType=BIGINT},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationDistVo">
    update wx_t_material_application_dist
    set APPLICATION_ID = #{applicationId,jdbcType=BIGINT},
      SHIPMENT_ID = #{shipmentId,jdbcType=BIGINT},
      MATERIAL_ID = #{materialId,jdbcType=BIGINT},
      MATERIAL_SKU_CODE = #{materialSkuCode,jdbcType=NVARCHAR},
      MATERIAL_QTY = #{materialQty,jdbcType=BIGINT},
      COMMENTS = #{comments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>