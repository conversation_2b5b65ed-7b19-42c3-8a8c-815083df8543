<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material.dao.WXTMaterialApplicationVoMapper">
<resultMap id="BaseResultMap" type="com.chevron.material.model.WXTMaterialApplicationVo">
	<id column="ID" jdbcType="BIGINT" property="id" />
	<result column="APPLICATION_NAME" jdbcType="NVARCHAR" property="applicationName" />
	<result column="APPLICATION_CODE" jdbcType="NVARCHAR" property="applicationCode" />
	<result column="APPLICATION_PERSON_ID" jdbcType="BIGINT" property="applicationPersonId" />
	<result column="APPLICATION_PERSON_NAME" jdbcType="NVARCHAR" property="applicationPersonName" />
	<result column="APPLICATION_ORG_ID" jdbcType="BIGINT" property="applicationOrgId" />
	<result column="APPLICATION_ORG_NAME" jdbcType="NVARCHAR" property="applicationOrgName" />
	<result column="APPLICATION_STATUS" jdbcType="NVARCHAR" property="applicationStatus" />
	<result column="COMMENTS" jdbcType="NVARCHAR" property="comments" />
	<result column="APPLICATION_TIME" jdbcType="TIMESTAMP" property="applicationTime" />
	<result column="SHIP_TO_WORKSHOP" jdbcType="BIT" property="shipToWorkshop" />
	<result column="WORKSHOP_ID" jdbcType="BIGINT" property="workshopId" />
	<result column="WORKSHOP_NAME" jdbcType="NVARCHAR" property="workshopName" />
	<result column="CONTACTS" jdbcType="NVARCHAR" property="contacts" />
	<result column="CONTACT_NUMBER" jdbcType="NVARCHAR" property="contactNumber" />
	<result column="ADDRESS_REGION" jdbcType="NVARCHAR" property="addressRegion" />
	<result column="ADDRESS_REGION_NAME" jdbcType="NVARCHAR" property="addressRegionName" />
	<result column="ADDRESS_DETAIL" jdbcType="NVARCHAR" property="addressDetail" />
	<result column="POST_COMPANY" jdbcType="NVARCHAR" property="postCompany" />
	<result column="RECIPIENT_DATE" jdbcType="TIMESTAMP" property="recipientDate" />
	<result column="IS_DONE_2_SMC" jdbcType="BIT" property="isDone2Smc" />
	<result column="SMC_ORDER_CODE" jdbcType="NVARCHAR" property="smcOrderCode" />
	<result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
	<result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
	<result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
	<result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
	<result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
	<result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
	<result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
	<result column="APPLICATION_TYPE" jdbcType="NVARCHAR" property="applicationType" />
	<result column="IS_URGENT" jdbcType="BIT" property="isUrgent" />
	<result column="total_price" jdbcType="NUMERIC" property="totalPrice" />	
	<result column="status" jdbcType="BIGINT" property="status" />
	<result column="BUSINESS_TYPE_CODE" jdbcType="NVARCHAR" property="businessTypeCode" />
	<result column="APPLICATION_USER_ID" jdbcType="NVARCHAR" property="applicationUserId" />
    <result column="APPLICATION_USER_NAME" jdbcType="NVARCHAR" property="applicationUserName" />
    <result column="ACTUAL_APPLICATION_USER_NAME" jdbcType="NVARCHAR" property="actualApplicationUserName" />
    <result column="employee_type" jdbcType="NVARCHAR" property="employeeType" />
    <result column="promotion_type" jdbcType="NVARCHAR" property="promotionType" />
    <result column="deliveryDetailId" jdbcType="BIGINT" property="deliveryDetailId" />
    <result column="promotionName" jdbcType="NVARCHAR" property="promotionName" />
    <result column="businessType" jdbcType="NVARCHAR" property="businessType" />
</resultMap>
<resultMap id="B2BPointDetailResultMap" type="com.chevron.point.model.B2BPointDetailResult">
	<result column="APPLICATION_ID" jdbcType="BIGINT" property="applicationId" />
	<result column="APPLICATION_CODE" jdbcType="NVARCHAR" property="applicationCode" />
	<result column="MECHANIC_CODE" jdbcType="BIGINT" property="mechanicCode" />
	<result column="SALES_CHANNEL" jdbcType="NVARCHAR" property="salesChannel" />
	<result column="POINT_VALUE" jdbcType="NUMERIC" property="pointValue" />
	<result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
	<result column="COMMENTS" jdbcType="NVARCHAR" property="comments" />
	<result column="EARN_TYPE" jdbcType="NVARCHAR" property="earnType" />
		<result column="version_no" property="versionNo" jdbcType="INTEGER"/>
</resultMap>
<resultMap id="PointYearSummaryResultMap" type="com.chevron.point.model.PointYearSummaryResult">
	<result column="year" jdbcType="NUMERIC" property="year" />
	<result column="point_type" jdbcType="NVARCHAR" property="pointType" />
	<result column="total_gain_point" jdbcType="NUMERIC" property="totalGainPoint" />
	<result column="total_left_point" jdbcType="NUMERIC" property="totalLeftPoint" />
</resultMap>
<sql id="Example_Where_Clause">
	<where>
	<foreach collection="oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Update_By_Example_Where_Clause">
	<where>
	<foreach collection="example.oredCriteria" item="criteria" separator="or">
		<if test="criteria.valid">
		<trim prefix="(" prefixOverrides="and" suffix=")">
			<foreach collection="criteria.criteria" item="criterion">
			<choose>
				<when test="criterion.noValue">
				and ${criterion.condition}
				</when>
				<when test="criterion.singleValue">
				and ${criterion.condition} #{criterion.value}
				</when>
				<when test="criterion.betweenValue">
				and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
				</when>
				<when test="criterion.listValue">
				and ${criterion.condition}
				<foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
					#{listItem}
				</foreach>
				</when>
			</choose>
			</foreach>
		</trim>
		</if>
	</foreach>
	</where>
</sql>
<sql id="Base_Column_List">
	ID, APPLICATION_NAME, APPLICATION_CODE, APPLICATION_PERSON_ID, APPLICATION_ORG_ID,
	APPLICATION_STATUS, COMMENTS, APPLICATION_TIME, SHIP_TO_WORKSHOP, WORKSHOP_ID, WORKSHOP_NAME,
	CONTACTS, CONTACT_NUMBER, ADDRESS_REGION, ADDRESS_DETAIL, POST_COMPANY, RECIPIENT_DATE,
	IS_DONE_2_SMC, SMC_ORDER_CODE, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME,
	CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY, APPLICATION_TYPE, IS_URGENT, BUSINESS_TYPE_CODE, PROMOTION_TYPE,APPLICATION_USER_ID,version_no
</sql>
<select id="selectByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationVoExample" resultMap="BaseResultMap">
	select
	<if test="distinct">
	distinct
	</if>
	'true' as QUERYID,
	<include refid="Base_Column_List" />
	from wx_t_material_application
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
	<if test="orderByClause != null">
	order by ${orderByClause}
	</if>
</select>
<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	ma.*, o.organization_name APPLICATION_ORG_NAME, case when ma.APPLICATION_TYPE='b2b' then (select we.name from wx_t_workshop_employee we where we.code=ma.APPLICATION_USER_ID)
			else (select u.CH_NAME from wx_t_user u
	where ma.APPLICATION_PERSON_ID = u.user_id) end APPLICATION_PERSON_NAME,
        (select u.CH_NAME from wx_t_user u
        where ma.APPLICATION_PERSON_ID = u.user_id) as ACTUAL_APPLICATION_USER_NAME
	from wx_t_material_application ma
	LEFT JOIN wx_t_organization o
	on ma.APPLICATION_ORG_ID = o.id
	where ma.ID = #{id,jdbcType=BIGINT}
</select>
<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
	delete from wx_t_material_application
	where ID = #{id,jdbcType=BIGINT}
</delete>
<delete id="deleteByExample" parameterType="com.chevron.material.model.WXTMaterialApplicationVoExample">
	delete from wx_t_material_application
	<if test="_parameter != null">
	<include refid="Example_Where_Clause" />
	</if>
</delete>
<insert id="insert" parameterType="com.chevron.material.model.WXTMaterialApplicationVo">
	insert into wx_t_material_application (ID, APPLICATION_NAME, APPLICATION_CODE,
	APPLICATION_PERSON_ID, APPLICATION_ORG_ID, APPLICATION_STATUS,
	COMMENTS, APPLICATION_TIME, SHIP_TO_WORKSHOP,
	WORKSHOP_ID, WORKSHOP_NAME, CONTACTS,
	CONTACT_NUMBER, ADDRESS_REGION, ADDRESS_DETAIL,
	POST_COMPANY, RECIPIENT_DATE, IS_DONE_2_SMC,
	SMC_ORDER_CODE, DELETE_FLAG, ATTRIBUTE1,
	ATTRIBUTE2, CREATION_TIME, CREATED_BY,
	LAST_UPDATE_TIME, LAST_UPDATED_BY, APPLICATION_TYPE,
	IS_URGENT,BUSINESS_TYPE_CODE,PROMOTION_TYPE)
	values (#{id,jdbcType=BIGINT}, #{applicationName,jdbcType=NVARCHAR}, #{applicationCode,jdbcType=NVARCHAR},
	#{applicationPersonId,jdbcType=BIGINT}, #{applicationOrgId,jdbcType=BIGINT}, #{applicationStatus,jdbcType=NVARCHAR},
	#{comments,jdbcType=NVARCHAR}, #{applicationTime,jdbcType=TIMESTAMP}, #{shipToWorkshop,jdbcType=BIT},
	#{workshopId,jdbcType=BIGINT}, #{workshopName,jdbcType=NVARCHAR}, #{contacts,jdbcType=NVARCHAR},
	#{contactNumber,jdbcType=NVARCHAR}, #{addressRegion,jdbcType=NVARCHAR}, #{addressDetail,jdbcType=NVARCHAR},
	#{postCompany,jdbcType=NVARCHAR}, #{recipientDate,jdbcType=TIMESTAMP}, #{isDone2Smc,jdbcType=BIT},
	#{smcOrderCode,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR},
	#{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT},
	#{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}, #{applicationType,jdbcType=NVARCHAR},
	#{isUrgent,jdbcType=BIT}, #{businessTypeCode, jdbcType=NVARCHAR}, #{promotionType, jdbcType=NVARCHAR})
</insert>
<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.material.model.WXTMaterialApplicationVo">
	insert into wx_t_material_application
	<trim prefix="(" suffix=")" suffixOverrides=",">
	<if test="id != null">
		ID,
	</if>
	<if test="applicationName != null">
		APPLICATION_NAME,
	</if>
	<if test="applicationCode != null">
		APPLICATION_CODE,
	</if>
	<if test="applicationPersonId != null">
		APPLICATION_PERSON_ID,
	</if>
	<if test="applicationOrgId != null">
		APPLICATION_ORG_ID,
	</if>
	<if test="applicationStatus != null">
		APPLICATION_STATUS,
	</if>
	<if test="comments != null">
		COMMENTS,
	</if>
	<if test="applicationTime != null">
		APPLICATION_TIME,
	</if>
	<if test="shipToWorkshop != null">
		SHIP_TO_WORKSHOP,
	</if>
	<if test="workshopId != null">
		WORKSHOP_ID,
	</if>
	<if test="workshopName != null">
		WORKSHOP_NAME,
	</if>
	<if test="contacts != null">
		CONTACTS,
	</if>
	<if test="contactNumber != null">
		CONTACT_NUMBER,
	</if>
	<if test="addressRegion != null">
		ADDRESS_REGION,
	</if>
	<if test="addressDetail != null">
		ADDRESS_DETAIL,
	</if>
	<if test="postCompany != null">
		POST_COMPANY,
	</if>
	<if test="recipientDate != null">
		RECIPIENT_DATE,
	</if>
	<if test="isDone2Smc != null">
		IS_DONE_2_SMC,
	</if>
	<if test="smcOrderCode != null">
		SMC_ORDER_CODE,
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG,
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1,
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2,
	</if>
	<if test="creationTime != null">
		CREATION_TIME,
	</if>
	<if test="createdBy != null">
		CREATED_BY,
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME,
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY,
	</if>
	<if test="applicationType != null">
		APPLICATION_TYPE,
	</if>
	<if test="isUrgent != null">
		IS_URGENT,
	</if>
	<if test="businessTypeCode != null">
		BUSINESS_TYPE_CODE,
	</if>
	<if test="applicationUserId != null">
		APPLICATION_USER_ID,
	</if>
    <if test="promotionType != null">
        PROMOTION_TYPE,
    </if>
			<if test="versionNo != null">
				version_no,
			</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
	<if test="id != null">
		#{id,jdbcType=BIGINT},
	</if>
	<if test="applicationName != null">
		#{applicationName,jdbcType=NVARCHAR},
	</if>
	<if test="applicationCode != null">
		#{applicationCode,jdbcType=NVARCHAR},
	</if>
	<if test="applicationPersonId != null">
		#{applicationPersonId,jdbcType=BIGINT},
	</if>
	<if test="applicationOrgId != null">
		#{applicationOrgId,jdbcType=BIGINT},
	</if>
	<if test="applicationStatus != null">
		#{applicationStatus,jdbcType=NVARCHAR},
	</if>
	<if test="comments != null">
		#{comments,jdbcType=NVARCHAR},
	</if>
	<if test="applicationTime != null">
		#{applicationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="shipToWorkshop != null">
		#{shipToWorkshop,jdbcType=BIT},
	</if>
	<if test="workshopId != null">
		#{workshopId,jdbcType=BIGINT},
	</if>
	<if test="workshopName != null">
		#{workshopName,jdbcType=NVARCHAR},
	</if>
	<if test="contacts != null">
		#{contacts,jdbcType=NVARCHAR},
	</if>
	<if test="contactNumber != null">
		#{contactNumber,jdbcType=NVARCHAR},
	</if>
	<if test="addressRegion != null">
		#{addressRegion,jdbcType=NVARCHAR},
	</if>
	<if test="addressDetail != null">
		#{addressDetail,jdbcType=NVARCHAR},
	</if>
	<if test="postCompany != null">
		#{postCompany,jdbcType=NVARCHAR},
	</if>
	<if test="recipientDate != null">
		#{recipientDate,jdbcType=TIMESTAMP},
	</if>
	<if test="isDone2Smc != null">
		#{isDone2Smc,jdbcType=BIT},
	</if>
	<if test="smcOrderCode != null">
		#{smcOrderCode,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null">
		#{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		#{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		#{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		#{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		#{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		#{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		#{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="applicationType != null">
		#{applicationType,jdbcType=NVARCHAR},
	</if>
	<if test="isUrgent != null">
		#{isUrgent,jdbcType=BIT},
	</if>
	<if test="businessTypeCode != null">
		#{businessTypeCode,jdbcType=NVARCHAR},
	</if>
	<if test="applicationUserId != null">
		#{applicationUserId,jdbcType=NVARCHAR},
	</if>
    <if test="promotionType != null">
        #{promotionType,jdbcType=NVARCHAR},
    </if>
			<if test="versionNo != null">
				#{versionNo,jdbcType=INTEGER},
			</if>
	</trim>
</insert>
<update id="updateByExampleSelective" parameterType="map">
	update wx_t_material_application
	<set>
	<if test="record.id != null">
		ID = #{record.id,jdbcType=BIGINT},
	</if>
	<if test="record.applicationName != null">
		APPLICATION_NAME = #{record.applicationName,jdbcType=NVARCHAR},
	</if>
	<if test="record.applicationCode != null">
		APPLICATION_CODE = #{record.applicationCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.applicationPersonId != null">
		APPLICATION_PERSON_ID = #{record.applicationPersonId,jdbcType=BIGINT},
	</if>
	<if test="record.applicationOrgId != null">
		APPLICATION_ORG_ID = #{record.applicationOrgId,jdbcType=BIGINT},
	</if>
	<if test="record.applicationStatus != null">
		APPLICATION_STATUS = #{record.applicationStatus,jdbcType=NVARCHAR},
	</if>
	<if test="record.comments != null">
		COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	</if>
	<if test="record.applicationTime != null">
		APPLICATION_TIME = #{record.applicationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.shipToWorkshop != null">
		SHIP_TO_WORKSHOP = #{record.shipToWorkshop,jdbcType=BIT},
	</if>
	<if test="record.workshopId != null">
		WORKSHOP_ID = #{record.workshopId,jdbcType=BIGINT},
	</if>
	<if test="record.workshopName != null">
		WORKSHOP_NAME = #{record.workshopName,jdbcType=NVARCHAR},
	</if>
	<if test="record.contacts != null">
		CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
	</if>
	<if test="record.contactNumber != null">
		CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
	</if>
	<if test="record.addressRegion != null">
		ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
	</if>
	<if test="record.addressDetail != null">
		ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
	</if>
	<if test="record.postCompany != null">
		POST_COMPANY = #{record.postCompany,jdbcType=NVARCHAR},
	</if>
	<if test="record.recipientDate != null">
		RECIPIENT_DATE = #{record.recipientDate,jdbcType=TIMESTAMP},
	</if>
	<if test="record.isDone2Smc != null">
		IS_DONE_2_SMC = #{record.isDone2Smc,jdbcType=BIT},
	</if>
	<if test="record.smcOrderCode != null">
		SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
	</if>
	<if test="record.deleteFlag != null">
		DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	</if>
	<if test="record.attribute1 != null">
		ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="record.attribute2 != null">
		ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="record.creationTime != null">
		CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.createdBy != null">
		CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	</if>
	<if test="record.lastUpdateTime != null">
		LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="record.lastUpdatedBy != null">
		LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="record.applicationType != null">
		APPLICATION_TYPE = #{record.applicationType,jdbcType=NVARCHAR},
	</if>
	<if test="record.isUrgent != null">
		IS_URGENT = #{record.isUrgent,jdbcType=BIT},
	</if>
	<if test="record.businessTypeCode != null">
		BUSINESS_TYPE_CODE  = #{record.businessTypeCode,jdbcType=NVARCHAR},
	</if>
    <if test="record.promotionType != null">
        PROMOTION_TYPE = #{promotionType,jdbcType=NVARCHAR},
    </if>
	</set>
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByExample" parameterType="map">
	update wx_t_material_application
	set ID = #{record.id,jdbcType=BIGINT},
	APPLICATION_NAME = #{record.applicationName,jdbcType=NVARCHAR},
	APPLICATION_CODE = #{record.applicationCode,jdbcType=NVARCHAR},
	APPLICATION_PERSON_ID = #{record.applicationPersonId,jdbcType=BIGINT},
	APPLICATION_ORG_ID = #{record.applicationOrgId,jdbcType=BIGINT},
	APPLICATION_STATUS = #{record.applicationStatus,jdbcType=NVARCHAR},
	COMMENTS = #{record.comments,jdbcType=NVARCHAR},
	APPLICATION_TIME = #{record.applicationTime,jdbcType=TIMESTAMP},
	SHIP_TO_WORKSHOP = #{record.shipToWorkshop,jdbcType=BIT},
	WORKSHOP_ID = #{record.workshopId,jdbcType=BIGINT},
	WORKSHOP_NAME = #{record.workshopName,jdbcType=NVARCHAR},
	CONTACTS = #{record.contacts,jdbcType=NVARCHAR},
	CONTACT_NUMBER = #{record.contactNumber,jdbcType=NVARCHAR},
	ADDRESS_REGION = #{record.addressRegion,jdbcType=NVARCHAR},
	ADDRESS_DETAIL = #{record.addressDetail,jdbcType=NVARCHAR},
	POST_COMPANY = #{record.postCompany,jdbcType=NVARCHAR},
	RECIPIENT_DATE = #{record.recipientDate,jdbcType=TIMESTAMP},
	IS_DONE_2_SMC = #{record.isDone2Smc,jdbcType=BIT},
	SMC_ORDER_CODE = #{record.smcOrderCode,jdbcType=NVARCHAR},
	DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
	APPLICATION_TYPE = #{record.applicationType,jdbcType=NVARCHAR},
	IS_URGENT = #{record.isUrgent,jdbcType=BIT},
	BUSINESS_TYPE_CODE  = #{record.businessTypeCode,jdbcType=NVARCHAR},
    PROMOTION_TYPE  = #{record.promotionType,jdbcType=NVARCHAR}
	<if test="_parameter != null">
	<include refid="Update_By_Example_Where_Clause" />
	</if>
</update>
<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material.model.WXTMaterialApplicationVo">
	update wx_t_material_application
	<set>
	<if test="applicationName != null">
		APPLICATION_NAME = #{applicationName,jdbcType=NVARCHAR},
	</if>
	<if test="applicationCode != null">
		APPLICATION_CODE = #{applicationCode,jdbcType=NVARCHAR},
	</if>
	<if test="applicationPersonId != null">
		APPLICATION_PERSON_ID = #{applicationPersonId,jdbcType=BIGINT},
	</if>
	<if test="applicationOrgId != null">
		APPLICATION_ORG_ID = #{applicationOrgId,jdbcType=BIGINT},
	</if>
	<if test="applicationStatus != null">
		APPLICATION_STATUS = #{applicationStatus,jdbcType=NVARCHAR},
	</if>
	<if test="comments != null">
		COMMENTS = #{comments,jdbcType=NVARCHAR},
	</if>
	<if test="applicationTime != null">
		APPLICATION_TIME = #{applicationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="shipToWorkshop != null">
		SHIP_TO_WORKSHOP = #{shipToWorkshop,jdbcType=BIT},
	</if>
	<if test="workshopId != null">
		WORKSHOP_ID = #{workshopId,jdbcType=BIGINT},
	</if>
	<if test="workshopName != null">
		WORKSHOP_NAME = #{workshopName,jdbcType=NVARCHAR},
	</if>
	<if test="contacts != null">
		CONTACTS = #{contacts,jdbcType=NVARCHAR},
	</if>
	<if test="contactNumber != null">
		CONTACT_NUMBER = #{contactNumber,jdbcType=NVARCHAR},
	</if>
	<if test="addressRegion != null">
		ADDRESS_REGION = #{addressRegion,jdbcType=NVARCHAR},
	</if>
	<if test="addressDetail != null">
		ADDRESS_DETAIL = #{addressDetail,jdbcType=NVARCHAR},
	</if>
	<if test="postCompany != null">
		POST_COMPANY = #{postCompany,jdbcType=NVARCHAR},
	</if>
	<if test="recipientDate != null">
		RECIPIENT_DATE = #{recipientDate,jdbcType=TIMESTAMP},
	</if>
	<if test="isDone2Smc != null">
		IS_DONE_2_SMC = #{isDone2Smc,jdbcType=BIT},
	</if>
	<if test="smcOrderCode != null">
		SMC_ORDER_CODE = #{smcOrderCode,jdbcType=NVARCHAR},
	</if>
	<if test="deleteFlag != null">
		DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	</if>
	<if test="attribute1 != null">
		ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	</if>
	<if test="attribute2 != null">
		ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	</if>
	<if test="creationTime != null">
		CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	</if>
	<if test="createdBy != null">
		CREATED_BY = #{createdBy,jdbcType=BIGINT},
	</if>
	<if test="lastUpdateTime != null">
		LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	</if>
	<if test="lastUpdatedBy != null">
		LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	</if>
	<if test="applicationType != null">
		APPLICATION_TYPE = #{applicationType,jdbcType=NVARCHAR},
	</if>
	<if test="isUrgent != null">
		IS_URGENT = #{isUrgent,jdbcType=BIT},
	</if>
	<if test="businessTypeCode != null">
		BUSINESS_TYPE_CODE = #{businessTypeCode,jdbcType=NVARCHAR},
	</if>
    <if test="promotionType != null">
        PROMOTION_TYPE = #{promotionType,jdbcType=NVARCHAR}
    </if>
			<if test="versionNo != null" >
				version_no = #{versionNo,jdbcType=INTEGER},
			</if>
	</set>
	where ID = #{id,jdbcType=BIGINT}
</update>
<update id="updateByPrimaryKey" parameterType="com.chevron.material.model.WXTMaterialApplicationVo">
	update wx_t_material_application
	set APPLICATION_NAME = #{applicationName,jdbcType=NVARCHAR},
	APPLICATION_CODE = #{applicationCode,jdbcType=NVARCHAR},
	APPLICATION_PERSON_ID = #{applicationPersonId,jdbcType=BIGINT},
	APPLICATION_ORG_ID = #{applicationOrgId,jdbcType=BIGINT},
	APPLICATION_STATUS = #{applicationStatus,jdbcType=NVARCHAR},
	COMMENTS = #{comments,jdbcType=NVARCHAR},
	APPLICATION_TIME = #{applicationTime,jdbcType=TIMESTAMP},
	SHIP_TO_WORKSHOP = #{shipToWorkshop,jdbcType=BIT},
	WORKSHOP_ID = #{workshopId,jdbcType=BIGINT},
	WORKSHOP_NAME = #{workshopName,jdbcType=NVARCHAR},
	CONTACTS = #{contacts,jdbcType=NVARCHAR},
	CONTACT_NUMBER = #{contactNumber,jdbcType=NVARCHAR},
	ADDRESS_REGION = #{addressRegion,jdbcType=NVARCHAR},
	ADDRESS_DETAIL = #{addressDetail,jdbcType=NVARCHAR},
	POST_COMPANY = #{postCompany,jdbcType=NVARCHAR},
	RECIPIENT_DATE = #{recipientDate,jdbcType=TIMESTAMP},
	IS_DONE_2_SMC = #{isDone2Smc,jdbcType=BIT},
	SMC_ORDER_CODE = #{smcOrderCode,jdbcType=NVARCHAR},
	DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
	ATTRIBUTE1 = #{attribute1,jdbcType=NVARCHAR},
	ATTRIBUTE2 = #{attribute2,jdbcType=NVARCHAR},
	CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
	CREATED_BY = #{createdBy,jdbcType=BIGINT},
	LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
	LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
	APPLICATION_TYPE = #{applicationType,jdbcType=NVARCHAR},
	IS_URGENT = #{isUrgent,jdbcType=BIT},
	BUSINESS_TYPE_CODE = #{businessTypeCode,jdbcType=NVARCHAR},
	PROMOTION_TYPE = #{promotionType,jdbcType=NVARCHAR}
	where ID = #{id,jdbcType=BIGINT}
</update>
<!-- 分页查询物料申请单信息 -->
<select id="selectByPagination" parameterType="com.chevron.material.model.ApplicationParams" resultMap="BaseResultMap" >
	<!-- select
	ma.*,
	ADDRESS_REGION_NAME= (SELECT province.region_name + '-' + city.region_name +'-'+ dist.region_name
FROM wx_t_region dist LEFT JOIN wx_t_region city ON dist.parent_id = city.id LEFT JOIN wx_t_region province ON city.parent_id = province.id
WHERE dist.region_code = ma.ADDRESS_REGION),
	o.organization_name APPLICATION_ORG_NAME,
	u.CH_NAME APPLICATION_PERSON_NAME
	from wx_t_material_application ma
	left join wx_t_user u
	on ma.APPLICATION_PERSON_ID = u.user_id
	LEFT JOIN wx_t_organization o
	on ma.APPLICATION_ORG_ID = o.id
	where ma.delete_flag=0 -->
select
    ma.*,
    ADDRESS_REGION_NAME= province.region_name + '-' + city.region_name +'-'+ dist.region_name,
    o.organization_name APPLICATION_ORG_NAME,
    u.CH_NAME APPLICATION_PERSON_NAME,
    aa.name as APPLICATION_USER_NAME,
    aa.employee_type
from wx_t_material_application ma
    left join wx_t_user u
    on ma.APPLICATION_PERSON_ID = u.user_id
    LEFT JOIN wx_t_organization o
    on ma.APPLICATION_ORG_ID = o.id
    LEFT JOIN wx_t_region dist ON dist.region_code = ma.ADDRESS_REGION
    LEFT JOIN wx_t_region city ON dist.parent_id = city.id
    LEFT JOIN wx_t_region province ON city.parent_id = province.id
    LEFT JOIN wx_t_workshop_employee aa on aa.code = ma.APPLICATION_USER_ID
    where ma.delete_flag=0
	<if test="applicationOrgId != null" >
	and ma.APPLICATION_ORG_ID = #{applicationOrgId}
	</if>
	<if test="applicationCode != null" >
	and ma.APPLICATION_CODE = #{applicationCode}
	</if>
	<if test="applicationName != null" >
	and ma.APPLICATION_NAME like '%'+#{applicationName}+'%'
	</if>
	<if test="applicationStatus != null" >
	and ma.APPLICATION_STATUS in
		<foreach collection="applicationStatus" item="status" open="(" separator="," close=")">
			'${status}'
		</foreach>
	</if>
	<if test="applicationPersonId != null" >
	and ma.APPLICATION_PERSON_ID = #{applicationPersonId}
	</if>
	<if test="applicationPersonName != null" >
	and u.CH_NAME = #{applicationPersonName}
	</if>
	<if test="creationTime != null" >
	and ma.CREATION_TIME = #{creationTime}
	</if>
	<if test="startTimeDate !=null">
	and ma.creation_time &gt; #{startTimeDate}
	</if>
	<if test="endTimeDate !=null">
	and ma.creation_time &lt; #{endTimeDate}
	</if>
	<if test="ready4LevelOne" >
	and (ma.APPLICATION_STATUS = 'READY' OR ma.APPLICATION_STATUS = 'PENDING_ON_L1')
	</if>
	<if test="ready4LevelTwo" >
	and (ma.APPLICATION_STATUS = 'PENDING_ON_L2')
	</if>
	<if test="applicantTodoList" >
	and (ma.APPLICATION_STATUS in ('DRAFT', 'REJECTED'))
	</if>
	<if test="applicantDoneList" >
	and (ma.APPLICATION_STATUS  not in ('DRAFT', 'REJECTED'))
	</if>
	<if test="pointMode" >
        and (ma.application_type in
        <foreach collection="applicationType" item="type" open="(" close=")" separator=",">'${type}'</foreach>
        or ma.promotion_type in
        <foreach collection="applicationType" item="type" open="(" close=")" separator=",">'${type}'</foreach>
        )
	</if>
	<if test="pointMode != true" >
	and  ma.APPLICATION_TYPE is null
	</if>
	<if test="isUrgent" >
	and (ma.IS_URGENT = '1')
	</if>
	<if test="businessTypeCode != null">
       AND ma.BUSINESS_TYPE_CODE = #{businessTypeCode}
	</if>
	<if test="applicationUserId != null">
	   AND ma.APPLICATION_USER_ID = #{applicationUserId}
	</if>
	<if test="keyword != null" >
	and (
		ma.APPLICATION_NAME like '%'+#{keyword}+'%'
		or ma.APPLICATION_CODE like '%'+#{keyword}+'%'
		or u.CH_NAME like '%'+#{keyword}+'%'
	    OR o.organization_name LIKE '%'+#{keyword}+'%'
        OR ma.CONTACTS LIKE '%'+#{keyword}+'%'
        OR ma.CONTACT_NUMBER LIKE '%'+#{keyword}+'%'
        OR ma.ADDRESS_DETAIL LIKE  '%'+#{keyword}+'%'
        OR province.region_name + '-' + city.region_name +'-'+ dist.region_name LIKE '%'+#{keyword}+'%'
        OR province.region_name + '-' + city.region_name +'-'+ dist.region_name+' '+ma.ADDRESS_DETAIL LIKE '%'+#{keyword}+'%'
	)
	</if>
	<if test="exportStartDateProperty != null and exportStartDateProperty != ''">
	and (not exists (select 1 from wx_t_properties p1 where p1.codetype=#{exportStartDateProperty}) 
		or ma.CREATION_TIME >= (select max(p1.code) from wx_t_properties p1 where p1.codetype=#{exportStartDateProperty}) )
	</if>

	$Permission_Clause$
</select>

<select id="selectSellInSacle" parameterType="java.lang.Long" resultType="map">
	select t.*,
	total=(SELECT sum(po1.total_liter_count) spSellin FROM wx_t_partner_order po1
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
		 WHERE po1.create_time > DateAdd(Month,-3,getdate()) and po1.partner_id = #{partnerId,jdbcType=BIGINT}
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
		 ),
	scale=(SELECT sum(po1.total_liter_count) + 0.0 spSellin FROM wx_t_partner_order po1 
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
	WHERE po1.create_time > DateAdd(Month,-3,getdate()) and po1.partner_id = #{partnerId,jdbcType=BIGINT}
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
	 )
		/(SELECT sum(po1.total_liter_count)+ 0.0 totalSellin FROM wx_t_partner_order po1 
		left join wx_t_statistics_cond_config scc1 on scc1.statistics_type='SellInOrderStatistics_status' and scc1.enable_flag=1
			and scc1.effective_from_date&lt;=po1.create_time and scc1.effective_to_date&gt;po1.create_time
		where po1.partner_id not in (8) and po1.create_time > DateAdd(Month,-3,getdate())
		and (scc1.id is null or exists (select 1 from wx_t_sta_cond_con_value sccv1 where sccv1.config_id=scc1.id and po1.status=sccv1.item_value))
		)* 100,
	isPartner=(select count(1) isPartner from wx_t_organization where type= 1 and id = #{partnerId,jdbcType=BIGINT})
	from (
		select
			DATEDIFF(month,
			create_time,
			getdate()) diffMonth,
			day(getdate())-day(create_time) diffDay
		from wx_t_organization
		where type= 1
			and id = #{partnerId,jdbcType=BIGINT}
	) t
</select>
<select id="selectAddressList" parameterType="java.lang.Long" resultMap="BaseResultMap">
	SELECT DISTINCT
		APPLICATION_ORG_ID,
		CONTACTS,
		CONTACT_NUMBER,
		ADDRESS_REGION,
		ADDRESS_DETAIL
	FROM
		wx_t_material_application
	WHERE
		APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
	ORDER BY
		APPLICATION_ORG_ID
</select>
<select id="countSPRegionNumber" parameterType="java.lang.Long" resultType="java.lang.Long">
	SELECT
		count(1)
	FROM wx_t_userrole ur
	LEFT JOIN wx_t_role r ON r.role_id = ur.role_id
	LEFT JOIN wx_t_user u ON u.user_id = ur.user_id
	WHERE
		u.org_id = #{partnerId,jdbcType=BIGINT}
	AND
		r.ch_role_name = 'SP_Region_Material_Admin'
</select>

<select id="selectShippingAppList" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select top 10 ma.*,
	ADDRESS_REGION_NAME= (SELECT province.region_name + '-' + city.region_name +'-'+ dist.region_name
FROM wx_t_region dist LEFT JOIN wx_t_region city ON dist.parent_id = city.id LEFT JOIN wx_t_region province ON city.parent_id = province.id
WHERE dist.region_code = ma.ADDRESS_REGION),
	o.organization_name APPLICATION_ORG_NAME,
	u.CH_NAME APPLICATION_PERSON_NAME
	from wx_t_material_application ma
	left join wx_t_user u
	on ma.APPLICATION_PERSON_ID = u.user_id
	LEFT JOIN wx_t_organization o
	on ma.APPLICATION_ORG_ID = o.id
	where ma.delete_flag=0
	and ma.APPLICATION_ORG_ID = #{partnerId,jdbcType=BIGINT}
	and ma.APPLICATION_STATUS = 'SHIPPING'
</select>

<select id="selectPointPayedDetailByParam" parameterType="com.chevron.point.model.WXTPointValueDetailVoParams"
	resultMap="BaseResultMap">
	SELECT * FROM
	(
	(
	select
	a.APPLICATION_ORG_ID,
	sum( de.MATERIAL_PRICE*de.APPLICATION_QTY ) AS total_price,
	a.CREATION_TIME,
	a.APPLICATION_TYPE,
	'订单:' + a.APPLICATION_CODE AS comments,
	0 as status,
	a.id as id,
	NULL promotionName,
	NULL businessType,
	NULL deliveryDetailId
	from
	dbo.wx_t_material_application a
	left join dbo.wx_t_organization o on
	o.id = a.APPLICATION_ORG_ID
	left join dbo.wx_t_material_application_detail de on
	de.APPLICATION_ID = a.ID
	where
	1 = 1
	and a.DELETE_FLAG = 0
	<choose>
        <when test="pointType!=null and pointType == 'cdm_newstore_open'">
            and a.APPLICATION_TYPE = 'cdm_store_open'
        </when>
        <otherwise>
            and a.APPLICATION_TYPE = #{pointType}
        </otherwise>
    </choose>
	<if test="dealerId != null">
	and a.APPLICATION_ORG_ID = #{dealerId}
	</if>
	<if test="accountOwnerId != null and accountOwnerId != ''">
	and a.APPLICATION_USER_ID = #{accountOwnerId}
	</if>
	<if test="accountOwnerCode != null and accountOwnerCode != ''">
	and a.APPLICATION_USER_ID = #{accountOwnerId}
	</if>
	<if test="findAll != null and !findAll">
	AND a.ATTRIBUTE1 != 'PROMOTION_EXCHANGE'
	</if>
	and a.APPLICATION_STATUS not in(
	'REJECTED',
	'DRAFT',
	'PENDING_4_URGENT'
	)
	group by
	a.APPLICATION_CODE,
	a.APPLICATION_TYPE,
	a.APPLICATION_STATUS,
	a.CREATION_TIME,
	a.LAST_UPDATE_TIME,
	a.APPLICATION_ORG_ID,
	a.APPLICATION_PERSON_ID,
	a.id
	)
	UNION all(
	SELECT
	pac.POINT_ACCOUNT_OWNER_ID,
	pde.POINT_VALUE,
	pde.CREATION_TIME,
	CASE
	WHEN pde.POINT_TYPE = 'CALTEX_POINT' THEN 'caltex'
	WHEN pde.POINT_TYPE = 'CDM_PIT_PACK' THEN 'cdm_pit_pack'
	WHEN pde.POINT_TYPE = 'PROMOTION_POINT' THEN 'promotion'
	WHEN pde.POINT_TYPE = 'CDM_STOCK_POINT' THEN 'cdm_stock'
	WHEN pde.POINT_TYPE = 'CDM_MATERIAL_POINT' THEN 'cdm_material'
	WHEN pde.POINT_TYPE = 'CDM_PROMOTION_POINT' THEN 'cdm_promotion'
	WHEN pde.POINT_TYPE = 'CDM_RED_BAG_POINT' THEN 'cdm_red_bag'
    WHEN pde.POINT_TYPE = 'CDM_PIT_PACK' THEN 'cdm_pit_pack'
	WHEN pde.POINT_TYPE = 'OEM_STOCK_POINT' THEN 'oem_stock'
	WHEN pde.POINT_TYPE = 'CDM_STORE_OPEN_POINT' THEN 'cdm_store_open'
	END AS application_type,
	plog.COMMENTS,
	1 as status,
	null as id,
	bu.ATTRIBUTE1 promotionName,
	bu.BUSINESS_TYPE_CODE businessType,
	bu.RELATED_ID deliveryDetailId
	FROM
	wx_t_point_value_detail pde
	LEFT JOIN wx_t_point_value_detail_log plog ON
	plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_business bu ON pde.BUSINESS_ID = bu.ID
	LEFT JOIN wx_t_point_account pac ON
	pac.id = pde.POINT_ACCOUNT_ID
	WHERE
	1 = 1
	<choose>
	<when test="pointType == 'caltex'">
	AND pde.POINT_TYPE = 'CALTEX_POINT'
	</when>
	<when test="pointType == 'cdm_pit_pack'">
	AND pde.POINT_TYPE = 'CDM_PIT_PACK'
	</when>
	<when test="pointType == 'promotion'">
	AND pde.POINT_TYPE = 'PROMOTION_POINT'
	</when>
	<when test="pointType == 'cdm_stock'">
	AND pde.POINT_TYPE = 'CDM_STOCK_POINT'
	</when>
	<when test="pointType == 'cdm_material'">
	AND pde.POINT_TYPE = 'CDM_MATERIAL_POINT'
	</when>
	<when test="pointType == 'cdm_promotion'">
	AND pde.POINT_TYPE = 'CDM_PROMOTION_POINT'
	</when>
    <when test="pointType == 'cdm_pit_pack'">
	  AND pde.POINT_TYPE = 'CDM_PIT_PACK'
	</when>
    <when test="pointType == 'cdm_red_bag'">
        AND pde.POINT_TYPE = 'CDM_RED_BAG_POINT'
    </when>
	<when test="pointType == 'oem_stock'">
		AND pde.POINT_TYPE = 'OEM_STOCK_POINT'
	</when>
	<when test="pointType == 'cdm_newstore_open'">
		AND pde.POINT_TYPE = 'CDM_STORE_OPEN_POINT'
	</when>
	</choose>
	<if test="actualPointType != null and actualPointType != ''">
	AND pde.POINT_TYPE = #{actualPointType}
	</if>
	and pde.POINT_VALUE!=0
	<if test="dealerId != null">
	AND pac.POINT_ACCOUNT_OWNER_ID = #{dealerId}
	</if>
	<if test="accountType != null and accountType != ''">
	and pac.POINT_ACCOUNT_TYPE = #{accountType}
	</if>
	<if test="accountOwnerId != null and accountOwnerId != ''">
	and pac.POINT_ACCOUNT_OWNER_ID = #{accountOwnerId}
	</if>
	<if test="accountOwnerCode != null and accountOwnerCode != ''">
	and pac.POINT_ACCOUNT_OWNER_CODE = #{accountOwnerCode}
	</if>
	)
	) a
</select>

<select id="selectPointPayedDetailByGift" parameterType="com.chevron.point.model.WXTPointValueDetailVoParams"
	resultMap="BaseResultMap">
SELECT
	gp.partner_id APPLICATION_ORG_ID,
	gml.modify_quantity total_price,
	gml.create_time CREATION_TIME,
	gp.application_type APPLICATION_TYPE,
	CASE WHEN gml.business_type = 'exchange' AND gml.business_key is not NULL  
	THEN '订单:' + gml.ext_property1
	ELSE gml.comments END comments,
	CASE
	WHEN gml.modify_type = 'ModifyTypeReduce'
	THEN	0
	WHEN gml.modify_type = 'modifyTypeAdd'
	THEN	1
	END AS status,
	CASE WHEN gml.business_type = 'exchange' AND gml.business_key is NOT NULL
	THEN gml.business_key ELSE NULL END  AS id,
	gml.ext_property2 promotionName,
	CASE WHEN gml.business_type = 'exchange' AND gml.business_key is not NULL
	THEN NULL ELSE gp.application_type END businessType,
	CASE WHEN gml.business_type != 'exchange' AND gml.business_key is NOT NULL
	THEN gml.business_key ELSE NULL END  deliveryDetailId
FROM
	wx_t_promotion_gift_pool gp
LEFT JOIN wx_t_promotion_gift_modify_log gml ON gp.id = gml.pool_id
where 1 = 1
<if test="dealerId != null">
and gp.partner_id = #{dealerId}
</if> 
	<choose>
	<when test="pointType == 'caiGiftPromotion'">
		and gp.application_type = 'promotion'
	</when>
	<when test="pointType == 'cmdGiftPromotion'">
		and gp.application_type = 'cdm_promotion'
	</when>
	</choose>
</select>
<select id="selectB2BPointPayedDetailByParam" parameterType="com.chevron.point.model.B2BPointDetailParam"
		resultMap="B2BPointDetailResultMap">
	SELECT * FROM
	(
	<if test="earnType == 'ALL' or earnType == 'CONSUME_POINT'">
	(
	select
	a.id as APPLICATION_ID,
	a.APPLICATION_CODE,
	a.APPLICATION_USER_ID MECHANIC_CODE,
	a.BUSINESS_TYPE_CODE SALES_CHANNEL,
	sum( de.MATERIAL_PRICE*de.APPLICATION_QTY ) AS POINT_VALUE,
	a.CREATION_TIME CREATE_TIME,
	'订单:' + a.APPLICATION_CODE AS COMMENTS,
	'CONSUME_POINT' as EARN_TYPE
	from
	dbo.wx_t_material_application a
	left join dbo.wx_t_organization o on
	o.id = a.APPLICATION_ORG_ID
	left join dbo.wx_t_material_application_detail de on
	de.APPLICATION_ID = a.ID
	where
	1 = 1
	AND a.DELETE_FLAG = 0
	AND a.APPLICATION_TYPE = 'b2b'
	<if test="salesChannel != null" >
		AND	BUSINESS_TYPE_CODE = #{salesChannel,jdbcType=VARCHAR}
	</if>
	AND a.APPLICATION_USER_ID = #{mechanicCode,jdbcType=VARCHAR}
	AND a.APPLICATION_STATUS not in(
	'REJECTED',
	'DRAFT',
	'PENDING_4_URGENT'
	)
	<if test="createDateFrom != null">
		and a.creation_time &gt;= #{createDateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="createDateTo != null">
		and a.creation_time &lt; #{createDateTo, jdbcType=TIMESTAMP}
	</if>
	group by
	a.id,
	a.APPLICATION_CODE,
	a.APPLICATION_TYPE,
	a.APPLICATION_STATUS,
	a.CREATION_TIME,
	a.LAST_UPDATE_TIME,
	a.APPLICATION_USER_ID,
	a.BUSINESS_TYPE_CODE
	)
	</if>
	<if test="earnType == 'ALL'">
	UNION all
	</if>
	<if test="earnType != 'CONSUME_POINT'">
	(
	SELECT
	null as APPLICATION_ID,
	null as APPLICATION_CODE,
	pac.POINT_ACCOUNT_OWNER_CODE MECHANIC_CODE,
	pde.SUB_TYPE SALES_CHANNEL,
	pde.POINT_VALUE,
	pde.CREATION_TIME CREATE_TIME,
	plog.COMMENTS,
	pb.EARN_TYPE as EARN_TYPE
	FROM
	wx_t_point_value_detail pde
	LEFT JOIN wx_t_point_value_detail_log plog ON
	plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_account pac ON
	pac.id = pde.POINT_ACCOUNT_ID
	LEFT JOIN wx_t_point_business pb ON
	pb.id = pde.business_id
	WHERE
	1 = 1
	AND pde.POINT_TYPE = 'B2B_POINT'
	AND pde.POINT_VALUE != 0
	AND pde.DELETE_FLAG = 0
	AND pac.POINT_ACCOUNT_OWNER_CODE = #{mechanicCode,jdbcType=VARCHAR}
	<if test="salesChannel != null" >
	AND pde.SUB_TYPE = #{salesChannel,jdbcType=VARCHAR}
	</if>
	<if test="createDateFrom != null">
		and pde.creation_time &gt;= #{createDateFrom, jdbcType=TIMESTAMP}
	</if>
	<if test="createDateTo != null">
		and pde.creation_time &lt; #{createDateTo, jdbcType=TIMESTAMP}
	</if>
	<if test="earnType!=null and earnType != 'ALL'">
		and pb.EARN_TYPE = #{earnType,jdbcType=VARCHAR}
	</if>
	)
	</if>
	) a
</select>
<select id="getPointSummaryByDealerId" parameterType="com.chevron.point.model.PointYearSummaryParams" resultMap="PointYearSummaryResultMap">
	SELECT a.year,a.point_type,sum(gain_point_value) total_gain_point,
	case
	when sum(ISNULL(gain_point_value, 0)) + sum(ISNULL(use_point_value, 0)) &gt;= 0 then sum(ISNULL(gain_point_value, 0)) + sum(ISNULL(use_point_value, 0))
	when sum(ISNULL(gain_point_value, 0)) + sum(ISNULL(use_point_value, 0)) &lt; 0  then 0 end
	as total_left_point FROM
	(
	(
	SELECT
	pac.POINT_ACCOUNT_OWNER_ID,
	pde.POINT_VALUE AS gain_point_value,
	pde.POINT_PAYED*-1 AS use_point_value,
	year(pde.TRANS_TIME) year,
	pde.point_type AS point_type
	FROM
	wx_t_point_value_detail pde
	LEFT JOIN wx_t_point_value_detail_log plog ON
	plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_account pac ON
	pac.id = pde.POINT_ACCOUNT_ID
	left join wx_t_point_business pb ON
	pb.ID = pde.BUSINESS_ID
	WHERE
	1 = 1
	AND (pb.BUSINESS_TYPE_CODE in ('DEALER_POINT_MONTHLY_IMPORT','PROMOTION_POINT_IMPORT','CALTEX_POINT_FROM_BI','CALTEX_POINT_FROM_PROMOTE','CDM_MATERIAL_POINT_IMPORT','CDM_STOCK_POINT_IMPORT','CDM_PROMOTION_POINT_IMPORT','CDM_STOCK_POINT_FROM_BI','OEM_STOCK_POINT_IMPORT','OEM_STOCK_POINT_FROM_BI','POINT_IMPORT','ADJUST_POINT') )
	<if test="pointTypeList != null">
		and pde.POINT_TYPE in
		<foreach collection="pointTypeList" item="type" open="(" close=")" separator=",">
			'${type}'
		</foreach>
	</if>
	and pde.POINT_VALUE!=0
	AND pac.POINT_ACCOUNT_OWNER_ID  = #{dealerId}
	AND year(pde.TRANS_TIME) = #{year}
	AND pde.DELETE_FLAG = 0
	)
	UNION all(
	SELECT
	pac.POINT_ACCOUNT_OWNER_ID,
	null AS gain_point_value,
	pde.POINT_VALUE AS use_point_value,
	year(pde.CLEAR_TIME) year,
	pde.point_type AS point_type
	FROM
	wx_t_point_value_detail pde
	LEFT JOIN wx_t_point_value_detail_log plog ON
	plog.POINT_VALUE_ID = pde.ID
	LEFT JOIN wx_t_point_account pac ON
	pac.id = pde.POINT_ACCOUNT_ID
	left join wx_t_point_business pb ON
	pb.ID = pde.BUSINESS_ID
	WHERE
	1 = 1
	AND (pb.BUSINESS_TYPE_CODE in ('CLEAR_POINT') )
	<if test="pointTypeList != null">
		and pde.POINT_TYPE in
		<foreach collection="pointTypeList" item="type" open="(" close=")" separator=",">
			'${type}'
		</foreach>
	</if>
	and pde.POINT_VALUE!=0
	AND pac.POINT_ACCOUNT_OWNER_ID  = #{dealerId}
	AND year(pde.CLEAR_TIME) = #{year}
	AND pde.DELETE_FLAG = 0
	)
	) a group by year,POINT_TYPE
</select>

    <resultMap id="abnormalDetection" type="com.chevron.material.model.B2BAbnormalDetection">
        <result column="APPLICATION_USER_ID" jdbcType="NUMERIC" property="applicationUserId" />
        <result column="APPLICATION_USER_NAME" jdbcType="NUMERIC" property="applicationUserName" />
        <result column="count_point" jdbcType="NVARCHAR" property="countPoint" />
        <result column="times" jdbcType="NUMERIC" property="times" />
    </resultMap>
    <select id="abnormalDetection" parameterType="map" resultMap="abnormalDetection">
        select a.APPLICATION_USER_ID,(case when a.APPLICATION_TYPE='b2b' then (select we.name from wx_t_workshop_employee we where we.code=a.APPLICATION_USER_ID) else 
        (select u.ch_name from wx_t_user u where u.user_id=a.APPLICATION_USER_ID) end) as APPLICATION_USER_NAME,sum(b.APPLICATION_QTY * b.MATERIAL_PRICE ) as
        count_point,count(0) as times from
        wx_t_material_application a
        inner join wx_t_material_application_detail b on a.ID = b.APPLICATION_ID
        <where>
            <if test="today != null and today!=''">
                and b.CREATION_TIME >= #{today}
            </if>
            <if test="applicationUserId != null and applicationUserId!=''">
                and a.APPLICATION_USER_ID = #{applicationUserId}
            </if>
            and a.APPLICATION_STATUS in ('VIRTUAL_SUBMIT','VIRTUAL_SUCCESS')
        </where>
        group by a.APPLICATION_USER_ID,a.APPLICATION_TYPE
    </select>
</mapper>