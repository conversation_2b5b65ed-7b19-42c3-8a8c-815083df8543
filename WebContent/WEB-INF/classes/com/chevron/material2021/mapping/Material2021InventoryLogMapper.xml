<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.material2021.dao.Material2021InventoryLogMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.material2021.model.Material2021InventoryLog">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="material_id" property="materialId" jdbcType="BIGINT"/>
		<result column="material_code" property="materialCode" jdbcType="VARCHAR"/>
		<result column="change_qty" property="changeQty" jdbcType="BIGINT"/>
		<result column="stock_type" property="stockType" jdbcType="VARCHAR"/>
		<result column="attribute1" property="attribute1" jdbcType="VARCHAR"/>
		<result column="attribute2" property="attribute2" jdbcType="VARCHAR"/>
		<result column="attribute3" property="attribute3" jdbcType="VARCHAR"/>
		<result column="warehouse_id" property="warehouseId" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
		<result column="original_qty" property="originalQty" jdbcType="BIGINT"/>
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,material_id,material_code,change_qty,stock_type,attribute1,attribute2,attribute3,create_user_id,create_time,
		update_user_id,update_time,del_flag,original_qty
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.material2021.model.Material2021InventoryLog">
		update wx_t_material2021_inventory_log set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.material2021.model.Material2021InventoryLog">
		update wx_t_material2021_inventory_log
		<set>
			<if test="materialId != null" >
				material_id = #{materialId,jdbcType=BIGINT},
			</if>
			<if test="materialCode != null" >
				material_code = #{materialCode,jdbcType=VARCHAR},
			</if>
			<if test="changeQty != null" >
				change_qty = #{changeQty,jdbcType=BIGINT},
			</if>
			<if test="stockType != null" >
				stock_type = #{stockType,jdbcType=VARCHAR},
			</if>
			<if test="attribute1 != null" >
				attribute1 = #{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null" >
				attribute2 = #{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null" >
				attribute3 = #{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="delFlag != null" >
				del_flag = #{delFlag,jdbcType=INTEGER},
			</if>
			<if test="originalQty != null" >
				original_qty = #{originalQty,jdbcType=BIGINT},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.material2021.model.Material2021InventoryLogExample">
    	delete from wx_t_material2021_inventory_log
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.material2021.model.Material2021InventoryLog" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_material2021_inventory_log
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="materialId != null">
				material_id,
			</if>
			<if test="materialCode != null">
				material_code,
			</if>
			<if test="changeQty != null">
				change_qty,
			</if>
			<if test="stockType != null">
				stock_type,
			</if>
			<if test="attribute1 != null">
				attribute1,
			</if>
			<if test="attribute2 != null">
				attribute2,
			</if>
			<if test="attribute3 != null">
				attribute3,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="delFlag != null">
				del_flag,
			</if>
			<if test="originalQty != null">
				original_qty,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="materialId != null">
				#{materialId,jdbcType=BIGINT},
			</if>
			<if test="materialCode != null">
				#{materialCode,jdbcType=VARCHAR},
			</if>
			<if test="changeQty != null">
				#{changeQty,jdbcType=BIGINT},
			</if>
			<if test="stockType != null">
				#{stockType,jdbcType=VARCHAR},
			</if>
			<if test="attribute1 != null">
				#{attribute1,jdbcType=VARCHAR},
			</if>
			<if test="attribute2 != null">
				#{attribute2,jdbcType=VARCHAR},
			</if>
			<if test="attribute3 != null">
				#{attribute3,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="delFlag != null">
				#{delFlag,jdbcType=INTEGER},
			</if>
			<if test="originalQty != null">
				#{originalQty,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_material2021_inventory_log
		<set>
			<if test="record.materialId != null">
				material_id = #{record.materialId,jdbcType=BIGINT},
			</if>
			<if test="record.materialCode != null">
				material_code = #{record.materialCode,jdbcType=VARCHAR},
			</if>
			<if test="record.changeQty != null">
				change_qty = #{record.changeQty,jdbcType=BIGINT},
			</if>
			<if test="record.stockType != null">
				stock_type = #{record.stockType,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute1 != null">
				attribute1 = #{record.attribute1,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute2 != null">
				attribute2 = #{record.attribute2,jdbcType=VARCHAR},
			</if>
			<if test="record.attribute3 != null">
				attribute3 = #{record.attribute3,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.delFlag != null">
				del_flag = #{record.delFlag,jdbcType=INTEGER},
			</if>
			<if test="record.originalQty != null">
				original_qty = #{record.originalQty,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.material2021.model.Material2021InventoryLogExample">
		delete from wx_t_material2021_inventory_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.material2021.model.Material2021InventoryLogExample" resultType="int">
		select count(1) from wx_t_material2021_inventory_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.material2021.model.Material2021InventoryLogExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_material2021_inventory_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.material2021.model.Material2021InventoryLogExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_material2021_inventory_log
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.material_id, t1.material_code, t1.change_qty, t1.stock_type, t1.attribute1, t1.attribute2,
			 t1.attribute3, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.del_flag, t1.original_qty,
			 u1.ch_name create_user_name
		  from wx_t_material2021_inventory_log t1
		  left join wx_t_user u1 on u1.user_id=t1.create_user_id
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.material2021.model.Material2021InventoryLogParams">
		select t1.id, t1.material_id, t1.material_code, t1.change_qty, t1.stock_type, t1.attribute1, t1.attribute2,
			 t1.attribute3, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.del_flag, t1.original_qty,
			 u1.ch_name create_user_name
		  from wx_t_material2021_inventory_log t1
		  left join wx_t_user u1 on u1.user_id=t1.create_user_id
		 where t1.del_flag=0
		<if test="materialId != null">
			and t1.material_id = #{materialId, jdbcType=INTEGER}
		</if>
		<if test="materialCode != null and materialCode != ''">
			and t1.material_code = #{materialCode, jdbcType=VARCHAR}
		</if>
		<if test="stockType != null and stockType != ''">
			and t1.stock_type = #{stockType, jdbcType=VARCHAR}
		</if>
	</select>
</mapper>
