<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.materialplatformorder.dao.SmaOrderDetailMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.materialplatformorder.model.SmaOrderDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="order_line_no" property="orderLineNo" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="product_code" property="productCode" jdbcType="VARCHAR"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="price" property="price" jdbcType="NUMERIC"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="color" property="color" jdbcType="VARCHAR"/>
		<result column="size" property="size" jdbcType="VARCHAR"/>
		<result column="product_name" property="productName" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="extras" property="extras" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,order_line_no,create_user_id,create_time,update_user_id,update_time,order_no,product_code,sku,price,ext_flag,color,size,
		product_name,quantity,extras
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetail">
		update wx_t_smartcomm_order_detail set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetail">
		update wx_t_smartcomm_order_detail
		<set>
			<if test="orderLineNo != null" >
				order_line_no = #{orderLineNo,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="orderNo != null" >
				order_no = #{orderNo,jdbcType=VARCHAR},
			</if>
			<if test="productCode != null" >
				product_code = #{productCode,jdbcType=VARCHAR},
			</if>
			<if test="sku != null" >
				sku = #{sku,jdbcType=VARCHAR},
			</if>
			<if test="price != null" >
				price = #{price,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="color != null" >
				color = #{color,jdbcType=VARCHAR},
			</if>
			<if test="size != null" >
				size = #{size,jdbcType=VARCHAR},
			</if>
			<if test="productName != null" >
				product_name = #{productName,jdbcType=VARCHAR},
			</if>
			<if test="quantity != null" >
				quantity = #{quantity,jdbcType=INTEGER},
			</if>
			<if test="extras != null" >
				extras = #{extras,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetailExample">
    	delete from wx_t_smartcomm_order_detail
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetail">
		insert into wx_t_smartcomm_order_detail
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="orderLineNo != null" >
				order_line_no,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="orderNo != null">
				order_no,
			</if>
			<if test="productCode != null">
				product_code,
			</if>
			<if test="sku != null">
				sku,
			</if>
			<if test="price != null">
				price,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="color != null">
				color,
			</if>
			<if test="size != null">
				size,
			</if>
			<if test="productName != null">
				product_name,
			</if>
			<if test="quantity != null">
				quantity,
			</if>
			<if test="extras != null">
				extras,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="orderLineNo != null" >
				#{orderLineNo,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
			</if>
			<if test="productCode != null">
				#{productCode,jdbcType=VARCHAR},
			</if>
			<if test="sku != null">
				#{sku,jdbcType=VARCHAR},
			</if>
			<if test="price != null">
				#{price,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="color != null">
				#{color,jdbcType=VARCHAR},
			</if>
			<if test="size != null">
				#{size,jdbcType=VARCHAR},
			</if>
			<if test="productName != null">
				#{productName,jdbcType=VARCHAR},
			</if>
			<if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
			</if>
			<if test="extras != null">
				#{extras,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_smartcomm_order_detail
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.orderNo != null">
				order_no = #{record.orderNo,jdbcType=VARCHAR},
			</if>
			<if test="record.productCode != null">
				product_code = #{record.productCode,jdbcType=VARCHAR},
			</if>
			<if test="record.sku != null">
				sku = #{record.sku,jdbcType=VARCHAR},
			</if>
			<if test="record.price != null">
				price = #{record.price,jdbcType=NUMERIC},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.color != null">
				color = #{record.color,jdbcType=VARCHAR},
			</if>
			<if test="record.size != null">
				size = #{record.size,jdbcType=VARCHAR},
			</if>
			<if test="record.productName != null">
				product_name = #{record.productName,jdbcType=VARCHAR},
			</if>
			<if test="record.quantity != null">
				quantity = #{record.quantity,jdbcType=INTEGER},
			</if>
			<if test="record.extras != null">
				extras = #{record.extras,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetailExample">
		delete from wx_t_smartcomm_order_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetailExample" resultType="int">
		select count(1) from wx_t_smartcomm_order_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetailExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_smartcomm_order_detail
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.materialplatformorder.model.SmaOrderDetailExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_smartcomm_order_detail
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.order_line_no, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.order_no,
			 t1.product_code, t1.sku, t1.price, t1.ext_flag, t1.color, t1.size, t1.product_name, t1.quantity, t1.extras
		  from wx_t_smartcomm_order_detail t1
		 where 1=1
	</select>
</mapper>
