<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget.dao.BudgetEditLogMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.budget.model.BudgetEditLog">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="program_id" property="programId" jdbcType="BIGINT"/>
		<result column="log_key" property="logKey" jdbcType="VARCHAR"/>
		<result column="log_type" property="logType" jdbcType="INTEGER"/>
		<result column="value_offset" property="valueOffset" jdbcType="NUMERIC"/>
		<result column="value_final" property="valueFinal" jdbcType="NUMERIC"/>
		<result column="input_user_permission_weight" property="inputUserPermissionWeight" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
		<result column="create_user_type" property="createUserType" jdbcType="VARCHAR"/>
		<result column="asm_name" property="asmName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,program_id,log_key,log_type,value_offset,value_final,input_user_permission_weight,create_user_id,create_time
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.budget.model.BudgetEditLog">
		update wx_t_budget_edit_log
		<set>
			<if test="programId != null" >
				program_id = #{programId,jdbcType=BIGINT},
			</if>
			<if test="logKey != null" >
				log_key = #{logKey,jdbcType=VARCHAR},
			</if>
			<if test="logType != null" >
				log_type = #{logType,jdbcType=INTEGER},
			</if>
			<if test="valueOffset != null" >
				value_offset = #{valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="valueFinal != null" >
				value_final = #{valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="inputUserPermissionWeight != null" >
				input_user_permission_weight = #{inputUserPermissionWeight,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.budget.model.BudgetEditLogExample">
    	delete from wx_t_budget_edit_log
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.budget.model.BudgetEditLog">
		insert into wx_t_budget_edit_log
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="programId != null">
				program_id,
			</if>
			<if test="logKey != null">
				log_key,
			</if>
			<if test="logType != null">
				log_type,
			</if>
			<if test="valueOffset != null">
				value_offset,
			</if>
			<if test="valueFinal != null">
				value_final,
			</if>
			<if test="inputUserPermissionWeight != null">
				input_user_permission_weight,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="programId != null">
				#{programId,jdbcType=BIGINT},
			</if>
			<if test="logKey != null">
				#{logKey,jdbcType=VARCHAR},
			</if>
			<if test="logType != null">
				#{logType,jdbcType=INTEGER},
			</if>
			<if test="valueOffset != null">
				#{valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="valueFinal != null">
				#{valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="inputUserPermissionWeight != null">
				#{inputUserPermissionWeight,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_budget_edit_log
		<set>
			<if test="record.programId != null">
				program_id = #{record.programId,jdbcType=BIGINT},
			</if>
			<if test="record.logKey != null">
				log_key = #{record.logKey,jdbcType=VARCHAR},
			</if>
			<if test="record.logType != null">
				log_type = #{record.logType,jdbcType=INTEGER},
			</if>
			<if test="record.valueOffset != null">
				value_offset = #{record.valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="record.valueFinal != null">
				value_final = #{record.valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="record.inputUserPermissionWeight != null">
				input_user_permission_weight = #{record.inputUserPermissionWeight,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.budget.model.BudgetEditLogExample">
		delete from wx_t_budget_edit_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.budget.model.BudgetEditLogExample" resultType="int">
		select count(1) from wx_t_budget_edit_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetEditLogExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_budget_edit_log
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetEditLogExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_budget_edit_log
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.program_id, t1.log_key, t1.log_type, t1.value_offset, t1.value_final, t1.input_user_permission_weight,
			 t1.create_user_id, t1.create_time, u1.ch_name create_user_name,
			 case when t1.log_key like '/1%' then 'MKT' else 'Sales' end create_user_type
		  from wx_t_budget_edit_log t1
		  left join wx_t_user u1 on t1.create_user_id=u1.user_id
		 where 1=1
			<if test="programId != null">
				and t1.program_id = #{programId,jdbcType=BIGINT}
			</if>
			<if test="logKey != null">
				and t1.log_key = #{logKey,jdbcType=VARCHAR}
			</if>
			<if test="inputUserPermissionWeight != null and inputUserPermissionWeight != 0" >
				and t1.input_user_permission_weight &amp; #{inputUserPermissionWeight,jdbcType=INTEGER} > 0
			</if>
	</select>
	
	<select id="queryByVoParams" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetEditLogParams">
		select t1.id, t1.program_id, t1.log_key, t1.log_type, t1.value_offset, t1.value_final, t1.input_user_permission_weight,
			 t1.create_user_id, t1.create_time, u1.ch_name create_user_name,
			 case when t1.log_key like '/1%' then 'MKT' else 'Sales' end create_user_type
			 <if test="includeAsm">
			 ,(select ba1.supervisor_name from wx_t_budget_asm ba1 where ba1.program_id=t1.program_id and '/' + convert(nvarchar(20), ba1.input_user) + '/' + ba1.expense_code + '/' + ba1.supervisor_cai=t1.log_key) asm_name
			 </if>
		  from wx_t_budget_edit_log t1
		  left join wx_t_user u1 on t1.create_user_id=u1.user_id
		 where 1=1
			<if test="programId != null">
				and t1.program_id = #{programId,jdbcType=BIGINT}
			</if>
			<if test="logKey != null">
				and t1.log_key = #{logKey,jdbcType=VARCHAR}
			</if>
			<if test="inputUserPermissionWeight != null and inputUserPermissionWeight != 0" >
				and t1.input_user_permission_weight &amp; #{inputUserPermissionWeight,jdbcType=INTEGER} > 0
			</if>
			<if test="regionNames != null">
			and exists (select 1 from dw_customer_region_sales_supervisor_rel crss where crss.region_name in (
		<foreach collection="regionNames" index="index" item="item" separator=",">
			'${item}'
		</foreach>
			) and t1.log_key like '/%/' + crss.suppervisor_cai)
			</if>
			<if test="expenseCode != null and expenseCode != ''">
			and t1.log_key like '/_/' + #{expenseCode,jdbcType=VARCHAR} + '/%'
			</if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_budget_edit_log (program_id, log_key, log_type, value_offset, value_final, input_user_permission_weight, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.programId, jdbcType=BIGINT}, #{item.logKey, jdbcType=VARCHAR}, #{item.logType, jdbcType=INTEGER}, #{item.valueOffset, jdbcType=NUMERIC}, #{item.valueFinal, jdbcType=NUMERIC}, #{item.inputUserPermissionWeight, jdbcType=INTEGER}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
