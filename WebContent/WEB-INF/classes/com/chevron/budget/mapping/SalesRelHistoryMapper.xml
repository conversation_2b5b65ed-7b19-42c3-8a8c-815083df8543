<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget.dao.SalesRelHistoryMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.budget.model.SalesRelHistory">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="program_id" property="programId" jdbcType="BIGINT"/>
		<result column="sales_channel_value" property="salesChannelValue" jdbcType="INTEGER"/>
		<result column="channel_manager_cai" property="channelManagerCai" jdbcType="VARCHAR"/>
		<result column="channel_manager_name" property="channelManagerName" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="supervisor_cai" property="supervisorCai" jdbcType="VARCHAR"/>
		<result column="supervisor_name" property="supervisorName" jdbcType="VARCHAR"/>
		<result column="sales_cai" property="salesCai" jdbcType="VARCHAR"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
	</resultMap>
		<resultMap id="ExtendResultMap" type="com.chevron.promotev2.model.SalesRelHistoryExtend">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="plan_id" property="planId" jdbcType="BIGINT"/>
		<result column="sales_channel_value" property="salesChannelValue" jdbcType="INTEGER"/>
		<result column="region_manager_cai" property="regionManagerCai" jdbcType="VARCHAR"/>
		<result column="region_manager_name" property="regionManagerName" jdbcType="VARCHAR"/>
		<result column="channel_manager_cai" property="channelManagerCai" jdbcType="VARCHAR"/>
		<result column="channel_manager_name" property="channelManagerName" jdbcType="VARCHAR"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="suppervisor_cai" property="supervisorCai" jdbcType="VARCHAR"/>
		<result column="suppervisor_name" property="supervisorName" jdbcType="VARCHAR"/>
		<result column="sales_cai" property="salesCai" jdbcType="VARCHAR"/>
		<result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,program_id,sales_channel_value,channel_manager_cai,channel_manager_name,region_name,supervisor_cai,
		supervisor_name,sales_cai,sales_name,distributor_id,create_user_id,create_time
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.budget.model.SalesRelHistory">
		update wx_t_sales_rel_history
		<set>
			<if test="programId != null" >
				program_id = #{programId,jdbcType=BIGINT},
			</if>
			<if test="salesChannelValue != null" >
				sales_channel_value = #{salesChannelValue,jdbcType=INTEGER},
			</if>
			<if test="channelManagerCai != null" >
				channel_manager_cai = #{channelManagerCai,jdbcType=VARCHAR},
			</if>
			<if test="channelManagerName != null" >
				channel_manager_name = #{channelManagerName,jdbcType=VARCHAR},
			</if>
			<if test="regionName != null" >
				region_name = #{regionName,jdbcType=VARCHAR},
			</if>
			<if test="supervisorCai != null" >
				supervisor_cai = #{supervisorCai,jdbcType=VARCHAR},
			</if>
			<if test="supervisorName != null" >
				supervisor_name = #{supervisorName,jdbcType=VARCHAR},
			</if>
			<if test="salesCai != null" >
				sales_cai = #{salesCai,jdbcType=VARCHAR},
			</if>
			<if test="salesName != null" >
				sales_name = #{salesName,jdbcType=VARCHAR},
			</if>
			<if test="distributorId != null" >
				distributor_id = #{distributorId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.budget.model.SalesRelHistoryExample">
    	delete from wx_t_sales_rel_history
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.budget.model.SalesRelHistory">
		insert into wx_t_sales_rel_history
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="programId != null">
				program_id,
			</if>
			<if test="salesChannelValue != null">
				sales_channel_value,
			</if>
			<if test="channelManagerCai != null">
				channel_manager_cai,
			</if>
			<if test="channelManagerName != null">
				channel_manager_name,
			</if>
			<if test="regionName != null">
				region_name,
			</if>
			<if test="supervisorCai != null">
				supervisor_cai,
			</if>
			<if test="supervisorName != null">
				supervisor_name,
			</if>
			<if test="salesCai != null">
				sales_cai,
			</if>
			<if test="salesName != null">
				sales_name,
			</if>
			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="programId != null">
				#{programId,jdbcType=BIGINT},
			</if>
			<if test="salesChannelValue != null">
				#{salesChannelValue,jdbcType=INTEGER},
			</if>
			<if test="channelManagerCai != null">
				#{channelManagerCai,jdbcType=VARCHAR},
			</if>
			<if test="channelManagerName != null">
				#{channelManagerName,jdbcType=VARCHAR},
			</if>
			<if test="regionName != null">
				#{regionName,jdbcType=VARCHAR},
			</if>
			<if test="supervisorCai != null">
				#{supervisorCai,jdbcType=VARCHAR},
			</if>
			<if test="supervisorName != null">
				#{supervisorName,jdbcType=VARCHAR},
			</if>
			<if test="salesCai != null">
				#{salesCai,jdbcType=VARCHAR},
			</if>
			<if test="salesName != null">
				#{salesName,jdbcType=VARCHAR},
			</if>
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_sales_rel_history
		<set>
			<if test="record.programId != null">
				program_id = #{record.programId,jdbcType=BIGINT},
			</if>
			<if test="record.salesChannelValue != null">
				sales_channel_value = #{record.salesChannelValue,jdbcType=INTEGER},
			</if>
			<if test="record.channelManagerCai != null">
				channel_manager_cai = #{record.channelManagerCai,jdbcType=VARCHAR},
			</if>
			<if test="record.channelManagerName != null">
				channel_manager_name = #{record.channelManagerName,jdbcType=VARCHAR},
			</if>
			<if test="record.regionName != null">
				region_name = #{record.regionName,jdbcType=VARCHAR},
			</if>
			<if test="record.supervisorCai != null">
				supervisor_cai = #{record.supervisorCai,jdbcType=VARCHAR},
			</if>
			<if test="record.supervisorName != null">
				supervisor_name = #{record.supervisorName,jdbcType=VARCHAR},
			</if>
			<if test="record.salesCai != null">
				sales_cai = #{record.salesCai,jdbcType=VARCHAR},
			</if>
			<if test="record.salesName != null">
				sales_name = #{record.salesName,jdbcType=VARCHAR},
			</if>
			<if test="record.distributorId != null">
				distributor_id = #{record.distributorId,jdbcType=BIGINT},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.budget.model.SalesRelHistoryExample">
		delete from wx_t_sales_rel_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.budget.model.SalesRelHistoryExample" resultType="int">
		select count(1) from wx_t_sales_rel_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.SalesRelHistoryExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_sales_rel_history
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.SalesRelHistoryExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_sales_rel_history
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.program_id, t1.sales_channel_value, t1.channel_manager_cai, t1.channel_manager_name, t1.region_name,
			 t1.supervisor_cai, t1.supervisor_name, t1.sales_cai, t1.sales_name, t1.distributor_id, t1.create_user_id,
			 t1.create_time
		  from wx_t_sales_rel_history t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_sales_rel_history (program_id, sales_channel_value, channel_manager_cai, channel_manager_name, region_name, supervisor_cai, supervisor_name, sales_cai, sales_name, distributor_id, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.programId, jdbcType=BIGINT}, #{item.salesChannelValue, jdbcType=INTEGER}, #{item.channelManagerCai, jdbcType=VARCHAR}, #{item.channelManagerName, jdbcType=VARCHAR}, #{item.regionName, jdbcType=VARCHAR}, #{item.supervisorCai, jdbcType=VARCHAR}, #{item.supervisorName, jdbcType=VARCHAR}, #{item.salesCai, jdbcType=VARCHAR}, #{item.salesName, jdbcType=VARCHAR}, #{item.distributorId, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	<select id="selectSalesRel" resultMap="BaseResultMap" parameterType="map">
	select a.* from (
		select t1.sales_channel_value, t1.channel_manager_cai, t1.channel_manager_name, t1.region_name,
			 t1.supervisor_cai, t1.supervisor_name, t1.sales_cai, t1.sales_name, t1.distributor_id,
			 (select max(c1.customer_name_cn) from dw_customer c1 where t1.distributor_id=c1.distributor_id) customer_name
		  from wx_t_sales_rel_history t1
		  left join wx_t_budget_program bp1 on t1.program_id=bp1.id
		 where t1.program_id=#{programId, jdbcType=BIGINT}
		 and bp1.status=100 and t1.sales_channel_value&amp;#{includeChannels, jdbcType=INTEGER}>0
		 <if test="channelManager">
		 and t1.channel_manager_cai=#{loginUserCai, jdbcType=VARCHAR}
		 </if>
		 union all 
		 select di1.dic_item_desc sales_channel_value, rsc.channel_manager_cai, rsc.channel_manager_name, rsc.region_name,
				crss.suppervisor_cai, crss.suppervisor_name, crss.sales_cai, crss.sales_name, crss.distributor_id, crss.customer_name_cn collate Chinese_PRC_CI_AS 
			from dw_customer_region_sales_supervisor_rel crss
			join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name
			join wx_t_budget_program bp1 on bp1.status!=100 and bp1.id=#{programId, jdbcType=BIGINT}
			join wx_t_dic_item di1 on di1.dic_type_code='Sys.salesChannel' 
<!-- 			and di1.dic_item_code=rsc.sales_channel_name
 -->			where convert(int, di1.dic_item_desc)&amp;bp1.sales_channels&amp;#{includeChannels, jdbcType=INTEGER}>0
			and exists(select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' and vtm1.value_after_transform=rsc.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I']]>, 'CDM'))
		 <if test="channelManager">
		 and rsc.channel_manager_cai=#{loginUserCai, jdbcType=VARCHAR}
		 </if>
			) a order by a.sales_channel_value, a.channel_manager_cai, a.supervisor_cai, a.sales_cai
	</select>
	<select id="selectSalesRelForMkt" resultMap="ExtendResultMap" parameterType="map">
		 	select distinct rsc.sales_channel_name, u.cai region_manager_cai ,u.ch_name region_manager_name ,rsc.channel_manager_cai,  (select tu.ch_name from wx_t_user tu where tu.cai=rsc.channel_manager_cai and tu.status=1) channel_manager_name, rsc.region_name,
				crss.suppervisor_cai, crss.suppervisor_name, crss.sales_cai, crss.sales_name, crss.distributor_id, crss.customer_name_cn collate Chinese_PRC_CI_AS customer_name 
				from dw_customer_region_sales_supervisor_rel crss
				join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name
				join wx_t_partner_responsible_main prm on crss.sales_cai = prm.sales_cai
				JOIN wx_t_partner_o2o_enterprise pe ON pe.distributor_id = crss.distributor_id
				JOIN wx_t_organization o ON o.id = pe.partner_id
				JOIN wx_t_user u ON u.user_id = prm.user_id AND u.status= 1 
				LEFT JOIN wx_t_userrole ur ON u.user_id = ur.user_id
				LEFT JOIN wx_t_role r ON ur.role_id = r.role_id 
				where 1=1 
				and exists (select 1 from wx_t_value_transform_map vtm1 where vtm1.transform_type='SalesChannelMapping' and vtm1.value_after_transform=rsc.sales_channel_name and vtm1.value_before_transform in (<![CDATA['C&I']]>, 'CDM'))
				AND r.ch_role_name = ( SELECT p0.code FROM wx_t_properties p0 WHERE p0.codetype= 'promote.mktRegionApproverRole' ) 
				<if test="channelManager">
	    		and rsc.channel_manager_cai=#{loginUserCai, jdbcType=VARCHAR}
	    		</if>
	    		<if test="suppervisor">
	   		    and crss.suppervisor_cai=#{loginUserCai, jdbcType=VARCHAR}
	   		    </if>
			order by region_manager_cai,channel_manager_cai, suppervisor_cai, sales_cai
	</select>
	<insert id="insertBySelectResult" parameterType="map">
		insert into wx_t_sales_rel_history 
			(program_id,sales_channel_value,
			channel_manager_cai,channel_manager_name,region_name,
			supervisor_cai,supervisor_name,sales_cai,sales_name,
			distributor_id,create_user_id,create_time)
			
			select  	
				#{programId, jdbcType=BIGINT} program_id,di1.dic_item_desc sales_channel_value,
				rsc.channel_manager_cai,rsc.channel_manager_name,rsc.region_name,
				crss.suppervisor_cai,crss.suppervisor_name,crss.sales_cai,crss.sales_name,
				crss.distributor_id,#{createUserId,jdbcType=BIGINT} create_user_id,#{createTime,jdbcType=TIMESTAMP} create_time
			from dw_customer_region_sales_supervisor_rel crss
				join dw_region_sales_channel_rel rsc on crss.region_name=rsc.region_name
				<!--  status=100 -->
				join wx_t_budget_program bp1 on bp1.status=100 and bp1.id=#{programId, jdbcType=BIGINT}
				join wx_t_dic_item di1 on di1.dic_type_code='Sys.salesChannel' and di1.dic_item_code=rsc.sales_channel_name
				where convert(int, di1.dic_item_desc)&amp;bp1.sales_channels>0
	</insert>
</mapper>
