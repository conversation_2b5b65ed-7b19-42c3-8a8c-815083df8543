<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.budget.dao.BudgetImpExpValueMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.budget.model.BudgetImpExpValue">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="expense_code" property="expenseCode" jdbcType="VARCHAR"/>
		<result column="expense_id" property="expenseId" jdbcType="BIGINT"/>
		<result column="expense_value" property="expenseValue" jdbcType="NUMERIC"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,expense_code,expense_id,expense_value
	</sql>

	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.budget.model.BudgetImpExpValue">
		update wx_t_budget_imp_exp_value
		<set>
			<if test="expenseCode != null" >
				expense_code = #{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="expenseId != null" >
				expense_id = #{expenseId,jdbcType=BIGINT},
			</if>
			<if test="expenseValue != null" >
				expense_value = #{expenseValue,jdbcType=NUMERIC},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.budget.model.BudgetImpExpValueExample">
    	delete from wx_t_budget_imp_exp_value
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.budget.model.BudgetImpExpValue">
		insert into wx_t_budget_imp_exp_value
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="expenseCode != null">
				expense_code,
			</if>
			<if test="expenseId != null">
				expense_id,
			</if>
			<if test="expenseValue != null">
				expense_value,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="expenseCode != null">
				#{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="expenseId != null">
				#{expenseId,jdbcType=BIGINT},
			</if>
			<if test="expenseValue != null">
				#{expenseValue,jdbcType=NUMERIC},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_budget_imp_exp_value
		<set>
			<if test="record.expenseCode != null">
				expense_code = #{record.expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="record.expenseId != null">
				expense_id = #{record.expenseId,jdbcType=BIGINT},
			</if>
			<if test="record.expenseValue != null">
				expense_value = #{record.expenseValue,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.budget.model.BudgetImpExpValueExample">
		delete from wx_t_budget_imp_exp_value
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.budget.model.BudgetImpExpValueExample" resultType="int">
		select count(1) from wx_t_budget_imp_exp_value
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetImpExpValueExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_budget_imp_exp_value
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.budget.model.BudgetImpExpValueExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_budget_imp_exp_value
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.expense_code, t1.expense_id, t1.expense_value
		  from wx_t_budget_imp_exp_value t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_budget_imp_exp_value (expense_code, expense_id, expense_value) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")">
			'${item.expenseCode}', (select t.id from wx_t_budget_import_expense t 
			where t.expense_month=#{expenseMonth, jdbcType=DATE} and t.distributor_id=${item.budgetImportExpense.distributorId} 
			and t.bu='${item.budgetImportExpense.bu}' and t.version_no=${item.budgetImportExpense.versionNo} 
			and t.batch_no='${item.budgetImportExpense.batchNo}'), ${item.expenseValue}
			</trim>
		</foreach>
	</insert>
</mapper>
