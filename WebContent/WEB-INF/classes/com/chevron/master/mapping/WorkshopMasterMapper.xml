<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.master.dao.WorkshopMasterMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.master.model.WorkshopMaster">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="work_shop_name" property="workshopName" jdbcType="VARCHAR"/>
		<result column="region_id" property="regionId" jdbcType="BIGINT"/>
		<result column="work_shop_address" property="workshopAddress" jdbcType="VARCHAR"/>
		<result column="longitude" property="longitude" jdbcType="NUMERIC"/>
		<result column="latitude" property="latitude" jdbcType="NUMERIC"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
		<result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
		<result column="contact_person_tel" property="contactPersonTel" jdbcType="VARCHAR"/>
		<result column="from_source" property="fromSource" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="business_weight" property="businessWeight" jdbcType="INTEGER"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="excute_user_id" property="excuteUserId" jdbcType="BIGINT"/>
		<result column="photo_id" property="photoId" jdbcType="VARCHAR"/>
		<result column="work_shop_name_py" property="workShopNamePy" jdbcType="VARCHAR"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="ext_flag2" property="extFlag2" jdbcType="INTEGER"/>
		<result column="ext_flag3" property="extFlag3" jdbcType="INTEGER"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="customer_type" property="customerType" jdbcType="INTEGER"/>
		<result column="dms_key" property="dmsKey" jdbcType="VARCHAR"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="ext_property6" property="extProperty6" jdbcType="VARCHAR"/>
		<result column="ext_property7" property="extProperty7" jdbcType="VARCHAR"/>
		<result column="ext_property8" property="extProperty8" jdbcType="VARCHAR"/>
		<result column="ext_property9" property="extProperty9" jdbcType="VARCHAR"/>
		<result column="ext_property10" property="extProperty10" jdbcType="VARCHAR"/>
		<result column="ext_property11" property="extProperty11" jdbcType="VARCHAR"/>
		<result column="ext_property12" property="extProperty12" jdbcType="VARCHAR"/>
		<result column="ext_property13" property="extProperty13" jdbcType="VARCHAR"/>
		<result column="ext_property14" property="extProperty14" jdbcType="VARCHAR"/>
		<result column="ext_property15" property="extProperty15" jdbcType="VARCHAR"/>
		<result column="ext_property16" property="extProperty16" jdbcType="VARCHAR"/>
		<result column="ext_property17" property="extProperty17" jdbcType="VARCHAR"/>
		<result column="ext_property18" property="extProperty18" jdbcType="VARCHAR"/>
		<result column="ext_property19" property="extProperty19" jdbcType="VARCHAR"/>
		<result column="ext_property20" property="extProperty20" jdbcType="TIMESTAMP"/>
		<result column="ext_property21" property="extProperty21" jdbcType="VARCHAR"/>
		<result column="ext_property22" property="extProperty22" jdbcType="VARCHAR"/>
		<result column="ext_property23" property="extProperty23" jdbcType="VARCHAR"/>
		<result column="ext_property24" property="extProperty24" jdbcType="VARCHAR"/>
		<result column="ext_property25" property="extProperty25" jdbcType="VARCHAR"/>
		<result column="ext_property26" property="extProperty26" jdbcType="VARCHAR"/>
		<result column="ext_property27" property="extProperty27" jdbcType="VARCHAR"/>
		<result column="ext_property28" property="extProperty28" jdbcType="VARCHAR"/>
		<result column="ext_property29" property="extProperty29" jdbcType="VARCHAR"/>
		
		<result column="province_name" property="provinceName" jdbcType="NVARCHAR" />
		<result column="city_name" property="cityName" jdbcType="NVARCHAR" />
		<result column="province_id" property="provinceId" jdbcType="BIGINT" />
		<result column="city_id" property="cityId" jdbcType="BIGINT" />
		<result column="execute_user_name" property="executeUserName" jdbcType="NVARCHAR" />
		<result column="att_id" property="attId" jdbcType="BIGINT" />
		<result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
		<result column="workshop_property" property="workshopProperty" jdbcType="INTEGER" />
		<result column="workshop_property2" property="workshopProperty2" jdbcType="INTEGER" />
	    <result column="type_text" property="typeText" jdbcType="VARCHAR"/> 
	    <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/> 
	    <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/> 
	    <result column="dist_name" property="distName" jdbcType="NVARCHAR" />
	    <result column="active_time" property="activeTime" jdbcType="TIMESTAMP" />
		<result column="activation_time" property="activationTime" jdbcType="TIMESTAMP" />
	    <result column="operation_flag" property="operationFlag" jdbcType="INTEGER"/>
	    <result column="work_shop_range" property="workShopRange" jdbcType="NUMERIC" />
	    <result column="task_time" property="taskTime" jdbcType="TIMESTAMP"/>
	    <result column="region_name" property="region" jdbcType="VARCHAR"/>
		<result column="business_weight_text" property="businessWeightText" jdbcType="NVARCHAR" />
		<result column="customer_type_text" property="customerTypeText" jdbcType="NVARCHAR" />
	    <result column="sales_name" property="salesName" jdbcType="VARCHAR"/>
		<result column="join_ck_plan" property="joinCkPlan" jdbcType="INTEGER"/>
		<result column="route" property="route" jdbcType="VARCHAR"/>
	</resultMap>
	
	
	<!-- 查询Example条件 -->
	<!-- 
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	更新操作Example条件
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	查询表字段集合
	<sql id="Base_Column_List">
		id,work_shop_name,region_id,work_shop_address,longitude,latitude,grade_abc,type,signboard_type,contact_person,
		contact_person_tel,is_multiple,area,seats_num,trench_numb,two_column_elevator_numb,four_column_elevator_numb,
		monthly_oil_sales_volume,monthly_chevron_oil_sales_volume,from_source,delete_flag,business_weight,work_shop_code,
		employees_num,service_scope,business_license_code,reserve_service_tel,business_detail,harvest_person,
		harvest_person_tel,butt_in_charge_person,butt_in_charge_person_tel,bank_acount_name,bank_acount,bank,credit_rating,
		create_time,update_time,creator,remark,status,business_license_expiry_date,mechanic_count,excute_user_id,photo_id,
		source,signed_type,scale,region_name,work_shop_name_py,active_fee_flag,management_fee_flag,workshopscore,
		active_seats_num,isgd_flag,workshop_owner,workshop_owner_mobile,workshop_tel,operation_year,operation_periods,
		is_holiday_close,payment_type,digital_management_intention,level_promotion_intention,auth_product_agency,
		sale_major_oil_viscosity,sale_major_oil_type,sale_major_oil_brand,car_maintain_numb,other_equipment,
		other_collocation,nearby_village_numb,spread_way,marketing_concept,has_warehouse,management_maturity,
		support_maintain_flag,shop_recruitment,join_shop_gift_plan,join_location_plan,shop_recruitment_update_time,
		sales_channels,delo_type,volume_month,sale_major_delo_product,delete_batch_no,dms_key,dms_match_update_time,
		dms_match_update_user,recruitment_amount,recruitment_confirmed,business_scope,ext_flag
	</sql>
 -->
 
 <!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,creator,create_time,update_user_id,update_time,work_shop_name,region_id,work_shop_address,longitude,latitude,type,
		contact_person,contact_person_tel,from_source,delete_flag,business_weight,remark,status,excute_user_id,photo_id,
		work_shop_name_py,ext_flag,ext_flag2,ext_flag3,partner_id,customer_type,dms_key,ext_property1,ext_property2,
		ext_property3,ext_property4,ext_property5,ext_property6,ext_property7,ext_property8,ext_property9,ext_property10,
		ext_property11,ext_property12,ext_property13,ext_property14,ext_property15,ext_property16,ext_property17,
		ext_property18,ext_property19,ext_property20,ext_property21,ext_property22,ext_property23,ext_property24,
		ext_property25,ext_property26,ext_property27,ext_property28,ext_property29
	</sql>
 
 
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.master.model.WorkshopMaster">
		update wx_t_work_shop
		<set>
			<if test="creator != null" >
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="activationTime != null" >
				activation_time = #{activationTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="workshopName != null" >
				work_shop_name = #{workshopName,jdbcType=VARCHAR},
				work_shop_name_py = left(dbo.f_GetPyToAboutHanyu(#{workshopName,jdbcType=NVARCHAR}),500),
			</if>
			<if test="regionId != null" >
				region_id = #{regionId,jdbcType=BIGINT},
			</if>
			<if test="workshopAddress != null" >
				work_shop_address = #{workshopAddress,jdbcType=VARCHAR},
			</if>
			<if test="longitude != null" >
				longitude = #{longitude,jdbcType=NUMERIC},
			</if>
			<if test="latitude != null" >
				latitude = #{latitude,jdbcType=NUMERIC},
			</if>
			<if test="type != null" >
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null" >
				contact_person = #{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="contactPersonTel != null" >
				contact_person_tel = #{contactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="fromSource != null" >
				from_source = #{fromSource,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="businessWeight != null" >
				business_weight = #{businessWeight,jdbcType=INTEGER},
			</if>
			<if test="remark != null" >
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="excuteUserId != null" >
				excute_user_id = #{excuteUserId,jdbcType=BIGINT},
			</if>
			<if test="photoId != null" >
				photo_id = #{photoId,jdbcType=VARCHAR},
			</if>
			<!-- [门店]1<<0-坐标确认,1-MKT已维护,2-金富力用户,4-网站门店导航,5-德乐店招户外广告,
			6-德乐店招车身广告,7-新店礼包申请门店,8-经销商老板门店确认, 9-经销商老板审核发放积分,
			10-参与德乐400MGX陈列活动,13-全合成门店(原蓝盾合作门店）,14-黑金店,19-中超店招店，30-不奖励技师注册积分 -->
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<choose>
				<when test="removedExtFlag != null and newExtFlag != null">
				ext_flag = (ext_flag - (ext_flag &amp; #{removedExtFlag,jdbcType=INTEGER})) | #{newExtFlag,jdbcType=INTEGER},
				</when>
				<when test="newExtFlag != null">
				ext_flag = ext_flag | #{newExtFlag,jdbcType=INTEGER},
				</when>
				<when test="removedExtFlag != null">
				ext_flag = ext_flag - (ext_flag &amp; #{removedExtFlag,jdbcType=INTEGER}),
				</when>
			</choose>
			<!-- [门店、车队] 意愿状态 1-意愿合作客户，2-潜在合作客户 -->
			<if test="extFlag2 != null" >
				ext_flag2 = #{extFlag2,jdbcType=INTEGER},
			</if>
			<if test="newExtFlag2 != null">
				ext_flag2 = ext_flag2 | #{newExtFlag2,jdbcType=INTEGER},
			</if>
			<if test="extFlag3 != null" >
				ext_flag3 = #{extFlag3,jdbcType=INTEGER},
			</if>
			<if test="newExtFlag3 != null">
				ext_flag3 = ext_flag3 | #{newExtFlag3,jdbcType=INTEGER},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="customerType != null" >
				customer_type = #{customerType,jdbcType=INTEGER},
			</if>
			<choose>
				<when test="dmsKey == '-1'">
				dms_key = null,
				</when>
				<when test="dmsKey != null">
				dms_key = #{dmsKey,jdbcType=VARCHAR},
				</when>
			</choose>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<!-- 客户联系人 -->
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null" >
				ext_property6 = #{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null" >
				ext_property7 = #{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null" >
				ext_property8 = #{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null" >
				ext_property9 = #{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="extProperty10 != null" >
				ext_property10 = #{extProperty10,jdbcType=VARCHAR},
			</if>
			<if test="extProperty11 != null" >
				ext_property11 = #{extProperty11,jdbcType=VARCHAR},
			</if>
			<if test="extProperty12 != null" >
				ext_property12 = #{extProperty12,jdbcType=VARCHAR},
			</if>
			<if test="extProperty13 != null" >
				ext_property13 = #{extProperty13,jdbcType=VARCHAR},
			</if>
			<if test="extProperty14 != null" >
				ext_property14 = #{extProperty14,jdbcType=VARCHAR},
			</if>
			<if test="extProperty15 != null" >
				ext_property15 = #{extProperty15,jdbcType=VARCHAR},
			</if>
			<if test="extProperty16 != null" >
				ext_property16 = #{extProperty16,jdbcType=VARCHAR},
			</if>
			<if test="extProperty17 != null" >
				ext_property17 = #{extProperty17,jdbcType=VARCHAR},
			</if>
			<if test="extProperty18 != null" >
				ext_property18 = #{extProperty18,jdbcType=VARCHAR},
			</if>
			<if test="extProperty19 != null" >
				ext_property19 = #{extProperty19,jdbcType=VARCHAR},
			</if>
			<if test="extProperty20 != null" >
				ext_property20 = #{extProperty20,jdbcType=TIMESTAMP},
			</if>
			<if test="extProperty21 != null" >
				ext_property21 = #{extProperty21,jdbcType=VARCHAR},
			</if>
			<if test="extProperty22 != null" >
				ext_property22 = #{extProperty22,jdbcType=VARCHAR},
			</if>
			<if test="extProperty23 != null" >
				ext_property23 = #{extProperty23,jdbcType=VARCHAR},
			</if>
			<if test="extProperty24 != null" >
				ext_property24 = #{extProperty24,jdbcType=VARCHAR},
			</if>
			<if test="extProperty25 != null" >
				ext_property25 = #{extProperty25,jdbcType=VARCHAR},
			</if>
			<if test="extProperty26 != null" >
				ext_property26 = #{extProperty26,jdbcType=VARCHAR},
			</if>
			<if test="extProperty27 != null" >
				ext_property27 = #{extProperty27,jdbcType=VARCHAR},
			</if>
			<if test="extProperty28 != null" >
				ext_property28 = #{extProperty28,jdbcType=VARCHAR},
			</if>
			<if test="extProperty29 != null" >
				ext_property29 = #{extProperty29,jdbcType=VARCHAR},
			</if>
			<if test="joinCkPlan != null">
				join_ck_plan = #{joinCkPlan,jdbcType=INTEGER},
			</if>
            <if test="route != null and route != ''">
                route = #{route,jdbcType=VARCHAR},
            </if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.master.model.WorkshopMasterExample">
    	delete from wx_t_work_shop
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.master.model.WorkshopMaster" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_work_shop
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="creator != null">
				creator,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="activationTime != null">
				activation_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="workshopName != null">
				work_shop_name,
				work_shop_name_py,
			</if>
			<if test="regionId != null">
				region_id,
			</if>
			<if test="workshopAddress != null">
				work_shop_address,
			</if>
			<if test="longitude != null">
				longitude,
			</if>
			<if test="latitude != null">
				latitude,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="contactPerson != null">
				contact_person,
			</if>
			<if test="contactPersonTel != null">
				contact_person_tel,
			</if>
			<if test="fromSource != null">
				from_source,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="businessWeight != null">
				business_weight,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="excuteUserId != null">
				excute_user_id,
			</if>
			<if test="photoId != null">
				photo_id,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="extFlag2 != null">
				ext_flag2,
			</if>
			<if test="extFlag3 != null">
				ext_flag3,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="customerType != null">
				customer_type,
			</if>
			<if test="dmsKey != null">
				dms_key,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="extProperty6 != null">
				ext_property6,
			</if>
			<if test="extProperty7 != null">
				ext_property7,
			</if>
			<if test="extProperty8 != null">
				ext_property8,
			</if>
			<if test="extProperty9 != null">
				ext_property9,
			</if>
			<if test="extProperty10 != null">
				ext_property10,
			</if>
			<if test="extProperty11 != null">
				ext_property11,
			</if>
			<if test="extProperty12 != null">
				ext_property12,
			</if>
			<if test="extProperty13 != null">
				ext_property13,
			</if>
			<if test="extProperty14 != null">
				ext_property14,
			</if>
			<if test="extProperty15 != null">
				ext_property15,
			</if>
			<if test="extProperty16 != null">
				ext_property16,
			</if>
			<if test="extProperty17 != null">
				ext_property17,
			</if>
			<if test="extProperty18 != null">
				ext_property18,
			</if>
			<if test="extProperty19 != null">
				ext_property19,
			</if>
			<if test="extProperty20 != null">
				ext_property20,
			</if>
			<if test="extProperty21 != null">
				ext_property21,
			</if>
			<if test="extProperty22 != null">
				ext_property22,
			</if>
			<if test="extProperty23 != null">
				ext_property23,
			</if>
			<if test="extProperty24 != null">
				ext_property24,
			</if>
			<if test="extProperty25 != null">
				ext_property25,
			</if>
			<if test="extProperty26 != null">
				ext_property26,
			</if>
			<if test="extProperty27 != null">
				ext_property27,
			</if>
			<if test="extProperty28 != null">
				ext_property28,
			</if>
			<if test="extProperty29 != null">
				ext_property29,
			</if>
			<if test="customerCode != null">
				customer_code,
			</if>
			<if test="joinCkPlan != null">
				join_ck_plan,
			</if>
            <if test="route != null and route != ''">
                route,
            </if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="creator != null">
				#{creator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="activationTime != null">
				#{activationTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="workshopName != null">
				#{workshopName,jdbcType=VARCHAR},
				left(dbo.f_GetPyToAboutHanyu(#{workshopName,jdbcType=NVARCHAR}),500),
			</if>
			<if test="regionId != null">
				#{regionId,jdbcType=BIGINT},
			</if>
			<if test="workshopAddress != null">
				#{workshopAddress,jdbcType=VARCHAR},
			</if>
			<if test="longitude != null">
				#{longitude,jdbcType=NUMERIC},
			</if>
			<if test="latitude != null">
				#{latitude,jdbcType=NUMERIC},
			</if>
			<if test="type != null">
				#{type,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null">
				#{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="contactPersonTel != null">
				#{contactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="fromSource != null">
				#{fromSource,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="businessWeight != null">
				#{businessWeight,jdbcType=INTEGER},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
			<if test="excuteUserId != null">
				#{excuteUserId,jdbcType=BIGINT},
			</if>
			<if test="photoId != null">
				#{photoId,jdbcType=VARCHAR},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extFlag2 != null">
				#{extFlag2,jdbcType=INTEGER},
			</if>
			<if test="extFlag3 != null">
				#{extFlag3,jdbcType=INTEGER},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="customerType != null">
				#{customerType,jdbcType=INTEGER},
			</if>
			<if test="dmsKey != null">
				#{dmsKey,jdbcType=VARCHAR},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="extProperty6 != null">
				#{extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="extProperty7 != null">
				#{extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="extProperty8 != null">
				#{extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="extProperty9 != null">
				#{extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="extProperty10 != null">
				#{extProperty10,jdbcType=VARCHAR},
			</if>
			<if test="extProperty11 != null">
				#{extProperty11,jdbcType=VARCHAR},
			</if>
			<if test="extProperty12 != null">
				#{extProperty12,jdbcType=VARCHAR},
			</if>
			<if test="extProperty13 != null">
				#{extProperty13,jdbcType=VARCHAR},
			</if>
			<if test="extProperty14 != null">
				#{extProperty14,jdbcType=VARCHAR},
			</if>
			<if test="extProperty15 != null">
				#{extProperty15,jdbcType=VARCHAR},
			</if>
			<if test="extProperty16 != null">
				#{extProperty16,jdbcType=VARCHAR},
			</if>
			<if test="extProperty17 != null">
				#{extProperty17,jdbcType=VARCHAR},
			</if>
			<if test="extProperty18 != null">
				#{extProperty18,jdbcType=VARCHAR},
			</if>
			<if test="extProperty19 != null">
				#{extProperty19,jdbcType=VARCHAR},
			</if>
			<if test="extProperty20 != null">
				#{extProperty20,jdbcType=TIMESTAMP},
			</if>
			<if test="extProperty21 != null">
				#{extProperty21,jdbcType=VARCHAR},
			</if>
			<if test="extProperty22 != null">
				#{extProperty22,jdbcType=VARCHAR},
			</if>
			<if test="extProperty23 != null">
				#{extProperty23,jdbcType=VARCHAR},
			</if>
			<if test="extProperty24 != null">
				#{extProperty24,jdbcType=VARCHAR},
			</if>
			<if test="extProperty25 != null">
				#{extProperty25,jdbcType=VARCHAR},
			</if>
			<if test="extProperty26 != null">
				#{extProperty26,jdbcType=VARCHAR},
			</if>
			<if test="extProperty27 != null">
				#{extProperty27,jdbcType=VARCHAR},
			</if>
			<if test="extProperty28 != null">
				#{extProperty28,jdbcType=VARCHAR},
			</if>
			<if test="extProperty29 != null">
				#{extProperty29,jdbcType=VARCHAR},
			</if>
			<if test="customerCode != null">
				#{customerCode,jdbcType=VARCHAR},
			</if>
			<if test="joinCkPlan != null">
				#{join_ck_plan,jdbcType=INTEGER},
			</if>
            <if test="route != null and route != ''">
                #{route,jdbcType=VARCHAR}
            </if>
		</trim>
	</insert>
	
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_work_shop
		<set>
			<if test="record.creator != null">
				creator = #{record.creator,jdbcType=VARCHAR},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.workshopName != null">
				work_shop_name = #{record.workshopName,jdbcType=VARCHAR},
			</if>
			<if test="record.regionId != null">
				region_id = #{record.regionId,jdbcType=BIGINT},
			</if>
			<if test="record.workshopAddress != null">
				work_shop_address = #{record.workshopAddress,jdbcType=VARCHAR},
			</if>
			<if test="record.longitude != null">
				longitude = #{record.longitude,jdbcType=NUMERIC},
			</if>
			<if test="record.latitude != null">
				latitude = #{record.latitude,jdbcType=NUMERIC},
			</if>
			<if test="record.type != null">
				type = #{record.type,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPerson != null">
				contact_person = #{record.contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPersonTel != null">
				contact_person_tel = #{record.contactPersonTel,jdbcType=VARCHAR},
			</if>
			<if test="record.fromSource != null">
				from_source = #{record.fromSource,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.businessWeight != null">
				business_weight = #{record.businessWeight,jdbcType=INTEGER},
			</if>
			<if test="record.remark != null">
				remark = #{record.remark,jdbcType=VARCHAR},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
			<if test="record.excuteUserId != null">
				excute_user_id = #{record.excuteUserId,jdbcType=BIGINT},
			</if>
			<if test="record.photoId != null">
				photo_id = #{record.photoId,jdbcType=VARCHAR},
			</if>
			<if test="record.workShopNamePy != null">
				work_shop_name_py = #{record.workShopNamePy,jdbcType=VARCHAR},
			</if>
			<if test="record.extFlag != null">
				ext_flag = #{record.extFlag,jdbcType=INTEGER},
			</if>
			<if test="record.extFlag2 != null">
				ext_flag2 = #{record.extFlag2,jdbcType=INTEGER},
			</if>
			<if test="record.extFlag3 != null">
				ext_flag3 = #{record.extFlag3,jdbcType=INTEGER},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.customerType != null">
				customer_type = #{record.customerType,jdbcType=INTEGER},
			</if>
			<choose>
				<when test="record.dmsKey == '-1'">
				dms_key = null,
				</when>
				<when test="record.dmsKey != null">
				dms_key = #{record.dmsKey,jdbcType=VARCHAR},
				</when>
			</choose>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty5 != null">
				ext_property5 = #{record.extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty6 != null">
				ext_property6 = #{record.extProperty6,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty7 != null">
				ext_property7 = #{record.extProperty7,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty8 != null">
				ext_property8 = #{record.extProperty8,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty9 != null">
				ext_property9 = #{record.extProperty9,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty10 != null">
				ext_property10 = #{record.extProperty10,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty11 != null">
				ext_property11 = #{record.extProperty11,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty12 != null">
				ext_property12 = #{record.extProperty12,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty13 != null">
				ext_property13 = #{record.extProperty13,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty14 != null">
				ext_property14 = #{record.extProperty14,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty15 != null">
				ext_property15 = #{record.extProperty15,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty16 != null">
				ext_property16 = #{record.extProperty16,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty17 != null">
				ext_property17 = #{record.extProperty17,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty18 != null">
				ext_property18 = #{record.extProperty18,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty19 != null">
				ext_property19 = #{record.extProperty19,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty20 != null">
				ext_property20 = #{record.extProperty20,jdbcType=TIMESTAMP},
			</if>
			<if test="record.extProperty21 != null">
				ext_property21 = #{record.extProperty21,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty22 != null">
				ext_property22 = #{record.extProperty22,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty23 != null">
				ext_property23 = #{record.extProperty23,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty24 != null">
				ext_property24 = #{record.extProperty24,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty25 != null">
				ext_property25 = #{record.extProperty25,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty26 != null">
				ext_property26 = #{record.extProperty26,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty27 != null">
				ext_property27 = #{record.extProperty27,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty28 != null">
				ext_property28 = #{record.extProperty28,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty29 != null">
				ext_property29 = #{record.extProperty29,jdbcType=VARCHAR},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.master.model.WorkshopMasterExample">
		delete from wx_t_work_shop
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.master.model.WorkshopMasterExample" resultType="int">
		select count(1) from wx_t_work_shop
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_work_shop
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterExample">
	select a.*, u.ch_name execute_user_name,tt_file.att_id,u1.ch_name create_user_name,u2.ch_name update_user_name
			<!--  ,(select top 1 o.id from wx_t_workshop_partner wp left join wx_t_organization o on wp.partner_id=o.id where wp.workshop_id=a.id) partner_id -->
	 from (
    	select t1.*,
    	    r2.id city_id,
			r2.parent_id province_id,
			r2.region_name city_name, 
			r3.region_name province_name,
			 (case when t1.customer_type=1/*****门店*****/ then
				 (case when t1.ext_flag&amp;65536>0 then 2 else 0 end/*陈列之星-1*/)
				 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 1024 else 0 end/*B2B店-10*/)
				 + (case when exists (select 1 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
						where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.check_evaluation>=6 and s.org_id=t1.id) then 32768 else 0 end/*陈列大比拼-15*/)
				  + (case when t1.ext_flag&amp;6144>0 then 2048 else 0 end/*店招店(11,12)-11*/) 
				  + (case when t1.ext_flag&amp;32768>0 then 4096 else 0 end/*定位店(15)-12*/)
				  + (case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
				  + (case when t1.ext_flag&amp;16>0 then 1073741824 else 0 end/*导航店(4)-30*/)
				  + (t1.ext_flag&amp;16384) /*黑金店(14)-14*/
				  + (t1.ext_flag&amp;131072) /*直播(17位)*/
				 	+ (case when t1.from_source!=8 and t1.dms_key is not null then 8 else 0 end/*PP中匹配的DMS门店-3*/)
		      + (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
		      + (t1.ext_flag&amp;2097152) /*超低灰门店-21*/
		when t1.customer_type=2/*****车队*****/ then
		    (case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
			+ (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
			else 0 end)  workshop_property,
			 (case when t1.customer_type=1/*****门店*****/ then
				 (case when exists (SELECT 1 FROM wx_t_material_application ma1 where ma1.APPLICATION_TYPE  = 'cdm_pit_pack' and ma1.WORKSHOP_ID=t1.id) then 2 else 0 end/*有PitPack兑换-1*/)
				 + (case when exists (select 1 from wx_t_work_shop_flag wf1 where wf1.USED_FLAG = 2 and wf1.WORKSHOP_ID=t1.id) then 1 else 0 end/*有开业礼包-0*/)
				 + (case when exists (SELECT 1 FROM dbo.wx_t_point_value_detail pvd LEFT JOIN wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID left join wx_t_workshop_employee we on we.code=pac.POINT_ACCOUNT_OWNER_CODE WHERE pvd.POINT_TYPE =  'B2B_POINT' and pac.POINT_ACCOUNT_TYPE='TC' and we.workshop_id=t1.id) then 2048 else 0 end/*有B2B积分-11*/)
			when t1.customer_type=2/*****车队*****/ 
				then (case when exists (SELECT 1 FROM wx_task_main tm1 LEFT JOIN wx_task_sub ts1 ON tm1.task_main_id = ts1.task_main_id
						WHERE tm1.tmb_type_code = 'TT_2_CDYJZM' and ts1.task_status = 4 AND ts1.check_evaluation = 3 and ts1.org_id=t1.id) then 1024 else 0 end/*合格/优秀业绩证明-10*/)
			else 0 end)  workshop_property2,
			 case when t1.status='0' then (select max(task_main.excute_user_id) FROM wx_task_main task_main join wx_task_sub ts1 on ts1.task_main_id=task_main.task_main_id where ts1.org_id=t1.id and task_main.tmb_type_code='TT_2_SD') else t1.excute_user_id end excute_user_id1
		from wx_t_work_shop t1
		    left join wx_t_region r1 on t1.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
    left join wx_t_region r3 on r2.parent_id=r3.id
		where t1.id = #{id,jdbcType=BIGINT}) a
		left join wx_t_user u on a.excute_user_id1=u.user_id
		left join wx_t_user u1 on a.creator=u1.user_id
		left join wx_t_user u2 on a.update_user_id=u2.user_id
		LEFT JOIN wx_att_file tt_file ON tt_file.uuid=a.photo_id
	</select>
	
	
	<select id="selectDmsWorkshopByKey" resultMap="BaseResultMap" parameterType="map">
    	select t1.[Customer_Name] work_shop_name, t1.[Customer_Addr] work_shop_address, t1.[Customer_Contact] contact_person, 
			t1.[Customer_Tel] contact_person_tel, 8 from_source, 0 business_weight, 
			 '3' status, 8 workshop_property, o1.organization_name partner_name,t1.[Distributor_Code] + '/' + t1.[Customer_Code] dms_key, 
			  wp1.partner_id
		from dw_dms_workshop t1
		  join wx_t_partner_o2o_enterprise wp1 on t1.[distributor_id]=wp1.distributor_id
		  join wx_t_organization o1 on o1.id=wp1.partner_id
		where t1.[Distributor_Code] + '/' + t1.[Customer_Code] = #{dmsKey,jdbcType=VARCHAR}
	</select>
	
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.*
		  from wx_t_work_shop t1
		  join wx_t_organization o on t1.partner_id=o.id 
		 where t1.delete_flag=0
		<if test="workshopName != null and workshopName != ''">
			and t1.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="regionId != null and regionId != ''">
			and t1.region_id like '%' + #{regionId, jdbcType=VARCHAR} + '%'
		</if>
		<if test="type != null and type != ''">
			and t1.type = #{type, jdbcType=VARCHAR}
		</if>
		<if test="source != null and source != ''">
			and t1.from_source = #{source,jdbcType=VARCHAR}
		</if>
		<if test="partnerId != null">
		and t1.partner_id=#{partnerId}
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterParams">
		select a.*, u.ch_name execute_user_name from (
			select
				<choose>
					<when test="longitude == null">
						null work_shop_range,
					</when>
					<otherwise>
						dbo.fun_cal_distance(${longitude}, ${latitude}, t1.longitude, t1.latitude) work_shop_range,
					</otherwise>
				</choose>
				t1.id, t1.customer_type, t1.work_shop_name,t1.ext_flag2, t1.region_id, t1.work_shop_address, t1.longitude, t1.latitude, <!-- t1.grade_abc, -->
				t1.type, t1.contact_person, t1.contact_person_tel, t1.from_source, t1.delete_flag, t1.business_weight,
				t1.remark, t1.status,  t1.ext_flag, t1.ext_property1, t1.ext_property2, t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.ext_property6,
				t1.ext_property7, t1.ext_property8, t1.ext_property9, t1.ext_property10, t1.ext_property11, t1.ext_property12,
				t1.ext_property13, t1.ext_property14, t1.ext_property15, t1.ext_property16, t1.ext_property17, t1.ext_property18,
				t1.ext_property19, t1.ext_property20, t1.ext_property21, t1.ext_property22, t1.ext_property23, t1.ext_property24,
				t1.ext_property25, t1.ext_property26, t1.ext_property27, t1.ext_property28, t1.ext_property29,
				t1.route, t1.route_owner, t1.route_owner_account,
				(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=t1.business_weight) business_weight_text,
				(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=t1.customer_type) customer_type_text,
				(case when t1.customer_type=1/*****门店*****/ then
					(case when t1.ext_flag&amp;65536>0 then 2 else 0 end/*陈列之星-1*/)
					+ (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 1024 else 0 end/*B2B店-10*/)
					+ (case when exists (select 1 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
							where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.check_evaluation>=6 and s.org_id=t1.id) then 32768 else 0 end/*陈列大比拼-15*/)
					+ (case when t1.ext_flag&amp;6144>0 then 2048 else 0 end/*店招店(11,12)-11*/)
					+ (case when t1.ext_flag&amp;32768>0 then 4096 else 0 end/*定位店(15)-12*/)
					+ (case when t1.ext_flag&amp;524288>0 then 4 else 0 end/*中超店招店*/)
					+ (case when t1.ext_flag&amp;8192>0 then 16 else 0 end/*蓝盾合作门店*/)
					+ (case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
					+ (case when t1.ext_flag&amp;16>0 then 1073741824 else 0 end/*导航店(4)-30*/)
					+ (t1.ext_flag&amp;16384) /*黑金店(14)-14*/
					+ (t1.ext_flag&amp;131072) /*直播(17位)*/
					+ (case when t1.from_source!=8 and t1.dms_key is not null then 8 else 0 end/*PP中匹配的DMS门店-3*/)
					+ (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
					+ (t1.ext_flag&amp;2097152) /*超低灰门店-21*/
				when t1.customer_type=2/*****车队*****/ then
					(case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
					+ (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
				else 0 end)  workshop_property,
				(case when t1.customer_type=1/*****门店*****/ then
					(case when exists (SELECT 1 FROM wx_t_material_application ma1 where ma1.APPLICATION_TYPE  = 'cdm_pit_pack' and ma1.WORKSHOP_ID=t1.id) then 2 else 0 end/*有PitPack兑换-1*/)
					+ (case when exists (select 1 from wx_t_work_shop_flag wf1 where wf1.USED_FLAG = 2 and wf1.WORKSHOP_ID=t1.id) then 1 else 0 end/*有开业礼包-0*/)
					+ (case when exists (SELECT 1 FROM dbo.wx_t_point_value_detail pvd LEFT JOIN wx_t_point_account pac ON pvd.POINT_ACCOUNT_ID = pac.ID left join wx_t_workshop_employee we on we.code=pac.POINT_ACCOUNT_OWNER_CODE WHERE pvd.POINT_TYPE =  'B2B_POINT' and pac.POINT_ACCOUNT_TYPE='TC' and we.workshop_id=t1.id) then 2048 else 0 end/*有B2B积分-11*/)
				when t1.customer_type=2/*****车队*****/
					then (case when exists (SELECT 1 FROM wx_task_main tm1 LEFT JOIN wx_task_sub ts1 ON tm1.task_main_id = ts1.task_main_id
						WHERE tm1.tmb_type_code = 'TT_2_CDYJZM' and ts1.task_status = 4 AND ts1.check_evaluation = 3 and ts1.org_id=t1.id) then 1024 else 0 end/*合格/优秀业绩证明-10*/)
				else 0 end)  workshop_property2,
				r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, (case when o.type=3 then r1o1.organization_name + ' - ' else '' end) + o.organization_name partner_name,
				t1.excute_user_id,
				tt_file.att_id,t1.activation_time active_time,
				t1.dms_key,0
				<choose>
					<when test="funFlag == 'CioMktApply'">
						+ (case when exists (select 1 from view_mkt_apply v where v.apply_key=#{mktKey} + '/' + convert(nvarchar(30), t1.id)) then 0 else 1 end)
					</when>
					<when test="funFlag == 'resourcePortal'">
						+ (case when exists (select 1 from wx_t_workshop_relationship wr1 where wr1.relation_type=#{funKey} and wr1.workshop_id=t1.id
						<if test="excludeFunKey != null and excludeFunKey != ''">
						and wr1.source_key!=#{excludeFunKey}
						</if>
						) then 0 else 1 end)
					</when>
					<when test="funFlag == 'mktCi'">
						+ (case when exists (select 1 from wx_t_mkt_ci_apply where sign_type=#{mktKey} and store_id=t1.id and form_status <![CDATA[ < ]]> 60
						union all select 1 from view_mkt_apply v where v.apply_key=#{oldMktKey} + '/' + convert(nvarchar(30), t1.id) and v.current_step1>1 and (v.flow_finish3 is null or v.flow_finish3!=1)) then 0 else 1 end)
					</when>
				</choose> operation_flag,
				t1.create_time,
				t1.update_time
				<if test="includeDmsMatchWeight">
					,case when dms_key is not null then -1 else (select top 1 a_0.match_weight from (select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person,
					dw.[Customer_Tel] contact_person_tel, dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, (SELECT sum(st.[SELL THROUGH L]) sell_through
					FROM [dw_dms_sell_through] st
					where st.[QBR CATEGORY] is not null
					and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code]
					and SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume,
					 (case when (t1.ext_property5 is not null and len(t1.ext_property5)>6 and charindex(t1.ext_property5,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0)
					or (t1.contact_person_tel is not null and len(t1.contact_person_tel)>6 and charindex(t1.contact_person_tel,dw.[Customer_Tel]  collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end)
					+ (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.ext_property5)>0 or
					charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, t1.contact_person_tel)>0) then 3000 else 0 end)
					+ (case when charindex(t1.work_shop_address, dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end)
					+ (case when charindex('号', t1.work_shop_address) > 0 then 100 else 0 end)
					 + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, t1.work_shop_address) > 0 then 100 else 0 end)
					+ (case when len(t1.work_shop_name)>1 and charindex(t1.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end)
					 + (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, t1.work_shop_name) > 0 then 1000 else 0 end)
					 + (case when t1.ext_flag&amp;6144>0 then 8 else 0 end)
					 + (case when t1.ext_flag&amp;32768>0 then 4 else 0 end)
					 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 2 else 0 end)
					 + (case when t1.from_source=2 then 1 else 0 end)
					 + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight
					  from dw_dms_workshop dw
						left join wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id
					where pe.partner_id=t1.partner_id and not exists (select 1 from wx_t_work_shop iw1 where iw1.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code])) a_0 order by a_0.match_weight desc) end dms_match_weight
				</if>
		  	from wx_t_work_shop t1
			join wx_t_organization o on t1.partner_id=o.id
			left join wx_t_region r1 on t1.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
		    left join wx_t_partner_o2o_enterprise r1pe1 on r1pe1.partner_id=o.id
		    left join wx_t_organization r1o1 on o.type=3 and r1pe1.ext_property1=r1o1.id
			left join wx_att_file tt_file ON tt_file.uuid=t1.photo_id
			where o.status='1' $Permission_Clause$
			<include refid="basePageCond"/>
		) a
		left join wx_t_user u on a.excute_user_id=u.user_id
		where 1 = 1
		<if test="workshopProperty != null">
			and a.workshop_property&amp;${workshopProperty}>0
		</if>
	</select>

	<select id="totalForPage" resultType="long" parameterType="com.chevron.master.model.WorkshopMasterParams">
		select count(*) from (
			select
				(case when t1.customer_type=1/*****门店*****/ then
					(case when t1.ext_flag&amp;65536>0 then 2 else 0 end/*陈列之星-1*/)
					+ (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then 1024 else 0 end/*B2B店-10*/)
					+ (case when exists (select 1 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
					where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.check_evaluation>=6 and s.org_id=t1.id) then 32768 else 0 end/*陈列大比拼-15*/)
					+ (case when t1.ext_flag&amp;6144>0 then 2048 else 0 end/*店招店(11,12)-11*/)
					+ (case when t1.ext_flag&amp;32768>0 then 4096 else 0 end/*定位店(15)-12*/)
					+ (case when t1.ext_flag&amp;524288>0 then 4 else 0 end/*中超店招店*/)
					+ (case when t1.ext_flag&amp;8192>0 then 16 else 0 end/*蓝盾合作门店*/)
					+ (case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
					+ (case when t1.ext_flag&amp;16>0 then 1073741824 else 0 end/*导航店(4)-30*/)
					+ (t1.ext_flag&amp;16384) /*黑金店(14)-14*/
					+ (t1.ext_flag&amp;131072) /*直播(17位)*/
					+ (case when t1.from_source!=8 and t1.dms_key is not null then 8 else 0 end/*PP中匹配的DMS门店-3*/)
					+ (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
					+ (t1.ext_flag&amp;2097152) /*超低灰门店-21*/
				when t1.customer_type=2/*****车队*****/ then
					(case when t1.ext_flag&amp;1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
					+ (case when t1.join_ck_plan!=0 then 1048576 else 0 end/*国六节油养护站(原CK合作门店)-20*/)
				else 0 end)  workshop_property
			from wx_t_work_shop t1
			join wx_t_organization o on t1.partner_id=o.id
			left join wx_t_region r1 on t1.region_id=r1.id
			left join wx_t_region r2 on r1.parent_id=r2.id
			left join wx_t_region r3 on r2.parent_id=r3.id
			left join wx_t_partner_o2o_enterprise r1pe1 on r1pe1.partner_id=o.id
			left join wx_t_organization r1o1 on o.type=3 and r1pe1.ext_property1=r1o1.id
			left join wx_att_file tt_file ON tt_file.uuid=t1.photo_id
			where o.status='1' $Permission_Clause$
			<include refid="basePageCond"/>
		) a
		where 1 = 1
		<if test="workshopProperty != null">
			and a.workshop_property&amp;${workshopProperty}>0
		</if>
	</select>

	<select id="queryMatchDmsWorkshop" resultMap="BaseResultMap" parameterType="map">
select w.id, dw.[Customer_Name] work_shop_name, dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, 
		dw.[Customer_Tel] contact_person_tel, dw.DSR_Name execute_user_name, w.dms_key, (SELECT sum(st.[SELL THROUGH L]) sell_through
  FROM [dw_dms_sell_through] st
  where st.[QBR CATEGORY] is not null
  and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code]
  and SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume from wx_t_work_shop w
left join dw_dms_workshop dw on dw.[Distributor_Code] + '/' + dw.[Customer_Code]=w.dms_key
where dw.Distributor_Code is not null
<if test="workshopIds != null">
and w.id in (
<foreach collection="workshopIds" item="item" separator=",">
${item}
</foreach>
)
</if>
union all 
select b.id,b.Customer_Name, b.Customer_Addr, b.Customer_Contact, b.Customer_Tel, b.DSR_Name,
b.[Distributor_Code] + '/' + b.[Customer_Code], (SELECT sum(st.[SELL THROUGH L]) sell_through
  FROM [dw_dms_sell_through] st
  where st.[QBR CATEGORY] is not null
  and st.[CUSTOMER CODE]=b.[distributor_code] and st.[WORKSHOP CODE]=b.[Customer_Code]
  and SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume from (
select a.*,
ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY a.match_weight DESC) AS rn from (
select w.id, dw.[Customer_Code], 
dw.[distributor_code], dw.[Customer_Name], dw.[Customer_Addr], dw.[DSR_Name], dw.[Customer_Tel], dw.[Customer_Contact],
 (case when (w.ext_property5 is not null and len(w.ext_property5)>6 and charindex(w.ext_property5,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0)
or (w.contact_person_tel is not null and len(w.contact_person_tel)>6 and charindex(w.contact_person_tel,dw.[Customer_Tel]  collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end)
+ (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, w.ext_property5)>0 or
charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, w.contact_person_tel)>0) then 3000 else 0 end)
+ (case when charindex(w.work_shop_address, dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end)
+ (case when charindex('号', w.work_shop_address) > 0 then 100 else 0 end)
 + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, w.work_shop_address) > 0 then 100 else 0 end)
+ (case when len(w.work_shop_name)>1 and charindex(w.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end)
 + (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, w.work_shop_name) > 0 then 1000 else 0 end)
 + (case when dw.[DSR_Name] collate Chinese_PRC_CI_AS=u.ch_name then 30 else 0 end)
 + (case when w.ext_flag&amp;6144>0 then 8 else 0 end)
 + (case when w.ext_flag&amp;32768>0 then 4 else 0 end)
 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 2 else 0 end)
 + (case when w.from_source=2 then 1 else 0 end)
 + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight
  from wx_t_work_shop w 
	left join wx_t_workshop_partner wp on wp.workshop_id=w.id 
	left join wx_t_partner_o2o_enterprise pe on wp.partner_id=pe.partner_id 
	left join dw_dms_workshop dw on dw.distributor_id=pe.distributor_id
left join wx_t_user u on u.user_id=w.[excute_user_id]
where (w.dms_key is null or not exists (select 1 from dw_dms_workshop dw1 where dw1.[Distributor_Code] + '/' + dw1.[Customer_Code]=w.dms_key))
 and not exists (select 1 from wx_t_workshop_partner iwp1 left join wx_t_work_shop iw1 on iwp1.workshop_id=iw1.id where iwp1.partner_id=pe.partner_id and iw1.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code])
<if test="workshopIds != null">
and w.id in (
<foreach collection="workshopIds" item="item" separator=",">
${item}
</foreach>
)
</if>
) a where a.match_weight>299
) b
where b.rn=1
	</select>
	
	
	<select id="countDmsWorkshopByParams" resultType="long" parameterType="map">
select count(1) from dw_dms_workshop dw left join wx_t_partner_o2o_enterprise pe on dw.distributor_id=pe.distributor_id
where 1=1 
<if test="partnerId != null">
	and pe.partner_id=#{partnerId}
</if>
	</select>
	
	<select id="queryDmsWorkshopForPage" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterParams">
select dw.[Customer_Code] work_shop_code, dw.[Customer_Name] work_shop_name, dw.[Customer_Addr] work_shop_address, dw.[Customer_Contact] contact_person, 
		dw.[Customer_Tel] contact_person_tel, dw.DSR_Name execute_user_name, dw.[Distributor_Code] + '/' + dw.[Customer_Code] dms_key, (SELECT sum(st.[SELL THROUGH L]) sell_through
  FROM [dw_dms_sell_through] st
  where st.[QBR CATEGORY] is not null
  and st.[CUSTOMER CODE]=dw.[distributor_code] and st.[WORKSHOP CODE]=dw.[Customer_Code]
  and SUBSTRING(st.[DMS DATE], 1, 4) = year(getdate())) dms_volume,
 (case when (w.ext_property5 is not null and len(w.ext_property5)>6 and charindex(w.ext_property5,dw.[Customer_Tel] collate Chinese_PRC_CI_AS )>0)
or (w.contact_person_tel is not null and len(w.contact_person_tel)>6 and charindex(w.contact_person_tel,dw.[Customer_Tel]  collate Chinese_PRC_CI_AS)>0) then 3000 else 0 end)
+ (case when dw.[Customer_Tel] is not null and len(dw.[Customer_Tel])>6 and (charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, w.ext_property5)>0 or
charindex(dw.[Customer_Tel] collate Chinese_PRC_CI_AS, w.contact_person_tel)>0) then 3000 else 0 end)
+ (case when charindex(w.work_shop_address, dw.[Customer_Addr] collate Chinese_PRC_CI_AS) > 0 then 100 else 0 end)
+ (case when charindex('号', w.work_shop_address) > 0 then 100 else 0 end)
 + (case when charindex(dw.[Customer_Addr] collate Chinese_PRC_CI_AS, w.work_shop_address) > 0 then 100 else 0 end)
+ (case when len(w.work_shop_name)>1 and charindex(w.work_shop_name, dw.[Customer_Name] collate Chinese_PRC_CI_AS) > 0 then 1000 else 0 end)
 + (case when len(dw.[Customer_Name])>1 and charindex(dw.[Customer_Name] collate Chinese_PRC_CI_AS, w.work_shop_name) > 0 then 1000 else 0 end)
 + (case when dw.[DSR_Name] collate Chinese_PRC_CI_AS=u.ch_name then 30 else 0 end)
 + (case when w.ext_flag&amp;6144>0 then 8 else 0 end)
 + (case when w.ext_flag&amp;32768>0 then 4 else 0 end)
 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 2 else 0 end)
 + (case when w.from_source=2 then 1 else 0 end)
 + (case when dw.[Customer_Addr] is not null then 0.1 else 0 end) match_weight
  from wx_t_work_shop w 
	join wx_t_workshop_partner wp on wp.workshop_id=w.id 
	join wx_t_partner_o2o_enterprise pe on wp.partner_id=pe.partner_id 
	join dw_dms_workshop dw on dw.distributor_id=pe.distributor_id
left join wx_t_user u on u.user_id=w.[excute_user_id]
where not exists (select 1 from wx_t_workshop_partner iwp1 left join wx_t_work_shop iw1 on iwp1.workshop_id=iw1.id where iwp1.partner_id=pe.partner_id and iw1.dms_key=dw.[Distributor_Code] + '/' + dw.[Customer_Code])
<if test="workshopId != null">
	and w.id=#{workshopId}
</if>
<if test="partnerId != null">
	and pe.partner_id=#{partnerId}
</if>
<if test="queryField != null and queryField != ''">
	and (dw.[Customer_Name] like '%' + #{queryField} + '%' or dw.[Customer_Code] like '%' + #{queryField} + '%')
</if>
<if test="workShopCode != null and workShopCode != ''">
	and dw.[Customer_Code] like '%' + #{workShopCode} + '%'
</if>
<if test="workshopName != null and workshopName != ''">
	and dw.[Customer_Name] like '%' + #{workshopName} + '%'
</if>
<if test="workshopAddress != null and workshopAddress != ''">
	and dw.[Customer_Addr] like '%' + #{workshopAddress} + '%'
</if>
<if test="contactPerson != null and contactPerson != ''">
	and dw.[Customer_Contact] like '%' + #{contactPerson} + '%'
</if>
<if test="contactPersonTel != null and contactPersonTel != ''">
	and dw.[Customer_Tel] like '%' + #{contactPersonTel} + '%'
</if>
<if test="executeUserName != null and executeUserName != ''">
	and dw.[DSR_Name] like '%' + #{executeUserName} + '%'
</if>
	</select>
	<select id="queryForCtrl" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopCtrlParams">
	select 
		<if test="limit != null">
			top ${limit}
		</if>
	a.* from (
		select t1.id, t1.work_shop_name, t1.region_id, t1.work_shop_address, t1.longitude, t1.latitude, 
			 t1.type, t1.contact_person, t1.contact_person_tel, t1.from_source, t1.delete_flag, t1.business_weight, 
			 t1.remark, t1.status, t1.excute_user_id, (select u0.status from wx_t_user u0 where u0.user_id=t1.excute_user_id) excute_user_status, 
			 r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, o.organization_name partner_name
		  from wx_t_work_shop t1
		  join wx_t_organization o on t1.partner_id=o.id 
		    left join wx_t_region r1 on t1.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
		 where o.status='1' and convert(int, t1.status)>=0 and t1.delete_flag = 0
		<choose>
			<when test="page == 'workshop'">
			and convert(int, t1.status) > -1
			</when>
			<when test="status != null">
				<foreach item="status" index="index" collection="status" open="and t1.status in (" separator="," close=")">
					#{status}
				</foreach>
			</when>
			<otherwise>
			and t1.status='3'
			</otherwise>
		</choose>
		 
		 $Permission_Clause$
		<if test="partnerId != null">
		and t1.partner_id=#{partnerId}
		</if>
		<if test="includeRetailer == null or includeRetailer == 0">
		and o.type=1
		</if>
		<if test="workshopName != null and workshopName != ''">
			and (t1.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
			or t1.work_shop_name_py LIKE  '%' + #{workshopName} + '%')
		</if>
		<if test="prov != null">
			and r3.id = #{prov,jdbcType=BIGINT}
		</if>
		<if test="city != null">
			and r2.id = #{city,jdbcType=BIGINT}
		</if>
		<if test="dist != null">
			and r1.id = #{dist,jdbcType=BIGINT}
		</if>
		<!-- <if test="includeChannels != -1">
			and convert(int, (select di1.dic_item_desc from wx_t_dic_item di1 where di1.dic_type_code='Workshop.fromSource' and di1.dic_item_code=t1.from_source)) &amp; ${includeChannels} > 0
		</if> -->
		<if test="customerType != null and customerType > 0">
		and t1.customer_type&amp;${customerType}>0
		</if>
		<if test="fromSource != null">
		and t1.from_source&amp;${fromSource}>0
		</if>
<!-- 	<choose>
		<when test="supportMaintainFlag == 1">
		and t1.support_maintain_flag=1
		</when>
		<when test="supportMaintainFlag == 0">
		and (t1.support_maintain_flag is null or t1.support_maintain_flag!=1)
		</when>
	</choose> -->
		<if test="includeDms">
			union all select null id, dw1.[Customer_Name] work_shop_name, null region_id, dw1.[Customer_Addr] work_shop_address, null longitude, 
			null latitude,null type, dw1.[Customer_Contact] contact_person, 
			dw1.[Customer_Tel] contact_person_tel, 8 from_source, 0 delete_flag, 0 business_weight, 
			 null remark, '3' status, null excute_user_id, null excute_user_status,
			 null dist_name, null city_name, null province_name, o1.organization_name partner_name
		  from dw_dms_workshop dw1
		  join wx_t_partner_o2o_enterprise t1 on dw1.[distributor_id]=t1.distributor_id
		  join wx_t_organization o1 on o1.id=t1.partner_id
		  where o1.status='1' 
		  and not exists (select 1 from wx_t_work_shop w1 where w1.dms_key=dw1.[Distributor_Code] + '/' + dw1.[Customer_Code])
		  $Permission_Clause$
		<if test="partnerId != null">
		and t1.partner_id=#{partnerId}
		</if>
		<if test="workshopName != null and workshopName != ''">
			and (dw1.[Customer_Name]  like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
			or dw1.customer_name_py like '%' + #{workshopName, jdbcType=VARCHAR} + '%')
		</if>
		</if>
		) a
		where 1= 1
	</select>
	<select id="queryWorkShopByParams" resultMap="BaseResultMap" parameterType="map">
	    select t.id,t.contact_person_tel,t.work_shop_address,t.work_shop_name, ts.regionName as region_name from  wx_t_work_shop t
	    left join (
        SELECT 
            isnull(t3.region_name ,'')+isnull(t2.region_name,'')+isnull(t1.region_name,'') as regionName,
		           t1.id region_id
                   FROM wx_t_region t1 LEFT JOIN wx_t_region t2
		           ON t1.parent_id = t2.id
		           LEFT JOIN wx_t_region t3
		           ON t2.parent_id= t3.id) ts on ts.region_id = t.region_id
		 where 1=1
		 <if test="workshopName != null and workshopName != ''">
			and t.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="fromSource != null and fromSource != ''">
			and t.from_source = #{fromSource,jdbcType=VARCHAR}
		</if>
	</select>
	
	<select id="queryWorkShopInfoForPage" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterParams">
		SELECT t1.*,u.ch_name execute_user_name, o.organization_name partner_name,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.businessWeight' and di1.dic_item_code=t1.business_weight) business_weight_text,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=t1.customer_type) customer_type_text,
			 cos.region region_name, cos.sales_name_cn sales_name
		FROM
			wx_t_work_shop t1
		JOIN wx_t_organization o ON t1.partner_id = o.id
		LEFT JOIN wx_t_region r1 ON t1.region_id = r1.id
		LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
		LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
		LEFT JOIN wx_t_user u ON t1.creator = u.user_id
		left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
		left join dw_customer_org_sales cos on cos.distributor_id=pe.distributor_id and cos.channel_weight&amp;(case when t1.business_weight&amp;1>0 then 1 else 2 end)>0
			and exists (select 1 from dw_region_sales_channel_rel rsc where rsc.region_name=cos.region and rsc.bu='Indirect')
		where 1 =1 
			AND t1.delete_flag = 0
		AND exists (select 1 from wx_t_workshop_status ws1 left join wx_task_sub ts1 on ts1.task_id=ws1.subtask_id where ws1.workshop_id=t1.id and ws1.workshop_with_status=2 and ts1.submit_time&lt;ws1.create_time)
		<choose>
			<when test="extFlag != null and extFlag == 0">
			and t1.ext_flag &amp; 256 != 256 and t1.ext_flag &amp; 512 != 512
			</when>
			<when test="extFlag != null and extFlag > 0">
			and t1.ext_flag &amp; #{extFlag} > 0
			</when>
		</choose>
		<!-- <if test="status != null and status != ''">
			AND t1.status = #{status} 
		</if>
		<if test="fromSource != null and fromSource != ''">
			AND t1.from_source = #{fromSource}
		</if> -->
		
		<if test="prov != null">
			and r3.id = #{prov,jdbcType=BIGINT}
		</if>
		<if test="city != null">
			and r2.id = #{city,jdbcType=BIGINT}
		</if>
		<if test="dist != null">
			and r1.id = #{dist,jdbcType=BIGINT}
		</if>
		<if test="activeDateFrom != null and activeDateFrom != ''">
			AND t1.activation_time &gt;= #{activeDateFrom}
		</if>
		<if test="activeDateTo != null and activeDateTo != ''">
			AND t1.activation_time &lt; #{activeDateTo}
		</if>
		<if test="createDateFrom != null and createDateFrom != ''">
			AND t1.create_time &gt;= #{createDateFrom}
		</if>
		<if test="createDateTo != null and createDateTo != ''">
			AND t1.create_time &lt; #{createDateTo}
		</if>
		<if test="executeUserName != null">
			AND u.ch_name like '%'+#{executeUserName}+'%'
		</if>
		<if test="executeUserId != null">
			AND t1.creator = #{executeUserId}
		</if>
		<if test="workshopName != null">
			AND t1.work_shop_name LIKE '%' + #{workshopName} + '%'
		</if>
		<if test="workshopId != null">
			AND t1.id = #{workshopId}          
		</if>
		<if test="partnerId != null and partnerId != ''">
			AND t1.partner_id = #{partnerId}
		</if>
		<if test="regionName != null and regionName != ''">
		and cos.region=#{regionName}
		</if>
		<if test="salesCai != null and salesCai != ''">
		and cos.sales_cai=#{salesCai}
		</if>
	</select>
	<select id="queryDeloMgxActivityForPage" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterParams">
select t_delo.* FROM (select t_delo_mgx.*,(CASE WHEN (t_delo_mgx.ext_flag1 &amp; 1024 = 0) AND ((t_delo_mgx.task_time) &gt; t_delo_mgx.ext_property20) 
THEN (CASE when (t_delo_mgx.ext_flag1 &amp; 2048 > 0)  THEN (t_delo_mgx.ext_flag1 - 2048) else t_delo_mgx.ext_flag1 end ) else t_delo_mgx.ext_flag1 END)  ext_flag from (
select t1.work_shop_name, o.organization_name partner_name, o.id partner_id, t1.id, v1.region_name, t1.dms_key,
pe.distributor_id, t1.ext_flag ext_flag1,(SELECT MAX(tm1.create_time) FROM wx_task_sub s LEFT JOIN wx_task_main tm1 ON s .task_main_id = tm1.task_main_id
where s.org_id = t1.id AND tm1.tmb_type_code IN ('TT_2_CLZX', 'TT_2_XD_CAI','TT_2_FLEET','TT_2_FLEET_ZF') and s.task_status=4 and tm1.create_time &gt;= '2020-01-01'
<if test="activeDateToStr != null and activeDateToStr != ''">
	and tm1.create_time &lt;  #{activeDateToStr}
</if>
<if test="activeDateFromStr != null and activeDateFromStr != ''">
	and tm1.create_time &gt;= #{activeDateFromStr}
</if>) task_time,t1.ext_property20, u.ch_name execute_user_name, t1.customer_type from wx_t_work_shop t1
<!-- left join wx_t_workshop_partner wp1 on t1.id=wp1.workshop_id -->
left join wx_t_partner_o2o_enterprise pe on t1.partner_id=pe.partner_id
left join wx_t_organization o on o.id=t1.partner_id
left join view_customer_region_sales_channel v1 on v1.distributor_id=pe.distributor_id and v1.channel_weight&amp;2>0
LEFT JOIN wx_t_user u ON u.user_id = t1.creator 
where t1.delete_flag=0 and t1.from_source = 2 and t1.status='3' $Permission_Clause$		
		<if test="customerType != null and customerType > 0">
			and t1.customer_type = #{customerType}
		</if>
		<if test="workshopName != null and workshopName != ''">
			AND t1.work_shop_name LIKE '%' + #{workshopName} + '%'
		</if>
		<if test="queryField != null and queryField != ''">
			AND t1.work_shop_name LIKE '%' + #{queryField} + '%'
		</if>
		<if test="partnerId != null and partnerId != ''">
			<!-- AND wp1.partner_id = #{partnerId} -->
			AND t1.partner_id = #{partnerId}
		</if>
		<if test="regionName != null and regionName != ''">
			AND v1.region_name = #{regionName}
		</if>
		<choose>
			<when test="extFlag != null and extFlag == 0">
			and t1.ext_flag&amp;1024=0
			</when>
			<when test="extFlag != null and extFlag > 0">
			and t1.ext_flag&amp;#{extFlag}>0
			</when>
		</choose>
and (exists (select 1 from wx_task_sub ts1 
LEFT HASH JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
left HASH join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id
left join wx_task_check tc1 on tc1.check_id=tic1.check_id 
left HASH join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id
left join wx_task_check tc2 on tc2.check_id=tic2.check_id 
left join wx_att_file af1 on af1.source_type=3 and af1.source_id=tic1.id 
where ts1.org_id=t1.id and tm1.tmb_type_code in ('TT_2_CLZX','TT_2_XD_CAI','TT_2_FLEET','TT_2_FLEET_ZF') 
and ts1.task_status=4 and tm1.create_time &gt;= '2020-06-01'
<if test="activeDateToStr != null and activeDateToStr != ''">
	and tm1.create_time &lt;  #{activeDateToStr}
</if>
<if test="activeDateFromStr != null and activeDateFromStr != ''">
	and tm1.create_time &gt;= #{activeDateFromStr}
</if>
and tc1.check_code='DELO400_MGX_PHOTO' and af1.att_id is not null
and tc2.check_code='SALE_DELO_PRODUCT' and tic2.check_value is not null and charindex('德乐400', tic2.check_value) > 0) 
OR 
exists (select 1 from wx_task_sub ts1 
LEFT HASH JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
left HASH join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id
left join wx_task_check tc1 on tc1.check_id=tic1.check_id 
left HASH join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id
left join wx_task_check tc2 on tc2.check_id=tic2.check_id 
left join wx_att_file af1 on af1.source_type=3 and af1.source_id=tic1.id 
where ts1.org_id=t1.id and tm1.tmb_type_code in ('TT_2_CLZX','TT_2_XD_CAI','TT_2_FLEET','TT_2_FLEET_ZF') 
AND tm1.create_time &gt;= '2020-01-01' AND tm1.create_time &lt; '2020-06-01' 
<if test="activeDateToStr != null and activeDateToStr != ''">
	and tm1.create_time &lt;  #{activeDateToStr}
</if>
<if test="activeDateFromStr != null and activeDateFromStr != ''">
	and tm1.create_time &gt;= #{activeDateFromStr}
</if>
and tc1.check_code='WORK_SHOW_PHOTO' and af1.att_id is not null
and tc2.check_code='SALE_DELO_PRODUCT' and tic2.check_value is not null and charindex('德乐400', tic2.check_value) > 0) ) 
<!-- union all
select wp1.fleet_name, o.organization_name partner_name, o.id partner_id, wp1.id, v1.region_name, null dms_key,
pe.distributor_id, wp1.ext_flag ext_flag1,(SELECT MAX(tm1.create_time) FROM wx_task_sub s LEFT JOIN wx_task_main tm1 ON s .task_main_id = tm1.task_main_id
where s.org_id = wp1.id AND tm1.tmb_type_code IN ('TT_2_FLEET','TT_2_FLEET_ZF') 
and s.task_status=4 and tm1.create_time >'2020-06-01'
<if test="activeDateToStr != null and activeDateToStr != ''">
	and tm1.create_time &lt;  #{activeDateToStr}
</if>
<if test="activeDateFromStr != null and activeDateFromStr != ''">
	and tm1.create_time &gt;= #{activeDateFromStr}
</if>) task_time, wp1.delo_400_xg_sj,u.ch_name execute_user_name,2 customer_type from wx_t_fleet_info wp1
left join wx_t_partner_o2o_enterprise pe on wp1.partner_id=pe.partner_id
left join wx_t_organization o on o.id=wp1.partner_id
left join view_customer_region_sales_channel v1 on v1.distributor_id=pe.distributor_id and v1.channel_weight&amp;2>0
LEFT JOIN wx_t_user u ON u.user_id = wp1.create_user_id 
where wp1.delete_flag=0 and wp1.status='3' $Permission_Clause$
		<if test="customerType != null and customerType > 0 and customerType != 2">
		and 1!=1
		</if>
		<if test="workshopName != null and workshopName != ''">
			AND wp1.fleet_name LIKE '%' + #{workshopName} + '%'
		</if>
		<if test="queryField != null and queryField != ''">
			AND wp1.fleet_name LIKE '%' + #{queryField} + '%'
		</if>
		<if test="partnerId != null and partnerId != ''">
			AND wp1.partner_id = #{partnerId}
		</if>
		<if test="regionName != null and regionName != ''">
			AND v1.region_name = #{regionName}
		</if>
		<choose>
			<when test="extFlag != null and extFlag == 0">
			and wp1.ext_flag&amp;1024=0
			</when>
			<when test="extFlag != null and extFlag > 0">
			and wp1.ext_flag&amp;#{extFlag}>0
			</when>
		</choose>
and exists (select 1 from wx_task_sub ts1 
LEFT HASH JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
left HASH join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id
left join wx_task_check tc1 on tc1.check_id=tic1.check_id 
left join wx_att_file af1 on af1.source_type=3 and af1.source_id=tic1.id 
left HASH join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id
left join wx_task_check tc2 on tc2.check_id=tic2.check_id 
where ts1.org_id=wp1.id and tm1.tmb_type_code in ('TT_2_FLEET', 'TT_2_FLEET_ZF') 
and ts1.task_status=4 and tm1.create_time >'2020-06-01'
<if test="activeDateToStr != null and activeDateToStr != ''">
	and tm1.create_time &lt;  #{activeDateToStr}
</if>
<if test="activeDateFromStr != null and activeDateFromStr != ''">
	and tm1.create_time &gt;= #{activeDateFromStr}
</if>
and tc1.check_code='DELO400_MGX_PHOTO' and af1.att_id is not null
and tc2.check_code='SALE_DELO_PRODUCT' and tic2.check_value is not null and charindex('德乐400', tic2.check_value) > 0)  --> ) t_delo_mgx
) t_delo where  1 = 1  
<choose>
	<when test="extFlag != null and extFlag == 0">
	and (t_delo.ext_flag&amp;1024=0 and t_delo.ext_flag&amp;2048=0 )
	</when>
	<when test="extFlag != null and extFlag == 1024">
	and t_delo.ext_flag&amp;#{extFlag}>0
	</when>
	<when test="extFlag != null and extFlag == 2048">
	and t_delo.ext_flag&amp;#{extFlag}>0
	and t_delo.ext_flag&amp;1024 = 0
	</when>
</choose>
	</select>
	<select id="queryDeloMgxActivityAtts" resultMap="BaseResultMap" parameterType="map">
select ts1.org_id*10 + (case when tm1.tmb_type_code in ('TT_2_CLZX','TT_2_XD_CAI') then 1 else 2 end) id, af1.att_id from wx_task_sub ts1 
LEFT JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
left join wx_task_instance_check tic1 on tic1.sub_task_id=ts1.task_id
left join wx_task_check tc1 on tc1.check_id=tic1.check_id 
left join wx_att_file af1 on af1.source_type=3 and af1.source_id=tic1.id 
left join wx_task_instance_check tic2 on tic2.sub_task_id=ts1.task_id
left join wx_task_check tc2 on tc2.check_id=tic2.check_id 
where 1 = 1 AND af1.att_id is not NULL and (tc1.check_code='DELO400_MGX_PHOTO' or tc1.check_code='WORK_SHOW_PHOTO') and ts1.task_status=4 and tm1.create_time >'2020-01-01'
and (1!=1 
<if test="workshopIds != null">
or (tm1.tmb_type_code in ('TT_2_CLZX','TT_2_XD_CAI') and ts1.org_id in (
<foreach collection="workshopIds" item="item" separator=",">
${item}
</foreach>
) and tc2.check_code='SALE_DELO_PRODUCT' and tic2.check_value is not null and charindex('德乐400', tic2.check_value) > 0)
</if>
<if test="fleetIds != null">
or (tm1.tmb_type_code in ('TT_2_FLEET', 'TT_2_FLEET_ZF') and ts1.org_id in (
<foreach collection="fleetIds" item="item" separator=",">
${item}
</foreach>
) and tc2.check_code='SALE_DELO_PRODUCT' and tic2.check_value is not null and charindex('德乐400', tic2.check_value) > 0)
</if>
)
	</select>
	    <!-- 分页查询 -->
	<select id="getMktCustomerList" resultMap="BaseResultMap"
			parameterType="com.chevron.master.model.WorkshopMasterParams">
		select t2.*,r1.region_name AS dist_name,
		r2.region_name AS city_name, r3.region_name AS province_name, o.organization_name AS partner_name
		<if test="funFlag != 'resourcePortal'">
			,0 + CASE WHEN EXISTS (
			SELECT 1
			FROM wx_t_mkt_ci_apply
			WHERE sign_type = 'STORE_SIGN'
			AND store_id = t2.id
			and customer_type = t2.customer_type
			AND form_status <![CDATA[ < ]]> 60
			) THEN 0 ELSE 1 end as operation_flag
		</if>
		from
		(
		select t1.customer_type,b.partner_id,t1.id, t1.work_shop_name, t1.work_shop_address,
		t1.type,t1.contact_person, t1.contact_person_tel,<!-- t1.shop_recruitment, --> t1.ext_flag , t1.ext_property7 ,t1.region_id
		<if test="funFlag == 'resourcePortal'">
			,(case when exists (select 1 from wx_t_workshop_relationship wr1 where wr1.relation_type=#{funKey} and ((convert(INTEGER,wr1.workshop_id)-1)/10)=t1.id
			<if test="excludeFunKey != null and excludeFunKey != ''">
				and wr1.source_key!=#{excludeFunKey}
			</if>
			) then 0 else 1 end ) operation_flag
		</if>
		from wx_t_work_shop t1 join wx_t_workshop_partner b ON b.workshop_id = t1.id where t1.delete_flag = 0 and
		t1.status = 3
		<if test="fromSource != null and fromSource > 0">
			AND t1.from_source <![CDATA[ & ]]> #{fromSource} > 0
		</if>
		<if test="businessWeight !=null and businessWeight > 0">
			and t1.business_weight &amp; #{businessWeight} &gt;0
		</if>
		<!--
         <if test="includeFleetIndustrial">
            union ALL
            select 2 as customer_type,t_main.tenant_id partner_id,t1.id, t1.fleet_name work_shop_name, t1.fleet_address work_shop_address,t1.fleet_type type,
            null as contact_person, t1.fleet_phone as contact_person_tel,case when t1.ext_flag  <![CDATA[ & ]]> 1073741856 > 0 then 1 else 0 end as shop_recruitment,
            t1.ext_flag ,t1.fleet_num as seats_num,t1.region_id
            <if test="funFlag == 'resourcePortal'">
                 ,(case when exists (select 1 from wx_t_workshop_relationship wr1 where wr1.relation_type=#{funKey} and ((convert(INTEGER,wr1.workshop_id)-2)/10)=t1.id
                 <if test="excludeFunKey != null and excludeFunKey != ''">
                 and wr1.source_key!=#{excludeFunKey}
                 </if>
                 ) then 0 else 1 end ) operation_flag
                 </if>
            from wx_t_fleet_info t1
            LEFT JOIN wx_task_sub t_sub ON t1.id = t_sub.org_id
            LEFT JOIN wx_task_main t_main ON t_sub.task_main_id = t_main.task_main_id
            where t1.delete_flag = 0 AND t_main.tmb_type_code = 'TT_2_FLEET' and t1.status = 3
            union ALL
            select 3 as customer_type,t1.partner_id,t1.id,t1.project_name work_shop_name,t1.project_address work_shop_address,null as type,null as contact_person,
            null as contact_person_tel,case when t1.ext_flag  <![CDATA[ & ]]> 1073741856 > 0 then 1 else 0
            end as shop_recruitment,t1.ext_flag ,null as seats_num,t1.region_id
            <if test="funFlag == 'resourcePortal'">
                 ,(case when exists (select 1 from wx_t_workshop_relationship wr1 where wr1.relation_type=#{funKey} and ((convert(INTEGER,wr1.workshop_id)-3)/10)=t1.id
                 <if test="excludeFunKey != null and excludeFunKey != ''">
                 and wr1.source_key!=#{excludeFunKey}
                 </if>
                 ) then 0 else 1 end ) operation_flag
                 </if>
            from wx_t_construction_machinery t1
            where t1.delete_flag = 0
          </if>
          -->
		) t2
		LEFT JOIN wx_t_partner_o2o_enterprise wp1 ON wp1.partner_id = t2.partner_id
		LEFT JOIN wx_t_organization o ON wp1.partner_id = o.id
		LEFT JOIN wx_t_region r1 ON t2.region_id = r1.id
		LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
		LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
		<where>
			and o.status = 1
			<if test="partnerId != null">
				and t2.partner_id = #{partnerId}
			</if>
			<if test="workshopName != null and workshopName != ''">
				and t2.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
			</if>
			<if test="customerType != null">
				and t2.customer_type &amp; #{customerType} > 0
			</if>
			<if test="queryField != null and queryField != ''">
				and (t2.work_shop_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
			</if>
		</where>
	</select>

	<!-- 分页查询 -->
	<select id="getMktApplyCustomerList" resultMap="BaseResultMap"
			parameterType="com.chevron.master.model.WorkshopMasterParams">
		select t2.*, r1.region_name AS dist_name,
		r2.region_name AS city_name, r3.region_name AS province_name, o.organization_name AS partner_name,
		0 + CASE WHEN EXISTS (
			SELECT 1
			FROM wx_t_mkt_signage_apply
			WHERE sign_type in ('STORE_SIGN','OUTSIDE_AD','CAR_AD')
			AND store_id = t2.id
-- 			and customer_type = t2.customer_type
			AND form_status <![CDATA[ >= ]]> 10
			AND form_status <![CDATA[ < ]]> 60
			<if test="brand != null">
				and brand = #{brand}
			</if>
			) THEN 0 ELSE 1 end as operation_flag
		from
		(
		select t1.customer_type, b.partner_id,t1.id, t1.work_shop_name, t1.work_shop_address,
		t1.type, t1.contact_person, t1.contact_person_tel, t1.ext_flag, t1.ext_property7, t1.region_id
		from wx_t_work_shop t1 join wx_t_workshop_partner b ON b.workshop_id = t1.id where t1.delete_flag = 0 and
		t1.status = 3
<!--		<if test="fromSource != null and fromSource > 0">-->
<!--&#45;&#45; 			AND t1.from_source <![CDATA[ & ]]> #{fromSource} > 0-->
<!--		</if>-->
<!--		<if test="businessWeight !=null and businessWeight > 0">-->
<!--&#45;&#45; 			and (t1.business_weight|(t1.from_source &amp; 3)) &amp; #{businessWeight} &gt;0-->
<!--		</if>-->
		) t2
		LEFT JOIN wx_t_partner_o2o_enterprise wp1 ON wp1.partner_id = t2.partner_id
		LEFT JOIN wx_t_organization o ON wp1.partner_id = o.id
		LEFT JOIN wx_t_region r1 ON t2.region_id = r1.id
		LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
		LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
		<where>
			and o.status = 1
			<if test="partnerId != null">
				and t2.partner_id = #{partnerId}
			</if>
			<if test="workshopName != null and workshopName != ''">
				and t2.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
			</if>
<!--			<if test="customerType != null">-->
<!--&#45;&#45; 				and t2.customer_type &amp; #{customerType} > 0-->
<!--			</if>-->
			<if test="queryField != null and queryField != ''">
				and (t2.work_shop_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
			</if>
		</where>
	</select>

    <select id="getReNameWorkShopList" resultType="com.chevron.master.model.ReNameWorkShopVo">
    	SELECT
		 v1.customer_name_cn customerNameCn,pe.distributor_id distributorId,wp1.partner_id partnerId,w1.work_shop_name workshopName,
		 w1.id,w1.create_time createTime,w2.work_shop_name workshopName2, w2.id id2,w2.create_time createTime2
		FROM
			wx_t_work_shop w1
		LEFT JOIN wx_t_workshop_partner wp1 ON w1.id = wp1.workshop_id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON wp1.partner_id = pe.partner_id
		LEFT JOIN view_customer_region_sales_channel v1 ON v1.distributor_id = pe.distributor_id
		LEFT JOIN wx_t_workshop_partner wp2 ON wp1.partner_id = wp2.partner_id
		LEFT JOIN wx_t_work_shop w2 ON wp2.workshop_id = w2.id
		where 1 = 1 
		AND w1.id != w2.id
		AND w1.create_time >= '2020-06-01'
		AND w2.create_time >= '2020-06-01'
		AND (w1.work_shop_name = w2.work_shop_name 
		OR (charindex(
									w1.work_shop_name,
									w2.work_shop_name
								) > 0 ) 
		OR (charindex(
									w2.work_shop_name,
									w1.work_shop_name
								) > 0 ))
		AND w1.delete_flag = 0
		AND w2.delete_flag = 0
		AND w1.status = 3 
		AND w2.status = 3
		ORDER BY wp1.partner_id
    </select>
    
	<select id="querySimpleByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.creator, t1.create_time, t1.update_user_id, t1.update_time, t1.work_shop_name, t1.region_id,
			 t1.work_shop_address, t1.longitude, t1.latitude, t1.type, t1.contact_person, t1.contact_person_tel, t1.from_source,
			 t1.delete_flag, t1.business_weight, t1.remark, t1.status, t1.excute_user_id, t1.photo_id, t1.work_shop_name_py,
			 t1.ext_flag, t1.ext_flag2, t1.ext_flag3, t1.partner_id, t1.customer_type, t1.dms_key, 
			 r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, o.organization_name partner_name,
			 tt_file.att_id, u1.ch_name execute_user_name
		  from wx_t_work_shop t1
		  left join wx_t_partner_o2o_enterprise wp1 on wp1.partner_id=t1.partner_id
		  join wx_t_organization o on wp1.partner_id=o.id 
		    left join wx_t_region r1 on t1.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
		    left join wx_t_user u1 on u1.user_id=t1.excute_user_id
		LEFT JOIN wx_att_file tt_file ON tt_file.uuid=t1.photo_id
		 where 
		 <choose>
		 	<when test="deleteFlag > 0">
		 		t1.delete_flag=#{deleteFlag}
		 	</when>
			<when test="deleteFlag &lt; 0"> 1 = 1 </when>
		 	<otherwise>
		 	 t1.delete_flag=0
		 	</otherwise>
		 </choose>
		<if test="id != null">
		and t1.id=#{id}
		</if>
		<if test="workshopName != null and workshopName != ''">
			and t1.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="regionId != null and regionId != ''">
			and t1.region_id like '%' + #{regionId, jdbcType=VARCHAR} + '%'
		</if>
		<if test="type != null and type != ''">
			and t1.type = #{type, jdbcType=VARCHAR}
		</if>
		<if test="source != null and source != ''">
			and t1.from_source = #{source,jdbcType=VARCHAR}
		</if>
		<if test="partnerId != null">
		and t1.partner_id=#{partnerId}
		</if>
		<if test="customerType != null">
		and t1.customer_type=#{customerType}
		</if>
		<if test="status != null">
		and t1.status=#{status}
		</if>
		<if test="executeUserId != null">
			and t1.excute_user_id=#{executeUserId}
		</if>
		<if test="executeUserName != null and executeUserName != ''">
		and u1.ch_name like '%' + #{executeUserName} + '%'
		</if>
		<if test="businessWeight != null and businessWeight > 0">
			and t1.business_weight &amp; ${businessWeight} > 0
		</if>
		<if test="employeeMobile != null">
		and exists (select 1 from wx_t_workshop_employee we where we.mobile=#{employeeMobile} and we.workshop_id=t1.id)
		</if>
		<if test="mechanicCode != null">
		and exists (select 1 from wx_t_workshop_employee we where we.code=#{mechanicCode} and we.workshop_id=t1.id)
		</if>
		<if test="existsEmployee">
		and exists (select 1 from wx_t_workshop_employee we where we.workshop_id=t1.id)
		</if>
		<if test="ids != null">
		and t1.id in
			<foreach item="id" index="index" collection="ids" open="(" separator="," close=")">  
	             #{id}  
	        </foreach> 
		</if>
		<if test="prov != null">
			and r2.parent_id = #{prov,jdbcType=BIGINT}
		</if>
		<if test="city != null">
			and r2.id = #{city,jdbcType=BIGINT}
		</if>
		<if test="dist != null">
			and r1.id = #{dist,jdbcType=BIGINT}
		</if>
		<if test="location != null">
			and (t1.longitude between #{location.minX} and #{location.maxX})
			and (t1.latitude between #{location.minY} and #{location.maxY})
		</if>
		<if test="amStatus != null">
		<foreach collection="amStatus" item="item" index="index" open=" and t1.status in (" close=")" separator=",">
		'${item}'
		</foreach>
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="querySimpleForPage" resultMap="BaseResultMap" parameterType="com.chevron.master.model.WorkshopMasterParams">
		select a.*, u.ch_name execute_user_name from (
			select t1.id, t1.work_shop_name,t1.region_id, t1.work_shop_address, t1.longitude, t1.latitude, t1.customer_type,
				<choose>
					<when test="longitude == null">
						null work_shop_range,
					</when>
					<otherwise>
						dbo.fun_cal_distance(${longitude}, ${latitude}, t1.longitude, t1.latitude) work_shop_range,
					</otherwise>
				</choose>
				t1.type, t1.contact_person, t1.contact_person_tel, t1.from_source, t1.delete_flag, t1.business_weight,
				t1.remark, t1.status, t1.excute_user_id,  t1.ext_flag, t1.ext_flag2, t1.ext_flag3,
				r1.region_name dist_name, r2.region_name city_name, r3.region_name province_name, o.organization_name partner_name,
				tt_file.att_id, t1.dms_key,0
				<choose>
					<when test="funFlag == 'CioMktApply'">
					+ (case when exists (select 1 from view_mkt_apply v where v.apply_key=#{mktKey} + '/' + convert(nvarchar(30), t1.id)) then 0 else 1 end)
					</when>
					<when test="funFlag == 'resourcePortal'">
					+ (case when exists (select 1 from wx_t_workshop_relationship wr1 where wr1.relation_type=#{funKey} and wr1.workshop_id=t1.id
					<if test="excludeFunKey != null and excludeFunKey != ''">
					and wr1.source_key!=#{excludeFunKey}
					</if>
					) then 0 else 1 end)
					</when>
					<when test="funFlag == 'mktCi'">
					 + (case when exists (select 1 from wx_t_mkt_ci_apply where sign_type=#{mktKey} and store_id=t1.id and form_status <![CDATA[ < ]]> 60
					 union all select 1 from view_mkt_apply v where v.apply_key=#{oldMktKey} + '/' + convert(nvarchar(30), t1.id) and v.current_step1>1 and (v.flow_finish3 is null or v.flow_finish3!=1)) then 0 else 1 end)
					</when>
				</choose> operation_flag,
				(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Workshop.customerType' and di1.dic_item_code=t1.customer_type) customer_type_text,
				t1.create_time,
				t1.update_time
			from wx_t_work_shop t1
			join wx_t_organization o on t1.partner_id=o.id
			left join wx_t_region r1 on t1.region_id=r1.id
			left join wx_t_region r2 on r1.parent_id=r2.id
			left join wx_t_region r3 on r2.parent_id=r3.id
			left join wx_att_file tt_file ON tt_file.uuid=t1.photo_id
			where t1.delete_flag=0 and t1.status>=0 $Permission_Clause$
			<include refid="basePageCond"/>
		) a
		left join wx_t_user u on a.excute_user_id=u.user_id
		where 1= 1
		<if test="distance != null">
			and (a.work_shop_range/1000) &lt;= #{distance}
		</if>
	</select>

	<select id="totalSimpleForPage" resultType="long" parameterType="com.chevron.master.model.WorkshopMasterParams">
		select count(*) from (
			select
				<choose>
					<when test="longitude == null">
						null work_shop_range
					</when>
					<otherwise>
						dbo.fun_cal_distance(${longitude}, ${latitude}, t1.longitude, t1.latitude) work_shop_range
					</otherwise>
				</choose>
			from wx_t_work_shop t1
			join wx_t_organization o on t1.partner_id=o.id
			left join wx_t_region r1 on t1.region_id=r1.id
			left join wx_t_region r2 on r1.parent_id=r2.id
			left join wx_t_region r3 on r2.parent_id=r3.id
			where t1.delete_flag=0 and t1.status>=0 $Permission_Clause$
			<include refid="basePageCond"/>
		) a
		where 1= 1
		<if test="distance != null">
			and (a.work_shop_range/1000) &lt;= #{distance}
		</if>
	</select>

	<sql id="basePageCond">
		 <choose>
		 	<when test="status == '6'.toString() or deleteFlag == 1">
		 	and t1.delete_flag=1
		 	</when>
		 	<otherwise>
		 	and t1.delete_flag=0
			</otherwise>
		 </choose>
		 <choose>
		 	<when test="null != status and status != '' and status != '6'.toString()">
		 	and t1.status=#{status, jdbcType=VARCHAR}
		 	</when>
		 	<when test="amStatus == null and statusArrays == null">
		 	and t1.status>=0
		 	</when>
		 </choose>
 		
 		<if test="regionId != null">
 			and t1.region_id = #{regionId}
 		</if>
		 <if test="workshopId != null">
		 and t1.id=#{workshopId}
		 </if>
		<if test="partnerId != null">
			and t1.partner_id=#{partnerId}
		</if>
		<if test="customerType != null and customerType > 0">
			and t1.customer_type&amp; #{customerType} > 0
		</if>
		<if test="extProperty6 != null">
			and ext_property6 = #{extProperty6}
		</if>
		<if test="workshopName != null and workshopName != ''">
			and t1.work_shop_name like '%' + #{workshopName, jdbcType=VARCHAR} + '%'
		</if>
		<if test="type != null and type != ''">
			and t1.type = #{type, jdbcType=VARCHAR}
		</if>
		<if test="prov != null">
			and r3.id = #{prov,jdbcType=BIGINT}
		</if>
		<if test="city != null">
			and r2.id = #{city,jdbcType=BIGINT}
		</if>
		<if test="dist != null">
			and r1.id = #{dist,jdbcType=BIGINT}
		</if>
		<if test="businessWeights != null and businessWeights != ''">
			and t1.business_weight = #{businessWeights} 
		</if>
		<if test="businessWeight != null and businessWeight > 0">
			and t1.business_weight &amp; ${businessWeight} > 0
		</if>
		<if test="amStatus != null">
		<foreach collection="amStatus" item="item" index="index" open=" and t1.status in (" close=")" separator=",">
		'${item}'
		</foreach>
		</if>
		<if test="statusArrays != null">
		<foreach collection="statusArrays" item="item" index="index" open=" and t1.status in (" close=")" separator=",">
		'${item}'
		</foreach>
		</if>
		<if test="partnerIds != null">
		<foreach collection="partnerIds" item="item" index="index" open=" and t1.partner_id in (" close=")" separator=",">
		'${item}'
		</foreach>
		</if>
		<if test="fromSource != null and fromSource > 0">
			and t1.from_source &amp; ${fromSource} > 0
		</if>
		<if test="queryField != null and queryField != ''">
			and (t1.work_shop_name like '%' + #{queryField, jdbcType=VARCHAR} + '%')
		</if>
		<if test="executeUserId != null">
			and t1.excute_user_id=#{executeUserId}
		</if>
		<if test="activeDateFrom != null">
			and t1.activation_time&gt;=#{activeDateFrom}
		</if>
		<if test="activeDateTo != null">
			and t1.activation_time&lt;#{activeDateTo}
		</if>
		<if test="extFlag != null">
			and (t1.ext_flag &amp; ${extFlag}) > 0
		</if>
		<if test="route != null and route != '' ">
            and t1.route = #{route}
        </if>
	</sql>
  
  <resultMap id="WorkShopDistanceMap" type="com.chevron.pms.model.WorkShopDistInfo" >
    <result column="dist" property="dist" jdbcType="NUMERIC" />
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="work_shop_name" property="workShopName" jdbcType="NVARCHAR" />
    <result column="fleet_name" property="fleetName" jdbcType="NVARCHAR" />
    <result column="project_name" property="projectName" jdbcType="NVARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="work_shop_num" property="workShopNum" jdbcType="BIGINT" />
    <result column="distance" property="distance" jdbcType="BIGINT" />
  </resultMap>
  <select id="queryWorkShopDistanceById" resultMap="WorkShopDistanceMap" parameterType="map">
		  	SELECT * FROM 
			(
				SELECT 
					dbo.fun_cal_distance (
						w.longitude,
						w.latitude,
						t1.longitude,
						t1.latitude
					) dist,t1.id,t1.work_shop_name,wp1.partner_id,
					
					(CASE WHEN (cd.client_num = null or cd.client_num = 0 ) THEN
					 (CASE WHEN w.business_weight &amp; 6 > 0 THEN 
					 (SELECT top 1 code FROM wx_t_properties where codetype = 'delo_work_num')
					  ELSE (SELECT top 1 code FROM wx_t_properties where codetype = 'cdm_work_num') END )
					   ELSE cd.client_num END ) work_shop_num,
					   
					 (CASE WHEN (cd.distance = null or cd.distance = 0 ) THEN
					 (CASE WHEN w.business_weight &amp; 6 > 0 THEN 
					 (SELECT top 1 code FROM wx_t_properties where codetype = 'delo_work_distance')
					  ELSE (SELECT top 1 code FROM wx_t_properties where codetype = 'cdm_work_distance') END )
					   ELSE cd.distance END ) distance
					<!--cd.client_num work_shop_num,
					cd.distance  o1.work_shop_num,o1.distance -->
				FROM
					wx_t_work_shop t1
				LEFT JOIN wx_t_work_shop w ON w.id != t1.id AND w.business_weight = t1.business_weight
				LEFT JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = t1.id
				LEFT JOIN wx_t_workshop_partner wp ON wp.workshop_id = w.id
				LEFT JOIN wx_t_client_distance cd ON cd.partner_id = wp1.partner_id AND cd.delete_flag = 0 AND cd.client_type = w.customer_type
					AND cd.from_source = (case when w.business_weight=4 then 2 else w.business_weight end)
				<!-- LEFT JOIN wx_t_organization o1 ON o1.id = wp1.partner_id 
				LEFT JOIN wx_t_organization o ON o.id = wp.partner_id  -->
				WHERE
					1 = 1 
				<!-- AND	w.status IN ('0', '3','-10','-5')
				AND	t1.status IN ('0', '3','-10','-5') -->
				AND	w.status IN ('0', '3','-5')
				AND	t1.status  = '3'
				AND w.delete_flag = 0
				AND w.longitude > 0
				AND w.latitude > 0
				AND w.id = #{id}
                AND t1.customer_type = w.customer_type
				AND wp1.partner_id = wp.partner_id
				<!-- AND o1.id = o.id -->
				AND t1.delete_flag = 0
				AND t1.longitude > 0
				AND t1.latitude > 0
				AND t1.id != w.id
			) a
		WHERE
			a.dist &lt;= a.distance
		AND a.dist >= -a.distance 
  </select>
  <select id="checkWorkShop" resultType="java.lang.Long" parameterType="map">
  		SELECT
			top 1 x3.id
		FROM
			(
				SELECT
					w1.id,
					(
						CASE
						WHEN w1.contact_person_tel IS NOT NULL
						AND len(w1.contact_person_tel) > 6
						AND w1.contact_person_tel = '${workShop.contactPersonTel}' THEN
							30000
						ELSE
							0
						END
					) 
					<if test="workShop.regionId != null">
					+ (
						CASE
						WHEN w1.region_id IS NOT NULL AND w1.region_id = '${workShop.regionId}' THEN
							5000
						ELSE
							0
						END
					)
					</if>
					 + (
						CASE
						WHEN wp1.partner_id = ${origId} THEN
							1000
						ELSE
							0
						END
					) + (
						CASE
						WHEN charindex('号', w1.work_shop_address) > 0
						AND charindex(
							w1.work_shop_address,
							'${workShop.workShopAddress}'
						) > 0 THEN
							100
						ELSE
							0
						END
					) + (
						CASE
						WHEN charindex(
							'号',
							'${workShop.workShopAddress}'
						) > 0
						AND charindex(
							'${workShop.workShopAddress}',
							w1.work_shop_address
						) > 0 THEN
							100
						ELSE
							0
						END
					) + (
						CASE
						WHEN charindex(
							w1.work_shop_name,
							'${workShop.workShopName}'
						) > 0 THEN
							300
						ELSE
							0
						END
					) + (
						CASE
						WHEN charindex(
							'${workShop.workShopName}',
							w1.work_shop_name
						) > 0 THEN
							300
						ELSE
							0
						END
					) match_weight
				FROM
					wx_t_work_shop w1
				LEFT JOIN wx_t_workshop_partner wp1 ON wp1.workshop_id = w1.id
				WHERE w1.delete_flag=0
				<if test="fromSource != null and fromSource != ''">
					and w1.from_source = ${fromSource} 
				</if>
				<if test="workShop.id != null and workShop.id != ''">
					and w1.id != #{workShop.id}
				</if>
				<if test="source != null">
					and w1.from_source &amp; #{source} >0
				</if>
				<if test="customerType != null">
					and w1.customer_type &amp; #{customerType} > 0
				</if>
			) x3
		WHERE
			x3.match_weight > 6200
  </select>
    <select id="getAvailableRoutes" resultType="java.lang.String" parameterType="string">
        select DISTINCT route from wx_t_work_shop t1 where t1.delete_flag=0 and t1.status>=0 and route is not null
        <if test="route !=null and route != ''">
            and route like concat('%',#{route, jdbcType=NVARCHAR},'%')
        </if>
    </select>

    <update id="updateDeleteFlagByIds"  parameterType="List">
		update wx_t_work_shop set       
	       delete_flag = 1
	       where id in
	       <if test="list != null">
			    <foreach item="item" collection="list" separator="," open="(" close=")" index="">
			          #{item, jdbcType=BIGINT}
			     </foreach>
		    </if>
		
	</update>

    <update id="batchUpdateRouteById" parameterType="list">
        <if test="list != null">
            <foreach collection="list" separator=";" item="item">
                    update wx_t_work_shop set route = #{item.route},route_owner = #{item.routeOwner},route_owner_account = #{item.routeOwnerAccount} where id = #{item.id}
            </foreach>
        </if>
    </update>
</mapper>

