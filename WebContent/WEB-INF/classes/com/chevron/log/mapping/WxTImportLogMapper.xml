<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.log.dao.WxTImportLogMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.log.model.WxTImportLog" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="file_name" property="fileName" jdbcType="NVARCHAR" />
    <result column="import_data_type" property="importDataType" jdbcType="NVARCHAR" />
    <result column="import_result" property="importResult" jdbcType="NVARCHAR" />
    <result column="import_time" property="importTime" jdbcType="TIMESTAMP" />
    <result column="import_detail_message" property="importDetailMessage" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, file_name, import_data_type, import_result, import_time, import_detail_message, 
    create_time, created_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.log.model.WxTImportLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_import_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.log.model.WxTImportLogExample" >
    delete from wx_t_import_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.log.model.WxTImportLog" >
    insert into wx_t_import_log (id, file_name, import_data_type, 
      import_result, import_time, import_detail_message, 
      create_time, created_by)
    values (#{id,jdbcType=BIGINT}, #{fileName,jdbcType=NVARCHAR}, #{importDataType,jdbcType=NVARCHAR}, 
      #{importResult,jdbcType=NVARCHAR}, #{importTime,jdbcType=TIMESTAMP}, #{importDetailMessage,jdbcType=NVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.log.model.WxTImportLog" >
    insert into wx_t_import_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="importDataType != null" >
        import_data_type,
      </if>
      <if test="importResult != null" >
        import_result,
      </if>
      <if test="importTime != null" >
        import_time,
      </if>
      <if test="importDetailMessage != null" >
        import_detail_message,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=NVARCHAR},
      </if>
      <if test="importDataType != null" >
        #{importDataType,jdbcType=NVARCHAR},
      </if>
      <if test="importResult != null" >
        #{importResult,jdbcType=NVARCHAR},
      </if>
      <if test="importTime != null" >
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="importDetailMessage != null" >
        #{importDetailMessage,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_import_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null" >
        file_name = #{record.fileName,jdbcType=NVARCHAR},
      </if>
      <if test="record.importDataType != null" >
        import_data_type = #{record.importDataType,jdbcType=NVARCHAR},
      </if>
      <if test="record.importResult != null" >
        import_result = #{record.importResult,jdbcType=NVARCHAR},
      </if>
      <if test="record.importTime != null" >
        import_time = #{record.importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.importDetailMessage != null" >
        import_detail_message = #{record.importDetailMessage,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_import_log
    set id = #{record.id,jdbcType=BIGINT},
      file_name = #{record.fileName,jdbcType=NVARCHAR},
      import_data_type = #{record.importDataType,jdbcType=NVARCHAR},
      import_result = #{record.importResult,jdbcType=NVARCHAR},
      import_time = #{record.importTime,jdbcType=TIMESTAMP},
      import_detail_message = #{record.importDetailMessage,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="countImportLogByConditionWithPagination" parameterType="com.chevron.log.model.WxTImportLogParams" resultType="long">
	SELECT count(1) from wx_t_import_log t 
	where 1=1
	<if test="fileName != null and fileName != '' ">
		and t.file_name = #{fileName,jdbcType=VARCHAR}
    </if>
    <if test="importResult != null and importResult != ''">
    	and t.import_result = #{importResult, jdbcType=VARCHAR}
 	</if>
  </select>
  <select id="queryImportLogByConditionWithPagination" parameterType="com.chevron.log.model.WxTImportLogParams" resultMap="BaseResultMap">
  <!-- 数据权限开启的情况 -->
   		 <if test="isOpernCustomMybatisInterceptor==1">
   		 	select 
		  	<include refid="Base_Column_List" />
		    	from wx_t_import_log t
  				where 1 = 1
	 		<if test="fileName != null and fileName != '' ">
				and t.file_name = #{fileName,jdbcType=VARCHAR}
	  		</if>
	  		<if test="importResult != null and importResult != ''">
	  			and t.import_result = #{importResult, jdbcType=VARCHAR}
			</if>
   		 </if>
   		 <!-- 数据权限开启的情况 -->
   		 <if test="isOpernCustomMybatisInterceptor==2">
			select 
			<if test="limit != -1">
		 		top ${limit}
		 	</if>
			<include refid="Base_Column_List" /> 
			from 
				  (
				  	  select 
				    <if test="orderBy != null">
				    	row_number() over(order by ${orderBy}) as rownumber,
				    </if>
				  	<include refid="Base_Column_List" />
				    	from wx_t_import_log t
		  				where 1 = 1
			 		<if test="fileName != null and fileName != '' ">
						and t.file_name = #{fileName,jdbcType=VARCHAR}
			  		</if>
			  		<if test="importResult != null and importResult != ''">
			  			and t.import_result = #{importResult, jdbcType=VARCHAR}
					</if>
				  ) t 
			 where 1 = 1
			 <if test="start != -1 and orderBy != null">
			     and t.rownumber > #{start, jdbcType=INTEGER} 
			 </if> 	
	 </if>
  </select>
  
  
  <select id="getLogInfo" parameterType="map" resultMap="BaseResultMap">   
    SELECT * FROM wx_t_import_log
    WHERE 
        <if test="importDataType!=null">
            import_data_type = #{importDataType}
        </if>
         <if test="importResult!=null">
	     AND import_result = #{importResult}
	     </if>
    </select>
</mapper>