<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.haorder.dao.HaOrderVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.haorder.model.HaOrderVo" >
    <id column="orderid" property="orderid" jdbcType="BIGINT" />
    <result column="batchid" property="batchid" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
    <result column="order_type" property="orderType" jdbcType="NVARCHAR" />
    <result column="order_source" property="orderSource" jdbcType="NVARCHAR" />
    <result column="order_status" property="orderStatus" jdbcType="NVARCHAR" />
    <result column="order_partnerid" property="orderPartnerid" jdbcType="BIGINT" />
    <result column="order_partnername" property="orderPartnername" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP" />
    <result column="deldate" property="deldate" jdbcType="TIMESTAMP" />
    <result column="pricingdt" property="pricingdt" jdbcType="TIMESTAMP" />
    <result column="bill_no" property="billNo" jdbcType="NVARCHAR" />
    <result column="record_type" property="recordType" jdbcType="NVARCHAR" />
    <result column="legsysid" property="legsysid" jdbcType="NVARCHAR" />
    <result column="extbolnr" property="extbolnr" jdbcType="NVARCHAR" />
    <result column="receive_user" property="receiveUser" jdbcType="NVARCHAR" />
    <result column="receive_phone" property="receivePhone" jdbcType="NVARCHAR" />
    <result column="receive_addr" property="receiveAddr" jdbcType="NVARCHAR" />
    <result column="receive_workshop" property="receiveWorkshop" jdbcType="NVARCHAR" />
    <result column="total_price" property="totalPrice" jdbcType="NVARCHAR" />
    <result column="delivery" property="delivery" jdbcType="NVARCHAR" />
    <result column="category" property="category" jdbcType="NVARCHAR" />
    <result column="client_remark" property="clientRemark" jdbcType="NVARCHAR" />
    <result column="custom_service_remark" property="customServiceRemark" jdbcType="NVARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="approve_status" property="approveStatus" jdbcType="BIGINT" />
    
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    orderid, batchid, order_no, order_type, order_source, order_status, order_partnerid, 
    order_partnername, create_time, trade_time, deldate, pricingdt, bill_no, record_type, 
    legsysid, extbolnr, receive_user, receive_phone, receive_addr, receive_workshop, 
    total_price, delivery, category, client_remark, custom_service_remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.haorder.model.HaOrderVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_haorder
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_haorder
    where orderid = #{orderid,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_haorder
    where orderid = #{orderid,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.haorder.model.HaOrderVoExample" >
    delete from wx_t_haorder
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.haorder.model.HaOrderVo" >
    insert into wx_t_haorder (orderid, batchid, order_no, 
      order_type, order_source, order_status, 
      order_partnerid, order_partnername, create_time, 
      trade_time, deldate, pricingdt, 
      bill_no, record_type, legsysid, 
      extbolnr, receive_user, receive_phone, 
      receive_addr, receive_workshop, total_price, 
      delivery, category, client_remark, 
      custom_service_remark)
    values (#{orderid,jdbcType=BIGINT}, #{batchid,jdbcType=BIGINT}, #{orderNo,jdbcType=NVARCHAR}, 
      #{orderType,jdbcType=NVARCHAR}, #{orderSource,jdbcType=NVARCHAR}, #{orderStatus,jdbcType=NVARCHAR}, 
      #{orderPartnerid,jdbcType=BIGINT}, #{orderPartnername,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{deldate,jdbcType=TIMESTAMP}, #{pricingdt,jdbcType=TIMESTAMP}, 
      #{billNo,jdbcType=NVARCHAR}, #{recordType,jdbcType=NVARCHAR}, #{legsysid,jdbcType=NVARCHAR}, 
      #{extbolnr,jdbcType=NVARCHAR}, #{receiveUser,jdbcType=NVARCHAR}, #{receivePhone,jdbcType=NVARCHAR}, 
      #{receiveAddr,jdbcType=NVARCHAR}, #{receiveWorkshop,jdbcType=NVARCHAR}, #{totalPrice,jdbcType=NVARCHAR}, 
      #{delivery,jdbcType=NVARCHAR}, #{category,jdbcType=NVARCHAR}, #{clientRemark,jdbcType=NVARCHAR}, 
      #{customServiceRemark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.haorder.model.HaOrderVo" >
    insert into wx_t_haorder
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderid != null" >
        orderid,
      </if>
      <if test="batchid != null" >
        batchid,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="orderType != null" >
        order_type,
      </if>
      <if test="orderSource != null" >
        order_source,
      </if>
      <if test="orderStatus != null" >
        order_status,
      </if>
      <if test="orderPartnerid != null" >
        order_partnerid,
      </if>
      <if test="orderPartnername != null" >
        order_partnername,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="tradeTime != null" >
        trade_time,
      </if>
      <if test="deldate != null" >
        deldate,
      </if>
      <if test="pricingdt != null" >
        pricingdt,
      </if>
      <if test="billNo != null" >
        bill_no,
      </if>
      <if test="recordType != null" >
        record_type,
      </if>
      <if test="legsysid != null" >
        legsysid,
      </if>
      <if test="extbolnr != null" >
        extbolnr,
      </if>
      <if test="receiveUser != null" >
        receive_user,
      </if>
      <if test="receivePhone != null" >
        receive_phone,
      </if>
      <if test="receiveAddr != null" >
        receive_addr,
      </if>
      <if test="receiveWorkshop != null" >
        receive_workshop,
      </if>
      <if test="totalPrice != null" >
        total_price,
      </if>
      <if test="delivery != null" >
        delivery,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="clientRemark != null" >
        client_remark,
      </if>
      <if test="customServiceRemark != null" >
        custom_service_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderid != null" >
        #{orderid,jdbcType=BIGINT},
      </if>
      <if test="batchid != null" >
        #{batchid,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="orderSource != null" >
        #{orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="orderStatus != null" >
        #{orderStatus,jdbcType=NVARCHAR},
      </if>
      <if test="orderPartnerid != null" >
        #{orderPartnerid,jdbcType=BIGINT},
      </if>
      <if test="orderPartnername != null" >
        #{orderPartnername,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null" >
        #{deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingdt != null" >
        #{pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="billNo != null" >
        #{billNo,jdbcType=NVARCHAR},
      </if>
      <if test="recordType != null" >
        #{recordType,jdbcType=NVARCHAR},
      </if>
      <if test="legsysid != null" >
        #{legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="extbolnr != null" >
        #{extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="receiveUser != null" >
        #{receiveUser,jdbcType=NVARCHAR},
      </if>
      <if test="receivePhone != null" >
        #{receivePhone,jdbcType=NVARCHAR},
      </if>
      <if test="receiveAddr != null" >
        #{receiveAddr,jdbcType=NVARCHAR},
      </if>
      <if test="receiveWorkshop != null" >
        #{receiveWorkshop,jdbcType=NVARCHAR},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="delivery != null" >
        #{delivery,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="clientRemark != null" >
        #{clientRemark,jdbcType=NVARCHAR},
      </if>
      <if test="customServiceRemark != null" >
        #{customServiceRemark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_haorder
    <set >
      <if test="record.orderid != null" >
        orderid = #{record.orderid,jdbcType=BIGINT},
      </if>
      <if test="record.batchid != null" >
        batchid = #{record.batchid,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderType != null" >
        order_type = #{record.orderType,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderSource != null" >
        order_source = #{record.orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderStatus != null" >
        order_status = #{record.orderStatus,jdbcType=NVARCHAR},
      </if>
      <if test="record.orderPartnerid != null" >
        order_partnerid = #{record.orderPartnerid,jdbcType=BIGINT},
      </if>
      <if test="record.orderPartnername != null" >
        order_partnername = #{record.orderPartnername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeTime != null" >
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deldate != null" >
        deldate = #{record.deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pricingdt != null" >
        pricingdt = #{record.pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billNo != null" >
        bill_no = #{record.billNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.recordType != null" >
        record_type = #{record.recordType,jdbcType=NVARCHAR},
      </if>
      <if test="record.legsysid != null" >
        legsysid = #{record.legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="record.extbolnr != null" >
        extbolnr = #{record.extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="record.receiveUser != null" >
        receive_user = #{record.receiveUser,jdbcType=NVARCHAR},
      </if>
      <if test="record.receivePhone != null" >
        receive_phone = #{record.receivePhone,jdbcType=NVARCHAR},
      </if>
      <if test="record.receiveAddr != null" >
        receive_addr = #{record.receiveAddr,jdbcType=NVARCHAR},
      </if>
      <if test="record.receiveWorkshop != null" >
        receive_workshop = #{record.receiveWorkshop,jdbcType=NVARCHAR},
      </if>
      <if test="record.totalPrice != null" >
        total_price = #{record.totalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="record.delivery != null" >
        delivery = #{record.delivery,jdbcType=NVARCHAR},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=NVARCHAR},
      </if>
      <if test="record.clientRemark != null" >
        client_remark = #{record.clientRemark,jdbcType=NVARCHAR},
      </if>
      <if test="record.customServiceRemark != null" >
        custom_service_remark = #{record.customServiceRemark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_haorder
    set orderid = #{record.orderid,jdbcType=BIGINT},
      batchid = #{record.batchid,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=NVARCHAR},
      order_type = #{record.orderType,jdbcType=NVARCHAR},
      order_source = #{record.orderSource,jdbcType=NVARCHAR},
      order_status = #{record.orderStatus,jdbcType=NVARCHAR},
      order_partnerid = #{record.orderPartnerid,jdbcType=BIGINT},
      order_partnername = #{record.orderPartnername,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      deldate = #{record.deldate,jdbcType=TIMESTAMP},
      pricingdt = #{record.pricingdt,jdbcType=TIMESTAMP},
      bill_no = #{record.billNo,jdbcType=NVARCHAR},
      record_type = #{record.recordType,jdbcType=NVARCHAR},
      legsysid = #{record.legsysid,jdbcType=NVARCHAR},
      extbolnr = #{record.extbolnr,jdbcType=NVARCHAR},
      receive_user = #{record.receiveUser,jdbcType=NVARCHAR},
      receive_phone = #{record.receivePhone,jdbcType=NVARCHAR},
      receive_addr = #{record.receiveAddr,jdbcType=NVARCHAR},
      receive_workshop = #{record.receiveWorkshop,jdbcType=NVARCHAR},
      total_price = #{record.totalPrice,jdbcType=NVARCHAR},
      delivery = #{record.delivery,jdbcType=NVARCHAR},
      category = #{record.category,jdbcType=NVARCHAR},
      client_remark = #{record.clientRemark,jdbcType=NVARCHAR},
      custom_service_remark = #{record.customServiceRemark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.haorder.model.HaOrderVo" >
    update wx_t_haorder
    <set >
      <if test="batchid != null" >
        batchid = #{batchid,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderType != null" >
        order_type = #{orderType,jdbcType=NVARCHAR},
      </if>
      <if test="orderSource != null" >
        order_source = #{orderSource,jdbcType=NVARCHAR},
      </if>
      <if test="orderStatus != null" >
        order_status = #{orderStatus,jdbcType=NVARCHAR},
      </if>
      <if test="orderPartnerid != null" >
        order_partnerid = #{orderPartnerid,jdbcType=BIGINT},
      </if>
      <if test="orderPartnername != null" >
        order_partnername = #{orderPartnername,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null" >
        deldate = #{deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingdt != null" >
        pricingdt = #{pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="billNo != null" >
        bill_no = #{billNo,jdbcType=NVARCHAR},
      </if>
      <if test="recordType != null" >
        record_type = #{recordType,jdbcType=NVARCHAR},
      </if>
      <if test="legsysid != null" >
        legsysid = #{legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="extbolnr != null" >
        extbolnr = #{extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="receiveUser != null" >
        receive_user = #{receiveUser,jdbcType=NVARCHAR},
      </if>
      <if test="receivePhone != null" >
        receive_phone = #{receivePhone,jdbcType=NVARCHAR},
      </if>
      <if test="receiveAddr != null" >
        receive_addr = #{receiveAddr,jdbcType=NVARCHAR},
      </if>
      <if test="receiveWorkshop != null" >
        receive_workshop = #{receiveWorkshop,jdbcType=NVARCHAR},
      </if>
      <if test="totalPrice != null" >
        total_price = #{totalPrice,jdbcType=NVARCHAR},
      </if>
      <if test="delivery != null" >
        delivery = #{delivery,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=NVARCHAR},
      </if>
      <if test="clientRemark != null" >
        client_remark = #{clientRemark,jdbcType=NVARCHAR},
      </if>
      <if test="customServiceRemark != null" >
        custom_service_remark = #{customServiceRemark,jdbcType=NVARCHAR},
      </if>
    </set>
    where orderid = #{orderid,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.haorder.model.HaOrderVo" >
    update wx_t_haorder
    set batchid = #{batchid,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=NVARCHAR},
      order_type = #{orderType,jdbcType=NVARCHAR},
      order_source = #{orderSource,jdbcType=NVARCHAR},
      order_status = #{orderStatus,jdbcType=NVARCHAR},
      order_partnerid = #{orderPartnerid,jdbcType=BIGINT},
      order_partnername = #{orderPartnername,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      deldate = #{deldate,jdbcType=TIMESTAMP},
      pricingdt = #{pricingdt,jdbcType=TIMESTAMP},
      bill_no = #{billNo,jdbcType=NVARCHAR},
      record_type = #{recordType,jdbcType=NVARCHAR},
      legsysid = #{legsysid,jdbcType=NVARCHAR},
      extbolnr = #{extbolnr,jdbcType=NVARCHAR},
      receive_user = #{receiveUser,jdbcType=NVARCHAR},
      receive_phone = #{receivePhone,jdbcType=NVARCHAR},
      receive_addr = #{receiveAddr,jdbcType=NVARCHAR},
      receive_workshop = #{receiveWorkshop,jdbcType=NVARCHAR},
      total_price = #{totalPrice,jdbcType=NVARCHAR},
      delivery = #{delivery,jdbcType=NVARCHAR},
      category = #{category,jdbcType=NVARCHAR},
      client_remark = #{clientRemark,jdbcType=NVARCHAR},
      custom_service_remark = #{customServiceRemark,jdbcType=NVARCHAR}
    where orderid = #{orderid,jdbcType=BIGINT}
  </update>
  
  
  
  
   <insert id="insertHaOrderBatch" parameterType="java.util.List">
    insert into wx_t_haorder (batchid, order_no, 
      order_type, order_source, order_status, 
      order_partnerid, order_partnername, create_time, 
      trade_time, deldate, pricingdt, 
      bill_no, record_type, legsysid, 
      extbolnr, receive_user, receive_phone, 
      receive_addr, receive_workshop, total_price, 
      delivery, category, client_remark, 
      custom_service_remark)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                 #{item.batchid,jdbcType=BIGINT}, #{item.orderNo,jdbcType=NVARCHAR}, 
                 #{item.orderType,jdbcType=NVARCHAR}, #{item.orderSource,jdbcType=NVARCHAR}, #{item.orderStatus,jdbcType=NVARCHAR}, 
                 #{item.orderPartnerid,jdbcType=BIGINT}, #{item.orderPartnername,jdbcType=NVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
                 #{item.tradeTime,jdbcType=TIMESTAMP}, #{item.deldate,jdbcType=TIMESTAMP}, #{item.pricingdt,jdbcType=TIMESTAMP}, 
                 #{item.billNo,jdbcType=NVARCHAR}, #{item.recordType,jdbcType=NVARCHAR}, #{item.legsysid,jdbcType=NVARCHAR}, 
                 #{item.extbolnr,jdbcType=NVARCHAR}, #{item.receiveUser,jdbcType=NVARCHAR}, #{item.receivePhone,jdbcType=NVARCHAR}, 
                 #{item.receiveAddr,jdbcType=NVARCHAR}, #{item.receiveWorkshop,jdbcType=NVARCHAR}, #{item.totalPrice,jdbcType=NVARCHAR}, 
                 #{item.delivery,jdbcType=NVARCHAR}, #{item.category,jdbcType=NVARCHAR}, #{item.clientRemark,jdbcType=NVARCHAR}, 
                 #{item.customServiceRemark,jdbcType=NVARCHAR}
            </trim>
        </foreach>
    </insert>
    
    
    
    
    <select id="getHaOrdersByCondition" parameterType="com.chevron.haorder.model.HaOrderConditions" resultMap="BaseResultMap">
    select v.* from
    (
       SELECT tt_order.*,t_order_batch.approve_status,tt_order.order_partnerid partner_id 
       FROM wx_t_haorder tt_order 
	       LEFT JOIN wx_t_haorder_application_batch t_order_batch
	       ON tt_order.batchid = t_order_batch.batchid
	   WHERE 
          <if test="orderStatus!=null and orderStatus!=''">
               (t_order_batch.approve_status = #{orderStatus,jdbcType=NVARCHAR}) 
          and
          </if>
           <if test="orderBatchId!=null">
               (tt_order.batchid = #{orderBatchId}) 
          and
          </if>
          <!-- 关键字搜索 -->
          <if test="queryField!=null and queryField != ''">
              (
               tt_order.order_partnername   like  '%' + #{queryField} +'%'
          or   tt_order.order_no    like    '%' + #{queryField} + '%'
          or   tt_order.batchid    like    '%' + #{queryField} + '%'
               )
          and
          </if>
          
          <!-- 高级搜索 -->
          <if test="queryField==null">
              <if test="orderCreateTime!=null and orderCreateTime != ''" >
                  <![CDATA[tt_order.create_time>= #{orderCreateTime} AND]]>
              </if>
              <if test="orderCreateEndTime!=null and orderCreateEndTime != ''" >
                  <![CDATA[tt_order.create_time<#{orderCreateEndTime} AND]]>
              </if>
              <if test="orderSource!=null" >
                  tt_order.order_source = #{orderSource,jdbcType=NVARCHAR}  
              and
              </if>
              <if test="orderCode!=null" >
                  tt_order.order_no = #{orderCode,jdbcType=NVARCHAR}  
              and
              </if>
          </if>
          1=1
     ) v
  </select>
  
    
</mapper>