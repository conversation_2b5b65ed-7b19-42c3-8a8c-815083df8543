<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.haorder.dao.HaOrderUploadVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.haorder.model.HaOrderUploadVo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="batchid" property="batchid" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP" />
    <result column="deldate" property="deldate" jdbcType="TIMESTAMP" />
    <result column="pricingdt" property="pricingdt" jdbcType="TIMESTAMP" />
    <result column="record_type" property="recordType" jdbcType="NVARCHAR" />
    <result column="legsysid" property="legsysid" jdbcType="NVARCHAR" />
    <result column="extbolnr" property="extbolnr" jdbcType="NVARCHAR" />
    <result column="delivery" property="delivery" jdbcType="NVARCHAR" />
    <result column="warehouse_name" property="warehouseName" jdbcType="NVARCHAR" />
    <result column="category" property="category" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, batchid, order_no, create_time, trade_time, deldate, pricingdt, record_type, 
    legsysid, extbolnr, delivery, warehouse_name, category, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.haorder.model.HaOrderUploadVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_haorder_upload
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_haorder_upload
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_haorder_upload
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.haorder.model.HaOrderUploadVoExample" >
    delete from wx_t_haorder_upload
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.haorder.model.HaOrderUploadVo" >
    insert into wx_t_haorder_upload (id, batchid, order_no, 
      create_time, trade_time, deldate, 
      pricingdt, record_type, legsysid, 
      extbolnr, delivery, warehouse_name, 
      category, remark)
    values (#{id,jdbcType=BIGINT}, #{batchid,jdbcType=BIGINT}, #{orderNo,jdbcType=NVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{tradeTime,jdbcType=TIMESTAMP}, #{deldate,jdbcType=TIMESTAMP}, 
      #{pricingdt,jdbcType=TIMESTAMP}, #{recordType,jdbcType=NVARCHAR}, #{legsysid,jdbcType=NVARCHAR}, 
      #{extbolnr,jdbcType=NVARCHAR}, #{delivery,jdbcType=NVARCHAR}, #{warehouseName,jdbcType=NVARCHAR}, 
      #{category,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.haorder.model.HaOrderUploadVo" >
    insert into wx_t_haorder_upload
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="batchid != null" >
        batchid,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="tradeTime != null" >
        trade_time,
      </if>
      <if test="deldate != null" >
        deldate,
      </if>
      <if test="pricingdt != null" >
        pricingdt,
      </if>
      <if test="recordType != null" >
        record_type,
      </if>
      <if test="legsysid != null" >
        legsysid,
      </if>
      <if test="extbolnr != null" >
        extbolnr,
      </if>
      <if test="delivery != null" >
        delivery,
      </if>
      <if test="warehouseName != null" >
        warehouse_name,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchid != null" >
        #{batchid,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null" >
        #{deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingdt != null" >
        #{pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="recordType != null" >
        #{recordType,jdbcType=NVARCHAR},
      </if>
      <if test="legsysid != null" >
        #{legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="extbolnr != null" >
        #{extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="delivery != null" >
        #{delivery,jdbcType=NVARCHAR},
      </if>
      <if test="warehouseName != null" >
        #{warehouseName,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_haorder_upload
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchid != null" >
        batchid = #{record.batchid,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null" >
        order_no = #{record.orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeTime != null" >
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deldate != null" >
        deldate = #{record.deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pricingdt != null" >
        pricingdt = #{record.pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordType != null" >
        record_type = #{record.recordType,jdbcType=NVARCHAR},
      </if>
      <if test="record.legsysid != null" >
        legsysid = #{record.legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="record.extbolnr != null" >
        extbolnr = #{record.extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="record.delivery != null" >
        delivery = #{record.delivery,jdbcType=NVARCHAR},
      </if>
      <if test="record.warehouseName != null" >
        warehouse_name = #{record.warehouseName,jdbcType=NVARCHAR},
      </if>
      <if test="record.category != null" >
        category = #{record.category,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_haorder_upload
    set id = #{record.id,jdbcType=BIGINT},
      batchid = #{record.batchid,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      deldate = #{record.deldate,jdbcType=TIMESTAMP},
      pricingdt = #{record.pricingdt,jdbcType=TIMESTAMP},
      record_type = #{record.recordType,jdbcType=NVARCHAR},
      legsysid = #{record.legsysid,jdbcType=NVARCHAR},
      extbolnr = #{record.extbolnr,jdbcType=NVARCHAR},
      delivery = #{record.delivery,jdbcType=NVARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=NVARCHAR},
      category = #{record.category,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.haorder.model.HaOrderUploadVo" >
    update wx_t_haorder_upload
    <set >
      <if test="batchid != null" >
        batchid = #{batchid,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null" >
        deldate = #{deldate,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingdt != null" >
        pricingdt = #{pricingdt,jdbcType=TIMESTAMP},
      </if>
      <if test="recordType != null" >
        record_type = #{recordType,jdbcType=NVARCHAR},
      </if>
      <if test="legsysid != null" >
        legsysid = #{legsysid,jdbcType=NVARCHAR},
      </if>
      <if test="extbolnr != null" >
        extbolnr = #{extbolnr,jdbcType=NVARCHAR},
      </if>
      <if test="delivery != null" >
        delivery = #{delivery,jdbcType=NVARCHAR},
      </if>
      <if test="warehouseName != null" >
        warehouse_name = #{warehouseName,jdbcType=NVARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.haorder.model.HaOrderUploadVo" >
    update wx_t_haorder_upload
    set batchid = #{batchid,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      deldate = #{deldate,jdbcType=TIMESTAMP},
      pricingdt = #{pricingdt,jdbcType=TIMESTAMP},
      record_type = #{recordType,jdbcType=NVARCHAR},
      legsysid = #{legsysid,jdbcType=NVARCHAR},
      extbolnr = #{extbolnr,jdbcType=NVARCHAR},
      delivery = #{delivery,jdbcType=NVARCHAR},
      warehouse_name = #{warehouseName,jdbcType=NVARCHAR},
      category = #{category,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
  
   <insert id="insertHaOrderUploadBatch" parameterType="java.util.List">
    insert into wx_t_haorder_upload (batchid, order_no, 
      create_time, trade_time, deldate, 
      pricingdt, record_type, legsysid, 
      extbolnr, delivery, warehouse_name, 
      category, remark)
    values 
        <foreach collection="list" index="index" item="item"
            separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                  #{item.batchid,jdbcType=BIGINT}, #{item.orderNo,jdbcType=NVARCHAR}, 
			      #{item.createTime,jdbcType=TIMESTAMP}, #{item.tradeTime,jdbcType=TIMESTAMP}, #{item.deldate,jdbcType=TIMESTAMP}, 
			      #{item.pricingdt,jdbcType=TIMESTAMP}, #{item.recordType,jdbcType=NVARCHAR}, #{item.legsysid,jdbcType=NVARCHAR}, 
			      #{item.extbolnr,jdbcType=NVARCHAR}, #{item.delivery,jdbcType=NVARCHAR}, #{item.warehouseName,jdbcType=NVARCHAR}, 
			      #{item.category,jdbcType=NVARCHAR}, #{item.remark,jdbcType=NVARCHAR}
            </trim>
        </foreach>
    </insert>
    
   <resultMap id="BaseResultViewMap" type="com.chevron.haorder.model.HaOrderUploadView" >
    <result column="batchid" property="batchid" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="NVARCHAR" />
    <result column="create_time" property="createDate" jdbcType="TIMESTAMP" />
    <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP" />
    <result column="snd_time" property="sndTime" jdbcType="TIMESTAMP" />
    <result column="record_type" property="recordtype" jdbcType="NVARCHAR" />
    <result column="legsysid" property="legsysid" jdbcType="NVARCHAR" />
    <result column="extbolnr" property="extbolnr" jdbcType="NVARCHAR" />
    <result column="warehouse_code" property="warehouseCode" jdbcType="NVARCHAR" />
    <result column="warehouse_soldto" property="warehouseSoldto" jdbcType="NVARCHAR" />
    <result column="warehouse_shipto" property="warehouseShipto" jdbcType="NVARCHAR" />
    <result column="itemrecordid" property="itemRecordId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="NVARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="item_record_type" property="itemRecordType" jdbcType="NVARCHAR" />
    <result column="sales_uom" property="salesuom" jdbcType="NVARCHAR" />
  </resultMap>
    
    
    
   <select id="getNeedUploadHaOrders" resultMap="BaseResultViewMap" parameterType="map">
    SELECT distinct t_h_u.batchid,t_h_u.order_no,t_h_u.create_time,
            t_h_u.trade_time trade_time,
            t_h_u.deldate snd_time,
            t_h_u.record_type,t_h_u.legsysid,t_h_u.extbolnr,
            t_w.warehouse_code,t_w.warehouse_soldto,t_w.warehouse_shipto,
            t_h_u_l.id itemrecordid, t_h_u_l.sku,t_h_u_l.amount,t_h_u_l.record_type item_record_type,t_h_u_l.sales_uom
    FROM wx_t_haorder_upload t_h_u
    INNER JOIN wx_t_warehouse_info t_w
        ON
        t_w.warehouse_alias_name=t_h_u.warehouse_name
        <if test="warehouseType !=null ">
        AND t_w.type=#{warehouseType}
        </if>
    LEFT JOIN wx_t_haorder_upload_line t_h_u_l
        ON 
        t_h_u_l.upload_order_no = t_h_u.order_no
    INNER JOIN wx_t_haorder_line t_h_l
        ON  t_h_l.sku = t_h_u_l.sku
        AND t_h_l.batchid = t_h_u_l.batchid
        AND t_h_l.unitprice!='0.00'
    WHERE t_h_u.batchid = #{batchId}
    ORDER BY t_h_u.order_no
   </select>
</mapper>