<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.credit.dao.WxTCreditLimitInfoVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.credit.model.WxTCreditLimitInfoVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_form_id" jdbcType="BIGINT" property="requestFormId" />
    <result column="request_no" jdbcType="NVARCHAR" property="requestNo" />
    <result column="credit_limit" jdbcType="NUMERIC" property="creditLimit" />
    <result column="credit_payment_term" jdbcType="INTEGER" property="creditPaymentTerm" />
    <result column="credit_limit_type" jdbcType="VARCHAR" property="creditLimitType" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="effective_date" jdbcType="TIMESTAMP" property="effectiveDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_form_id, request_no, credit_limit, credit_payment_term, credit_limit_type, 
    begin_time, effective_date, create_time, update_time, delete_flag
  </sql>
  <select id="selectByExample" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_credit_limit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_credit_limit_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_credit_limit_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVoExample">
    delete from wx_t_credit_limit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVo">
    insert into wx_t_credit_limit_info (id, request_form_id, request_no, 
      credit_limit, credit_payment_term, credit_limit_type, 
      begin_time, effective_date, create_time, 
      update_time, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{requestFormId,jdbcType=BIGINT}, #{requestNo,jdbcType=NVARCHAR}, 
      #{creditLimit,jdbcType=NUMERIC}, #{creditPaymentTerm,jdbcType=INTEGER}, #{creditLimitType,jdbcType=VARCHAR}, 
      #{beginTime,jdbcType=TIMESTAMP}, #{effectiveDate,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVo">
    insert into wx_t_credit_limit_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="requestFormId != null">
        request_form_id,
      </if>
      <if test="requestNo != null">
        request_no,
      </if>
      <if test="creditLimit != null">
        credit_limit,
      </if>
      <if test="creditPaymentTerm != null">
        credit_payment_term,
      </if>
      <if test="creditLimitType != null">
        credit_limit_type,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="requestFormId != null">
        #{requestFormId,jdbcType=BIGINT},
      </if>
      <if test="requestNo != null">
        #{requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="creditLimit != null">
        #{creditLimit,jdbcType=NUMERIC},
      </if>
      <if test="creditPaymentTerm != null">
        #{creditPaymentTerm,jdbcType=INTEGER},
      </if>
      <if test="creditLimitType != null">
        #{creditLimitType,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_credit_limit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_credit_limit_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestFormId != null">
        request_form_id = #{record.requestFormId,jdbcType=BIGINT},
      </if>
      <if test="record.requestNo != null">
        request_no = #{record.requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.creditLimit != null">
        credit_limit = #{record.creditLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.creditPaymentTerm != null">
        credit_payment_term = #{record.creditPaymentTerm,jdbcType=INTEGER},
      </if>
      <if test="record.creditLimitType != null">
        credit_limit_type = #{record.creditLimitType,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_credit_limit_info
    set id = #{record.id,jdbcType=BIGINT},
      request_form_id = #{record.requestFormId,jdbcType=BIGINT},
      request_no = #{record.requestNo,jdbcType=NVARCHAR},
      credit_limit = #{record.creditLimit,jdbcType=NUMERIC},
      credit_payment_term = #{record.creditPaymentTerm,jdbcType=INTEGER},
      credit_limit_type = #{record.creditLimitType,jdbcType=VARCHAR},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{record.deleteFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVo">
    update wx_t_credit_limit_info
    <set>
      <if test="requestFormId != null">
        request_form_id = #{requestFormId,jdbcType=BIGINT},
      </if>
      <if test="requestNo != null">
        request_no = #{requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="creditLimit != null">
        credit_limit = #{creditLimit,jdbcType=NUMERIC},
      </if>
      <if test="creditPaymentTerm != null">
        credit_payment_term = #{creditPaymentTerm,jdbcType=INTEGER},
      </if>
      <if test="creditLimitType != null">
        credit_limit_type = #{creditLimitType,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.credit.model.WxTCreditLimitInfoVo">
    update wx_t_credit_limit_info
    set request_form_id = #{requestFormId,jdbcType=BIGINT},
      request_no = #{requestNo,jdbcType=NVARCHAR},
      credit_limit = #{creditLimit,jdbcType=NUMERIC},
      credit_payment_term = #{creditPaymentTerm,jdbcType=INTEGER},
      credit_limit_type = #{creditLimitType,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>