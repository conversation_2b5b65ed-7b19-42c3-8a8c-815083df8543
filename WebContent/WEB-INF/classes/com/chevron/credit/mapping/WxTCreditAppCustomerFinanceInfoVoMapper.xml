<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.credit.dao.WxTCreditAppCustomerFinanceInfoVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cfi_year_n1_payment_record" jdbcType="NVARCHAR" property="cfiYearN1PaymentRecord" />
    <result column="cfi_pay_history_with_chevron" jdbcType="NUMERIC" property="cfiPayHistoryWithChevron" />
    <result column="cfi_dso_in_chevron_china" jdbcType="NUMERIC" property="cfiDsoInChevronChina" />
    <result column="cfi_quick_ratio" jdbcType="NUMERIC" property="cfiQuickRatio" />
    <result column="cfi_current_ratio" jdbcType="NUMERIC" property="cfiCurrentRatio" />
    <result column="cfi_daily_sales" jdbcType="NUMERIC" property="cfiDailySales" />
    <result column="cfi_net_working_capital_cycle" jdbcType="NUMERIC" property="cfiNetWorkingCapitalCycle" />
    <result column="cfi_cash_flow_coverage" jdbcType="NUMERIC" property="cfiCashFlowCoverage" />
    <result column="cfi_tangible_net_worth_ratio_g32" jdbcType="NUMERIC" property="cfiTangibleNetWorthRatioG32" />
    <result column="cfi_ap_days" jdbcType="NUMERIC" property="cfiApDays" />
    <result column="cfi_tangible_net_worth" jdbcType="NUMERIC" property="cfiTangibleNetWorth" />
    <result column="cfi_current_liability_to_equity" jdbcType="NUMERIC" property="cfiCurrentLiabilityToEquity" />
    <result column="cfi_long_term_liability_total_assets_ratio" jdbcType="NUMERIC" property="cfiLongTermLiabilityTotalAssetsRatio" />
    <result column="cfi_liablities_assets" jdbcType="NUMERIC" property="cfiLiablitiesAssets" />
    <result column="cfi_equity_ratio" jdbcType="DECIMAL" property="cfiEquityRatio" />
    <result column="cfi_inventory_turnover" jdbcType="DECIMAL" property="cfiInventoryTurnover" />
    <result column="cfi_days_in_inventory" jdbcType="DECIMAL" property="cfiDaysInInventory" />
    <result column="cfi_account_receivable_trunover" jdbcType="DECIMAL" property="cfiAccountReceivableTrunover" />
    <result column="cfi_days_in_accounts_receivable" jdbcType="DECIMAL" property="cfiDaysInAccountsReceivable" />
    <result column="cfi_sale_current_assets" jdbcType="DECIMAL" property="cfiSaleCurrentAssets" />
    <result column="cfi_asset_turnover" jdbcType="DECIMAL" property="cfiAssetTurnover" />
    <result column="cfi_profit_margin" jdbcType="DECIMAL" property="cfiProfitMargin" />
    <result column="cfi_after_tax_profit_ratio" jdbcType="DECIMAL" property="cfiAfterTaxProfitRatio" />
    <result column="cfi_return_on_equity" jdbcType="DECIMAL" property="cfiReturnOnEquity" />
    <result column="cfi_asset_turnover_net_sales_to_total_assets" jdbcType="DECIMAL" property="cfiAssetTurnoverNetSalesToTotalAssets" />
    <result column="cfi_working_capital" jdbcType="DECIMAL" property="cfiWorkingCapital" />
    <result column="cfi_equity" jdbcType="DECIMAL" property="cfiEquity" />
    <result column="cfi_working_assets" jdbcType="DECIMAL" property="cfiWorkingAssets" />
    <result column="cfi_estimated_value" jdbcType="DECIMAL" property="cfiEstimatedValue" />
    <result column="cfi_credit_index" jdbcType="DECIMAL" property="cfiCreditIndex" />
    <result column="cfi_credit_limit_estimated_value" jdbcType="DECIMAL" property="cfiCreditLimitEstimatedValue" />
    <result column="cfi_calculated_credit_limit_per_credit_policy" jdbcType="DECIMAL" property="cfiCalculatedCreditLimitPerCreditPolicy" />
    <result column="cfi_current_exposure" jdbcType="DECIMAL" property="cfiCurrentExposure" />
    <result column="cfi_cv_amount" jdbcType="DECIMAL" property="cfiCvAmount" />
    <result column="cfi_screenshot_of_current_exposure_att_id" jdbcType="BIGINT" property="cfiScreenshotOfCurrentExposureAttId" />
    <result column="others_att_id" jdbcType="BIGINT" property="othersAttId" />
    <result column="cfi_rec_credit_limit_of_current_year" jdbcType="NUMERIC" property="cfiRecCreditLimitOfCurrentYear" />
    <result column="cfi_rec_credit_payment_term" jdbcType="NVARCHAR" property="cfiRecCreditPaymentTerm" />
    <result column="cfi_rec_add_temp_credit_limit" jdbcType="NUMERIC" property="cfiRecAddTempCreditLimit" />
    <result column="cfi_rec_temp_payment_term" jdbcType="NVARCHAR" property="cfiRecTempPaymentTerm" />
    <result column="cfi_total_score" jdbcType="NUMERIC" property="cfiTotalScore" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cfi_comments_from_credit" jdbcType="NVARCHAR" property="cfiCommentsFromCredit" />
    <result column="cfi_confirmed_credit_limit_of_current_year" jdbcType="NUMERIC" property="cfiConfirmedCreditLimitOfCurrentYear" />
    <result column="cfi_confirmed_payment_term_of_current_year" jdbcType="NVARCHAR" property="cfiConfirmedPaymentTermOfCurrentYear" />
    <result column="cfi_confirmed_temp_credit_limit" jdbcType="NUMERIC" property="cfiConfirmedTempCreditLimit" />
    <result column="cfi_confirmed_temp_payment_term" jdbcType="NVARCHAR" property="cfiConfirmedTempPaymentTerm" />
    <result column="cfi_confirmed_expired_date" jdbcType="TIMESTAMP" property="cfiConfirmedExpiredDate" />
    <result column="cfi_upload_art_att_id" jdbcType="BIGINT" property="cfiUploadArtAttId" />
    <result column="cfi_upload_investigation_report_att_id" jdbcType="BIGINT" property="cfiUploadInvestigationReportAttId" />
		<result column="form_id" property="formId" jdbcType="BIGINT"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cfi_year_n1_payment_record, cfi_pay_history_with_chevron, cfi_dso_in_chevron_china, 
    cfi_quick_ratio, cfi_current_ratio, cfi_daily_sales, cfi_net_working_capital_cycle, 
    cfi_cash_flow_coverage, cfi_tangible_net_worth_ratio_g32, cfi_ap_days, cfi_tangible_net_worth, 
    cfi_current_liability_to_equity, cfi_long_term_liability_total_assets_ratio, cfi_liablities_assets, 
    cfi_equity_ratio, cfi_inventory_turnover, cfi_days_in_inventory, cfi_account_receivable_trunover, 
    cfi_days_in_accounts_receivable, cfi_sale_current_assets, cfi_asset_turnover, cfi_profit_margin, 
    cfi_after_tax_profit_ratio, cfi_return_on_equity, cfi_asset_turnover_net_sales_to_total_assets, 
    cfi_working_capital, cfi_equity, cfi_working_assets, cfi_estimated_value, cfi_credit_index, 
    cfi_credit_limit_estimated_value, cfi_calculated_credit_limit_per_credit_policy, 
    cfi_current_exposure, cfi_cv_amount, cfi_screenshot_of_current_exposure_att_id, others_att_id, 
    cfi_rec_credit_limit_of_current_year, cfi_rec_credit_payment_term, cfi_rec_add_temp_credit_limit, 
    cfi_rec_temp_payment_term, cfi_total_score, create_time, update_time, cfi_comments_from_credit, 
    cfi_confirmed_credit_limit_of_current_year, cfi_confirmed_payment_term_of_current_year, 
    cfi_confirmed_temp_credit_limit, cfi_confirmed_temp_payment_term, cfi_confirmed_expired_date, 
    cfi_upload_art_att_id, cfi_upload_investigation_report_att_id,form_id
  </sql>
  <select id="selectByExample" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_credit_app_customer_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_credit_app_customer_finance_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_credit_app_customer_finance_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVoExample">
    delete from wx_t_credit_app_customer_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    insert into wx_t_credit_app_customer_finance_info (id, cfi_year_n1_payment_record, cfi_pay_history_with_chevron, 
      cfi_dso_in_chevron_china, cfi_quick_ratio, cfi_current_ratio, 
      cfi_daily_sales, cfi_net_working_capital_cycle, 
      cfi_cash_flow_coverage, cfi_tangible_net_worth_ratio_g32, 
      cfi_ap_days, cfi_tangible_net_worth, cfi_current_liability_to_equity, 
      cfi_long_term_liability_total_assets_ratio, cfi_liablities_assets, 
      cfi_equity_ratio, cfi_inventory_turnover, cfi_days_in_inventory, 
      cfi_account_receivable_trunover, cfi_days_in_accounts_receivable, 
      cfi_sale_current_assets, cfi_asset_turnover, 
      cfi_profit_margin, cfi_after_tax_profit_ratio, 
      cfi_return_on_equity, cfi_asset_turnover_net_sales_to_total_assets, 
      cfi_working_capital, cfi_equity, cfi_working_assets, 
      cfi_estimated_value, cfi_credit_index, cfi_credit_limit_estimated_value, 
      cfi_calculated_credit_limit_per_credit_policy, cfi_current_exposure, 
      cfi_cv_amount, cfi_screenshot_of_current_exposure_att_id, 
      others_att_id, cfi_rec_credit_limit_of_current_year, 
      cfi_rec_credit_payment_term, cfi_rec_add_temp_credit_limit, 
      cfi_rec_temp_payment_term, cfi_total_score, 
      create_time, update_time, cfi_comments_from_credit, 
      cfi_confirmed_credit_limit_of_current_year, cfi_confirmed_payment_term_of_current_year, 
      cfi_confirmed_temp_credit_limit, cfi_confirmed_temp_payment_term, 
      cfi_confirmed_expired_date, cfi_upload_art_att_id, 
      cfi_upload_investigation_report_att_id)
    values (#{id,jdbcType=BIGINT}, #{cfiYearN1PaymentRecord,jdbcType=NVARCHAR}, #{cfiPayHistoryWithChevron,jdbcType=NUMERIC}, 
      #{cfiDsoInChevronChina,jdbcType=NUMERIC}, #{cfiQuickRatio,jdbcType=NUMERIC}, #{cfiCurrentRatio,jdbcType=NUMERIC}, 
      #{cfiDailySales,jdbcType=NUMERIC}, #{cfiNetWorkingCapitalCycle,jdbcType=NUMERIC}, 
      #{cfiCashFlowCoverage,jdbcType=NUMERIC}, #{cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC}, 
      #{cfiApDays,jdbcType=NUMERIC}, #{cfiTangibleNetWorth,jdbcType=NUMERIC}, #{cfiCurrentLiabilityToEquity,jdbcType=NUMERIC}, 
      #{cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC}, #{cfiLiablitiesAssets,jdbcType=NUMERIC}, 
      #{cfiEquityRatio,jdbcType=DECIMAL}, #{cfiInventoryTurnover,jdbcType=DECIMAL}, #{cfiDaysInInventory,jdbcType=DECIMAL}, 
      #{cfiAccountReceivableTrunover,jdbcType=DECIMAL}, #{cfiDaysInAccountsReceivable,jdbcType=DECIMAL}, 
      #{cfiSaleCurrentAssets,jdbcType=DECIMAL}, #{cfiAssetTurnover,jdbcType=DECIMAL}, 
      #{cfiProfitMargin,jdbcType=DECIMAL}, #{cfiAfterTaxProfitRatio,jdbcType=DECIMAL}, 
      #{cfiReturnOnEquity,jdbcType=DECIMAL}, #{cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL}, 
      #{cfiWorkingCapital,jdbcType=DECIMAL}, #{cfiEquity,jdbcType=DECIMAL}, #{cfiWorkingAssets,jdbcType=DECIMAL}, 
      #{cfiEstimatedValue,jdbcType=DECIMAL}, #{cfiCreditIndex,jdbcType=DECIMAL}, #{cfiCreditLimitEstimatedValue,jdbcType=DECIMAL}, 
      #{cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL}, #{cfiCurrentExposure,jdbcType=DECIMAL}, 
      #{cfiCvAmount,jdbcType=DECIMAL}, #{cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT}, 
      #{othersAttId,jdbcType=BIGINT}, #{cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC}, 
      #{cfiRecCreditPaymentTerm,jdbcType=NVARCHAR}, #{cfiRecAddTempCreditLimit,jdbcType=NUMERIC}, 
      #{cfiRecTempPaymentTerm,jdbcType=NVARCHAR}, #{cfiTotalScore,jdbcType=NUMERIC}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{cfiCommentsFromCredit,jdbcType=NVARCHAR}, 
      #{cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC}, #{cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR}, 
      #{cfiConfirmedTempCreditLimit,jdbcType=NUMERIC}, #{cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR}, 
      #{cfiConfirmedExpiredDate,jdbcType=TIMESTAMP}, #{cfiUploadArtAttId,jdbcType=BIGINT}, 
      #{cfiUploadInvestigationReportAttId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective"  useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    insert into wx_t_credit_app_customer_finance_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cfiYearN1PaymentRecord != null">
        cfi_year_n1_payment_record,
      </if>
      <if test="cfiPayHistoryWithChevron != null">
        cfi_pay_history_with_chevron,
      </if>
      <if test="cfiDsoInChevronChina != null">
        cfi_dso_in_chevron_china,
      </if>
      <if test="cfiQuickRatio != null">
        cfi_quick_ratio,
      </if>
      <if test="cfiCurrentRatio != null">
        cfi_current_ratio,
      </if>
      <if test="cfiDailySales != null">
        cfi_daily_sales,
      </if>
      <if test="cfiNetWorkingCapitalCycle != null">
        cfi_net_working_capital_cycle,
      </if>
      <if test="cfiCashFlowCoverage != null">
        cfi_cash_flow_coverage,
      </if>
      <if test="cfiTangibleNetWorthRatioG32 != null">
        cfi_tangible_net_worth_ratio_g32,
      </if>
      <if test="cfiApDays != null">
        cfi_ap_days,
      </if>
      <if test="cfiTangibleNetWorth != null">
        cfi_tangible_net_worth,
      </if>
      <if test="cfiCurrentLiabilityToEquity != null">
        cfi_current_liability_to_equity,
      </if>
      <if test="cfiLongTermLiabilityTotalAssetsRatio != null">
        cfi_long_term_liability_total_assets_ratio,
      </if>
      <if test="cfiLiablitiesAssets != null">
        cfi_liablities_assets,
      </if>
      <if test="cfiEquityRatio != null">
        cfi_equity_ratio,
      </if>
      <if test="cfiInventoryTurnover != null">
        cfi_inventory_turnover,
      </if>
      <if test="cfiDaysInInventory != null">
        cfi_days_in_inventory,
      </if>
      <if test="cfiAccountReceivableTrunover != null">
        cfi_account_receivable_trunover,
      </if>
      <if test="cfiDaysInAccountsReceivable != null">
        cfi_days_in_accounts_receivable,
      </if>
      <if test="cfiSaleCurrentAssets != null">
        cfi_sale_current_assets,
      </if>
      <if test="cfiAssetTurnover != null">
        cfi_asset_turnover,
      </if>
      <if test="cfiProfitMargin != null">
        cfi_profit_margin,
      </if>
      <if test="cfiAfterTaxProfitRatio != null">
        cfi_after_tax_profit_ratio,
      </if>
      <if test="cfiReturnOnEquity != null">
        cfi_return_on_equity,
      </if>
      <if test="cfiAssetTurnoverNetSalesToTotalAssets != null">
        cfi_asset_turnover_net_sales_to_total_assets,
      </if>
      <if test="cfiWorkingCapital != null">
        cfi_working_capital,
      </if>
      <if test="cfiEquity != null">
        cfi_equity,
      </if>
      <if test="cfiWorkingAssets != null">
        cfi_working_assets,
      </if>
      <if test="cfiEstimatedValue != null">
        cfi_estimated_value,
      </if>
      <if test="cfiCreditIndex != null">
        cfi_credit_index,
      </if>
      <if test="cfiCreditLimitEstimatedValue != null">
        cfi_credit_limit_estimated_value,
      </if>
      <if test="cfiCalculatedCreditLimitPerCreditPolicy != null">
        cfi_calculated_credit_limit_per_credit_policy,
      </if>
      <if test="cfiCurrentExposure != null">
        cfi_current_exposure,
      </if>
      <if test="cfiCvAmount != null">
        cfi_cv_amount,
      </if>
      <if test="cfiScreenshotOfCurrentExposureAttId != null">
        cfi_screenshot_of_current_exposure_att_id,
      </if>
      <if test="othersAttId != null">
        others_att_id,
      </if>
      <if test="cfiRecCreditLimitOfCurrentYear != null">
        cfi_rec_credit_limit_of_current_year,
      </if>
      <if test="cfiRecCreditPaymentTerm != null">
        cfi_rec_credit_payment_term,
      </if>
      <if test="cfiRecAddTempCreditLimit != null">
        cfi_rec_add_temp_credit_limit,
      </if>
      <if test="cfiRecTempPaymentTerm != null">
        cfi_rec_temp_payment_term,
      </if>
      <if test="cfiTotalScore != null">
        cfi_total_score,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="cfiCommentsFromCredit != null">
        cfi_comments_from_credit,
      </if>
      <if test="cfiConfirmedCreditLimitOfCurrentYear != null">
        cfi_confirmed_credit_limit_of_current_year,
      </if>
      <if test="cfiConfirmedPaymentTermOfCurrentYear != null">
        cfi_confirmed_payment_term_of_current_year,
      </if>
      <if test="cfiConfirmedTempCreditLimit != null">
        cfi_confirmed_temp_credit_limit,
      </if>
      <if test="cfiConfirmedTempPaymentTerm != null">
        cfi_confirmed_temp_payment_term,
      </if>
      <if test="cfiConfirmedExpiredDate != null">
        cfi_confirmed_expired_date,
      </if>
      <if test="cfiUploadArtAttId != null">
        cfi_upload_art_att_id,
      </if>
      <if test="cfiUploadInvestigationReportAttId != null">
        cfi_upload_investigation_report_att_id,
      </if>
			<if test="formId != null">
				form_id,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cfiYearN1PaymentRecord != null">
        #{cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      </if>
      <if test="cfiPayHistoryWithChevron != null">
        #{cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      </if>
      <if test="cfiDsoInChevronChina != null">
        #{cfiDsoInChevronChina,jdbcType=NUMERIC},
      </if>
      <if test="cfiQuickRatio != null">
        #{cfiQuickRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentRatio != null">
        #{cfiCurrentRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiDailySales != null">
        #{cfiDailySales,jdbcType=NUMERIC},
      </if>
      <if test="cfiNetWorkingCapitalCycle != null">
        #{cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      </if>
      <if test="cfiCashFlowCoverage != null">
        #{cfiCashFlowCoverage,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorthRatioG32 != null">
        #{cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      </if>
      <if test="cfiApDays != null">
        #{cfiApDays,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorth != null">
        #{cfiTangibleNetWorth,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentLiabilityToEquity != null">
        #{cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      </if>
      <if test="cfiLongTermLiabilityTotalAssetsRatio != null">
        #{cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiLiablitiesAssets != null">
        #{cfiLiablitiesAssets,jdbcType=NUMERIC},
      </if>
      <if test="cfiEquityRatio != null">
        #{cfiEquityRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiInventoryTurnover != null">
        #{cfiInventoryTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInInventory != null">
        #{cfiDaysInInventory,jdbcType=DECIMAL},
      </if>
      <if test="cfiAccountReceivableTrunover != null">
        #{cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInAccountsReceivable != null">
        #{cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      </if>
      <if test="cfiSaleCurrentAssets != null">
        #{cfiSaleCurrentAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnover != null">
        #{cfiAssetTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiProfitMargin != null">
        #{cfiProfitMargin,jdbcType=DECIMAL},
      </if>
      <if test="cfiAfterTaxProfitRatio != null">
        #{cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiReturnOnEquity != null">
        #{cfiReturnOnEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnoverNetSalesToTotalAssets != null">
        #{cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingCapital != null">
        #{cfiWorkingCapital,jdbcType=DECIMAL},
      </if>
      <if test="cfiEquity != null">
        #{cfiEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingAssets != null">
        #{cfiWorkingAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiEstimatedValue != null">
        #{cfiEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditIndex != null">
        #{cfiCreditIndex,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditLimitEstimatedValue != null">
        #{cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCalculatedCreditLimitPerCreditPolicy != null">
        #{cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      </if>
      <if test="cfiCurrentExposure != null">
        #{cfiCurrentExposure,jdbcType=DECIMAL},
      </if>
      <if test="cfiCvAmount != null">
        #{cfiCvAmount,jdbcType=DECIMAL},
      </if>
      <if test="cfiScreenshotOfCurrentExposureAttId != null">
        #{cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      </if>
      <if test="othersAttId != null">
        #{othersAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiRecCreditLimitOfCurrentYear != null">
        #{cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecCreditPaymentTerm != null">
        #{cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiRecAddTempCreditLimit != null">
        #{cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecTempPaymentTerm != null">
        #{cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiTotalScore != null">
        #{cfiTotalScore,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiCommentsFromCredit != null">
        #{cfiCommentsFromCredit,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedCreditLimitOfCurrentYear != null">
        #{cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedPaymentTermOfCurrentYear != null">
        #{cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedTempCreditLimit != null">
        #{cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedTempPaymentTerm != null">
        #{cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedExpiredDate != null">
        #{cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiUploadArtAttId != null">
        #{cfiUploadArtAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiUploadInvestigationReportAttId != null">
        #{cfiUploadInvestigationReportAttId,jdbcType=BIGINT},
      </if>
			<if test="formId != null">
				#{formId,jdbcType=BIGINT},
			</if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_credit_app_customer_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_credit_app_customer_finance_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cfiYearN1PaymentRecord != null">
        cfi_year_n1_payment_record = #{record.cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiPayHistoryWithChevron != null">
        cfi_pay_history_with_chevron = #{record.cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiDsoInChevronChina != null">
        cfi_dso_in_chevron_china = #{record.cfiDsoInChevronChina,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiQuickRatio != null">
        cfi_quick_ratio = #{record.cfiQuickRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiCurrentRatio != null">
        cfi_current_ratio = #{record.cfiCurrentRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiDailySales != null">
        cfi_daily_sales = #{record.cfiDailySales,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiNetWorkingCapitalCycle != null">
        cfi_net_working_capital_cycle = #{record.cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiCashFlowCoverage != null">
        cfi_cash_flow_coverage = #{record.cfiCashFlowCoverage,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiTangibleNetWorthRatioG32 != null">
        cfi_tangible_net_worth_ratio_g32 = #{record.cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiApDays != null">
        cfi_ap_days = #{record.cfiApDays,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiTangibleNetWorth != null">
        cfi_tangible_net_worth = #{record.cfiTangibleNetWorth,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiCurrentLiabilityToEquity != null">
        cfi_current_liability_to_equity = #{record.cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiLongTermLiabilityTotalAssetsRatio != null">
        cfi_long_term_liability_total_assets_ratio = #{record.cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiLiablitiesAssets != null">
        cfi_liablities_assets = #{record.cfiLiablitiesAssets,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiEquityRatio != null">
        cfi_equity_ratio = #{record.cfiEquityRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiInventoryTurnover != null">
        cfi_inventory_turnover = #{record.cfiInventoryTurnover,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiDaysInInventory != null">
        cfi_days_in_inventory = #{record.cfiDaysInInventory,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiAccountReceivableTrunover != null">
        cfi_account_receivable_trunover = #{record.cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiDaysInAccountsReceivable != null">
        cfi_days_in_accounts_receivable = #{record.cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiSaleCurrentAssets != null">
        cfi_sale_current_assets = #{record.cfiSaleCurrentAssets,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiAssetTurnover != null">
        cfi_asset_turnover = #{record.cfiAssetTurnover,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiProfitMargin != null">
        cfi_profit_margin = #{record.cfiProfitMargin,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiAfterTaxProfitRatio != null">
        cfi_after_tax_profit_ratio = #{record.cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiReturnOnEquity != null">
        cfi_return_on_equity = #{record.cfiReturnOnEquity,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiAssetTurnoverNetSalesToTotalAssets != null">
        cfi_asset_turnover_net_sales_to_total_assets = #{record.cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiWorkingCapital != null">
        cfi_working_capital = #{record.cfiWorkingCapital,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiEquity != null">
        cfi_equity = #{record.cfiEquity,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiWorkingAssets != null">
        cfi_working_assets = #{record.cfiWorkingAssets,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiEstimatedValue != null">
        cfi_estimated_value = #{record.cfiEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiCreditIndex != null">
        cfi_credit_index = #{record.cfiCreditIndex,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiCreditLimitEstimatedValue != null">
        cfi_credit_limit_estimated_value = #{record.cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiCalculatedCreditLimitPerCreditPolicy != null">
        cfi_calculated_credit_limit_per_credit_policy = #{record.cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiCurrentExposure != null">
        cfi_current_exposure = #{record.cfiCurrentExposure,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiCvAmount != null">
        cfi_cv_amount = #{record.cfiCvAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.cfiScreenshotOfCurrentExposureAttId != null">
        cfi_screenshot_of_current_exposure_att_id = #{record.cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      </if>
      <if test="record.othersAttId != null">
        others_att_id = #{record.othersAttId,jdbcType=BIGINT},
      </if>
      <if test="record.cfiRecCreditLimitOfCurrentYear != null">
        cfi_rec_credit_limit_of_current_year = #{record.cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiRecCreditPaymentTerm != null">
        cfi_rec_credit_payment_term = #{record.cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiRecAddTempCreditLimit != null">
        cfi_rec_add_temp_credit_limit = #{record.cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiRecTempPaymentTerm != null">
        cfi_rec_temp_payment_term = #{record.cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiTotalScore != null">
        cfi_total_score = #{record.cfiTotalScore,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cfiCommentsFromCredit != null">
        cfi_comments_from_credit = #{record.cfiCommentsFromCredit,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiConfirmedCreditLimitOfCurrentYear != null">
        cfi_confirmed_credit_limit_of_current_year = #{record.cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiConfirmedPaymentTermOfCurrentYear != null">
        cfi_confirmed_payment_term_of_current_year = #{record.cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiConfirmedTempCreditLimit != null">
        cfi_confirmed_temp_credit_limit = #{record.cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.cfiConfirmedTempPaymentTerm != null">
        cfi_confirmed_temp_payment_term = #{record.cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="record.cfiConfirmedExpiredDate != null">
        cfi_confirmed_expired_date = #{record.cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cfiUploadArtAttId != null">
        cfi_upload_art_att_id = #{record.cfiUploadArtAttId,jdbcType=BIGINT},
      </if>
      <if test="record.cfiUploadInvestigationReportAttId != null">
        cfi_upload_investigation_report_att_id = #{record.cfiUploadInvestigationReportAttId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_credit_app_customer_finance_info
    set id = #{record.id,jdbcType=BIGINT},
      cfi_year_n1_payment_record = #{record.cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      cfi_pay_history_with_chevron = #{record.cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      cfi_dso_in_chevron_china = #{record.cfiDsoInChevronChina,jdbcType=NUMERIC},
      cfi_quick_ratio = #{record.cfiQuickRatio,jdbcType=NUMERIC},
      cfi_current_ratio = #{record.cfiCurrentRatio,jdbcType=NUMERIC},
      cfi_daily_sales = #{record.cfiDailySales,jdbcType=NUMERIC},
      cfi_net_working_capital_cycle = #{record.cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      cfi_cash_flow_coverage = #{record.cfiCashFlowCoverage,jdbcType=NUMERIC},
      cfi_tangible_net_worth_ratio_g32 = #{record.cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      cfi_ap_days = #{record.cfiApDays,jdbcType=NUMERIC},
      cfi_tangible_net_worth = #{record.cfiTangibleNetWorth,jdbcType=NUMERIC},
      cfi_current_liability_to_equity = #{record.cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      cfi_long_term_liability_total_assets_ratio = #{record.cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      cfi_liablities_assets = #{record.cfiLiablitiesAssets,jdbcType=NUMERIC},
      cfi_equity_ratio = #{record.cfiEquityRatio,jdbcType=DECIMAL},
      cfi_inventory_turnover = #{record.cfiInventoryTurnover,jdbcType=DECIMAL},
      cfi_days_in_inventory = #{record.cfiDaysInInventory,jdbcType=DECIMAL},
      cfi_account_receivable_trunover = #{record.cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      cfi_days_in_accounts_receivable = #{record.cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      cfi_sale_current_assets = #{record.cfiSaleCurrentAssets,jdbcType=DECIMAL},
      cfi_asset_turnover = #{record.cfiAssetTurnover,jdbcType=DECIMAL},
      cfi_profit_margin = #{record.cfiProfitMargin,jdbcType=DECIMAL},
      cfi_after_tax_profit_ratio = #{record.cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      cfi_return_on_equity = #{record.cfiReturnOnEquity,jdbcType=DECIMAL},
      cfi_asset_turnover_net_sales_to_total_assets = #{record.cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      cfi_working_capital = #{record.cfiWorkingCapital,jdbcType=DECIMAL},
      cfi_equity = #{record.cfiEquity,jdbcType=DECIMAL},
      cfi_working_assets = #{record.cfiWorkingAssets,jdbcType=DECIMAL},
      cfi_estimated_value = #{record.cfiEstimatedValue,jdbcType=DECIMAL},
      cfi_credit_index = #{record.cfiCreditIndex,jdbcType=DECIMAL},
      cfi_credit_limit_estimated_value = #{record.cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      cfi_calculated_credit_limit_per_credit_policy = #{record.cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      cfi_current_exposure = #{record.cfiCurrentExposure,jdbcType=DECIMAL},
      cfi_cv_amount = #{record.cfiCvAmount,jdbcType=DECIMAL},
      cfi_screenshot_of_current_exposure_att_id = #{record.cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      others_att_id = #{record.othersAttId,jdbcType=BIGINT},
      cfi_rec_credit_limit_of_current_year = #{record.cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      cfi_rec_credit_payment_term = #{record.cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      cfi_rec_add_temp_credit_limit = #{record.cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      cfi_rec_temp_payment_term = #{record.cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      cfi_total_score = #{record.cfiTotalScore,jdbcType=NUMERIC},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      cfi_comments_from_credit = #{record.cfiCommentsFromCredit,jdbcType=NVARCHAR},
      cfi_confirmed_credit_limit_of_current_year = #{record.cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      cfi_confirmed_payment_term_of_current_year = #{record.cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      cfi_confirmed_temp_credit_limit = #{record.cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      cfi_confirmed_temp_payment_term = #{record.cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      cfi_confirmed_expired_date = #{record.cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      cfi_upload_art_att_id = #{record.cfiUploadArtAttId,jdbcType=BIGINT},
      cfi_upload_investigation_report_att_id = #{record.cfiUploadInvestigationReportAttId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    update wx_t_credit_app_customer_finance_info
    <set>
      <if test="cfiYearN1PaymentRecord != null">
        cfi_year_n1_payment_record = #{cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      </if>
      <if test="cfiPayHistoryWithChevron != null">
        cfi_pay_history_with_chevron = #{cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      </if>
      <if test="cfiDsoInChevronChina != null">
        cfi_dso_in_chevron_china = #{cfiDsoInChevronChina,jdbcType=NUMERIC},
      </if>
      <if test="cfiQuickRatio != null">
        cfi_quick_ratio = #{cfiQuickRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentRatio != null">
        cfi_current_ratio = #{cfiCurrentRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiDailySales != null">
        cfi_daily_sales = #{cfiDailySales,jdbcType=NUMERIC},
      </if>
      <if test="cfiNetWorkingCapitalCycle != null">
        cfi_net_working_capital_cycle = #{cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      </if>
      <if test="cfiCashFlowCoverage != null">
        cfi_cash_flow_coverage = #{cfiCashFlowCoverage,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorthRatioG32 != null">
        cfi_tangible_net_worth_ratio_g32 = #{cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      </if>
      <if test="cfiApDays != null">
        cfi_ap_days = #{cfiApDays,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorth != null">
        cfi_tangible_net_worth = #{cfiTangibleNetWorth,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentLiabilityToEquity != null">
        cfi_current_liability_to_equity = #{cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      </if>
      <if test="cfiLongTermLiabilityTotalAssetsRatio != null">
        cfi_long_term_liability_total_assets_ratio = #{cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiLiablitiesAssets != null">
        cfi_liablities_assets = #{cfiLiablitiesAssets,jdbcType=NUMERIC},
      </if>
      <if test="cfiEquityRatio != null">
        cfi_equity_ratio = #{cfiEquityRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiInventoryTurnover != null">
        cfi_inventory_turnover = #{cfiInventoryTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInInventory != null">
        cfi_days_in_inventory = #{cfiDaysInInventory,jdbcType=DECIMAL},
      </if>
      <if test="cfiAccountReceivableTrunover != null">
        cfi_account_receivable_trunover = #{cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInAccountsReceivable != null">
        cfi_days_in_accounts_receivable = #{cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      </if>
      <if test="cfiSaleCurrentAssets != null">
        cfi_sale_current_assets = #{cfiSaleCurrentAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnover != null">
        cfi_asset_turnover = #{cfiAssetTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiProfitMargin != null">
        cfi_profit_margin = #{cfiProfitMargin,jdbcType=DECIMAL},
      </if>
      <if test="cfiAfterTaxProfitRatio != null">
        cfi_after_tax_profit_ratio = #{cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiReturnOnEquity != null">
        cfi_return_on_equity = #{cfiReturnOnEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnoverNetSalesToTotalAssets != null">
        cfi_asset_turnover_net_sales_to_total_assets = #{cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingCapital != null">
        cfi_working_capital = #{cfiWorkingCapital,jdbcType=DECIMAL},
      </if>
      <if test="cfiEquity != null">
        cfi_equity = #{cfiEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingAssets != null">
        cfi_working_assets = #{cfiWorkingAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiEstimatedValue != null">
        cfi_estimated_value = #{cfiEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditIndex != null">
        cfi_credit_index = #{cfiCreditIndex,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditLimitEstimatedValue != null">
        cfi_credit_limit_estimated_value = #{cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCalculatedCreditLimitPerCreditPolicy != null">
        cfi_calculated_credit_limit_per_credit_policy = #{cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      </if>
      <if test="cfiCurrentExposure != null">
        cfi_current_exposure = #{cfiCurrentExposure,jdbcType=DECIMAL},
      </if>
      <if test="cfiCvAmount != null">
        cfi_cv_amount = #{cfiCvAmount,jdbcType=DECIMAL},
      </if>
      <if test="cfiScreenshotOfCurrentExposureAttId != null">
        cfi_screenshot_of_current_exposure_att_id = #{cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      </if>
      <if test="othersAttId != null">
        others_att_id = #{othersAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiRecCreditLimitOfCurrentYear != null">
        cfi_rec_credit_limit_of_current_year = #{cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecCreditPaymentTerm != null">
        cfi_rec_credit_payment_term = #{cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiRecAddTempCreditLimit != null">
        cfi_rec_add_temp_credit_limit = #{cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecTempPaymentTerm != null">
        cfi_rec_temp_payment_term = #{cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiTotalScore != null">
        cfi_total_score = #{cfiTotalScore,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiCommentsFromCredit != null">
        cfi_comments_from_credit = #{cfiCommentsFromCredit,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedCreditLimitOfCurrentYear != null">
        cfi_confirmed_credit_limit_of_current_year = #{cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedPaymentTermOfCurrentYear != null">
        cfi_confirmed_payment_term_of_current_year = #{cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedTempCreditLimit != null">
        cfi_confirmed_temp_credit_limit = #{cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedTempPaymentTerm != null">
        cfi_confirmed_temp_payment_term = #{cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedExpiredDate != null">
        cfi_confirmed_expired_date = #{cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiUploadArtAttId != null">
        cfi_upload_art_att_id = #{cfiUploadArtAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiUploadInvestigationReportAttId != null">
        cfi_upload_investigation_report_att_id = #{cfiUploadInvestigationReportAttId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByFormIdSelective" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    update wx_t_credit_app_customer_finance_info
    <set>
      <if test="cfiYearN1PaymentRecord != null">
        cfi_year_n1_payment_record = #{cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      </if>
      <if test="cfiPayHistoryWithChevron != null">
        cfi_pay_history_with_chevron = #{cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      </if>
      <if test="cfiDsoInChevronChina != null">
        cfi_dso_in_chevron_china = #{cfiDsoInChevronChina,jdbcType=NUMERIC},
      </if>
      <if test="cfiQuickRatio != null">
        cfi_quick_ratio = #{cfiQuickRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentRatio != null">
        cfi_current_ratio = #{cfiCurrentRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiDailySales != null">
        cfi_daily_sales = #{cfiDailySales,jdbcType=NUMERIC},
      </if>
      <if test="cfiNetWorkingCapitalCycle != null">
        cfi_net_working_capital_cycle = #{cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      </if>
      <if test="cfiCashFlowCoverage != null">
        cfi_cash_flow_coverage = #{cfiCashFlowCoverage,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorthRatioG32 != null">
        cfi_tangible_net_worth_ratio_g32 = #{cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      </if>
      <if test="cfiApDays != null">
        cfi_ap_days = #{cfiApDays,jdbcType=NUMERIC},
      </if>
      <if test="cfiTangibleNetWorth != null">
        cfi_tangible_net_worth = #{cfiTangibleNetWorth,jdbcType=NUMERIC},
      </if>
      <if test="cfiCurrentLiabilityToEquity != null">
        cfi_current_liability_to_equity = #{cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      </if>
      <if test="cfiLongTermLiabilityTotalAssetsRatio != null">
        cfi_long_term_liability_total_assets_ratio = #{cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      </if>
      <if test="cfiLiablitiesAssets != null">
        cfi_liablities_assets = #{cfiLiablitiesAssets,jdbcType=NUMERIC},
      </if>
      <if test="cfiEquityRatio != null">
        cfi_equity_ratio = #{cfiEquityRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiInventoryTurnover != null">
        cfi_inventory_turnover = #{cfiInventoryTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInInventory != null">
        cfi_days_in_inventory = #{cfiDaysInInventory,jdbcType=DECIMAL},
      </if>
      <if test="cfiAccountReceivableTrunover != null">
        cfi_account_receivable_trunover = #{cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      </if>
      <if test="cfiDaysInAccountsReceivable != null">
        cfi_days_in_accounts_receivable = #{cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      </if>
      <if test="cfiSaleCurrentAssets != null">
        cfi_sale_current_assets = #{cfiSaleCurrentAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnover != null">
        cfi_asset_turnover = #{cfiAssetTurnover,jdbcType=DECIMAL},
      </if>
      <if test="cfiProfitMargin != null">
        cfi_profit_margin = #{cfiProfitMargin,jdbcType=DECIMAL},
      </if>
      <if test="cfiAfterTaxProfitRatio != null">
        cfi_after_tax_profit_ratio = #{cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="cfiReturnOnEquity != null">
        cfi_return_on_equity = #{cfiReturnOnEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiAssetTurnoverNetSalesToTotalAssets != null">
        cfi_asset_turnover_net_sales_to_total_assets = #{cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingCapital != null">
        cfi_working_capital = #{cfiWorkingCapital,jdbcType=DECIMAL},
      </if>
      <if test="cfiEquity != null">
        cfi_equity = #{cfiEquity,jdbcType=DECIMAL},
      </if>
      <if test="cfiWorkingAssets != null">
        cfi_working_assets = #{cfiWorkingAssets,jdbcType=DECIMAL},
      </if>
      <if test="cfiEstimatedValue != null">
        cfi_estimated_value = #{cfiEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditIndex != null">
        cfi_credit_index = #{cfiCreditIndex,jdbcType=DECIMAL},
      </if>
      <if test="cfiCreditLimitEstimatedValue != null">
        cfi_credit_limit_estimated_value = #{cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      </if>
      <if test="cfiCalculatedCreditLimitPerCreditPolicy != null">
        cfi_calculated_credit_limit_per_credit_policy = #{cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      </if>
      <if test="cfiCurrentExposure != null">
        cfi_current_exposure = #{cfiCurrentExposure,jdbcType=DECIMAL},
      </if>
      <if test="cfiCvAmount != null">
        cfi_cv_amount = #{cfiCvAmount,jdbcType=DECIMAL},
      </if>
      <if test="cfiScreenshotOfCurrentExposureAttId != null">
        cfi_screenshot_of_current_exposure_att_id = #{cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      </if>
      <if test="othersAttId != null">
        others_att_id = #{othersAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiRecCreditLimitOfCurrentYear != null">
        cfi_rec_credit_limit_of_current_year = #{cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecCreditPaymentTerm != null">
        cfi_rec_credit_payment_term = #{cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiRecAddTempCreditLimit != null">
        cfi_rec_add_temp_credit_limit = #{cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiRecTempPaymentTerm != null">
        cfi_rec_temp_payment_term = #{cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiTotalScore != null">
        cfi_total_score = #{cfiTotalScore,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiCommentsFromCredit != null">
        cfi_comments_from_credit = #{cfiCommentsFromCredit,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedCreditLimitOfCurrentYear != null">
        cfi_confirmed_credit_limit_of_current_year = #{cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedPaymentTermOfCurrentYear != null">
        cfi_confirmed_payment_term_of_current_year = #{cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedTempCreditLimit != null">
        cfi_confirmed_temp_credit_limit = #{cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cfiConfirmedTempPaymentTerm != null">
        cfi_confirmed_temp_payment_term = #{cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cfiConfirmedExpiredDate != null">
        cfi_confirmed_expired_date = #{cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiUploadArtAttId != null">
        cfi_upload_art_att_id = #{cfiUploadArtAttId,jdbcType=BIGINT},
      </if>
      <if test="cfiUploadInvestigationReportAttId != null">
        cfi_upload_investigation_report_att_id = #{cfiUploadInvestigationReportAttId,jdbcType=BIGINT},
      </if>
    </set>
    where form_id = #{formId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.credit.model.WxTCreditAppCustomerFinanceInfoVo">
    update wx_t_credit_app_customer_finance_info
    set cfi_year_n1_payment_record = #{cfiYearN1PaymentRecord,jdbcType=NVARCHAR},
      cfi_pay_history_with_chevron = #{cfiPayHistoryWithChevron,jdbcType=NUMERIC},
      cfi_dso_in_chevron_china = #{cfiDsoInChevronChina,jdbcType=NUMERIC},
      cfi_quick_ratio = #{cfiQuickRatio,jdbcType=NUMERIC},
      cfi_current_ratio = #{cfiCurrentRatio,jdbcType=NUMERIC},
      cfi_daily_sales = #{cfiDailySales,jdbcType=NUMERIC},
      cfi_net_working_capital_cycle = #{cfiNetWorkingCapitalCycle,jdbcType=NUMERIC},
      cfi_cash_flow_coverage = #{cfiCashFlowCoverage,jdbcType=NUMERIC},
      cfi_tangible_net_worth_ratio_g32 = #{cfiTangibleNetWorthRatioG32,jdbcType=NUMERIC},
      cfi_ap_days = #{cfiApDays,jdbcType=NUMERIC},
      cfi_tangible_net_worth = #{cfiTangibleNetWorth,jdbcType=NUMERIC},
      cfi_current_liability_to_equity = #{cfiCurrentLiabilityToEquity,jdbcType=NUMERIC},
      cfi_long_term_liability_total_assets_ratio = #{cfiLongTermLiabilityTotalAssetsRatio,jdbcType=NUMERIC},
      cfi_liablities_assets = #{cfiLiablitiesAssets,jdbcType=NUMERIC},
      cfi_equity_ratio = #{cfiEquityRatio,jdbcType=DECIMAL},
      cfi_inventory_turnover = #{cfiInventoryTurnover,jdbcType=DECIMAL},
      cfi_days_in_inventory = #{cfiDaysInInventory,jdbcType=DECIMAL},
      cfi_account_receivable_trunover = #{cfiAccountReceivableTrunover,jdbcType=DECIMAL},
      cfi_days_in_accounts_receivable = #{cfiDaysInAccountsReceivable,jdbcType=DECIMAL},
      cfi_sale_current_assets = #{cfiSaleCurrentAssets,jdbcType=DECIMAL},
      cfi_asset_turnover = #{cfiAssetTurnover,jdbcType=DECIMAL},
      cfi_profit_margin = #{cfiProfitMargin,jdbcType=DECIMAL},
      cfi_after_tax_profit_ratio = #{cfiAfterTaxProfitRatio,jdbcType=DECIMAL},
      cfi_return_on_equity = #{cfiReturnOnEquity,jdbcType=DECIMAL},
      cfi_asset_turnover_net_sales_to_total_assets = #{cfiAssetTurnoverNetSalesToTotalAssets,jdbcType=DECIMAL},
      cfi_working_capital = #{cfiWorkingCapital,jdbcType=DECIMAL},
      cfi_equity = #{cfiEquity,jdbcType=DECIMAL},
      cfi_working_assets = #{cfiWorkingAssets,jdbcType=DECIMAL},
      cfi_estimated_value = #{cfiEstimatedValue,jdbcType=DECIMAL},
      cfi_credit_index = #{cfiCreditIndex,jdbcType=DECIMAL},
      cfi_credit_limit_estimated_value = #{cfiCreditLimitEstimatedValue,jdbcType=DECIMAL},
      cfi_calculated_credit_limit_per_credit_policy = #{cfiCalculatedCreditLimitPerCreditPolicy,jdbcType=DECIMAL},
      cfi_current_exposure = #{cfiCurrentExposure,jdbcType=DECIMAL},
      cfi_cv_amount = #{cfiCvAmount,jdbcType=DECIMAL},
      cfi_screenshot_of_current_exposure_att_id = #{cfiScreenshotOfCurrentExposureAttId,jdbcType=BIGINT},
      others_att_id = #{othersAttId,jdbcType=BIGINT},
      cfi_rec_credit_limit_of_current_year = #{cfiRecCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      cfi_rec_credit_payment_term = #{cfiRecCreditPaymentTerm,jdbcType=NVARCHAR},
      cfi_rec_add_temp_credit_limit = #{cfiRecAddTempCreditLimit,jdbcType=NUMERIC},
      cfi_rec_temp_payment_term = #{cfiRecTempPaymentTerm,jdbcType=NVARCHAR},
      cfi_total_score = #{cfiTotalScore,jdbcType=NUMERIC},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      cfi_comments_from_credit = #{cfiCommentsFromCredit,jdbcType=NVARCHAR},
      cfi_confirmed_credit_limit_of_current_year = #{cfiConfirmedCreditLimitOfCurrentYear,jdbcType=NUMERIC},
      cfi_confirmed_payment_term_of_current_year = #{cfiConfirmedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      cfi_confirmed_temp_credit_limit = #{cfiConfirmedTempCreditLimit,jdbcType=NUMERIC},
      cfi_confirmed_temp_payment_term = #{cfiConfirmedTempPaymentTerm,jdbcType=NVARCHAR},
      cfi_confirmed_expired_date = #{cfiConfirmedExpiredDate,jdbcType=TIMESTAMP},
      cfi_upload_art_att_id = #{cfiUploadArtAttId,jdbcType=BIGINT},
      cfi_upload_investigation_report_att_id = #{cfiUploadInvestigationReportAttId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>