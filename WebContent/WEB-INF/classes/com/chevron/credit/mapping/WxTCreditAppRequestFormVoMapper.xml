<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.credit.dao.WxTCreditAppRequestFormVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_instance_id" jdbcType="NVARCHAR" property="processInstanceId" />
    <result column="request_no" jdbcType="NVARCHAR" property="requestNo" />
    <result column="currency" jdbcType="NVARCHAR" property="currency" />
    <result column="ai_prepared_by" jdbcType="BIGINT" property="aiPreparedBy" />
    <result column="ai_region_id" jdbcType="BIGINT" property="aiRegionId" />
    <result column="ai_request_date" jdbcType="TIMESTAMP" property="aiRequestDate" />
    <result column="ai_requested_by" jdbcType="NVARCHAR" property="aiRequestedBy" />
    <result column="ai_telephone" jdbcType="NVARCHAR" property="aiTelephone" />
    <result column="ai_sales_team" jdbcType="NVARCHAR" property="aiSalesTeam" />
    <result column="cbi_customer_id" jdbcType="NVARCHAR" property="cbiCustomerId" />
    <result column="cbi_customer_name" jdbcType="NVARCHAR" property="cbiCustomerName" />
    <result column="cbi_province_id" jdbcType="BIGINT" property="cbiProvinceId" />
    <result column="cbi_cooperation_years_with_cvx" jdbcType="INTEGER" property="cbiCooperationYearsWithCvx" />
    <result column="cbi_year_n1_total_sales" jdbcType="NUMERIC" property="cbiYearN1TotalSales" />
    <result column="cbi_date_establishment" jdbcType="TIMESTAMP" property="cbiDateEstablishment" />
    <result column="cbi_sales_target_of_cio" jdbcType="NUMERIC" property="cbiSalesTargetOfCio" />
    <result column="cbi_sales_target_of_cdm" jdbcType="NUMERIC" property="cbiSalesTargetOfCdm" />
    <result column="cbi_credit_limit_of_year_N1" jdbcType="NUMERIC" property="cbiCreditLimitOfYearN1" />
    <result column="cbi_payment_term_of_year_N1" jdbcType="NVARCHAR" property="cbiPaymentTermOfYearN1" />
    <result column="cbi_requested_credit_limit_current_year" jdbcType="NUMERIC" property="cbiRequestedCreditLimitCurrentYear" />
    <result column="cbi_requested_payment_term_of_current_year" jdbcType="NVARCHAR" property="cbiRequestedPaymentTermOfCurrentYear" />
    <result column="cbi_current_credit_limit_info_id" jdbcType="BIGINT" property="cbiCurrentCreditLimitInfoId" />
    <result column="cbi_requested_temp_credit_limit" jdbcType="NUMERIC" property="cbiRequestedTempCreditLimit" />
    <result column="cbi_requested_temp_payment_term" jdbcType="NVARCHAR" property="cbiRequestedTempPaymentTerm" />
    <result column="cbi_expire_date" jdbcType="TIMESTAMP" property="cbiExpireDate" />
    <result column="cbi_requested_cv_order_no" jdbcType="NVARCHAR" property="cbiRequestedCvOrderNo" />
    <result column="cbi_comments_from_BU" jdbcType="NVARCHAR" property="cbiCommentsFromBu" />
    <result column="cbi_financial_statements_att_id" jdbcType="BIGINT" property="cbiFinancialStatementsAttId" />
    <result column="cbi_cash_deposit_with_amount" jdbcType="NUMERIC" property="cbiCashDepositWithAmount" />
    <result column="cbi_cash_deposit_with_amount_upload_scancopy_id" jdbcType="BIGINT" property="cbiCashDepositWithAmountUploadScancopyId" />
    <result column="cbi_cash_deposit_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiCashDepositWithAmountValidDate" />
    <result column="cbi_the_3rd_party_guarantee_with_amount" jdbcType="NUMERIC" property="cbiThe3rdPartyGuaranteeWithAmount" />
    <result column="cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id" jdbcType="BIGINT" property="cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId" />
    <result column="cbi_the_3rd_party_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiThe3rdPartyGuaranteeWithAmountValidDate" />
    <result column="cbi_bank_guarantee_with_amount" jdbcType="NUMERIC" property="cbiBankGuaranteeWithAmount" />
    <result column="cbi_bank_guarantee_with_amount_upload_scancopy_id" jdbcType="BIGINT" property="cbiBankGuaranteeWithAmountUploadScancopyId" />
    <result column="cbi_bank_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiBankGuaranteeWithAmountValidDate" />
    <result column="cbi_personal_guarantee_with_amount" jdbcType="NUMERIC" property="cbiPersonalGuaranteeWithAmount" />
    <result column="cbi_personal_guarantee_with_amount_upload_scancopy_id" jdbcType="BIGINT" property="cbiPersonalGuaranteeWithAmountUploadScancopyId" />
    <result column="cbi_personal_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiPersonalGuaranteeWithAmountValidDate" />
    <result column="cfi_info_id" jdbcType="BIGINT" property="cfiInfoId" />
    <result column="credit_type" jdbcType="NVARCHAR" property="creditType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="credit_dollar_rate" jdbcType="NUMERIC" property="creditDollarRate" />
    <!--(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,-->
    <result column="ai_prepared_by_name" jdbcType="NVARCHAR" property="aiPreparedByName" />
    <!--(select ch_name from dbo.wx_t_user where user_id = ai_requested_by) as ai_requested_by_name-->
    <result column="ai_requested_by_name" jdbcType="NVARCHAR" property="aiRequestedByName" />
    <result column="current_credit_limit" jdbcType="NUMERIC" property="currentCreditLimit" />
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="credit_csr" jdbcType="VARCHAR" property="cbiCreditCsr" />
    <result column="release_order_status" jdbcType="VARCHAR" property="cbiReleaseOrderStatus" />
		<result column="bu" property="bu" jdbcType="VARCHAR"/>
		<result column="workflow_status" property="workflowStatus" jdbcType="INTEGER"/>
		<result column="customer_type" property="customerType" jdbcType="VARCHAR"/>
		<result column="sold_to_code" property="soldToCode" jdbcType="VARCHAR"/>
		<result column="payer_code" property="payerCode" jdbcType="VARCHAR"/>
		<result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
		<result column="direct_annual_sales_plan" property="directAnnualSalesPlan" jdbcType="NUMERIC"/>
		<result column="indirect_annual_sales_plan" property="indirectAnnualSalesPlan" jdbcType="NUMERIC"/>
		<result column="ext_flag" property="extFlag" jdbcType="INTEGER"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="ext_property5" property="extProperty5" jdbcType="VARCHAR"/>
		<result column="flsr_id" property="flsrId" jdbcType="BIGINT"/>
		<result column="form_status" property="formStatus" jdbcType="INTEGER"/>
		<result column="version_no" property="formVersionNo" jdbcType="BIGINT"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<result column="locker_id" property="workflowLockerId" jdbcType="VARCHAR"/>
		<result column="workflow_status_text" property="workflowStatusText" jdbcType="VARCHAR"/>
		<result column="apply_amount_usd" property="applyAmountUsd" jdbcType="NUMERIC"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, process_instance_id, request_no, currency, ai_prepared_by, ai_region_id, ai_request_date, 
    ai_requested_by, ai_telephone, ai_sales_team, cbi_customer_id, cbi_province_id, cbi_cooperation_years_with_cvx, 
    cbi_year_n1_total_sales, cbi_date_establishment, cbi_sales_target_of_cio, cbi_sales_target_of_cdm, 
    cbi_credit_limit_of_year_N1, cbi_payment_term_of_year_N1, cbi_requested_credit_limit_current_year, 
    cbi_requested_payment_term_of_current_year, cbi_current_credit_limit_info_id, cbi_requested_temp_credit_limit, 
    cbi_requested_temp_payment_term, cbi_expire_date, cbi_requested_cv_order_no, cbi_comments_from_BU, 
    cbi_financial_statements_att_id, cbi_cash_deposit_with_amount, cbi_cash_deposit_with_amount_upload_scancopy_id, 
    cbi_cash_deposit_with_amount_valid_date, cbi_the_3rd_party_guarantee_with_amount, 
    cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id, cbi_the_3rd_party_guarantee_with_amount_valid_date, 
    cbi_bank_guarantee_with_amount, cbi_bank_guarantee_with_amount_upload_scancopy_id, 
    cbi_bank_guarantee_with_amount_valid_date, cbi_personal_guarantee_with_amount, cbi_personal_guarantee_with_amount_upload_scancopy_id, 
    cbi_personal_guarantee_with_amount_valid_date, cfi_info_id, credit_type, create_time, 
    update_time, delete_flag, credit_dollar_rate, current_credit_limit, process_status,
    credit_csr,release_order_status,process_instance_id,bu,workflow_status,customer_type,
		sold_to_code,payer_code,customer_name,direct_annual_sales_plan,indirect_annual_sales_plan,ext_flag,ext_property1,
		ext_property2,ext_property3,ext_property4,ext_property5,flsr_id,form_status,version_no,sales_channel,locker_id,apply_amount_usd
  </sql>
  <!--(select ch_name from dbo.wx_t_user where user_id = ai_requested_by) as ai_requested_by_name,-->
    <sql id="Extend_Column_List">
    (select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
    (select top 1 customer_name
      from dbo.wx_t_credit_customer_info_final
      where (payer  + '_' + type) = cbi_customer_id ) as cbi_customer_name
  </sql>
  <select id="selectByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_credit_app_request_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_credit_app_request_form
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_credit_app_request_form
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVoExample">
    delete from wx_t_credit_app_request_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    insert into wx_t_credit_app_request_form (id, process_instance_id, request_no, 
      currency, ai_prepared_by, ai_region_id, 
      ai_request_date, ai_requested_by, ai_telephone, 
      ai_sales_team, cbi_customer_id, cbi_province_id, 
      cbi_cooperation_years_with_cvx, cbi_year_n1_total_sales, 
      cbi_date_establishment, cbi_sales_target_of_cio, 
      cbi_sales_target_of_cdm, cbi_credit_limit_of_year_N1, 
      cbi_payment_term_of_year_N1, cbi_requested_credit_limit_current_year, 
      cbi_requested_payment_term_of_current_year, cbi_current_credit_limit_info_id, 
      cbi_requested_temp_credit_limit, cbi_requested_temp_payment_term, 
      cbi_expire_date, cbi_requested_cv_order_no, 
      cbi_comments_from_BU, cbi_financial_statements_att_id, 
      cbi_cash_deposit_with_amount, cbi_cash_deposit_with_amount_upload_scancopy_id, 
      cbi_cash_deposit_with_amount_valid_date, cbi_the_3rd_party_guarantee_with_amount, 
      cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id, cbi_the_3rd_party_guarantee_with_amount_valid_date, 
      cbi_bank_guarantee_with_amount, cbi_bank_guarantee_with_amount_upload_scancopy_id, 
      cbi_bank_guarantee_with_amount_valid_date, cbi_personal_guarantee_with_amount, 
      cbi_personal_guarantee_with_amount_upload_scancopy_id, cbi_personal_guarantee_with_amount_valid_date, 
      cfi_info_id, credit_type, create_time, 
      update_time, delete_flag, credit_dollar_rate, 
      current_credit_limit, process_status,
      credit_csr,release_order_status)
    values (#{id,jdbcType=BIGINT}, #{processInstanceId,jdbcType=NVARCHAR}, #{requestNo,jdbcType=NVARCHAR}, 
      #{currency,jdbcType=NVARCHAR}, #{aiPreparedBy,jdbcType=BIGINT}, #{aiRegionId,jdbcType=BIGINT}, 
      #{aiRequestDate,jdbcType=TIMESTAMP}, #{aiRequestedBy,jdbcType=NVARCHAR}, #{aiTelephone,jdbcType=NVARCHAR}, 
      #{aiSalesTeam,jdbcType=NVARCHAR}, #{cbiCustomerId,jdbcType=NVARCHAR}, #{cbiProvinceId,jdbcType=BIGINT}, 
      #{cbiCooperationYearsWithCvx,jdbcType=INTEGER}, #{cbiYearN1TotalSales,jdbcType=NUMERIC}, 
      #{cbiDateEstablishment,jdbcType=TIMESTAMP}, #{cbiSalesTargetOfCio,jdbcType=NUMERIC}, 
      #{cbiSalesTargetOfCdm,jdbcType=NUMERIC}, #{cbiCreditLimitOfYearN1,jdbcType=NUMERIC}, 
      #{cbiPaymentTermOfYearN1,jdbcType=NVARCHAR}, #{cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC}, 
      #{cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR}, #{cbiCurrentCreditLimitInfoId,jdbcType=BIGINT}, 
      #{cbiRequestedTempCreditLimit,jdbcType=NUMERIC}, #{cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR}, 
      #{cbiExpireDate,jdbcType=TIMESTAMP}, #{cbiRequestedCvOrderNo,jdbcType=NVARCHAR}, 
      #{cbiCommentsFromBu,jdbcType=NVARCHAR}, #{cbiFinancialStatementsAttId,jdbcType=BIGINT}, 
      #{cbiCashDepositWithAmount,jdbcType=NUMERIC}, #{cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT}, 
      #{cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP}, #{cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC}, 
      #{cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT}, #{cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP}, 
      #{cbiBankGuaranteeWithAmount,jdbcType=NUMERIC}, #{cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT}, 
      #{cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP}, #{cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC}, 
      #{cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT}, #{cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP}, 
      #{cfiInfoId,jdbcType=BIGINT}, #{creditType,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER}, #{creditDollarRate,jdbcType=NUMERIC}, 
      #{currentCreditLimit,jdbcType=NUMERIC}, #{processStatus,jdbcType=VARCHAR},
      #{cbiCreditCsr,jdbcType=VARCHAR},#{cbiReleaseOrderStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    insert into wx_t_credit_app_request_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="processInstanceId != null">
        process_instance_id,
      </if>
      <if test="requestNo != null">
        request_no,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="aiPreparedBy != null">
        ai_prepared_by,
      </if>
      <if test="aiRegionId != null">
        ai_region_id,
      </if>
      <if test="aiRequestDate != null">
        ai_request_date,
      </if>
      <if test="aiRequestedBy != null">
        ai_requested_by,
      </if>
      <if test="aiTelephone != null">
        ai_telephone,
      </if>
      <if test="aiSalesTeam != null">
        ai_sales_team,
      </if>
      <if test="cbiCustomerId != null">
        cbi_customer_id,
      </if>
      <if test="cbiProvinceId != null">
        cbi_province_id,
      </if>
      <if test="cbiCooperationYearsWithCvx != null">
        cbi_cooperation_years_with_cvx,
      </if>
      <if test="cbiYearN1TotalSales != null">
        cbi_year_n1_total_sales,
      </if>
      <if test="cbiDateEstablishment != null">
        cbi_date_establishment,
      </if>
      <if test="cbiSalesTargetOfCio != null">
        cbi_sales_target_of_cio,
      </if>
      <if test="cbiSalesTargetOfCdm != null">
        cbi_sales_target_of_cdm,
      </if>
      <if test="cbiCreditLimitOfYearN1 != null">
        cbi_credit_limit_of_year_N1,
      </if>
      <if test="cbiPaymentTermOfYearN1 != null">
        cbi_payment_term_of_year_N1,
      </if>
      <if test="cbiRequestedCreditLimitCurrentYear != null">
        cbi_requested_credit_limit_current_year,
      </if>
      <if test="cbiRequestedPaymentTermOfCurrentYear != null">
        cbi_requested_payment_term_of_current_year,
      </if>
      <if test="cbiCurrentCreditLimitInfoId != null">
        cbi_current_credit_limit_info_id,
      </if>
      <if test="cbiRequestedTempCreditLimit != null">
        cbi_requested_temp_credit_limit,
      </if>
      <if test="cbiRequestedTempPaymentTerm != null">
        cbi_requested_temp_payment_term,
      </if>
      <if test="cbiExpireDate != null">
        cbi_expire_date,
      </if>
      <if test="cbiRequestedCvOrderNo != null">
        cbi_requested_cv_order_no,
      </if>
      <if test="cbiCommentsFromBu != null">
        cbi_comments_from_BU,
      </if>
      <if test="cbiFinancialStatementsAttId != null">
        cbi_financial_statements_att_id,
      </if>
      <if test="cbiCashDepositWithAmount != null">
        cbi_cash_deposit_with_amount,
      </if>
      <if test="cbiCashDepositWithAmountUploadScancopyId != null">
        cbi_cash_deposit_with_amount_upload_scancopy_id,
      </if>
      <if test="cbiCashDepositWithAmountValidDate != null">
        cbi_cash_deposit_with_amount_valid_date,
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmount != null">
        cbi_the_3rd_party_guarantee_with_amount,
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId != null">
        cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id,
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountValidDate != null">
        cbi_the_3rd_party_guarantee_with_amount_valid_date,
      </if>
      <if test="cbiBankGuaranteeWithAmount != null">
        cbi_bank_guarantee_with_amount,
      </if>
      <if test="cbiBankGuaranteeWithAmountUploadScancopyId != null">
        cbi_bank_guarantee_with_amount_upload_scancopy_id,
      </if>
      <if test="cbiBankGuaranteeWithAmountValidDate != null">
        cbi_bank_guarantee_with_amount_valid_date,
      </if>
      <if test="cbiPersonalGuaranteeWithAmount != null">
        cbi_personal_guarantee_with_amount,
      </if>
      <if test="cbiPersonalGuaranteeWithAmountUploadScancopyId != null">
        cbi_personal_guarantee_with_amount_upload_scancopy_id,
      </if>
      <if test="cbiPersonalGuaranteeWithAmountValidDate != null">
        cbi_personal_guarantee_with_amount_valid_date,
      </if>
      <if test="cfiInfoId != null">
        cfi_info_id,
      </if>
      <if test="creditType != null">
        credit_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creditDollarRate != null">
        credit_dollar_rate,
      </if>
      <if test="currentCreditLimit != null">
        current_credit_limit,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="cbiCreditCsr != null">
        credit_csr,
      </if>
      <if test="cbiReleaseOrderStatus != null">
        release_order_status,
      </if>
			<if test="workflowStatus != null">
				workflow_status,
			</if>
			<if test="customerType != null">
				customer_type,
			</if>
			<if test="soldToCode != null">
				sold_to_code,
			</if>
			<if test="payerCode != null">
				payer_code,
			</if>
			<if test="customerName != null">
				customer_name,
			</if>
			<if test="directAnnualSalesPlan != null">
				direct_annual_sales_plan,
			</if>
			<if test="indirectAnnualSalesPlan != null">
				indirect_annual_sales_plan,
			</if>
			<if test="extFlag != null">
				ext_flag,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="extProperty5 != null">
				ext_property5,
			</if>
			<if test="flsrId != null">
				flsr_id,
			</if>
			<if test="bu != null">
				bu,
			</if>
			<if test="salesChannel != null">
				sales_channel,
			</if>
			<if test="formStatus != null">
				form_status,
			</if>
			<if test="formVersionNo != null">
				version_no,
			</if>
			<if test="workflowLockerId != null">
				locker_id,
			</if>
			<if test="applyAmountUsd != null">
				apply_amount_usd,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="processInstanceId != null">
        #{processInstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="requestNo != null">
        #{requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=NVARCHAR},
      </if>
      <if test="aiPreparedBy != null">
        #{aiPreparedBy,jdbcType=BIGINT},
      </if>
      <if test="aiRegionId != null">
        #{aiRegionId,jdbcType=BIGINT},
      </if>
      <if test="aiRequestDate != null">
        #{aiRequestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="aiRequestedBy != null">
        #{aiRequestedBy,jdbcType=NVARCHAR},
      </if>
      <if test="aiTelephone != null">
        #{aiTelephone,jdbcType=NVARCHAR},
      </if>
      <if test="aiSalesTeam != null">
        #{aiSalesTeam,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCustomerId != null">
        #{cbiCustomerId,jdbcType=NVARCHAR},
      </if>
      <if test="cbiProvinceId != null">
        #{cbiProvinceId,jdbcType=BIGINT},
      </if>
      <if test="cbiCooperationYearsWithCvx != null">
        #{cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      </if>
      <if test="cbiYearN1TotalSales != null">
        #{cbiYearN1TotalSales,jdbcType=NUMERIC},
      </if>
      <if test="cbiDateEstablishment != null">
        #{cbiDateEstablishment,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiSalesTargetOfCio != null">
        #{cbiSalesTargetOfCio,jdbcType=NUMERIC},
      </if>
      <if test="cbiSalesTargetOfCdm != null">
        #{cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      </if>
      <if test="cbiCreditLimitOfYearN1 != null">
        #{cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      </if>
      <if test="cbiPaymentTermOfYearN1 != null">
        #{cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      </if>
      <if test="cbiRequestedCreditLimitCurrentYear != null">
        #{cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedPaymentTermOfCurrentYear != null">
        #{cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCurrentCreditLimitInfoId != null">
        #{cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      </if>
      <if test="cbiRequestedTempCreditLimit != null">
        #{cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedTempPaymentTerm != null">
        #{cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cbiExpireDate != null">
        #{cbiExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiRequestedCvOrderNo != null">
        #{cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCommentsFromBu != null">
        #{cbiCommentsFromBu,jdbcType=NVARCHAR},
      </if>
      <if test="cbiFinancialStatementsAttId != null">
        #{cbiFinancialStatementsAttId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmount != null">
        #{cbiCashDepositWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiCashDepositWithAmountUploadScancopyId != null">
        #{cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmountValidDate != null">
        #{cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmount != null">
        #{cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId != null">
        #{cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountValidDate != null">
        #{cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiBankGuaranteeWithAmount != null">
        #{cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiBankGuaranteeWithAmountUploadScancopyId != null">
        #{cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiBankGuaranteeWithAmountValidDate != null">
        #{cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiPersonalGuaranteeWithAmount != null">
        #{cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountUploadScancopyId != null">
        #{cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountValidDate != null">
        #{cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiInfoId != null">
        #{cfiInfoId,jdbcType=BIGINT},
      </if>
      <if test="creditType != null">
        #{creditType,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="creditDollarRate != null">
        #{creditDollarRate,jdbcType=NUMERIC},
      </if>
      <if test="currentCreditLimit != null">
        #{currentCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="cbiCreditCsr != null">
        #{cbiCreditCsr,jdbcType=VARCHAR},
      </if>
      <if test="cbiReleaseOrderStatus != null">
        #{cbiReleaseOrderStatus,jdbcType=VARCHAR},
      </if>
			<if test="workflowStatus != null">
				#{workflowStatus,jdbcType=INTEGER},
			</if>
			<if test="customerType != null">
				#{customerType,jdbcType=VARCHAR},
			</if>
			<if test="soldToCode != null">
				#{soldToCode,jdbcType=VARCHAR},
			</if>
			<if test="payerCode != null">
				#{payerCode,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null">
				#{customerName,jdbcType=VARCHAR},
			</if>
			<if test="directAnnualSalesPlan != null">
				#{directAnnualSalesPlan,jdbcType=NUMERIC},
			</if>
			<if test="indirectAnnualSalesPlan != null">
				#{indirectAnnualSalesPlan,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null">
				#{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null">
				#{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="flsrId != null">
				#{flsrId,jdbcType=BIGINT},
			</if>
			<if test="bu != null">
				#{bu,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null">
				#{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null">
				#{formStatus,jdbcType=INTEGER},
			</if>
			<if test="formVersionNo != null">
				#{formVersionNo,jdbcType=BIGINT},
			</if>
			<if test="workflowLockerId != null">
				#{workflowLockerId,jdbcType=VARCHAR},
			</if>
			<if test="applyAmountUsd != null">
				#{applyAmountUsd,jdbcType=NUMERIC},
			</if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_credit_app_request_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_credit_app_request_form
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.processInstanceId != null">
        process_instance_id = #{record.processInstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="record.requestNo != null">
        request_no = #{record.requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=NVARCHAR},
      </if>
      <if test="record.aiPreparedBy != null">
        ai_prepared_by = #{record.aiPreparedBy,jdbcType=BIGINT},
      </if>
      <if test="record.aiRegionId != null">
        ai_region_id = #{record.aiRegionId,jdbcType=BIGINT},
      </if>
      <if test="record.aiRequestDate != null">
        ai_request_date = #{record.aiRequestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aiRequestedBy != null">
        ai_requested_by = #{record.aiRequestedBy,jdbcType=NVARCHAR},
      </if>
      <if test="record.aiTelephone != null">
        ai_telephone = #{record.aiTelephone,jdbcType=NVARCHAR},
      </if>
      <if test="record.aiSalesTeam != null">
        ai_sales_team = #{record.aiSalesTeam,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiCustomerId != null">
        cbi_customer_id = #{record.cbiCustomerId,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiProvinceId != null">
        cbi_province_id = #{record.cbiProvinceId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiCooperationYearsWithCvx != null">
        cbi_cooperation_years_with_cvx = #{record.cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      </if>
      <if test="record.cbiYearN1TotalSales != null">
        cbi_year_n1_total_sales = #{record.cbiYearN1TotalSales,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiDateEstablishment != null">
        cbi_date_establishment = #{record.cbiDateEstablishment,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cbiSalesTargetOfCio != null">
        cbi_sales_target_of_cio = #{record.cbiSalesTargetOfCio,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiSalesTargetOfCdm != null">
        cbi_sales_target_of_cdm = #{record.cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiCreditLimitOfYearN1 != null">
        cbi_credit_limit_of_year_N1 = #{record.cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiPaymentTermOfYearN1 != null">
        cbi_payment_term_of_year_N1 = #{record.cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiRequestedCreditLimitCurrentYear != null">
        cbi_requested_credit_limit_current_year = #{record.cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiRequestedPaymentTermOfCurrentYear != null">
        cbi_requested_payment_term_of_current_year = #{record.cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiCurrentCreditLimitInfoId != null">
        cbi_current_credit_limit_info_id = #{record.cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiRequestedTempCreditLimit != null">
        cbi_requested_temp_credit_limit = #{record.cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiRequestedTempPaymentTerm != null">
        cbi_requested_temp_payment_term = #{record.cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiExpireDate != null">
        cbi_expire_date = #{record.cbiExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cbiRequestedCvOrderNo != null">
        cbi_requested_cv_order_no = #{record.cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiCommentsFromBu != null">
        cbi_comments_from_BU = #{record.cbiCommentsFromBu,jdbcType=NVARCHAR},
      </if>
      <if test="record.cbiFinancialStatementsAttId != null">
        cbi_financial_statements_att_id = #{record.cbiFinancialStatementsAttId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiCashDepositWithAmount != null">
        cbi_cash_deposit_with_amount = #{record.cbiCashDepositWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiCashDepositWithAmountUploadScancopyId != null">
        cbi_cash_deposit_with_amount_upload_scancopy_id = #{record.cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiCashDepositWithAmountValidDate != null">
        cbi_cash_deposit_with_amount_valid_date = #{record.cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cbiThe3rdPartyGuaranteeWithAmount != null">
        cbi_the_3rd_party_guarantee_with_amount = #{record.cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId != null">
        cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id = #{record.cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiThe3rdPartyGuaranteeWithAmountValidDate != null">
        cbi_the_3rd_party_guarantee_with_amount_valid_date = #{record.cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cbiBankGuaranteeWithAmount != null">
        cbi_bank_guarantee_with_amount = #{record.cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiBankGuaranteeWithAmountUploadScancopyId != null">
        cbi_bank_guarantee_with_amount_upload_scancopy_id = #{record.cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiBankGuaranteeWithAmountValidDate != null">
        cbi_bank_guarantee_with_amount_valid_date = #{record.cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cbiPersonalGuaranteeWithAmount != null">
        cbi_personal_guarantee_with_amount = #{record.cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.cbiPersonalGuaranteeWithAmountUploadScancopyId != null">
        cbi_personal_guarantee_with_amount_upload_scancopy_id = #{record.cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="record.cbiPersonalGuaranteeWithAmountValidDate != null">
        cbi_personal_guarantee_with_amount_valid_date = #{record.cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cfiInfoId != null">
        cfi_info_id = #{record.cfiInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.creditType != null">
        credit_type = #{record.creditType,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="record.creditDollarRate != null">
        credit_dollar_rate = #{record.creditDollarRate,jdbcType=NUMERIC},
      </if>
      <if test="record.currentCreditLimit != null">
        current_credit_limit = #{record.currentCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.cbiCreditCsr != null">
        credit_csr = #{record.cbiCreditCsr,jdbcType=VARCHAR},
      </if>
      <if test="record.cbiReleaseOrderStatus != null">
        release_order_status = #{record.cbiReleaseOrderStatus,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_credit_app_request_form
    set id = #{record.id,jdbcType=BIGINT},
      process_instance_id = #{record.processInstanceId,jdbcType=NVARCHAR},
      request_no = #{record.requestNo,jdbcType=NVARCHAR},
      currency = #{record.currency,jdbcType=NVARCHAR},
      ai_prepared_by = #{record.aiPreparedBy,jdbcType=BIGINT},
      ai_region_id = #{record.aiRegionId,jdbcType=BIGINT},
      ai_request_date = #{record.aiRequestDate,jdbcType=TIMESTAMP},
      ai_requested_by = #{record.aiRequestedBy,jdbcType=NVARCHAR},
      ai_telephone = #{record.aiTelephone,jdbcType=NVARCHAR},
      ai_sales_team = #{record.aiSalesTeam,jdbcType=NVARCHAR},
      cbi_customer_id = #{record.cbiCustomerId,jdbcType=NVARCHAR},
      cbi_province_id = #{record.cbiProvinceId,jdbcType=BIGINT},
      cbi_cooperation_years_with_cvx = #{record.cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      cbi_year_n1_total_sales = #{record.cbiYearN1TotalSales,jdbcType=NUMERIC},
      cbi_date_establishment = #{record.cbiDateEstablishment,jdbcType=TIMESTAMP},
      cbi_sales_target_of_cio = #{record.cbiSalesTargetOfCio,jdbcType=NUMERIC},
      cbi_sales_target_of_cdm = #{record.cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      cbi_credit_limit_of_year_N1 = #{record.cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      cbi_payment_term_of_year_N1 = #{record.cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      cbi_requested_credit_limit_current_year = #{record.cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      cbi_requested_payment_term_of_current_year = #{record.cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      cbi_current_credit_limit_info_id = #{record.cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      cbi_requested_temp_credit_limit = #{record.cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      cbi_requested_temp_payment_term = #{record.cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      cbi_expire_date = #{record.cbiExpireDate,jdbcType=TIMESTAMP},
      cbi_requested_cv_order_no = #{record.cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      cbi_comments_from_BU = #{record.cbiCommentsFromBu,jdbcType=NVARCHAR},
      cbi_financial_statements_att_id = #{record.cbiFinancialStatementsAttId,jdbcType=BIGINT},
      cbi_cash_deposit_with_amount = #{record.cbiCashDepositWithAmount,jdbcType=NUMERIC},
      cbi_cash_deposit_with_amount_upload_scancopy_id = #{record.cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_cash_deposit_with_amount_valid_date = #{record.cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_the_3rd_party_guarantee_with_amount = #{record.cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id = #{record.cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_the_3rd_party_guarantee_with_amount_valid_date = #{record.cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_bank_guarantee_with_amount = #{record.cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_bank_guarantee_with_amount_upload_scancopy_id = #{record.cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_bank_guarantee_with_amount_valid_date = #{record.cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_personal_guarantee_with_amount = #{record.cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_personal_guarantee_with_amount_upload_scancopy_id = #{record.cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_personal_guarantee_with_amount_valid_date = #{record.cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cfi_info_id = #{record.cfiInfoId,jdbcType=BIGINT},
      credit_type = #{record.creditType,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      credit_dollar_rate = #{record.creditDollarRate,jdbcType=NUMERIC},
      current_credit_limit = #{record.currentCreditLimit,jdbcType=NUMERIC},
      process_status = #{record.processStatus,jdbcType=VARCHAR},
      credit_csr = #{record.cbiCreditCsr,jdbcType=VARCHAR},
      release_order_status = #{record.cbiReleaseOrderStatus,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    update wx_t_credit_app_request_form
    <set>
      <if test="processInstanceId != null">
        process_instance_id = #{processInstanceId,jdbcType=NVARCHAR},
      </if>
      <if test="requestNo != null">
        request_no = #{requestNo,jdbcType=NVARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=NVARCHAR},
      </if>
      <if test="aiPreparedBy != null">
        ai_prepared_by = #{aiPreparedBy,jdbcType=BIGINT},
      </if>
      <if test="aiRegionId != null">
        ai_region_id = #{aiRegionId,jdbcType=BIGINT},
      </if>
      <if test="aiRequestDate != null">
        ai_request_date = #{aiRequestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="aiRequestedBy != null">
        ai_requested_by = #{aiRequestedBy,jdbcType=NVARCHAR},
      </if>
      <if test="aiTelephone != null">
        ai_telephone = #{aiTelephone,jdbcType=NVARCHAR},
      </if>
      <if test="aiSalesTeam != null">
        ai_sales_team = #{aiSalesTeam,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCustomerId != null">
        cbi_customer_id = #{cbiCustomerId,jdbcType=NVARCHAR},
      </if>
      <if test="cbiProvinceId != null">
        cbi_province_id = #{cbiProvinceId,jdbcType=BIGINT},
      </if>
      <if test="cbiCooperationYearsWithCvx != null">
        cbi_cooperation_years_with_cvx = #{cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      </if>
      <if test="cbiYearN1TotalSales != null">
        cbi_year_n1_total_sales = #{cbiYearN1TotalSales,jdbcType=NUMERIC},
      </if>
      <if test="cbiDateEstablishment != null">
        cbi_date_establishment = #{cbiDateEstablishment,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiSalesTargetOfCio != null">
        cbi_sales_target_of_cio = #{cbiSalesTargetOfCio,jdbcType=NUMERIC},
      </if>
      <if test="cbiSalesTargetOfCdm != null">
        cbi_sales_target_of_cdm = #{cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      </if>
      <if test="cbiCreditLimitOfYearN1 != null">
        cbi_credit_limit_of_year_N1 = #{cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      </if>
      <if test="cbiPaymentTermOfYearN1 != null">
        cbi_payment_term_of_year_N1 = #{cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      </if>
      <if test="cbiRequestedCreditLimitCurrentYear != null">
        cbi_requested_credit_limit_current_year = #{cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedPaymentTermOfCurrentYear != null">
        cbi_requested_payment_term_of_current_year = #{cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCurrentCreditLimitInfoId != null">
        cbi_current_credit_limit_info_id = #{cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      </if>
      <if test="cbiRequestedTempCreditLimit != null">
        cbi_requested_temp_credit_limit = #{cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedTempPaymentTerm != null">
        cbi_requested_temp_payment_term = #{cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cbiExpireDate != null">
        cbi_expire_date = #{cbiExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiRequestedCvOrderNo != null">
        cbi_requested_cv_order_no = #{cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCommentsFromBu != null">
        cbi_comments_from_BU = #{cbiCommentsFromBu,jdbcType=NVARCHAR},
      </if>
      <if test="cbiFinancialStatementsAttId != null">
        cbi_financial_statements_att_id = #{cbiFinancialStatementsAttId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmount != null">
        cbi_cash_deposit_with_amount = #{cbiCashDepositWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiCashDepositWithAmountUploadScancopyId != null">
        cbi_cash_deposit_with_amount_upload_scancopy_id = #{cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmountValidDate != null">
        cbi_cash_deposit_with_amount_valid_date = #{cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmount != null">
        cbi_the_3rd_party_guarantee_with_amount = #{cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId != null">
        cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id = #{cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountValidDate != null">
        cbi_the_3rd_party_guarantee_with_amount_valid_date = #{cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiBankGuaranteeWithAmount != null">
        cbi_bank_guarantee_with_amount = #{cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiBankGuaranteeWithAmountUploadScancopyId != null">
        cbi_bank_guarantee_with_amount_upload_scancopy_id = #{cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiBankGuaranteeWithAmountValidDate != null">
        cbi_bank_guarantee_with_amount_valid_date = #{cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiPersonalGuaranteeWithAmount != null">
        cbi_personal_guarantee_with_amount = #{cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountUploadScancopyId != null">
        cbi_personal_guarantee_with_amount_upload_scancopy_id = #{cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountValidDate != null">
        cbi_personal_guarantee_with_amount_valid_date = #{cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiInfoId != null">
        cfi_info_id = #{cfiInfoId,jdbcType=BIGINT},
      </if>
      <if test="creditType != null">
        credit_type = #{creditType,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="creditDollarRate != null">
        credit_dollar_rate = #{creditDollarRate,jdbcType=NUMERIC},
      </if>
      <if test="currentCreditLimit != null">
        current_credit_limit = #{currentCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="cbiCreditCsr != null">
        credit_csr = #{cbiCreditCsr,jdbcType=VARCHAR},
      </if>
      <if test="cbiReleaseOrderStatus != null">
        release_order_status = #{cbiReleaseOrderStatus,jdbcType=VARCHAR},
      </if>
			<if test="workflowStatus != null" >
				workflow_status = #{workflowStatus,jdbcType=INTEGER},
			</if>
			<if test="customerType != null" >
				customer_type = #{customerType,jdbcType=VARCHAR},
			</if>
			<if test="soldToCode != null" >
				sold_to_code = #{soldToCode,jdbcType=VARCHAR},
			</if>
			<if test="payerCode != null" >
				payer_code = #{payerCode,jdbcType=VARCHAR},
			</if>
			<if test="customerName != null" >
				customer_name = #{customerName,jdbcType=VARCHAR},
			</if>
			<if test="directAnnualSalesPlan != null" >
				direct_annual_sales_plan = #{directAnnualSalesPlan,jdbcType=NUMERIC},
			</if>
			<if test="indirectAnnualSalesPlan != null" >
				indirect_annual_sales_plan = #{indirectAnnualSalesPlan,jdbcType=NUMERIC},
			</if>
			<if test="extFlag != null" >
				ext_flag = #{extFlag,jdbcType=INTEGER},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="extProperty5 != null" >
				ext_property5 = #{extProperty5,jdbcType=VARCHAR},
			</if>
			<if test="flsrId != null" >
				flsr_id = #{flsrId,jdbcType=BIGINT},
			</if>
			<if test="bu != null" >
				bu = #{bu,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null" >
				sales_channel = #{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null" >
				form_status = #{formStatus,jdbcType=INTEGER},
			</if>
				version_no=version_no+1,
			<if test="workflowLockerId != null" >
				locker_id = #{workflowLockerId,jdbcType=VARCHAR},
			</if>
			<if test="applyAmountUsd != null" >
				apply_amount_usd = #{applyAmountUsd,jdbcType=NUMERIC},
			</if>
    </set>
    where id = #{id,jdbcType=BIGINT} and version_no = #{formVersionNo,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    update wx_t_credit_app_request_form
    set process_instance_id = #{processInstanceId,jdbcType=NVARCHAR},
      request_no = #{requestNo,jdbcType=NVARCHAR},
      currency = #{currency,jdbcType=NVARCHAR},
      ai_prepared_by = #{aiPreparedBy,jdbcType=BIGINT},
      ai_region_id = #{aiRegionId,jdbcType=BIGINT},
      ai_request_date = #{aiRequestDate,jdbcType=TIMESTAMP},
      ai_requested_by = #{aiRequestedBy,jdbcType=NVARCHAR},
      ai_telephone = #{aiTelephone,jdbcType=NVARCHAR},
      ai_sales_team = #{aiSalesTeam,jdbcType=NVARCHAR},
      cbi_customer_id = #{cbiCustomerId,jdbcType=NVARCHAR},
      cbi_province_id = #{cbiProvinceId,jdbcType=BIGINT},
      cbi_cooperation_years_with_cvx = #{cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      cbi_year_n1_total_sales = #{cbiYearN1TotalSales,jdbcType=NUMERIC},
      cbi_date_establishment = #{cbiDateEstablishment,jdbcType=TIMESTAMP},
      cbi_sales_target_of_cio = #{cbiSalesTargetOfCio,jdbcType=NUMERIC},
      cbi_sales_target_of_cdm = #{cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      cbi_credit_limit_of_year_N1 = #{cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      cbi_payment_term_of_year_N1 = #{cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      cbi_requested_credit_limit_current_year = #{cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      cbi_requested_payment_term_of_current_year = #{cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      cbi_current_credit_limit_info_id = #{cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      cbi_requested_temp_credit_limit = #{cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      cbi_requested_temp_payment_term = #{cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      cbi_expire_date = #{cbiExpireDate,jdbcType=TIMESTAMP},
      cbi_requested_cv_order_no = #{cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      cbi_comments_from_BU = #{cbiCommentsFromBu,jdbcType=NVARCHAR},
      cbi_financial_statements_att_id = #{cbiFinancialStatementsAttId,jdbcType=BIGINT},
      cbi_cash_deposit_with_amount = #{cbiCashDepositWithAmount,jdbcType=NUMERIC},
      cbi_cash_deposit_with_amount_upload_scancopy_id = #{cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_cash_deposit_with_amount_valid_date = #{cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_the_3rd_party_guarantee_with_amount = #{cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id = #{cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_the_3rd_party_guarantee_with_amount_valid_date = #{cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_bank_guarantee_with_amount = #{cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_bank_guarantee_with_amount_upload_scancopy_id = #{cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_bank_guarantee_with_amount_valid_date = #{cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cbi_personal_guarantee_with_amount = #{cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      cbi_personal_guarantee_with_amount_upload_scancopy_id = #{cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      cbi_personal_guarantee_with_amount_valid_date = #{cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      cfi_info_id = #{cfiInfoId,jdbcType=BIGINT},
      credit_type = #{creditType,jdbcType=NVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=INTEGER},
      credit_dollar_rate = #{creditDollarRate,jdbcType=NUMERIC},
      current_credit_limit = #{currentCreditLimit,jdbcType=NUMERIC},
      process_status = #{processStatus,jdbcType=VARCHAR},
      credit_csr = #{cbiCreditCsr,jdbcType=VARCHAR},
      release_order_status = #{cbiReleaseOrderStatus,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectDraftAppFormByUserIdAndType" parameterType="map" resultMap="BaseResultMap">
    select top 1
    <include refid="Base_Column_List" />,
    <include refid="Extend_Column_List"/>
    from wx_t_credit_app_request_form
    where 1=1
        and ai_prepared_by = #{aiPreparedBy,jdbcType=BIGINT}
        and credit_type = #{creditType,jdbcType=BIGINT}
        and delete_flag != 1
        and process_instance_id is null
  </select>
  <select id="selectInfoByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />,
        <include refid="Extend_Column_List"/>
        from wx_t_credit_app_request_form
        where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectMyListByUserId" parameterType="com.chevron.credit.model.CreditListQueryParam" resultMap="BaseResultMap">
    select * from(
    select
    <include refid="Base_Column_List" />,
    <include refid="Extend_Column_List"/>
    from wx_t_credit_app_request_form
    where 1=1
     <if test="userId != null">
     and ai_prepared_by = #{userId,jdbcType=BIGINT}
     </if>
    <if test="creditAppTypes!=null">
      and credit_type in
      <foreach collection="creditAppTypes" item="type" open="(" close=")" separator=",">
        '${type}'
      </foreach>
    </if>
    <if test="status == 'draft'">
      and process_instance_id is null
    </if>
    ) aa where 1=1
    <if test="searchWord != null">
        and aa.cbi_customer_name like  CONCAT(CONCAT('%', #{searchWord,jdbcType=VARCHAR}),'%')
    </if>
  </select>
  <update id="saveApplication" parameterType="com.chevron.credit.model.WxTCreditAppRequestFormVo">
    update wx_t_credit_app_request_form
    <set>
      <if test="processInstanceId != null">
        process_instance_id = #{processInstanceId,jdbcType=NVARCHAR},
      </if>
      <!--<if test="requestNo != null">-->
        <!--request_no = #{requestNo,jdbcType=NVARCHAR},-->
      <!--</if>-->
      <if test="currency != null">
        currency = #{currency,jdbcType=NVARCHAR},
      </if>
      <!--<if test="aiPreparedBy != null">-->
        <!--ai_prepared_by = #{aiPreparedBy,jdbcType=BIGINT},-->
      <!--</if>-->
      <if test="aiRegionId != null">
        ai_region_id = #{aiRegionId,jdbcType=BIGINT},
      </if>
      <if test="aiRequestDate != null">
        ai_request_date = #{aiRequestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="aiRequestedBy != null">
        ai_requested_by = #{aiRequestedBy,jdbcType=NVARCHAR},
      </if>
      <if test="aiTelephone != null">
        ai_telephone = #{aiTelephone,jdbcType=NVARCHAR},
      </if>
      <if test="aiSalesTeam != null">
        ai_sales_team = #{aiSalesTeam,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCustomerId != null">
        cbi_customer_id = #{cbiCustomerId,jdbcType=NVARCHAR},
      </if>
      <if test="cbiProvinceId != null">
        cbi_province_id = #{cbiProvinceId,jdbcType=BIGINT},
      </if>
      <if test="cbiCooperationYearsWithCvx != null">
        cbi_cooperation_years_with_cvx = #{cbiCooperationYearsWithCvx,jdbcType=INTEGER},
      </if>
      <if test="cbiYearN1TotalSales != null">
        cbi_year_n1_total_sales = #{cbiYearN1TotalSales,jdbcType=NUMERIC},
      </if>
      <if test="cbiDateEstablishment != null">
        cbi_date_establishment = #{cbiDateEstablishment,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiSalesTargetOfCio != null">
        cbi_sales_target_of_cio = #{cbiSalesTargetOfCio,jdbcType=NUMERIC},
      </if>
      <if test="cbiSalesTargetOfCdm != null">
        cbi_sales_target_of_cdm = #{cbiSalesTargetOfCdm,jdbcType=NUMERIC},
      </if>
      <if test="cbiCreditLimitOfYearN1 != null">
        cbi_credit_limit_of_year_N1 = #{cbiCreditLimitOfYearN1,jdbcType=NUMERIC},
      </if>
      <if test="cbiPaymentTermOfYearN1 != null">
        cbi_payment_term_of_year_N1 = #{cbiPaymentTermOfYearN1,jdbcType=NVARCHAR},
      </if>
      <if test="cbiRequestedCreditLimitCurrentYear != null">
        cbi_requested_credit_limit_current_year = #{cbiRequestedCreditLimitCurrentYear,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedPaymentTermOfCurrentYear != null">
        cbi_requested_payment_term_of_current_year = #{cbiRequestedPaymentTermOfCurrentYear,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCurrentCreditLimitInfoId != null">
        cbi_current_credit_limit_info_id = #{cbiCurrentCreditLimitInfoId,jdbcType=BIGINT},
      </if>
      <if test="cbiRequestedTempCreditLimit != null">
        cbi_requested_temp_credit_limit = #{cbiRequestedTempCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="cbiRequestedTempPaymentTerm != null">
        cbi_requested_temp_payment_term = #{cbiRequestedTempPaymentTerm,jdbcType=NVARCHAR},
      </if>
      <if test="cbiExpireDate != null">
        cbi_expire_date = #{cbiExpireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiRequestedCvOrderNo != null">
        cbi_requested_cv_order_no = #{cbiRequestedCvOrderNo,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCommentsFromBu != null">
        cbi_comments_from_BU = #{cbiCommentsFromBu,jdbcType=NVARCHAR},
      </if>
      <if test="cbiFinancialStatementsAttId != null">
        cbi_financial_statements_att_id = #{cbiFinancialStatementsAttId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmount != null">
        cbi_cash_deposit_with_amount = #{cbiCashDepositWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiCashDepositWithAmountUploadScancopyId != null">
        cbi_cash_deposit_with_amount_upload_scancopy_id = #{cbiCashDepositWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiCashDepositWithAmountValidDate != null">
        cbi_cash_deposit_with_amount_valid_date = #{cbiCashDepositWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmount != null">
        cbi_the_3rd_party_guarantee_with_amount = #{cbiThe3rdPartyGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId != null">
        cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id = #{cbiThe3rdPartyGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiThe3rdPartyGuaranteeWithAmountValidDate != null">
        cbi_the_3rd_party_guarantee_with_amount_valid_date = #{cbiThe3rdPartyGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiBankGuaranteeWithAmount != null">
        cbi_bank_guarantee_with_amount = #{cbiBankGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiBankGuaranteeWithAmountUploadScancopyId != null">
        cbi_bank_guarantee_with_amount_upload_scancopy_id = #{cbiBankGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiBankGuaranteeWithAmountValidDate != null">
        cbi_bank_guarantee_with_amount_valid_date = #{cbiBankGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cbiPersonalGuaranteeWithAmount != null">
        cbi_personal_guarantee_with_amount = #{cbiPersonalGuaranteeWithAmount,jdbcType=NUMERIC},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountUploadScancopyId != null">
        cbi_personal_guarantee_with_amount_upload_scancopy_id = #{cbiPersonalGuaranteeWithAmountUploadScancopyId,jdbcType=BIGINT},
      </if>
      <if test="cbiPersonalGuaranteeWithAmountValidDate != null">
        cbi_personal_guarantee_with_amount_valid_date = #{cbiPersonalGuaranteeWithAmountValidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cfiInfoId != null">
        cfi_info_id = #{cfiInfoId,jdbcType=BIGINT},
      </if>
      <if test="creditType != null">
        credit_type = #{creditType,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
     <if test="creditDollarRate != null">
        credit_dollar_rate = #{creditDollarRate,jdbcType=NUMERIC},
      </if>
       <if test="currentCreditLimit != null">
        current_credit_limit = #{currentCreditLimit,jdbcType=NUMERIC},
      </if>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=NVARCHAR},
      </if>
      <if test="cbiCreditCsr != null">
        credit_csr = #{cbiCreditCsr,jdbcType=NVARCHAR},
      </if>
      <if test="cbiReleaseOrderStatus != null">
        release_order_status = #{cbiReleaseOrderStatus,jdbcType=NVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <resultMap id="FormApprovalInfoMap" type="com.chevron.newhomepage.model.FormApprovalInfo">
    <!--//    actinst.PROC_INST_ID_ process_instance_id,-->
    <!--//    actinst.PROC_DEF_ID_  process_definition_id,-->
    <!--//    actinst.ACT_NAME_     step_name,-->
    <!--//    actinst.ASSIGNEE_     executor_id,-->
    <!--//    executor_user.ch_name executor_name,-->
    <!--//    actinst.ASSIGNEE_     actual_executor_id,-->
    <!--//    executor_user.ch_name actual_executor_name,-->
    <!--//    actinst.TASK_ID_      task_id,-->
    <!--//    actinst.START_TIME_   start_time,-->
    <!--//    actinst.END_TIME_     end_time,-->
    <!--//    varinst_approve.LONG_ approve,-->
    <!--//    varinst_commnet.TEXT_ comments,-->
    <!--//        case when actinst.END_TIME_ is null-->
    <!--//    then 0-->
    <!--//    when actinst.END_TIME_ is not null-->
    <!--//    then 1 end          finished-->
    <!--private String processInstanceId;-->
    <!--private String processDefinitionId;-->
    <!--private String stepName;-->
    <!--private String executionId;-->
    <!--private String executionName;-->
    <!--private String actualExecutionId;-->
    <!--private String actualExecutionName;-->
    <!--private String taskId;-->
    <!--private Date startTime;-->
    <!--private Date endTime;-->
    <!--private boolean approve;-->
    <!--private String comments;-->

    <result column="process_instance_id" jdbcType="NVARCHAR" property="processInstanceId" />
    <result column="process_definition_id" jdbcType="NVARCHAR" property="processDefinitionId" />
    <result column="step_name" jdbcType="NVARCHAR" property="stepName" />
    <result column="executor_id" jdbcType="NVARCHAR" property="executionId" />
    <result column="executor_name" jdbcType="NVARCHAR" property="executionName" />
    <result column="actual_executor_id" jdbcType="NVARCHAR" property="actualExecutionId" />
    <result column="actual_executor_name" jdbcType="NVARCHAR" property="actualExecutionName" />
    <result column="task_id" jdbcType="NVARCHAR" property="taskId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="approve" jdbcType="BIGINT" property="approve" />
    <result column="auto" jdbcType="BIGINT" property="auto" />
    <result column="finished" jdbcType="BIGINT" property="finished" />
  </resultMap>
  <select id="queryApprovalHistory" parameterType="map" resultMap="FormApprovalInfoMap">
      select
        actinst.PROC_INST_ID_ process_instance_id,
        actinst.PROC_DEF_ID_  process_definition_id,
        actinst.ACT_NAME_     step_name,
        actinst.ASSIGNEE_     executor_id,
        case when (varinst_auto.LONG_ = 0 or varinst_auto.LONG_ is null)
        then executor_user.ch_name
        when varinst_auto.LONG_ = 1
        then concat(executor_user.ch_name,'(auto)') end executor_name,
        actinst.ASSIGNEE_     actual_executor_id,
        case when (varinst_auto.LONG_ = 0 or varinst_auto.LONG_ is null)
        then executor_user.ch_name
        when varinst_auto.LONG_ = 1
        then concat(executor_user.ch_name,'(auto)') end actual_executor_name,
        actinst.TASK_ID_      task_id,
        actinst.START_TIME_   start_time,
        actinst.END_TIME_     end_time,
        varinst_approve.LONG_ approve,
        varinst_commnet.TEXT_ comments,
        varinst_auto.LONG_ auto,
        case when actinst.END_TIME_ is null
          then 0
        when actinst.END_TIME_ is not null
          then 1 end          finished
      from dbo.ACT_HI_ACTINST actinst left join dbo.ACT_HI_VARINST varinst_commnet
          on actinst.TASK_ID_ = varinst_commnet.TASK_ID_ and varinst_commnet.TASK_ID_ is not null  and varinst_commnet.NAME_ = 'comment'
        left join dbo.ACT_HI_VARINST varinst_approve
          on actinst.TASK_ID_ = varinst_approve.TASK_ID_ and varinst_approve.TASK_ID_ is not null  and varinst_approve.NAME_ = 'approve'
        left join dbo.ACT_HI_VARINST varinst_auto
          on actinst.TASK_ID_ = varinst_auto.TASK_ID_ and varinst_auto.TASK_ID_ is not null  and varinst_auto.NAME_ = 'auto'
        left join dbo.wx_t_user executor_user on executor_user.user_id = try_cast(actinst.ASSIGNEE_ as bigint)
      where actinst.PROC_INST_ID_ = #{processInstanceId,jdbcType=NVARCHAR} and ACT_TYPE_ = 'userTask'
      order by actinst.START_TIME_
    <choose>
      <when test="order != null">
        ${order}
      </when>
      <otherwise>
        desc
      </otherwise>
    </choose>
  </select>
  <select id="getCreditAppRequestFormsByCondition" parameterType="com.chevron.credit.model.CreditListQueryParam" resultMap="BaseResultMap">
    select * from (
		select carf.*,
		(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
    			(select top 1 customer_name
                  from dbo.wx_t_credit_customer_info_final
                  where (payer  + '_' + type) = cbi_customer_id ) as cbi_customer_name
		  	     from wx_t_credit_app_request_form carf		  	    
		  	    left join wx_t_partner_o2o_enterprise poe on carf.cbi_customer_id = poe.sap_code
		  	    left join wx_t_organization org on org.id = poe.partner_id
		 where 1 = 1
              <choose>
                <!--如果是登录的这个用户是supervisor及以上-->
                <when test="isSupervisorAndAbove">
                  and exists (select distinct
                  dwrsr.sales_channel_name,
                  dwrssr.sales_cai,
                  dwrssr.suppervisor_cai,
                  dwrsr.channel_manager_cai,
                  dwrsr.bu_manager_cai
                  FROM
                  wx_t_organization organ
                  LEFT JOIN wx_t_partner_o2o_enterprise e ON
                  organ.id = e.partner_id
                  LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
                  dwrssr.distributor_id = e.distributor_id
                  LEFT JOIN dw_region_sales_channel_rel dwrsr ON
                  dwrsr.region_name = dwrssr.region_name
                  LEFT JOIN wx_t_user u ON u.cai = dwrssr.sales_cai
                  where 1 = 1
                  and  carf.ai_requested_by like concat(concat('%(',dwrssr.sales_cai),')%' )
                  and (
                  dwrssr.suppervisor_cai = #{cai} or
                  dwrsr.channel_manager_cai = #{cai} or
                  dwrsr.bu_manager_cai = #{cai}
                  ))
              </when>
                <otherwise>

                </otherwise>
              </choose>
              <if test="sales">
              and carf.ai_requested_by like concat(concat('%(',#{cai}),')%' )
              </if>
              <choose>
                <when test="idList != null and idList.size() >0" >
                  and carf.id in
                  <foreach collection="idList" item="listItem" open="(" close=")" separator="," >
                    '${listItem}'
                  </foreach>
                </when>
                <otherwise>
                  and 1 != 1
                </otherwise>
              </choose>
          ) aa
         where  1=1
        <if test="queryType == 1">
          <if test="cbiCustomerId != null and cbiCustomerId != ''">
            and cbi_customer_id like '%' + #{cbiCustomerId,jdbcType=VARCHAR} + '%'
          </if>
          <if test="partnerName != null and partnerName != ''">
            and cbi_customer_name like '%' + #{partnerName,jdbcType=VARCHAR} + '%'
          </if>
          <if test="dateStart != null">
            and CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
          </if>
          <if test="dateEnd != null">
            and CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
          </if>
          <if test="aiRequestedBy != null and aiRequestedBy != '' ">
            and ai_requested_by like '%' + #{aiRequestedBy,jdbcType=VARCHAR} + '%'
          </if>
          <if test="creditAppTypes != null and creditAppTypes.size() > 0" >
            and credit_type in
            <foreach collection="creditAppTypes" item="listItem" open="(" close=")" separator="," >
              '${listItem}'
            </foreach>
          </if>
        </if>
          <if test="queryType == 2">
            <if test="queryField != null and queryField != ''">
              and (	cbi_customer_id like '%' + #{queryField} + '%'
              or cbi_customer_name like '%' + #{queryField} + '%'
              or ai_requested_by like '%' + #{queryField} + '%'
              )
            </if>
          </if>
	</select>
  <resultMap id="ApprovalTeamVoMap" type="com.chevron.credit.model.ApprovalTeamVo">
    <!--private String salesChannelName;-->
    <!--private String salesCai;-->
    <!--private String salesName;-->
    <!--private String suppervisorCai;-->
    <!--private String supervisorMName;-->
    <!--private String channelManagerCai;-->
    <!--private String channelMName;-->
    <!--private String buManagerCai;-->
    <!--private String buMName;-->
    <!--private String localCreditAnalyst;-->
    <!--private String localCreditTeamLead;-->
    <!--private String chinaFinanceManager;-->
    <!--private String apCreditTeam;-->
    <result column="sales_channel_name" jdbcType="NVARCHAR" property="salesChannelName" />
    <result column="sales_cai" jdbcType="NVARCHAR" property="salesCai" />
    <result column="sales_name" jdbcType="NVARCHAR" property="salesName" />
    <result column="suppervisor_cai" jdbcType="NVARCHAR" property="suppervisorCai" />
    <result column="supervisor_m_name" jdbcType="NVARCHAR" property="supervisorMName" />
    <result column="channel_manager_cai" jdbcType="NVARCHAR" property="channelManagerCai" />
    <result column="channel_m_name" jdbcType="NVARCHAR" property="channelMName" />
    <result column="bu_manager_cai" jdbcType="NVARCHAR" property="buManagerCai" />
    <result column="bu_m_name" jdbcType="NVARCHAR" property="buMName" />
    <result column="Local_Credit_Analyst" jdbcType="NVARCHAR" property="localCreditAnalyst" />
    <result column="Local_Credit_Team_Lead" jdbcType="NVARCHAR" property="localCreditTeamLead" />
    <result column="China_Finance_Manager" jdbcType="NVARCHAR" property="chinaFinanceManager" />
    <result column="AP_Credit_Team" jdbcType="NVARCHAR" property="apCreditTeam" />
    <result column="AP_Credit_Team2" jdbcType="NVARCHAR" property="apCreditTeam2" />
    <result column="GM" jdbcType="NVARCHAR" property="gm" />
    <result column="ai_prepared_by_name" jdbcType="NVARCHAR" property="aiPreparedByName" />
  </resultMap>
    <select id="queryApprovalTeamByCai" resultMap="ApprovalTeamVoMap" parameterType="map">
        select distinct
            dwrsr.sales_channel_name,
            dwrssr.sales_cai,
            u.ch_name            sales_name,
            dwrssr.suppervisor_cai,
            supervisor_u.ch_name supervisor_m_name,
            dwrsr.channel_manager_cai,
            channel_u.ch_name    channel_m_name,
            dwrsr.bu_manager_cai,
            bu_u.ch_name         bu_m_name,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'Local_Credit_Analyst'  and credit_u.status =1 order by credit_u.user_id desc
            ) as                 Local_Credit_Analyst,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'Local_Credit_Team_Lead'  and credit_u.status =1 order by credit_u.user_id desc
            ) as                 Local_Credit_Team_Lead,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'China_Finance_Manager'  and credit_u.status =1 order by credit_u.user_id desc
            ) as                 China_Finance_Manager,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'AP_Credit_Team'  and credit_u.status =1 order by credit_u.user_id desc
            ) as                 AP_Credit_Team,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'AP_Credit_Team2' and credit_u.status =1 order by credit_u.user_id desc
            ) as                 AP_Credit_Team2,
            (select top 1 credit_u.ch_name
             from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
                 inner join wx_t_role r on r.role_id = ur.role_id
             where r.ch_role_name = 'GM' and credit_u.status =1 order by credit_u.user_id desc
            ) as                 GM,
            (select top 1 u.ch_name from dbo.wx_t_user u where u.user_id = #{aiPreparedBy}) as ai_prepared_by_name
        FROM
            wx_t_organization o
            LEFT JOIN wx_t_partner_o2o_enterprise e ON
                                                                                                o.id = e.partner_id
            LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
                                                                                                                                 dwrssr.distributor_id = e.distributor_id
            LEFT JOIN dw_region_sales_channel_rel dwrsr ON
                                                                                                        dwrsr.region_name = dwrssr.region_name
            LEFT JOIN wx_t_user u ON u.cai = dwrssr.sales_cai
            LEFT JOIN wx_t_user supervisor_u ON supervisor_u.cai = dwrssr.suppervisor_cai
            LEFT JOIN wx_t_user channel_u ON channel_u.cai = dwrsr.channel_manager_cai
            LEFT JOIN wx_t_user bu_u ON bu_u.cai = dwrsr.bu_manager_cai
        where 1 = 1
                    and dwrssr.sales_cai = #{cai}
    </select>
  <resultMap id="ApprovalTeamIdVoMap" type="com.chevron.credit.model.ApprovalTeamIdVo">
    <result column="sales_id" jdbcType="BIGINT" property="salesId" />
    <result column="supervisor_id" jdbcType="BIGINT" property="supervisorId" />
    <result column="channel_m_id" jdbcType="BIGINT" property="channelMId" />
    <result column="bu_id" jdbcType="BIGINT" property="buId" />
    <result column="Local_Credit_Analyst_id" jdbcType="BIGINT" property="localCreditAnalystId" />
    <result column="Local_Credit_Team_Lead_id" jdbcType="BIGINT" property="localCreditTeamLeadId" />
    <result column="China_Finance_Manager_id" jdbcType="BIGINT" property="chinaFinanceManagerId" />
    <result column="GM_id" jdbcType="BIGINT" property="gmId" />
    <result column="AP_Credit_Team_id" jdbcType="BIGINT" property="apCreditTeamId" />
    <result column="ap_credit_team_backup_id" jdbcType="BIGINT" property="apCreditTeamBackupId" />
    <result column="AP_Credit_Team2_id" jdbcType="BIGINT" property="apCreditTeam2Id" />
    <result column="ai_prepared_by_id" jdbcType="BIGINT" property="aiPreparedById" />
  </resultMap>
  <select id="queryApprovalTeamIdsByCai" resultMap="ApprovalTeamIdVoMap" parameterType="map">
    select distinct
    dwrsr.sales_channel_name,
    dwrssr.sales_cai,
    u.user_id                                                                    sales_id,
    supervisor_u.user_id                                                         supervisor_id,
    channel_u.user_id                                                            channel_m_id,
    bu_u.user_id                                                                 bu_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'Local_Credit_Analyst' and credit_u.status = 1)   as Local_Credit_Analyst_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'Local_Credit_Team_Lead' and credit_u.status = 1) as Local_Credit_Team_Lead_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'China_Finance_Manager' and credit_u.status = 1)  as China_Finance_Manager_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'AP_Credit_Team' and credit_u.status = 1)         as AP_Credit_Team_id,
     (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'AP_Credit_Team_backup' and credit_u.status = 1)  as ap_credit_team_backup_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'AP_Credit_Team2' and credit_u.status = 1)        as AP_Credit_Team2_id,
    (select top 1 credit_u.user_id
     from dbo.wx_t_user credit_u inner join wx_t_userrole ur on credit_u.user_id = ur.user_id
       inner join wx_t_role r on r.role_id = ur.role_id
     where r.ch_role_name = 'GM' and credit_u.status = 1)                     as GM_id,
    #{aiPreparedBy}  as ai_prepared_by_id
  FROM wx_t_organization o LEFT JOIN wx_t_partner_o2o_enterprise e ON o.id = e.partner_id
    LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON dwrssr.distributor_id = e.distributor_id
    LEFT JOIN dw_region_sales_channel_rel dwrsr ON dwrsr.region_name = dwrssr.region_name
    LEFT JOIN wx_t_user u ON u.cai = dwrssr.sales_cai
    LEFT JOIN wx_t_user supervisor_u ON supervisor_u.cai = dwrssr.suppervisor_cai
    LEFT JOIN wx_t_user channel_u ON channel_u.cai = dwrsr.channel_manager_cai
    LEFT JOIN wx_t_user bu_u ON bu_u.cai = dwrsr.bu_manager_cai
  where 1 = 1 and dwrssr.sales_cai = #{cai}
    </select>
   <resultMap id="fullInfoMap" type="com.chevron.credit.model.CreditAppRequestDetailView">
    <result column="request_no" jdbcType="NVARCHAR" property="requestNo" />
    <result column="ai_prepared_by_name" jdbcType="NVARCHAR" property="aiPreparedByName" />
    <!-- <result column="ai_request_date" jdbcType="TIMESTAMP" property="aiRequestDate" /> -->
    <result column="ai_requested_by" jdbcType="NVARCHAR" property="aiRequestedBy" />
    <result column="ai_telephone" jdbcType="NVARCHAR" property="aiTelephone" />
    <result column="ai_sales_team" jdbcType="NVARCHAR" property="aiSalesTeam" />
    <result column="cbi_customer_id" jdbcType="NVARCHAR" property="cbiCustomerId" />
    <result column="cbi_customer_name" jdbcType="NVARCHAR" property="cbiCustomerName" />
    <result column="provice_name" jdbcType="NVARCHAR" property="cbiProvinceName" />
    <result column="cbi_cooperation_years_with_cvx" jdbcType="INTEGER" property="cbiCooperationYearsWithCvx" />
    <result column="cbi_year_n1_total_sales" jdbcType="NUMERIC" property="cbiYearN1TotalSales" />
    <result column="cbi_date_establishment" jdbcType="TIMESTAMP" property="cbiDateEstablishment" />
    <result column="cbi_sales_target_of_cio" jdbcType="NUMERIC" property="cbiSalesTargetOfCio" />
    <result column="cbi_sales_target_of_cdm" jdbcType="NUMERIC" property="cbiSalesTargetOfCdm" />
    <result column="cbi_credit_limit_of_year_N1" jdbcType="NUMERIC" property="cbiCreditLimitOfYearN1" />
    <result column="cbi_payment_term_of_year_N1" jdbcType="NVARCHAR" property="cbiPaymentTermOfYearN1" />
    <result column="cbi_requested_credit_limit_current_year" jdbcType="NUMERIC" property="cbiRequestedCreditLimitCurrentYear" />
    <result column="cbi_requested_payment_term_of_current_year" jdbcType="NVARCHAR" property="cbiRequestedPaymentTermOfCurrentYear" />
    <result column="cbi_requested_temp_credit_limit" jdbcType="NUMERIC" property="cbiRequestedTempCreditLimit" />
    <result column="cbi_requested_temp_payment_term" jdbcType="NVARCHAR" property="cbiRequestedTempPaymentTerm" />
    <result column="cbi_expire_date" jdbcType="TIMESTAMP" property="cbiExpireDate" />
    <result column="cbi_requested_cv_order_no" jdbcType="NVARCHAR" property="cbiRequestedCvOrderNo" />
    <result column="cbi_comments_from_BU" jdbcType="NVARCHAR" property="cbiCommentsFromBu" />
    <result column="cbi_cash_deposit_with_amount" jdbcType="NUMERIC" property="cbiCashDepositWithAmount" />
    <result column="cbi_cash_deposit_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiCashDepositWithAmountValidDate" />
    <result column="cbi_the_3rd_party_guarantee_with_amount" jdbcType="NUMERIC" property="cbiThe3rdPartyGuaranteeWithAmount" />
    <result column="cbi_the_3rd_party_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiThe3rdPartyGuaranteeWithAmountValidDate" />
    <result column="cbi_bank_guarantee_with_amount" jdbcType="NUMERIC" property="cbiBankGuaranteeWithAmount" />
    <result column="cbi_bank_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiBankGuaranteeWithAmountValidDate" />
    <result column="cbi_personal_guarantee_with_amount" jdbcType="NUMERIC" property="cbiPersonalGuaranteeWithAmount" />
    <result column="cbi_personal_guarantee_with_amount_valid_date" jdbcType="TIMESTAMP" property="cbiPersonalGuaranteeWithAmountValidDate" />
    <!--
	<result column="cbi_confirmed_credit_limit_of_current_year" jdbcType="NUMERIC" property="cbiConfirmedCreditLimitOfCurrentYear" />
    <result column="cbi_confirmed_payment_term_of_current_year" jdbcType="NVARCHAR" property="cbiConfirmedPaymentTermOfCurrentYear" />
    <result column="cbi_confirmed_temp_credit_limit" jdbcType="NUMERIC" property="cbiConfirmedTempCreditLimit" />
    <result column="cbi_confirmed_temp_payment_term" jdbcType="NVARCHAR" property="cbiConfirmedTempPaymentTerm" />
    <result column="cbi_confirmed_expired_date" jdbcType="TIMESTAMP" property="cbiConfirmedExpiredDate" />-->
    <result column="credit_type" jdbcType="NVARCHAR" property="creditType" />
    <!-- <result column="process_status" jdbcType="VARCHAR" property="processStatus" /> -->
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
 
    <result column="cfi_year_n1_payment_record" jdbcType="NVARCHAR" property="cfiYearN1PaymentRecord" />
    <result column="cfi_pay_history_with_chevron" jdbcType="NUMERIC" property="cfiPayHistoryWithChevron" />
    <result column="cfi_dso_in_chevron_china" jdbcType="NUMERIC" property="cfiDsoInChevronChina" />
    <result column="cfi_quick_ratio" jdbcType="NUMERIC" property="cfiQuickRatio" />
    <result column="cfi_current_ratio" jdbcType="NUMERIC" property="cfiCurrentRatio" />
    <result column="cfi_daily_sales" jdbcType="NUMERIC" property="cfiDailySales" />
    <result column="cfi_net_working_capital_cycle" jdbcType="NUMERIC" property="cfiNetWorkingCapitalCycle" />
    <result column="cfi_cash_flow_coverage" jdbcType="NUMERIC" property="cfiCashFlowCoverage" />
    <result column="cfi_tangible_net_worth_ratio_g32" jdbcType="NUMERIC" property="cfiTangibleNetWorthRatioG32" />
    <result column="cfi_ap_days" jdbcType="NUMERIC" property="cfiApDays" />
    <result column="cfi_tangible_net_worth" jdbcType="NUMERIC" property="cfiTangibleNetWorth" />
    <result column="cfi_current_liability_to_equity" jdbcType="NUMERIC" property="cfiCurrentLiabilityToEquity" />
    <result column="cfi_long_term_liability_total_assets_ratio" jdbcType="NUMERIC" property="cfiLongTermLiabilityTotalAssetsRatio" />
    <result column="cfi_liablities_assets" jdbcType="NUMERIC" property="cfiLiablitiesAssets" />
    <result column="cfi_equity_ratio" jdbcType="DECIMAL" property="cfiEquityRatio" />
    <result column="cfi_inventory_turnover" jdbcType="DECIMAL" property="cfiInventoryTurnover" />
    <result column="cfi_days_in_inventory" jdbcType="DECIMAL" property="cfiDaysInInventory" />
    <result column="cfi_account_receivable_trunover" jdbcType="DECIMAL" property="cfiAccountReceivableTrunover" />
    <result column="cfi_days_in_accounts_receivable" jdbcType="DECIMAL" property="cfiDaysInAccountsReceivable" />
    <result column="cfi_sale_current_assets" jdbcType="DECIMAL" property="cfiSaleCurrentAssets" />
    <result column="cfi_asset_turnover" jdbcType="DECIMAL" property="cfiAssetTurnover" />
    <result column="cfi_profit_margin" jdbcType="DECIMAL" property="cfiProfitMargin" />
    <result column="cfi_after_tax_profit_ratio" jdbcType="DECIMAL" property="cfiAfterTaxProfitRatio" />
    <result column="cfi_return_on_equity" jdbcType="DECIMAL" property="cfiReturnOnEquity" />
    <!--<result column="cfi_asset_turnover_net_sales_to_total_assets" jdbcType="DECIMAL" property="cfiAssetTurnoverNetSalesToTotalAssets" />-->
    <result column="cfi_working_capital" jdbcType="DECIMAL" property="cfiWorkingCapital" />
    <result column="cfi_equity" jdbcType="DECIMAL" property="cfiEquity" />
    <result column="cfi_working_assets" jdbcType="DECIMAL" property="cfiWorkingAssets" />
    <result column="cfi_estimated_value" jdbcType="DECIMAL" property="cfiEstimatedValue" />
    <!--<result column="cfi_credit_index" jdbcType="DECIMAL" property="cfiCreditIndex" />-->
    <result column="cfi_credit_limit_estimated_value" jdbcType="DECIMAL" property="cfiCreditLimitEstimatedValue" />
    <result column="cfi_calculated_credit_limit_per_credit_policy" jdbcType="DECIMAL" property="cfiCalculatedCreditLimitPerCreditPolicy" />
    <result column="cfi_current_exposure" jdbcType="DECIMAL" property="cfiCurrentExposure" />
    <result column="cfi_cv_amount" jdbcType="DECIMAL" property="cfiCvAmount" />
    <result column="cfi_rec_credit_limit_of_current_year" jdbcType="NUMERIC" property="cfiRecCreditLimitOfCurrentYear" />
    <result column="cfi_rec_credit_payment_term" jdbcType="NVARCHAR" property="cfiRecCreditPaymentTerm" />
    <result column="cfi_rec_add_temp_credit_limit" jdbcType="NUMERIC" property="cfiRecAddTempCreditLimit" />
    <result column="cfi_rec_temp_payment_term" jdbcType="NVARCHAR" property="cfiRecTempPaymentTerm" />
    <result column="cfi_total_score" jdbcType="NUMERIC" property="cfiTotalScore" />
    <result column="cfi_comments_from_credit" jdbcType="NVARCHAR" property="cfiCommentsFromCredit" />
		<result column="workflow_status_text" property="workflowStatusText" jdbcType="VARCHAR"/>
  </resultMap>
    <select id="getCreditAppRequestFullInfoByCondition" parameterType="com.chevron.credit.model.CreditListQueryParam" resultMap="fullInfoMap">
		select cacfi.[cfi_year_n1_payment_record]
      ,cacfi.[cfi_pay_history_with_chevron]
      ,cacfi.[cfi_dso_in_chevron_china]
      ,cacfi.[cfi_quick_ratio]
      ,cacfi.[cfi_current_ratio]
      ,cacfi.[cfi_daily_sales]
      ,cacfi.[cfi_net_working_capital_cycle]
      ,cacfi.[cfi_cash_flow_coverage]
      ,cacfi.[cfi_tangible_net_worth_ratio_g32]
      ,cacfi.[cfi_ap_days]
      ,cacfi.[cfi_tangible_net_worth]
      ,cacfi.[cfi_current_liability_to_equity]
      ,cacfi.[cfi_long_term_liability_total_assets_ratio]
      ,cacfi.[cfi_liablities_assets]
      ,cacfi.[cfi_equity_ratio]
      ,cacfi.[cfi_inventory_turnover]
      ,cacfi.[cfi_days_in_inventory]
      ,cacfi.[cfi_account_receivable_trunover]
      ,cacfi.[cfi_days_in_accounts_receivable]
      ,cacfi.[cfi_sale_current_assets]
      ,cacfi.[cfi_asset_turnover]
      ,cacfi.[cfi_profit_margin]
      ,cacfi.[cfi_after_tax_profit_ratio]
      ,cacfi.[cfi_return_on_equity]
      ,cacfi.[cfi_asset_turnover_net_sales_to_total_assets]
      ,cacfi.[cfi_working_capital]
      ,cacfi.[cfi_equity]
      ,cacfi.[cfi_working_assets]
      ,cacfi.[cfi_estimated_value]
      ,cacfi.[cfi_credit_index]
      ,cacfi.[cfi_credit_limit_estimated_value]
      ,cacfi.[cfi_calculated_credit_limit_per_credit_policy]
      ,cacfi.[cfi_current_exposure]
      ,cacfi.[cfi_cv_amount]
      ,cacfi.[cfi_screenshot_of_current_exposure_att_id]
      ,cacfi.[others_att_id]
      ,cacfi.[cfi_rec_credit_limit_of_current_year]
      ,cacfi.[cfi_rec_credit_payment_term]
      ,cacfi.[cfi_rec_add_temp_credit_limit]
      ,cacfi.[cfi_rec_temp_payment_term]
      ,cacfi.[cfi_total_score]
      ,cacfi.[cfi_comments_from_credit]
      ,cacfi.[cfi_confirmed_credit_limit_of_current_year]
      ,cacfi.[cfi_confirmed_payment_term_of_current_year]
      ,cacfi.[cfi_confirmed_temp_credit_limit]
      ,cacfi.[cfi_confirmed_temp_payment_term]
      ,cacfi.[cfi_confirmed_expired_date]
      ,cacfi.[cfi_upload_art_att_id]
      ,cacfi.[cfi_upload_investigation_report_att_id]
      ,cacfi.[form_id],t1.id,t1.process_instance_id, t1.request_no, t1.currency, t1.ai_prepared_by, t1.ai_region_id, t1.ai_request_date, 
		t1.ai_requested_by, t1.ai_telephone, t1.ai_sales_team, t1.cbi_customer_id, t1.cbi_province_id, t1.cbi_cooperation_years_with_cvx, 
    t1.cbi_year_n1_total_sales, t1.cbi_date_establishment, t1.cbi_sales_target_of_cio, t1.cbi_sales_target_of_cdm, 
    t1.cbi_credit_limit_of_year_N1, t1.cbi_payment_term_of_year_N1, t1.cbi_requested_credit_limit_current_year, 
    t1.cbi_requested_payment_term_of_current_year, t1.cbi_current_credit_limit_info_id, t1.cbi_requested_temp_credit_limit, 
    t1.cbi_requested_temp_payment_term, t1.cbi_expire_date, t1.cbi_requested_cv_order_no, t1.cbi_comments_from_BU, 
    t1.cbi_financial_statements_att_id, t1.cbi_cash_deposit_with_amount, t1.cbi_cash_deposit_with_amount_upload_scancopy_id, 
    t1.cbi_cash_deposit_with_amount_valid_date, t1.cbi_the_3rd_party_guarantee_with_amount, 
    t1.cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id, t1.cbi_the_3rd_party_guarantee_with_amount_valid_date, 
    t1.cbi_bank_guarantee_with_amount, t1.cbi_bank_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_bank_guarantee_with_amount_valid_date, t1.cbi_personal_guarantee_with_amount, t1.cbi_personal_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_personal_guarantee_with_amount_valid_date, t1.cfi_info_id, t1.credit_type, t1.create_time, 
    t1.update_time, t1.delete_flag, t1.credit_dollar_rate, t1.current_credit_limit, t1.process_status,
    t1.credit_csr,t1.release_order_status, t1.bu,
			 t1.workflow_status, t1.customer_type, t1.sold_to_code, t1.payer_code, t1.customer_name,
			 t1.direct_annual_sales_plan, t1.indirect_annual_sales_plan, t1.ext_flag, t1.ext_property1, t1.ext_property2,
			 t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.flsr_id, t1.form_status, t1.version_no, t1.sales_channel,
			 t1.locker_id, t1.apply_amount_usd,
		(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
    			 t1.customer_name cbi_customer_name,
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Credit.workflowStatus' and CHARINDEX('|' + cast(t1.workflow_status as varchar) + '|', '|' + di1.dic_item_code + '|')>0) workflow_status_text,
(SELECT region_name FROM wx_t_region WHERE id = t1.cbi_province_id) AS provice_name			 
		  from wx_t_credit_app_request_form t1
		  LEFT JOIN wx_t_credit_app_customer_finance_info cacfi ON cacfi.form_id = t1.id	  
		 where 1=1
		 <include refid="base_cond"/>
		 <include refid="workflow_cond"/>
    </select>
    <!-- <select id="getCreditAppRequestFullInfoByCondition" parameterType="com.chevron.credit.model.CreditListQueryParam" resultMap="fullInfoMap">

	select carf.*,
		cacfi.*,
		(SELECT region_name FROM wx_t_region WHERE id = carf.cbi_province_id) AS provice_name,
			(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
            (select top 1 customer_name
            from dbo.wx_t_credit_customer_info_final
            where (payer  + '_' + type) = cbi_customer_id ) as cbi_customer_name
		from wx_t_credit_app_request_form carf	
		  	LEFT JOIN wx_t_credit_app_customer_finance_info cacfi ON cacfi.id = carf.cfi_info_id	  	    
		  	left join wx_t_partner_o2o_enterprise poe on carf.cbi_customer_id = poe.sap_code
		  	left join wx_t_organization org on org.id = poe.partner_id
		 where 1 = 1
              <!-如果是登录的这个用户是supervisor及以上->
              <if test="isSupervisorAndAbove">
                  and exists (select distinct
                  dwrsr.sales_channel_name,
                  dwrssr.sales_cai,
                  dwrssr.suppervisor_cai,
                  dwrsr.channel_manager_cai,
                  dwrsr.bu_manager_cai
                  FROM
                  wx_t_organization organ
                  LEFT JOIN wx_t_partner_o2o_enterprise e ON
                  organ.id = e.partner_id
                  LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
                  dwrssr.distributor_id = e.distributor_id
                  LEFT JOIN dw_region_sales_channel_rel dwrsr ON
                  dwrsr.region_name = dwrssr.region_name
                  LEFT JOIN wx_t_user u ON u.cai = dwrssr.sales_cai
                  where 1 = 1
                  and  carf.ai_requested_by like concat(concat('%(',dwrssr.sales_cai),')%' )
                  and (
                  dwrssr.suppervisor_cai = #{cai} or
                  dwrsr.channel_manager_cai = #{cai} or
                  dwrsr.bu_manager_cai = #{cai}
                  ))
              </if>
		     <if test="queryType == 1">
				  <if test="cbiCustomerId != null and cbiCustomerId != ''">
					 and carf.cbi_customer_id like '%' + #{cbiCustomerId,jdbcType=VARCHAR} + '%'
				  </if>
				  <if test="partnerName != null and partnerName != ''">
                    and  exists (select 1
                    from dbo.wx_t_credit_customer_info_final
                    where ((payer  + '_' + type) = cbi_customer_id  or )
                    and customer_name like '%' + #{partnerName,jdbcType=VARCHAR} + '%'
                    )
				  </if>
				  <if test="dateStart != null">
				    and carf.CREATE_TIME &gt;= #{dateStart, jdbcType=TIMESTAMP}
				  </if>
				  <if test="dateEnd != null">
				    and carf.CREATE_TIME &lt;= #{dateEnd, jdbcType=TIMESTAMP}
				  </if>
				  <if test="aiRequestedBy != null and aiRequestedBy != '' ">
					and carf.ai_requested_by like '%' + #{aiRequestedBy,jdbcType=VARCHAR} + '%'
				  </if>
				  <if test="creditAppTypes != null and creditAppTypes.size() > 0 " >
                  	and carf.credit_type in
                  	<foreach collection="creditAppTypes" item="listItem" open="(" close=")" separator="," >
                   	 '${listItem}'
                  	</foreach>
                  </if>
                  <choose>
                  <when test="idList != null and idList.size() >0" >
                  	and carf.id in
                  	<foreach collection="idList" item="listItem" open="(" close=")" separator="," >
                   	 '${listItem}'
                  	</foreach>
                  </when>
                    <otherwise>
                     and 1 != 1
                    </otherwise>
                  </choose>
			 </if>
			 <if test="queryType == 2">
               <choose>
                 <when test="idList != null and idList.size() >0" >
                   and carf.id in
                   <foreach collection="idList" item="listItem" open="(" close=")" separator="," >
                     '${listItem}'
                   </foreach>
                 </when>
                 <otherwise>
                   and 1 != 1
                 </otherwise>
               </choose>
			 	 <if test="queryField!= null and queryField != ''">
			  		and (	carf.cbi_customer_id like '%' + #{queryField} + '%'
                           or exists (select 1
                           from dbo.wx_t_credit_customer_info_final
                           where ((payer  + '_' + type) = cbi_customer_id )
                           and customer_name like '%' + #{queryField} + '%'
                           )
			  				or carf.ai_requested_by like '%' + #{queryField} + '%'
			  			)
			 	 </if>
		 	 </if>
	</select> -->
  <select id="querySalesChannelByUserId" resultType="String" parameterType="java.lang.String">
    select top 1 dwrsr.sales_channel_name
	FROM
	  wx_t_organization o
	  LEFT JOIN wx_t_partner_o2o_enterprise e ON
	                                            o.id = e.partner_id
	  LEFT JOIN dw_customer_region_sales_supervisor_rel dwrssr ON
	                                                             dwrssr.distributor_id = e.distributor_id
	  LEFT JOIN dw_region_sales_channel_rel dwrsr ON
	                                                dwrsr.region_name = dwrssr.region_name
	  LEFT JOIN wx_t_user u ON (dwrssr.sales_cai = u.cai or dwrssr.suppervisor_cai = u.cai
	                            or dwrsr.channel_manager_cai = u.cai or dwrsr.bu_manager_cai = u.cai)
	where 1 = 1
	      and u.cai = '${cai}'
  </select>
	<!-- 基础查询条件 -->
	<sql id="base_cond">
        <if test="queryType == 1">
          <if test="cbiCustomerId != null and cbiCustomerId != ''">
            and t1.cbi_customer_id like '%' + #{cbiCustomerId,jdbcType=VARCHAR} + '%'
          </if>
          <if test="partnerName != null and partnerName != ''">
            and t1.customer_name like '%' + #{partnerName,jdbcType=VARCHAR} + '%'
          </if>
          <if test="dateStart != null and dateStart != ''">
            and t1.ai_request_date &gt;= #{dateStart,jdbcType=VARCHAR}
          </if>
          <if test="dateEnd != null and dateEnd != ''">
            and t1.ai_request_date &lt; dateadd(day, 1, #{dateEnd,jdbcType=VARCHAR})
          </if>
          <if test="aiRequestedBy != null and aiRequestedBy != '' ">
            and t1.ai_requested_by like '%' + #{aiRequestedBy,jdbcType=VARCHAR} + '%'
          </if>
          <if test="creditAppTypes != null and creditAppTypes.length > 0" >
            and t1.credit_type in
            <foreach collection="creditAppTypes" item="listItem" open="(" close=")" separator="," >
              '${listItem}'
            </foreach>
          </if>
        </if>
          <if test="queryType == 2">
            <if test="queryField != null and queryField != ''">
              and (	cbi_customer_id like '%' + #{queryField} + '%'
              or t1.customer_name like '%' + #{queryField} + '%'
              or ai_requested_by like '%' + #{queryField} + '%'
              )
            </if>
          </if>
	</sql>
	<sql id="workflow_cond">
		<choose>
			<when test="fromPage == 'todo'">
				<!-- 待办查看自己的草稿或流程待办列表 -->
				and ((t1.form_status=0 and t1.ai_prepared_by=#{executorId})
				<if test="processInstanceIds != null">
					<foreach collection="processInstanceIds" item="listItem" open=" or t1.process_instance_id in (" close=")" separator="," >
						'${listItem}'
					</foreach>
				</if>
				<if test="includeTobeReleaseForm">
					or (t1.workflow_status=100 and t1.credit_type='CV_REQUEST' and t1.release_order_status is null)
				</if>
				)
			</when>
			<when test="fromPage == 'done'">
				<choose>
					<when test="processInstanceIds != null">
						<foreach collection="processInstanceIds" item="listItem" open=" and t1.process_instance_id in (" close=")" separator="," >
							'${listItem}'
						</foreach>
					</when>
					<otherwise>
					and 1!=1
					</otherwise>
				</choose>
			</when>
			<when test="fromPage == 'all'">
                <!-- 查看所有包括已拒绝 -->
			and (t1.form_status>0 or t1.workflow_status = 30)
			</when>
		</choose>
		<choose>
			<when test="fromRequestor == 'self'">
				and t1.ai_prepared_by=#{executorId}
			</when>
			<when test="fromRequestor == 'others'">
				and t1.ai_prepared_by!=#{executorId}
			</when>
		</choose>
		<if test="workflowStatus != null and workflowStatus != ''">
		and CHARINDEX(',' + cast(t1.workflow_status as varchar) + ',', ',' + REPLACE(#{workflowStatus}, '|', ',') + ',')>0
		</if>
		<if test="requestNo != null and requestNo != ''">
		and t1.request_no=#{requestNo}
		</if>
	</sql>
  	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.process_instance_id, t1.request_no, t1.currency, t1.ai_prepared_by, t1.ai_region_id, t1.ai_request_date, 
		t1.ai_requested_by, t1.ai_telephone, t1.ai_sales_team, t1.cbi_customer_id, t1.cbi_province_id, t1.cbi_cooperation_years_with_cvx, 
    t1.cbi_year_n1_total_sales, t1.cbi_date_establishment, t1.cbi_sales_target_of_cio, t1.cbi_sales_target_of_cdm, 
    t1.cbi_credit_limit_of_year_N1, t1.cbi_payment_term_of_year_N1, t1.cbi_requested_credit_limit_current_year, 
    t1.cbi_requested_payment_term_of_current_year, t1.cbi_current_credit_limit_info_id, t1.cbi_requested_temp_credit_limit, 
    t1.cbi_requested_temp_payment_term, t1.cbi_expire_date, t1.cbi_requested_cv_order_no, t1.cbi_comments_from_BU, 
    t1.cbi_financial_statements_att_id, t1.cbi_cash_deposit_with_amount, t1.cbi_cash_deposit_with_amount_upload_scancopy_id, 
    t1.cbi_cash_deposit_with_amount_valid_date, t1.cbi_the_3rd_party_guarantee_with_amount, 
    t1.cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id, t1.cbi_the_3rd_party_guarantee_with_amount_valid_date, 
    t1.cbi_bank_guarantee_with_amount, t1.cbi_bank_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_bank_guarantee_with_amount_valid_date, t1.cbi_personal_guarantee_with_amount, t1.cbi_personal_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_personal_guarantee_with_amount_valid_date, t1.cfi_info_id, t1.credit_type, t1.create_time, 
    t1.update_time, t1.delete_flag, t1.credit_dollar_rate, t1.current_credit_limit, t1.process_status,
    t1.credit_csr,t1.release_order_status, t1.bu,
			 t1.workflow_status, t1.customer_type, t1.sold_to_code, t1.payer_code, t1.customer_name,
			 t1.direct_annual_sales_plan, t1.indirect_annual_sales_plan, t1.ext_flag, t1.ext_property1, t1.ext_property2,
			 t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.flsr_id, t1.form_status, t1.version_no, t1.sales_channel,
			 t1.locker_id, t1.apply_amount_usd,
		(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
    			 t1.customer_name cbi_customer_name,
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Credit.workflowStatus' and CHARINDEX('|' + cast(t1.workflow_status as varchar) + '|', '|' + di1.dic_item_code + '|')>0) workflow_status_text			 
		  from wx_t_credit_app_request_form t1
		 where 1=1
		 <include refid="base_cond"/>
		 <if test="id != null">
		 and t1.id=#{id}
		 </if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.credit.model.CreditListQueryParam">
		select t1.id, t1.process_instance_id, t1.request_no, t1.currency, t1.ai_prepared_by, t1.ai_region_id, t1.ai_request_date, 
		t1.ai_requested_by, t1.ai_telephone, t1.ai_sales_team, t1.cbi_customer_id, t1.cbi_province_id, t1.cbi_cooperation_years_with_cvx, 
    t1.cbi_year_n1_total_sales, t1.cbi_date_establishment, t1.cbi_sales_target_of_cio, t1.cbi_sales_target_of_cdm, 
    t1.cbi_credit_limit_of_year_N1, t1.cbi_payment_term_of_year_N1, t1.cbi_requested_credit_limit_current_year, 
    t1.cbi_requested_payment_term_of_current_year, t1.cbi_current_credit_limit_info_id, t1.cbi_requested_temp_credit_limit, 
    t1.cbi_requested_temp_payment_term, t1.cbi_expire_date, t1.cbi_requested_cv_order_no, t1.cbi_comments_from_BU, 
    t1.cbi_financial_statements_att_id, t1.cbi_cash_deposit_with_amount, t1.cbi_cash_deposit_with_amount_upload_scancopy_id, 
    t1.cbi_cash_deposit_with_amount_valid_date, t1.cbi_the_3rd_party_guarantee_with_amount, 
    t1.cbi_the_3rd_party_guarantee_with_amount_upload_scancopy_id, t1.cbi_the_3rd_party_guarantee_with_amount_valid_date, 
    t1.cbi_bank_guarantee_with_amount, t1.cbi_bank_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_bank_guarantee_with_amount_valid_date, t1.cbi_personal_guarantee_with_amount, t1.cbi_personal_guarantee_with_amount_upload_scancopy_id, 
    t1.cbi_personal_guarantee_with_amount_valid_date, t1.cfi_info_id, t1.credit_type, t1.create_time, 
    t1.update_time, t1.delete_flag, t1.credit_dollar_rate, t1.current_credit_limit, t1.process_status,
    t1.credit_csr,t1.release_order_status, t1.bu,
			 t1.workflow_status, t1.customer_type, t1.sold_to_code, t1.payer_code, t1.customer_name,
			 t1.direct_annual_sales_plan, t1.indirect_annual_sales_plan, t1.ext_flag, t1.ext_property1, t1.ext_property2,
			 t1.ext_property3, t1.ext_property4, t1.ext_property5, t1.flsr_id, t1.form_status, t1.version_no, t1.sales_channel,
			 t1.locker_id, t1.apply_amount_usd,
		(select ch_name from dbo.wx_t_user where user_id = ai_prepared_by) as ai_prepared_by_name,
    			 t1.customer_name cbi_customer_name,
(select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='Credit.workflowStatus' and CHARINDEX('|' + cast(t1.workflow_status as varchar) + '|', '|' + di1.dic_item_code + '|')>0) workflow_status_text			 
		  from wx_t_credit_app_request_form t1
		 where 1=1
		 <include refid="base_cond"/>
		 <include refid="workflow_cond"/>
	</select>

  	<!-- 构建初始化表单 -->
	<select id="buildInitForm" resultMap="BaseResultMap" parameterType="map">
		select top 1 u.ch_name ai_prepared_by_name, u.user_id ai_prepared_by, credit_bcs1.region ai_sales_team, 
		case when credit_bcs1.sales_cai is not null then u.mobile_tel else '' end ai_telephone, 
		case when credit_bcs1.sales_cai is not null then u.ch_name+'('+credit_bcs1.sales_cai+')' else '' end ai_requested_by,
		u.user_id flsr_id, (select top 1 r1.rate from PP_MID.dbo.bit_rate_by_month r1 order by r1.effective_month desc) credit_dollar_rate
from wx_t_user u 
left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on u.cai=credit_bcs1.sales_cai
 where u.user_id=#{executorId}			 
	</select>
  <select id="buildInitFormByFlsr" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select top 1 credit_rs1.bu, credit_rs1.sales_channel_name sales_channel 
    from wx_t_user u left join [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] credit_bcs1 on u.cai=credit_bcs1.sales_cai
				 left join dw_region_sales_channel_rel credit_rs1 on credit_rs1.region_name=credit_bcs1.region
				 where u.user_id=#{flsrId,jdbcType=BIGINT}
  </select>
  	<select id="getCreditDollarRate" resultType="double">
		select top 1 r1.rate from PP_MID.dbo.bit_rate_by_month r1 order by r1.effective_month desc		 
	</select>
  
</mapper>