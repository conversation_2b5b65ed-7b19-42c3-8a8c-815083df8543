<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.credit.dao.WxTCreditAppRequestFromAttVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="application_id" jdbcType="BIGINT" property="applicationId" />
    <result column="att_column_name" jdbcType="VARCHAR" property="attColumnName" />
    <result column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
		<result column="request_no" property="requestNo" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="AttCountMap" type="com.chevron.credit.model.AttCountInfo">
    <result column="att_column_name" jdbcType="VARCHAR" property="attColumnName" />
    <result column="att_count" jdbcType="BIGINT" property="attCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, application_id, att_column_name, att_id, delete_flag,request_no
  </sql>
  <select id="selectByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_t_credit_app_request_from_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_t_credit_app_request_from_att
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_t_credit_app_request_from_att
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVoExample">
    delete from wx_t_credit_app_request_from_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id"  parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    insert into wx_t_credit_app_request_from_att (id, application_id, att_column_name, 
      att_id, delete_flag)
    values (#{id,jdbcType=BIGINT}, #{applicationId,jdbcType=BIGINT}, #{attColumnName,jdbcType=VARCHAR}, 
      #{attId,jdbcType=BIGINT}, #{deleteFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    insert into wx_t_credit_app_request_from_att
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applicationId != null">
        application_id,
      </if>
      <if test="attColumnName != null">
        att_column_name,
      </if>
      <if test="attId != null">
        att_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
			<if test="requestNo != null">
				request_no,
			</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applicationId != null">
        #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="attColumnName != null">
        #{attColumnName,jdbcType=VARCHAR},
      </if>
      <if test="attId != null">
        #{attId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
			<if test="requestNo != null">
				#{requestNo,jdbcType=VARCHAR},
			</if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVoExample" resultType="java.lang.Long">
    select count(*) from wx_t_credit_app_request_from_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_credit_app_request_from_att
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applicationId != null">
        application_id = #{record.applicationId,jdbcType=BIGINT},
      </if>
      <if test="record.attColumnName != null">
        att_column_name = #{record.attColumnName,jdbcType=VARCHAR},
      </if>
      <if test="record.attId != null">
        att_id = #{record.attId,jdbcType=BIGINT},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_credit_app_request_from_att
    set id = #{record.id,jdbcType=BIGINT},
      application_id = #{record.applicationId,jdbcType=BIGINT},
      att_column_name = #{record.attColumnName,jdbcType=VARCHAR},
      att_id = #{record.attId,jdbcType=BIGINT},
      delete_flag = #{record.deleteFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    update wx_t_credit_app_request_from_att
    <set>
      <if test="applicationId != null">
        application_id = #{applicationId,jdbcType=BIGINT},
      </if>
      <if test="attColumnName != null">
        att_column_name = #{attColumnName,jdbcType=VARCHAR},
      </if>
      <if test="attId != null">
        att_id = #{attId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    update wx_t_credit_app_request_from_att
    set application_id = #{applicationId,jdbcType=BIGINT},
      att_column_name = #{attColumnName,jdbcType=VARCHAR},
      att_id = #{attId,jdbcType=BIGINT},
      delete_flag = #{deleteFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="deleteById" parameterType="com.chevron.credit.model.WxTCreditAppRequestFromAttVo">
    update wx_t_credit_app_request_from_att
        set delete_flag = 1
    where id = #{id,jdbcType=BIGINT}
  </update>
  <resultMap id="CreditAppAttVoMap" type="com.chevron.credit.model.CreditAppAttVo">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="application_id" jdbcType="BIGINT" property="applicationId" />
    <result column="att_column_name" jdbcType="VARCHAR" property="attColumnName" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="att_id" property="attId" jdbcType="BIGINT"/>
    <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
    <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
    <result column="storage_name" property="storageName" jdbcType="VARCHAR"/>
    <result column="file_type" property="fileType" jdbcType="VARCHAR"/>
    <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
    <result column="store_path" property="storePath" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
    <result column="upload_user" property="uploadUser" jdbcType="BIGINT"/>
		<result column="request_no" property="requestNo" jdbcType="VARCHAR"/>
  </resultMap>
  <select id="queryAttListByAttColumnName" parameterType="com.chevron.credit.model.CreditListQueryParam" resultMap="CreditAppAttVoMap">
    select
    form_att.id,
    form_att.application_id,
    form_att.request_no,
    form_att.att_column_name,
    form_att.att_id,
    form_att.delete_flag,
    att_file.source_id,
    att_file.file_name,
    att_file.storage_name,
    att_file.file_type,
    att_file.file_size,
    att_file.upload_user,
    att_file.create_time
    from wx_t_credit_app_request_from_att form_att left join wx_att_file att_file on form_att.att_id = att_file.att_id
    where 1 = 1
    <!--<if test="applicationId != null">
      and form_att.application_id = #{applicationId,jdbcType=BIGINT}
    </if>  -->
    <if test="requestNo != null and requestNo != ''">
      and form_att.request_no = #{requestNo,jdbcType=VARCHAR}
    </if>
    <if test="attColumnName != null">
      and form_att.att_column_name = #{attColumnName,jdbcType=VARCHAR}
    </if>
    <if test="id != null">
      and form_att.id = #{id,jdbcType=BIGINT}
    </if>
    and form_att.delete_flag = 0
  </select>
  <select id="queryAttCount" resultMap="AttCountMap" parameterType="map">
    select
	count(1) att_count,
	att_column_name
    from dbo.wx_t_credit_app_request_from_att
    where 1 = 1 and request_no = #{requestNo,jdbcType=VARCHAR} and delete_flag = 0
    group by att_column_name
  </select>
</mapper>