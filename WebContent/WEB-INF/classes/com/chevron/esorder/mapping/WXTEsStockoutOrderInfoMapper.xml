<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.esorder.dao.WXTEsStockoutOrderInfoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.esorder.model.WXTEsStockoutOrderInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="stockout_id" property="stockoutId" jdbcType="NVARCHAR" />
    <result column="stockout_no" property="stockoutNo" jdbcType="NVARCHAR" />
    <result column="goods_id" property="goodsId" jdbcType="INTEGER" />
    <result column="spec_id" property="specId" jdbcType="INTEGER" />
    <result column="goods_code" property="goodsCode" jdbcType="NVARCHAR" />
    <result column="goods_name" property="goodsName" jdbcType="NVARCHAR" />
    <result column="spec_code" property="specCode" jdbcType="NVARCHAR" />
    <result column="spec_name" property="specName" jdbcType="NVARCHAR" />
    <result column="goods_unit" property="goodsUnit" jdbcType="NVARCHAR" />
    <result column="sell_count" property="sellCount" jdbcType="DECIMAL" />
    <result column="sell_price" property="sellPrice" jdbcType="DECIMAL" />
    <result column="sell_total" property="sellTotal" jdbcType="DECIMAL" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="sid" property="sid" jdbcType="NVARCHAR" />
    <result column="createtime" property="createtime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, stockout_id, stockout_no, goods_id, spec_id, goods_code, goods_name, spec_code, 
    spec_name, goods_unit, sell_count, sell_price, sell_total, remark, sid, createtime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_es_stockout_order_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_es_stockout_order_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_es_stockout_order_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfoExample" >
    delete from wx_t_es_stockout_order_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfo" >
    insert into wx_t_es_stockout_order_info (id, stockout_id, stockout_no, 
      goods_id, spec_id, goods_code, 
      goods_name, spec_code, spec_name, 
      goods_unit, sell_count, sell_price, 
      sell_total, remark, sid, 
      createtime)
    values (#{id,jdbcType=BIGINT}, #{stockoutId,jdbcType=NVARCHAR}, #{stockoutNo,jdbcType=NVARCHAR}, 
      #{goodsId,jdbcType=INTEGER}, #{specId,jdbcType=INTEGER}, #{goodsCode,jdbcType=NVARCHAR}, 
      #{goodsName,jdbcType=NVARCHAR}, #{specCode,jdbcType=NVARCHAR}, #{specName,jdbcType=NVARCHAR}, 
      #{goodsUnit,jdbcType=NVARCHAR}, #{sellCount,jdbcType=DECIMAL}, #{sellPrice,jdbcType=DECIMAL}, 
      #{sellTotal,jdbcType=DECIMAL}, #{remark,jdbcType=NVARCHAR}, #{sid,jdbcType=NVARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfo" >
    insert into wx_t_es_stockout_order_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="stockoutId != null" >
        stockout_id,
      </if>
      <if test="stockoutNo != null" >
        stockout_no,
      </if>
      <if test="goodsId != null" >
        goods_id,
      </if>
      <if test="specId != null" >
        spec_id,
      </if>
      <if test="goodsCode != null" >
        goods_code,
      </if>
      <if test="goodsName != null" >
        goods_name,
      </if>
      <if test="specCode != null" >
        spec_code,
      </if>
      <if test="specName != null" >
        spec_name,
      </if>
      <if test="goodsUnit != null" >
        goods_unit,
      </if>
      <if test="sellCount != null" >
        sell_count,
      </if>
      <if test="sellPrice != null" >
        sell_price,
      </if>
      <if test="sellTotal != null" >
        sell_total,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="sid != null" >
        sid,
      </if>
      <if test="createtime != null" >
        createtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="stockoutId != null" >
        #{stockoutId,jdbcType=NVARCHAR},
      </if>
      <if test="stockoutNo != null" >
        #{stockoutNo,jdbcType=NVARCHAR},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="specId != null" >
        #{specId,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null" >
        #{goodsCode,jdbcType=NVARCHAR},
      </if>
      <if test="goodsName != null" >
        #{goodsName,jdbcType=NVARCHAR},
      </if>
      <if test="specCode != null" >
        #{specCode,jdbcType=NVARCHAR},
      </if>
      <if test="specName != null" >
        #{specName,jdbcType=NVARCHAR},
      </if>
      <if test="goodsUnit != null" >
        #{goodsUnit,jdbcType=NVARCHAR},
      </if>
      <if test="sellCount != null" >
        #{sellCount,jdbcType=DECIMAL},
      </if>
      <if test="sellPrice != null" >
        #{sellPrice,jdbcType=DECIMAL},
      </if>
      <if test="sellTotal != null" >
        #{sellTotal,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="sid != null" >
        #{sid,jdbcType=NVARCHAR},
      </if>
      <if test="createtime != null" >
        #{createtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_es_stockout_order_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.stockoutId != null" >
        stockout_id = #{record.stockoutId,jdbcType=NVARCHAR},
      </if>
      <if test="record.stockoutNo != null" >
        stockout_no = #{record.stockoutNo,jdbcType=NVARCHAR},
      </if>
      <if test="record.goodsId != null" >
        goods_id = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.specId != null" >
        spec_id = #{record.specId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsCode != null" >
        goods_code = #{record.goodsCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.goodsName != null" >
        goods_name = #{record.goodsName,jdbcType=NVARCHAR},
      </if>
      <if test="record.specCode != null" >
        spec_code = #{record.specCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.specName != null" >
        spec_name = #{record.specName,jdbcType=NVARCHAR},
      </if>
      <if test="record.goodsUnit != null" >
        goods_unit = #{record.goodsUnit,jdbcType=NVARCHAR},
      </if>
      <if test="record.sellCount != null" >
        sell_count = #{record.sellCount,jdbcType=DECIMAL},
      </if>
      <if test="record.sellPrice != null" >
        sell_price = #{record.sellPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.sellTotal != null" >
        sell_total = #{record.sellTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
      <if test="record.sid != null" >
        sid = #{record.sid,jdbcType=NVARCHAR},
      </if>
      <if test="record.createtime != null" >
        createtime = #{record.createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_es_stockout_order_info
    set id = #{record.id,jdbcType=BIGINT},
      stockout_id = #{record.stockoutId,jdbcType=NVARCHAR},
      stockout_no = #{record.stockoutNo,jdbcType=NVARCHAR},
      goods_id = #{record.goodsId,jdbcType=INTEGER},
      spec_id = #{record.specId,jdbcType=INTEGER},
      goods_code = #{record.goodsCode,jdbcType=NVARCHAR},
      goods_name = #{record.goodsName,jdbcType=NVARCHAR},
      spec_code = #{record.specCode,jdbcType=NVARCHAR},
      spec_name = #{record.specName,jdbcType=NVARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=NVARCHAR},
      sell_count = #{record.sellCount,jdbcType=DECIMAL},
      sell_price = #{record.sellPrice,jdbcType=DECIMAL},
      sell_total = #{record.sellTotal,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=NVARCHAR},
      sid = #{record.sid,jdbcType=NVARCHAR},
      createtime = #{record.createtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfo" >
    update wx_t_es_stockout_order_info
    <set >
      <if test="stockoutId != null" >
        stockout_id = #{stockoutId,jdbcType=NVARCHAR},
      </if>
      <if test="stockoutNo != null" >
        stockout_no = #{stockoutNo,jdbcType=NVARCHAR},
      </if>
      <if test="goodsId != null" >
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="specId != null" >
        spec_id = #{specId,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null" >
        goods_code = #{goodsCode,jdbcType=NVARCHAR},
      </if>
      <if test="goodsName != null" >
        goods_name = #{goodsName,jdbcType=NVARCHAR},
      </if>
      <if test="specCode != null" >
        spec_code = #{specCode,jdbcType=NVARCHAR},
      </if>
      <if test="specName != null" >
        spec_name = #{specName,jdbcType=NVARCHAR},
      </if>
      <if test="goodsUnit != null" >
        goods_unit = #{goodsUnit,jdbcType=NVARCHAR},
      </if>
      <if test="sellCount != null" >
        sell_count = #{sellCount,jdbcType=DECIMAL},
      </if>
      <if test="sellPrice != null" >
        sell_price = #{sellPrice,jdbcType=DECIMAL},
      </if>
      <if test="sellTotal != null" >
        sell_total = #{sellTotal,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="sid != null" >
        sid = #{sid,jdbcType=NVARCHAR},
      </if>
      <if test="createtime != null" >
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.esorder.model.WXTEsStockoutOrderInfo" >
    update wx_t_es_stockout_order_info
    set stockout_id = #{stockoutId,jdbcType=NVARCHAR},
      stockout_no = #{stockoutNo,jdbcType=NVARCHAR},
      goods_id = #{goodsId,jdbcType=INTEGER},
      spec_id = #{specId,jdbcType=INTEGER},
      goods_code = #{goodsCode,jdbcType=NVARCHAR},
      goods_name = #{goodsName,jdbcType=NVARCHAR},
      spec_code = #{specCode,jdbcType=NVARCHAR},
      spec_name = #{specName,jdbcType=NVARCHAR},
      goods_unit = #{goodsUnit,jdbcType=NVARCHAR},
      sell_count = #{sellCount,jdbcType=DECIMAL},
      sell_price = #{sellPrice,jdbcType=DECIMAL},
      sell_total = #{sellTotal,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=NVARCHAR},
      sid = #{sid,jdbcType=NVARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <insert id="insertBatchSelective"  parameterType="java.util.List" >
		insert into wx_t_es_stockout_order_info (stockout_id, stockout_no, 
		      goods_id, spec_id, goods_code, 
		      goods_name, spec_code, spec_name, 
		      goods_unit, sell_count, sell_price, 
		      sell_total, remark, sid, 
		      createtime)
      	 values
	    <foreach collection="list" index="index" item="item"
			separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			  #{item.stockoutId,jdbcType=NVARCHAR}, #{item.stockoutNo,jdbcType=NVARCHAR}, 
		      #{item.goodsId,jdbcType=INTEGER}, #{item.specId,jdbcType=INTEGER}, #{item.goodsCode,jdbcType=NVARCHAR}, 
		      #{item.goodsName,jdbcType=NVARCHAR}, #{item.specCode,jdbcType=NVARCHAR}, #{item.specName,jdbcType=NVARCHAR}, 
		      #{item.goodsUnit,jdbcType=NVARCHAR}, #{item.sellCount,jdbcType=DECIMAL}, #{item.sellPrice,jdbcType=DECIMAL}, 
		      #{item.sellTotal,jdbcType=DECIMAL}, #{item.remark,jdbcType=NVARCHAR}, #{item.sid,jdbcType=NVARCHAR}, 
		      #{item.createtime,jdbcType=TIMESTAMP}
			</trim>
		</foreach>
  </insert>
</mapper>