<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.newhomepagev2.dao.HomePageLinkConfigMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.newhomepagev2.model.HomePageLinkConfig">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="user_id" property="userId" jdbcType="BIGINT"/>
		<result column="menu_id" property="menuId" jdbcType="BIGINT"/>
		<result column="home_page_id" property="homePageId" jdbcType="BIGINT"/>
		<result column="menu_alias" property="menuAlias" jdbcType="VARCHAR"/>
		<result column="sort" property="sort" jdbcType="NUMERIC"/>
		<result column="ext_property1" property="extProperty1" jdbcType="VARCHAR"/>
		<result column="ext_property2" property="extProperty2" jdbcType="VARCHAR"/>
		<result column="ext_property3" property="extProperty3" jdbcType="VARCHAR"/>
		<result column="ext_property4" property="extProperty4" jdbcType="VARCHAR"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,user_id,menu_id,home_page_id,menu_alias,sort,ext_property1,ext_property2,ext_property3,ext_property4,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfig">
		update wx_t_home_page_link_config set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfig">
		update wx_t_home_page_link_config
		<set>
			<if test="userId != null" >
				user_id = #{userId,jdbcType=BIGINT},
			</if>
			<if test="menuId != null" >
				menu_id = #{menuId,jdbcType=BIGINT},
			</if>
			<if test="homePageId != null" >
				home_page_id = #{homePageId,jdbcType=BIGINT},
			</if>
			<if test="menuAlias != null" >
				menu_alias = #{menuAlias,jdbcType=VARCHAR},
			</if>
			<if test="sort != null" >
				sort = #{sort,jdbcType=NUMERIC},
			</if>
			<if test="extProperty1 != null" >
				ext_property1 = #{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null" >
				ext_property2 = #{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null" >
				ext_property3 = #{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null" >
				ext_property4 = #{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample">
    	delete from wx_t_home_page_link_config
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfig" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_home_page_link_config
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userId != null">
				user_id,
			</if>
			<if test="menuId != null">
				menu_id,
			</if>
			<if test="homePageId != null">
				home_page_id,
			</if>
			<if test="menuAlias != null">
				menu_alias,
			</if>
			<if test="sort != null">
				sort,
			</if>
			<if test="extProperty1 != null">
				ext_property1,
			</if>
			<if test="extProperty2 != null">
				ext_property2,
			</if>
			<if test="extProperty3 != null">
				ext_property3,
			</if>
			<if test="extProperty4 != null">
				ext_property4,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userId != null">
				#{userId,jdbcType=BIGINT},
			</if>
			<if test="menuId != null">
				#{menuId,jdbcType=BIGINT},
			</if>
			<if test="homePageId != null">
				#{homePageId,jdbcType=BIGINT},
			</if>
			<if test="menuAlias != null">
				#{menuAlias,jdbcType=VARCHAR},
			</if>
			<if test="sort != null">
				#{sort,jdbcType=NUMERIC},
			</if>
			<if test="extProperty1 != null">
				#{extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="extProperty2 != null">
				#{extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="extProperty3 != null">
				#{extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="extProperty4 != null">
				#{extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_home_page_link_config
		<set>
			<if test="record.userId != null">
				user_id = #{record.userId,jdbcType=BIGINT},
			</if>
			<if test="record.menuId != null">
				menu_id = #{record.menuId,jdbcType=BIGINT},
			</if>
			<if test="record.homePageId != null">
				home_page_id = #{record.homePageId,jdbcType=BIGINT},
			</if>
			<if test="record.menuAlias != null">
				menu_alias = #{record.menuAlias,jdbcType=VARCHAR},
			</if>
			<if test="record.sort != null">
				sort = #{record.sort,jdbcType=NUMERIC},
			</if>
			<if test="record.extProperty1 != null">
				ext_property1 = #{record.extProperty1,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty2 != null">
				ext_property2 = #{record.extProperty2,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty3 != null">
				ext_property3 = #{record.extProperty3,jdbcType=VARCHAR},
			</if>
			<if test="record.extProperty4 != null">
				ext_property4 = #{record.extProperty4,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample">
		delete from wx_t_home_page_link_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample" resultType="int">
		select count(1) from wx_t_home_page_link_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_home_page_link_config
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_home_page_link_config
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.user_id, t1.menu_id, t1.home_page_id, t1.menu_alias, t1.sort, t1.ext_property1, t1.ext_property2,
			 t1.ext_property3, t1.ext_property4, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_home_page_link_config t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_home_page_link_config (user_id, menu_id, home_page_id, menu_alias, sort, ext_property1, ext_property2, ext_property3, ext_property4, create_user_id, create_time, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.userId, jdbcType=BIGINT}, #{item.menuId, jdbcType=BIGINT}, #{item.homePageId, jdbcType=BIGINT}, #{item.menuAlias, jdbcType=VARCHAR}, #{item.sort, jdbcType=NUMERIC}, #{item.extProperty1, jdbcType=VARCHAR}, #{item.extProperty2, jdbcType=VARCHAR}, #{item.extProperty3, jdbcType=VARCHAR}, #{item.extProperty4, jdbcType=VARCHAR}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>

    <!-- 查询满足Example条件的记录集合 -->
    <select id="selectByUseAbleConfig" resultMap="BaseResultMap" parameterType="com.chevron.newhomepagev2.model.HomePageLinkConfigExample">
        select
            t1.id, t1.user_id, t1.menu_id, t1.home_page_id, t1.menu_alias, t1.sort, t1.ext_property1, t1.ext_property2,
            t1.ext_property3, t1.ext_property4, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
        from wx_t_home_page_link_config t1
        left join wx_t_menu m on t1.menu_id = m.menu_id and m.ext_flag <![CDATA[ & ]]>  1 > 0
        where t1.user_id = #{userId} and (t1.menu_id is null or m.status = 1)
    </select>
</mapper>
