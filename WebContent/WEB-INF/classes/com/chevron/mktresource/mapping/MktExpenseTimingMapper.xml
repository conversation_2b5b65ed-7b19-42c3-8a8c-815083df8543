<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.mktresource.dao.MktExpenseTimingMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.mktresource.model.MktExpenseTiming">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="year" property="year" jdbcType="INTEGER"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<result column="expense_code" property="expenseCode" jdbcType="VARCHAR"/>
		<result column="start_timing_time" property="startTimingTime" jdbcType="TIMESTAMP"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,year,region_name,sales_channel,expense_code,start_timing_time,create_user_id,create_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.mktresource.model.MktExpenseTiming">
		update wx_t_mkt_expense_timing set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.mktresource.model.MktExpenseTiming">
		update wx_t_mkt_expense_timing
		<set>
			<if test="year != null" >
				year = #{year,jdbcType=INTEGER},
			</if>
			<if test="regionName != null" >
				region_name = #{regionName,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null" >
				sales_channel = #{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="expenseCode != null" >
				expense_code = #{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="startTimingTime != null" >
				start_timing_time = #{startTimingTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.mktresource.model.MktExpenseTimingExample">
    	delete from wx_t_mkt_expense_timing
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.mktresource.model.MktExpenseTiming" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_mkt_expense_timing
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="year != null">
				year,
			</if>
			<if test="regionName != null">
				region_name,
			</if>
			<if test="salesChannel != null">
				sales_channel,
			</if>
			<if test="expenseCode != null">
				expense_code,
			</if>
			<if test="startTimingTime != null">
				start_timing_time,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="year != null">
				#{year,jdbcType=INTEGER},
			</if>
			<if test="regionName != null">
				#{regionName,jdbcType=VARCHAR},
			</if>
			<if test="salesChannel != null">
				#{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="expenseCode != null">
				#{expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="startTimingTime != null">
				#{startTimingTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_mkt_expense_timing
		<set>
			<if test="record.year != null">
				year = #{record.year,jdbcType=INTEGER},
			</if>
			<if test="record.regionName != null">
				region_name = #{record.regionName,jdbcType=VARCHAR},
			</if>
			<if test="record.salesChannel != null">
				sales_channel = #{record.salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.expenseCode != null">
				expense_code = #{record.expenseCode,jdbcType=VARCHAR},
			</if>
			<if test="record.startTimingTime != null">
				start_timing_time = #{record.startTimingTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.mktresource.model.MktExpenseTimingExample">
		delete from wx_t_mkt_expense_timing
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.mktresource.model.MktExpenseTimingExample" resultType="int">
		select count(1) from wx_t_mkt_expense_timing
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktExpenseTimingExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_mkt_expense_timing
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktExpenseTimingExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_mkt_expense_timing
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.year, t1.region_name, t1.sales_channel, t1.expense_code, t1.start_timing_time, t1.create_user_id,
			 t1.create_time
		  from wx_t_mkt_expense_timing t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_mkt_expense_timing (year, region_name, sales_channel, expense_code, start_timing_time, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.year, jdbcType=INTEGER}, #{item.regionName, jdbcType=VARCHAR}, #{item.salesChannel, jdbcType=VARCHAR}, #{item.expenseCode, jdbcType=VARCHAR}, #{item.startTimingTime, jdbcType=TIMESTAMP}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
