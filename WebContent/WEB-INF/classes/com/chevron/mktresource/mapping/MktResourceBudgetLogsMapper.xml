<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.mktresource.dao.MktResourceBudgetLogsMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.mktresource.model.MktResourceBudgetLogs">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="resource_key" property="resourceKey" jdbcType="VARCHAR"/>
		<result column="log_type" property="logType" jdbcType="INTEGER"/>
		<result column="value_offset" property="valueOffset" jdbcType="NUMERIC"/>
		<result column="value_final" property="valueFinal" jdbcType="NUMERIC"/>
		<result column="inputUserPermissionWeight" property="inputuserpermissionweight" jdbcType="INTEGER"/>
		<result column="year" property="year" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,resource_key,log_type,value_offset,value_final,inputUserPermissionWeight,year,create_user_id,create_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogs">
		update wx_t_mkt_resource_budget_logs set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogs">
		update wx_t_mkt_resource_budget_logs
		<set>
			<if test="resourceKey != null" >
				resource_key = #{resourceKey,jdbcType=VARCHAR},
			</if>
			<if test="logType != null" >
				log_type = #{logType,jdbcType=INTEGER},
			</if>
			<if test="valueOffset != null" >
				value_offset = #{valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="valueFinal != null" >
				value_final = #{valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="inputuserpermissionweight != null" >
				inputUserPermissionWeight = #{inputuserpermissionweight,jdbcType=INTEGER},
			</if>
			<if test="year != null" >
				year = #{year,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsExample">
    	delete from wx_t_mkt_resource_budget_logs
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogs">
		insert into wx_t_mkt_resource_budget_logs
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="resourceKey != null">
				resource_key,
			</if>
			<if test="logType != null">
				log_type,
			</if>
			<if test="valueOffset != null">
				value_offset,
			</if>
			<if test="valueFinal != null">
				value_final,
			</if>
			<if test="inputuserpermissionweight != null">
				inputUserPermissionWeight,
			</if>
			<if test="year != null">
				year,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="resourceKey != null">
				#{resourceKey,jdbcType=VARCHAR},
			</if>
			<if test="logType != null">
				#{logType,jdbcType=INTEGER},
			</if>
			<if test="valueOffset != null">
				#{valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="valueFinal != null">
				#{valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="inputuserpermissionweight != null">
				#{inputuserpermissionweight,jdbcType=INTEGER},
			</if>
			<if test="year != null">
				#{year,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_mkt_resource_budget_logs
		<set>
			<if test="record.resourceKey != null">
				resource_key = #{record.resourceKey,jdbcType=VARCHAR},
			</if>
			<if test="record.logType != null">
				log_type = #{record.logType,jdbcType=INTEGER},
			</if>
			<if test="record.valueOffset != null">
				value_offset = #{record.valueOffset,jdbcType=NUMERIC},
			</if>
			<if test="record.valueFinal != null">
				value_final = #{record.valueFinal,jdbcType=NUMERIC},
			</if>
			<if test="record.inputuserpermissionweight != null">
				inputUserPermissionWeight = #{record.inputuserpermissionweight,jdbcType=INTEGER},
			</if>
			<if test="record.year != null">
				year = #{record.year,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsExample">
		delete from wx_t_mkt_resource_budget_logs
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsExample" resultType="int">
		select count(1) from wx_t_mkt_resource_budget_logs
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_mkt_resource_budget_logs
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_mkt_resource_budget_logs
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.resource_key, t1.log_type, t1.value_offset, t1.value_final, t1.inputUserPermissionWeight, t1.year,
			 t1.create_user_id, t1.create_time
		  from wx_t_mkt_resource_budget_logs t1
		 where 1=1
	</select>
	
	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResourceBudgetLogsParams">
		select t1.id, t1.resource_key, t1.log_type, t1.value_offset, t1.value_final, t1.inputUserPermissionWeight, t1.year,
			 t1.create_user_id, t1.create_time,u.ch_name create_user_name
		  from wx_t_mkt_resource_budget_logs t1
		  left join wx_t_user u on t1.create_user_id=u.user_id
		 where 1=1
		 <if test="resourceKey != null and resourceKey != ''">
			and t1.resource_key = #{resourceKey, jdbcType=VARCHAR}
		</if>
		 <if test="year != null">
			and t1.year = #{year, jdbcType=INTEGER}
		</if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_mkt_resource_budget_logs (resource_key, log_type, value_offset, value_final, inputUserPermissionWeight, year, create_user_id, create_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			 #{item.resourceKey, jdbcType=VARCHAR}, #{item.logType, jdbcType=INTEGER}, #{item.valueOffset, jdbcType=NUMERIC}, #{item.valueFinal, jdbcType=NUMERIC}, #{item.inputuserpermissionweight, jdbcType=INTEGER}, #{item.year, jdbcType=INTEGER}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
