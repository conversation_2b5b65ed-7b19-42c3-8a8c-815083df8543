<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.mktresource.dao.MktDealerQuarterMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.mktresource.model.MktDealerQuarter">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
		<result column="year" property="year" jdbcType="INTEGER"/>
		<result column="q1" property="q1" jdbcType="NUMERIC"/>
		<result column="q2" property="q2" jdbcType="NUMERIC"/>
		<result column="q3" property="q3" jdbcType="NUMERIC"/>
		<result column="q4" property="q4" jdbcType="NUMERIC"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="customer_name_cn" property="distributorName" jdbcType="VARCHAR"/>
		<result column="total_plan_volumn" property="totalPlanVolume" jdbcType="VARCHAR"/>
		<result column="total_volume" property="totalVolume" jdbcType="VARCHAR"/>
		<result column="total_snAbove_volume" property="totalSnAboveVolume" jdbcType="VARCHAR"/>
		
	</resultMap>
	
	<resultMap id="BaseResultRegionTotal" type="com.chevron.mktresource.model.MktRegionTotalVo">
		<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
		<result column="budget_total" property="budgetTotal" jdbcType="NUMERIC"/>
        <result column="sales_cai" property="flsrCai" jdbcType="VARCHAR"/>
	</resultMap>
	
	<resultMap id="BaseResultDistributorTotal" type="com.chevron.mktresource.model.MktDistributorVo">
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
		<result column="distributor_num" property="distributorNum" jdbcType="BIGINT"/>
		<result column="act_liters" property="actLiters" jdbcType="BIGINT"/>
	</resultMap>
	
	<resultMap id="BaseResultSpDistributorTotal" type="com.chevron.mktresource.model.MktDealerQuarterVo">
		<result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
	    <result column="customer_name_cn" property="distributorName" jdbcType="VARCHAR"/>
		<result column="allocate_budget_total" property="allocateBudgetTotal" jdbcType="NUMERIC"/>
		<result column="used_budget_total" property="usedBudgetTotal" jdbcType="NUMERIC"/>
		<result column="remain_budget_total" property="remainBudgetTotal" jdbcType="NUMERIC"/>
	</resultMap>
	
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,distributor_id,sales_channel,year,q1,q2,q3,q4,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.mktresource.model.MktDealerQuarter">
		update wx_t_mkt_dealer_quarter set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.mktresource.model.MktDealerQuarter">
		update wx_t_mkt_dealer_quarter
		<set>
			<if test="distributorId != null" >
				distributor_id = #{distributorId,jdbcType=BIGINT},
			</if>
			<if test="salesChannel != null" >
				sales_channel = #{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="year != null" >
				year = #{year,jdbcType=INTEGER},
			</if>
			<if test="q1 != null" >
				q1 = #{q1,jdbcType=NUMERIC},
			</if>
			<if test="q2 != null" >
				q2 = #{q2,jdbcType=NUMERIC},
			</if>
			<if test="q3 != null" >
				q3 = #{q3,jdbcType=NUMERIC},
			</if>
			<if test="q4 != null" >
				q4 = #{q4,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.mktresource.model.MktDealerQuarterExample">
    	delete from wx_t_mkt_dealer_quarter
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.mktresource.model.MktDealerQuarter" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_mkt_dealer_quarter
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="distributorId != null">
				distributor_id,
			</if>
			<if test="salesChannel != null">
				sales_channel,
			</if>
			<if test="year != null">
				year,
			</if>
			<if test="q1 != null">
				q1,
			</if>
			<if test="q2 != null">
				q2,
			</if>
			<if test="q3 != null">
				q3,
			</if>
			<if test="q4 != null">
				q4,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="distributorId != null">
				#{distributorId,jdbcType=BIGINT},
			</if>
			<if test="salesChannel != null">
				#{salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="year != null">
				#{year,jdbcType=INTEGER},
			</if>
			<if test="q1 != null">
				#{q1,jdbcType=NUMERIC},
			</if>
			<if test="q2 != null">
				#{q2,jdbcType=NUMERIC},
			</if>
			<if test="q3 != null">
				#{q3,jdbcType=NUMERIC},
			</if>
			<if test="q4 != null">
				#{q4,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_mkt_dealer_quarter
		<set>
			<if test="record.distributorId != null">
				distributor_id = #{record.distributorId,jdbcType=BIGINT},
			</if>
			<if test="record.salesChannel != null">
				sales_channel = #{record.salesChannel,jdbcType=VARCHAR},
			</if>
			<if test="record.year != null">
				year = #{record.year,jdbcType=INTEGER},
			</if>
			<if test="record.q1 != null">
				q1 = #{record.q1,jdbcType=NUMERIC},
			</if>
			<if test="record.q2 != null">
				q2 = #{record.q2,jdbcType=NUMERIC},
			</if>
			<if test="record.q3 != null">
				q3 = #{record.q3,jdbcType=NUMERIC},
			</if>
			<if test="record.q4 != null">
				q4 = #{record.q4,jdbcType=NUMERIC},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.mktresource.model.MktDealerQuarterExample">
		delete from wx_t_mkt_dealer_quarter
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.mktresource.model.MktDealerQuarterExample" resultType="int">
		select count(1) from wx_t_mkt_dealer_quarter
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktDealerQuarterExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_mkt_dealer_quarter
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktDealerQuarterExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_mkt_dealer_quarter
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.distributor_id, t1.sales_channel, t1.year, t1.q1, t1.q2, t1.q3, t1.q4, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_mkt_dealer_quarter t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktDealerQuarterParams">
		select t1.id, t1.distributor_id, t1.sales_channel, t1.year, t1.q1, t1.q2, t1.q3, t1.q4, t1.create_user_id,
			 t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_mkt_dealer_quarter t1
		 where 1=1
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_mkt_dealer_quarter (distributor_id, sales_channel, year, q1, q2, q3, q4, create_user_id, create_time, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.distributorId, jdbcType=BIGINT}, #{item.salesChannel, jdbcType=VARCHAR}, #{item.year, jdbcType=INTEGER}, #{item.q1, jdbcType=NUMERIC}, #{item.q2, jdbcType=NUMERIC}, #{item.q3, jdbcType=NUMERIC}, #{item.q4, jdbcType=NUMERIC}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
	<select id="queryListByParams" resultMap="BaseResultMap" parameterType="map">
		select distinct crss.region_name,crss.distributor_id,
		crss.customer_name_cn, t1.id, t1.sales_channel,
		t1.year, t1.q1, t1.q2,
		t1.q3, t1.q4, t1.create_user_id,
		t1.create_time, t1.update_user_id,
		t1.update_time,
		(select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='Plan' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_plan_volumn,
	   (select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='Total' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_volume,
		(select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='SN_Above' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_snAbove_volume
		from view_customer_region_sales_channel crss
		JOIN
		wx_t_partner_o2o_enterprise pe ON pe.distributor_id =
		crss.distributor_id
		JOIN wx_t_organization o ON o.id = pe.partner_id
		left join wx_t_mkt_dealer_quarter t1 on pe.distributor_id =
		t1.distributor_id
		<if test="salesChannel !=null and salesChannel !=''">
		  and t1.sales_channel = #{salesChannel}
		</if>
		<if test="year !=null ">
		  and t1.year = #{year}
		</if>
		where 1=1 and exists
		(select t.id from wx_t_dic_item t where 1=1 and
		t.dic_type_code='mkt_sp_distributer' and
		t.sort_numb=crss.distributor_id and t.status=1
		<if test="salesChannel !=null and salesChannel !=''">
		   and t.dic_item_desc = #{salesChannel}
		</if>
		)
		<if test="regionName !=null and regionName !=''">
		   and crss.region_name = #{regionName}
		</if>
		<if test="suppervisorCai !=null and suppervisorCai !=''">
				and crss.suppervisor_cai = #{suppervisorCai}
	     </if>
	     <if test="salesCai !=null and salesCai !=''">
				and crss.sales_cai = #{salesCai}
	     </if>
	     <if test="teamLeaderCai !=null and teamLeaderCai!=''">
	        and crss.sales_cai in (select t.sales_cai from dw_sales_role t where 1=1 and t.sales_cai_level like '%'+#{teamLeaderCai,jdbcType=VARCHAR}+'%')
	     </if>
	     <if test="regionList!=null and regionList.size() >0">
           and crss.region_name in 
        <foreach item="region" index="index" collection="regionList" open="(" separator="," close=")">  
             #{region}  
        </foreach> 
        </if>
	</select>
	
<select id="queryRegionTotal" resultMap="BaseResultRegionTotal" parameterType="map">
	select t2.region_name,isnull(SUM(t2.q1),0) + isnull(SUM(t2.q2),0)+isnull(SUM(t2.q3),0)+isnull(SUM(t2.q4),0) as budget_total from (
      select distinct crss.region_name,crss.distributor_id,
		crss.customer_name_cn, t1.id, t1.sales_channel,
		t1.year, t1.q1, t1.q2,
		t1.q3, t1.q4, t1.create_user_id,
		t1.create_time, t1.update_user_id,
		t1.update_time
		from view_customer_region_sales_channel crss
		JOIN
		wx_t_partner_o2o_enterprise pe ON pe.distributor_id =
		crss.distributor_id
		JOIN wx_t_organization o ON o.id = pe.partner_id
		left join wx_t_mkt_dealer_quarter t1 
		on (crss.channel_weight &amp; (case when t1.sales_channel='Consumer' then 1 when t1.sales_channel='Commercial' then 2 else 3 end)) &gt; 0  and pe.distributor_id =t1.distributor_id
		<if test="salesChannel !=null and salesChannel !=''">
		  and t1.sales_channel = #{salesChannel}
		</if>
		<if test="year !=null ">
		  and t1.year = #{year}
		</if>
		where 1=1 and exists
		(select t.id from wx_t_dic_item t where 1=1 and
		t.dic_type_code='mkt_sp_distributer' and
		t.sort_numb=crss.distributor_id and t.status=1
		<if test="salesChannel !=null and salesChannel !=''">
		   and t.dic_item_desc = #{salesChannel}
		</if>
		)
		<if test="regionName !=null and regionName !=''">
		   and crss.region_name = #{regionName}
		</if>
		<if test="suppervisorCai !=null and suppervisorCai !=''">
				and crss.suppervisor_cai = #{suppervisorCai}
	     </if>
	     <if test="salesCai !=null and salesCai !=''">
				and crss.sales_cai = #{salesCai}
	     </if>
	     <if test="channelWeights !=null">
				and crss.channel_weight &amp; #{channelWeights,jdbcType=INTEGER} &gt;0
	     </if>
	     <if test="distributorId !=null">
				and crss.distributor_id = #{distributorId}
	     </if>
	     <if test="teamLeaderCai !=null and teamLeaderCai!=''">
	         and crss.sales_cai in (select t.sales_cai from dw_sales_role t where 1=1 and t.sales_cai_level like '%'+#{teamLeaderCai,jdbcType=VARCHAR}+'%')
	     </if>
	      <if test="regionList!=null and regionList.size() >0">
           and crss.region_name in 
        <foreach item="region" index="index" collection="regionList" open="(" separator="," close=")">  
             #{region}  
        </foreach> 
        </if>
		)t2 group by t2.region_name
	</select>

    <select id="querySalesTotal" resultMap="BaseResultRegionTotal" parameterType="map">
        select t2.region_name,t2.sales_cai,isnull(SUM(t2.q1),0) + isnull(SUM(t2.q2),0)+isnull(SUM(t2.q3),0)+isnull(SUM(t2.q4),0) as budget_total from (
        select distinct crss.region_name,crss.distributor_id,
        crss.customer_name_cn, t1.id, t1.sales_channel, crss.sales_cai,
        t1.year, t1.q1, t1.q2,
        t1.q3, t1.q4, t1.create_user_id,
        t1.create_time, t1.update_user_id,
        t1.update_time
        from view_customer_region_sales_channel crss
        JOIN
        wx_t_partner_o2o_enterprise pe ON pe.distributor_id =
        crss.distributor_id
        JOIN wx_t_organization o ON o.id = pe.partner_id
        left join wx_t_mkt_dealer_quarter t1
        on (crss.channel_weight &amp; (case when t1.sales_channel='Consumer' then 1 when t1.sales_channel='Commercial' then 2 else 3 end)) &gt; 0  and pe.distributor_id =t1.distributor_id
        <if test="salesChannel !=null and salesChannel !=''">
            and t1.sales_channel = #{salesChannel}
        </if>
        <if test="year !=null ">
            and t1.year = #{year}
        </if>
        where 1=1 and exists
        (select t.id from wx_t_dic_item t where 1=1 and
        t.dic_type_code='mkt_sp_distributer' and
        t.sort_numb=crss.distributor_id and t.status=1
        <if test="salesChannel !=null and salesChannel !=''">
            and t.dic_item_desc = #{salesChannel}
        </if>
        )
        <if test="regionName !=null and regionName !=''">
            and crss.region_name = #{regionName}
        </if>
        <if test="suppervisorCai !=null and suppervisorCai !=''">
            and crss.suppervisor_cai = #{suppervisorCai}
        </if>
        <if test="salesCai !=null and salesCai !=''">
            and crss.sales_cai = #{salesCai}
        </if>
        <if test="channelWeights !=null">
            and crss.channel_weight &amp; #{channelWeights,jdbcType=INTEGER} &gt;0
        </if>
        <if test="distributorId !=null">
            and crss.distributor_id = #{distributorId}
        </if>
        <if test="teamLeaderCai !=null and teamLeaderCai!=''">
            and crss.sales_cai in (select t.sales_cai from dw_sales_role t where 1=1 and t.sales_cai_level like '%'+#{teamLeaderCai,jdbcType=VARCHAR}+'%')
        </if>
        <if test="regionList!=null and regionList.size() >0">
            and crss.region_name in
            <foreach item="region" index="index" collection="regionList" open="(" separator="," close=")">
                #{region}
            </foreach>
        </if>
        )t2 group by t2.region_name,t2.sales_cai
    </select>
	
	<select id="queryDistributorTotal" resultMap="BaseResultDistributorTotal" parameterType="map">
	   select distinct crss.distributor_id from view_customer_region_sales_channel crss 
        where 1=1 and exists
		(select t.id from wx_t_dic_item t where 1=1 and
		t.dic_type_code='mkt_sp_distributer' and
		t.sort_numb=crss.distributor_id and t.status=1
		and (crss.channel_weight &amp; (Case when t.dic_item_desc='Consumer' then 1 when t.dic_item_desc='Commercial' then 2 else 3 end)) &gt; 0
		<if test="salesChannel !=null and salesChannel !=''">
		   and t.dic_item_desc = #{salesChannel}
		</if>
		)
		<if test="regionName !=null and regionName !=''">
		   and crss.region_name = #{regionName}
		</if>
		<if test="suppervisorCai !=null and suppervisorCai !=''">
				and crss.suppervisor_cai = #{suppervisorCai}
	     </if>
	      <if test="salesCai !=null and salesCai !=''">
				and crss.sales_cai = #{salesCai}
	     </if>
	      <if test="distributorId !=null">
				and crss.distributor_id = #{distributorId}
	     </if>
	      <if test="distributorIds !=null">
	      and crss.distributor_id in (
			<foreach collection="distributorIds" item="item" separator=",">
			${item}
			</foreach>)
	     </if>
	     <if test="teamLeaderCai !=null and teamLeaderCai !=''">
	     	    and crss.sales_cai in (select t.sales_cai from dw_sales_role t where 1=1 and t.sales_cai_level like '%'+#{teamLeaderCai,jdbcType=VARCHAR}+'%')
	     </if>
	     <if test="salesChannel !=null and salesChannel !=''">
	           and (crss.channel_weight &amp; (case when #{salesChannel}='Consumer' then 1 when #{salesChannel}='Commercial' then 2 else 3 end)) &gt; 0 
	     </if>
	      <if test="regionList!=null and regionList.size() >0">
           and crss.region_name in 
        <foreach item="region" index="index" collection="regionList" open="(" separator="," close=")">  
             #{region}  
        </foreach> 
        </if>
		group by crss.distributor_id
		<!-- )t1 -->
	</select>
	
	<!-- 根据经销商Id获取星火总预算 -->
	<select id="getTotalAmountByParams" resultType="Double" parameterType="map">
	  select isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0)from wx_t_mkt_dealer_quarter t 
       where 1=1 
       <if test="salesChannel !=null and salesChannel !=''">
          and t.sales_channel = #{salesChannel}
       </if>
       <if test="distributorId !=null">
          and t.distributor_id= #{distributorId}
        </if>
         <if test="year !=null">
          and t.year= #{year}
        </if>
	</select>
	
		<!-- 根据经销商Id获取星火总预算 -->
	<select id="getSpDistributorTotalByParams" resultMap="BaseResultSpDistributorTotal" parameterType="map">
	  select crss.distributor_id,crss.customer_name_cn,isnull(SUM(t.q1),0) + isnull(SUM(t.q2),0) + isnull(SUM(t.q3),0) + isnull(SUM(t.q4),0) as allocate_budget_total
	  from view_customer_region_sales_channel crss
      inner join wx_t_mkt_dealer_quarter t on crss.distributor_id = t.distributor_id
       where 1=1 
       and exists
		(select t.id from wx_t_dic_item t where 1=1 and
		t.dic_type_code='mkt_sp_distributer' and
		t.sort_numb=crss.distributor_id and t.status=1
		)
		<if test="salesCai !=null and salesCai !=''">
		  and crss.sales_cai=#{salesCai}
		</if>
        <if test="channelWeight !=null and channelWeight !=''">
		  and crss.channel_weight &amp; #{channelWeight,jdbcType=INTEGER} &gt;0
		</if>
         <if test="year !=null">
          and t.year= #{year}
        </if>
        <if test="distributorId !=null">
          and t.distributor_id =#{distributorId}
        </if>
         group by  crss.distributor_id,crss.customer_name_cn
	</select>
	
	<!-- qbr获取所有的经销商数据 -->
	<select id="queryQbrListByParams" resultMap="BaseResultMap" parameterType="map">
		select distinct crss.region as region_name,crss.distributor_id,
		crss.customer_name_cn, t1.id, t1.sales_channel,
		t1.year, t1.q1, t1.q2,
		t1.q3, t1.q4, t1.create_user_id,
		t1.create_time, t1.update_user_id,
		t1.update_time,
		(select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='Plan' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_plan_volumn,
	   (select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='Total' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_volume,
		(select vol.total_value from wx_t_mkt_spark_sales_vol vol where 1=1 and vol.total_type='SN_Above' and crss.distributor_id=vol.distributor_id and vol.sales_channel= #{salesChannel}) as total_snAbove_volume
		from PP_MID.dbo.syn_dw_to_pp_customer_org_sales crss
		left join wx_t_mkt_dealer_quarter t1 on crss.distributor_id =
		t1.distributor_id
		<if test="salesChannel !=null and salesChannel !=''">
		  and t1.sales_channel = #{salesChannel}
		</if>
		<if test="year !=null ">
		  and t1.year = #{year}
		</if>
		where 1=1 
		and crss.sales_name_cn!='NA'
		<if test="regionName !=null and regionName !=''">
		   and crss.region = #{regionName}
		</if>
		<if test="suppervisorCai !=null and suppervisorCai !=''">
				and crss.supervisor_cai = #{suppervisorCai}
	     </if>
	     <if test="salesCai !=null and salesCai !=''">
				and crss.sales_cai = #{salesCai}
	     </if>
	     <if test="distributorId != null">
	        and crss.distributor_id = #{distributorId}
	     </if>
	      <if test="salesChannel !=null and salesChannel !=''">
	        and crss.product_channel = #{salesChannel}
	     </if>
	     <if test="regionList!=null and regionList.size() >0">
           and crss.region in 
        <foreach item="region" index="index" collection="regionList" open="(" separator="," close=")">  
             #{region}  
        </foreach> 
        </if>
	</select>
	
</mapper>
