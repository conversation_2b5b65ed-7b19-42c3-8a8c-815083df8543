<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.mktresource.dao.MktResCostBudHisMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.mktresource.model.MktResCostBudHis">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="budget_id" property="budgetId" jdbcType="BIGINT"/>
		<result column="year" property="year" jdbcType="INTEGER"/>
		<result column="log_type" property="logType" jdbcType="INTEGER"/>
		<result column="off_set" property="offSet" jdbcType="NUMERIC"/>
		<result column="final_value" property="finalValue" jdbcType="NUMERIC"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	    <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,budget_id,year,log_type,off_set,final_value,create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.mktresource.model.MktResCostBudHis">
		update wx_t_mkt_res_cost_bud_his set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.mktresource.model.MktResCostBudHis">
		update wx_t_mkt_res_cost_bud_his
		<set>
			<if test="budgetId != null" >
				budget_id = #{budgetId,jdbcType=BIGINT},
			</if>
			<if test="year != null" >
				year = #{year,jdbcType=INTEGER},
			</if>
			<if test="logType != null" >
				log_type = #{logType,jdbcType=INTEGER},
			</if>
			<if test="offSet != null" >
				off_set = #{offSet,jdbcType=NUMERIC},
			</if>
			<if test="finalValue != null" >
				final_value = #{finalValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.mktresource.model.MktResCostBudHisExample">
    	delete from wx_t_mkt_res_cost_bud_his
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.mktresource.model.MktResCostBudHis" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_mkt_res_cost_bud_his
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="budgetId != null">
				budget_id,
			</if>
			<if test="year != null">
				year,
			</if>
			<if test="logType != null">
				log_type,
			</if>
			<if test="offSet != null">
				off_set,
			</if>
			<if test="finalValue != null">
				final_value,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="budgetId != null">
				#{budgetId,jdbcType=BIGINT},
			</if>
			<if test="year != null">
				#{year,jdbcType=INTEGER},
			</if>
			<if test="logType != null">
				#{logType,jdbcType=INTEGER},
			</if>
			<if test="offSet != null">
				#{offSet,jdbcType=NUMERIC},
			</if>
			<if test="finalValue != null">
				#{finalValue,jdbcType=NUMERIC},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_mkt_res_cost_bud_his
		<set>
			<if test="record.budgetId != null">
				budget_id = #{record.budgetId,jdbcType=BIGINT},
			</if>
			<if test="record.year != null">
				year = #{record.year,jdbcType=INTEGER},
			</if>
			<if test="record.logType != null">
				log_type = #{record.logType,jdbcType=INTEGER},
			</if>
			<if test="record.offSet != null">
				off_set = #{record.offSet,jdbcType=NUMERIC},
			</if>
			<if test="record.finalValue != null">
				final_value = #{record.finalValue,jdbcType=NUMERIC},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.mktresource.model.MktResCostBudHisExample">
		delete from wx_t_mkt_res_cost_bud_his
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.mktresource.model.MktResCostBudHisExample" resultType="int">
		select count(1) from wx_t_mkt_res_cost_bud_his
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResCostBudHisExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_mkt_res_cost_bud_his
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResCostBudHisExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_mkt_res_cost_bud_his
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.budget_id, t1.year, t1.log_type, t1.off_set, t1.final_value, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time
		  from wx_t_mkt_res_cost_bud_his t1
		 where 1=1
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.mktresource.model.MktResCostBudHisParams">
		select t1.id, t1.budget_id, t1.year, t1.log_type, t1.off_set, t1.final_value, t1.create_user_id, t1.create_time,
			 t1.update_user_id, t1.update_time,t2.ch_name as create_user_name
		  from wx_t_mkt_res_cost_bud_his t1
		  inner join wx_t_mkt_resource_cost_budget t on t1.budget_id = t.id 
		  left join wx_t_user t2 on t1.create_user_id = t2.user_id and t2.status=1
		 where 1=1
		 <if test="budgetId !=null">
		     and t1.budget_id = #{budgetId, jdbcType=BIGINT}
		 </if>
		 <if test="year !=null">
		     and t.year = #{year, jdbcType=INTEGER}
		 </if>
		 <if test="regionName !=null and regionName !=''">
		     and t.region_name = #{regionName}
		 </if>
		 <if test="salesChannel !=null and salesChannel !=''">
		     and t.sales_channel = #{salesChannel}
		 </if>
	</select>

	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_mkt_res_cost_bud_his (budget_id, year, log_type, off_set, final_value, create_user_id, create_time, update_user_id, update_time) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.budgetId, jdbcType=BIGINT}, #{item.year, jdbcType=INTEGER}, #{item.logType, jdbcType=INTEGER}, #{item.offSet, jdbcType=NUMERIC}, #{item.finalValue, jdbcType=NUMERIC}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}
			</trim>
		</foreach>
	</insert>
</mapper>
