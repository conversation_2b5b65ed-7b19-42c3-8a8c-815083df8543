<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.core.conf.dao.PromotionalCampaignUserRecordMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.core.conf.model.PromotionalCampaignUserRecord" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
    <result column="promotion_campaign_id" property="promotionCampaignId" jdbcType="BIGINT" />
    <result column="promotion_sku" property="promotionSku" jdbcType="NVARCHAR" />
    <result column="promotion_title" property="promotionTitle" jdbcType="NVARCHAR" />
    <result column="promotion_remark" property="promotionRemark" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, partner_id, partner_name, promotion_campaign_id, promotion_sku, promotion_title, 
    promotion_remark, create_time, update_time, creator, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.core.conf.model.PromotionalCampaignUserRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_promotional_campaign_user_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.core.conf.model.PromotionalCampaignUserRecordExample" >
    delete from wx_t_promotional_campaign_user_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.core.conf.model.PromotionalCampaignUserRecord" >
    insert into wx_t_promotional_campaign_user_record (id, partner_id, partner_name, 
      promotion_campaign_id, promotion_sku, promotion_title, 
      promotion_remark, create_time, update_time, 
      creator, remark)
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, 
      #{promotionCampaignId,jdbcType=BIGINT}, #{promotionSku,jdbcType=NVARCHAR}, #{promotionTitle,jdbcType=NVARCHAR}, 
      #{promotionRemark,jdbcType=NVARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.core.conf.model.PromotionalCampaignUserRecord" >
    insert into wx_t_promotional_campaign_user_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerName != null" >
        partner_name,
      </if>
      <if test="promotionCampaignId != null" >
        promotion_campaign_id,
      </if>
      <if test="promotionSku != null" >
        promotion_sku,
      </if>
      <if test="promotionTitle != null" >
        promotion_title,
      </if>
      <if test="promotionRemark != null" >
        promotion_remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null" >
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="promotionCampaignId != null" >
        #{promotionCampaignId,jdbcType=BIGINT},
      </if>
      <if test="promotionSku != null" >
        #{promotionSku,jdbcType=NVARCHAR},
      </if>
      <if test="promotionTitle != null" >
        #{promotionTitle,jdbcType=NVARCHAR},
      </if>
      <if test="promotionRemark != null" >
        #{promotionRemark,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_promotional_campaign_user_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null" >
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.promotionCampaignId != null" >
        promotion_campaign_id = #{record.promotionCampaignId,jdbcType=BIGINT},
      </if>
      <if test="record.promotionSku != null" >
        promotion_sku = #{record.promotionSku,jdbcType=NVARCHAR},
      </if>
      <if test="record.promotionTitle != null" >
        promotion_title = #{record.promotionTitle,jdbcType=NVARCHAR},
      </if>
      <if test="record.promotionRemark != null" >
        promotion_remark = #{record.promotionRemark,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_promotional_campaign_user_record
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      promotion_campaign_id = #{record.promotionCampaignId,jdbcType=BIGINT},
      promotion_sku = #{record.promotionSku,jdbcType=NVARCHAR},
      promotion_title = #{record.promotionTitle,jdbcType=NVARCHAR},
      promotion_remark = #{record.promotionRemark,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>