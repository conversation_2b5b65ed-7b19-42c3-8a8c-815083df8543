<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.core.conf.dao.PromotionalCampaignMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.core.conf.model.PromotionalCampaign" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="promotion_title" property="promotionTitle" jdbcType="NVARCHAR" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_type" property="partnerType" jdbcType="NVARCHAR" />
    <result column="product_sku" property="productSku" jdbcType="NVARCHAR" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="condition_count" property="conditionCount" jdbcType="BIGINT" />
    <result column="proportion_count" property="proportionCount" jdbcType="BIGINT" />
    <result column="gift_count" property="giftCount" jdbcType="BIGINT" />
    <result column="product_min_amount" property="productMinAmount" jdbcType="BIGINT" />
    <result column="product_max_amount" property="productMaxAmount" jdbcType="BIGINT" />
    <result column="ladder_price" property="ladderPrice" jdbcType="NUMERIC" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="bz1" property="bz1" jdbcType="NVARCHAR" />
    <result column="bz2" property="bz2" jdbcType="NVARCHAR" />
    <result column="bz3" property="bz3" jdbcType="NVARCHAR" />
    <result column="import_excel" property="importExcel" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="NVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id,promotion_title, partner_id,partner_type, product_sku, type, condition_count,proportion_count, gift_count, product_min_amount, 
    product_max_amount, ladder_price, start_time, end_time, status, create_time, update_time, 
    creator, remark, bz1, bz2, bz3
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.core.conf.model.PromotionalCampaignExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_promotional_campaign
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.core.conf.model.PromotionalCampaignExample" >
    delete from wx_t_promotional_campaign
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.core.conf.model.PromotionalCampaign" >
    insert into wx_t_promotional_campaign (id,promotion_title, partner_id,partner_type, product_sku, 
      type, condition_count,proportion_count, gift_count, 
      product_min_amount, product_max_amount, ladder_price, 
      start_time, end_time, status, 
      create_time, update_time, creator, 
      remark, bz1, bz2, 
      bz3)
    values (#{id,jdbcType=BIGINT},  #{promotionTitle,jdbcType=NVARCHAR}, #{partnerId,jdbcType=BIGINT},#{partnerType,jdbcType=NVARCHAR},  #{productSku,jdbcType=NVARCHAR}, 
      #{type,jdbcType=NVARCHAR}, #{conditionCount,jdbcType=BIGINT}, 
      #{proportionCount,jdbcType=BIGINT}, #{giftCount,jdbcType=BIGINT}, 
      #{productMinAmount,jdbcType=BIGINT}, #{productMaxAmount,jdbcType=BIGINT}, #{ladderPrice,jdbcType=NUMERIC}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=NVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=NVARCHAR}, 
      #{remark,jdbcType=NVARCHAR}, #{bz1,jdbcType=NVARCHAR}, #{bz2,jdbcType=NVARCHAR}, 
      #{bz3,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.core.conf.model.PromotionalCampaign" >
    insert into wx_t_promotional_campaign
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="promotionTitle != null" >
        promotion_title,
      </if>
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="partnerType != null" >
        partner_type,
      </if>
      <if test="productSku != null" >
        product_sku,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="conditionCount != null" >
        condition_count,
      </if>
      <if test="proportionCount != null" >
        proportion_count,
      </if>
      <if test="giftCount != null" >
        gift_count,
      </if>
      <if test="productMinAmount != null" >
        product_min_amount,
      </if>
      <if test="productMaxAmount != null" >
        product_max_amount,
      </if>
      <if test="ladderPrice != null" >
        ladder_price,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="bz1 != null" >
        bz1,
      </if>
      <if test="bz2 != null" >
        bz2,
      </if>
      <if test="bz3 != null" >
        bz3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="promotionTitle != null" >
        #{promotionTitle,jdbcType=NVARCHAR},
      </if>
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerType != null" >
        #{partnerType,jdbcType=NVARCHAR},
      </if>
      <if test="productSku != null" >
        #{productSku,jdbcType=NVARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="conditionCount != null" >
        #{conditionCount,jdbcType=BIGINT},
      </if>
      <if test="proportionCount != null" >
        #{proportionCount,jdbcType=BIGINT},
      </if>
      <if test="giftCount != null" >
        #{giftCount,jdbcType=BIGINT},
      </if>
      <if test="productMinAmount != null" >
        #{productMinAmount,jdbcType=BIGINT},
      </if>
      <if test="productMaxAmount != null" >
        #{productMaxAmount,jdbcType=BIGINT},
      </if>
      <if test="ladderPrice != null" >
        #{ladderPrice,jdbcType=NUMERIC},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NVARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="bz1 != null" >
        #{bz1,jdbcType=NVARCHAR},
      </if>
      <if test="bz2 != null" >
        #{bz2,jdbcType=NVARCHAR},
      </if>
      <if test="bz3 != null" >
        #{bz3,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_promotional_campaign
    <set >
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.promotionTitle != null" >
        promotion_title = #{record.promotionTitle,jdbcType=NVARCHAR},
      </if>
      <if test="record.partnerType != null" >
        partner_type = #{record.partnerType,jdbcType=NVARCHAR},
      </if>
      <if test="record.productSku != null" >
        product_sku = #{record.productSku,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.conditionCount != null" >
        condition_count = #{record.conditionCount,jdbcType=BIGINT},
      </if>
      <if test="record.proportionCount != null" >
        proportion_count = #{record.proportionCount,jdbcType=BIGINT},
      </if>
      <if test="record.giftCount != null" >
        gift_count = #{record.giftCount,jdbcType=BIGINT},
      </if>
      <if test="record.productMinAmount != null" >
        product_min_amount = #{record.productMinAmount,jdbcType=BIGINT},
      </if>
      <if test="record.productMaxAmount != null" >
        product_max_amount = #{record.productMaxAmount,jdbcType=BIGINT},
      </if>
      <if test="record.ladderPrice != null" >
        ladder_price = #{record.ladderPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.startTime != null" >
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null" >
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=NVARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null" >
        creator = #{record.creator,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
      <if test="record.bz1 != null" >
        bz1 = #{record.bz1,jdbcType=NVARCHAR},
      </if>
      <if test="record.bz2 != null" >
        bz2 = #{record.bz2,jdbcType=NVARCHAR},
      </if>
      <if test="record.bz3 != null" >
        bz3 = #{record.bz3,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_promotional_campaign
    set 
      promotion_title = #{record.promotionTitle,jdbcType=NVARCHAR},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_type = #{record.partnerType,jdbcType=NVARCHAR},
      product_sku = #{record.productSku,jdbcType=NVARCHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      condition_count = #{record.conditionCount,jdbcType=BIGINT},
      proportion_count = #{record.proportionCount,jdbcType=BIGINT},
      gift_count = #{record.giftCount,jdbcType=BIGINT},
      product_min_amount = #{record.productMinAmount,jdbcType=BIGINT},
      product_max_amount = #{record.productMaxAmount,jdbcType=BIGINT},
      ladder_price = #{record.ladderPrice,jdbcType=NUMERIC},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=NVARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR},
      bz1 = #{record.bz1,jdbcType=NVARCHAR},
      bz2 = #{record.bz2,jdbcType=NVARCHAR},
      bz3 = #{record.bz3,jdbcType=NVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <resultMap id="promotionalCampaignPriceConfMap" type="com.chevron.core.conf.model.PromotionalCampaignView" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="promotion_title" property="promotionTitle" jdbcType="BIGINT" />
    <result column="partner_id" property="partnerId" jdbcType="BIGINT" />
    <result column="partner_name" property="partnerName" jdbcType="BIGINT" />
    <result column="partner_type" property="partnerType" jdbcType="NVARCHAR" />
    <result column="product_sku" property="productSku" jdbcType="NVARCHAR" />
    <result column="product_name" property="productName" jdbcType="NVARCHAR" />
    <result column="type" property="type" jdbcType="NVARCHAR" />
    <result column="type_str" property="typeStr" jdbcType="NVARCHAR" />
    <result column="condition_count" property="conditionCount" jdbcType="BIGINT" />
    <result column="proportion_count" property="proportionCount" jdbcType="BIGINT" />
    <result column="gift_count" property="giftCount" jdbcType="BIGINT" />
    <result column="product_min_amount" property="productMinAmount" jdbcType="BIGINT" />
    <result column="product_max_amount" property="productMaxAmount" jdbcType="BIGINT" />
    <result column="ladder_price" property="ladderPrice" jdbcType="NUMERIC" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="NVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
    <result column="bz1" property="bz1" jdbcType="NVARCHAR" />
    <result column="bz2" property="bz2" jdbcType="NVARCHAR" />
    <result column="bz3" property="bz3" jdbcType="NVARCHAR" />
  </resultMap>
  <select id="selectPromotionalCampaignConf" resultMap="promotionalCampaignPriceConfMap" parameterType="com.chevron.core.conf.model.PromotionalCampaignViewQueryParams" >
	SELECT t.id,
		   t.promotion_title,
		   t.partner_id,
		   t2.organization_name as partner_name,
		   t.partner_type,
		   t.product_sku,
	       t1.name as product_name,
	       t.type,
	       dicitem.dic_item_name as type_str,
	       t.condition_count,
	       t.proportion_count,	 
	       t.gift_count,
	       t.product_min_amount,
	       t.product_max_amount,
	       t.ladder_price,      
	       t.start_time,
	       t.end_time,
	       t.status,
	       t.create_time,
	       t.update_time,
	       t.creator,	       
	       t.remark,
	       t.bz1,
	       t.bz2	       
	FROM wx_t_promotional_campaign t
	LEFT JOIN wx_t_product t1 ON t1.sku=t.product_sku
	LEFT JOIN wx_t_organization t2 ON t.partner_id = t2.id
	LEFT JOIN wx_t_dic_item dicitem on dicitem.dic_item_code = t.type and dicitem.dic_type_code = 'pricepromotioncampaign.type'
	WHERE 1 = 1 
	<if test="partnerName != null" >
	  AND t2.organization_name LIKE '%' + #{partnerName,jdbcType=NVARCHAR} + '%'
	</if>        
	<if test="partnerId != null and partnerId == 0">
		and t.partner_id = 0 
	</if>
	<if test="partnerId != null and partnerId != 0">
		and t.partner_id=#{partnerId,jdbcType=BIGINT}
	</if>
	<if test="productName != null" >
	  AND t1.name LIKE '%' + #{productName,jdbcType=NVARCHAR} + '%'
	</if>
	<if test="productSku != null" >
	  AND t1.sku LIKE '%' + #{productSku,jdbcType=NVARCHAR} + '%'
	</if>
   </select>
	<!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
    insert into wx_t_promotional_campaign (promotion_title, partner_id,partner_type, product_sku, 
      type, condition_count,proportion_count, gift_count, 
      product_min_amount, product_max_amount, ladder_price, 
      start_time, end_time, status, 
      create_time, creator, 
      remark, bz1, bz2, 
      bz3,import_excel)
    values 
 		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
    #{item.promotionTitle,jdbcType=NVARCHAR}, #{item.partnerId,jdbcType=BIGINT},#{item.partnerType,jdbcType=NVARCHAR},  #{item.productSku,jdbcType=NVARCHAR}, 
      #{item.type,jdbcType=NVARCHAR}, #{item.conditionCount,jdbcType=BIGINT}, 
      #{item.proportionCount,jdbcType=BIGINT}, #{item.giftCount,jdbcType=BIGINT}, 
      #{item.productMinAmount,jdbcType=BIGINT}, #{item.productMaxAmount,jdbcType=BIGINT}, #{item.ladderPrice,jdbcType=NUMERIC}, 
      #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=NVARCHAR}, 
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=NVARCHAR}, 
      #{item.remark,jdbcType=NVARCHAR}, #{item.bz1,jdbcType=NVARCHAR}, #{item.bz2,jdbcType=NVARCHAR}, 
      #{item.bz3,jdbcType=NVARCHAR}, #{item.importExcel,jdbcType=BIGINT}
			</trim>
		</foreach>
	</insert>
  <select id="buildImportData" resultMap="BaseResultMap" parameterType="map" >
 		<foreach collection="records" index="index" item="item" separator=" union all ">
			<trim suffixOverrides=" union all ">
    select 
    			<choose>
    				<when test="item.partnerType == 'sold to'">
    		(select pe.partner_id from wx_t_partner_o2o_enterprise pe where pe.sap_code=#{item.partnerName,jdbcType=NVARCHAR}) partner_id,
    		(select o.organization_name from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id where pe.sap_code=#{item.partnerName,jdbcType=NVARCHAR}) partner_name,
    				</when>
    				<when test="item.partnerType == 'ship to'">
    		(select pe.partner_id from wx_t_partner_o2o_enterprise pe where pe.ship_to_code=#{item.partnerName,jdbcType=NVARCHAR}) partner_id,
    		(select o.organization_name from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id where pe.ship_to_code=#{item.partnerName,jdbcType=NVARCHAR}) partner_name,
    				</when>
    			</choose>
    		case when not exists (select 1 from wx_t_product p where p.sku=#{item.productSku,jdbcType=NVARCHAR}) then 1 else 0 end status,
    		${item.id} id
			</trim>
		</foreach>
   </select>
	<update id="updateOldStatusByImport" parameterType="map">
 		<foreach collection="records" index="index" item="item" separator=";">
			<trim suffixOverrides=";">
    update wx_t_promotional_campaign set status=0, import_excel=#{item.importExcel,jdbcType=BIGINT}, update_time=#{item.createTime,jdbcType=TIMESTAMP}  where status=1 and partner_id=#{item.partnerId,jdbcType=BIGINT} and product_sku=#{item.productSku,jdbcType=NVARCHAR} and type='specialdiscount'
			</trim>
		</foreach>
	</update>
</mapper>