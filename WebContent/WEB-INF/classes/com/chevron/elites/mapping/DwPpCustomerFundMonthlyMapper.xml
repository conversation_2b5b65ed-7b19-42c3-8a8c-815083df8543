<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.DwPpCustomerFundMonthlyMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.DwPpCustomerFundMonthly">
	<result column="Year" jdbcType="VARCHAR" property="year" />
	<result column="Month" jdbcType="VARCHAR" property="month" />
	<result column="Customer_Name_CN" jdbcType="NVARCHAR" property="customerNameCN" />
	<result column="Distributor_id" jdbcType="BIGINT" property="distributorId" />
	<result column="Customer_Grade" jdbcType="VARCHAR" property="customerGrade" />
	
	<result column="BD_Fund_Total" jdbcType="NUMERIC" property="bdFundTotal" />
	<result column="BD_Fund_CDM" jdbcType="NUMERIC" property="bdFundCDM" />
	<result column="BD_Fund_C&amp;I" jdbcType="NUMERIC" property="bdFundCI" />
	
	<result column="Marketing_Fund_Total" jdbcType="NUMERIC" property="marketingFundTotal" />
	<result column="Marketing_Fund_CDM" jdbcType="NUMERIC" property="marketingFundCDM" />
	<result column="Marketing_Fund_C&amp;I" jdbcType="NUMERIC" property="marketingFundCI" />
	
	<result column="ivi_fund_rmb" jdbcType="NUMERIC" property="iviFundRmb" />
	<result column="ivi_fund_rmb_CDM" jdbcType="NUMERIC" property="iviFundCDMRmb" />
	<result column="ivi_fund_rmb_C&amp;I" jdbcType="NUMERIC" property="iviFundCIORmb" />
</resultMap>

<sql id="Base_Column_List">
	Year, Month, sp.Customer_Name_CN, Customer_Grade, sp.Distributor_id,
	BD_Fund_Total, BD_Fund_CDM, [BD_Fund_C&amp;I],
	Marketing_Fund_Total, Marketing_Fund_CDM, [Marketing_Fund_C&amp;I],
	ivi_fund_rmb, ivi_fund_rmb_CDM, [ivi_fund_rmb_C&amp;I]
</sql>

<select id="getPerformanceByDistributorId" resultMap="BaseResultMap">
	SELECT DISTINCT
	<include refid="Base_Column_List" />
	from dw_pp_customer_fund_monthly sp
	where year = #{year, jdbcType=VARCHAR}
	<if test="distributorId != null">
		and Distributor_id = #{distributorId, jdbcType=BIGINT}
	</if>
</select>

<select id="getPerformanceByMonth" resultMap="BaseResultMap">
	SELECT DISTINCT
	<include refid="Base_Column_List" />
	from dw_pp_customer_fund_monthly sp
	LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON sp.Distributor_id = crss.distributor_id
	where sp.Month BETWEEN #{startDate, jdbcType=NVARCHAR} and #{endDate, jdbcType=NVARCHAR}
	<if test="distributorId != null">
		and crss.distributor_id = #{distributorId, jdbcType=BIGINT}
	</if>
</select>

<select id="getCustomerDistributorIdList" resultType="java.lang.Long">
	select Distributor_id
	from dw_pp_customer_fund_monthly
	GROUP BY Distributor_id
</select>

</mapper>