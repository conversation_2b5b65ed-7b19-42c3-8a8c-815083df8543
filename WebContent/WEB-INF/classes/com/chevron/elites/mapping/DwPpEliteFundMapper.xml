<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.DwPpEliteFundMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.DwPpEliteFund">
	<result column="month_interval" jdbcType="NVARCHAR" property="monthInterval" />
	<result column="customer_name_cn" jdbcType="NVARCHAR" property="customerNameCn" />
	<result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
	<result column="company_code" jdbcType="NVARCHAR" property="companyCode" />
	<result column="elite_fund_rmb" jdbcType="NUMERIC" property="eliteFundRmb" />
	<result column="total_elite_fund_rmb" jdbcType="NUMERIC" property="totalEliteFundRmb" />
	<result column="elite_percent" jdbcType="NVARCHAR" property="elitePercent" />
</resultMap>

<sql id="Base_Column_List">
	month_interval, customer_name_cn, distributor_id, company_code, elite_fund_rmb, total_elite_fund_rmb, elite_percent
</sql>

<select id="getFundDetailListByOrgId" resultMap="BaseResultMap">
	SELECT
	<include refid="Base_Column_List" />
	from dw_pp_elite_fund_2018
	where distributor_id = #{orgId, jdbcType=BIGINT}
	and month_interval = #{month, jdbcType=NVARCHAR}
</select>

</mapper>