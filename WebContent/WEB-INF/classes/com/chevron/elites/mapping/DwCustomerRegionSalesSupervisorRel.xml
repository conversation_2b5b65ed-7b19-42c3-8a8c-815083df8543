<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.elites.dao.DwCustomerRegionSalesSupervisorRelMapper" >
<resultMap id="BaseResultMap" type="com.chevron.elites.model.DwCustomerRegionSalesSupervisorRel">
	<result column="customer_name_cn" jdbcType="NVARCHAR" property="customerNameCn" />
	<result column="customer_name_en" jdbcType="NVARCHAR" property="customerNameEn" />
	<result column="region_name" jdbcType="NVARCHAR" property="regionName" />
	<result column="sales_cai" jdbcType="NVARCHAR" property="salesCai" />
	<result column="sales_name" jdbcType="NVARCHAR" property="salesName" />
	<result column="suppervisor_cai" jdbcType="NVARCHAR" property="suppervisorCai" />
	<result column="suppervisor_name" jdbcType="NVARCHAR" property="suppervisorName" />
</resultMap>


<sql id="Base_Column_List">
	customer_name_cn, customer_name_en, region_name, sales_cai, sales_name, suppervisor_cai, suppervisor_name
</sql>

<select id="getCustomerInfoById" resultMap="BaseResultMap">
	SELECT DISTINCT
	<include refid="Base_Column_List" />
	from dw_customer_region_sales_supervisor_rel 
	WHERE distributor_id = #{orgId, jdbcType = BIGINT}
</select>
<select id="getSalesChannelByCai" resultType="java.lang.String" parameterType="java.lang.String">
	Select DISTINCT TOP(1)
	sales_channel_name
	from dw_customer_region_sales_supervisor_rel crss
	left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
	left join wx_t_user tt_user ON tt_user.cai = crss.sales_cai WHERE tt_user.cai = #{cai, jdbcType = BIGINT}
</select>

<select id="getUserListByUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
	SELECT DISTINCT
		crss.sales_cai, crss.sales_name
	from dw_customer_region_sales_supervisor_rel crss
	left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
	left join wx_t_user tt_user ON tt_user.cai = crss.sales_cai 
	WHERE 1 = 1
	<choose>
		<when test="isSale != null">
			<if test="curUserName != null">
                and crss.sales_name = #{curUserName,jdbcType=VARCHAR}
			</if>
		</when>
		<when test="isCsr != null">
			<if test="salesName != null">
				and (crss.sales_name like concat('%',#{salesName,jdbcType=VARCHAR},'%')
				or crss.sales_cai like concat('%',#{salesName,jdbcType=VARCHAR},'%'))
			</if>
			<if test="curUserId != null">
				and crss.distributor_id IN (
				select
				DISTINCT poe.distributor_id
				from wx_t_partner_responsible pr1
				LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.partner_id = pr1.partner_id
				join wx_t_partner_responsible_main prm1 on pr1.responsible_main_id=prm1.id
				join wx_t_user ut1 on prm1.config_type=0 and prm1.user_id=ut1.user_id
				where 1 = 1
				and exists (select 1 from wx_t_userrole ur1 join wx_t_role r1 on r1.role_id=ur1.role_id where
				ur1.user_id=prm1.user_id and r1.ch_role_name='Chevron_SAP_CSR')

				and prm1.fun_flag = 'order_confirm'
				AND prm1.user_id = #{curUserId,jdbcType=VARCHAR}
				)
			</if>
		</when>
		<otherwise>
			<if test="salesName != null">
				and (crss.sales_name like concat('%',#{salesName,jdbcType=VARCHAR},'%')
				   or crss.sales_cai like concat('%',#{salesName,jdbcType=VARCHAR},'%'))
			</if>
		</otherwise>
	</choose>
</select>

<select id="getCustomerInfoByPra" parameterType="map" resultMap="BaseResultMap">
	SELECT DISTINCT
	crss.customer_name_cn, crss.customer_name_en, crss.region_name, 
		crss.sales_cai, crss.sales_name, crss.suppervisor_cai, crss.suppervisor_name
	from dw_customer_region_sales_supervisor_rel crss
	left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
	left join wx_t_user tt_user ON tt_user.cai = crss.sales_cai 
	WHERE 1 = 1 
	<if test="distributorId != null">
		and distributor_id=#{distributorId,jdbcType=BIGINT}
	</if>
	<if test="salesChannelName != null">
		and rsc.sales_channel_name = #{salesChannelName,jdbcType=NVARCHAR}
	</if>
	<if test="loginName != null">
		and tt_user.login_name = #{loginName,jdbcType=NVARCHAR}
	</if>
</select>

<select id="getCustomerEnName" resultType="java.lang.String">
	SELECT 
	DISTINCT customer_name_en
	from dw_customer_region_sales_supervisor_rel 
	WHERE customer_name_cn = #{customerNameCn, jdbcType = NVARCHAR}
</select>
<select id="getCustomerNameBySapCodeWithSales" resultType="java.lang.String" parameterType="map">
	select t.organization_name
    from wx_t_organization t 
    left join wx_t_partner_o2o_enterprise t1 on t.id=t1.partner_id
    LEFT JOIN dw_customer_region_sales_supervisor_rel crss ON crss.distributor_id = t1.distributor_id
     
      where t.type=1 and t.status=1
      <if test="sapCode">
	     	and t1.sap_code=#{sapCode}
	  </if>
	  <if test="salesCai">
	     	and crss.sales_cai = #{salesCai}
	  </if>
</select>

</mapper>