<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.WXTRebateAuditMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.WXTRebateAudit">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="status" jdbcType="NVARCHAR" property="status" />
	<result column="total_fund" jdbcType="DECIMAL" property="totalFund" />
	<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	<result column="create_by" jdbcType="BIGINT" property="createBy" />
	<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	<result column="update_by" jdbcType="BIGINT" property="updateBy" />
	<result column="create_by_name" jdbcType="NVARCHAR" property="createByName" />
	<result column="cio_region" jdbcType="NVARCHAR" property="cioRegion" />
	<result column="is_summary" jdbcType="NVARCHAR" property="isSummary" />
	<result column="summary_id" jdbcType="BIGINT" property="summaryId" />
</resultMap>

<sql id="Base_Column_List">
	id, status, total_fund, create_time, create_by, update_time, update_by, create_by_name, cio_region, is_summary, summary_id
</sql>

<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_rebate_audit
	where ID = #{id,jdbcType=BIGINT}
</select>

<insert id="insertSelective" parameterType="com.chevron.elites.model.WXTRebateAudit" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_rebate_audit
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<if test="status != null and status != '' ">
			status,
		</if>
		<if test="totalFund != null">
			total_fund,
		</if>
		<if test="createTime != null">
			create_time,
		</if>
		<if test="createBy != null">
			create_by,
		</if>
		<if test="updateTime != null">
			update_time,
		</if>
		<if test="updateBy != null">
			update_by,
		</if>
		<if test="createByName != null and createByName != '' ">
			create_by_name,
		</if>
		<if test="cioRegion != null and cioRegion != '' ">
			cio_region,
		</if>
		<if test="isSummary != null and isSummary != '' ">
			is_summary,
		</if>
		<if test="summaryId != null">
			summary_id,
		</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
		<if test="status != null and status != '' ">
			#{status, jdbcType=NVARCHAR},
		</if>
		<if test="totalFund != null">
			#{totalFund, jdbcType=DECIMAL},
		</if>
		<if test="createTime != null">
			#{createTime, jdbcType=TIMESTAMP},
		</if>
		<if test="createBy != null">
			#{createBy, jdbcType=BIGINT},
		</if>
		<if test="updateTime != null">
			#{updateTime, jdbcType=TIMESTAMP},
		</if>
		<if test="updateBy != null">
			#{updateBy, jdbcType=BIGINT},
		</if>
		<if test="createByName != null and createByName != '' ">
			#{createByName, jdbcType=NVARCHAR},
		</if>
		<if test="cioRegion != null and cioRegion != '' ">
			#{cioRegion, jdbcType=NVARCHAR},
		</if>
		<if test="isSummary != null and isSummary != '' ">
			#{isSummary, jdbcType=NVARCHAR},
		</if>
		<if test="summaryId != null">
			#{summaryId, jdbcType=BIGINT},
		</if>
	</trim>
</insert>

<update id="updateSelective" parameterType="com.chevron.elites.model.WXTRebateAudit">
    update wx_t_rebate_audit
    <set>
    	<if test="status != null and status != '' ">
			status = #{status, jdbcType=NVARCHAR},
		</if>
		<if test="totalFund != null">
			total_fund = #{totalFund, jdbcType=DECIMAL},
		</if>
		<if test="updateTime != null">
			update_time = #{updateTime, jdbcType=TIMESTAMP},
		</if>
		<if test="updateBy != null">
			update_by = #{updateBy, jdbcType=BIGINT},
		</if>
		<if test="isSummary != null and isSummary != '' ">
			is_summary = #{isSummary, jdbcType=NVARCHAR},
		</if>
		<if test="summaryId != null">
			summary_id = #{summaryId, jdbcType=BIGINT},
		</if>
	</set>
	where id = #{id,jdbcType=BIGINT}
</update>

<update id="clearSummaryId" parameterType="java.lang.Long">
	update wx_t_rebate_audit
	set summary_id = null
	where summary_id = #{summaryId, jdbcType=BIGINT}
</update>

<select id="getSummaryAudit" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_rebate_audit
	where summary_id = #{summaryId, jdbcType=BIGINT}
	and is_summary = '-1'
</select>

<select id="getRebateAuditByPage" parameterType="com.chevron.elites.dto.request.RebateAuditPageRequest" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_rebate_audit
	where 1=1
	<if test='(searchType == "bsNorth" or searchType == "bsSouth" or searchType == "bsSW") and type == "1" '>
		and status != '9'
		and create_by = #{userId, jdbcType=BIGINT}
	</if>
	<if test='(searchType == "bsNorth" or searchType == "bsSouth" or searchType == "bsSW") and type == "2" '>
		and status = '9'
		and create_by = #{userId, jdbcType=BIGINT}
	</if>
	<if test='searchType == "channelAudit" and type == "1" '>
		and status in ('1', '5')
		and is_summary in ('0', '-1')
		and id NOT IN (SELECT audit_id FROM wx_t_rebate_audit_history WHERE operator = #{userId, jdbcType=BIGINT} AND status = '1')
	</if>
	<if test='searchType == "channelAudit" and type == "2" '>
		and status+0 >= 2 
		and is_summary in ('0', '-1')
		or id IN (SELECT audit_id FROM wx_t_rebate_audit_history WHERE operator = #{userId, jdbcType=BIGINT} AND status = '1')
	</if>
	<if test='searchType == "channelAudit" and userChannel == "C&amp;I" '>
		and cio_region IN (SELECT rsc.region_name FROM dw_region_sales_channel_rel rsc
		LEFT JOIN wx_t_user u ON u.cai = rsc.channel_manager_cai
		WHERE u.user_id = #{userId, jdbcType=BIGINT})
	</if>
	<if test='searchType == "bsmAudit" and type == "1" '>
		and status in ('2', '8')
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "bsmAudit" and type == "2" '>
		and status+0 >= 4
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "bsmAudit" and type == "3" '>
		and status = '4'
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "sdoAudit" and type == "1" '>
		and status in ('6', '10')
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "sdoAudit" and type == "2" '>
		and status+0 >= 7
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "abmAudit" and type == "1" '>
		and status in ('7')
		and id NOT IN (SELECT audit_id FROM wx_t_rebate_audit_history WHERE operator = #{userId, jdbcType=BIGINT} AND status = '1')
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "abmAudit" and type == "2" '>
		and status+0 >= 7
		and id IN (SELECT audit_id FROM wx_t_rebate_audit_history WHERE operator = #{userId, jdbcType=BIGINT} AND status = '1')
		and is_summary in ('0', '1')
	</if>
	<if test='searchType == "admin" and type == "1" '>
		and status+0 = -2
	</if>
	<if test='searchType == "admin" and type == "2" '>
		and status+0 = 9
		and is_summary in ('0', '1')
	</if>
</select>

</mapper>