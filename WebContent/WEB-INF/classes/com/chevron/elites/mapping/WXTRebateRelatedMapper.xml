<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.WXTRebateRelatedMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.WXTRebateRelated">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="rebate_id" jdbcType="BIGINT" property="rebateId" />
	<result column="audit_id" jdbcType="BIGINT" property="auditId" />
</resultMap>

<sql id="Base_Column_List">
	id, rebate_id, audit_id
</sql>

<insert id="insert" parameterType="com.chevron.elites.model.WXTRebateRelated" useGeneratedKeys="true" keyProperty="id">
	insert into wx_t_rebate_related (
	rebate_id,
	audit_id
	) values(
	#{rebateId, jdbcType=BIGINT},
	#{auditId, jdbcType=BIGINT}
	)
</insert>
	
<delete id="delete" parameterType="java.lang.Long">
	delete from wx_t_rebate_related 
	where audit_id = #{auditId, jdbcType=BIGINT}
</delete>

<select id="getListByAuditId" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from wx_t_rebate_related
	where audit_id = #{auditId, jdbcType=BIGINT}
</select>
</mapper>