<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.DwRebateFundMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.DwRebateFund">
	<id column="id" jdbcType="BIGINT" property="id" />
	<result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
	<result column="organization_name" jdbcType="NVARCHAR" property="organizationName" />
	<result column="apply_fund_cdm" jdbcType="DECIMAL" property="applyFundCdm" />
	<result column="apply_fund_cio" jdbcType="DECIMAL" property="applyFundCio" />
	<result column="total_fund_cdm" jdbcType="DECIMAL" property="totalFundCdm" />
	<result column="total_fund_cio" jdbcType="DECIMAL" property="totalFundCio" />
	<result column="used_fund_cdm" jdbcType="DECIMAL" property="usedFundCdm" />
	<result column="used_fund_cio" jdbcType="DECIMAL" property="usedFundCio" />
	<result column="remain_fund_cdm" jdbcType="DECIMAL" property="remainFundCdm" />
	<result column="remain_fund_cio" jdbcType="DECIMAL" property="remainFundCio" />
	<result column="invoice_amount_total" jdbcType="DECIMAL" property="invoiceAmountTotal" />
	<result column="fund_ownership" jdbcType="NVARCHAR" property="fundOwnership" />
	<result column="fund_type_code" jdbcType="NVARCHAR" property="fundTypeCode" />
	<result column="fund_type" jdbcType="NVARCHAR" property="fundType" />
	<result column="apply_person_name" jdbcType="NVARCHAR" property="applyPersonName" />
	<result column="apply_person_cai" jdbcType="NVARCHAR" property="applyPersonCai" />
	<result column="status" jdbcType="NVARCHAR" property="status" />
	<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
	<result column="sync_status" jdbcType="NVARCHAR" property="syncStatus" />
</resultMap>

<sql id="Base_Column_List">
	id, distributor_id, organization_name, apply_fund_cdm, apply_fund_cio, total_fund_cdm, total_fund_cio, used_fund_cdm, used_fund_cio, 
	remain_fund_cdm, remain_fund_cio, invoice_amount_total, fund_ownership, fund_type_code, fund_type, status, create_time, update_time, 
	apply_person_name, apply_person_cai, sync_status
</sql>

<insert id="insertSelective" parameterType="com.chevron.elites.model.DwRebateFund" useGeneratedKeys="true" keyProperty="id">
	insert into dw_rebate_fund
	<trim prefix="(" suffix=")" suffixOverrides=",">
		<if test="distributorId != null">
			distributor_id,
		</if>
		<if test="organizationName != null and organizationName != '' ">
			organization_name,
		</if>
		<if test="applyFundCdm != null">
			apply_fund_cdm,
		</if>
		<if test="applyFundCio != null">
			apply_fund_cio,
		</if>
		<if test="totalFundCdm != null">
			total_fund_cdm,
		</if>
		<if test="totalFundCio != null">
			total_fund_cio,
		</if>
		<if test="usedFundCdm != null">
			used_fund_cdm,
		</if>
		<if test="usedFundCio != null">
			used_fund_cio,
		</if>
		<if test="remainFundCdm != null">
			remain_fund_cdm,
		</if>
		<if test="remainFundCio != null">
			remain_fund_cio,
		</if>
		<if test="invoiceAmountTotal != null">
			invoice_amount_total,
		</if>
		<if test="fundTypeCode != null and fundTypeCode != '' ">
			fund_type_code,
		</if>
		<if test="fundType != null and fundType!= '' ">
			fund_type,
		</if>
		<if test="fundOwnership != null and fundOwnership != '' ">
			fund_ownership,
		</if>
		<if test="status != null and status != '' ">
			status,
		</if>
		<if test="createTime != null">
			create_time,
		</if>
		<if test="updateTime != null">
			update_time,
		</if>
		<if test="applyPersonName != null and applyPersonName != '' ">
			apply_person_name,
		</if>
		<if test="applyPersonCai != null and applyPersonCai != '' ">
			apply_person_cai,
		</if>
		<if test="syncStatus != null and syncStatus != '' ">
			sync_status,
		</if>
	</trim>
	<trim prefix="values (" suffix=")" suffixOverrides=",">
		<if test="distributorId != null">
			#{distributorId, jdbcType=BIGINT},
		</if>
		<if test="organizationName != null and organizationName != '' ">
			#{organizationName, jdbcType=NVARCHAR},
		</if>
		<if test="applyFundCdm != null">
			#{applyFundCdm, jdbcType=DECIMAL},
		</if>
		<if test="applyFundCio != null">
			#{applyFundCio, jdbcType=DECIMAL},
		</if>
		<if test="totalFundCdm != null">
			#{totalFundCdm, jdbcType=DECIMAL},
		</if>
		<if test="totalFundCio != null">
			#{totalFundCio, jdbcType=DECIMAL},
		</if>
		<if test="usedFundCdm != null">
			#{usedFundCdm, jdbcType=DECIMAL},
		</if>
		<if test="usedFundCio != null">
			#{usedFundCio, jdbcType=DECIMAL},
		</if>
		<if test="remainFundCdm != null">
			#{remainFundCdm, jdbcType=DECIMAL},
		</if>
		<if test="remainFundCio != null">
			#{remainFundCio, jdbcType=DECIMAL},
		</if>
		<if test="invoiceAmountTotal != null">
			#{invoiceAmountTotal, jdbcType=DECIMAL},
		</if>
		<if test="fundTypeCode != null and fundTypeCode != '' ">
			#{fundTypeCode, jdbcType=NVARCHAR},
		</if>
		<if test="fundType != null and fundType!= '' ">
			#{fundType, jdbcType=NVARCHAR},
		</if>
		<if test="fundOwnership != null and fundOwnership != '' ">
			#{fundOwnership, jdbcType=NVARCHAR},
		</if>
		<if test="status != null and status != '' ">
			#{status, jdbcType=NVARCHAR},
		</if>
		<if test="createTime != null">
			#{createTime, jdbcType=TIMESTAMP},
		</if>
		<if test="updateTime != null">
			#{updateTime, jdbcType=TIMESTAMP},
		</if>
		<if test="applyPersonName != null and applyPersonName != '' ">
			#{applyPersonName, jdbcType=NVARCHAR},
		</if>
		<if test="applyPersonCai != null and applyPersonCai != '' ">
			#{applyPersonCai, jdbcType=NVARCHAR},
		</if>
		<if test="syncStatus != null and syncStatus != '' ">
			#{syncStatus, jdbcType=NVARCHAR},
		</if>
	</trim>
</insert>


<update id="updateSelective" parameterType="com.chevron.elites.model.DwRebateFund">
	update dw_rebate_fund
	<set>
		<if test="status != null and status != '' ">
			status = #{status, jdbcType=NVARCHAR},
		</if>
		<if test="updateTime != null">
			update_time = #{updateTime, jdbcType=TIMESTAMP},
		</if>
	</set>
	where distributor_id = #{distributorId, jdbcType=BIGINT}
	and create_time = #{createTime, jdbcType=TIMESTAMP}
	and sync_status = '1'
</update>

<update id="deleteByPrimaryKey" parameterType="com.chevron.elites.model.DwRebateFund">
	update dw_rebate_fund set
	status = 'delete',
	sync_status = '0'
	where distributor_id = #{distributorId, jdbcType=BIGINT}
	and create_time = #{createTime, jdbcType=TIMESTAMP}
	and sync_status = '1'
</update>

<select id="getRebateFundListByOrgId" resultMap="BaseResultMap">
	select 
	<include refid="Base_Column_List" />
	from dw_rebate_fund
	where 1=1
<!-- 	and create_time like CONCAT(CONCAT('%', #{year, jdbcType=NVARCHAR}),'%') -->
	<if test="orgId != null">
		and distributor_id = #{orgId, jdbcType=BIGINT}
	</if>
	<if test="status != null and status != '' ">
		and status = #{status, jdbcType=NVARCHAR}
	</if>
	and sync_status = '1'
</select>

<select id="getRebateFundByOrg" resultMap="BaseResultMap">
	select 
	<include refid="Base_Column_List" />
	from dw_rebate_fund
	where distributor_id = #{orgId, jdbcType=BIGINT}
	and create_time = #{createTime, jdbcType=TIMESTAMP}
	<if test="status != null and status != '' ">
		and status = #{status, jdbcType=NVARCHAR}
	</if>
	and sync_status = '1'
</select>

<select id="getNegativeFundOrg" resultMap="BaseResultMap">
	select 
	<include refid="Base_Column_List" />
	from dw_rebate_fund
	where 1=1
	AND (remain_fund_cdm <![CDATA[ < ]]> 0
	OR remain_fund_cio <![CDATA[ < ]]> 0)
	AND fund_type = #{fundType, jdbcType=NVARCHAR}
	AND sync_status = '1'
</select>
</mapper>