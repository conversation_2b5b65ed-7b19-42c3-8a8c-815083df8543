<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites.dao.DwSalesPerformanceCustomerProductChannelQuarterMapper">
<resultMap id="BaseResultMap" type="com.chevron.elites.model.DwSalesPerformanceCustomerProductChannelQuarter">
	<result column="Year" jdbcType="VARCHAR" property="year" />
	<result column="Quarter" jdbcType="VARCHAR" property="quarter" />
	<result column="Customer_Name_CN" jdbcType="NVARCHAR" property="customerNameCN" />
	<result column="Customer_Grade" jdbcType="VARCHAR" property="customerGrade" />
	
	<result column="Total_Volume" jdbcType="NUMERIC" property="totalVolume" />
	<result column="IVI_Total_Target" jdbcType="FLOAT" property="iviTotalTarget" />
	<result column="IVI_Percentage" jdbcType="FLOAT" property="iviPercentage" />
	
	<result column="BD_Fund_Total" jdbcType="NUMERIC" property="bdFundTotal" />
	<result column="BD_Fund_CDM" jdbcType="NUMERIC" property="bdFundCDM" />
	<result column="BD_Fund_C&amp;I" jdbcType="NUMERIC" property="bdFundCI" />
	
	<result column="Marketing_Fund_Total" jdbcType="NUMERIC" property="marketingFundTotal" />
	<result column="Marketing_Fund_CDM" jdbcType="NUMERIC" property="marketingFundCDM" />
	<result column="Marketing_Fund_C&amp;I" jdbcType="NUMERIC" property="marketingFundCI" />
	
	<result column="Car_Oil_Volume" jdbcType="NUMERIC" property="carOilVolume" />
	<result column="Car_Oil_Volume_Y-1" jdbcType="NUMERIC" property="carOilVolumeY1" />
	<result column="Car_Oil_Increased_Percentage" jdbcType="NUMERIC" property="carOilIncreasedPercentage" />
	<result column="Car_Oil_Target" jdbcType="FLOAT" property="carOilTarget" />
	<result column="Car_Oil_Complete_Percentage" jdbcType="FLOAT" property="carOilCompletePercentage" />
	
	<result column="Commercial_Oil_Volume" jdbcType="NUMERIC" property="commercialOilVolume" />
	<result column="Commercial_Oil_Volume_Y-1" jdbcType="NUMERIC" property="commercialOilVolumeY1" />
	<result column="Commercial_Oil_Increased_Percentage" jdbcType="NUMERIC" property="commercialOilIncreasedPercentage" />
	<result column="Commercial_Oil_Target" jdbcType="FLOAT" property="commercialOilTarget" />
	<result column="Commercial_Oil_Complete_Percentage" jdbcType="FLOAT" property="commercialOilCompletePercentage" />
	
	<result column="Industrial_Oil_Volume" jdbcType="NUMERIC" property="industrialOilVolume" />
	<result column="Industrial_Oil_Volume_Y-1" jdbcType="NUMERIC" property="industrialOilVolumeY1" />
	<result column="Industrial_Oil_Increased_Percentage" jdbcType="NUMERIC" property="industrialOilIncreasedPercentage" />
	<result column="Industrial_Oil_Target" jdbcType="FLOAT" property="industrialOilTarget" />
	<result column="Industrial_Oil_Complete_Percentage" jdbcType="FLOAT" property="industrialOilCompletePercentage" />
	
	<result column="abp_rmb" jdbcType="NUMERIC" property="abpRmb" />
</resultMap>


<sql id="Base_Column_List">
	Year, Quarter, Customer_Name_CN, Customer_Grade, 
	Total_Volume, IVI_Total_Target, IVI_Percentage, 
	BD_Fund_Total, BD_Fund_CDM, [BD_Fund_C&amp;I],
	Marketing_Fund_Total, Marketing_Fund_CDM, [Marketing_Fund_C&amp;I],
	Car_Oil_Volume, [Car_Oil_Volume_Y-1], Car_Oil_Increased_Percentage, Car_Oil_Target, Car_Oil_Complete_Percentage, 
	Commercial_Oil_Volume, [Commercial_Oil_Volume_Y-1], Commercial_Oil_Increased_Percentage, Commercial_Oil_Target, Commercial_Oil_Complete_Percentage,
	Industrial_Oil_Volume, [Industrial_Oil_Volume_Y-1], Industrial_Oil_Increased_Percentage, Industrial_Oil_Target, Industrial_Oil_Complete_Percentage,
	abp_rmb
</sql>

<select id="getPerformanceByPartnerId" parameterType="java.lang.Long" resultMap="BaseResultMap">
	select
	<include refid="Base_Column_List" />
	from dw_sales_performance_customer_product_channel_quarter sp
	LEFT JOIN wx_t_organization o ON  o.organization_name = sp.Customer_Name_CN
	where o.id = #{partnerId, jdbcType=BIGINT} and sp.year = #{year, jdbcType=VARCHAR}
</select>

</mapper>