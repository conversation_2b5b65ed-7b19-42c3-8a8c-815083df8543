<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.sop.dao.SopSupportSummaryMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.sop.model.SopSupportSummary">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
		<result column="channel" property="channel" jdbcType="VARCHAR"/>
		<result column="year" property="year" jdbcType="VARCHAR"/>
		<result column="quarter" property="quarter" jdbcType="VARCHAR"/>
		<result column="signage" property="signage" jdbcType="NUMERIC"/>
		<result column="seminar" property="seminar" jdbcType="NUMERIC"/>
		<result column="oil_change_car" property="oilChangeCar" jdbcType="NUMERIC"/>
		<result column="equipment_tools" property="equipmentTools" jdbcType="NUMERIC"/>
		<result column="other_amount" property="otherAmount" jdbcType="NUMERIC"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
        <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
        <result column="distributor_id" property="distributorId" jdbcType="VARCHAR"/>
        <result column="region_name" property="region" jdbcType="VARCHAR"/>
        <result column="belong_to_user" property="belongToUser" jdbcType="VARCHAR"/>
        <result column="sop_sum" property="sopAmount" jdbcType="NUMERIC" />
        <association property="sopAmountAllot" column="allot_id" javaType="com.chevron.sop.model.SopAmountAllot">
            <id column="allot_id" property="id" jdbcType="VARCHAR" />
            <result column="sop_amount" property="sopAmount" jdbcType="NUMERIC" />
            <result column="signage_flag" property="signageFlag" />
            <result column="seminar_flag" property="seminarFlag"/>
            <result column="oil_change_car_flag" property="oilChangeCarFlag"/>
            <result column="equipment_tools_flag" property="equipmentToolsFlag"/>
        </association>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,create_user_id,create_time,update_user_id,update_time,partner_id,channel,year,quarter,signage,seminar,
		oil_change_car,equipment_tools,other_amount,status
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.sop.model.SopSupportSummary">
		update wx_t_sop_support_summary set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.sop.model.SopSupportSummary">
		update wx_t_sop_support_summary
		<set>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="partnerId != null" >
				partner_id = #{partnerId,jdbcType=BIGINT},
			</if>
			<if test="channel != null" >
				channel = #{channel,jdbcType=VARCHAR},
			</if>
			<if test="year != null" >
				year = #{year,jdbcType=VARCHAR},
			</if>
			<if test="quarter != null" >
				quarter = #{quarter,jdbcType=VARCHAR},
			</if>
			<if test="signage != null" >
				signage = #{signage,jdbcType=NUMERIC},
			</if>
			<if test="seminar != null" >
				seminar = #{seminar,jdbcType=NUMERIC},
			</if>
			<if test="oilChangeCar != null" >
				oil_change_car = #{oilChangeCar,jdbcType=NUMERIC},
			</if>
			<if test="equipmentTools != null" >
				equipment_tools = #{equipmentTools,jdbcType=NUMERIC},
			</if>
			<if test="otherAmount != null" >
				other_amount = #{otherAmount,jdbcType=NUMERIC},
			</if>
			<if test="status != null" >
				status = #{status,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.sop.model.SopSupportSummaryExample">
    	delete from wx_t_sop_support_summary
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.sop.model.SopSupportSummary">
		insert into wx_t_sop_support_summary
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null">
				id,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="partnerId != null">
				partner_id,
			</if>
			<if test="channel != null">
				channel,
			</if>
			<if test="year != null">
				year,
			</if>
			<if test="quarter != null">
				quarter,
			</if>
			<if test="signage != null">
				signage,
			</if>
			<if test="seminar != null">
				seminar,
			</if>
			<if test="oilChangeCar != null">
				oil_change_car,
			</if>
			<if test="equipmentTools != null">
				equipment_tools,
			</if>
			<if test="otherAmount != null">
				other_amount,
			</if>
			<if test="status != null">
				status,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="partnerId != null">
				#{partnerId,jdbcType=BIGINT},
			</if>
			<if test="channel != null">
				#{channel,jdbcType=VARCHAR},
			</if>
			<if test="year != null">
				#{year,jdbcType=VARCHAR},
			</if>
			<if test="quarter != null">
				#{quarter,jdbcType=VARCHAR},
			</if>
			<if test="signage != null">
				#{signage,jdbcType=NUMERIC},
			</if>
			<if test="seminar != null">
				#{seminar,jdbcType=NUMERIC},
			</if>
			<if test="oilChangeCar != null">
				#{oilChangeCar,jdbcType=NUMERIC},
			</if>
			<if test="equipmentTools != null">
				#{equipmentTools,jdbcType=NUMERIC},
			</if>
			<if test="otherAmount != null">
				#{otherAmount,jdbcType=NUMERIC},
			</if>
			<if test="status != null">
				#{status,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_sop_support_summary
		<set>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.partnerId != null">
				partner_id = #{record.partnerId,jdbcType=BIGINT},
			</if>
			<if test="record.channel != null">
				channel = #{record.channel,jdbcType=VARCHAR},
			</if>
			<if test="record.year != null">
				year = #{record.year,jdbcType=VARCHAR},
			</if>
			<if test="record.quarter != null">
				quarter = #{record.quarter,jdbcType=VARCHAR},
			</if>
			<if test="record.signage != null">
				signage = #{record.signage,jdbcType=NUMERIC},
			</if>
			<if test="record.seminar != null">
				seminar = #{record.seminar,jdbcType=NUMERIC},
			</if>
			<if test="record.oilChangeCar != null">
				oil_change_car = #{record.oilChangeCar,jdbcType=NUMERIC},
			</if>
			<if test="record.equipmentTools != null">
				equipment_tools = #{record.equipmentTools,jdbcType=NUMERIC},
			</if>
			<if test="record.otherAmount != null">
				other_amount = #{record.otherAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.status != null">
				status = #{record.status,jdbcType=INTEGER},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.sop.model.SopSupportSummaryExample">
		delete from wx_t_sop_support_summary
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.sop.model.SopSupportSummaryExample" resultType="int">
		select count(1) from wx_t_sop_support_summary
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.sop.model.SopSupportSummaryExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_sop_support_summary
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.sop.model.SopSupportSummaryExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_sop_support_summary
		where id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.partner_id, t1.channel,
			 t1.year, t1.quarter, t1.signage, t1.seminar, t1.oil_change_car, t1.equipment_tools, t1.other_amount, t1.status
		  from wx_t_sop_support_summary t1
		 where 1=1
	</select>

    <!-- 批量插入 -->
	<insert id="insertBatch" parameterType="map">
		insert into wx_t_sop_support_summary (id, create_user_id, create_time, update_user_id, update_time, partner_id, channel, year, quarter, signage, seminar, oil_change_car, equipment_tools, other_amount, status) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
			#{item.id, jdbcType=BIGINT}, #{item.createUserId, jdbcType=BIGINT}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateUserId, jdbcType=BIGINT}, #{item.updateTime, jdbcType=TIMESTAMP}, #{item.partnerId, jdbcType=BIGINT}, #{item.channel, jdbcType=VARCHAR}, #{item.year, jdbcType=VARCHAR}, #{item.quarter, jdbcType=VARCHAR}, #{item.signage, jdbcType=NUMERIC}, #{item.seminar, jdbcType=NUMERIC}, #{item.oilChangeCar, jdbcType=NUMERIC}, #{item.equipmentTools, jdbcType=NUMERIC}, #{item.otherAmount, jdbcType=NUMERIC}, #{item.status, jdbcType=INTEGER}
			</trim>
		</foreach>
	</insert>

    <select id="getPagePartnerIds" resultMap="BaseResultMap"  parameterType="com.chevron.sop.model.SopSupportParams">
        select max(t1.id) as id, t1.partner_id
        from wx_t_sop_support_summary t1
        <where>
            <if test="year != null and year!= ''">
                and t1.year = #{year}
            </if>
            <if test="quarter != null and quarter!= ''">
                and t1.quarter = #{quarter}
            </if>
            <if test="partnerId != null">
                and t1.partner_id = #{partnerId}
            </if>
            <if test="status != null">
                and t1.status = #{status}
            </if>
        </where>
        group by t1.partner_id
    </select>

    <select id="findSopPartnerSummary" resultMap="BaseResultMap" parameterType="com.chevron.sop.model.SopSupportParams">
    select t1.id, t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time, t1.partner_id, t1.channel,
    t1.year, t1.quarter, t1.signage, t1.seminar, t1.oil_change_car, t1.equipment_tools, t1.status,
    t3.organization_name as partner_name , IIF(t1.status != 0, t1.other_amount, t2.amount) as other_amount,t4.distributor_id,
    t5.id allot_id,t5.signage_flag,t5.seminar_flag,t5.oil_change_car_flag,t5.equipment_tools_flag
        <!--  ,case when t7.id is not null then 1 else 0 end as belong_to_user-->
            from wx_t_sop_support_summary t1
            left join (select partner_id,substring(target_month,1,4) as year,quarter,sum(amount) as amount
            from wx_t_sop_other_support_detail group by partner_id,substring(target_month,1,4),quarter) t2 on t1.partner_id
            = t2.partner_id and t1.quarter = t2.quarter and t1.year = t2.year
            left join (select distinct id,organization_name from wx_t_organization ) t3 on t1.partner_id = t3.id
            left join (select distinct distributor_id,partner_id from wx_t_partner_o2o_enterprise) t4 on t1.partner_id = t4.partner_id
            left join (select * from wx_t_sop_amount_allot where sop_amount > 0) t5 on t1.partner_id = t5.partner_id and t1.year = t5.year
        <!--     left join (select distinct region,distributor_id from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null) t6 on t4.distributor_id = t6.distributor_id
             left join wx_t_partner_responsible_main t7 on t6.region = t7.region_name and t7.fun_flag= 'sop_region' and t7.user_id = #{userId}-->
    <where>
        <if test="year != null and year!= ''">
            and t1.year = #{year}
        </if>
        <if test="quarter != null and quarter!= ''">
            and t1.quarter = #{quarter}
        </if>
        <if test="partnerId != null">
            and t1.partner_id = #{partnerId}
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>

        <if test="partnerIds != null and partnerIds.size > 1">
            and t1.partner_id in
            <foreach collection="partnerIds" index="index" item="item" separator=","  open="(" close=")">
                    #{item}
            </foreach>
        </if>
        <if test="regionFlag != null and regionFlag == true">
            and EXISTS (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
            left join wx_t_partner_responsible_main b on a.region = b.region_name and b.fun_flag= 'sop_region'
            where t4.distributor_id = a.distributor_id and b.user_id = #{userId})
        </if>
    </where>
</select>

    <select id="getDistributorIdByOrgId" resultType="long">
        select distinct pe.distributor_id
        from wx_t_partner_o2o_enterprise pe
        where pe.partner_id = #{partnerId}
    </select>

    <select id="calSopSupportInfo" resultMap="BaseResultMap">
        select
        <if test="partnerId != null">
            t2.partner_id,
        </if>
        <if test="region != null and region != ''">
            t3.region_name,
        </if>
        <if test="channel == 'consumer'">
            0 as sop_sum,
        </if>
        <if test="channel == 'commercial'">
            sum(ISNULL(t4.sop_amount,0)) sop_sum,
        </if>
        t4.year,
        '${channel}' as channel,
        sum(ISNULL(t1.signage,0)) signage,
        sum(ISNULL(t1.seminar,0)) seminar,
        sum(ISNULL(t1.oil_change_car,0)) oil_change_car,
        sum(ISNULL(t1.equipment_tools,0)) equipment_tools,
        sum(ISNULL(t1.other_amount,0)) other_amount
        from
        (select distinct distributor_id,region_name,sales_cai,suppervisor_cai,channel_manager_cai from
        view_customer_region_sales_channel
        where bu = 'Indirect'
        <if test="channel == 'consumer'">
            and channel_weight <![CDATA[ & ]]> 1 = 1
        </if>
        <if test="channel == 'commercial'">
            and channel_weight <![CDATA[ & ]]> 2 = 2
        </if>
        <if test="distributorId != null and distributorId !=''">
            and distributor_id = #{distributorId}
        </if>
        <if test="salesCai != null and salesCai!=''">
            and sales_cai = #{salesCai}
        </if>
        <if test="suppervisorCai != null and suppervisorCai!=''">
            and suppervisor_cai = #{suppervisorCai}
        </if>
        <if test="channelManagerCai != null and channelManagerCai!=''">
            and channel_manager_cai = #{channelManagerCai}
        </if>
        <if test="teamLeaderCai != null and teamLeaderCai!=''">
            and sales_cai in (select sales_cai from dw_sales_role where sales_cai_level like '%${teamLeaderCai}%')
        </if>
        ) t3
        inner join (select distinct distributor_id,partner_id from wx_t_partner_o2o_enterprise ) t2 on t2.distributor_id
        = t3.distributor_id
        left join
        (select * from wx_t_sop_support_summary
        <where>
            and year = #{year}
            <if test="status != null">
                and status >= #{status}
            </if>
            <if test="channel != null and channel != '' ">
                and channel = #{channel}
            </if>
            <if test="quarters != null and quarters.size() > 0">
                and quarter in
                <foreach collection="quarters" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) t1 on t1.partner_id = t2.partner_id
        left join wx_t_sop_amount_allot t4 on t4.partner_id = t2.partner_id
        where t4.year = #{year}
        <if test="region != null and region != ''">
            and t3.region_name = #{region}
        </if>
        group by t4.year
        <if test="partnerId != null">
            ,t2.partner_id
        </if>
        <if test="region != null and region != ''">
            ,t3.region_name
        </if>
    </select>

    <select id="qbrSopActualSupport" resultMap="BaseResultMap">
        select year,quarter,partner_id,signage,seminar,oil_change_car,equipment_tools from wx_t_sop_support_summary
        where year = #{year} and channel = #{channel} and status >= #{status}
    </select>
    <select id="sumSparkAmountByPartnerId" resultType="double">
        select sum(NULLIF(seminar,0) + NULLIF(signage,0) + NULLIF(oil_change_car,0) + NULLIF(equipment_tools,0)) from wx_t_sop_support_summary
        where partner_id = #{partnerId}
    </select>

    <select id="batchConfirm" resultMap="com.chevron.sop.dao.SopSupportAdjustApplyMapper.BaseResultMap">
        select t.year,t.channel,t.quarter,sum(NULLIF(t.signage,0)) signage,sum(NULLIF(t.seminar,0)) seminar,sum(NULLIF(t.oil_change_car,0)) oil_change_car,
               sum(NULLIF(t.equipment_tools,0)) equipment_tools,sum(NULLIF(t.other_amount,0)) other_amount from wx_t_sop_support_summary t
        where t.year = #{year} and t.channel =  #{channel} and t.quarter = #{quarter} and t.status = 10
        group by t.year,t.channel,t.quarter
    </select>
</mapper>
