<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.task.dao.WxTaskMainMapper">
  <resultMap id="BaseResultMap" type="com.chevron.task.model.WxTaskMain">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    <id column="task_main_id" jdbcType="BIGINT" property="taskMainId" />
    <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode" />
    <result column="tmb_type_name" jdbcType="VARCHAR" property="tmbTypeName" />
    
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_mb_id" jdbcType="BIGINT" property="taskMbId" />
    <result column="tmb_classify" jdbcType="INTEGER" property="tmbClassify" />
    <result column="exec_user_type" jdbcType="INTEGER" property="execUserType" />
    <result column="inner_org_type" jdbcType="INTEGER" property="innerOrgType" />
    <result column="inner_org_id" jdbcType="BIGINT" property="innerOrgId" />
    <result column="inner_org_name" jdbcType="VARCHAR" property="innerOrgName" /> 
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />    
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="check_item_status" jdbcType="INTEGER" property="checkItemStatus" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="task_priority" jdbcType="VARCHAR" property="taskPriority" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="publish_status" jdbcType="INTEGER" property="publishStatus" />
    <result column="frequency_type" jdbcType="INTEGER" property="frequencyType" />
    <result column="create_source" jdbcType="INTEGER" property="createSource" />
    <result column="source_task_pid" property="sourceTaskPid" jdbcType="BIGINT" />
    <result column="finish_day" property="finishDay" jdbcType="INTEGER" />    
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="xg_sj" jdbcType="TIMESTAMP" property="xgSj" />
    <result column="xg_user" jdbcType="BIGINT" property="xgUser" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="operate" jdbcType="INTEGER" property="operate" /> 
    <result column="excute_user_id" property="excuteUserId" jdbcType="BIGINT" />
    <result column="workshop_ids" property="workshopIds" jdbcType="NVARCHAR" />
    <result column="task_steps" property="taskSteps" jdbcType="NVARCHAR" />   
    
    <result column="handle_task_status" jdbcType="VARCHAR" property="handleTaskStatus" />
    <result column="handle_task_priority" jdbcType="VARCHAR" property="handleTaskPriority" />
    <result column="exec_user_name" jdbcType="VARCHAR" property="executeUserName" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workShopName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workShopId" />
    <result column="fleet_name" jdbcType="VARCHAR" property="fleetName" />
    <result column="fleet_id" jdbcType="BIGINT" property="fleetId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="machinery_id" jdbcType="BIGINT" property="machineryId" />
    <result column="workshop_status" jdbcType="BIGINT" property="workShopStatus" />
    <result column="IS_TO_LD" jdbcType="NVARCHAR" property="isToLD" />
    <result column="IS_TO_GD" jdbcType="NVARCHAR" property="isToGD" />
    <result column="organizationname" jdbcType="NVARCHAR" property="organizationName" />
    <result column="new_task_status" jdbcType="NVARCHAR" property="taskNewStatus" />
    <result column="IS_TO_SD" jdbcType="NVARCHAR" property="isToSD" />
    <result column="subtask_id" jdbcType="BIGINT" property="subtaskId" />
    
    
    <result column="in_stock_no" jdbcType="VARCHAR" property="inStockNo" />
    <result column="out_stock_no" jdbcType="VARCHAR" property="outStockNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" /> 
    <result column="workshop_addr" jdbcType="VARCHAR" property="workshopAddress" /> 
    <result column="access_count" jdbcType="INTEGER" property="accessCount" /> 
    <result column="task_id" jdbcType="BIGINT" property="taskId" /> 
    <result column="is_pass" jdbcType="INTEGER" property="isPass" /> 
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" /> 
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="xd_problem_feedback" jdbcType="VARCHAR" property="xdProblemFeedback" /> 
     
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
   <sql id="Example_noWhere_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    task_main_id, tmb_type_code, task_name, task_mb_id, tmb_classify, exec_user_type, 
    inner_org_type,inner_org_id, task_description, task_start_time, task_finish_time, task_create_user, 
    check_status, check_item_status, task_status, task_priority, room_id, room_name, 
    publish_status, frequency_type, create_source, source_task_pid, finish_day, 
    create_time, create_user, xg_sj, xg_user, status, tenant_id, excute_user_id, workshop_ids, 
    task_steps
  </sql>
  <select id="selectByExample" parameterType="com.chevron.task.model.WxTaskMainExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_task_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />,
    (select d.data_name from wx_t_data d where d.data_code=wx_task_main.tmb_type_code
      ) as tmb_type_name
    from wx_task_main
    where task_main_id = #{taskMainId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    delete from wx_task_main
    where task_main_id = #{taskMainId,jdbcType=BIGINT}
  </delete>
  <delete id="cascadeDeleteBySubTask" parameterType="string">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    delete m from wx_task_main m
    where m.task_main_id in (#{_parameter}) and not exists (select 1 from wx_task_sub s where s.task_main_id=m.task_main_id)
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.WxTaskMainExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    delete from wx_task_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert"  useGeneratedKeys="true" keyProperty="taskMainId" parameterType="com.chevron.task.model.WxTaskMain">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    insert into wx_task_main (task_main_id, tmb_type_code, task_name, 
      task_mb_id, tmb_classify, exec_user_type, 
      inner_org_type,inner_org_id, task_description, task_start_time, 
      task_finish_time, task_create_user, check_status, 
      check_item_status, task_status, task_priority, 
      room_id, room_name, publish_status, 
      frequency_type, create_source, create_time, 
      create_user, xg_sj, xg_user, 
      status, tenant_id, excute_user_id, 
      workshop_ids, task_steps)
    values (#{taskMainId,jdbcType=BIGINT}, #{tmbTypeCode,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, 
      #{taskMbId,jdbcType=BIGINT}, #{tmbClassify,jdbcType=INTEGER}, #{execUserType,jdbcType=INTEGER}, 
      #{innerOrgType,jdbcType=INTEGER},#{innerOrgId,jdbcType=INTEGER}, #{taskDescription,jdbcType=VARCHAR}, #{taskStartTime,jdbcType=TIMESTAMP}, 
      #{taskFinishTime,jdbcType=TIMESTAMP}, #{taskCreateUser,jdbcType=BIGINT}, #{checkStatus,jdbcType=INTEGER}, 
      #{checkItemStatus,jdbcType=INTEGER}, #{taskStatus,jdbcType=VARCHAR}, #{taskPriority,jdbcType=VARCHAR}, 
      #{roomId,jdbcType=VARCHAR}, #{roomName,jdbcType=VARCHAR}, #{publishStatus,jdbcType=INTEGER}, 
      #{frequencyType,jdbcType=INTEGER}, #{createSource,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=BIGINT},getdate(), #{xgUser,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT}, #{excuteUserId,jdbcType=BIGINT}, 
      #{workshopIds,jdbcType=NVARCHAR}, #{taskSteps,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="taskMainId" parameterType="com.chevron.task.model.WxTaskMain">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    insert into wx_task_main
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskMainId != null">
        task_main_id,
      </if>
      <if test="tmbTypeCode != null">
        tmb_type_code,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskMbId != null">
        task_mb_id,
      </if>
      <if test="tmbClassify != null">
        tmb_classify,
      </if>
      <if test="execUserType != null">
        exec_user_type,
      </if>
      <if test="innerOrgType != null">
        inner_org_type,
      </if>
      <if test="innerOrgId != null">
        inner_org_id,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="taskStartTime != null">
        task_start_time,
      </if>
      <if test="taskFinishTime != null">
        task_finish_time,
      </if>
      <if test="taskCreateUser != null">
        task_create_user,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkItemStatus != null">
        check_item_status,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="taskPriority != null">
        task_priority,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="roomName != null">
        room_name,
      </if>
      <if test="publishStatus != null">
        publish_status,
      </if>
      <if test="frequencyType != null">
        frequency_type,
      </if>
      <if test="createSource != null">
        create_source,
      </if>
      <if test="sourceTaskPid != null" >
        source_task_pid,
      </if>
      <if test="finishDay != null" >
        finish_day,
      </if>
      create_time,
      <if test="createUser != null">
        create_user,
      </if>
      xg_sj,
      <if test="xgUser != null">
        xg_user,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="excuteUserId != null" >
        excute_user_id,
      </if>
      <if test="workshopIds != null" >
        workshop_ids,
      </if>
      <if test="taskSteps != null" >
        task_steps,
      </if>
      
       <if test="inStockNo != null" >
        in_stock_no,
      </if>
      <if test="outStockNo != null" >
        out_stock_no,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskMainId != null">
        #{taskMainId,jdbcType=BIGINT},
      </if>
      <if test="tmbTypeCode != null">
        #{tmbTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskMbId != null">
        #{taskMbId,jdbcType=BIGINT},
      </if>
      <if test="tmbClassify != null">
        #{tmbClassify,jdbcType=INTEGER},
      </if>
      <if test="execUserType != null">
        #{execUserType,jdbcType=INTEGER},
      </if>
      <if test="innerOrgType != null">
        #{innerOrgType,jdbcType=INTEGER},
      </if>
      <if test="innerOrgId != null">
        #{innerOrgId,jdbcType=INTEGER},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskFinishTime != null">
        #{taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCreateUser != null">
        #{taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkItemStatus != null">
        #{checkItemStatus,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskPriority != null">
        #{taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="frequencyType != null">
        #{frequencyType,jdbcType=INTEGER},
      </if>
      <if test="createSource != null">
        #{createSource,jdbcType=INTEGER},
      </if>
      <if test="sourceTaskPid != null" >
        #{sourceTaskPid,jdbcType=BIGINT},
      </if>
      <if test="finishDay != null" >
        #{finishDay,jdbcType=INTEGER},
      </if>
      getdate(),
      <if test="createUser != null">
        #{createUser,jdbcType=BIGINT},
      </if>
      getdate(),
      <if test="xgUser != null">
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="excuteUserId != null" >
        #{excuteUserId,jdbcType=BIGINT},
      </if>
      <if test="workshopIds != null" >
        #{workshopIds,jdbcType=NVARCHAR},
      </if>
      <if test="taskSteps != null" >
        #{taskSteps,jdbcType=NVARCHAR},
      </if>
      
       <if test="inStockNo != null" >
        #{inStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="outStockNo != null" >
         #{outStockNo,jdbcType=NVARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
    </trim>
    	select @@IDENTITY
  </insert>
  <select id="countByExample" parameterType="com.chevron.task.model.WxTaskMainExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    select count(*) from wx_task_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    update wx_task_main
    <set>
      <if test="record.taskMainId != null">
        task_main_id = #{record.taskMainId,jdbcType=BIGINT},
      </if>
      <if test="record.tmbTypeCode != null">
        tmb_type_code = #{record.tmbTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskMbId != null">
        task_mb_id = #{record.taskMbId,jdbcType=BIGINT},
      </if>
      <if test="record.tmbClassify != null">
        tmb_classify = #{record.tmbClassify,jdbcType=INTEGER},
      </if>
      <if test="record.execUserType != null">
        exec_user_type = #{record.execUserType,jdbcType=INTEGER},
      </if>
      <if test="record.innerOrgType != null">
        inner_org_type = #{record.innerOrgType,jdbcType=INTEGER},
      </if>
      <if test="record.taskDescription != null">
        task_description = #{record.taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStartTime != null">
        task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskFinishTime != null">
        task_finish_time = #{record.taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskCreateUser != null">
        task_create_user = #{record.taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="record.checkStatus != null">
        check_status = #{record.checkStatus,jdbcType=INTEGER},
      </if>
      <if test="record.checkItemStatus != null">
        check_item_status = #{record.checkItemStatus,jdbcType=INTEGER},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.taskPriority != null">
        task_priority = #{record.taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=VARCHAR},
      </if>
      <if test="record.roomName != null">
        room_name = #{record.roomName,jdbcType=VARCHAR},
      </if>
      <if test="record.publishStatus != null">
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.frequencyType != null">
        frequency_type = #{record.frequencyType,jdbcType=INTEGER},
      </if>
      <if test="record.createSource != null">
        create_source = #{record.createSource,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=BIGINT},
      </if>
      <if test="record.xgSj != null">
        xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xgUser != null">
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="excuteUserId != null" >
        excute_user_id = #{excuteUserId,jdbcType=BIGINT},
      </if>
      <if test="workshopIds != null" >
        workshop_ids = #{workshopIds,jdbcType=NVARCHAR},
      </if>
      <if test="taskSteps != null" >
        task_steps = #{taskSteps,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    update wx_task_main
    set task_main_id = #{record.taskMainId,jdbcType=BIGINT},
      tmb_type_code = #{record.tmbTypeCode,jdbcType=VARCHAR},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_mb_id = #{record.taskMbId,jdbcType=BIGINT},
      tmb_classify = #{record.tmbClassify,jdbcType=INTEGER},
      exec_user_type = #{record.execUserType,jdbcType=INTEGER},
      inner_org_type = #{record.innerOrgType,jdbcType=INTEGER},
      task_description = #{record.taskDescription,jdbcType=VARCHAR},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      task_finish_time = #{record.taskFinishTime,jdbcType=TIMESTAMP},
      task_create_user = #{record.taskCreateUser,jdbcType=BIGINT},
      check_status = #{record.checkStatus,jdbcType=INTEGER},
      check_item_status = #{record.checkItemStatus,jdbcType=INTEGER},
      task_status = #{record.taskStatus,jdbcType=VARCHAR},
      task_priority = #{record.taskPriority,jdbcType=VARCHAR},
      room_id = #{record.roomId,jdbcType=VARCHAR},
      room_name = #{record.roomName,jdbcType=VARCHAR},
      publish_status = #{record.publishStatus,jdbcType=INTEGER},
      frequency_type = #{record.frequencyType,jdbcType=INTEGER},
      create_source = #{record.createSource,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=BIGINT},
      xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=BIGINT,
      excute_user_id = #{excuteUserId,jdbcType=BIGINT},
      workshop_ids = #{workshopIds,jdbcType=NVARCHAR},
      task_steps = #{taskSteps,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.WxTaskMain">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    update wx_task_main
    <set>
      <if test="tmbTypeCode != null">
        tmb_type_code = #{tmbTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskMbId != null">
        task_mb_id = #{taskMbId,jdbcType=BIGINT},
      </if>
      <if test="tmbClassify != null">
        tmb_classify = #{tmbClassify,jdbcType=INTEGER},
      </if>
      <if test="execUserType != null">
        exec_user_type = #{execUserType,jdbcType=INTEGER},
      </if>
      <if test="innerOrgType != null">
        inner_org_type = #{innerOrgType,jdbcType=INTEGER},
      </if>
      <if test="innerOrgId != null">
        inner_org_id = #{innerOrgId,jdbcType=INTEGER},
      </if>
      <if test="taskDescription != null">
        task_description = #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskFinishTime != null">
        task_finish_time = #{taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCreateUser != null">
        task_create_user = #{taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkItemStatus != null">
        check_item_status = #{checkItemStatus,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskPriority != null">
        task_priority = #{taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        room_name = #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="frequencyType != null">
        frequency_type = #{frequencyType,jdbcType=INTEGER},
      </if>
      <if test="createSource != null">
        create_source = #{createSource,jdbcType=INTEGER},
      </if>
       <if test="sourceTaskPid != null" >
        source_task_pid = #{sourceTaskPid,jdbcType=BIGINT},
      </if>
      <if test="finishDay != null" >
        finish_day = #{finishDay,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = getdate(),
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=BIGINT},
      </if>
        xg_sj = getdate(),
      <if test="xgUser != null">
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="excuteUserId != null" >
        excute_user_id = #{excuteUserId,jdbcType=BIGINT},
      </if>
      <if test="workshopIds != null" >
        workshop_ids = #{workshopIds,jdbcType=NVARCHAR},
      </if>
      <if test="taskSteps != null" >
        task_steps = #{taskSteps,jdbcType=NVARCHAR},
      </if>
    </set>
    where task_main_id = #{taskMainId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.WxTaskMain">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 15 18:11:01 CST 2015.
    -->
    update wx_task_main
    set tmb_type_code = #{tmbTypeCode,jdbcType=VARCHAR},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_mb_id = #{taskMbId,jdbcType=BIGINT},
      tmb_classify = #{tmbClassify,jdbcType=INTEGER},
      exec_user_type = #{execUserType,jdbcType=INTEGER},
      inner_org_type = #{innerOrgType,jdbcType=INTEGER},
      inner_org_id = #{innerOrgId,jdbcType=INTEGER},
      task_description = #{taskDescription,jdbcType=VARCHAR},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      task_finish_time = #{taskFinishTime,jdbcType=TIMESTAMP},
      task_create_user = #{taskCreateUser,jdbcType=BIGINT},
      check_status = #{checkStatus,jdbcType=INTEGER},
      check_item_status = #{checkItemStatus,jdbcType=INTEGER},
      task_status = #{taskStatus,jdbcType=VARCHAR},
      task_priority = #{taskPriority,jdbcType=VARCHAR},
      room_id = #{roomId,jdbcType=VARCHAR},
      room_name = #{roomName,jdbcType=VARCHAR},
      publish_status = #{publishStatus,jdbcType=INTEGER},
      frequency_type = #{frequencyType,jdbcType=INTEGER},
      create_source = #{createSource,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=BIGINT},
      xg_sj = getdate(),
      xg_user = #{xgUser,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      excute_user_id = #{excuteUserId,jdbcType=BIGINT},
      workshop_ids = #{workshopIds,jdbcType=NVARCHAR},
      task_steps = #{taskSteps,jdbcType=NVARCHAR}
    where task_main_id = #{taskMainId,jdbcType=BIGINT}
  </update>
  
 <!-- 根据模板id  查询创建任务所需信息 -->
  <select id="selectByMbId" parameterType="java.lang.Long" resultMap="BaseResultMap">

    select  m.mb_id as task_mb_id ,m.tmb_type_code as tmb_type_code ,m.mb_name as task_name,
	tm.check_item_status,tm.check_status,tm.exec_user_type,
	tm.inner_org_type,tm.tmb_classify
	 from wx_task_mb m INNER JOIN  wx_tasktype_mb  tm 
	 on (m.mb_id = #{taskMainId,jdbcType=BIGINT}  and  m.tmb_id = tm.tmb_id) 
  </select>
  
  
  <select id="selectTaskMainInfo" parameterType="com.chevron.task.model.WxTaskMainExample" resultMap="BaseResultMap">

  select
      distinct 
      
 m.task_main_id, m.tmb_type_code, m.task_name, m.task_mb_id, m.tmb_classify, m.exec_user_type, 
    m.inner_org_type, m.task_description, m.task_start_time, m.task_finish_time, m.task_create_user, 
    m.check_status, m.check_item_status, m.task_status, m.task_priority, m.room_id, m.room_name, 
    m.publish_status, m.frequency_type, m.create_source, source_task_pid, finish_day,     
    m.create_time, m.create_user, m.xg_sj, m.xg_user, 
    m.status, m.tenant_id ,
    (select u.ch_name from wx_t_user u where u.user_id=m.create_user  ) as create_user_name
    
    from wx_task_main  m
      <if test="deptIdList != null">
      	inner join (
      	select distinct sub.task_main_id from wx_task_sub sub 
      	where sub.org_id in
							<foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
										#{item}
							</foreach>
				
      	) ts1  on(
	      	<trim  prefixOverrides="and|or" >
		      	<if test="_parameter != null">
				      <include refid="Example_noWhere_Clause" />
				</if>
				
				<if test="createUser != null or execUser != null or watchUser != null">
			    and 
			    <trim prefix="(" prefixOverrides="or" suffix=")">
				    <if test="createUser != null">
						m.create_user =#{execUser,jdbcType=BIGINT}
					</if>
					<if test="execUser != null">
					  or 
					   m.task_main_id in(
					   select ts.task_main_id from wx_task_sub ts where ts.task_main_id=m.task_main_id and 
					   	ts.exec_user = #{execUser,jdbcType=BIGINT}
					   	)
				  	</if>
				  	<if test="watchUser != null">
					   or  
						m.task_main_id in(
							select  ru.task_id from  wx_relation_user ru where ru.source_type=3  and ru.relation_user_type=2 and ru.`status`=1 and
					   ru.task_id = m.task_main_id
					   and  ru.user_id=#{watchUser,jdbcType=BIGINT}
						)
					</if>
				</trim>	
				</if>
		      	and 
		      	 m.task_main_id=ts1.task_main_id 
			</trim>	
		)
		</if>
	<if test="deptIdList == null">
	  <where> 
	  	 <trim prefix="" prefixOverrides="and|or" suffix="">
	  	 
		    <if test="_parameter != null">
		      <include refid="Example_noWhere_Clause" />
		    </if>
		    <if test="createUser != null or execUser != null or watchUser != null">
			    and 
		    <trim prefix="(" prefixOverrides="or" suffix=")">
			    <if test="createUser != null">
					m.create_user =#{execUser,jdbcType=BIGINT}
				</if>
				<if test="execUser != null">
				  or 
				   m.task_main_id in(
				   select ts.task_main_id from wx_task_sub ts where ts.task_main_id=m.task_main_id and 
				   	ts.exec_user = #{execUser,jdbcType=BIGINT}
				   	)
			  	</if>
			  	<if test="watchUser != null">
				   or  
					m.task_main_id in(
						select  ru.task_id from  wx_relation_user ru where ru.source_type=3  and ru.relation_user_type=2 and ru.`status`=1 and
				   ru.task_id = m.task_main_id
				   and  ru.user_id=#{watchUser,jdbcType=BIGINT}
					)
				</if>
			</trim>	
			</if>
		</trim>
    </where>
    </if>
     <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
 
    
  </select>
  <sql id="qxorg_sql">
      (
		select DISTINCT role_org.org_id  from(
			select  source_id  as org_id from wx_t_rolesource where role_id in (
			select role_id from wx_t_userrole where user_id = #{curUser,jdbcType=BIGINT} 
			
			) and rs_type = 4
				union 
			select source_id as org_id from wx_t_userbase where rs_type = 4 
			and user_id = #{curUser,jdbcType=BIGINT}  
      			union
      		select org_id  from wx_t_user u where u.user_id =#{curUser,jdbcType=BIGINT} 
      		)as role_org
      )   
  </sql>
  <sql id="deptqxorg_sql">
	  (	select 
	      	org.org_id
	      	from  (select o.org_id,o.cmscode from  wx_t_org  o ,   
	      	(
	      		select DISTINCT role_org.org_id  from(
				select  rs.source_id as org_id from wx_t_rolesource rs,wx_t_userrole ur 
				where ur.user_id =  #{curUser,jdbcType=BIGINT}  and rs.role_id=ur.role_id
				and rs.rs_type = 4 
					union 
				select source_id as org_id from wx_t_userbase where rs_type = 4 and
				 user_id = #{curUser,jdbcType=BIGINT}
				union
	      		select org_id  from wx_t_user u where u.user_id =#{curUser,jdbcType=BIGINT}
				)as role_org
	      			
	      	)as qxorg1 
	      	where  o.org_root_id = 1   
	      	and   qxorg1.org_id = o.org_id	
	      	<![CDATA[and o.dept_type<>0]]>
	      	and o.status = 1        	
	      	<if test="dept.deptCodeOrName != null">
	       		and  (
	       		o.org_code like  CONCAT(CONCAT('%', #{dept.deptCodeOrName,jdbcType=VARCHAR}),'%')
	       		or  o.org_name like CONCAT(CONCAT('%', #{dept.deptCodeOrName,jdbcType=VARCHAR}),'%')
	       		)
	        </if>
	      	 
	      	) as org      	
	      	inner join wx_dept_option dept on ( org.cmscode = dept.cmscode and dept.status = 1)
	      <where>
	      <trim prefix="(" prefixOverrides="and" suffix=")">
		      <if test="dept.brandList != null">
		      	and brand in
		     	<foreach item="item" index="index" collection="dept.brandList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.dplxList != null">
		      	and dplx in
		     	<foreach item="item" index="index" collection="dept.dplxList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.scjbList != null">
		      	and scjb in
		     	<foreach item="item" index="index" collection="dept.scjbList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.dpjbList != null">
		      	and dpjb in
		     	<foreach item="item" index="index" collection="dept.dpjbList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.zdjbList != null">
		      	and lsdpjb in
		     	<foreach item="item" index="index" collection="dept.zdjbList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.cmstypeList != null">
		      	and cmstype in
		     	<foreach item="item" index="index" collection="dept.cmstypeList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.sqjbList != null">
		      	and sqjb in
		     	<foreach item="item" index="index" collection="dept.sqjbList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.jylxList != null">
		      	and jylx in
		     	<foreach item="item" index="index" collection="dept.jylxList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.dpxzList != null">
		      	and dpxz in
		     	<foreach item="item" index="index" collection="dept.dpxzList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.qjlxList != null">
		      	and qjlx in
		     	<foreach item="item" index="index" collection="dept.qjlxList" open="(" separator="," close=")">
		     		#{item}
		     	</foreach>
		      </if>
		      <if test="dept.area != null and dept.area == 'AREA01'">
		      	<![CDATA[and dept.area > 0 and dept.area < 200]]>
		      </if>
		      <if test="dept.area != null and dept.area == 'AREA02'">
		      	<![CDATA[and dept.area >= 200]]>
		      </if>
	      </trim>
	      </where>  
	
	)
   </sql>  
  <!-- 特殊任务主任务查询   （（当前人为任务创建人 并且（修改人为空 或者修改人等于当前人） 并且任务状态为暂存  ） 
  	或者     (当前人为任务负责人  并且  （如果任务状态为提交 并且当前人不等于修改人） 或者（任务状态为暂存 当前人等于修改人）)  	
  	） 	
   当前人可修改任务-->
   <!-- /*GROUP_CONCAT(
	 CASE when sub.exec_user=#{curUser,jdbcType=BIGINT} or sub.confirm_user=#{curUser,jdbcType=BIGINT} THEN sub.task_status
	 end )*/'' as sub_status -->
  <select id="selectTaskMainInfoSubStatus" parameterType="com.chevron.task.model.WxTaskMainExample" resultMap="BaseResultMap">
	select tasks.*, 
 CASE when (tasks.sub_status is not null and tasks.main_status!='11')  THEN tasks.sub_status else tasks.main_status
 end as task_status  
	 from (
	  select
	      distinct       
 	m.task_main_id, m.tmb_type_code, m.task_name, m.task_mb_id, m.tmb_classify,
    m.inner_org_type, m.task_description, m.task_start_time, m.task_finish_time, m.task_create_user, 
    m.publish_status, m.frequency_type, m.create_source, 
    source_task_pid, finish_day, 
    m.create_time, m.create_user, m.xg_sj, m.xg_user, 
    m.status,
	(select u.ch_name from wx_t_user u where u.user_id=m.create_user ) as create_user_name,
	case when ( m.tmb_type_code ='TASKTYPE014')
	then
		CASE when 
		
		 (m.create_user=#{curUser,jdbcType=BIGINT} 
		 and (m.xg_user is null or m.xg_user=#{curUser,jdbcType=BIGINT} ) and m.task_status='11' )
		THEN 1 
		when  (sub.exec_user = #{curUser,jdbcType=BIGINT} and 
		    
			(   <![CDATA[ (m.task_status='1' and  m.xg_user<>#{curUser,jdbcType=BIGINT}		          
			     )]]> 
			     <!-- or m.xg_user is null -->
				or
				(m.task_status='11' and  m.xg_user=#{curUser,jdbcType=BIGINT})
			 )
			)
		then 1
		else 0 
		end 
	end  as  operate,
	'' as sub_status,
	m.task_status as main_status
    from wx_task_main  m
      ,wx_task_sub sub,
		<if test="dept == null">
			<include refid="qxorg_sql" />
		</if>
		<if test="dept != null">
			<include refid="deptqxorg_sql" />
		</if>
		as qxorg
         where 
      	 <trim  prefixOverrides="and|or" >
		      	<if test="_parameter != null">
				      <include refid="Example_noWhere_Clause" />
				</if>
				<!-- 如果三者都要匹配  则直接不带此三条件 -->
				<if test="createUser != null  or watchUser != null">
			    and 
			    <trim prefix="(" prefixOverrides="or" suffix=")">
				    <if test="createUser != null">
						m.create_user =#{createUser,jdbcType=BIGINT}
					</if>
					
				  	<if test="watchUser != null">
					  or  
						m.task_main_id in(
							select  ru.task_id from  wx_relation_user ru where 
							ru.source_type=3  and
					   ru.task_id = m.task_main_id
					   and  ru.user_id=#{watchUser,jdbcType=BIGINT}
					   and ru.relation_user_type=2 
							and ru.`status`=1 
						)
					</if>
				</trim>	
				</if>
				<if test="execUser != null">
					and m.publish_status=1
					  and (
					  (sub.exec_user = #{execUser,jdbcType=BIGINT}
					   or sub.confirm_user = #{execUser,jdbcType=BIGINT}
					   )
				   	   <if test="statusList != null">
				   			and 
				   			sub.task_status in
				   			<foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
									#{item}
							</foreach>
   						</if>

					   )
				 </if>
		      	and 
		      	 m.task_main_id=sub.task_main_id  and sub.org_id =qxorg.org_id
		</trim>	
 
 
 )as  tasks  
 <if test="execUser == null"> 
 	<where>
 	 <trim  prefixOverrides="and|or" >
 	 <!-- tasks.task_status='11' or  frequency_type =2 -->
 		(  ( 
 		((tasks.create_user =#{curUser,jdbcType=BIGINT} and (tasks.xg_user=#{curUser,jdbcType=BIGINT} or tasks.xg_user is null)) or 
		(tasks.create_user !=#{curUser,jdbcType=BIGINT} and tasks.xg_user=#{curUser,jdbcType=BIGINT}))
 		<![CDATA[ and (publish_status<>1)  ) or  
 		  (publish_status=1)]]>
 		) 
	   </trim>
    </where>
    </if>
     <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
  </select> 

<!-- /*GROUP_CONCAT(
	 CASE when sub.exec_user=#{curUser,jdbcType=BIGINT} or sub.confirm_user=#{curUser,jdbcType=BIGINT} THEN sub.task_status
	 end )*/'' as sub_status -->
  <select id="selectTaskMainInfoSubStatusNoExec" parameterType="com.chevron.task.model.WxTaskMainExample" resultMap="BaseResultMap">
	select *  
	 from (
	 select ts1.*,
	  CASE when (ts1.sub_status is not null  and ts1.main_status!='11') THEN ts1.sub_status else ts1.main_status
 		end as task_status 
	  from (
	  select
	      distinct       
 	m.task_main_id, m.tmb_type_code, m.task_name, m.task_mb_id, m.tmb_classify,
    m.inner_org_type, m.task_description, m.task_start_time, m.task_finish_time, m.task_create_user, 
    m.publish_status, m.frequency_type, m.create_source, 
    source_task_pid, finish_day, 
    m.create_time, m.create_user, m.xg_sj, m.xg_user, 
    m.status,
	(select u.ch_name from wx_t_user u where u.user_id=m.create_user ) as create_user_name,
	case when ( m.tmb_type_code ='TASKTYPE014')
	then
		CASE when 
		
		 (m.create_user=#{curUser,jdbcType=BIGINT} 
		 and (m.xg_user is null or m.xg_user=#{curUser,jdbcType=BIGINT} ) and m.task_status='11' )
		THEN 1 
		when  (sub.exec_user = #{curUser,jdbcType=BIGINT} and 
		    
			(   <![CDATA[ (m.task_status='1' and  m.xg_user<>#{curUser,jdbcType=BIGINT}		          
			     )]]> 
			     <!-- or m.xg_user is null -->
				or
				(m.task_status='11' and  m.xg_user=#{curUser,jdbcType=BIGINT})
			 )
			)
		then 1
		else 0 
		end 
	end  as  operate,
	'' as sub_status,
	m.task_status as main_status
    from wx_task_main  m
      ,wx_task_sub sub,
		<if test="dept == null">
			<include refid="qxorg_sql" />
		</if>
		<if test="dept != null">
			<include refid="deptqxorg_sql" />
		</if>
		as qxorg
         where 
      	 <trim  prefixOverrides="and|or" >
		      	<if test="_parameter != null">
				      <include refid="Example_noWhere_Clause" />
				</if>
				<!-- 如果三者都要匹配  则直接不带此三条件 -->
				<if test="createUser != null  or watchUser != null">
			    and 
			    <trim prefix="(" prefixOverrides="or" suffix=")">
				    <if test="createUser != null">
						m.create_user =#{createUser,jdbcType=BIGINT}
					</if>
					
				  	<if test="watchUser != null">
					  or  
						m.task_main_id in(
							select  ru.task_id from  wx_relation_user ru where 
							ru.source_type=3  and
					   ru.task_id = m.task_main_id
					   and  ru.user_id=#{watchUser,jdbcType=BIGINT}
					   and ru.relation_user_type=2 
							and ru.`status`=1 
						)
					</if>
				</trim>	
				</if>
				<if test="execUser != null">
					and m.publish_status=1
					  and (
					  (sub.exec_user = #{execUser,jdbcType=BIGINT}
					   or sub.confirm_user = #{execUser,jdbcType=BIGINT}
					   )
				   	   <if test="statusList != null">
				   			and 
				   			sub.task_status in
				   			<foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
									#{item}
							</foreach>
   						</if>

					   )
				 </if>
		      	and 
		      	 m.task_main_id=sub.task_main_id  and sub.org_id =qxorg.org_id
		</trim>	
  ) as  ts1
 
 )as  tasks  
 <if test="execUser == null"> 
 	<where>
 	 <trim  prefixOverrides="and|or" >
 	 <!-- tasks.task_status='11' or  frequency_type =2 -->
 		(  ( 		
 		((tasks.create_user =#{curUser,jdbcType=BIGINT} and (tasks.xg_user=#{curUser,jdbcType=BIGINT} or tasks.xg_user is null)) or 
		(tasks.create_user !=#{curUser,jdbcType=BIGINT} and tasks.xg_user=#{curUser,jdbcType=BIGINT}))
		<![CDATA[ and (publish_status<>1)  ) or  
 		  (publish_status=1)]]>
 		) 
	   <if test="statusList != null">
	   		and 
	   			tasks.task_status in
	   			<foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
						#{item}
				</foreach>
	   		
	   </if>
	   </trim>
    </where>
    </if>
     <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
  </select>   
  <update id="updateByInnerOrgId" parameterType="map">
    update    wx_task_main 
    set inner_org_id = #{newOrgId,jdbcType=BIGINT} 
    where inner_org_id = #{oldOrgId,jdbcType=BIGINT}
  </update>
  
  <select id="selectPtaskAndTask" resultMap="BaseResultMap">
 
		select DISTINCT *  from (
		select * from  wx_task_main  tm where tm.source_task_pid in(
		select m.source_task_pid from wx_task_main m where 
		m.task_main_id in
	    <foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
      			#{item}
         </foreach>		
		)
		and 
		(tm.frequency_type=2 or tm.create_source=2)
		UNION
		select * from  wx_task_main  tm where tm.source_task_pid in
		<foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
      			#{item}
         </foreach>
		and 
		(tm.frequency_type=2 or tm.create_source=2)
		
		UNION		
		select  * from  wx_task_main  m2  where m2.task_main_id in
	    <foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
      			#{item}
         </foreach>	
		) as  m3 where  (m3.task_status!=0 and m3.task_status!=11) ORDER BY  m3.create_time
	</select>
	<!-- 根据taskMainIds查询所有周期的主任务和非周期的主任务 -->
   <select id="selPTaskByAllZqAndNotZq" resultMap="BaseResultMap">
	   select DISTINCT * from (
		select * from wx_task_main m1 where m1.task_main_id in
	 	<foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
	      			#{item}
	    </foreach>
	    and m1.frequency_type=2
		UNION
		select * from wx_task_main m3 where m3.task_main_id in(
		select DISTINCT m2.source_task_pid from wx_task_main m2 where m2.task_main_id in
		 	<foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
		      	#{item}
		    </foreach>
	     and m2.create_source=2
		) 
		UNION
		select * from wx_task_main m4 where m4.task_main_id in
		 	<foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
		      	#{item}
		    </foreach>	
		 and  m4.frequency_type!=2 and m4.create_source !=2
		)as tm  ORDER BY tm.create_time asc
    </select>
    <!-- 根据主任务的第一个周期的id 查询周期数 -->
    <select id="selTaskZqCountByPId" resultMap="BaseResultMap">
    	select * from wx_task_main m1 where m1.task_main_id= #{taskMainPId,jdbcType=BIGINT} 
    	 or (
 		m1.source_task_pid=#{taskMainPId,jdbcType=BIGINT} 
 		  and create_source =2) ORDER BY m1.create_time asc
    </select>
    <!-- 根据父任务id  查询taskMainIds里面同一个主任务的数据 -->
   <select id="selTaskByIdsInOnePid" resultMap="BaseResultMap">
   	select * from wx_task_main m1 where m1.task_main_id in
		<foreach item="item" index="index" collection="taskMainIdList" open="(" separator="," close=")">
		     #{item}
		</foreach>	
 	and (m1.task_main_id= #{taskMainPId,jdbcType=BIGINT} 
 	   or (m1.source_task_pid =#{taskMainPId,jdbcType=BIGINT} and create_source =2)
	) ORDER BY m1.create_time asc
   </select>  
   
   <!-- by bo.liu start -->
   <!-- 查询住任务列表，，可根据条件选择查询 -->
   <select id="getMainTaskByTaskTypeOrParams" parameterType="map" resultMap="BaseResultMap">
   SELECT 
   <if test="limit!=null and limit!=-1">
    top #{limit}
   </if>
   tt2.*,(SELECT tt_org.organization_name FROM  wx_t_organization tt_org WHERE tt_org.id = tt2.excute_tenant_id) AS organizationname 
   from
   (
    select
	max(t.IS_TO_LD) AS IS_TO_LD ,max(t.IS_TO_SD) AS IS_TO_SD,
	<if test="start != -1 and start != null">
          row_number() over(order by #{order}) as rownumber,
    </if>
	t.task_main_id ,   t.tmb_type_code,t.task_name,t.task_mb_id,
	t.tmb_classify,t.exec_user_type,t.inner_org_type,
	t.inner_org_id,t.task_description,t.task_start_time,
	t.task_finish_time,t.task_create_user,t.check_status,
	t.check_item_status,t.task_status,t.task_priority,t.room_id,
	t.room_name,t.publish_status,t.frequency_type,t.create_source,
	t.source_task_pid,t.finish_day,t.create_time,t.create_user,
	t.xg_sj,t.xg_user,t.status,t.tenant_id,
	t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name ,
	t.exec_user_name,t.handle_task_priority, t.excute_tenant_id
    FROM(
   	 SELECT DISTINCT(task_main.task_main_id), 
		task_main.tmb_type_code,task_main.task_name,task_main.task_mb_id,
		task_main.tmb_classify,task_main.exec_user_type,task_main.inner_org_type,
		task_main.inner_org_id,task_main.task_description,task_main.task_start_time,
		task_main.task_finish_time,task_main.task_create_user,task_main.check_status,
		task_main.check_item_status,task_main.task_status,task_main.task_priority,task_main.room_id,
		task_main.room_name,task_main.publish_status,task_main.frequency_type,task_main.create_source,
		task_main.source_task_pid,task_main.finish_day,task_main.create_time,task_main.create_user,
		task_main.xg_sj,task_main.xg_user,task_main.status,task_main.tenant_id,
		task_main.excute_user_id,task_main.workshop_ids,task_main.task_steps,
		t_user.ch_name create_user_name,
		(SELECT tt_user.ch_name  FROM wx_t_user  tt_user WHERE tt_user.user_id = task_main.excute_user_id ) AS exec_user_name,
		<if test="taskTypeCodeModel==1">
			t_workshop.work_shop_name workshop_name,t_workshop.id workshop_id,t_workshop.status workshop_status,
		</if>
		(CASE task_main.task_priority 
		WHEN '1' THEN '低'
		WHEN '2' THEN '中'
		WHEN '3' THEN '高'
		WHEN '4' THEN '紧急'
		ELSE '其他' END) handle_task_priority,
		<!-- ADD BY BO.LIU 1105 START -->
		(CASE WHEN t_sub.task_status='4' AND t_workshop.status='0'  THEN '1' ELSE '0' end) AS IS_TO_LD,
        (CASE WHEN t_sub.task_status='4' AND (t_workshop.excute_user_id=NULL OR t_workshop.excute_user_id=-1)  THEN '1' ELSE '0' end) AS IS_TO_SD,
		<!-- ADD BY BO.LIU 1105 END -->
		(SELECT tt_user1.org_id FROM wx_t_user tt_user1 WHERE tt_user1.user_id = task_main.excute_user_id ) AS excute_tenant_id
	FROM
		wx_task_main task_main 
	left JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id  
	left JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id 
  	left JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
  	
	where
	<if test="tenantId!=1">
		(task_main.create_user = #{currentId,jdbcType=BIGINT} or task_main.excute_user_id = #{currentId,jdbcType=BIGINT}  
		 OR task_main.tenant_id=#{orgId,jdbcType=BIGINT}  
		)
		and
	</if>
   	<if test="taskTypeCode!=null">
   		task_main.tmb_type_code = #{taskTypeCode,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="taskName!=null">
   		task_main.task_name like '%' + #{taskName} + '%'
   		and
   	</if>
   	<if test="taskMainStauts!=null">
   		task_main.task_status = #{taskMainStauts,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isCreateUser==true">
   		task_main.create_user = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isWatchUser==true">
   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isExcuteUser==true">
   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="taskExcuteUID!=null">
   		task_main.excute_user_id = #{taskExcuteUID,jdbcType=BIGINT}
   		and
   	</if>
   	
   	<if test="workshopName!=null">
   		t_workshop.work_shop_name like '%' + #{workshopName} + '%'
   		and
   	</if>
   	<if test="workshopScale!=null">
   		t_workshop.ext_property15 like '%' + #{workshopScale} + '%'
   		and
   	</if>
   	<if test="taskStartTime!=null and taskStartTime!=''">
   		 <![CDATA[  CONVERT(varchar(100), task_main.task_start_time, 23) >=  CONVERT(varchar(100), #{taskStartTime}, 23)  and ]]>
    </if>
    <if test="taskEndTime!=null and taskEndTime!=''">
    	<![CDATA[   CONVERT(varchar(100), task_main.task_finish_time, 23) <= CONVERT(varchar(100), #{taskEndTime}, 23)    and ]]>
    </if>
    <if test="queryField!=null">
   		(t_workshop.work_shop_name like '%' + #{queryField} + '%'
   	or
   		t_workshop.ext_property15 like '%' + #{queryField} + '%'
   	or	task_main.task_name  LIKE '%' + #{queryField} + '%' 
	OR  t_user.ch_name LIKE '%' + #{queryField} + '%'
	OR  (SELECT tt_user.ch_name FROM wx_t_user tt_user WHERE tt_user.user_id = task_main.excute_user_id ) LIKE '%' + #{queryField} + '%'
	OR  (CASE task_main.task_priority 
		WHEN '1' THEN '低'
		WHEN '2' THEN '中'
		WHEN '3' THEN '高'
		WHEN '4' THEN '紧急'
		ELSE '其他' END) LIKE '%' + #{queryField} + '%'
	or  left(dbo.f_GetPyToAboutHanyu(task_main.task_name),500) LIKE #{firtPyForQueryField} + '%'
		)
   	and
   </if>
   <if test="tenantId==1 and partnerId!=null and partnerId!=''">
 	    t_user.org_id = #{partnerId}  AND 
   </if>
   
    	t_user.ch_name IS  NOT NULL
    and 
   	1 = 1
   	)AS t  
	GROUP BY 
	t.task_main_id ,t.tmb_type_code,t.task_name,t.task_mb_id,
	t.tmb_classify,t.exec_user_type,t.inner_org_type,
	t.inner_org_id,t.task_description,t.task_start_time,
	t.task_finish_time,t.task_create_user,t.check_status,
	t.check_item_status,t.task_status,t.task_priority,t.room_id,
	t.room_name,t.publish_status,t.frequency_type,t.create_source,
	t.source_task_pid,t.finish_day,t.create_time,t.create_user,
	t.xg_sj,t.xg_user,t.status,t.tenant_id,
	t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name,
	t.exec_user_name,t.handle_task_priority,t.excute_tenant_id
	) as tt2 	
	where
   	1 = 1
   	<if test="start != -1 and start != null">
		  and tt2.rownumber > #{start, jdbcType=INTEGER} 
	</if>
	<!-- add by bo.liu 1116 -->
	<!-- <if test="queryField!=null">
		and 
		(tt2.task_name LIKE '%${queryField}%' 
		OR tt2.create_user_name LIKE '%${queryField}%'
		OR tt2.exec_user_name LIKE '%${queryField}%'
		OR tt2.handle_task_priority LIKE '%${queryField}%')
	</if> -->
	<!--ORDER BY   tt2.create_time DESC,tt2.task_priority DESC,tt2.task_finish_time DESC  -->
   </select>  
   
   <select id="getMainIds"  resultType="java.lang.Long">
	SELECT  max(wx_task_main.task_main_id) FROM
	wx_task_main
   </select> 
   
   
  <select id="getMainTaskById"  resultType="java.lang.Long" resultMap="BaseResultMap">
  SELECT ttt2.*, CASE ttt2.temp_status WHEN '0' THEN '未生效' WHEN '1' THEN '待处理' WHEN '2' THEN '处理中' WHEN '3' THEN '待整改' WHEN '4' THEN '完成' WHEN '6' THEN '延期' WHEN '7' THEN '已取消' WHEN '8' THEN '完成待攻店' WHEN '9' THEN '完成已攻店' WHEN '10' THEN '完成待录店' WHEN '11' THEN '完成已录店' ELSE '其他' END AS handle_task_status
FROM (SELECT ttt.*, CASE WHEN IS_TO_LD = '0'
		AND ttt.task_status = '4'
		AND ttt.tmb_type_code = 'TT_2_LD' THEN '11' WHEN IS_TO_LD = '1'
		AND ttt.task_status = '4'
		AND ttt.tmb_type_code = 'TT_2_LD' THEN '10' WHEN IS_TO_GD = '0'
		AND ttt.task_status = '4'
		AND ttt.tmb_type_code = 'TT_2_SD' THEN '9' WHEN IS_TO_GD = '1'
		AND ttt.task_status = '4'
		AND ttt.tmb_type_code = 'TT_2_SD' THEN '8' ELSE ttt.task_status END AS temp_status
	FROM (
	 SELECT 
          max(t.IS_TO_LD) AS IS_TO_LD, 
     	  max(t.IS_TO_GD) AS IS_TO_GD, 
          t.task_main_id, 
              t.tmb_type_code, 
              t.task_name, 
              t.task_mb_id, 
              t.task_description, 
              t.task_start_time, 
              t.task_finish_time, 
              t.task_create_user, 
              t.task_status, 
              t.task_priority, 
              t.publish_status, 
              t.frequency_type, 
              t.create_source, 
              t.source_task_pid, 
              t.finish_day, 
              t.create_time, 
              t.create_user, 
              t.xg_sj, 
              t.xg_user, 
              t.status, 
              t.tenant_id, 
              t.excute_user_id, 
              t.workshop_ids, 
              t.task_steps, 
              t.create_user_name, 
              t.exec_user_name, 
              t.handle_task_priority, 
              t.excute_tenant_id,
              t.in_stock_no,
              t.out_stock_no,
			  t.order_no
		FROM (SELECT DISTINCT task_main.task_main_id, task_main.tmb_type_code, task_main.task_name, task_main.task_mb_id, task_main.tmb_classify
				, task_main.exec_user_type, task_main.inner_org_type, task_main.inner_org_id, task_main.task_description, task_main.task_start_time
				, task_main.task_finish_time, task_main.task_create_user, task_main.check_status, task_main.check_item_status, task_main.task_status
				, task_main.task_priority, task_main.room_id, task_main.room_name, task_main.publish_status, task_main.frequency_type
				, task_main.create_source, task_main.source_task_pid, task_main.finish_day, task_main.create_time, task_main.create_user
				, task_main.xg_sj, task_main.xg_user, task_main.status, task_main.tenant_id, task_main.excute_user_id
				, task_main.workshop_ids, task_main.task_steps, t_user.ch_name AS create_user_name, (
					SELECT tt_user.ch_name
					FROM wx_t_user tt_user
					WHERE tt_user.user_id = task_main.excute_user_id
					) AS exec_user_name, CASE task_main.task_priority WHEN '1' THEN '低' WHEN '2' THEN '中' WHEN '3' THEN '高' WHEN '4' THEN '紧急' ELSE '其他' END AS handle_task_priority
				, CASE WHEN t_gd.isgd_mainid IS NULL THEN '0' ELSE '1' END AS IS_TO_GD, CASE WHEN t_sub.task_status = '4'
				AND t_workshop.status = '1' THEN '1' ELSE '0' END AS IS_TO_LD, (
					SELECT tt_user1.org_id
					FROM wx_t_user tt_user1
					WHERE tt_user1.user_id = task_main.excute_user_id
					) AS excute_tenant_id, task_main.in_stock_no, task_main.out_stock_no
				, task_main.order_no
			FROM wx_task_main task_main
				LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
				LEFT JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id
				LEFT JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
				LEFT JOIN (SELECT task_main.task_main_id AS isgd_mainid
					FROM wx_task_main task_main
						LEFT JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id
						LEFT JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
					WHERE t_sub.task_status = '4'
						AND t_workshop.status = '0'
						AND task_main.task_main_id = #{taskMainId,jdbcType=BIGINT} 
						AND t_sub.org_id NOT IN (SELECT task_sub2.org_id
							FROM wx_task_sub task_sub2
							WHERE EXISTS (
								SELECT 1
								FROM wx_task_main tt_main1
								WHERE tt_main1.tmb_type_code = 'TT_2_LD'
									AND task_sub2.task_main_id = tt_main1.task_main_id
								))
					) t_gd ON t_sub.task_main_id = t_gd.isgd_mainid
			) t
		WHERE t.task_main_id = #{taskMainId,jdbcType=BIGINT} 
		GROUP BY t.task_main_id, 
              t.tmb_type_code, 
              t.task_name, 
              t.task_mb_id, 
              t.task_description, 
              t.task_start_time, 
              t.task_finish_time, 
              t.task_create_user, 
              t.task_status, 
              t.task_priority, 
              t.publish_status, 
              t.frequency_type, 
              t.create_source, 
              t.source_task_pid, 
              t.finish_day, 
              t.create_time, 
              t.create_user, 
              t.xg_sj, 
              t.xg_user, 
              t.status, 
              t.tenant_id, 
              t.excute_user_id, 
              t.workshop_ids, 
              t.task_steps, 
              t.create_user_name, 
              t.exec_user_name, 
              t.handle_task_priority, 
              t.excute_tenant_id,
              t.in_stock_no,
              t.out_stock_no,
			  t.order_no
		) ttt
	) ttt2
  
  
  <!-- 
     SELECT 
  ttt2.*, 
  (
    CASE ttt2.temp_status WHEN '0' THEN '未生效' WHEN '1' THEN '待处理' WHEN '2' THEN '处理中' WHEN '3' THEN '待整改' WHEN '4' THEN '完成' WHEN '6' THEN '延期' WHEN '7' THEN '已取消' WHEN '8' THEN '完成待攻店' WHEN '9' THEN '完成已攻店' WHEN '10' THEN '完成待录店' WHEN '11' THEN '完成已录店' ELSE '其他' END
  ) AS handle_task_status 
FROM 
  (
    SELECT 
      ttt.*, 
      (
        CASE WHEN IS_TO_LD = '0' 
        AND ttt.task_status = '4' 
        AND ttt.tmb_type_code = 'TT_2_LD' THEN '11' WHEN IS_TO_LD = '1' 
        AND ttt.task_status = '4' 
        AND ttt.tmb_type_code = 'TT_2_LD' THEN '10' WHEN IS_TO_GD = '0' 
        AND ttt.task_status = '4' 
        AND ttt.tmb_type_code = 'TT_2_SD' THEN '9' WHEN IS_TO_GD = '1' 
        AND ttt.task_status = '4' 
        AND ttt.tmb_type_code = 'TT_2_SD' THEN '8' ELSE ttt.task_status END
      ) AS temp_status 
    from 
      (
        SELECT 
          max(t.IS_TO_LD) AS IS_TO_LD, 
          max(t.IS_TO_GD) AS IS_TO_GD, 
          t.task_main_id, 
              t.tmb_type_code, 
              t.task_name, 
              t.task_mb_id, 
              t.tmb_classify, 
              t.exec_user_type, 
              t.inner_org_type, 
              t.inner_org_id, 
              t.task_description, 
              t.task_start_time, 
              t.task_finish_time, 
              t.task_create_user, 
              t.check_status, 
              t.check_item_status, 
              t.task_status, 
              t.task_priority, 
              t.room_id, 
              t.room_name, 
              t.publish_status, 
              t.frequency_type, 
              t.create_source, 
              t.source_task_pid, 
              t.finish_day, 
              t.create_time, 
              t.create_user, 
              t.xg_sj, 
              t.xg_user, 
              t.status, 
              t.tenant_id, 
              t.excute_user_id, 
              t.workshop_ids, 
              t.task_steps, 
              t.create_user_name, 
              t.exec_user_name, 
              t.handle_task_priority, 
              t.excute_tenant_id,
              t.in_stock_no,
              t.out_stock_no,
			  t.order_no
              
        FROM 
          wx_v_query_maintask_list_new t 
        WHERE 
          t.task_main_id =  #{taskMainId,jdbcType=BIGINT} 
        GROUP BY 
          t.task_main_id, 
              t.tmb_type_code, 
              t.task_name, 
              t.task_mb_id, 
              t.tmb_classify, 
              t.exec_user_type, 
              t.inner_org_type, 
              t.inner_org_id, 
              t.task_description, 
              t.task_start_time, 
              t.task_finish_time, 
              t.task_create_user, 
              t.check_status, 
              t.check_item_status, 
              t.task_status, 
              t.task_priority, 
              t.room_id, 
              t.room_name, 
              t.publish_status, 
              t.frequency_type, 
              t.create_source, 
              t.source_task_pid, 
              t.finish_day, 
              t.create_time, 
              t.create_user, 
              t.xg_sj, 
              t.xg_user, 
              t.status, 
              t.tenant_id, 
              t.excute_user_id, 
              t.workshop_ids, 
              t.task_steps, 
              t.create_user_name, 
              t.exec_user_name, 
              t.handle_task_priority, 
              t.excute_tenant_id,
              t.in_stock_no,
              t.out_stock_no,
			  t.order_no
      ) AS ttt
  ) AS ttt2
	 -->
   </select> 
   
   
   <!-- 更新任务状态 -->
   <update id="updateWxTaskMainStatus"  parameterType="map">
   	update wx_task_main
   	set task_status = #{maintaskstatus,jdbcType=VARCHAR},
   		xg_sj = getDate()
   		<if test="xgUser!=null">
   		,
   		xg_user = #{xgUser,jdbcType=BIGINT}
   		</if>
   	where
   		task_main_id = #{mainTaskId,jdbcType=BIGINT} 
   </update>
   
   <!-- 执行定时任务的时候查询源主任务 -->
   <select id="getTaskByQuarztTaskMainId" parameterType="java.lang.Long" resultMap="BaseResultMap">
   	select 
   		*
   	from
   		wx_task_main
   	where 
   		task_main_id = #{taskMainId,jdbcType=BIGINT} 
   		
   </select>
   
   
   
   <!-- quartz -->
   <!-- 获取延期任务  需考虑到待整改状态的任务是延期的？-->
   <select id="getDelayMainTask"  resultMap="BaseResultMap">
   	SELECT
	*
	FROM
	wx_task_main task_main
	where
		<![CDATA[ CONVERT(varchar(100), dateadd(day,task_main.finish_day,task_main.task_start_time), 23) <= CONVERT(varchar(100), getdate(), 23)]]>
	AND
	task_main.task_status='1'
	<!-- task_main.task_status!='4'
	and
	task_main.task_status!='6' -->
	</select>
   		
   <!-- end -->
   <!-- 任务分页查询 add by lizhentao --> 
   <select id="countTasks" resultType="long" parameterType="com.chevron.pms.model.TaskParams">
   SELECT count(1) 
FROM wx_task_main task_main
LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
WHERE 1 = 1
	<if test="workshopId != null">
		and exists (select 1 from wx_task_sub i1 where i1.task_main_id=task_main.task_main_id and i1.org_id=#{workshopId,jdbcType=BIGINT})
	</if>
	<if test="loginUserType != 1 and loginOrgType != 1">
		and (task_main.excute_user_id is null or exists (select 1 from wx_t_user i1 where i1.user_id=task_main.excute_user_id and i1.org_id=#{loginOrg}))
		and (t_user.org_id=#{loginOrg} or not exists (select 1 from wx_t_organization i2 where i2.id=t_user.org_id and i2.type=1))
	</if>
   </select>
   <select id="selTasks" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.TaskParams">
SELECT task_main.*,
       t_user.ch_name create_user_name,
       t1.data_name tmb_type_name
FROM wx_task_main task_main
LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
LEFT JOIN wx_t_data t1 on task_main.tmb_type_code=t1.data_code
WHERE 1=1
	<if test="workshopId != null">
		and exists (select 1 from wx_task_sub i1 where i1.task_main_id=task_main.task_main_id and i1.org_id=#{workshopId,jdbcType=BIGINT})
	</if>
	<if test="loginUserType != 1 and loginOrgType != 1">
		and (task_main.excute_user_id is null or exists (select 1 from wx_t_user i1 where i1.user_id=task_main.excute_user_id and i1.org_id=#{loginOrg}))
		and (t_user.org_id=#{loginOrg} or not exists (select 1 from wx_t_organization i2 where i2.id=t_user.org_id and i2.type=1))
	</if>
   </select>
   <select id="selTasksByWorkshop" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.TaskParams">
SELECT task_main.*,
       t_user.ch_name create_user_name,
       t1.data_name tmb_type_name,
       i1.task_id subtask_id,
       i1.org_id workshop_id,
       case when i1.task_status=4 then i1.xg_sj else null end complete_time,
       (select a.work_shop_name from wx_t_work_shop a where a.id=i1.org_id) workshop_name,
       (case when task_main.tmb_type_code='TT_2_XD' then (select tic2.remark from wx_task_instance_check tic2 where tic2.sub_task_id=i1.task_id and tic2.check_id=23051840) else '-' end) xd_problem_feedback
FROM wx_task_main task_main
JOIN wx_task_sub i1 on i1.task_main_id=task_main.task_main_id
LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
LEFT JOIN wx_t_data t1 on task_main.tmb_type_code=t1.data_code
WHERE i1.org_id=#{workshopId,jdbcType=BIGINT}
	<if test="loginUserType != 1 and loginOrgType != 1">
		and (task_main.excute_user_id is null or exists (select 1 from wx_t_user i1 where i1.user_id=task_main.excute_user_id and i1.org_id=#{loginOrg}))
	</if>
	<if test="taskTypeCodes != null">
	         AND task_main.tmb_type_code in 
	         <foreach item="item" index="index" collection="taskTypeCodes" open="(" separator="," close=")">  
				 '${item}'
			</foreach> 
	</if>
	<if test="excludeTaskTypeCodes != null">
	         AND task_main.tmb_type_code not in 
	         <foreach item="item" index="index" collection="excludeTaskTypeCodes" open="(" separator="," close=")">  
				 '${item}'
			</foreach> 
	</if>
	<if test="taskStatusArray != null">
	         AND i1.task_status in 
	         <foreach item="item" index="index" collection="taskStatusArray" open="(" separator="," close=")">  
				 ${item}
			</foreach> 
	</if>
   </select>
   <select id="selTasksByFleet" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.TaskParams">
		SELECT task_main.*,
		       t_user.ch_name create_user_name,
		       t1.data_name tmb_type_name,
		       i1.task_id subtask_id,
		       i1.org_id fleet_id,
		       case when i1.task_status=4 then i1.xg_sj else null end complete_time,
		       (select a.fleet_name from wx_t_fleet_info a where a.id=i1.org_id) fleet_name,
		       (case when task_main.tmb_type_code='TT_2_XD' then (select tic2.remark from wx_task_instance_check tic2 where tic2.sub_task_id=i1.task_id and tic2.check_id=23051840) else '-' end) xd_problem_feedback
		FROM wx_task_main task_main
		JOIN wx_task_sub i1 on i1.task_main_id=task_main.task_main_id
		LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
		LEFT JOIN wx_t_data t1 on task_main.tmb_type_code=t1.data_code
		WHERE i1.org_id=#{fleetId,jdbcType=BIGINT} 
		<if test="loginUserType != 1 and loginOrgType != 1">
			and (task_main.excute_user_id is null or exists (select 1 from wx_t_user i1 where i1.user_id=task_main.excute_user_id and i1.org_id=#{loginOrg}))
		</if>
		<if test="taskTypeCodes != null">
		         AND task_main.tmb_type_code in 
		         <foreach item="item" index="index" collection="taskTypeCodes" open="(" separator="," close=")">  
					 '${item}'
				</foreach> 
		</if>
		<if test="excludeTaskTypeCodes != null">
		         AND task_main.tmb_type_code not in 
		         <foreach item="item" index="index" collection="excludeTaskTypeCodes" open="(" separator="," close=")">  
					 '${item}'
				</foreach> 
		</if>
		<if test="taskStatusArray != null">
		         AND i1.task_status in 
		         <foreach item="item" index="index" collection="taskStatusArray" open="(" separator="," close=")">
                   '${item}'
				</foreach> 
		</if>
   </select>
   
   <select id="selTasksByMachinery" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.TaskParams">
SELECT task_main.*,
       t_user.ch_name create_user_name,
       t1.data_name tmb_type_name,
       i1.task_id subtask_id,
       i1.org_id machinery_id,
       case when i1.task_status=4 then i1.xg_sj else null end complete_time,
       (select a.project_name from wx_t_construction_machinery a where a.id=i1.org_id) project_name,
       (case when task_main.tmb_type_code='TT_2_XD' then (select tic2.remark from wx_task_instance_check tic2 where tic2.sub_task_id=i1.task_id and tic2.check_id=23051840) else '-' end) xd_problem_feedback
FROM wx_task_main task_main
JOIN wx_task_sub i1 on i1.task_main_id=task_main.task_main_id
LEFT JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id
LEFT JOIN wx_t_data t1 on task_main.tmb_type_code=t1.data_code
WHERE i1.org_id=#{machineryId,jdbcType=BIGINT}
	<if test="loginUserType != 1 and loginOrgType != 1">
		and (task_main.excute_user_id is null or exists (select 1 from wx_t_user i1 where i1.user_id=task_main.excute_user_id and i1.org_id=#{loginOrg}))
	</if>
	<if test="taskTypeCodes != null">
	         AND task_main.tmb_type_code in 
	         <foreach item="item" index="index" collection="taskTypeCodes" open="(" separator="," close=")">
               '${item}'
			</foreach> 
	</if>
	<if test="excludeTaskTypeCodes != null">
	         AND task_main.tmb_type_code not in 
	         <foreach item="item" index="index" collection="excludeTaskTypeCodes" open="(" separator="," close=")">  
				 '${item}'
			</foreach> 
	</if>
	<if test="taskStatusArray != null">
	         AND i1.task_status in 
	         <foreach item="item" index="index" collection="taskStatusArray" open="(" separator="," close=")">
               '${item}'
			</foreach> 
	</if>
   </select>
   
   
  <resultMap id="ReportView" type="com.chevron.task.model.TaskReportView">
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
   	<result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
    <result column="record_count" jdbcType="INTEGER" property="count" />
    <result column="report_time" jdbcType="INTEGER" property="time" />
   	<result column="tmb_type_code" jdbcType="VARCHAR" property="taskType"/>
  </resultMap>
  <select id="queryIndexReport" parameterType="map" resultMap="ReportView">
  select a.partner_id, a.report_time, a.tmb_type_code, count(1) record_count from (select * from (
 select isnull((select i1.org_id from wx_t_user i1 where i1.user_id=m.excute_user_id), (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=s.org_id)) partner_id, 
 0 report_time, m.tmb_type_code, s.org_id workshop_id
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
 where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt;= #{dateTo, jdbcType=TIMESTAMP}
  and s.task_status = 4
 union all 
 select (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=wss1.workshop_id), 
 0 report_time, 'TT_2_LD' tmb_type_code, wss1.workshop_id
 from wx_t_workshop_status wss1
 where wss1.workshop_with_status='3' and not exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status != '3')
 and wss1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
 and wss1.create_time &lt;= #{dateTo, jdbcType=TIMESTAMP}
  group by wss1.workshop_id
 union all
  select isnull((select i1.org_id from wx_t_user i1 where i1.user_id=m.excute_user_id), (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=s.org_id)) partner_id, 
  1 report_time, m.tmb_type_code, s.org_id
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
 where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom1, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt;= #{dateTo1, jdbcType=TIMESTAMP}
  and s.task_status = 4
 union all 
 select (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=wss1.workshop_id), 
 1 report_time, 'TT_2_LD' tmb_type_code, wss1.workshop_id
 from wx_t_workshop_status wss1
 where wss1.workshop_with_status='3' and not exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status != '3')
 and wss1.create_time &gt;= #{dateFrom1, jdbcType=TIMESTAMP}
 and wss1.create_time &lt;= #{dateTo1, jdbcType=TIMESTAMP}
  group by wss1.workshop_id
 union all
  select isnull((select i1.org_id from wx_t_user i1 where i1.user_id=m.excute_user_id), (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=s.org_id)) partner_id, 
  2 report_time, m.tmb_type_code, s.org_id
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
 where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom2, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt;= #{dateTo2, jdbcType=TIMESTAMP}
  and s.task_status = 4
 union all 
 select (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=wss1.workshop_id), 
 2 report_time, 'TT_2_LD' tmb_type_code, wss1.workshop_id
 from wx_t_workshop_status wss1
 where wss1.workshop_with_status='3' and not exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status != '3')
 and wss1.create_time &gt;= #{dateFrom2, jdbcType=TIMESTAMP}
 and wss1.create_time &lt;= #{dateTo2, jdbcType=TIMESTAMP}
  group by wss1.workshop_id ) p0 where 1=1 $Permission_Clause$
  	) a
  	<if test="partnerId != null">
  	where a.partner_id=#{partnerId,jdbcType=BIGINT}
  	</if>
 group by a.report_time, a.tmb_type_code, a.partner_id
 </select> 
  <select id="queryTaskSpKpiReportBySp" parameterType="map" resultMap="ReportView">
select max(organization_name) partner_name, partner_id, tmb_type_code, count(1) record_count from (
select t_p.organization_name, t_p.id partner_id, m.tmb_type_code, s.org_id workshop_id 
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
 left join wx_t_user i1 on i1.user_id=m.excute_user_id
 left join wx_t_organization t_p on t_p.id=i1.org_id
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt; #{dateTo, jdbcType=TIMESTAMP}
  and s.task_status = 4
  <choose>
  	<when test="executeUserType == 0">
  	and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  	<when test="executeUserType == 1">
  	and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  </choose>
 union all 
 select max(t_p1.organization_name), max(t_p1.id) partner_id, 'TT_2_LD' tmb_type_code, wss1.workshop_id
 from wx_t_workshop_status wss1
 join wx_task_sub s1 on s1.task_id=wss1.subtask_id
 join wx_task_main m1 on m1.task_main_id = s1.task_main_id and m1.tmb_type_code = 'TT_2_LD'
 left join wx_t_user u1 on u1.user_id=m1.excute_user_id
 left join wx_t_organization t_p1 on t_p1.id=u1.org_id
 where wss1.workshop_with_status='3' and not exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status != '3') 
	<if test="executeUserType == 1"> and 1!=1</if>
 and wss1.subtask_id is not null
 and wss1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
 and wss1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
  group by wss1.workshop_id
 union all 
 select t_p2.organization_name, t_p2.id partner_id, 'TT_2_LD' tmb_type_code, wss2.workshop_id
 from wx_t_workshop_status wss2
 join wx_t_workshop_partner wp2 on wp2.workshop_id=wss2.workshop_id
 join wx_t_organization t_p2 on t_p2.id=wp2.partner_id
 where wss2.workshop_with_status='3' 
	<if test="executeUserType == 1"> and 1!=1</if>
 and wss2.subtask_id is null
 and wss2.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
 and wss2.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
  ) a where 1=1 $Permission_Clause$
 group by partner_id, tmb_type_code
 </select> 
  <select id="queryTaskSpKpiReportByYear" parameterType="map" resultMap="ReportView">
  select a.report_time, a.tmb_type_code, count(1) record_count from (select * from (
select  isnull((select i1.org_id from wx_t_user i1 where i1.user_id=m.excute_user_id), (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=s.org_id)) partner_id,
MONTH(s.xg_sj) report_time, m.tmb_type_code, s.org_id workshop_id
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt; #{dateTo, jdbcType=TIMESTAMP}
  and s.task_status = 4
  <choose>
  	<when test="executeUserType == 0">
  	and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  	<when test="executeUserType == 1">
  	and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  </choose>
union all 
select (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=wss1.workshop_id), max(MONTH(wss1.create_time)) report_time, 'TT_2_LD' tmb_type_code, wss1.workshop_id
from wx_t_workshop_status wss1
where wss1.workshop_with_status='3' and exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status = '3')
and wss1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
and wss1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	<if test="executeUserType == 1"> and 1!=1</if>
 group by wss1.workshop_id ) p0 where 1=1 $Permission_Clause$
  	) a
  	<if test="newSp != null">
	left join wx_t_partner_o2o_enterprise en on a.partner_id=en.partner_id
	where en.new_sp_flag=#{newSp,jdbcType=INTEGER}
	</if>
  	<if test="partnerId != null">
  		where a.partner_id= #{partnerId}
  	</if>
 group by a.report_time, a.tmb_type_code
 </select> 
  <select id="queryTaskSpKpiReportByMonth" parameterType="map" resultMap="ReportView">
  select a.report_time, a.tmb_type_code, count(1) record_count from (select * from (
select isnull((select i1.org_id from wx_t_user i1 where i1.user_id=m.excute_user_id), (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=s.org_id)) partner_id, 
DAY(s.xg_sj) report_time, m.tmb_type_code, s.org_id workshop_id
 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id
where m.tmb_type_code in ('TT_2_SD', 'TT_2_XD')
  and s.xg_sj &gt;= #{dateFrom, jdbcType=TIMESTAMP} 
  and s.xg_sj &lt; #{dateTo, jdbcType=TIMESTAMP}
  and s.task_status = 4
  <choose>
  	<when test="executeUserType == 0">
  	and not exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  	<when test="executeUserType == 1">
  	and exists (select 1 from wx_t_userrole ur1 left join wx_t_role r1 on r1.role_id=ur1.role_id where ur1.user_id=s.exec_user and r1.ch_role_name='TMM')
  	</when>
  </choose>
union all 
select (select top 1 i1.partner_id from wx_t_workshop_partner i1 where i1.workshop_id=wss1.workshop_id),
 max(DAY(wss1.create_time)) report_time, 'TT_2_LD' tmb_type_code, wss1.workshop_id
from wx_t_workshop_status wss1
where wss1.workshop_with_status='3' and not exists(select 1 from wx_t_work_shop i1 where wss1.workshop_id=i1.id and i1.status != '3') 
and wss1.create_time &gt;= #{dateFrom, jdbcType=TIMESTAMP}
and wss1.create_time &lt; #{dateTo, jdbcType=TIMESTAMP}
	<if test="executeUserType == 1"> and 1!=1</if>
 group by wss1.workshop_id ) p0 where 1=1 $Permission_Clause$
  	) a
  	<if test="partnerId != null">
 		where a.partner_id=#{partnerId}
  	</if>
 group by a.report_time, a.tmb_type_code
 </select> 
 
 
 
 
 <select id="countMainTaskByTaskTypeOrParams" parameterType="map" resultType="int">
 select
 	count(1)
 from
 (
 select 
 	tt2.*
 from
 	(
   SELECT 
	max(t.IS_TO_LD) AS IS_TO_LD ,max(t.IS_TO_SD) AS IS_TO_SD,
	t.task_main_id ,   t.tmb_type_code,t.task_name,t.task_mb_id,
	t.tmb_classify,t.exec_user_type,t.inner_org_type,
	t.inner_org_id,t.task_description,t.task_start_time,
	t.task_finish_time,t.task_create_user,t.check_status,
	t.check_item_status,t.task_status,t.task_priority,t.room_id,
	t.room_name,t.publish_status,t.frequency_type,t.create_source,
	t.source_task_pid,t.finish_day,t.create_time,t.create_user,
	t.xg_sj,t.xg_user,t.status,t.tenant_id,
	t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name ,
	t.exec_user_name,t.handle_task_priority
    FROM(
   	 SELECT DISTINCT(task_main.task_main_id), 
		task_main.tmb_type_code,task_main.task_name,task_main.task_mb_id,
		task_main.tmb_classify,task_main.exec_user_type,task_main.inner_org_type,
		task_main.inner_org_id,task_main.task_description,task_main.task_start_time,
		task_main.task_finish_time,task_main.task_create_user,task_main.check_status,
		task_main.check_item_status,task_main.task_status,task_main.task_priority,task_main.room_id,
		task_main.room_name,task_main.publish_status,task_main.frequency_type,task_main.create_source,
		task_main.source_task_pid,task_main.finish_day,task_main.create_time,task_main.create_user,
		task_main.xg_sj,task_main.xg_user,task_main.status,task_main.tenant_id,
		task_main.excute_user_id,task_main.workshop_ids,task_main.task_steps,
		t_user.ch_name create_user_name,
		(SELECT tt_user.ch_name  FROM wx_t_user  tt_user WHERE tt_user.user_id = task_main.excute_user_id ) AS exec_user_name,
		<if test="taskTypeCodeModel==1">
			t_workshop.work_shop_name workshop_name,t_workshop.id workshop_id,t_workshop.status workshop_status,
		</if>
		(CASE task_main.task_priority 
		WHEN '1' THEN '低'
		WHEN '2' THEN '中'
		WHEN '3' THEN '高'
		WHEN '4' THEN '紧急'
		ELSE '其他' END) handle_task_priority,
		(CASE WHEN t_sub.task_status='4' AND t_workshop.status='0'  THEN '1' ELSE '0' end) AS IS_TO_LD,
        (CASE WHEN t_sub.task_status='4' AND (t_workshop.excute_user_id=NULL OR t_workshop.excute_user_id=-1)  THEN '1' ELSE '0' end) AS IS_TO_SD
	FROM
		wx_task_main task_main 
	left JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id  
	left JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id 
  	left JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
  	
	where
	<if test="tenantId!=1">
		(task_main.create_user = #{currentId,jdbcType=BIGINT} or task_main.excute_user_id = #{currentId,jdbcType=BIGINT}  
		 OR task_main.tenant_id=#{orgId,jdbcType=BIGINT}  
		)
		and
	</if>
	
   	<if test="taskTypeCode!=null">
   		task_main.tmb_type_code = #{taskTypeCode,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="taskName!=null">
   		task_main.task_name like '%' + #{taskName} + '%'
   		and
   	</if>
   	<if test="taskMainStauts!=null">
   		task_main.task_status = #{taskMainStauts,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isCreateUser==true">
   		task_main.create_user = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isWatchUser==true">
   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="isExcuteUser==true">
   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
   		and
   	</if>
   	<if test="taskExcuteUID!=null">
   		task_main.excute_user_id = #{taskExcuteUID,jdbcType=BIGINT}
   		and
   	</if>
   	
   	<if test="workshopName!=null">
   		t_workshop.work_shop_name like '%' + #{workshopName} + '%'
   		and
   	</if>
   	<if test="workshopScale!=null">
   		t_workshop.ext_property15 like '%' + #{workshopScale} + '%'
   		and
   	</if>
   	<if test="taskStartTime!=null and taskStartTime!=''">
   		 <![CDATA[  CONVERT(varchar(100), task_main.task_start_time, 23) >=  CONVERT(varchar(100), #{taskStartTime}, 23)  and ]]>
    </if>
    <if test="taskEndTime!=null and taskEndTime!=''">
    	<![CDATA[   CONVERT(varchar(100), task_main.task_finish_time, 23) <= CONVERT(varchar(100), #{taskEndTime}, 23)    and ]]>
    </if>
   <if test="queryField!=null">
   		(t_workshop.work_shop_name like '%' + #{queryField} + '%'
   	or
   		t_workshop.ext_property15 like '%' + #{queryField} + '%'
   	or	task_main.task_name  LIKE '%' + #{queryField} + '%' 
	OR  t_user.ch_name LIKE '%' + #{queryField} + '%'
	OR  (SELECT tt_user.ch_name FROM wx_t_user tt_user WHERE tt_user.user_id = task_main.excute_user_id ) LIKE '%' + #{queryField} + '%'
	OR  (CASE task_main.task_priority 
		WHEN '1' THEN '低'
		WHEN '2' THEN '中'
		WHEN '3' THEN '高'
		WHEN '4' THEN '紧急'
		ELSE '其他' END) LIKE '%' + #{queryField} + '%'
	or  left(dbo.f_GetPyToAboutHanyu(task_main.task_name),500) LIKE #{firtPyForQueryField} + '%'
		)
   	and
   </if>
    <if test="tenantId==1 and partnerId!=null and partnerId!=''">
 	    t_user.org_id = #{partnerId}  AND 
   </if>
    	t_user.ch_name IS  NOT NULL
    and 
   	1 = 1
   	)AS t  
	GROUP BY 
	t.task_main_id ,t.tmb_type_code,t.task_name,t.task_mb_id,
	t.tmb_classify,t.exec_user_type,t.inner_org_type,
	t.inner_org_id,t.task_description,t.task_start_time,
	t.task_finish_time,t.task_create_user,t.check_status,
	t.check_item_status,t.task_status,t.task_priority,t.room_id,
	t.room_name,t.publish_status,t.frequency_type,t.create_source,
	t.source_task_pid,t.finish_day,t.create_time,t.create_user,
	t.xg_sj,t.xg_user,t.status,t.tenant_id,
	t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name,
	t.exec_user_name,t.handle_task_priority
	) as tt2 where 1 =1 
	)as tt3
   </select>  
   
   
   <!-- 含有数据权限控制 -->
   <select id="getMainTaskByTaskTypeOrParamsNew" parameterType="com.chevron.task.model.MainTaskQueryConditions" resultMap="BaseResultMap">
    <!-- 数据权限开启的情况 -->
    <if test="isOpernCustomMybatisInterceptor==1">
    select * from(
       SELECT *,(CASE 
          	WHEN IS_TO_LD='0' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_LD'  THEN '11'
          	WHEN IS_TO_LD='1' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_LD' THEN '10'
          	
          	WHEN IS_TO_GD='0' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_SD' THEN '9'
          	WHEN IS_TO_GD='1' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_SD' THEN '8'
          	ELSE nest_table1.task_status end
          )AS new_task_status from
       ( 
	    select
			max(t.IS_TO_LD) AS IS_TO_LD ,max(t.IS_TO_GD) AS IS_TO_GD,
			t.task_main_id ,   t.tmb_type_code,t.task_name,t.task_mb_id,
			t.tmb_classify,t.exec_user_type,t.inner_org_type,
			t.inner_org_id,t.task_description,t.task_start_time,
			t.task_finish_time,t.task_create_user,t.check_status,
			t.check_item_status,t.task_status,t.task_priority,t.room_id,
			t.room_name,t.publish_status,t.frequency_type,t.create_source,
			t.source_task_pid,t.finish_day,t.create_time,t.create_user,
			t.xg_sj,t.xg_user,t.status,t.tenant_id,
			t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name ,
			t.exec_user_name,t.handle_task_priority, t.excute_tenant_id,t.partner_id,
			t.organizationname   
	    FROM(
	   	select task_main.* ,task_main.excute_tenant_id partner_id, tt_org.organization_name organizationname 
	   	from (SELECT tt_main1.*, (CASE  WHEN t_gd.isgd_mainid IS NULL THEN '0' ELSE '1' END) AS IS_TO_GD  FROM 
				(SELECT DISTINCT(task_main.task_main_id), 
						task_main.tmb_type_code,task_main.task_name,task_main.task_mb_id,
						task_main.tmb_classify,task_main.exec_user_type,task_main.inner_org_type,
						task_main.inner_org_id,task_main.task_description,task_main.task_start_time,
						task_main.task_finish_time,task_main.task_create_user,task_main.check_status,
						task_main.check_item_status,task_main.task_status,task_main.task_priority,task_main.room_id,
						task_main.room_name,task_main.publish_status,task_main.frequency_type,task_main.create_source,
						task_main.source_task_pid,task_main.finish_day,task_main.create_time,task_main.create_user,
						task_main.xg_sj,task_main.xg_user,task_main.status,task_main.tenant_id,
						task_main.excute_user_id,task_main.workshop_ids,task_main.task_steps,
						t_user.ch_name create_user_name,
						(SELECT tt_user.ch_name  FROM wx_t_user  tt_user WHERE tt_user.user_id = task_main.excute_user_id ) AS exec_user_name,
						(CASE task_main.task_priority 
					    WHEN '1' THEN '低'
						WHEN '2' THEN '中'
						WHEN '3' THEN '高'
						WHEN '4' THEN '紧急'
						ELSE '其他' END) handle_task_priority,
					        (CASE WHEN t_sub.task_status='4' AND t_workshop.status='1'   THEN '1' ELSE '0' end) AS IS_TO_LD,
						(SELECT tt_user1.org_id FROM wx_t_user tt_user1 WHERE tt_user1.user_id = task_main.excute_user_id ) AS excute_tenant_id,
						task_main.in_stock_no,
						task_main.out_stock_no,
						task_main.order_no
					FROM
						wx_task_main task_main 
					left JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id  
					left JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id 
				  	left JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
				  	where 1=1
			  	)as  tt_main1
				LEFT JOIN (SELECT task_main.task_main_id AS isgd_mainid
					FROM wx_task_main task_main
						LEFT JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id
						LEFT JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
					WHERE t_sub.task_status = '4'
						AND t_workshop.status = '0'
						AND t_sub.org_id NOT IN (SELECT task_sub2.org_id
							FROM wx_task_sub task_sub2
							WHERE EXISTS (
								SELECT 1
								FROM wx_task_main tt_main1
								WHERE tt_main1.tmb_type_code = 'TT_2_LD'
									AND task_sub2.task_main_id = tt_main1.task_main_id
								))
					) t_gd ON tt_main1.task_main_id = t_gd.isgd_mainid
			) AS task_main  
	   	left JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id  
		left JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id 
	  	left JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
	  	LEFT JOIN  wx_t_organization tt_org ON task_main.excute_tenant_id= tt_org.id
	  	
		where
		   	<if test="taskTypeCode!=null">
		   		task_main.tmb_type_code = #{taskTypeCode,jdbcType=VARCHAR}
		   		and
		   	</if>
		 <!-- 高级搜索 -->
		 <if test="queryType==1"> 
		   	<if test="taskName!=null">
		   		task_main.task_name like '%'+#{taskName}+'%'
		   		and
		   	</if>
		   	<if test="createUser==true">
		   		task_main.create_user = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="watchUser==true">
		   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="execUser==true">
		   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="taskExcuteUserName!=null">
		   		task_main.excute_user_id = #{taskExcuteUserName,jdbcType=BIGINT}
		   		and
		   	</if>
		   	
		   	<if test="taskWorkshopName!=null">
		   		t_workshop.work_shop_name like '%'+#{taskWorkshopName}+'%'
		   		and
		   	</if>
		   	<if test="taskWorkshopScale!=null">
		   		t_workshop.ext_property15 like '%'+#{taskWorkshopScale}+'%'
		   		and
		   	</if>
		   	<if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
		   		 <![CDATA[  CONVERT(varchar(100), task_main.create_time, 23) >=  CONVERT(varchar(100), #{taskFinishTimeS}, 23)  and ]]>
		    </if>
		    <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
		    	<![CDATA[   CONVERT(varchar(100), task_main.create_time, 23) <= CONVERT(varchar(100), #{taskFinishTimeE}, 23)    and ]]>
		    </if>
		   <if test="currentUserType==1 and partnerId!=null and partnerId!=''">
		 	    task_main.excute_tenant_id = #{partnerId}  AND 
		   </if> 
	    </if> 
	    
	    <!-- 关键字搜索 -->
		  <if test="queryType==2"> 
		    <if test="queryField!=null and queryField!=''">
		   		(t_workshop.work_shop_name like '%'+#{queryField}+'%'  
		   	or
		   		t_workshop.ext_property15 like '%'+#{queryField}+'%'  
		   	or	task_main.task_name  LIKE '%'+#{queryField}+'%'  
			OR  t_user.ch_name LIKE '%'+#{queryField}+'%'  
			OR  (SELECT tt_user.ch_name FROM wx_t_user tt_user WHERE tt_user.user_id = task_main.excute_user_id ) LIKE '%'+#{queryField}+'%'   
			OR  (CASE task_main.task_priority 
				WHEN '1' THEN '低'
				WHEN '2' THEN '中'
				WHEN '3' THEN '高'
				WHEN '4' THEN '紧急'
				ELSE '其他' END) LIKE '%'+#{queryField}+'%'   
			or  left(dbo.f_GetPyToAboutHanyu(task_main.task_name),500) LIKE #{firtPyForQueryField}+'%'  
			OR tt_org.organization_name LIKE '%'+#{queryField}+'%'  
				)
		   	and
		   	</if>
	    </if> 
	   
	    	t_user.ch_name IS  NOT NULL
	    and 
	   	1 = 1 $Permission_Clause$
	   	)AS t  
		GROUP BY 
		t.task_main_id ,t.tmb_type_code,t.task_name,t.task_mb_id,
		t.tmb_classify,t.exec_user_type,t.inner_org_type,
		t.inner_org_id,t.task_description,t.task_start_time,
		t.task_finish_time,t.task_create_user,t.check_status,
		t.check_item_status,t.task_status,t.task_priority,t.room_id,
		t.room_name,t.publish_status,t.frequency_type,t.create_source,
		t.source_task_pid,t.finish_day,t.create_time,t.create_user,
		t.xg_sj,t.xg_user,t.status,t.tenant_id,
		t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name,
		t.exec_user_name,t.handle_task_priority,t.excute_tenant_id, t.partner_id,
		t.organizationname 
		)nest_table1) as nest_table1_temp where 
		  <if test="queryType==1">
		<if test="taskMainStatus!=null">
		new_task_status = #{taskMainStatus,jdbcType=VARCHAR} and
		</if> 
		</if>
		1=1
	</if>
   </select>  
   
   <select id="countMainTaskByTaskTypeOrParamsNew" parameterType="com.chevron.task.model.MainTaskQueryConditions" resultType="Long">
   	SELECT  count(1)   
	from
	(
		SELECT *,(CASE 
          	WHEN IS_TO_LD='0' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_LD'  THEN '11'
          	WHEN IS_TO_LD='1' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_LD' THEN '10'
          	WHEN IS_TO_GD='0' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_SD' THEN '9'
          	WHEN IS_TO_GD='1' AND nest_table1.task_status='4' AND nest_table1.tmb_type_code='TT_2_SD' THEN '8'
          	ELSE nest_table1.task_status end
          )AS new_task_status from
       ( 
		 select
			max(t.IS_TO_LD) AS IS_TO_LD ,max(t.IS_TO_GD) AS IS_TO_GD,
			t.task_main_id ,   t.tmb_type_code,t.task_name,t.task_mb_id,
			t.tmb_classify,t.exec_user_type,t.inner_org_type,
			t.inner_org_id,t.task_description,t.task_start_time,
			t.task_finish_time,t.task_create_user,t.check_status,
			t.check_item_status,t.task_status,t.task_priority,t.room_id,
			t.room_name,t.publish_status,t.frequency_type,t.create_source,
			t.source_task_pid,t.finish_day,t.create_time,t.create_user,
			t.xg_sj,t.xg_user,t.status,t.tenant_id,
			t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name ,
			t.exec_user_name,t.handle_task_priority, t.excute_tenant_id,
		    t.organizationname 
	    FROM(
	   	select task_main.*,tt_org.organization_name organizationname  from wx_v_query_maintask_list_new task_main
	   	left JOIN wx_t_user t_user ON task_main.create_user = t_user.user_id  
		left JOIN wx_task_sub t_sub ON t_sub.task_main_id = task_main.task_main_id 
	  	left JOIN wx_t_work_shop t_workshop ON t_sub.org_id = t_workshop.id
	  	LEFT JOIN  wx_t_organization tt_org ON task_main.excute_tenant_id= tt_org.id
		where
			 <if test="currentUserType!=1">
					(task_main.create_user = #{currentUserId,jdbcType=BIGINT} or task_main.excute_user_id = #{currentUserId,jdbcType=BIGINT}  
					 OR task_main.tenant_id=#{orgId,jdbcType=BIGINT}  
					)
					and
			 </if>
			 <if test="taskTypeCode!=null">
			   		task_main.tmb_type_code = #{taskTypeCode,jdbcType=VARCHAR}
			   		and
			  </if>
		 <!-- 高级搜索 -->
		 <if test="queryType==1"> 
		   	<if test="taskName!=null">
		   		task_main.task_name like '%' + #{taskName} +'%'
		   		and
		   	</if>
		   <!-- 	<if test="taskMainStatus!=null">
		   		task_main.task_status = #{taskMainStatus,jdbcType=VARCHAR}
		   		and
		   	</if> -->
		   	<if test="createUser==true">
		   		task_main.create_user = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="watchUser==true">
		   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="execUser==true">
		   		task_main.excute_user_id = #{currentUserId,jdbcType=VARCHAR}
		   		and
		   	</if>
		   	<if test="taskExcuteUserName!=null">
		   		task_main.excute_user_id = #{taskExcuteUserName,jdbcType=BIGINT}
		   		and
		   	</if>
		   	
		   	<if test="taskWorkshopName!=null">
		   		t_workshop.work_shop_name like '%'+ #{taskWorkshopName} + '%'
		   		and
		   	</if>
		   	<if test="taskWorkshopScale!=null">
		   		t_workshop.ext_property15 like '%'+ #{taskWorkshopScale} + '%'
		   		and
		   	</if>
		   	<if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
		   		 <![CDATA[  CONVERT(varchar(100), task_main.task_start_time, 23) >=  CONVERT(varchar(100), #{taskFinishTimeS}, 23)  and ]]>
		    </if>
		    <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
		    	<![CDATA[   CONVERT(varchar(100), task_main.task_finish_time, 23) <= CONVERT(varchar(100), #{taskFinishTimeE}, 23)    and ]]>
		    </if>
		     <if test="currentUserType==1 and partnerId!=null and partnerId!=''">
	 	         t_user.org_id = #{partnerId}  AND 
	          </if>
	     </if>  
	    
	    <!-- 关键字搜索 -->
	   	<if test="queryType==2"> 
		    <if test="queryField!=null and queryField!=''">
		   		(t_workshop.work_shop_name like  '%'+ #{queryField} + '%'
		   	or
		   		t_workshop.ext_property15 like '%'+ #{queryField} + '%'
		   	or	task_main.task_name  LIKE '%'+ #{queryField} + '%'
			OR  t_user.ch_name LIKE '%'+ #{queryField} + '%'
			OR  (SELECT tt_user.ch_name FROM wx_t_user tt_user WHERE tt_user.user_id = task_main.excute_user_id ) LIKE '%'+ #{queryField} + '%'
			OR  (CASE task_main.task_priority 
				WHEN '1' THEN '低'
				WHEN '2' THEN '中'
				WHEN '3' THEN '高'
				WHEN '4' THEN '紧急'
				ELSE '其他' END) LIKE '%'+ #{queryField} + '%'
			or  left(dbo.f_GetPyToAboutHanyu(task_main.task_name),500) LIKE '#{firtPyForQueryField}%'
			OR  tt_org.organization_name LIKE '%'+ #{queryField} + '%'
				)
		   	and
		   </if>
	   </if> 
	  
	    	t_user.ch_name IS  NOT NULL
	    and 
	   	1 = 1
	   	)AS t  
		GROUP BY 
		t.task_main_id ,t.tmb_type_code,t.task_name,t.task_mb_id,
		t.tmb_classify,t.exec_user_type,t.inner_org_type,
		t.inner_org_id,t.task_description,t.task_start_time,
		t.task_finish_time,t.task_create_user,t.check_status,
		t.check_item_status,t.task_status,t.task_priority,t.room_id,
		t.room_name,t.publish_status,t.frequency_type,t.create_source,
		t.source_task_pid,t.finish_day,t.create_time,t.create_user,
		t.xg_sj,t.xg_user,t.status,t.tenant_id,
		t.excute_user_id,t.workshop_ids,t.task_steps,t.create_user_name,
		t.exec_user_name,t.handle_task_priority,t.excute_tenant_id,
		t.organizationname 
		)as nest_table1) as tt2 where 
		  <if test="queryType==1">
		<if test="taskMainStatus!=null">
		new_task_status = #{taskMainStatus,jdbcType=VARCHAR} and
		</if></if> 1=1
   </select>
   
   
   <resultMap id="BaseResultMapNew" type="com.chevron.task.model.WxTaskMainNew">
    <result column="task_main_id" jdbcType="BIGINT" property="taskMainId" />
    <result column="tmb_type_code" jdbcType="VARCHAR" property="tmbTypeCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" /> 
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="task_priority" jdbcType="VARCHAR" property="taskPriority" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="in_stock_no" jdbcType="VARCHAR" property="inStockNo" />
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="excute_user_id" property="excuteUserId" jdbcType="BIGINT" />
    <result column="exec_user_name" jdbcType="VARCHAR" property="executeUserName" />
    <result column="is_pass" jdbcType="INTEGER" property="isPass" /> 
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" /> 
    <result column="task_id" jdbcType="BIGINT" property="taskId" /> 
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="organizationname" jdbcType="NVARCHAR" property="organizationName" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workShopName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workShopId" />
    <result column="workshop_addr" jdbcType="VARCHAR" property="workshopAddress" /> 
    <result column="scale" jdbcType="VARCHAR" property="workshopScale" /> 
    <result column="f_workshopstatus" jdbcType="VARCHAR" property="workshopStatus" />
    <result column="access_count" jdbcType="INTEGER" property="accessCount" /> 
    <result column="taskstatus" jdbcType="VARCHAR" property="taskStatusChn" />  
    <result column="IS_TO_LD" jdbcType="NVARCHAR" property="isToLD" />
    <result column="IS_TO_GD" jdbcType="NVARCHAR" property="isToGD" />
    <result column="new_task_status" jdbcType="NVARCHAR" property="taskNewStatus" />
    <result column="task_remark" jdbcType="NVARCHAR" property="taskRemark" />
    <result column="check_count" jdbcType="INTEGER" property="checkCount" />
    <result column="fleet_id" jdbcType="BIGINT" property="fleetId" />
    <result column="fleet_name" jdbcType="NVARCHAR" property="fleetName" />
     <result column="machinery_id" jdbcType="BIGINT" property="machineryId" />
    <result column="machinery_name" jdbcType="NVARCHAR" property="machineryName" />
    <result column="workshop_type" jdbcType="NVARCHAR" property="workshopType" />
    <result column="monthly_oil_sales_volume" jdbcType="BIGINT" property="monthlyOilSalesVolume" />
    <result column="monthly_chevron_oil_sales_volume" jdbcType="BIGINT" property="monthlyChevronOilSalesVolume" />
	<result column="check_evaluation" property="checkEvaluation" jdbcType="INTEGER"/>
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="attribute1" jdbcType="NVARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="NVARCHAR" property="attribute3" />
    <result column="email" jdbcType="NVARCHAR" property="createUserEmail" />
    <result column="mobile_tel" jdbcType="NVARCHAR" property="createUserMobile" />
     <result column="brand" jdbcType="INTEGER" property="brand" />
    </resultMap>
    <!-- 含有数据权限控制 -->
   <select id="getTaskByTypeOrParamsNew" parameterType="com.chevron.task.model.MainTaskQueryConditions" resultMap="BaseResultMapNew">
    SELECT 
	 *
	FROM
	(
	SELECT 
	*,
	(CASE 
	 WHEN IS_TO_LD='0' AND t.task_status='4' AND t.tmb_type_code='TT_2_LD' THEN '11'
	 WHEN IS_TO_LD='1' AND t.task_status='4' AND t.tmb_type_code='TT_2_LD' THEN '10' 
	 WHEN IS_TO_GD='0' AND t.task_status='4' AND (CONVERT(INT, t.ext_property16) &amp; 1) = 1  AND t.tmb_type_code='TT_2_SD' THEN '8' 
	 WHEN IS_TO_GD='1' AND t.task_status='4' AND (CONVERT(INT, t.ext_property16) &amp; 1) = 1  AND t.f_workshopstatus='0' AND t.tmb_type_code='TT_2_SD' THEN '5' 
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_CLZX' and t.is_pass=1 THEN '8' 
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_CLZX' and t.is_pass=-1 AND (CONVERT(INT, t.ext_property16) &amp; 1) = 1 THEN '5' 
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_CDMCLZX' and t.is_pass=1 THEN '8' 
	 <if test="taskTypeCode == 'TT_2_CDYJZM' or taskTypeCode=='TT_2_FLEET'">
     WHEN t.task_status='4' AND t.tmb_type_code='TT_2_CDYJZM' and t.is_pass=1 THEN '8'
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_FLEET' and t.is_pass=1 THEN '8' 
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_FLEET' and t.is_pass= -1 AND (CONVERT(INT, t.ext_property16) &amp; 1) = 1 THEN '5' 
	 </if>
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_PROJECT_FLEET' and t.is_pass=1 THEN '8' 
	 WHEN t.task_status='4' AND t.tmb_type_code='TT_2_PROJECT_CLIENT' and t.is_pass=1 THEN '8'
     WHEN t.task_status='4' AND t.tmb_type_code='TT_2_PROJECT_CLIENT_YJZM' and t.is_pass=1 THEN '8'
     WHEN t.task_status='4' AND t.tmb_type_code='TT_2_7nk/' and t.is_pass=1 THEN '8'
	 ELSE t.task_status end )AS new_task_status
	 FROM (
	SELECT ppcos.region as region_name ,
	tt_main.task_name as task_name,tt_main.task_start_time,t_sub.xg_sj task_finish_time,tt_main.tmb_type_code,
	tt_main.create_user,tt_main_createu.ch_name create_user_name,tt_main.create_time,tt_main.in_stock_no,
	t_sub.task_status,t_sub.exec_user excute_user_id,tt_user.ch_name exec_user_name ,
	t_sub.check_result,t_sub.is_pass,t_sub.task_remark ,t_sub.check_count ,t_sub.process_status,t_sub.attribute1,t_sub.attribute2,t_sub.attribute3,tt_main_createu.mobile_tel,tt_main_createu.email,
	t_sub.task_id,t_sub.task_main_id,t_sub.tenant_id partner_id,tt_org.organization_name organizationname,
	tt_workshop.work_shop_name workshop_name,tt_workshop.work_shop_address workshop_addr,
	tt_workshop.id workshop_id,tt_workshop.type workshop_type, 
	tt_workshop.ext_property8 monthly_oil_sales_volume, tt_workshop.ext_property9 monthly_chevron_oil_sales_volume,
	tt_main.task_priority,tt_workshop.ext_property16,
	<if test="taskTypeCode == 'TT_2_CDYJZM' or taskTypeCode=='TT_2_FLEET'">
	 tt_workshop.work_shop_name fleet_name ,
	 tt_workshop.id fleet_id ,
	</if>
	<if test="taskTypeCode == 'TT_2_GC_JX'">
		tt_workshop.id machinery_id ,
		tt_workshop.work_shop_name machinery_name,
	</if>
	<if test="taskTypeCode == 'TT_2_PROJECT_FLEET'">
	 	tt_workshop.work_shop_name company_name,
	</if>
	tt_workshop.status f_workshopstatus,
	tt_has_gd_workshopid.workshop_id has_gd_workshopid,
	t_sub.check_evaluation,
	(SELECT
			count(*)
			FROM
			wx_t_task_exec_trace
			WHERE
			work_shop_id = tt_workshop.id
			AND
			sub_task_id = t_sub.task_id) AS access_count,
    (CASE WHEN  tt_has_gd_workshopid.workshop_id IS NOT NULL OR tt_workshop.status='3' 
    			THEN
           		'0'
                ELSE 
                '1'
              END)AS IS_TO_GD,
	(CASE 
			  WHEN t_sub.task_status='4' AND tt_workshop.status='1' AND tt_main.tmb_type_code='TT_2_LD'  
			  THEN '1' 
			  ELSE '0' END)AS IS_TO_LD
	
	FROM wx_task_sub t_sub 
	LEFT JOIN wx_task_main tt_main ON tt_main.task_main_id = t_sub.task_main_id
	LEFT JOIN wx_t_user tt_user ON tt_user.user_id=t_sub.exec_user 
	LEFT JOIN wx_t_user tt_main_createu ON tt_main.create_user = tt_main_createu.user_id
	LEFT JOIN wx_t_organization tt_org ON tt_user.org_id= tt_org.id 
	LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = t_sub.org_id
    LEFT JOIN (select workshop_id from wx_task_has_gd_workshop group by workshop_id) tt_has_gd_workshopid ON tt_has_gd_workshopid.workshop_id = t_sub.org_id
    LEFT JOIN (select partner_id,distributor_id from wx_t_partner_o2o_enterprise group by partner_id,distributor_id) poe ON poe .partner_id = tt_workshop.partner_id
    LEFT JOIN (select distributor_id ,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales group by distributor_id ,region) ppcos ON poe.distributor_id = ppcos.distributor_id
<!-- 	<if test="taskTypeCode == 'TT_2_CDYJZM' or taskTypeCode=='TT_2_FLEET'">
		LEFT JOIN wx_t_fleet_info ft ON t_sub.org_id = ft.id
	</if>
	<if test="taskTypeCode == 'TT_2_GC_JX'">
		LEFT JOIN wx_t_construction_machinery t_gc ON t_sub.org_id = t_gc.id and t_gc.delete_flag = 0
	</if>
	
	 <if test="taskTypeCode == 'TT_2_PROJECT_FLEET'">
		LEFT JOIN wx_t_con_mac_fleet m_f ON t_sub.org_id = m_f.id
	</if> -->
	WHERE 
	<if test="taskTypeCode!=null">
	 tt_main.tmb_type_code= #{taskTypeCode,jdbcType=VARCHAR}
	 and
   	</if>
	
	
	 <!-- 高级搜索 -->
	<if test="queryType==1"> 
		<if test="regionName != null and regionName != ''">
		ppcos.region = #{regionName} and
		</if>
		<if test="taskName!=null">
		tt_main.task_name LIKE  '%'+#{taskName}+'%' 
		and	
		</if>
		<if test="taskExcuteUserName!=null">	   	 
		t_sub.exec_user = #{taskExcuteUserName,jdbcType=BIGINT}
		AND
		</if>
		<if test="taskWorkshopName!=null">
		tt_workshop.work_shop_name like '%'+#{taskWorkshopName}+'%'
		and
		</if>
		<if test="taskWorkshopScale!=null">
		tt_workshop.ext_property15 like '%'+#{taskWorkshopScale}+'%'
		and
		</if>
		<if test="fleetName != null and (taskTypeCode=='TT_2_FLEET' or taskTypeCode == 'TT_2_CDYJZM')">
		tt_workshop.work_shop_name LIKE '%' + #{fleetName} + '%' 
		and
		</if>
		<if test="machineryName != null and (taskTypeCode == 'TT_2_GC_JX' )">
		tt_workshop.work_shop_name like '%'+#{machineryName}+'%'
		and
		</if>
		<if test="companyName != null and (taskTypeCode == 'TT_2_PROJECT_FLEET' )">
		tt_workshop.work_shop_name like '%'+#{companyName}+'%'
		and
		</if>
		<if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
				<![CDATA[ t_sub.xg_sj>= #{taskFinishTimeS} and ]]>
		</if>
	    <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
	    		<![CDATA[ t_sub.xg_sj<= #{taskFinishTimeE} and ]]>
	    </if>
	    <if test="currentUserType==1 and partnerId!=null and partnerId!=''">
		t_sub.tenant_id = #{partnerId}  
		AND 
		</if> 
	</if>
	
	<if test="queryType==2"> 
	    <if test="queryField!=null and queryField!=''">
		(tt_workshop.work_shop_name  like '%'+#{queryField}+'%'  
				   	or
				   		tt_workshop.ext_property15 like '%'+#{queryField}+'%'  
				   	or	tt_main.task_name  LIKE '%'+#{queryField}+'%' 
					OR  tt_user.ch_name LIKE '%'+#{queryField}+'%'
					OR  (SELECT tt_user1.ch_name FROM wx_t_user tt_user1 WHERE tt_user1.user_id = tt_main.excute_user_id ) LIKE '%'+#{queryField}+'%'    
					or  left(dbo.f_GetPyToAboutHanyu(tt_main.task_name),500) LIKE #{firtPyForQueryField}+'%'  
					OR tt_org.organization_name LIKE '%'+#{queryField}+'%'   
					<if test="taskTypeCode == 'TT_2_CDYJZM' or taskTypeCode=='TT_2_FLEET' or taskTypeCode == 'TT_2_GC_JX' or taskTypeCode == 'TT_2_PROJECT_FLEET' ">
					OR tt_workshop.work_shop_name like '%'+#{queryField}+'%'
					</if>
				<!-- 	<if test="">
					OR tt_workshop.work_shop_name like '%'+#{queryField}+'%'
					</if>
					<if test="">
					OR tt_workshop.company_name like '%'+#{queryField}+'%'
					</if> -->
						)
		AND
		</if>
	</if>
	 tt_user.ch_name IS  NOT NULL
	 <if test="taskTypeCode == 'TT_2_XD_CAI'">
	 AND t_sub.task_status != 1
	 </if>
	 and 
	 1 = 1 $Permission_Clause$
	) t
	) tt 
	WHERE 1=1 
	<if test="queryType==1">
		<if test="taskMainStatus!=null">
		AND tt.new_task_status=#{taskMainStatus,jdbcType=VARCHAR}
		</if>
	</if>
	<if test="taskMainStatus==null">
		<choose>
			<when test="taskTypeCode=='TT_2_SD'">
		AND (tt.new_task_status='8' or tt.new_task_status='3' or tt.new_task_status='5')
			</when>
			<when test="taskTypeCode=='TT_2_LD'">
		AND (tt.new_task_status='10' or tt.new_task_status='3' or tt.new_task_status='1')
			</when>
			<when test="taskTypeCode=='TT_2_XD' or taskTypeCode=='TT_2_FLEET_ZF' or taskTypeCode=='TT_2_XD_CAI'">
		AND (tt.new_task_status='4' or tt.new_task_status='3' or tt.new_task_status='12')
			</when> 
			<when test="taskTypeCode=='TT_2_CLZX' or taskTypeCode=='TT_2_FLEET'">
				AND (tt.new_task_status='5' or tt.new_task_status='3' or tt.new_task_status='8' or tt.new_task_status='12')
			</when>
			<when test="taskTypeCode=='TT_2_CDMCLZX' or taskTypeCode=='TT_2_CDYJZM' or taskTypeCode=='TT_2_7nk/' or taskTypeCode=='TT_2_GC_JX' or taskTypeCode=='TT_2_PROJECT_FLEET' or taskTypeCode=='TT_2_PROJECT_CLIENT' or taskTypeCode=='TT_2_PROJECT_CLIENT_VISIT' or taskTypeCode == 'TT_2_PROJECT_CLIENT_YJZM'">
		AND (tt.new_task_status='4' or tt.new_task_status='3' or tt.new_task_status='8' or tt.new_task_status='12')
			</when>
		</choose>
	</if>
   </select>  
   
   
    <resultMap id="BaseResultMapForXDAttFile" type="com.chevron.task.model.WxTaskForXDAttFile">
    <result column="task_main_id" jdbcType="BIGINT" property="taskMainId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="excute_user_id" property="excuteUserId" jdbcType="BIGINT" />
    <result column="exec_user_name" jdbcType="VARCHAR" property="executeUserName" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" /> 
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="organizationname" jdbcType="NVARCHAR" property="organizationName" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workShopName" />
    <result column="workshop_id" jdbcType="BIGINT" property="workShopId" />
    <result column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="storage_name" jdbcType="VARCHAR" property="storageName" />
    <result column="store_path" jdbcType="TIMESTAMP" property="storagePath" />
    
    </resultMap>
   <select id="getTaskForXDAttFileLst" parameterType="com.chevron.task.model.MainTaskForAttFileQueryConditions" resultMap="BaseResultMapForXDAttFile">
	SELECT tt_org.id partner_id,tt_org.organization_name organizationname, 
	tt_main.task_main_id, tt_main.task_name, tt_main.create_time,
	tt_sub.task_id,tt_sub.exec_user excute_user_id,tt_sub.org_id workshop_id,
	tt_workshop.work_shop_name workshop_name,tt_user.ch_name exec_user_name,
	tt_att.att_id
	FROM wx_task_main tt_main
	LEFT JOIN wx_task_sub  tt_sub ON tt_main.task_main_id = tt_sub.task_main_id
	LEFT JOIN wx_t_user tt_user ON tt_user.user_id = tt_sub.exec_user
	LEFT JOIN wx_t_organization tt_org ON tt_user.org_id = tt_org.id
	LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_sub.org_id
	LEFT JOIN wx_task_instance_check tt_check ON tt_check.sub_task_id = tt_sub.task_id
	LEFT JOIN wx_att_file tt_att ON tt_att.source_id = tt_check.id
	where
	tt_att.source_type='3' $Permission_Clause$
	AND tt_att.storage_name IS NOT NULL
	<!--AND tt_att.file_type ='image/jpeg'-->
	AND tt_main.tmb_type_code='TT_2_XD'
	AND tt_sub.task_status = '4'
	AND tt_att.passflag!='0'
	<!-- 查询4月份之后的图片 bo.liu-->
	AND CONVERT(varchar(100), tt_att.create_time, 23)>='2017-03-01' 
	<!-- 高级搜索 -->
	<if test="queryType==1"> 
		<if test="taskName!=null">
		and	
		tt_main.task_name LIKE  '%'+#{taskName}+'%' 
		</if>
		<if test="taskExcuteUserName!=null">	 
		AND  	 
		tt_sub.exec_user = #{taskExcuteUserName,jdbcType=BIGINT}
		</if>
		<if test="taskWorkshopName!=null">
		and
		tt_workshop.work_shop_name like '%'+#{taskWorkshopName}+'%'
		</if>
		<if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
	   		 <![CDATA[and tt_sub.xg_sj>= #{taskFinishTimeS}]]>
		</if>
	    <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
	    	 <![CDATA[and tt_sub.xg_sj<= #{taskFinishTimeE}]]>
	    </if>
	    <if test="currentUserType==1 and partnerId!=null and partnerId!=''">
	    AND 
		tt_sub.tenant_id = #{partnerId}  
		</if> 
	</if>
	<if test="queryType==2"> 
	    <if test="queryField!=null and queryField!=''">
	    AND
		(tt_workshop.work_shop_name  like '%'+#{queryField}+'%'  
				   	or
				   		tt_workshop.ext_property15 like '%'+#{queryField}+'%'  
				   	or	tt_main.task_name  LIKE '%'+#{queryField}+'%' 
					OR  tt_user.ch_name LIKE '%'+#{queryField}+'%'
					OR  (SELECT tt_user1.ch_name FROM wx_t_user tt_user1 WHERE tt_user1.user_id = tt_main.excute_user_id ) LIKE '%'+#{queryField}+'%'    
					or  left(dbo.f_GetPyToAboutHanyu(tt_main.task_name),500) LIKE #{firtPyForQueryField}+'%'  
					OR tt_org.organization_name LIKE '%'+#{queryField}+'%'   
						)
		</if>
	</if>
   </select> 
   
   <update id="updateMainTaskStatusByTaskMainIds" parameterType="map" >
    	update wx_task_main
    	set task_status = #{taskStatus,jdbcType=VARCHAR}
    	where
    	<if test="taskMainIds!=null">
	    	task_main_id in
	    	<foreach item="taskMainId" index="index" collection="taskMainIds" open="(" separator="," close=")">  
				 '${taskMainId}'
			</foreach> 
			and
	    </if>
	    1 = 1
   </update>
   
   
   <select id="getExportAttForXD" parameterType="map" resultMap="BaseResultMapForXDAttFile">
	SELECT tt_org.id partner_id,tt_org.organization_name organizationname, 
	tt_main.task_main_id, tt_main.task_name, tt_main.create_time,
	tt_sub.task_id,tt_sub.exec_user excute_user_id,tt_sub.org_id workshop_id,
	tt_workshop.work_shop_name workshop_name,tt_user.ch_name exec_user_name,
	tt_att.att_id,
	tt_att.storage_name,
	tt_att.store_path
	FROM wx_task_main tt_main
	LEFT JOIN wx_task_sub  tt_sub ON tt_main.task_main_id = tt_sub.task_main_id
	LEFT JOIN wx_t_user tt_user ON tt_user.user_id = tt_sub.exec_user
	LEFT JOIN wx_t_organization tt_org ON tt_user.org_id = tt_org.id
	LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = tt_sub.org_id
	LEFT JOIN wx_task_instance_check tt_check ON tt_check.sub_task_id = tt_sub.task_id
	LEFT JOIN wx_att_file tt_att ON tt_att.source_id = tt_check.id
	INNER JOIN wx_t_step_check t_step_check ON t_step_check.check_id = tt_check.check_id
	where
	tt_att.source_type='3'
	AND tt_att.storage_name IS NOT NULL
	<!--AND tt_att.file_type ='image/jpeg'-->
	AND tt_main.tmb_type_code='TT_2_XD'
	AND tt_sub.task_status = '4'
	AND tt_att.passflag!='0'
	AND t_step_check.is_export_img=1
	AND t_step_check.step_id = tt_check.step_id
	<!-- 查询4月份之后的图片 bo.liu-->
	AND CONVERT(varchar(100), tt_att.create_time, 23)>='2017-03-01' 
    <if test="partnerId!=null and partnerId!=''">
    AND 
	tt_sub.tenant_id = #{partnerId}  
	</if> 
	<if test="startTime!=null and startTime!='' and startTime!='null'">
	   		<![CDATA[and tt_sub.xg_sj>= #{startTime}]]>
	</if>
    <if test="endTime!=null and endTime!='' and endTime!='null'">
    		<![CDATA[and tt_sub.xg_sj<= #{endTime} ]]>
    </if>
	
	
   </select> 
   
   
   
   
   
   <resultMap id="ExportImgForActiveWorkshopMap" type="com.chevron.task.model.ExportImgForActiveWorkshop">
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="organizationname" jdbcType="NVARCHAR" property="organizationName" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workShopName" />
    <result column="att_id" jdbcType="BIGINT" property="attId" />
    <result column="storage_name" jdbcType="VARCHAR" property="storageName" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="store_path" jdbcType="VARCHAR" property="storagePath" />
    <result column="activetime" jdbcType="TIMESTAMP" property="activeTime" />
    <result column="workshop_id" jdbcType="VARCHAR" property="workshopId" />
    <result column="work_shop_address" jdbcType="VARCHAR" property="workshopAdd" />
    <result column="img_creat_time" jdbcType="TIMESTAMP" property="attCreatTime" />
    
    
    </resultMap>
   <select id="exportActiveWorkshopImg" parameterType="map" resultMap="ExportImgForActiveWorkshopMap">
		SELECT *
	FROM 
	    (SELECT row_number()
	        OVER (partition BY tt_org.organization_name,
	    <if test="isworkshopIdFlag!=null">
	   	tt_workshop.id,
	    </if>
	    tt_workshop.work_shop_name
	    
	    ORDER BY  tt_org.organization_name,
	    tt_workshop.work_shop_name,
	    tt_check.check_id DESC) AS group_idx, 
	    
	    tt_org.id partner_id,tt_org.organization_name organizationname, 
        tt_workshop.id workshop_id,tt_workshop.work_shop_name workshop_name,
        tt_workshop.work_shop_address, tt_w_status.create_time activetime,
	    tt_att.att_id,
	    tt_att.file_name,
	    tt_att.storage_name,
	    tt_att.store_path,
	    tt_att.create_time img_creat_time,
	    
	    tt_check.check_id
	    FROM wx_task_main tt_main
	    LEFT JOIN wx_task_sub tt_sub
	        ON tt_main.task_main_id = tt_sub.task_main_id
	    LEFT JOIN wx_t_user tt_user
	        ON tt_user.user_id = tt_sub.exec_user
	    LEFT JOIN wx_t_organization tt_org
	        ON tt_user.org_id = tt_org.id
	    LEFT JOIN wx_t_work_shop tt_workshop
	        ON tt_workshop.id = tt_sub.org_id
	    LEFT JOIN wx_task_instance_check tt_check
	        ON tt_check.sub_task_id = tt_sub.task_id
	    LEFT JOIN wx_att_file tt_att
	        ON tt_att.source_id = tt_check.id
	    INNER JOIN wx_t_step_check t_step_check
	        ON t_step_check.check_id = tt_check.check_id
	    INNER JOIN wx_t_workshop_status tt_w_status ON tt_w_status.workshop_id = tt_workshop.id AND tt_w_status.workshop_with_status='3'
	    WHERE tt_att.source_type='3'
	            AND tt_att.storage_name IS NOT NULL
	            <!-- AND tt_att.file_type ='image/jpeg'  -->
	            AND tt_main.tmb_type_code='TT_2_XD'
	            AND tt_att.passflag!='0'
	            AND t_step_check.is_export_img=1
	            AND t_step_check.step_id = tt_check.step_id
	            <if test="taskFinishStartTime!=null ">
	           <![CDATA[ AND tt_sub.xg_sj>=#{taskFinishStartTime}]]>
	            </if>
	            <if test="taskFinishEndTime!=null ">
	            <![CDATA[ AND tt_sub.xg_sj<#{taskFinishEndTime}]]>
	            </if>
	            AND tt_workshop.status='3'
	             <if test="activeWorkShopStartTime!=null ">
	           <![CDATA[ AND tt_w_status.create_time>=#{activeWorkShopStartTime}]]>
	            </if>
	              <if test="activeWorkshopEndTime!=null ">
	           <![CDATA[ AND tt_w_status.create_time<#{activeWorkshopEndTime}]]>
	            </if>
	            <if test="partnerId!=null ">
	            AND tt_org.id=#{partnerId}
	            </if>
	            ) s
	WHERE s.group_idx = 1
	order by organizationname
   </select> 
   
   
    <resultMap id="ExportMtImgPdfMap" type="com.chevron.task.model.ExportMtImg">
	    <result column="dmonth" jdbcType="VARCHAR" property="dataMonth" />
	    <result column="organization_name" jdbcType="VARCHAR" property="orgName" />
	    <result column="workshop_id" jdbcType="BIGINT" property="workshopId" />
	    <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
	    <result column="file_name_mt" jdbcType="VARCHAR" property="mtImgName" />
	    <result column="store_path_mt" jdbcType="VARCHAR" property="mtImgPath" />
	    <result column="prov_name" jdbcType="VARCHAR" property="provName" />
	    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
	    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
	    <result column="reach_standard_flag" jdbcType="INTEGER" property="reachFlag" />
    </resultMap>
  <select id="exportMtImgPdf" parameterType="map" resultMap="ExportMtImgPdfMap">  
    SELECT * FROM
    (SELECT RIGHT(LEFT(a.close_date,
         7),
         2) dmonth,
         t_org.organization_name,
         a.workshop_id,
         a.workshop_name,
         af.storage_name AS file_name_mt,
         af.store_path AS store_path_mt ,
         t3.region_name prov_name,
         t2.region_name city_name,
         t_region.region_name,
         isnull(a.reach_standard_flag,
         dbo.fun_is_def_reach_standard(a.id,
         a.cishu,
         null)) reach_standard_flag
    FROM 
        (SELECT t.id,
         t.workshop_id,
         t.partner_id,
         t.scan_capacity,
         ws.work_shop_name AS workshop_name ,
         t.close_date,
         t.award_fee ,
         
            (SELECT COUNT(1) AS xc
            FROM wx_task_sub s
            JOIN wx_task_main m
                ON m.task_main_id = s.task_main_id
            WHERE s.org_id = t.workshop_id
                    AND s.task_status = 4
                    AND m.tmb_type_code = 'TT_2_XD'
                    AND CONVERT(varchar(7), s.xg_sj, 23) = CONVERT(varchar(7), t.close_date, 23)) AS cishu, cxpj.storage_name, cxpj.store_path, t.reach_standard_flag, t.review_flag
            FROM wx_t_oil_ver_award_det t
            JOIN wx_t_work_shop ws
                ON ws.id = t.workshop_id
            LEFT JOIN 
                (SELECT af2.storage_name,
         af2.store_path,
         ts2.org_id ,
         CONVERT(varchar(7),
         ts2.xg_sj,
         23) AS xg_sj ,
         ROW_NUMBER()
                    OVER (PARTITION BY CONVERT(varchar(7), ts2.xg_sj, 23), ts2.org_id
                ORDER BY  tic2.check_id DESC, af2.att_id) AS rn
                FROM wx_task_sub ts2
                JOIN wx_task_main tm2
                    ON tm2.task_main_id = ts2.task_main_id
                        AND tm2.tmb_type_code = 'TT_2_XD'
                JOIN wx_task_instance_check tic2
                    ON tic2.sub_task_id = ts2.task_id
                        AND tic2.check_id IN (23061847, 23041850)
                JOIN wx_att_file af2
                    ON af2.source_type = 3
                        AND af2.source_id = tic2.id
                WHERE af2.att_id IS NOT NULL ) cxpj
                    ON cxpj.org_id = t.workshop_id
                        AND cxpj.xg_sj = CONVERT(varchar(7), t.close_date, 23)
                        AND cxpj.rn = 1
                WHERE 1 = 1
                        AND t.close_date &gt;= #{startTime}
                        AND t.close_date &lt; #{endTime}
                        AND t.partner_id = #{partnerId} ) a
                LEFT JOIN wx_t_work_shop w
                    ON w.id = a.workshop_id
                LEFT JOIN wx_att_file af
                    ON af.uuid = w.photo_id
                LEFT JOIN wx_t_region t_region
                    ON t_region.id = w.region_id
                LEFT JOIN wx_t_region t2
                    ON t2.id = t_region.parent_id
                LEFT JOIN wx_t_region t3
                    ON t3.id = t2.parent_id
                LEFT JOIN wx_t_organization t_org
                    ON t_org.id = a.partner_id
                WHERE 1 = 1
                        AND cishu &gt;= #{cishu} ) aa
            WHERE reach_standard_flag=1
        ORDER BY  dmonth,reach_standard_flag DESC 
 </select>  
   <select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
  		select s.*, m.create_user, m.task_name from wx_task_sub s 
  		left join wx_task_main m on s.task_main_id=m.task_main_id
  		where 1=1
  		<if test="taskMainId != null">
  		and s.task_main_id = #{taskMainId}  
  		</if>
  		<if test="orderNo != null">
  		and m.order_no = #{orderNo}  
  		</if>
  		<if test="subTaskId != null">
  		and s.task_id in (#{subTaskId})
  		</if>
  </select>
  <!-- <delete id="deleteTaskMainBySubOrgId" parameterType="map">
  	DELETE wx_task_main
		FROM
			wx_task_main w
		WHERE
			 w.task_main_id NOT IN (
				SELECT DISTINCT
					task_main_id
				FROM
					wx_task_sub
			)
			<if test="tmbTypes != null ">
				AND w.tmb_type_code in
				<foreach item="tmbTypeCode" index="index" collection="tmbTypes" open="(" separator="," close=")">  
				 #{tmbTypeCode}  
				</foreach> 
			</if>
  </delete> -->


  <select id="queryTaskByTypeAndBrand" parameterType="com.chevron.task.model.MainTaskQueryConditions"
          resultMap="BaseResultMapNew">
    SELECT
    tt.*
    FROM
    (
    SELECT
    t.*, (
    CASE
    WHEN t.task_status = '4' AND t.is_pass = 1 THEN '8' /*合格*/
    WHEN t.task_status = '4' AND t.is_pass = - 1 THEN '5' /*未抽查*/
    ELSE t.task_status END) AS new_task_status
    FROM
    (
    SELECT
    ppcos.region AS region_name,
    tt_main.task_name AS task_name,
    tt_main.task_start_time,
    t_sub.xg_sj task_finish_time,
    tt_main.tmb_type_code,
    tt_main.create_user,
    tt_main_createu.ch_name create_user_name,
    tt_main.create_time,
    tt_main.in_stock_no,
    t_sub.task_status,
    t_sub.exec_user excute_user_id,
    tt_user.ch_name exec_user_name,
    t_sub.check_result,
    t_sub.is_pass,
    t_sub.task_remark,
    t_sub.check_count,
    t_sub.process_status,
    t_sub.attribute1,
    t_sub.attribute2,
    t_sub.attribute3,
    t_sub.brand,
    tt_main_createu.mobile_tel,
    tt_main_createu.email,
    t_sub.task_id,
    t_sub.task_main_id,
    t_sub.tenant_id partner_id,
    tt_org.organization_name organizationname,
    tt_workshop.work_shop_name workshop_name,
    tt_workshop.work_shop_address workshop_addr,
    tt_workshop.id workshop_id,
    tt_workshop.type workshop_type,
    tt_workshop.ext_property8 monthly_oil_sales_volume,
    tt_workshop.ext_property9 monthly_chevron_oil_sales_volume,
    tt_main.task_priority,
    tt_workshop.ext_property16,
    tt_workshop.status f_workshopstatus,
    t_sub.check_evaluation
    FROM
    wx_task_sub t_sub
    INNER JOIN wx_task_main tt_main ON tt_main.task_main_id = t_sub.task_main_id
    LEFT JOIN wx_t_user tt_user ON tt_user.user_id = t_sub.exec_user
    LEFT JOIN wx_t_user tt_main_createu ON tt_main.create_user = tt_main_createu.user_id
    LEFT JOIN wx_t_organization tt_org ON tt_user.org_id = tt_org.id
    LEFT JOIN wx_t_work_shop tt_workshop ON tt_workshop.id = t_sub.org_id
    LEFT JOIN wx_t_partner_o2o_enterprise poe ON poe.partner_id=(case when tt_org.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id=tt_org.id) else tt_org.id end)
    LEFT JOIN (
    SELECT
    distributor_id,
    region
    FROM
    PP_MID.dbo.syn_dw_to_pp_customer_org_sales
    GROUP BY
    distributor_id,
    region
    ) ppcos ON poe.distributor_id = ppcos.distributor_id
    where tt_main.tmb_type_code = #{taskTypeCode}
      <!-- 新客户录入排除提交的潜在客户 -->
      <if test="taskTypeCode == 'TT_2_7n3/' or taskTypeCode == 'TT_2_7n7/'">
      and (CONVERT(INT, tt_workshop.ext_property16) &amp; 1) = 1
      </if>
      <!-- 高级搜索 -->
      <if test="queryType==1">
      <if test="regionName != null and regionName != ''">
        AND ppcos.region = #{regionName}
      </if>
      <if test="taskName!=null">
        AND tt_main.task_name LIKE '%'+#{taskName}+'%'
      </if>
      <if test="taskExcuteUserName!=null">
        AND t_sub.exec_user = #{taskExcuteUserName,jdbcType=BIGINT}
      </if>
      <if test="taskWorkshopName!=null">
        AND tt_workshop.work_shop_name like '%'+#{taskWorkshopName}+'%'
      </if>
      <if test="taskWorkshopScale!=null">
        AND tt_workshop.ext_property15 like '%'+#{taskWorkshopScale}+'%'
      </if>
      <if test="taskFinishTimeS!=null and taskFinishTimeS!=''">
        AND <![CDATA[ t_sub.xg_sj>= #{taskFinishTimeS} ]]>
      </if>
      <if test="taskFinishTimeE!=null and taskFinishTimeE!=''">
        AND <![CDATA[ t_sub.xg_sj<= #{taskFinishTimeE}  ]]>
      </if>
      <if test="currentUserType==1 and partnerId!=null and partnerId!=''">
        AND t_sub.tenant_id = #{partnerId}
      </if>
      <if test="brand != null">
        AND t_sub.brand = #{brand}
      </if>
      <if test="subTaskId != null">
        AND t_sub.task_id = #{subTaskId}
      </if>
      </if>
      and tt_user.ch_name IS NOT NULL
      and 1 = 1 $Permission_Clause$
    ) AS t
    ) tt
    <where>
      <if test="taskMainStatus!=null">
        AND tt.new_task_status=#{taskMainStatus,jdbcType=VARCHAR}
      </if>
      AND tt.new_task_status in (3,4,5,8,12)
    </where>
  </select>
</mapper>