<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.task.dao.PartnerDefTemplateMapper">
  <resultMap id="BaseResultMap" type="com.chevron.task.model.PartnerDefTemplate">
    <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
    <result column="task_type" property="taskType" jdbcType="VARCHAR"/>
    <result column="def_task_template" property="defTaskTemplate" jdbcType="BIGINT"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="partner_name" jdbcType="VARCHAR" property="partnerName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    partner_id,task_type,def_task_template,create_time,update_user,update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.task.model.PartnerDefTemplateExample">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from wx_task_partner_def_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.PartnerDefTemplateExample">
    delete from wx_task_partner_def_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.PartnerDefTemplate">
    insert into wx_task_partner_def_template (partner_id,task_type,def_task_template,create_time,update_user,update_time)
    values (#{partner_id,jdbcType=BIGINT},#{task_type,jdbcType=VARCHAR},#{def_task_template,jdbcType=BIGINT},#{create_time,jdbcType=TIMESTAMP},
      #{update_user,jdbcType=BIGINT},#{update_time,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.task.model.PartnerDefTemplate">
    insert into wx_task_partner_def_template
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="partnerId != null" >
        partner_id,
      </if>
      <if test="taskType != null" >
        task_type,
      </if>
      <if test="defTaskTemplate != null" >
        def_task_template,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="partnerId != null" >
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="defTaskTemplate != null" >
        #{defTaskTemplate,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.task.model.PartnerDefTemplateExample">
    select count(1) from wx_task_partner_def_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_task_partner_def_template
    <set>
      <if test="record.partnerId != null" >
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null" >
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.defTaskTemplate != null" >
        def_task_template = #{record.defTaskTemplate,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null" >
        update_user = #{record.updateUser,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="selectByParams" resultMap="BaseResultMap" parameterType="map">
    select p1.organization_name partner_name, pdt1.partner_id,pdt1.task_type,pdt1.def_task_template,pdt1.create_time,pdt1.update_user,pdt1.update_time
    from wx_task_partner_def_template pdt1
    join (select o.organization_name, o.id from wx_t_organization o where o.type=1
			union all select '全局', 1) p1 on p1.id=pdt1.partner_id
	where pdt1.task_type=#{taskType}
	<if test="defTaskTemplate != null">
		pdt1.def_task_template=#{defTaskTemplate}
	</if>
  </select>
</mapper>
