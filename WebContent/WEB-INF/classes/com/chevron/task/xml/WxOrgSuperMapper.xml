<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.task.dao.WxOrgSuperMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.task.model.WxOrgSuper" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    <id column="ds_id" property="dsId" jdbcType="BIGINT" />
    <result column="ds_type" property="dsType" jdbcType="INTEGER" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="create_user" property="createUser" jdbcType="BIGINT" />
    <result column="xg_sj" property="xgSj" jdbcType="TIMESTAMP" />
    <result column="xg_user" property="xgUser" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="tenant_id" property="tenantId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    ds_id, ds_type, org_id, user_id, create_user, xg_sj, xg_user, status, tenant_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.task.model.WxOrgSuperExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_org_super
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />
    from wx_org_super
    where ds_id = #{dsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    delete from wx_org_super
    where ds_id = #{dsId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.WxOrgSuperExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    delete from wx_org_super
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.WxOrgSuper" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    insert into wx_org_super (ds_id, ds_type, org_id, 
      user_id, create_user, xg_sj, 
      xg_user, status, tenant_id
      )
    values (#{dsId,jdbcType=BIGINT}, #{dsType,jdbcType=INTEGER}, #{orgId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{createUser,jdbcType=BIGINT}, #{xgSj,jdbcType=TIMESTAMP}, 
      #{xgUser,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.task.model.WxOrgSuper" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    insert into wx_org_super
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dsId != null" >
        ds_id,
      </if>
      <if test="dsType != null" >
        ds_type,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="xgSj != null" >
        xg_sj,
      </if>
      <if test="xgUser != null" >
        xg_user,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dsId != null" >
        #{dsId,jdbcType=BIGINT},
      </if>
      <if test="dsType != null" >
        #{dsType,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null" >
        #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null" >
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.task.model.WxOrgSuperExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    select count(*) from wx_org_super
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    update wx_org_super
    <set >
      <if test="record.dsId != null" >
        ds_id = #{record.dsId,jdbcType=BIGINT},
      </if>
      <if test="record.dsType != null" >
        ds_type = #{record.dsType,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null" >
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null" >
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null" >
        create_user = #{record.createUser,jdbcType=BIGINT},
      </if>
      <if test="record.xgSj != null" >
        xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xgUser != null" >
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    update wx_org_super
    set ds_id = #{record.dsId,jdbcType=BIGINT},
      ds_type = #{record.dsType,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=BIGINT},
      xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.WxOrgSuper" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    update wx_org_super
    <set >
      <if test="dsType != null" >
        ds_type = #{dsType,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="createUser != null" >
        create_user = #{createUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null" >
        xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xgUser != null" >
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where ds_id = #{dsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.WxOrgSuper" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 14 11:07:05 CST 2015.
    -->
    update wx_org_super
    set ds_type = #{dsType,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=BIGINT},
      xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      xg_user = #{xgUser,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where ds_id = #{dsId,jdbcType=BIGINT}
  </update>
</mapper>