<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.task.dao.WxTaskInfoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.task.model.WxTaskInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    <id column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser" />
    <result column="cur_chief_user" jdbcType="BIGINT" property="curChiefUser" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="task_priority" jdbcType="VARCHAR" property="taskPriority" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="zt" jdbcType="VARCHAR" property="zt" />
    <result column="xz_sj" jdbcType="TIMESTAMP" property="xzSj" />
    <result column="xz_user" jdbcType="BIGINT" property="xzUser" />
    <result column="xg_sj" jdbcType="TIMESTAMP" property="xgSj" />
    <result column="xg_user" jdbcType="BIGINT" property="xgUser" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <resultMap id="SimpResultMap" type="com.chevron.task.model.WxTaskInfoSimp">
    <id column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="taskTypeName" jdbcType="VARCHAR" property="taskTypeName" />
    
    
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="task_create_user" jdbcType="BIGINT" property="taskCreateUser" />
    <result column="createUserName" jdbcType="VARCHAR" property="taskCreateUserName" />
    
    <result column="cur_chief_user" jdbcType="BIGINT" property="curChiefUser" />
    <result column="curChiefUserName" jdbcType="VARCHAR" property="curChiefUserName" />
    
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="task_priority" jdbcType="VARCHAR" property="taskPriority" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
  </resultMap>  
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    task_id, task_name, task_type, task_description, task_start_time, task_finish_time, 
    task_create_user, cur_chief_user, task_status, task_priority, room_id, room_name, 
    zt, xz_sj, xz_user, xg_sj, xg_user, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.chevron.task.model.WxTaskInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wx_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
   <select id="selectTaskInfo" parameterType="com.chevron.task.model.WxTaskInfoExample" resultMap="SimpResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    select
      distinct
    <include refid="Base_Column_List" />,
    (select u.ch_name from wx_t_user u where u.user_id=task_create_user  ) as createUserName,
    (select u.ch_name from wx_t_user u where u.user_id=cur_chief_user  ) as curChiefUserName,    
    (select m.data_name from wx_t_data m where m.data_code = task_type  ) as taskTypeName    
    
    from wx_task_info
    <where>
    <if test="_parameter != null">
      <include refid="Example_noWhere_Clause" />
    </if>
    <if test="particiUser != null">
      	or task_id in(
			select m.task_id tid from wx_task_member m where m.partici_user 
				=  #{particiUser,jdbcType=BIGINT}
      	)
    </if>
    </where>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="SimpResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    select 
    <include refid="Base_Column_List" />,
    (select u.ch_name from wx_t_user u where u.user_id=task_create_user  ) as createUserName,
    (select u.ch_name from wx_t_user u where u.user_id=cur_chief_user  ) as curChiefUserName,    
    (select m.data_name from wx_t_data m where m.data_code = task_type  ) as taskTypeName 
    from wx_task_info
    where task_id = #{taskId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    delete from wx_task_info
    where task_id = #{taskId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.WxTaskInfoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    delete from wx_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="taskId" parameterType="com.chevron.task.model.WxTaskInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    insert into wx_task_info (task_id, task_name, task_type, 
      task_description, task_start_time, task_finish_time, 
      task_create_user, cur_chief_user, task_status, 
      task_priority, room_id, room_name, 
      zt, xz_sj, xz_user, 
      xg_sj, xg_user, tenant_id
      )
    values (#{taskId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{taskType,jdbcType=VARCHAR}, 
      #{taskDescription,jdbcType=VARCHAR}, #{taskStartTime,jdbcType=TIMESTAMP}, #{taskFinishTime,jdbcType=TIMESTAMP}, 
      #{taskCreateUser,jdbcType=BIGINT}, #{curChiefUser,jdbcType=BIGINT}, #{taskStatus,jdbcType=VARCHAR}, 
      #{taskPriority,jdbcType=VARCHAR}, #{roomId,jdbcType=VARCHAR}, #{roomName,jdbcType=VARCHAR}, 
      #{zt,jdbcType=VARCHAR}, #{xzSj,jdbcType=TIMESTAMP}, #{xzUser,jdbcType=BIGINT}, 
      #{xgSj,jdbcType=TIMESTAMP}, #{xgUser,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="taskId" parameterType="com.chevron.task.model.WxTaskInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    insert into wx_task_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="taskStartTime != null">
        task_start_time,
      </if>
      <if test="taskFinishTime != null">
        task_finish_time,
      </if>
      <if test="taskCreateUser != null">
        task_create_user,
      </if>
      <if test="curChiefUser != null">
        cur_chief_user,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="taskPriority != null">
        task_priority,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="roomName != null">
        room_name,
      </if>
      <if test="zt != null">
        zt,
      </if>
      <if test="xzSj != null">
        xz_sj,
      </if>
      <if test="xzUser != null">
        xz_user,
      </if>
      <if test="xgSj != null">
        xg_sj,
      </if>
      <if test="xgUser != null">
        xg_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskFinishTime != null">
        #{taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCreateUser != null">
        #{taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="curChiefUser != null">
        #{curChiefUser,jdbcType=BIGINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskPriority != null">
        #{taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="zt != null">
        #{zt,jdbcType=VARCHAR},
      </if>
      <if test="xzSj != null">
        #{xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xzUser != null">
        #{xzUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null">
        getdate(),
      </if>
      <if test="xgUser != null">
        #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.task.model.WxTaskInfoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    select count(*) from wx_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    update wx_task_info
    <set>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDescription != null">
        task_description = #{record.taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStartTime != null">
        task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskFinishTime != null">
        task_finish_time = #{record.taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskCreateUser != null">
        task_create_user = #{record.taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="record.curChiefUser != null">
        cur_chief_user = #{record.curChiefUser,jdbcType=BIGINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.taskPriority != null">
        task_priority = #{record.taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=VARCHAR},
      </if>
      <if test="record.roomName != null">
        room_name = #{record.roomName,jdbcType=VARCHAR},
      </if>
      <if test="record.zt != null">
        zt = #{record.zt,jdbcType=VARCHAR},
      </if>
      <if test="record.xzSj != null">
        xz_sj = #{record.xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xzUser != null">
        xz_user = #{record.xzUser,jdbcType=BIGINT},
      </if>
      <if test="record.xgSj != null">
        xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xgUser != null">
        xg_user = #{record.xgUser,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    update wx_task_info
    set task_id = #{record.taskId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_description = #{record.taskDescription,jdbcType=VARCHAR},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      task_finish_time = #{record.taskFinishTime,jdbcType=TIMESTAMP},
      task_create_user = #{record.taskCreateUser,jdbcType=BIGINT},
      cur_chief_user = #{record.curChiefUser,jdbcType=BIGINT},
      task_status = #{record.taskStatus,jdbcType=VARCHAR},
      task_priority = #{record.taskPriority,jdbcType=VARCHAR},
      room_id = #{record.roomId,jdbcType=VARCHAR},
      room_name = #{record.roomName,jdbcType=VARCHAR},
      zt = #{record.zt,jdbcType=VARCHAR},
      xz_sj = #{record.xzSj,jdbcType=TIMESTAMP},
      xz_user = #{record.xzUser,jdbcType=BIGINT},
      xg_sj = #{record.xgSj,jdbcType=TIMESTAMP},
      xg_user = #{record.xgUser,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.WxTaskInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    update wx_task_info
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskDescription != null">
        task_description = #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskFinishTime != null">
        task_finish_time = #{taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCreateUser != null">
        task_create_user = #{taskCreateUser,jdbcType=BIGINT},
      </if>
      <if test="curChiefUser != null">
        cur_chief_user = #{curChiefUser,jdbcType=BIGINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskPriority != null">
        task_priority = #{taskPriority,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        room_name = #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="zt != null">
        zt = #{zt,jdbcType=VARCHAR},
      </if>
      <if test="xzSj != null">
        xz_sj = #{xzSj,jdbcType=TIMESTAMP},
      </if>
      <if test="xzUser != null">
        xz_user = #{xzUser,jdbcType=BIGINT},
      </if>
      <if test="xgSj != null">
        xg_sj = getdate(),
      </if>
      <if test="xgUser != null">
        xg_user = #{xgUser,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where task_id = #{taskId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.WxTaskInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 28 16:39:27 CST 2015.
    -->
    update wx_task_info
    set task_name = #{taskName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      task_description = #{taskDescription,jdbcType=VARCHAR},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      task_finish_time = #{taskFinishTime,jdbcType=TIMESTAMP},
      task_create_user = #{taskCreateUser,jdbcType=BIGINT},
      cur_chief_user = #{curChiefUser,jdbcType=BIGINT},
      task_status = #{taskStatus,jdbcType=VARCHAR},
      task_priority = #{taskPriority,jdbcType=VARCHAR},
      room_id = #{roomId,jdbcType=VARCHAR},
      room_name = #{roomName,jdbcType=VARCHAR},
      zt = #{zt,jdbcType=VARCHAR},
      xz_sj = #{xzSj,jdbcType=TIMESTAMP},
      xz_user = #{xzUser,jdbcType=BIGINT},
      xg_sj = #{xgSj,jdbcType=TIMESTAMP},
      xg_user = #{xgUser,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT}
    where task_id = #{taskId,jdbcType=BIGINT}
  </update>
    <sql id="Example_noWhere_Clause">

      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
  </sql>
</mapper>