<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.task.dao.TaskmbStepVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.task.model.TaskmbStepVoKey" >
    <id column="relate_type" property="relateType" jdbcType="TINYINT" />
    <id column="step_id" property="stepId" jdbcType="BIGINT" />
    <id column="relate_id" property="relateId" jdbcType="BIGINT" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="com.chevron.task.model.TaskmbStepVoKey" >
    delete from wx_taskmb_step
    where relate_type = #{relateType,jdbcType=TINYINT}
      and step_id = #{stepId,jdbcType=BIGINT}
      and relate_id = #{relateId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.TaskmbStepVoKey" >
    insert into wx_taskmb_step (relate_type, step_id, relate_id
      )
    values (#{relateType,jdbcType=TINYINT}, #{stepId,jdbcType=BIGINT}, #{relateId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.task.model.TaskmbStepVoKey" >
    insert into wx_taskmb_step
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="relateType != null" >
        relate_type,
      </if>
      <if test="stepId != null" >
        step_id,
      </if>
      <if test="relateId != null" >
        relate_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="relateType != null" >
        #{relateType,jdbcType=TINYINT},
      </if>
      <if test="stepId != null" >
        #{stepId,jdbcType=BIGINT},
      </if>
      <if test="relateId != null" >
        #{relateId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  
  <!-- 查询步骤选择信息 -->
  <resultMap id="SelectResultMap" type="com.chevron.task.model.SelectStep" >
    <result column="step_id" property="stepId" jdbcType="BIGINT" />
    <result column="selected" property="selected" jdbcType="TINYINT" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="step_desc" jdbcType="VARCHAR" property="stepDesc" />
    <result column="step_code" jdbcType="VARCHAR" property="stepCode" />
  </resultMap>
  <select id="selSelectSteps" resultMap="SelectResultMap">
 SELECT * from
(
SELECT t.data_name AS step_name,
       t.data_id AS step_id,
       t.data_descript AS step_desc,
       t.data_code as step_code,
       CASE
           WHEN t1.relate_id IS NULL THEN 0
           ELSE 1
       END AS [selected]
FROM wx_t_data t
LEFT JOIN wx_taskmb_step t1 ON t.data_id=t1.step_id
AND t1.relate_type=#{relateType,jdbcType=TINYINT}
AND t1.relate_id=#{relateId,jdbcType=BIGINT}
WHERE t.data_code LIKE 'TS_%_${taskType}' and t.status=1
)tt
WHERE tt.selected=1

  </select>
  <!-- 批量插入步骤关联信息 -->
    <insert id="insertBatch" parameterType="java.util.List">
    insert into wx_taskmb_step (relate_type, step_id, relate_id
      )
    values 
    <foreach collection="array" index="index" item="item" separator=",">
	   	<trim prefix="(" suffix=")" suffixOverrides=",">
    #{item.relateType,jdbcType=TINYINT}, #{item.stepId,jdbcType=BIGINT}, #{item.relateId,jdbcType=BIGINT}
	   	</trim>
	</foreach>
  </insert>
  <!-- 通过关联关系删除关系记录 -->
  <delete id="deleteByRelate">
    delete from wx_taskmb_step
    where relate_type = #{relateType,jdbcType=TINYINT}
      and relate_id = #{relateId,jdbcType=BIGINT}
  </delete>
  
  
   <select id="selSelectSteps2" resultMap="SelectResultMap">
	SELECT t.data_name AS step_name,
	       t.data_id AS step_id,
	       t.data_descript AS step_desc,
	       t.data_code as step_code,
	       CASE
	           WHEN t1.relate_id IS NULL THEN 0
	           ELSE 1
	       END AS [selected]
	FROM wx_t_data t
	LEFT JOIN wx_taskmb_step t1 ON t.data_id=t1.step_id
	AND t1.relate_type=#{relateType,jdbcType=TINYINT}
	AND t1.relate_id=#{relateId,jdbcType=BIGINT}
	WHERE t.data_code LIKE 'TS_%_${taskType}' and t.status=1
  </select>
</mapper>