<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.task.dao.WxTaskHasgdWorkshopMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.task.model.WxTaskHasgdWorkshop" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="workshop_id" property="workshopId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, workshop_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.task.model.WxTaskHasgdWorkshopExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_task_has_gd_workshop
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_task_has_gd_workshop
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_task_has_gd_workshop
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.chevron.task.model.WxTaskHasgdWorkshopExample" >
    delete from wx_task_has_gd_workshop
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.task.model.WxTaskHasgdWorkshop" >
    insert into wx_task_has_gd_workshop (id, workshop_id)
    values (#{id,jdbcType=BIGINT}, #{workshopId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.task.model.WxTaskHasgdWorkshop" >
    insert into wx_task_has_gd_workshop
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="workshopId != null" >
        workshop_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workshopId != null" >
        #{workshopId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_task_has_gd_workshop
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workshopId != null" >
        workshop_id = #{record.workshopId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_task_has_gd_workshop
    set id = #{record.id,jdbcType=BIGINT},
      workshop_id = #{record.workshopId,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.task.model.WxTaskHasgdWorkshop" >
    update wx_task_has_gd_workshop
    <set >
      <if test="workshopId != null" >
        workshop_id = #{workshopId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.task.model.WxTaskHasgdWorkshop" >
    update wx_task_has_gd_workshop
    set workshop_id = #{workshopId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  
 <delete id="deleteByWorkshopIds" parameterType="map">
  	 delete
  	 	from wx_task_has_gd_workshop
  	 	where
   		 <if test="workshopIds!=null">
	    	workshop_id in
	    	<foreach item="workshopId" index="index" collection="workshopIds" open="(" separator="," close=")">  
				 #{workshopId}  
			</foreach> 
			and
	    </if>
	    1 = 1
  </delete>
  
  
  <insert id="insertBatch" parameterType="map" >
   insert into wx_task_has_gd_workshop (workshop_id)
    values
    <foreach collection="workshopIds" index="index" item="workshopId" separator=",">
	   	<trim prefix="(" suffix=")" suffixOverrides=",">
	    #{workshopId,jdbcType=BIGINT}
	   	</trim>
	</foreach>
  </insert> 
  
   <select id="selectByWorkshopIds" parameterType="map" resultMap="BaseResultMap">
     select *
        from wx_task_has_gd_workshop
        where
         <if test="workshopIds2!=null">
            workshop_id in
            <foreach item="workshopId" index="index" collection="workshopIds2" open="(" separator="," close=")">  
                 #{workshopId}  
            </foreach> 
            and
        </if>
        1 = 1
  </select>
  
  
</mapper>