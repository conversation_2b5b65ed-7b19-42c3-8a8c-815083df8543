<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.point.dao.WXTPointValueDetailVoMapper">
  <resultMap id="BaseResultMap" type="com.chevron.point.model.WXTPointValueDetailVo">
    <id column="ID" property="id" jdbcType="BIGINT"/>
    <result column="BUSINESS_ID" property="businessId" jdbcType="BIGINT"/>
    <result column="POINT_ACCOUNT_ID" property="pointAccountId" jdbcType="BIGINT"/>
    <result column="POINT_TYPE" property="pointType" jdbcType="VARCHAR"/>
    <result column="POINT_VALUE" property="pointValue" jdbcType="NUMERIC"/>
    <result column="POINT_PAYED" property="pointPayed" jdbcType="NUMERIC"/>
    <result column="ACTIVATION_DATE" property="activationDate" jdbcType="TIMESTAMP"/>
    <result column="EXPIRED_DATE" property="expiredDate" jdbcType="TIMESTAMP"/>
    <result column="POINT_RULE_ID" property="pointRuleId" jdbcType="BIGINT"/>
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
    <result column="STATE" property="state" jdbcType="VARCHAR"/>
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT"/>
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="VARCHAR"/>
    <result column="ATTRIBUTE2" property="attribute2" jdbcType="VARCHAR"/>
    <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP"/>
    <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT"/>
    <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT"/>
    <result column="TRANS_TIME" property="transTime" jdbcType="TIMESTAMP"/>
    <result column="SUB_TYPE" jdbcType="NVARCHAR" property="subType" />
    <result column="CLEAR_TIME" property="clearTime" jdbcType="TIMESTAMP"/>
  </resultMap>
  <resultMap id="PointSummaryResultMap" type="com.chevron.point.model.PointSummary">
    <result column="SAP_CODE" property="sapCode" jdbcType="VARCHAR"/>
    <result column="ORGANIZATION_NAME" property="organizationName" jdbcType="NVARCHAR"/>
    <result column="FROZEN_TYPE_STATE" property="frozenTypeState" jdbcType="NVARCHAR" />
    <result column="TOTAL_IMPORT_VALUE" property="totalImportValue" jdbcType="NUMERIC"/>
    <result column="TOTAL_POINT_VALUE" property="totalPointValue" jdbcType="NUMERIC"/>
    <result column="TOTAL_PAID_VALUE" property="totalPaidValue" jdbcType="NUMERIC"/>
    <result column="TOTAL_ROLLBACK_VALUE" property="totalRollbackValue" jdbcType="NUMERIC"/>
    <result column="TOTAL_MINUS_VALUE" property="totalMinusValue" jdbcType="NUMERIC"/>
    <result column="TOTAL_LEFT_VALUE" property="totalLeftValue" jdbcType="NUMERIC"/>
    <result column="POINT_TYPE" property="pointType" jdbcType="VARCHAR"/>
    <result column="POINT_ACCOUNT_ID" property="pointAccountId" jdbcType="BIGINT"/>
    <result column="SUB_TYPE" property="subType" jdbcType="NVARCHAR"/>
    <result column="EM_NAME" property="emName" jdbcType="NVARCHAR" />
    <result column="EM_CODE" property="emCode" jdbcType="NVARCHAR" />
  </resultMap>
  <resultMap id="PointMonthSumMap" type="com.chevron.point.model.PointMonthSum">
    <result column="IMPORT_KEY" property="importKey" jdbcType="VARCHAR"/>
    <result column="POINT_ACCOUNT_ID" property="pointAccountId" jdbcType="BIGINT"/>
    <result column="SAP_CODE" property="sapCode" jdbcType="VARCHAR"/>
    <result column="ORGANIZATION_NAME" property="organizationName" jdbcType="VARCHAR"/>
    <result column="POINT_TYPE" property="pointType" jdbcType="VARCHAR"/>
    <result column="IMPORT_TIME" property="importTime" jdbcType="VARCHAR" />
    <result column="TOTAL_IMPORT_VALUE" property="totalImportValue" jdbcType="NUMERIC"/>
  </resultMap>
  <resultMap id="OrgInfoMap" type="com.chevron.point.model.OrgInfo">
    <result column="id" property="id" jdbcType="BIGINT"/>
    <result column="organization_code" property="organizationCode" jdbcType="VARCHAR"/>
    <result column="organization_name" property="organizationName" jdbcType="VARCHAR"/>
    <result column="distributor_id" property="distributorId" jdbcType="BIGINT"/>
    <result column="sap_code" property="sapCode" jdbcType="VARCHAR"/>
    <result column="sales_channel_name" property="salesChannelName" jdbcType="VARCHAR" />
    <result column="region_name" property="regionName" jdbcType="VARCHAR"/>
    <result column="enterprise_code" property="enterpriseCode" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="PointAdjustListItemMap" type="com.chevron.point.model.PointAdjustListItem">
    <result column="ID" property="id" jdbcType="BIGINT"/>
    <result column="BUSINESS_TYPE_CODE" property="businessTypeCode" jdbcType="VARCHAR"/>
    <result column="BUSINESS_DESC" property="businessDesc" jdbcType="VARCHAR"/>
    <result column="PB_CREATION_TIME" property="pbCreationTime" jdbcType="TIMESTAMP"/>
    <result column="ATT_ID" property="attId" jdbcType="BIGINT"/>
    <result column="POINT_VALUE" property="pointValue" jdbcType="BIGINT" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
    <result column="TRANS_TIME" property="transTime" jdbcType="TIMESTAMP" />
    <result column="PVD_CREATION_TIME" property="pvdCreationTime" jdbcType="TIMESTAMP" />
    <result column="CREATED_BY_USER" property="createdByUser" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="B2BPointRankMap" type="com.chevron.point.model.B2BPointRank">
    <result column="point_position" property="position" jdbcType="NUMERIC"/>
    <result column="total_import_point" property="totalImportPoint" jdbcType="NUMERIC"/>
    <result column="code" property="code" jdbcType="VARCHAR"/>
    <result column="name" property="name" jdbcType="VARCHAR"/>
    <result column="workshop_id" property="workshopId" jdbcType="NUMERIC"/>
    <result column="workshop_name" property="workshopName" jdbcType="VARCHAR"/>
    <result column="region_name" property="regionName" jdbcType="VARCHAR"/>
    <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
    <result column="province_name" property="provinceName" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="EmB2bPointSummaryMap" type="com.chevron.point.model.EmB2bPointSummary">
    <result column="code" property="code" jdbcType="VARCHAR"/>
    <result column="name" property="name" jdbcType="VARCHAR"/>
    <result column="employee_type" property="employeeType" jdbcType="VARCHAR"/>
    <result column="total_left_point" property="totalLeftPoint" jdbcType="NUMERIC"/>
  </resultMap>

  <!-- 结果集映射 经销商积分-->
  <resultMap id="PartnerPointResultMap" type="com.chevron.sellin.model.PartnerPromotionVo">
    <result column="partner_id" property="partnerId" jdbcType="BIGINT"/>
    <result column="promotion_id" property="promotionId" jdbcType="BIGINT"/>
    <result column="promotion_name" property="promotionName" jdbcType="VARCHAR"/>
    <result column="partner_name" property="partnerName" jdbcType="VARCHAR"/>
    <result column="point_type" property="pointType" jdbcType="VARCHAR"/>
    <result column="point_code" property="pointCode" jdbcType="VARCHAR"/>
    <result column="point_name" property="pointName" jdbcType="VARCHAR"/>
    <result column="balance" property="balance" jdbcType="NUMERIC"/>
    <result column="type" property="type" jdbcType="VARCHAR"/>
    <result column="promotion_flag" property="promotionFlag"/>
    <result column="region" property="region" jdbcType="VARCHAR"/>
    <result column="predefine_flag" property="predefineFlag"/>
    <result column="coupon_count" property="couponCount"/>
    <result column="retailer_flag" property="retailerFlag"/>
  </resultMap>
  <!--查询distributor_id和sales_channel的关系-->
  <sql id="query_sales_channel_relation">
		(SELECT DISTINCT
		v.distributor_id,
		v.channel_weight 
	FROM
		view_customer_region_sales_channel v
		
		)
  </sql>
  <sql id="sub_type_where_clause">
    <choose>
      <when test="bizType != null">
       AND pvd.sub_type = #{bizType}
      </when>
      <when test="bizType == null">
       AND pvd.sub_type is null
      </when>
    </choose>
  </sql>
  <sql id="import_point_where_clause">
      AND (pb.BUSINESS_TYPE_CODE in ('DEALER_POINT_MONTHLY_IMPORT','PROMOTION_POINT_IMPORT','CALTEX_POINT_FROM_BI','CALTEX_POINT_FROM_PROMOTE','CDM_MATERIAL_POINT_IMPORT','CDM_STOCK_POINT_IMPORT','CDM_PROMOTION_POINT_IMPORT','CDM_STOCK_POINT_FROM_BI','OEM_STOCK_POINT_IMPORT','OEM_STOCK_POINT_FROM_BI','POINT_IMPORT','ADJUST_POINT','CDM_STORE_OPEN_POINT_IMPORT','COMMERCIAL_HERO_PRODUCT_POINT_IMPORT','SELL_IN_PROMOTION_DELIVERY_V2','CONSUMER_HERO_PRODUCT_POINT_IMPORT') )
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID,BUSINESS_ID,POINT_ACCOUNT_ID,POINT_TYPE,POINT_VALUE,POINT_PAYED,ACTIVATION_DATE,EXPIRED_DATE,POINT_RULE_ID,COMMENTS,
    STATE,DELETE_FLAG,ATTRIBUTE1,ATTRIBUTE2,CREATION_TIME,CREATED_BY,LAST_UPDATE_TIME,LAST_UPDATED_BY,TRANS_TIME,SUB_TYPE,CLEAR_TIME
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.point.model.WXTPointValueDetailVoExample">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from wx_t_point_value_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.point.model.WXTPointValueDetailVoExample">
    delete from wx_t_point_value_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.point.model.WXTPointValueDetailVo">
    insert into wx_t_point_value_detail (BUSINESS_ID,POINT_ACCOUNT_ID,POINT_TYPE,POINT_VALUE,POINT_PAYED,ACTIVATION_DATE,EXPIRED_DATE,POINT_RULE_ID,COMMENTS,
      STATE,DELETE_FLAG,ATTRIBUTE1,ATTRIBUTE2,CREATION_TIME,CREATED_BY,LAST_UPDATE_TIME,LAST_UPDATED_BY,TRANS_TIME,SUB_TYPE)
    values (#{businessId,jdbcType=BIGINT},#{pointAccountId,jdbcType=BIGINT},#{pointType,jdbcType=VARCHAR},#{pointValue,jdbcType=NUMERIC},
      #{pointPayed,jdbcType=NUMERIC},#{activationDate,jdbcType=TIMESTAMP},#{expiredDate,jdbcType=TIMESTAMP},#{pointRuleId,jdbcType=BIGINT},#{comments,jdbcType=VARCHAR},
      #{state,jdbcType=VARCHAR},#{deleteFlag,jdbcType=BIT},#{attribute1,jdbcType=VARCHAR},#{attribute2,jdbcType=VARCHAR},#{creationTime,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=BIGINT},#{lastUpdateTime,jdbcType=TIMESTAMP},#{lastUpdatedBy,jdbcType=BIGINT},#{transTime,jdbcType=TIMESTAMP},
      #{subType,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.point.model.WXTPointValueDetailVo" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_point_value_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="businessId != null" >
        BUSINESS_ID,
      </if>
      <if test="pointAccountId != null" >
        POINT_ACCOUNT_ID,
      </if>
      <if test="pointType != null" >
        POINT_TYPE,
      </if>
      <if test="pointValue != null" >
        POINT_VALUE,
      </if>
      <if test="pointPayed != null" >
        POINT_PAYED,
      </if>
      <if test="activationDate != null" >
        ACTIVATION_DATE,
      </if>
      <if test="expiredDate != null" >
        EXPIRED_DATE,
      </if>
      <if test="pointRuleId != null" >
        POINT_RULE_ID,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
      <if test="transTime != null" >
        TRANS_TIME,
      </if>
      <if test="subType != null">
        SUB_TYPE,
      </if>
      <if test="clearTime != null" >
        CLEAR_TIME,
      </if>
        <if test="sparkAmount != null" >
            spark_amount,
        </if>
        <if test="onlineAmount != null" >
            online_amount,
        </if>
      <if test="orderNo != null" >
        order_no,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="businessId != null" >
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="pointAccountId != null" >
        #{pointAccountId,jdbcType=BIGINT},
      </if>
      <if test="pointType != null" >
        #{pointType,jdbcType=VARCHAR},
      </if>
      <if test="pointValue != null" >
        #{pointValue,jdbcType=NUMERIC},
      </if>
      <if test="pointPayed != null" >
        #{pointPayed,jdbcType=NUMERIC},
      </if>
      <if test="activationDate != null" >
        #{activationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredDate != null" >
        #{expiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="pointRuleId != null" >
        #{pointRuleId,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="transTime != null" >
        #{transTime,jdbcType=TIMESTAMP},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=NVARCHAR},
      </if>
      <if test="clearTime != null" >
        #{clearTime,jdbcType=TIMESTAMP},
      </if>
        <if test="sparkAmount != null" >
            #{sparkAmount,jdbcType=NUMERIC},
        </if>
        <if test="onlineAmount != null" >
            #{onlineAmount,jdbcType=NUMERIC},
        </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chevron.point.model.WXTPointValueDetailVoExample">
    select count(1) from wx_t_point_value_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.point.model.WXTPointValueDetailVo">
    update wx_t_point_value_detail
    <set>
      <if test="businessId != null" >
        BUSINESS_ID = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="pointAccountId != null" >
        POINT_ACCOUNT_ID = #{pointAccountId,jdbcType=BIGINT},
      </if>
      <if test="pointType != null" >
        POINT_TYPE = #{pointType,jdbcType=VARCHAR},
      </if>
      <if test="pointValue != null" >
        POINT_VALUE = #{pointValue,jdbcType=NUMERIC},
      </if>
      <if test="pointPayed != null" >
        POINT_PAYED = #{pointPayed,jdbcType=NUMERIC},
      </if>
      <if test="activationDate != null" >
        ACTIVATION_DATE = #{activationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredDate != null" >
        EXPIRED_DATE = #{expiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="pointRuleId != null" >
        POINT_RULE_ID = #{pointRuleId,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG = #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="transTime != null" >
        TRANS_TIME = #{transTime,jdbcType=TIMESTAMP},
      </if>
      <if test="subType != null">
        SUB_TYPE = #{subType,jdbcType=NVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_point_value_detail
    <set>
      <if test="record.businessId != null" >
        BUSINESS_ID = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.pointAccountId != null" >
        POINT_ACCOUNT_ID = #{record.pointAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.pointType != null" >
        POINT_TYPE = #{record.pointType,jdbcType=VARCHAR},
      </if>
      <if test="record.pointValue != null" >
        POINT_VALUE = #{record.pointValue,jdbcType=NUMERIC},
      </if>
      <if test="record.pointPayed != null" >
        POINT_PAYED = #{record.pointPayed,jdbcType=NUMERIC},
      </if>
      <if test="record.activationDate != null" >
        ACTIVATION_DATE = #{record.activationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiredDate != null" >
        EXPIRED_DATE = #{record.expiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pointRuleId != null" >
        POINT_RULE_ID = #{record.pointRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.comments != null" >
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null" >
        STATE = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.transTime != null" >
        TRANS_TIME = #{record.transTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.subType != null">
        SUB_TYPE = #{record.subType,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.point.model.WXTPointValueDetailVoParams">
<![CDATA[
    select IDt1.BUSINESS_ID,t1.POINT_ACCOUNT_ID,t1.POINT_TYPE,t1.POINT_VALUE,t1.POINT_PAYED,t1.ACTIVATION_DATE,
           t1.EXPIRED_DATE,t1.POINT_RULE_ID,t1.COMMENTS,t1.STATE,t1.DELETE_FLAG,t1.ATTRIBUTE1,t1.ATTRIBUTE2,
           t1.CREATION_TIME,t1.CREATED_BY,t1.LAST_UPDATE_TIME,t1.LAST_UPDATED_BY,t1.TRANS_TIME,t1.SUB_TYPE
      from wx_t_point_value_detail t1
     where 1=1
]]>
  </select>
  <select id="selectPointDetailByPointTypeAndBizType" resultMap="BaseResultMap" parameterType="map">
    SELECT
      d.ID,
      d.BUSINESS_ID,
      d.POINT_ACCOUNT_ID,
      d.POINT_TYPE,
      d.POINT_VALUE,
      d.POINT_PAYED,
      d.ACTIVATION_DATE,
      d.EXPIRED_DATE,
      d.POINT_RULE_ID,
      d.COMMENTS,
      d.STATE,
      d.DELETE_FLAG,
      d.ATTRIBUTE1,
      d.ATTRIBUTE2,
      d.CREATION_TIME,
      d.CREATED_BY,
      d.LAST_UPDATE_TIME,
      d.LAST_UPDATED_BY,
      d.TRANS_TIME,
      d.SUB_TYPE
    FROM
      dbo.wx_t_point_value_detail d
    LEFT JOIN dbo.wx_t_point_business pb on
      d.BUSINESS_ID = pb.ID
    WHERE 1=1
     AND d.POINT_TYPE = #{pointType, jdbcType=NVARCHAR}
     AND d.POINT_ACCOUNT_ID = #{pointAccountId,jdbcType=BIGINT}
    <choose>
      <when test='bizType != null'>
        AND d.SUB_TYPE = #{bizType, jdbcType=NVARCHAR}
      </when>
      <when test='bizType == null'>
        AND d.SUB_TYPE is null
      </when>
    </choose>
      AND ((   	<![CDATA[d.ACTIVATION_DATE <= #{nowDate,jdbcType=DATE}
      and d.EXPIRED_DATE >= #{nowDate,jdbcType=DATE} ]]>)
      or( 1=1
      and d.ACTIVATION_DATE is null
      and d.EXPIRED_DATE is null))
      AND d.DELETE_FLAG = 0
    order by
      d.CREATION_TIME ASC
  </select>
  <!--这个是查询机构的PointAccountType那些SP的-->
<select id="selectPointSummaryByPointType" resultMap="PointSummaryResultMap" parameterType="map">
    select
    total_info.SAP_CODE,
    total_info.organization_name ORGANIZATION_NAME,
    total_info.FROZEN_TYPE_STATE,
    import_info.TOTAL_IMPORT_VALUE,
    ISNULL(( total_info.TOTAL_PAID_VALUE - ISNULL(rollback_info.TOTAL_ROLLBACK_VALUE,0)),
    0 ) AS TOTAL_PAID_VALUE,
    total_info.TOTAL_POINT_VALUE,
    ISNULL(rollback_info.TOTAL_ROLLBACK_VALUE,0) TOTAL_ROLLBACK_VALUE,
    ISNULL(minus_info.TOTAL_MINUS_VALUE,0) TOTAL_MINUS_VALUE,
    total_info.LEFT_POINT as TOTAL_LEFT_VALUE,
    total_info.POINT_TYPE,
    total_info.POINT_ACCOUNT_ID
    from
    (
    SELECT
    pac.id POINT_ACCOUNT_ID,
    o.id ORG_ID,
    en.sap_code SAP_CODE,
    o.organization_name,
    sum(pvd.POINT_VALUE) TOTAL_POINT_VALUE,
    sum(pvd.POINT_PAYED) TOTAL_PAID_VALUE,
    sum( pvd.POINT_VALUE - pvd.POINT_PAYED ) AS LEFT_POINT,
    pvd.POINT_TYPE,
    case
    when CHARINDEX( #{frozenType,jdbcType=NVARCHAR}, pac.FROZEN_TYPE ) > 0 then '冻结'
    else '正常'
    end as FROZEN_TYPE_STATE
    FROM
    wx_t_organization o
    LEFT JOIN wx_t_partner_o2o_enterprise en ON
        en.partner_id = o.id
        <if test='orgStatus !=null'>
        AND o.status = #{orgStatus,jdbcType=INTEGER}
      </if>
<!--     LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = en.distributor_id -->
    LEFT JOIN wx_t_point_account pac ON
        pac.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN wx_t_point_value_detail pvd ON
        pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN (
		select
			kpi.KPI_TARGET,
			kpi.START_TIME,
			kpi.END_TIME,
			kpi.PARTNER_ID
		from
			dbo.wx_t_dealer_business_kpi kpi
		where
			(
				kpi.start_time &lt;= getdate()
				and kpi.end_time &gt; getdate()
			)
	) k on
	  k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
	  dw.customer_name_cn = o.organization_name
	  and dw.trans_time &gt;= k.start_time
	  and dw.trans_time &lt; k.end_time
  LEFT JOIN dbo.wx_t_point_business pb on
      pvd.BUSINESS_ID = pb.ID
    WHERE
    1 = 1
	<if test="channelWeight > 0">
		<!-- and (dwrsr.channel_weight &amp; #{channelWeight} >0 -->
		AND (
		EXISTS (SELECT 1 FROM view_customer_region_sales_channel dwrsr WHERE dwrsr.distributor_id = en.distributor_id AND dwrsr.channel_weight &amp; #{channelWeight} >0 )
		OR EXISTS (
		SELECT
			1 
		FROM
			wx_t_user u1
			LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
			LEFT JOIN wx_t_role r1 ON r1.role_id= ur1.role_id
			LEFT JOIN wx_t_value_transform_map vtm1 ON vtm1.transform_type= 'ChannelWieghtMapping' 
			AND vtm1.value_before_transform= r1.sales_channel
		WHERE
			( u1.type IS NULL OR u1.type != '1' ) 
			AND u1.org_id= o.id 
			AND u1.status= 1 
			AND vtm1.value_after_transform &amp; #{channelWeight} > 0 
			)
			)
	</if>
	AND len(en.sap_code) &gt;= 7
    AND pvd.POINT_TYPE = #{pointType,jdbcType=NVARCHAR}
    AND pvd.DELETE_FLAG = 0
    <include refid="sub_type_where_clause"></include>
    <if test='orgId != null'>
      AND (pac.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT})
    </if>
    GROUP BY
    o.id,
    pac.id,
    en.sap_code,
    o.organization_name,
    pvd.POINT_TYPE,
    pac.FROZEN_TYPE ) total_info
    left join (
    SELECT
    en.sap_code SAP_CODE,
    sum(pvd.POINT_VALUE) TOTAL_MINUS_VALUE
    FROM
    wx_t_organization o
    LEFT JOIN wx_t_partner_o2o_enterprise en ON
    en.partner_id = o.id
    LEFT JOIN wx_t_point_account pac ON
    pac.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN wx_t_point_value_detail pvd ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN (
		select
			kpi.KPI_TARGET,
			kpi.START_TIME,
			kpi.END_TIME,
			kpi.PARTNER_ID
		from
			dbo.wx_t_dealer_business_kpi kpi
		where
			(
				kpi.start_time &lt;= getdate()
				and kpi.end_time &gt; getdate()
			)
	) k on
	  k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
	  dw.customer_name_cn = o.organization_name
	  and dw.trans_time &gt;= k.start_time
	  and dw.trans_time &lt; k.end_time
  LEFT JOIN dbo.wx_t_point_business pb on
    pvd.BUSINESS_ID = pb.ID
    WHERE
    1 = 1
    AND len(en.sap_code) &gt;= 7
    AND pvd.POINT_TYPE =  #{pointType,jdbcType=NVARCHAR}
    AND pvd.DELETE_FLAG = 0
    AND pb.BUSINESS_TYPE_CODE = 'CLEAR_POINT'
    <include refid="sub_type_where_clause"></include>
    <if test='orgId != null'>
      AND (pac.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT})
    </if>
    GROUP BY
    o.id,
    pac.id,
    en.sap_code,
    o.organization_name,
    pvd.POINT_TYPE,
    pac.FROZEN_TYPE ) minus_info on
    total_info.SAP_CODE = minus_info.SAP_CODE
    left join (
    SELECT
    en.sap_code SAP_CODE,
    sum(pvd.POINT_VALUE) TOTAL_IMPORT_VALUE
    FROM
    wx_t_organization o
    LEFT JOIN wx_t_partner_o2o_enterprise en ON
    en.partner_id = o.id
    LEFT JOIN wx_t_point_account pac ON
    pac.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN wx_t_point_value_detail pvd ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN (
		select
			kpi.KPI_TARGET,
			kpi.START_TIME,
			kpi.END_TIME,
			kpi.PARTNER_ID
		from
			dbo.wx_t_dealer_business_kpi kpi
		where
			(
				kpi.start_time &lt;= getdate()
				and kpi.end_time &gt; getdate()
			)
	) k on
	  k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
	  dw.customer_name_cn = o.organization_name
	  and dw.trans_time &gt;= k.start_time
	  and dw.trans_time &lt; k.end_time
    LEFT JOIN dbo.wx_t_point_business pb on
      pvd.BUSINESS_ID = pb.ID
    WHERE
    1 = 1
    AND len(en.sap_code) &gt;= 7
    AND pvd.POINT_TYPE =  #{pointType,jdbcType=NVARCHAR}
    <include refid="sub_type_where_clause"></include>
    AND pvd.DELETE_FLAG = 0
    <include refid="import_point_where_clause"></include>
    <if test='orgId != null'>
      AND (pac.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT})
    </if>
    GROUP BY
    o.id,
    pac.id,
    en.sap_code,
    o.organization_name,
    pvd.POINT_TYPE,
    pac.FROZEN_TYPE ) import_info on
    total_info.SAP_CODE = import_info.SAP_CODE
    left join (
    SELECT
    en.sap_code SAP_CODE,
    sum(pvd.POINT_VALUE) TOTAL_ROLLBACK_VALUE
    FROM
    wx_t_organization o
    LEFT JOIN wx_t_partner_o2o_enterprise en ON
    en.partner_id = o.id
    LEFT JOIN wx_t_point_account pac ON
    pac.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN wx_t_point_value_detail pvd ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN (
		select
			kpi.KPI_TARGET,
			kpi.START_TIME,
			kpi.END_TIME,
			kpi.PARTNER_ID
		from
			dbo.wx_t_dealer_business_kpi kpi
		where
			(
				kpi.start_time &lt;= getdate()
				and kpi.end_time &gt; getdate()
			)
	) k on
	  k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
	  dw.customer_name_cn = o.organization_name
	  and dw.trans_time &gt;= k.start_time
	  and dw.trans_time &lt; k.end_time
  LEFT JOIN dbo.wx_t_point_business pb on
    pvd.BUSINESS_ID = pb.ID
    WHERE
    1 = 1
    AND len(en.sap_code) &gt;= 7
    AND pvd.POINT_TYPE =  #{pointType,jdbcType=NVARCHAR}
    AND pvd.DELETE_FLAG = 0
    AND pvd.POINT_VALUE > 0
    AND (pb.BUSINESS_TYPE_CODE = 'ROLLBACK_POINT')
    <include refid="sub_type_where_clause"></include>
      <if test='orgId != null'>
        AND (pac.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT})
      </if>
    GROUP BY
    o.id,
    pac.id,
    en.sap_code,
    o.organization_name,
    pvd.POINT_TYPE,
    pac.FROZEN_TYPE ) rollback_info on
    total_info.SAP_CODE = rollback_info.SAP_CODE
    ORDER BY
    total_info.organization_name
</select>
  <sql id="getB2BRankList_sql">
    select
    <if test="top != null">
      top ${top}
    </if>
    rank() over (ORDER BY sum(pvd.POINT_VALUE) desc ) point_position,
    sum(pvd.POINT_VALUE) total_import_point,
    em.code,
    em.name,
    em.workshop_id,
    em.workshop_name,
    t1.region_name region_name,
    t2.region_name city_name,
    t3.region_name province_name
    from dbo.wx_t_point_value_detail pvd
    left join dbo.wx_t_point_business pb on pb.id = pvd.BUSINESS_ID
    left join dbo.wx_t_point_account pa on pa.ID = pvd.POINT_ACCOUNT_ID
    left join dbo.wx_t_workshop_employee em on em.code = pa.POINT_ACCOUNT_OWNER_CODE
    left join dbo.wx_t_work_shop shop on shop.id = em.workshop_id
    left join wx_t_region t1 on shop.region_id=t1.id
    left join wx_t_region t2 on t1.parent_id=t2.id
    left join wx_t_region t3 on t2.parent_id=t3.id
    where
    1 = 1
    and pvd.POINT_TYPE = 'B2B_POINT'
    <if test='subType != null'>
      and pvd.sub_type = #{subType,jdbcType=NVARCHAR}
    </if>
    and em.code is not null
    and pb.BUSINESS_TYPE_CODE = 'POINT_IMPORT'
    group by em.code,em.workshop_id,em.workshop_name,em.name,t1.region_name,t2.region_name,t3.region_name
  </sql>
<select id="getB2BRankList" resultMap="B2BPointRankMap" parameterType="map">
  <include refid="getB2BRankList_sql" />
     order by total_import_point desc
</select>
<select id="getB2BRankPositionByEmCode" resultMap="B2BPointRankMap" parameterType="map">
  select position_info.* from(
  <include refid="getB2BRankList_sql" />
) position_info where position_info.code = #{emCode,jdbcType=NVARCHAR}
</select>
<select id="queryUnapprovedPoint" resultType="java.lang.Double" parameterType="map">
  select isnull(sum(s.award_point),0) award_point
  from wx_t_schedule s left join wx_t_schedule_entity se on s.id=se.schedule_id
  left join wx_t_schedule_instance si on se.id=si.schedule_id
  where 1=1 and si.executor_type='mechanic' and si.status &gt;= 15 and si.status &lt; 100
  <if test="salesChannel != null">
    and s.sales_channel = #{salesChannel,jdbcType=NVARCHAR}
  </if>
  <if test="emCode != null">
    and si.executor_key = #{emCode,jdbcType=NVARCHAR}
  </if>
  group by si.executor_key
</select>
<select id="selectIndividualPointSummaryByPointType" resultMap="PointSummaryResultMap" parameterType="map">
  SELECT
    total_info.EM_CODE,
    total_info.EM_NAME EM_NAME,
    total_info.FROZEN_TYPE_STATE,
    import_info.TOTAL_IMPORT_VALUE,
    ISNULL(( total_info.TOTAL_PAID_VALUE - ISNULL(rollback_info.TOTAL_ROLLBACK_VALUE,0)),0 ) AS TOTAL_PAID_VALUE,
    total_info.TOTAL_POINT_VALUE,
    ISNULL(rollback_info.TOTAL_ROLLBACK_VALUE,0) TOTAL_ROLLBACK_VALUE,
    ISNULL(minus_info.TOTAL_MINUS_VALUE,0) TOTAL_MINUS_VALUE,
    (case
    when minus_info.TOTAL_MINUS_VALUE is null then total_info.LEFT_POINT
    else total_info.LEFT_POINT + minus_info.TOTAL_MINUS_VALUE
    end ) as TOTAL_LEFT_VALUE,
    total_info.POINT_TYPE,
    total_info.POINT_ACCOUNT_ID,
    total_info.SUB_TYPE
  FROM
  (
  SELECT pac.id POINT_ACCOUNT_ID,
    em.code EM_CODE,
    em.name EM_NAME,
    sum(pvd.POINT_VALUE) TOTAL_POINT_VALUE,
    sum(pvd.POINT_PAYED) TOTAL_PAID_VALUE,
    sum( pvd.POINT_VALUE - pvd.POINT_PAYED ) AS LEFT_POINT,
    pvd.POINT_TYPE,
    case
    when CHARINDEX(#{salesChannel,jdbcType=NVARCHAR},pac.FROZEN_TYPE ) > 0 then '冻结'
    else '正常'
    end as FROZEN_TYPE_STATE,
    pvd.SUB_TYPE
    FROM
    dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pac ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN dbo.wx_t_point_business pb ON
    pvd.BUSINESS_ID = pb.ID
    LEFT JOIN dbo.wx_t_workshop_employee em ON
    em.code = pac.POINT_ACCOUNT_OWNER_CODE
  WHERE
    1 = 1
    <if test='pointAccountType != null'>
    AND pac.POINT_ACCOUNT_TYPE = #{pointAccountType,jdbcType=NVARCHAR}
    </if>
    <if test='pointType != null'>
    AND pvd.POINT_TYPE = #{pointType,jdbcType=NVARCHAR}
    </if>
    <if test='salesChannel != null'>
    AND pvd.SUB_TYPE = #{salesChannel,jdbcType=NVARCHAR}
    </if>
    <if test='userId != null'>
    AND pac.POINT_ACCOUNT_OWNER_CODE = #{userId,jdbcType=NVARCHAR}
    </if>
    AND pvd.POINT_VALUE &gt; 0
    AND pvd.DELETE_FLAG = 0
  GROUP BY
    em.code,
    pac.id,
    em.name,
    pvd.POINT_TYPE,
    pac.FROZEN_TYPE,
    pvd.SUB_TYPE) total_info
  LEFT JOIN (
  SELECT em.code EM_CODE,
    sum(pvd.POINT_VALUE) TOTAL_MINUS_VALUE
    FROM
    dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pac ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN dbo.wx_t_point_business pb ON
    pvd.BUSINESS_ID = pb.ID
    LEFT JOIN dbo.wx_t_workshop_employee em ON
    em.code = pac.POINT_ACCOUNT_OWNER_CODE
  WHERE
    1 = 1
    AND pvd.POINT_VALUE &lt; 0
    <if test='pointAccountType != null'>
      AND pac.POINT_ACCOUNT_TYPE = #{pointAccountType,jdbcType=NVARCHAR}
    </if>
    <if test='pointType != null'>
      AND pvd.POINT_TYPE = #{pointType,jdbcType=NVARCHAR}
    </if>
    <if test='salesChannel != null'>
      AND pvd.SUB_TYPE = #{salesChannel,jdbcType=NVARCHAR}
    </if>
    <if test='userId != null'>
      AND pac.POINT_ACCOUNT_OWNER_CODE = #{userId,jdbcType=NVARCHAR}
    </if>
    AND pvd.DELETE_FLAG = 0
  GROUP BY
    em.code) minus_info ON
  minus_info.EM_CODE = total_info.EM_CODE
  LEFT JOIN (
    SELECT em.code EM_CODE,
    sum(pvd.POINT_VALUE) TOTAL_IMPORT_VALUE
    FROM
    dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pac ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN dbo.wx_t_point_business pb ON
    pvd.BUSINESS_ID = pb.ID
    LEFT JOIN dbo.wx_t_workshop_employee em ON
    em.code = pac.POINT_ACCOUNT_OWNER_CODE
  WHERE
    1 = 1
    AND pvd.POINT_VALUE &gt; 0
    <if test='pointAccountType != null'>
      AND pac.POINT_ACCOUNT_TYPE = #{pointAccountType,jdbcType=NVARCHAR}
    </if>
    <if test='pointType != null'>
      AND pvd.POINT_TYPE = #{pointType,jdbcType=NVARCHAR}
    </if>
    <if test='salesChannel != null'>
      AND pvd.SUB_TYPE = #{salesChannel,jdbcType=NVARCHAR}
    </if>
    <if test='userId != null'>
      AND pac.POINT_ACCOUNT_OWNER_CODE = #{userId,jdbcType=NVARCHAR}
    </if>
    AND pb.BUSINESS_TYPE_CODE != 'ROLLBACK_POINT'
    AND pvd.DELETE_FLAG = 0
  GROUP BY
    em.code) import_info ON
    import_info.EM_CODE = total_info.EM_CODE
  LEFT JOIN (
  SELECT em.code EM_CODE,
    sum(pvd.POINT_VALUE) TOTAL_ROLLBACK_VALUE
    FROM
    dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pac ON
    pvd.POINT_ACCOUNT_ID = pac.ID
    LEFT JOIN dbo.wx_t_point_business pb ON
    pvd.BUSINESS_ID = pb.ID
    LEFT JOIN dbo.wx_t_workshop_employee em ON
    em.code = pac.POINT_ACCOUNT_OWNER_CODE
  WHERE
    1 = 1
    <if test='pointAccountType != null'>
      AND pac.POINT_ACCOUNT_TYPE = #{pointAccountType,jdbcType=NVARCHAR}
    </if>
    <if test='pointType != null'>
      AND pvd.POINT_TYPE = #{pointType,jdbcType=NVARCHAR}
    </if>
    <if test='salesChannel != null'>
      AND pvd.SUB_TYPE = #{salesChannel,jdbcType=NVARCHAR}
    </if>
    <if test='userId != null'>
      AND pac.POINT_ACCOUNT_OWNER_CODE = #{userId,jdbcType=NVARCHAR}
    </if>
    AND pb.BUSINESS_TYPE_CODE = 'ROLLBACK_POINT'
    AND pvd.DELETE_FLAG = 0
  GROUP BY
    em.code) rollback_info ON
    rollback_info.EM_CODE = total_info.EM_CODE
  ORDER BY
  total_info.EM_NAME
</select>
<select id="queryImportPointMonthly" parameterType="map" resultMap="PointMonthSumMap">
SELECT
       CONCAT(point_value_detail.SAP_CODE,'-',point_value_detail.IMPORT_TIME) IMPORT_KEY,
       point_value_detail.POINT_ACCOUNT_ID,
       point_value_detail.SAP_CODE,
       point_value_detail.ORGANIZATION_NAME,
       point_value_detail.POINT_TYPE,
       point_value_detail.IMPORT_TIME,
       SUM(point_value_detail.POINT_VALUE) TOTAL_IMPORT_VALUE
FROM
	(SELECT pvd.POINT_ACCOUNT_ID,o.organization_name ORGANIZATION_NAME,pvd.POINT_TYPE,en.sap_code SAP_CODE,pvd.POINT_VALUE POINT_VALUE,
    case
    when pvd.TRANS_TIME is not NULL then FORMAT( pvd.TRANS_TIME,
    'yyyy-MM-01 00:00:00' )
    else FORMAT( pvd.CREATION_TIME,
    'yyyy-MM-01 00:00:00' )
    end AS IMPORT_TIME
  FROM dbo.wx_t_point_value_detail pvd
	LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
	LEFT JOIN dbo.wx_t_organization o ON pa.POINT_ACCOUNT_OWNER_ID = o.id
	LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
    <!-- LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = en.distributor_id -->
    LEFT JOIN (
		select
			kpi.KPI_TARGET,
			kpi.START_TIME,
			kpi.END_TIME,
			kpi.PARTNER_ID
		from
			dbo.wx_t_dealer_business_kpi kpi
		where
			(
				kpi.start_time &lt;= getdate()
				and kpi.end_time &gt; getdate()
			)
	) k on
	  k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
	  dw.customer_name_cn = o.organization_name
	  and dw.trans_time &gt;= k.start_time
	  and dw.trans_time &lt; k.end_time
	LEFT JOIN dbo.wx_t_point_business pb on
      pvd.BUSINESS_ID = pb.ID
	WHERE
	1 = 1
	<if test="channelWeight > 0">
		<!-- and (dwrsr.channel_weight &amp; #{channelWeight} >0 -->
		AND (
		EXISTS (SELECT 1 FROM view_customer_region_sales_channel dwrsr WHERE dwrsr.distributor_id = en.distributor_id AND dwrsr.channel_weight &amp; #{channelWeight} >0 )
		OR EXISTS (
		SELECT
			1 
		FROM
			wx_t_user u1
			LEFT JOIN wx_t_userrole ur1 ON ur1.user_id = u1.user_id
			LEFT JOIN wx_t_role r1 ON r1.role_id= ur1.role_id
			LEFT JOIN wx_t_value_transform_map vtm1 ON vtm1.transform_type= 'ChannelWieghtMapping' 
			AND vtm1.value_before_transform= r1.sales_channel
		WHERE
			( u1.type IS NULL OR u1.type != '1' ) 
			AND u1.org_id= o.id 
			AND u1.status= 1 
			AND vtm1.value_after_transform &amp; #{channelWeight} > 0 
			)
			)
	</if>
	AND len(en.sap_code) &gt;= 7
	AND pvd.POINT_TYPE =  #{pointType, jdbcType=NVARCHAR}
	AND pvd.DELETE_FLAG = 0
    AND pvd.POINT_VALUE != 0
    <include refid="sub_type_where_clause"></include>
    <include refid="import_point_where_clause"></include>
  ) point_value_detail
	WHERE point_value_detail.SAP_CODE IS NOT NULL
GROUP BY point_value_detail.IMPORT_TIME,point_value_detail.ORGANIZATION_NAME,point_value_detail.SAP_CODE,point_value_detail.POINT_ACCOUNT_ID,point_value_detail.POINT_TYPE
ORDER BY point_value_detail.IMPORT_TIME,ORGANIZATION_NAME

  </select>
  <select id="selectReturnedPoint" parameterType="map" resultMap="PointMonthSumMap">
    SELECT
    CONCAT(point_value_detail.SAP_CODE,'-',point_value_detail.IMPORT_TIME) IMPORT_KEY,
    point_value_detail.POINT_ACCOUNT_ID,
    point_value_detail.SAP_CODE,
    point_value_detail.ORGANIZATION_NAME,
    point_value_detail.POINT_TYPE,
    point_value_detail.IMPORT_TIME,
    SUM(point_value_detail.POINT_VALUE) TOTAL_IMPORT_VALUE
    FROM
    (SELECT pvd.POINT_ACCOUNT_ID,o.organization_name ORGANIZATION_NAME,pvd.POINT_TYPE,en.sap_code SAP_CODE,pvd.POINT_VALUE POINT_VALUE,
    case
    when pvd.TRANS_TIME is not NULL then FORMAT( pvd.TRANS_TIME,
    'yyyy-MM-01 00:00:00' )
    else FORMAT( pvd.CREATION_TIME,
    'yyyy-MM-01 00:00:00' )
    end AS IMPORT_TIME
    FROM dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pa ON pa.ID = pvd.POINT_ACCOUNT_ID
    LEFT JOIN dbo.wx_t_organization o ON pa.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON en.partner_id = o.id
    LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = en.distributor_id
    LEFT JOIN (
    select
    kpi.KPI_TARGET,
    kpi.START_TIME,
    kpi.END_TIME,
    kpi.PARTNER_ID
    from
    dbo.wx_t_dealer_business_kpi kpi
    where
    (
    kpi.start_time &lt;= getdate()
    and kpi.end_time &gt; getdate()
    )
    ) k on
    k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
    dw.customer_name_cn = o.organization_name
    and dw.trans_time &gt;= k.start_time
    and dw.trans_time &lt; k.end_time
    LEFT JOIN dbo.wx_t_point_business pb on
    pvd.BUSINESS_ID = pb.ID
    WHERE
    1 = 1
    <if test="salesChannelName == 'Consumer'">
	and dwrsr.channel_weight &amp;1>0
	</if>
	<if test="salesChannelName == 'Commercial'">
	and dwrsr.channel_weight &amp;2>0
	</if>
	<if test="salesChannelName == 'OEM'">
	and dwrsr.channel_weight = 4
	</if>
    AND len(en.sap_code) &gt;= 7
    AND pvd.POINT_TYPE =  #{pointType, jdbcType=NVARCHAR}
    AND pvd.DELETE_FLAG = 0
    AND pvd.POINT_VALUE != 0
    AND pvd.COMMENTS like '%归还2018%'
    ) point_value_detail
    GROUP BY point_value_detail.IMPORT_TIME,point_value_detail.ORGANIZATION_NAME,point_value_detail.SAP_CODE,point_value_detail.POINT_ACCOUNT_ID,point_value_detail.POINT_TYPE
  </select>
  <select id="getOrgInfoByOrgId" parameterType="map" resultMap="OrgInfoMap">
    SELECT
          o.id,o.organization_code,o.organization_name,e.distributor_id,e.sap_code,e.enterprise_code,dwrsr.sales_channel_name,dwrsr.region_name
    FROM
        dbo.wx_t_organization o
    LEFT JOIN dbo.wx_t_partner_o2o_enterprise e ON
        o.id = e.partner_id
    LEFT JOIN dbo.dw_customer_region_sales_supervisor_rel dwrssr ON
        dwrssr.distributor_id = e.distributor_id
    LEFT JOIN dbo.dw_region_sales_channel_rel dwrsr ON
        dwrsr.region_name = dwrssr.region_name
    WHERE 1 = 1
    AND o.id = #{orgId,jdbcType=BIGINT}
  </select>
  <select id="getAdjustList" parameterType="com.chevron.point.model.PointAdjustParams" resultMap="PointAdjustListItemMap">
  SELECT
      pb.ID,
      pb.BUSINESS_TYPE_CODE,
      pb.BUSINESS_DESC,
      pb.CREATION_TIME PB_CREATION_TIME,
      pb.ATT_ID,
      pvd.POINT_VALUE,
      pvd.COMMENTS,
      pvd.TRANS_TIME,
      pb.CREATION_TIME PVD_CREATION_TIME,
      u.login_name CREATED_BY_USER,
      pac.POINT_ACCOUNT_OWNER_ID
  FROM
      dbo.wx_t_point_business pb
  LEFT JOIN dbo.wx_t_point_value_detail pvd ON
      pvd.BUSINESS_ID = pb.ID
  LEFT JOIN dbo.wx_t_user u ON
      pb.CREATED_BY = u.user_id
  LEFT JOIN dbo.wx_t_point_account pac ON
      pvd.POINT_ACCOUNT_ID = pac.ID
  WHERE 1=1
      <if test='orgId != null'>
        AND pac.POINT_ACCOUNT_OWNER_ID = #{orgId}
      </if>
      AND pb.BUSINESS_STATUS = 'DONE'
      AND pb.DELETE_FLAG = 0
      AND pvd.DELETE_FLAG = 0
      AND pvd.POINT_TYPE = #{pointType}
      AND  ( pb.BUSINESS_TYPE_CODE = 'CLEAR_POINT' OR pb.BUSINESS_TYPE_CODE = 'ADJUST_POINT' )
      <include refid="sub_type_where_clause"></include>
  </select>
  <select id="queryPromoteAsCaltexImportPointMonthly" resultMap="PointMonthSumMap">
    SELECT
    CONCAT( point_value_detail.SAP_CODE, '-', point_value_detail.IMPORT_TIME ) IMPORT_KEY,
    point_value_detail.POINT_ACCOUNT_ID,
    point_value_detail.SAP_CODE,
    point_value_detail.ORGANIZATION_NAME,
    point_value_detail.POINT_TYPE,
    point_value_detail.IMPORT_TIME,
    SUM( point_value_detail.POINT_VALUE ) TOTAL_IMPORT_VALUE
    FROM
    (
    SELECT
    pvd.POINT_ACCOUNT_ID,
    o.organization_name ORGANIZATION_NAME,
    pvd.POINT_TYPE,
    en.sap_code SAP_CODE,
    pvd.POINT_VALUE POINT_VALUE,
    case
    when pvd.TRANS_TIME is not NULL then FORMAT( pvd.TRANS_TIME,
    'yyyy-MM-01 00:00:00' )
    else FORMAT( pvd.CREATION_TIME,
    'yyyy-MM-01 00:00:00' )
    end AS IMPORT_TIME
    FROM
    dbo.wx_t_point_value_detail pvd
    LEFT JOIN dbo.wx_t_point_account pa ON
    pa.ID = pvd.POINT_ACCOUNT_ID
    LEFT JOIN dbo.wx_t_organization o ON
    pa.POINT_ACCOUNT_OWNER_ID = o.id
    LEFT JOIN dbo.wx_t_partner_o2o_enterprise en ON
    en.partner_id = o.id
    LEFT JOIN <include refid="query_sales_channel_relation"/> dwrsr ON dwrsr.distributor_id = en.distributor_id
    LEFT JOIN (
    select
    kpi.KPI_TARGET,
    kpi.START_TIME,
    kpi.END_TIME,
    kpi.PARTNER_ID
    from
    dbo.wx_t_dealer_business_kpi kpi
    where
    ( kpi.start_time &lt;= getdate()
    and kpi.end_time &gt; getdate() ) ) k on
    k.partner_id = o.id
    left join dbo.dw_base_trans_sell_in dw on
    dw.customer_name_cn = o.organization_name
    and dw.trans_time &gt;= k.start_time
    and dw.trans_time &lt; k.end_time
                        LEFT JOIN dbo.wx_t_point_business pb on
                        pvd.BUSINESS_ID = pb.ID
                        WHERE
    1 = 1
	and dwrsr.channel_weight &amp;2>0
    AND len( en.sap_code ) &gt;= 7
    AND pvd.POINT_TYPE = 'CALTEX_POINT'
    AND pb.BUSINESS_TYPE_CODE = 'CALTEX_POINT_FROM_PROMOTE'
    AND CHARINDEX('回退',pvd.COMMENTS) = 0
    AND pvd.POINT_VALUE &gt; 0
    AND pvd.DELETE_FLAG = 0
    AND ( pb.BUSINESS_TYPE_CODE != 'ROLLBACK_POINT' ) ) point_value_detail
    WHERE
    point_value_detail.SAP_CODE IS NOT NULL
    GROUP BY
    point_value_detail.IMPORT_TIME,
    point_value_detail.ORGANIZATION_NAME,
    point_value_detail.SAP_CODE,
    point_value_detail.POINT_ACCOUNT_ID,
    point_value_detail.POINT_TYPE
    ORDER BY
    point_value_detail.IMPORT_TIME,
    ORGANIZATION_NAME
  </select>
  <select id="getEmPointSumByWorkshopId" parameterType="map" resultMap="EmB2bPointSummaryMap">
    select em.code,em.name,SUM(ISNULL((pvd.POINT_VALUE - pvd.POINT_PAYED),0)) total_left_point,em.employee_type  from dbo.wx_t_workshop_employee em
     left join dbo.wx_t_point_account pac on em.code = pac.POINT_ACCOUNT_OWNER_CODE
     left join dbo.wx_t_point_value_detail pvd on pvd.POINT_ACCOUNT_ID = pac.ID and pvd.DELETE_FLAG = 0
                <if test="salesChannel != null">
                  and pvd.sub_type = #{salesChannel,jdbcType = NVARCHAR}
                </if>
                and pvd.POINT_TYPE = 'B2B_POINT'
     where 1=1
     and em.workshop_id = #{workshopId,jdbcType=BIGINT}
    group by
      em.code,em.name,em.employee_type
    order by em.name
  </select>
  <insert id="insertBatch" parameterType="map">
    insert into wx_t_point_value_detail (BUSINESS_ID,POINT_ACCOUNT_ID,POINT_TYPE,POINT_VALUE,POINT_PAYED,COMMENTS,
      DELETE_FLAG,ATTRIBUTE1,ATTRIBUTE2,CREATION_TIME,CREATED_BY,TRANS_TIME,SUB_TYPE) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
    (select id from wx_t_point_business 
	where BUSINESS_TYPE_CODE='${item.pointBusinessVo.businessTypeCode}' and RELATED_CODE='${item.pointBusinessVo.relatedCode}')
	,(select ID from wx_t_point_account where POINT_ACCOUNT_TYPE='${item.pointAccountVo.pointAccountType}' and POINT_ACCOUNT_OWNER_ID=${item.pointAccountVo.pointAccountOwnerId}),
	#{item.pointType,jdbcType=VARCHAR},#{item.pointValue,jdbcType=NUMERIC},#{item.pointPayed,jdbcType=NUMERIC},#{item.comments,jdbcType=VARCHAR},
      #{item.deleteFlag,jdbcType=BIT},#{item.attribute1,jdbcType=VARCHAR},#{item.attribute2,jdbcType=VARCHAR},#{item.creationTime,jdbcType=TIMESTAMP},
      #{item.createdBy,jdbcType=BIGINT},#{item.transTime,jdbcType=TIMESTAMP},#{item.subType,jdbcType=NVARCHAR}
			</trim>
		</foreach>
  </insert>


  <select id="sumPitpackPointOfPartner" resultType="double">
    SELECT SUM(point_value) from
    (SELECT CASE WHEN SUM(pvd.point_value) IS NULL THEN 0.0 ELSE SUM(pvd.point_value) END AS point_value from wx_t_point_account pa
    LEFT JOIN wx_t_point_value_detail pvd on pa.id=pvd.POINT_ACCOUNT_ID
    WHERE pvd.POINT_TYPE='New_workshop' and pvd.delete_flag = 0 and pvd.POINT_VALUE>0
    and pvd.creation_time &lt; #{endDate,jdbcType=DATE}
    and pa.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT}
    UNION ALL
    SELECT CASE WHEN SUM(pvd.point_value) IS NULL THEN 0.0 ELSE SUM(pvd.point_value) END AS point_value from wx_t_point_account pa
    LEFT JOIN wx_t_point_value_detail pvd on pa.id=pvd.POINT_ACCOUNT_ID
    WHERE pvd.POINT_TYPE='New_workshop' and pvd.delete_flag = 0 and pvd.POINT_VALUE &lt; 0
    and pa.POINT_ACCOUNT_OWNER_ID = #{orgId,jdbcType=BIGINT} )t
  </select>

  <select id="ClearPitpackPointOfPartner" resultType="map">
    SELECT
    t.POINT_ACCOUNT_OWNER_ID AS orgId,
    t.point_value AS point
    FROM
    (
    SELECT
    pa.POINT_ACCOUNT_OWNER_ID,
    SUM (pvd.point_value) AS point_value
    FROM
    wx_t_point_account pa
    LEFT JOIN wx_t_point_value_detail pvd ON pa.id = pvd.POINT_ACCOUNT_ID
    WHERE
    pvd.POINT_TYPE = 'New_workshop' and pvd.delete_flag = 0
    AND pvd.creation_time &lt; #{endDate,jdbcType=DATE}
    GROUP BY
    pa.POINT_ACCOUNT_OWNER_ID
    ) t
    WHERE
    t.point_value > 0
  </select>

  <select id="ClearMothPitpackPointOfPartner" resultType="map">
    SELECT * FROM
    (SELECT
    t.POINT_ACCOUNT_OWNER_ID AS orgId,
    SUM(t.point_value) point
    from
    (
    SELECT
    pa.POINT_ACCOUNT_OWNER_ID,
    SUM (pvd.point_value) AS point_value
    FROM
    wx_t_point_account pa
    LEFT JOIN wx_t_point_value_detail pvd ON
    pa.id = pvd.POINT_ACCOUNT_ID
    WHERE
    pvd.POINT_TYPE = 'New_workshop'
    and pvd.delete_flag = 0
    --AND pvd.TRANS_TIME &lt; #{endDate,jdbcType=DATE}
    AND pvd.CREATION_TIME &lt; #{endDate,jdbcType=DATE}
    AND pvd.POINT_VALUE>0
    GROUP BY
    pa.POINT_ACCOUNT_OWNER_ID
    UNION All
    SELECT
    pa.POINT_ACCOUNT_OWNER_ID,
    SUM (pvd.point_value) AS point_value
    FROM
    wx_t_point_account pa
    LEFT JOIN wx_t_point_value_detail pvd ON
    pa.id = pvd.POINT_ACCOUNT_ID
    WHERE
    pvd.POINT_TYPE = 'New_workshop'
    and pvd.delete_flag = 0
    AND pvd.POINT_VALUE&lt;0
    GROUP BY
    pa.POINT_ACCOUNT_OWNER_ID )t
    GROUP BY t.POINT_ACCOUNT_OWNER_ID)tt
    WHERE tt.point > 0
  </select>

  <!--获取剩余积分，清除-->

  <!--<select id="getAllPromotionPartnerInfo" resultType="map">
    select partner_id as partnerId,partner_name,sum(balance) as balance, point_type from
    (select 'point' as [type],t4.id as partner_id,
    t4.organization_name as partner_name,
    sum(t3.POINT_VALUE-t3.POINT_PAYED) as balance,t5.point_type,
    case when t4.type=3 then 1 else 0 end retailer_flag from
    wx_t_point_account t1
    inner join wx_t_partner_o2o_enterprise t2 on t1.POINT_ACCOUNT_OWNER_ID = t2.partner_id
    left join wx_t_organization t4 on t2.partner_id  = t4.id
    left join wx_t_organization r1o1 on t4.type=3 and r1o1.id=t2.ext_property1
    left join wx_t_point_value_detail t3 on t1.id = t3.POINT_ACCOUNT_ID
    inner join wx_t_promotion_point t5 on t5.point_code = t3.POINT_TYPE
    where t1.DELETE_FLAG != 'true'
    and t1.POINT_ACCOUNT_TYPE = 'SP'
    and t3.DELETE_flag = 0
    and t5.point_type = #{params.pointType}
    and t2.partner_id in (
    select temp.partner_id from
    (select
    o.id as partner_id,
    tt4.region,
    case when exists (select 1 from wx_t_promotion_delivery_detail e inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
    where e2.partner_id = o.id) then 1 else 0 end as promotion_flag
    from wx_t_point_account tt1
    inner join wx_t_point_value_detail tt2 on tt1.id = tt2.POINT_ACCOUNT_ID
    inner join  wx_t_organization o on tt1.POINT_ACCOUNT_OWNER_ID  = o.id
    inner join wx_t_partner_o2o_enterprise tt3 on tt3.partner_id = (case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id = o.id) else o.id end)
    inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) tt4
    on tt4.distributor_id = tt3.distributor_id
    where o.status=1
    and tt2.DELETE_FLAG = 0
    and tt1.POINT_ACCOUNT_TYPE = 'SP'
    group by o.id,tt4.region
    union
    select o.partner_id,
    tt4.region,
    case when exists (select 1 from wx_t_promotion_delivery_detail e
    inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
    where e2.partner_id = o.partner_id) then 1 else 0 end as promotion_flag
    from wx_t_promotion_gift_pool o
    inner join wx_t_partner_o2o_enterprise tt3 on tt3.partner_id = o.partner_id
    inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) tt4
    on tt4.distributor_id = tt3.distributor_id
    where 1 = 1
    group by o.partner_id,tt4.region
    ) temp
    )
  &lt;!&ndash;  <choose>
      <when test="params.partnerIds!=null and params.partnerIds.size > 0">
        and t2.partner_id in
        <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
          #{listItem}
        </foreach>
      </when>
      <otherwise>
        and 1!=1
      </otherwise>
    </choose>&ndash;&gt;
    /*and EXISTS (select 1 from wx_t_promotion_delivery_detail e1 where t2.distributor_id = cast(e1.ext_property7 as bigint))*/
    group by t4.id,t4.organization_name,t5.point_type,t4.type,r1o1.organization_name
    union all
    select 'gift' as [type],t1.partner_id,
    <if test="params.byRetailer == null or params.byRetailer == false">
      (case when t2.type=3 then r1o1.organization_name + ' - ' else '' end) +
    </if>
    t2.organization_name as partner_name, sum(available_quantity) as balance, t1.application_type as point_type,
    case when t2.type=3 then 1 else 0 end retailer_flag from wx_t_promotion_gift_pool t1
    inner join wx_t_organization t2 on t1.partner_id = t2.id
    left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=t2.id
    left join wx_t_organization r1o1 on t2.type=3 and r1o1.id=pe1.ext_property1
    where t2.status = 1
    and t1.application_type = #{params.pointType}

    and t1.partner_id in(
    select temp.partner_id from
    (select
    o.id as partner_id,
    tt4.region,
    case when exists (select 1 from wx_t_promotion_delivery_detail e inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
    where e2.partner_id = o.id) then 1 else 0 end as promotion_flag
    from wx_t_point_account tt1
    inner join wx_t_point_value_detail tt2 on tt1.id = tt2.POINT_ACCOUNT_ID
    inner join  wx_t_organization o on tt1.POINT_ACCOUNT_OWNER_ID  = o.id
    inner join wx_t_partner_o2o_enterprise tt3 on tt3.partner_id = (case when o.type=3 then (select r1pe1.ext_property1 from wx_t_partner_o2o_enterprise r1pe1 where r1pe1.partner_id = o.id) else o.id end)
    inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) tt4
    on tt4.distributor_id = tt3.distributor_id
    where o.status=1
    and tt2.DELETE_FLAG = 0
    and tt1.POINT_ACCOUNT_TYPE = 'SP'
    group by o.id,tt4.region
    union
    select o.partner_id,
    tt4.region,
    case when exists (select 1 from wx_t_promotion_delivery_detail e
    inner join wx_t_partner_o2o_enterprise e2 on cast(e.ext_property7 as bigint) = e2.distributor_id
    where e2.partner_id = o.partner_id) then 1 else 0 end as promotion_flag
    from wx_t_promotion_gift_pool o
    inner join wx_t_partner_o2o_enterprise tt3 on tt3.partner_id = o.partner_id
    inner join (select distributor_id,region from PP_MID.dbo.syn_dw_to_pp_customer_org_sales where product_channel is not null group by distributor_id,region) tt4
    on tt4.distributor_id = tt3.distributor_id
    where 1 = 1
    group by o.partner_id,tt4.region
    ) temp
    )
&lt;!&ndash;    <choose>
      <when test="params.partnerIds!=null and params.partnerIds.size > 0">
        and t1.partner_id in
        <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
          #{listItem}
        </foreach>
      </when>
      <otherwise>
        and 1!=1
      </otherwise>
    </choose>&ndash;&gt;
    group by t1.partner_id,t2.organization_name ,t1.application_type,t2.type,r1o1.organization_name
    )t
      where t.point_type = #{params.pointType} and t.balance>0
    group by  t.partner_id,t.partner_name, point_type

  </select>-->

  <select id="getAllPromotionPartnerInfo" resultType="map">
    select partner_id as partnerId,partner_name,sum(balance) as balance, point_type from
    (select 'point' as [type],t4.id as partner_id,
    t4.organization_name as partner_name,
    sum(t3.POINT_VALUE-t3.POINT_PAYED) as balance,t5.point_type,
    case when t4.type=3 then 1 else 0 end retailer_flag from
    wx_t_point_account t1
    inner join wx_t_partner_o2o_enterprise t2 on t1.POINT_ACCOUNT_OWNER_ID = t2.partner_id
    left join wx_t_organization t4 on t2.partner_id  = t4.id
    left join wx_t_organization r1o1 on t4.type=3 and r1o1.id=t2.ext_property1
    left join wx_t_point_value_detail t3 on t1.id = t3.POINT_ACCOUNT_ID
    inner join wx_t_promotion_point t5 on t5.point_code = t3.POINT_TYPE
    where t1.DELETE_FLAG != 'true'
    and t1.POINT_ACCOUNT_TYPE = 'SP'
    and t3.DELETE_flag = 0
    and t5.point_type = #{params.pointType}
      <choose>
        <when test="params.partnerIds!=null and params.partnerIds.size > 0">
          and t2.partner_id in
          <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
            #{listItem}
          </foreach>
        </when>
        <otherwise>
          and 1!=1
        </otherwise>
      </choose>
    /*and EXISTS (select 1 from wx_t_promotion_delivery_detail e1 where t2.distributor_id = cast(e1.ext_property7 as bigint))*/
    group by t4.id,t4.organization_name,t5.point_type,t4.type,r1o1.organization_name
    union all
    select 'gift' as [type],t1.partner_id,
    <if test="params.byRetailer == null or params.byRetailer == false">
      (case when t2.type=3 then r1o1.organization_name + ' - ' else '' end) +
    </if>
    t2.organization_name as partner_name, sum(available_quantity) as balance, t1.application_type as point_type,
    case when t2.type=3 then 1 else 0 end retailer_flag from wx_t_promotion_gift_pool t1
    inner join wx_t_organization t2 on t1.partner_id = t2.id
    left join wx_t_partner_o2o_enterprise pe1 on pe1.partner_id=t2.id
    left join wx_t_organization r1o1 on t2.type=3 and r1o1.id=pe1.ext_property1
    where t2.status = 1
    and t1.application_type = #{params.pointType}
        <choose>
          <when test="params.partnerIds!=null and params.partnerIds.size > 0">
            and t1.partner_id in
            <foreach collection="params.partnerIds" item="listItem" open="(" close=")" separator="," >
              #{listItem}
            </foreach>
          </when>
          <otherwise>
            and 1!=1
          </otherwise>
        </choose>
    group by t1.partner_id,t2.organization_name ,t1.application_type,t2.type,r1o1.organization_name
    )t
    where t.point_type = #{params.pointType} and (t.balance > 0 or t.balance &lt; 0)
    group by  t.partner_id,t.partner_name, point_type

  </select>
</mapper>
