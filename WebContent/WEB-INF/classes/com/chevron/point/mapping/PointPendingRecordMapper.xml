<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.point.dao.PointPendingRecordMapper">
  <resultMap id="BaseResultMap" type="com.chevron.point.model.PointPendingRecord">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="partner_id" jdbcType="BIGINT" property="partnerId" />
    <result column="partner_name" jdbcType="NVARCHAR" property="partnerName" />
    <result column="bi_sellin_line_id" jdbcType="BIGINT" property="biSellinLineId" />
    <result column="pending_score" jdbcType="NUMERIC" property="pendingScore" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="point_business_id" jdbcType="BIGINT" property="pointBusinessId" />
    <result column="COMMENTS" jdbcType="NVARCHAR" property="comments" />
    <result column="DELETE_FLAG" jdbcType="BIT" property="deleteFlag" />
    <result column="ATTRIBUTE1" jdbcType="NVARCHAR" property="attribute1" />
    <result column="ATTRIBUTE2" jdbcType="NVARCHAR" property="attribute2" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
    <result column="LAST_UPDATE_TIME" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
    <result column="LITERS" jdbcType="BIGINT" property="liters" />
    <result column="SKU" jdbcType="NVARCHAR" property="sku" />
    <result column="TRANS_TIME" jdbcType="TIMESTAMP" property="transTime" />
    <result column="sales_channel_name" jdbcType="NVARCHAR" property="salesChannelName" />
  </resultMap>
  <resultMap id="SummaryResultMap" type="com.chevron.point.model.PointPendingRecordSummary">
    <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
    <result column="organization_name" jdbcType="NVARCHAR" property="organizationName" />
    <result column="total_point" jdbcType="BIGINT" property="totalPoint" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, partner_id, partner_name, bi_sellin_line_id, pending_score, status, point_business_id, sales_channel_name,
    COMMENTS, DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME,
    LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" parameterType="com.chevron.point.model.PointPendingRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_point_pending_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.point.model.PointPendingRecordExample">
    delete from wx_t_point_pending_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.point.model.PointPendingRecord">
    insert into wx_t_point_pending_record (id, partner_id, partner_name, 
      bi_sellin_line_id, pending_score, status,
      point_business_id, sales_channel_name, COMMENTS, DELETE_FLAG,
      ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, 
      CREATED_BY, LAST_UPDATE_TIME, LAST_UPDATED_BY
      )
    values (#{id,jdbcType=BIGINT}, #{partnerId,jdbcType=BIGINT}, #{partnerName,jdbcType=NVARCHAR}, 
      #{biSellinLineId,jdbcType=BIGINT}, #{pendingScore,jdbcType=NUMERIC}, #{status,jdbcType=INTEGER}, 
      #{pointBusinessId,jdbcType=BIGINT}, #{salesChannelName,jdbcType=NVARCHAR}, #{comments,jdbcType=NVARCHAR}, #{deleteFlag,jdbcType=BIT},
      #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.point.model.PointPendingRecord" useGeneratedKeys="true" keyProperty="id">
    insert into wx_t_point_pending_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partnerId != null">
        partner_id,
      </if>
      <if test="partnerName != null">
        partner_name,
      </if>
      <if test="biSellinLineId != null">
        bi_sellin_line_id,
      </if>
      <if test="pendingScore != null">
        pending_score,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="pointBusinessId != null">
        point_business_id,
      </if>
      <if test="sales_channel_name != null">
        sales_channel_name,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="deleteFlag != null">
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null">
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null">
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null">
        CREATION_TIME,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null">
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="partnerName != null">
        #{partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="biSellinLineId != null">
        #{biSellinLineId,jdbcType=BIGINT},
      </if>
      <if test="pendingScore != null">
        #{pendingScore,jdbcType=NUMERIC},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="pointBusinessId != null">
        #{pointBusinessId,jdbcType=BIGINT},
      </if>
      <if test="salesChannelName != null">
        #{salesChannelName,jdbcType=NVARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update wx_t_point_pending_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null">
        partner_id = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerName != null">
        partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      </if>
      <if test="record.biSellinLineId != null">
        bi_sellin_line_id = #{record.biSellinLineId,jdbcType=BIGINT},
      </if>
      <if test="record.pendingScore != null">
        pending_score = #{record.pendingScore,jdbcType=NUMERIC},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.pointBusinessId != null">
        point_business_id = #{record.pointBusinessId,jdbcType=BIGINT},
      </if>
      <if test="record.salesChannelName != null">
        sales_channel_name = #{salesChannelName,jdbcType=NVARCHAR},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null">
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null">
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null">
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null">
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wx_t_point_pending_record
    set id = #{record.id,jdbcType=BIGINT},
      partner_id = #{record.partnerId,jdbcType=BIGINT},
      partner_name = #{record.partnerName,jdbcType=NVARCHAR},
      bi_sellin_line_id = #{record.biSellinLineId,jdbcType=BIGINT},
      pending_score = #{record.pendingScore,jdbcType=NUMERIC},
      status = #{record.status,jdbcType=INTEGER},
      point_business_id = #{record.pointBusinessId,jdbcType=BIGINT},
      sales_channel_name = #{salesChannelName,jdbcType=NVARCHAR},
      COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertPendingRecordFromDw" parameterType="map">
    insert into dbo.wx_t_point_pending_record (distributor_id,partner_id,partner_name,bi_sellin_line_id,pending_score,status,sales_channel_name,
      creation_time,
      created_by,
      last_update_time,
      last_updated_by,
      liters,
      product_sku,
      trans_time)
    SELECT
        distributor_id,
		partner_id,
		partner_name,
		bi_sellin_line_id,
		pending_score,
		status,
		sales_channel_name,
		creation_time,
		created_by,
		last_update_time,
		last_updated_by,
		liters,
		product_sku,
		trans_time
		FROM
		(
          select
            dw.distributor_id,
            o.id AS partner_id,
            o.organization_name AS partner_name,
            dw.base_trans_sell_in_id AS bi_sellin_line_id,
            sum( round( dw.abp_rmb, 0 )) AS pending_score,
            0 AS status,
            dw.product_channel sales_channel_name,
            getdate() AS creation_time,
            #{creator} AS created_by,
            getdate() AS last_update_time,
            #{updator} AS last_updated_by,
            sum( round( dw.liters, 0 )) AS liters,
            dw.product_code_sap product_sku,
            dw.trans_time
          from PP_MID.dbo.syn_dw_to_pp_abp dw
			INNER JOIN dbo.wx_t_partner_o2o_enterprise en on
			      en.distributor_id = dw.distributor_id
			INNER JOIN dbo.wx_t_organization o ON
			      o.id = isnull(dw.partner_id,en.partner_id)
          WHERE
            1 = 1
            AND (dw.trans_time &gt;= #{startDate} and trans_time &lt; #{endDate})
            AND dw.product_channel = #{salesChannelName}
            and dw.del_flag=0
            AND not EXISTS(
                select
                  1
                from
                  dbo.wx_t_point_pending_record re
                where
                  re.trans_time = dw.trans_time
                  AND re.distributor_id = dw.distributor_id
                  AND re.product_sku = dw.product_code_sap
                  AND re.sales_channel_name = dw.product_channel
                  AND re.bi_sellin_line_id = dw.base_trans_sell_in_id
            )
            group by
            dw.distributor_id,
            o.id,
            o.organization_name,
            dw.product_code_sap,
            dw.product_channel,
            dw.trans_time,
            dw.base_trans_sell_in_id
      ) total_pending_info
	  WHERE total_pending_info.pending_score != 0
		  ORDER BY
			total_pending_info.partner_name,
			total_pending_info.product_sku
  </insert>
  <select id="selectPendingPointsFromDw" parameterType="map" resultMap="BaseResultMap">
    SELECT
      partner_id,
      partner_name,
      bi_sellin_line_id,
      pending_score,
      status,
      sales_channel_name,
      creation_time,
      created_by,
      last_update_time,
      last_updated_by,
      liters,
      sku,
      trans_time
    FROM
      (
      select
      o.id AS partner_id,
      dw.customer_name_cn AS partner_name,
      0 AS bi_sellin_line_id,
      sum( round( dw.points_rmb, 0 )) AS pending_score,
      0 AS status,
      dw.sales_channel_name,
      getdate() AS creation_time,
      0 AS created_by,
      getdate() AS last_update_time,
      0 AS last_updated_by,
      sum( round( dw.liters, 0 )) AS liters,
      dw.product_sku as sku,
      dw.trans_time
      from
      dbo.dw_base_trans_sell_in dw
      left join wx_t_organization o on
      o.organization_name = dw.customer_name_cn
      WHERE
      1 = 1
      AND dw.sales_channel_name = #{salesChannelName}
      AND ( dw.trans_time &gt;= #{startDate}
      and trans_time &lt;  #{endDate} )
      group by
      o.id,
      dw.customer_name_cn,
      dw.product_sku,
      dw.sales_channel_name,
      dw.trans_time ) total_pending_info
    WHERE
      1 = 1
      and total_pending_info.pending_score != 0
    ORDER BY
      total_pending_info.partner_name
  </select>
  <select id="selectByPagenation" parameterType="com.chevron.point.model.PointPendingRecordParams" resultMap="BaseResultMap">
    SELECT r.id,o.id as partner_id,r.partner_name,round(r.pending_score,0) as pending_score,r.status,r.trans_time,r.product_sku as sku,round(r.liters,0) as liters, r.sales_channel_name
    FROM wx_t_point_pending_record r
    LEFT JOIN wx_t_organization o ON r.partner_id=o.id
    where 1=1
    and (r.status=0)
    <if test="salesChannelName != null">
      and r.sales_channel_name=#{salesChannelName}
    </if>
	<if test="partnerId != null">
	and r.partner_id=#{partnerId}
	</if>
	<if test="key != null">
	and (r.partner_name like '%'+#{key}+'%'
	or si.product_sku like '%'+#{key}+'%')
	</if>
  </select>
  
   <select id="selectSummaryByPagenation" parameterType="com.chevron.point.model.PointPendingRecordParams" resultMap="SummaryResultMap">
  	SELECT o.id as organization_id,r.partner_name as organization_name,sum(round(r.pending_score,0)) as total_point
	FROM wx_t_point_pending_record r
	LEFT JOIN wx_t_organization o ON r.partner_id=o.id
	where 1=1
	and (r.status=0)
    <if test="salesChannelName != null">
       and r.sales_channel_name=#{salesChannelName}
    </if>
	<if test="partnerId != null">
	and r.partner_id=#{partnerId}
	</if>
	<if test="key != null">
	and (r.partner_name like '%'+#{key}+'%')
	</if>
	group by r.partner_name,o.id,r.partner_name
  </select>

  <select id="countPendingPoint" parameterType="java.lang.Long" resultType="java.lang.Double">
	SELECT
	sum( pe.pending_score )
	FROM
	dbo.wx_t_point_pending_record pe
	WHERE
	pe.DELETE_FLAG = 0
	and pe.status = 0
	<if test="partnerId != null">
	and pe.partner_id=#{partnerId}
	</if>
    <if test="salesChannelName != null">
    and pe.sales_channel_name=#{salesChannelName}
    </if>
  </select>
</mapper>