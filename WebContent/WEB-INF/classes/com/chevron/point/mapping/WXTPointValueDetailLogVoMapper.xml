<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.point.dao.WXTPointValueDetailLogVoMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.point.model.WXTPointValueDetailLogVo" >
    <result column="POINT_VALUE_ID" property="pointValueId" jdbcType="BIGINT" />
    <result column="MODIFIED_VALUE" property="modifiedValue" jdbcType="NUMERIC" />
    <result column="POINT_ACCOUNT_ID" property="pointAccountId" jdbcType="BIGINT" />
    <result column="BUSINESS_ID" property="businessId" jdbcType="BIGINT" />
    <result column="POINT_RULE_ID" property="pointRuleId" jdbcType="BIGINT" />
    <result column="COMMENTS" property="comments" jdbcType="NVARCHAR" />
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="BIT" />
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="NVARCHAR" />
    <result column="ATTRIBUTE2" property="attribute2" jdbcType="NVARCHAR" />
    <result column="CREATION_TIME" property="creationTime" jdbcType="TIMESTAMP" />
    <result column="CREATED_BY" property="createdBy" jdbcType="BIGINT" />
    <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP" />
    <result column="LAST_UPDATED_BY" property="lastUpdatedBy" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    POINT_VALUE_ID, MODIFIED_VALUE, POINT_ACCOUNT_ID, BUSINESS_ID, POINT_RULE_ID, COMMENTS, 
    DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
    LAST_UPDATED_BY
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.point.model.WXTPointValueDetailLogVoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_point_value_detail_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chevron.point.model.WXTPointValueDetailLogVoExample" >
    delete from wx_t_point_value_detail_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chevron.point.model.WXTPointValueDetailLogVo" >
    insert into wx_t_point_value_detail_log (POINT_VALUE_ID, MODIFIED_VALUE, POINT_ACCOUNT_ID, 
      BUSINESS_ID, POINT_RULE_ID, COMMENTS, 
      DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, 
      CREATION_TIME, CREATED_BY, LAST_UPDATE_TIME, 
      LAST_UPDATED_BY)
    values (#{pointValueId,jdbcType=BIGINT}, #{modifiedValue,jdbcType=NUMERIC}, #{pointAccountId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{pointRuleId,jdbcType=BIGINT}, #{comments,jdbcType=NVARCHAR}, 
      #{deleteFlag,jdbcType=BIT}, #{attribute1,jdbcType=NVARCHAR}, #{attribute2,jdbcType=NVARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chevron.point.model.WXTPointValueDetailLogVo" >
    insert into wx_t_point_value_detail_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="pointValueId != null" >
        POINT_VALUE_ID,
      </if>
      <if test="modifiedValue != null" >
        MODIFIED_VALUE,
      </if>
      <if test="pointAccountId != null" >
        POINT_ACCOUNT_ID,
      </if>
      <if test="businessId != null" >
        BUSINESS_ID,
      </if>
      <if test="pointRuleId != null" >
        POINT_RULE_ID,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="attribute2 != null" >
        ATTRIBUTE2,
      </if>
      <if test="creationTime != null" >
        CREATION_TIME,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="lastUpdateTime != null" >
        LAST_UPDATE_TIME,
      </if>
      <if test="lastUpdatedBy != null" >
        LAST_UPDATED_BY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="pointValueId != null" >
        #{pointValueId,jdbcType=BIGINT},
      </if>
      <if test="modifiedValue != null" >
        #{modifiedValue,jdbcType=NUMERIC},
      </if>
      <if test="pointAccountId != null" >
        #{pointAccountId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null" >
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="pointRuleId != null" >
        #{pointRuleId,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=NVARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateTime != null" >
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update wx_t_point_value_detail_log
    <set >
      <if test="record.pointValueId != null" >
        POINT_VALUE_ID = #{record.pointValueId,jdbcType=BIGINT},
      </if>
      <if test="record.modifiedValue != null" >
        MODIFIED_VALUE = #{record.modifiedValue,jdbcType=NUMERIC},
      </if>
      <if test="record.pointAccountId != null" >
        POINT_ACCOUNT_ID = #{record.pointAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null" >
        BUSINESS_ID = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.pointRuleId != null" >
        POINT_RULE_ID = #{record.pointRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.comments != null" >
        COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      </if>
      <if test="record.deleteFlag != null" >
        DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      </if>
      <if test="record.attribute1 != null" >
        ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null" >
        CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.lastUpdateTime != null" >
        LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update wx_t_point_value_detail_log
    set POINT_VALUE_ID = #{record.pointValueId,jdbcType=BIGINT},
      MODIFIED_VALUE = #{record.modifiedValue,jdbcType=NUMERIC},
      POINT_ACCOUNT_ID = #{record.pointAccountId,jdbcType=BIGINT},
      BUSINESS_ID = #{record.businessId,jdbcType=BIGINT},
      POINT_RULE_ID = #{record.pointRuleId,jdbcType=BIGINT},
      COMMENTS = #{record.comments,jdbcType=NVARCHAR},
      DELETE_FLAG = #{record.deleteFlag,jdbcType=BIT},
      ATTRIBUTE1 = #{record.attribute1,jdbcType=NVARCHAR},
      ATTRIBUTE2 = #{record.attribute2,jdbcType=NVARCHAR},
      CREATION_TIME = #{record.creationTime,jdbcType=TIMESTAMP},
      CREATED_BY = #{record.createdBy,jdbcType=BIGINT},
      LAST_UPDATE_TIME = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      LAST_UPDATED_BY = #{record.lastUpdatedBy,jdbcType=BIGINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="insertBatch" parameterType="map" >
    insert into wx_t_point_value_detail_log (POINT_VALUE_ID, MODIFIED_VALUE, POINT_ACCOUNT_ID, 
      BUSINESS_ID, COMMENTS, 
      DELETE_FLAG, ATTRIBUTE1, ATTRIBUTE2, 
      CREATION_TIME, CREATED_BY) values 
		<foreach collection="records" index="index" item="item" separator=",">
			<trim prefix="(" suffix=")" suffixOverrides=",">
    (select pvd.id from wx_t_point_value_detail pvd left join wx_t_point_business pb1 on pb1.id=pvd.BUSINESS_ID 
    where pb1.BUSINESS_TYPE_CODE='${item.businessVo.businessTypeCode}' and pb1.RELATED_CODE='${item.businessVo.relatedCode}'), 
    #{item.modifiedValue,jdbcType=NUMERIC}, (select ID from wx_t_point_account where POINT_ACCOUNT_TYPE='${item.accountVo.pointAccountType}' and POINT_ACCOUNT_OWNER_ID=${item.accountVo.pointAccountOwnerId}), 
      (select id from wx_t_point_business 
	where BUSINESS_TYPE_CODE='${item.businessVo.businessTypeCode}' and RELATED_CODE='${item.businessVo.relatedCode}'), #{item.comments,jdbcType=NVARCHAR}, 
      #{item.deleteFlag,jdbcType=BIT}, #{item.attribute1,jdbcType=NVARCHAR}, #{item.attribute2,jdbcType=NVARCHAR}, 
      #{item.creationTime,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT}
			</trim>
		</foreach>
  </insert>
</mapper>