<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.DwPpCustomerFundQuarterNewMapper">
    <resultMap id="BaseResultMap" type="com.chevron.elites2.model.DwPpCustomerFundQuarterNew">
        <result column="Year" jdbcType="INTEGER" property="year"/>
        <result column="Quarter" jdbcType="VARCHAR" property="quarter"/>
        <result column="Customer_Name_CN" jdbcType="NVARCHAR" property="customerNameCn"/>
        <result column="Distributor_id" jdbcType="BIGINT" property="distributorId"/>
        <result column="Customer_Grade" jdbcType="VARCHAR" property="customerGrade"/>
        <result column="Total_Volume" jdbcType="NUMERIC" property="totalVolume"/>
        <result column="Total_Fund" jdbcType="NUMERIC" property="totalFund"/>
        <result column="CIO_BD_Fund" jdbcType="NUMERIC" property="cioBdFund"/>
        <result column="CDM_BD_Fund" jdbcType="NUMERIC" property="cdmBdFund"/>
        <result column="CIO_Marketing_Fund" jdbcType="NUMERIC" property="cioMarketingFund"/>
        <result column="CDM_Marketing_Fund" jdbcType="NUMERIC" property="cdmMarketingFund"/>
        <result column="CIO_IVI_Fund" jdbcType="NUMERIC" property="cioIviFund"/>
        <result column="CDM_IVI_Fund" jdbcType="NUMERIC" property="cdmIviFund"/>
        <result column="actual_CIO_BD_Fund" jdbcType="NUMERIC" property="actualCioBdFund"/>
        <result column="actual_CDM_BD_Fund" jdbcType="NUMERIC" property="actualCdmBdFund"/>
        <result column="actual_CIO_Marketing_Fund" jdbcType="NUMERIC" property="actualCioMarketingFund"/>
        <result column="actual_CDM_Marketing_Fund" jdbcType="NUMERIC" property="actualCdmMarketingFund"/>
        <result column="actual_CIO_IVI_Fund" jdbcType="NUMERIC" property="actualCioIviFund"/>
        <result column="actual_CDM_IVI_Fund" jdbcType="NUMERIC" property="actualCdmIviFund"/>
        <result column="Is_Elite_Customer" jdbcType="INTEGER" property="isEliteCustomer"/>
    </resultMap>

    <resultMap id="DwFundSumResultMap" type="com.chevron.elites2.model.vo.DwFundSumVo">
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="quarter" jdbcType="NVARCHAR" property="quarter"/>
        <result column="bd_fund_cio" jdbcType="DECIMAL" property="bdFundCIO"/>
        <result column="mkt_fund_cio" jdbcType="DECIMAL" property="mktFundCIO"/>
        <result column="bd_fund_cdm" jdbcType="DECIMAL" property="bdFundCDM"/>
        <result column="mkt_fund_cdm" jdbcType="DECIMAL" property="mktFundCDM"/>
        <result column="ivi_fund_cdm" jdbcType="DECIMAL" property="iviFundCDM"/>
        <result column="ivi_fund_cio" jdbcType="DECIMAL" property="iviFundCIO"/>
    </resultMap>

    <sql id="Base_Column_List">
    Year, Quarter, Customer_Name_CN, Distributor_id, Customer_Grade, Total_Volume, Total_Fund, 
    CIO_BD_Fund, CDM_BD_Fund, CIO_Marketing_Fund, CDM_Marketing_Fund, CIO_IVI_Fund, CDM_IVI_Fund, 
    actual_CIO_BD_Fund, actual_CDM_BD_Fund, actual_CIO_Marketing_Fund, actual_CDM_Marketing_Fund, 
    actual_CIO_IVI_Fund, actual_CDM_IVI_Fund,Is_Elite_Customer
  </sql>

    <select id="getCustomerDistributorIdList" resultType="java.lang.Long">
	SELECT DISTINCT Distributor_id
	from dw_pp_customer_fund_quarter_new_view t1
	WHERE Is_Elite_Customer=1
	AND t1.Distributor_id IN (
        SELECT t2.distributor_id FROM wx_t_partner_o2o_enterprise t2 INNER JOIN wx_t_organization t3 ON
        t2.partner_id=t3.id WHERE t3.status=1 )
</select>


    <select id="getEarnFundByOrgQuarter" resultMap="DwFundSumResultMap">
        SELECT t1.Distributor_id AS org_id,[Quarter],
        ISNULL(sum(actual_CDM_BD_Fund), 0) as bd_fund_cdm,
        ISNULL(sum(actual_CDM_Marketing_Fund), 0) as mkt_fund_cdm,
        ISNULL(sum(actual_CDM_IVI_Fund), 0) as ivi_fund_cdm,
        ISNULL(sum(actual_CIO_BD_Fund), 0) as bd_fund_cio,
        ISNULL(sum(actual_CIO_Marketing_Fund), 0) as mkt_fund_cio,
        ISNULL(sum(actual_CIO_IVI_Fund), 0) as ivi_fund_cio
        FROM dw_pp_customer_fund_quarter_new_view t1
        WHERE [Year]= #{year, jdbcType=INTEGER}
        AND t1.Distributor_id IN (
        SELECT t2.distributor_id FROM wx_t_partner_o2o_enterprise t2 INNER JOIN wx_t_organization t3 ON
        t2.partner_id=t3.id WHERE t3.status=1 )
        <if test="quarters != null">
            AND [Quarter] IN
            <foreach collection="quarters" item="quarter" index="index" open="(" close=")" separator=",">
                #{quarter}
            </foreach>
        </if>
        <if test="orgIdList != null">
            AND t1.Distributor_id in
            <foreach collection="orgIdList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        GROUP BY t1.Distributor_id,Quarter
    </select>
</mapper>