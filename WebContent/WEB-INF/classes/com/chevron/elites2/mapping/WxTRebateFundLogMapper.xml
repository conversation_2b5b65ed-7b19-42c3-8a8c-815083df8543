<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.WxTRebateFundLogMapper">
  <resultMap id="BaseResultMap" type="com.chevron.elites2.model.WxTRebateFundLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_id" jdbcType="BIGINT" property="applyId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="fund_type" jdbcType="INTEGER" property="fundType" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="used_fund" jdbcType="DECIMAL" property="usedFund" />
    <result column="used_fund_ivi" jdbcType="DECIMAL" property="usedFundIvi" />
    <result column="used_fund_q1" jdbcType="DECIMAL" property="usedFundQ1" />
    <result column="used_fund_ivi_q1" jdbcType="DECIMAL" property="usedFundIviQ1" />
    <result column="used_fund_q234" jdbcType="DECIMAL" property="usedFundQ234" />
    <result column="used_fund_ivi_q234" jdbcType="DECIMAL" property="usedFundIviQ234" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="org_name" jdbcType="NVARCHAR" property="orgName" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>

  <sql id="Base_Column_List">
    id, apply_id, org_id, fund_type, channel, used_fund, used_fund_ivi, used_fund_q1, 
    used_fund_q234, company_code, org_name, created_by, created_time, status,used_fund_ivi_q1,used_fund_ivi_q234
  </sql>

</mapper>