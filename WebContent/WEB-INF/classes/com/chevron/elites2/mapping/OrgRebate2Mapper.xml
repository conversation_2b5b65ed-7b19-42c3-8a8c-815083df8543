<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.OrgRebate2Mapper">
<resultMap id="BaseResultMap" type="com.chevron.elites2.model.vo.OrgRebate">
	<result column="id" jdbcType="BIGINT" property="orgId" />
	<result column="organization_name" jdbcType="NVARCHAR" property="orgName" />
	<result column="organization_en_name" jdbcType="NVARCHAR" property="orgEnName" />
	<result column="dic_item_name" jdbcType="NVARCHAR" property="specialApply" />
</resultMap>

<select id="getOrgRebateList" resultMap="BaseResultMap" parameterType="java.lang.Long">
	SELECT crss.customer_name_cn organization_name, crss.distributor_id id
	FROM view_customer_region_sales_supervisor_rel crss
	LEFT JOIN wx_t_user u ON u.cai = crss.sales_cai
	<where>
	<if test="userId != null and userId != 1">
	   u.user_id = #{userId, jdbcType=BIGINT}
	</if>
	</where>
	ORDER BY u.user_id,crss.customer_name_cn
</select>

<select id="getRegionNameByOrgId" resultType="java.lang.String">
	SELECT DISTINCT region_name FROM dw_customer_region_sales_supervisor_rel
	WHERE distributor_id = #{distributorId, jdbcType = BIGINT}
	AND region_name like CONCAT(#{salesChannel, jdbcType=NVARCHAR},'%')
</select>

<select id="getOrgIdByRegionName" resultType="java.lang.Long">
	SELECT crss.distributor_id id
	FROM dw_customer_region_sales_supervisor_rel crss
	WHERE crss.region_name = #{regionName, jdbcType=NVARCHAR}
</select>

<select id="getOrgIdByChannelManagerCai" resultMap="BaseResultMap">
	SELECT crss.distributor_id id
	FROM dw_customer_region_sales_supervisor_rel crss
	LEFT JOIN dw_region_sales_channel_rel rsc ON rsc.region_name = crss.region_name
	WHERE rsc.channel_manager_cai = #{channelManagerCai, jdbcType=NVARCHAR}
</select>

<select id="getOrgNameById" resultMap="BaseResultMap">
	SELECT DISTINCT crss.customer_name_cn organization_name, crss.customer_name_en organization_en_name
	FROM dw_customer_region_sales_supervisor_rel crss
	WHERE distributor_id = #{distributorId, jdbcType = BIGINT}
</select>

</mapper>