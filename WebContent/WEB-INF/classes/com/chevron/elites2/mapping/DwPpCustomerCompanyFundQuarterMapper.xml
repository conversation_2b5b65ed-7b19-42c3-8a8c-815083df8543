<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.DwPpCustomerCompanyFundQuarterMapper">
    <resultMap id="BaseResultMap" type="com.chevron.elites2.model.DwPpCustomerCompanyFundQuarter">
        <result column="Year" jdbcType="INTEGER" property="year"/>
        <result column="Quarter" jdbcType="VARCHAR" property="quarter"/>
        <result column="Customer_Name_CN" jdbcType="NVARCHAR" property="customerNameCn"/>
        <result column="Distributor_id" jdbcType="BIGINT" property="distributorId"/>
        <result column="sales_channel_name" jdbcType="NVARCHAR" property="salesChannelName"/>
        <result column="company_code" jdbcType="NVARCHAR" property="companyCode"/>
        <result column="Total_Volume" jdbcType="NUMERIC" property="totalVolume"/>
        <result column="Total_Fund" jdbcType="NUMERIC" property="totalFund"/>
        <result column="BD_Fund" jdbcType="NUMERIC" property="bdFund"/>
        <result column="Marketing_Fund" jdbcType="NUMERIC" property="marketingFund"/>
        <result column="IVI_Fund" jdbcType="NUMERIC" property="iviFund"/>
        <result column="BD_Fund_Total" jdbcType="NUMERIC" property="bdFundTotal"/>
        <result column="Marketing_Fund_Total" jdbcType="NUMERIC" property="marketingFundTotal"/>
        <result column="IVI_Fund_Total" jdbcType="NUMERIC" property="iviFundTotal"/>
        <result column="BD_Fund_Rate" jdbcType="NUMERIC" property="bdFundRate"/>
        <result column="Marketing_Fund_Rate" jdbcType="NUMERIC" property="marketingFundRate"/>
        <result column="IVI_Fund_Rate" jdbcType="NUMERIC" property="iviFundRate"/>
    </resultMap>

    <sql id="Base_Column_List">
    [Year], Quarter, Customer_Name_CN, Distributor_id, sales_channel_name, company_code,
    Total_Volume, Total_Fund, BD_Fund, Marketing_Fund, IVI_Fund
  </sql>

    <select id="getFundDetailListByOrgId" resultMap="BaseResultMap">
        SELECT t1.Distributor_id,t1.Customer_Name_CN,t1.company_code,
(t1.BD_Fund) AS BD_Fund, ABS(t1.Marketing_Fund) AS Marketing_Fund,
t2.BD_Fund_total,t2.Marketing_Fund_Total,t2.IVI_Fund_Total,
(IIF( t2.BD_Fund_Total>0 ,CAST ((ABS(t1.BD_Fund)/t2.BD_Fund_Total) AS Decimal(20,4)) , 0)) as BD_Fund_Rate,
(IIF( t2.Marketing_Fund_Total>0 ,CAST ((ABS(t1.Marketing_Fund)/t2.Marketing_Fund_Total) AS Decimal(20,4)) , 0 )) as Marketing_Fund_Rate,
(IIF( t2.IVI_Fund_Total>0 ,CAST ((ABS(t1.IVI_Fund)/t2.IVI_Fund_Total) AS Decimal(20,4)) , 0)) as IVI_Fund_Rate
FROM dw_pp_customer_company_fund_quarter t1
left join (SELECT
	Distributor_id,
	ABS(SUM( BD_Fund )) AS BD_Fund_total,
	ABS(SUM( Marketing_Fund )) AS Marketing_Fund_Total,
	ABS(SUM( IVI_Fund )) AS IVI_Fund_Total
FROM
	dw_pp_customer_company_fund_quarter
<where>
        <if test="salesChannel != null and salesChannel!=''">
             sales_channel_name = #{salesChannel, jdbcType=NVARCHAR}
        </if>
        <if test="quarters != null">
            AND [Quarter] IN
            <foreach collection="quarters" item="quarter" index="index" open="(" close=")" separator=",">
                #{quarter}
            </foreach>
        </if>
</where>
GROUP BY Distributor_id) t2
ON t1.Distributor_id=t2.Distributor_id
where t1.Distributor_id=#{orgId, jdbcType=BIGINT}
        <if test="salesChannel != null and salesChannel!=''">
            AND t1.sales_channel_name = #{salesChannel, jdbcType=NVARCHAR}
        </if>
        <if test="quarters != null">
            AND t1.[Quarter] IN
            <foreach collection="quarters" item="quarter" index="index" open="(" close=")" separator=",">
                #{quarter}
            </foreach>
        </if>
    </select>

    <select id="getFundSumListByOrgId" resultMap="BaseResultMap">
        SELECT
        Distributor_id,Customer_Name_CN,
        ABS(SUM( BD_Fund )) AS BD_Fund,
        ABS(SUM( Marketing_Fund )) AS Marketing_Fund,
        ABS(SUM( IVI_Fund )) AS IVI_Fund,
        ABS(SUM( Total_Fund )) AS Total_Fund
        FROM
        dw_pp_customer_company_fund_quarter t1
        <where>
            sales_channel_name=#{salesChannel,jdbcType=NVARCHAR}
            AND [Year]=#{year,jdbcType=INTEGER}
            <if test="quarters != null">
                AND [Quarter] IN
                <foreach collection="quarters" item="quarter" index="index" open="(" close=")" separator=",">
                    #{quarter}
                </foreach>
            </if>
            <if test="orgIdList != null">
                AND t1.Distributor_id in
                <foreach collection="orgIdList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY
        Distributor_id,Customer_Name_CN
    </select>

    <select id="getCompanyCodeList" resultMap="BaseResultMap">
        SELECT DISTINCT company_code,Distributor_id,Customer_Name_CN FROM dw_pp_customer_company_fund_quarter
        <where>
            <if test='orgId != null'>
                Distributor_id=#{orgId,jdbcType=BIGINT}
            </if>
            <if test="salesChannel != null and salesChannel!=''">
                AND sales_channel_name = #{salesChannel, jdbcType=NVARCHAR}
            </if>
        </where>
    </select>
</mapper>