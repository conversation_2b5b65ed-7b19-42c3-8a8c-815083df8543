<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.WxTRebateApply2Mapper">
    <resultMap id="BaseResultMap" type="com.chevron.elites2.model.WxTRebateApply2">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="organization_name" jdbcType="NVARCHAR" property="organizationName"/>
        <result column="apply_fund_cdm" jdbcType="DECIMAL" property="applyFundCdm"/>
        <result column="apply_fund_cio" jdbcType="DECIMAL" property="applyFundCio"/>
        <result column="total_fund_cdm" jdbcType="DECIMAL" property="totalFundCdm"/>
        <result column="total_fund_cio" jdbcType="DECIMAL" property="totalFundCio"/>
        <result column="used_fund_cdm" jdbcType="DECIMAL" property="usedFundCdm"/>
        <result column="used_fund_cio" jdbcType="DECIMAL" property="usedFundCio"/>
        <result column="remain_fund_cdm" jdbcType="DECIMAL" property="remainFundCdm"/>
        <result column="remain_fund_cio" jdbcType="DECIMAL" property="remainFundCio"/>
        <result column="fund_type" jdbcType="NVARCHAR" property="fundType"/>
        <result column="fund_ownership" jdbcType="NVARCHAR" property="fundOwnership"/>
        <result column="activity_type" jdbcType="NVARCHAR" property="activityType"/>
        <result column="activity_desc" jdbcType="NVARCHAR" property="activityDesc"/>
        <result column="billboard_type" jdbcType="NVARCHAR" property="billboardType"/>
        <result column="status" jdbcType="NVARCHAR" property="status"/>
        <result column="apply_person_name" jdbcType="NVARCHAR" property="applyPersonName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="customer_interaction_desc" jdbcType="NVARCHAR" property="customerInteractionDesc"/>
        <result column="invoice_amount_total" jdbcType="DECIMAL" property="invoiceAmountTotal"/>
        <result column="process" jdbcType="INTEGER" property="process"/>
        <result column="export_remark" jdbcType="NVARCHAR" property="exportRemark"/>
        <result column="descirption" jdbcType="NVARCHAR" property="descirption"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="activity_theme" jdbcType="NVARCHAR" property="activityTheme"/>
        <result column="customer_number" jdbcType="INTEGER" property="customerNumber"/>
        <result column="billboard_content" jdbcType="NVARCHAR" property="billboardContent"/>
        <result column="device_detail" jdbcType="NVARCHAR" property="deviceDetail"/>
        <result column="promote_product" jdbcType="NVARCHAR" property="promoteProduct"/>
        <result column="promote_type" jdbcType="NVARCHAR" property="promoteType"/>
        <result column="promote_number" jdbcType="INTEGER" property="promoteNumber"/>
        <result column="start_date_activity" jdbcType="TIMESTAMP" property="startDateActivity"/>
        <result column="end_date_activity" jdbcType="TIMESTAMP" property="endDateActivity"/>
        <result column="start_date_billboard" jdbcType="TIMESTAMP" property="startDateBillboard"/>
        <result column="end_date_billboard" jdbcType="TIMESTAMP" property="endDateBillboard"/>
        <result column="start_date_promote" jdbcType="TIMESTAMP" property="startDatePromote"/>
        <result column="end_date_promote" jdbcType="TIMESTAMP" property="endDatePromote"/>
        <result column="current_step" jdbcType="INTEGER" property="currentStep"/>
        <result column="apply_fund" jdbcType="DECIMAL" property="applyFund"/>
        <result column="apply_fund_plan" jdbcType="DECIMAL" property="applyFundPlan"/>
        <result column="use_ivi_fund" jdbcType="DECIMAL" property="useIviFund"/>
        <result column="use_invoice_amount" jdbcType="INTEGER" property="useInvoiceAmount"/>
        <result column="remain_fund" jdbcType="DECIMAL" property="remainFund"/>
        <result column="used_fund" jdbcType="DECIMAL" property="usedFund"/>
        <result column="total_fund" jdbcType="DECIMAL" property="totalFund"/>
        <result column="remain_fund_ivi" jdbcType="DECIMAL" property="remainFundIvi"/>
        <result column="used_fund_ivi" jdbcType="DECIMAL" property="usedFundIvi"/>
        <result column="total_fund_ivi" jdbcType="DECIMAL" property="totalFundIvi"/>
        <result column="invoice_redundant" jdbcType="DECIMAL" property="invoiceRedundant"/>
        <result column="remain_ivi_fund" jdbcType="DECIMAL" property="remainIviFund"/>
        <result column="has_rent_invoice" jdbcType="INTEGER" property="hasRentInvoice"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.chevron.elites2.model.WxTRebateApply21">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="organization_name" jdbcType="NVARCHAR" property="organizationName"/>
        <result column="apply_fund_cdm" jdbcType="DECIMAL" property="applyFundCdm"/>
        <result column="apply_fund_cio" jdbcType="DECIMAL" property="applyFundCio"/>
        <result column="total_fund_cdm" jdbcType="DECIMAL" property="totalFundCdm"/>
        <result column="total_fund_cio" jdbcType="DECIMAL" property="totalFundCio"/>
        <result column="used_fund_cdm" jdbcType="DECIMAL" property="usedFundCdm"/>
        <result column="used_fund_cio" jdbcType="DECIMAL" property="usedFundCio"/>
        <result column="remain_fund_cdm" jdbcType="DECIMAL" property="remainFundCdm"/>
        <result column="remain_fund_cio" jdbcType="DECIMAL" property="remainFundCio"/>
        <result column="fund_type" jdbcType="NVARCHAR" property="fundType"/>
        <result column="fund_ownership" jdbcType="NVARCHAR" property="fundOwnership"/>
        <result column="activity_type" jdbcType="NVARCHAR" property="activityType"/>
        <result column="activity_desc" jdbcType="NVARCHAR" property="activityDesc"/>
        <result column="billboard_type" jdbcType="NVARCHAR" property="billboardType"/>
        <result column="status" jdbcType="NVARCHAR" property="status"/>
        <result column="apply_person_name" jdbcType="NVARCHAR" property="applyPersonName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="customer_interaction_desc" jdbcType="NVARCHAR" property="customerInteractionDesc"/>
        <result column="invoice_amount_total" jdbcType="DECIMAL" property="invoiceAmountTotal"/>
        <result column="process" jdbcType="INTEGER" property="process"/>
        <result column="export_remark" jdbcType="NVARCHAR" property="exportRemark"/>
        <result column="descirption" jdbcType="NVARCHAR" property="descirption"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="activity_theme" jdbcType="NVARCHAR" property="activityTheme"/>
        <result column="customer_number" jdbcType="INTEGER" property="customerNumber"/>
        <result column="billboard_content" jdbcType="NVARCHAR" property="billboardContent"/>
        <result column="device_detail" jdbcType="NVARCHAR" property="deviceDetail"/>
        <result column="promote_product" jdbcType="NVARCHAR" property="promoteProduct"/>
        <result column="promote_type" jdbcType="NVARCHAR" property="promoteType"/>
        <result column="promote_number" jdbcType="INTEGER" property="promoteNumber"/>
        <result column="start_date_activity" jdbcType="TIMESTAMP" property="startDateActivity"/>
        <result column="end_date_activity" jdbcType="TIMESTAMP" property="endDateActivity"/>
        <result column="start_date_billboard" jdbcType="TIMESTAMP" property="startDateBillboard"/>
        <result column="end_date_billboard" jdbcType="TIMESTAMP" property="endDateBillboard"/>
        <result column="start_date_promote" jdbcType="TIMESTAMP" property="startDatePromote"/>
        <result column="end_date_promote" jdbcType="TIMESTAMP" property="endDatePromote"/>
        <result column="current_step" jdbcType="INTEGER" property="currentStep"/>
        <result column="apply_fund" jdbcType="DECIMAL" property="applyFund"/>
        <result column="apply_fund_plan" jdbcType="DECIMAL" property="applyFundPlan"/>
        <result column="use_ivi_fund" jdbcType="DECIMAL" property="useIviFund"/>
        <result column="use_invoice_amount" jdbcType="INTEGER" property="useInvoiceAmount"/>
        <result column="remain_fund" jdbcType="DECIMAL" property="remainFund"/>
        <result column="used_fund" jdbcType="DECIMAL" property="usedFund"/>
        <result column="total_fund" jdbcType="DECIMAL" property="totalFund"/>
        <result column="remain_fund_ivi" jdbcType="DECIMAL" property="remainFundIvi"/>
        <result column="used_fund_ivi" jdbcType="DECIMAL" property="usedFundIvi"/>
        <result column="total_fund_ivi" jdbcType="DECIMAL" property="totalFundIvi"/>
        <result column="invoice_redundant" jdbcType="DECIMAL" property="invoiceRedundant"/>
        <result column="remain_ivi_fund" jdbcType="DECIMAL" property="remainIviFund"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="region_name" jdbcType="VARCHAR" property="regionName"/>
        <result column="sales_name" jdbcType="VARCHAR" property="salesName"/>
        <result column="current_operator_name" jdbcType="VARCHAR" property="currentOperatorName"/>
        <result column="next_operator_name" jdbcType="VARCHAR" property="nextOperatorName"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, organization_id, organization_name, apply_fund_cdm, apply_fund_cio, total_fund_cdm,
    total_fund_cio, used_fund_cdm, used_fund_cio, remain_fund_cdm, remain_fund_cio, fund_type,
    fund_ownership, activity_type, activity_desc, billboard_type, status, apply_person_name,
    create_time, create_by, update_time, update_by, customer_interaction_desc, invoice_amount_total,
    process, export_remark, descirption, start_date, end_date, activity_theme, customer_number,
    billboard_content, device_detail, promote_product, promote_type, promote_number,
    start_date_activity, end_date_activity, start_date_billboard, end_date_billboard,
    start_date_promote, end_date_promote, current_step, apply_fund, use_ivi_fund, use_invoice_amount,
    remain_fund,remain_ivi_fund, used_fund, total_fund,remain_fund_ivi,used_fund_ivi,total_fund_ivi,
    invoice_redundant,apply_fund_plan
  </sql>

    <select id="getRebateApplyByPage" parameterType="com.chevron.elites2.dto.request.RebateApplyPageRequest"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_t_rebate_apply_v2 ra
        <where>
            <if test='searchType == "sales" and type == "1" '>
                status in ('0','1','3')
                and create_by = #{userId, jdbcType=BIGINT}
            </if>
            <if test='searchType == "sales" and type == "2" '>
                status+0 >=2 AND status NOT IN ('3')
                and create_by = #{userId, jdbcType=BIGINT}
            </if>
            <if test='searchType == "flsrAudit" and type=="1"'>
                status+0 >= 1 AND current_step=3
                and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                and operator = #{userId, jdbcType=BIGINT}
                and status ='-1')
            </if>
            <if test='searchType == "flsrAudit" and type=="2"'>
                status+0 >= 1
                and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                and operator = #{userId, jdbcType=BIGINT}
                and status ='1')
            </if>
            <if test='searchType == "flsrAudit,asmAudit" and type=="1"'>
                <if test="step==null">
                    (status+0 >= 1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                    and operator = #{userId, jdbcType=BIGINT}
                    and status ='-1')) OR
                    ((status+0=1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')) OR (status+0 >= 6 AND current_step=4
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')
                    ))
                </if>
                <if test="step!=null and step==1">
                    (status+0 >= 1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                    and operator = #{userId, jdbcType=BIGINT}
                    and status ='-1')) OR
                    ((status+0=1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')) )
                </if>
                <if test="step!=null and step==4">
                    (status+0 >= 1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 4
                    and operator = #{userId, jdbcType=BIGINT}
                    and status ='-1')) OR
                    ( (status+0 >= 6 AND current_step=4
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')
                    ))
                </if>
            </if>

            <if test='searchType == "flsrAudit,asmAudit" and type=="2"'>
                status+0 > 1
                and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                and operator = #{userId, jdbcType=BIGINT}
                and status = '1')
            </if>

            <if test='searchType == "partner" and type == "1" '>
                status IN('2','4') AND current_step IN (2,3) AND ra.id in (select DISTINCT rebate_id from
                wx_t_rebate_flow_v2 where
                step = 3
                and operator = #{userId, jdbcType=BIGINT}
                and status ='-1')
            </if>
            <if test='searchType == "partner" and type == "2" '>
                status+0 >=5 AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2
                where step = 3
                and operator = #{userId, jdbcType=BIGINT}
                and status ='1')
            </if>
            <if test='(searchType == "areaAudit" or searchType == "asmAudit") and type == "1" '>
                <if test="step==null">
                    ((status+0=1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')) OR (status+0 >= 6 AND current_step=4
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')
                    ))
                </if>
                <if test="step!=null and step==1">
                    (status+0=1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1'))
                </if>
                <if test="step!=null and step==4">
                    (status+0 >= 6 AND current_step=4
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                    and operator = #{userId, jdbcType=BIGINT}
                    and status!='1')
                    )
                </if>
            </if>
            <if test='(searchType == "areaAudit" or searchType == "asmAudit") and type == "2" '>
                <if test="step!=null and step==1">
                    status+0 > 1
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 2
                    and operator = #{userId, jdbcType=BIGINT}
                    and status = '1')
                </if>
                <if test="step!=null and step==4">
                    (status+0 >= 8
                    and ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 5
                    and operator = #{userId, jdbcType=BIGINT}
                    and status='1')
                    )
                </if>
            </if>

            <if test='searchType == "channelAudit" and (type=="1") '>
                status+0 >= 8 AND current_step=5
                AND create_by in (SELECT DISTINCT u.user_id
                FROM dw_customer_region_sales_supervisor_rel crss
                LEFT JOIN wx_t_user u ON crss.sales_cai = u.cai
                WHERE sales_cai != suppervisor_cai AND u.status=1 and u.user_id NOT IN (
                SELECT user_id FROM dw_customer_region_sales_supervisor_rel crss LEFT JOIN wx_t_user u ON
                crss.suppervisor_cai = u.cai WHERE sales_cai = suppervisor_cai
                AND crss.region_name in (select region_name from dw_region_sales_channel_rel where sales_channel_name=
                #{salesChannel, jdbcType=NVARCHAR})))
                AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 6
                and operator = #{userId, jdbcType=BIGINT})
            </if>
            <if test='searchType == "channelAudit" and type=="2"'>
                status+0 >= 10
                AND create_by in (SELECT DISTINCT u.user_id
                FROM dw_customer_region_sales_supervisor_rel crss
                LEFT JOIN wx_t_user u ON crss.sales_cai = u.cai
                WHERE sales_cai != suppervisor_cai AND u.status=1 and u.user_id NOT IN (
                SELECT user_id FROM dw_customer_region_sales_supervisor_rel crss LEFT JOIN wx_t_user u ON
                crss.suppervisor_cai = u.cai WHERE sales_cai = suppervisor_cai
                AND crss.region_name in (select region_name from dw_region_sales_channel_rel where
                sales_channel_name=#{salesChannel, jdbcType=NVARCHAR})))
                AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where step = 6
                and operator = #{userId, jdbcType=BIGINT})
            </if>

            <if test='searchType == "channelAudit" and (tabSearchType == "1") '>
                status IN ('1')
                AND create_by in (SELECT DISTINCT u.user_id
                FROM dw_customer_region_sales_supervisor_rel crss
                LEFT JOIN wx_t_user u ON crss.suppervisor_cai = u.cai
                WHERE sales_cai = suppervisor_cai)
            </if>
            <if test='searchType == "channelAudit" and (tabSearchType == "2")'>
                status IN ('1')
                AND create_by in (SELECT DISTINCT u.user_id
                FROM dw_customer_region_sales_supervisor_rel crss
                LEFT JOIN wx_t_user u ON crss.suppervisor_cai = u.cai
                WHERE sales_cai = suppervisor_cai)
            </if>
            <if test='(searchType == "bsNorth" or searchType == "bsSouth" or searchType == "bsSW") and type == "1" '>
                status in ('10', '11') AND current_step=6
                AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where  operator = #{userId, jdbcType=BIGINT}
                and status ='-1')
            </if>
            <if test='(searchType == "bsNorth" or searchType == "bsSouth" or searchType == "bsSW") and type == "2" '>
                status+0 >= 12 AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where  operator = #{userId, jdbcType=BIGINT}
                and status ='1')
            </if>
            <if test='(searchType == "bsNorth" or searchType == "bsSouth" or searchType == "bsSW") and type == "3" '>
                status = '12' AND ra.id in (select DISTINCT rebate_id from wx_t_rebate_flow_v2 where  operator = #{userId, jdbcType=BIGINT}
                and status ='1')
            </if>
            <!--<if test='searchType == "bsNorth" '>
                AND organization_id IN (
                SELECT distributor_id FROM dw_customer_region_sales_supervisor_rel
                WHERE region_name IN ('C&amp;I-North' , 'C&amp;I-NE&amp;NW','CDM-North','CDM-NE&amp;NW','CDM-East')
                )
            </if>
            <if test='searchType == "bsSouth" '>
                AND organization_id IN (
                SELECT distributor_id FROM dw_customer_region_sales_supervisor_rel
                WHERE region_name = 'C&amp;I-South')
            </if>
            <if test='searchType == "bsSW" '>
                AND organization_id IN (
                SELECT distributor_id FROM dw_customer_region_sales_supervisor_rel
                WHERE region_name = 'C&amp;I-SW')
            </if>-->

            <if test="orgName != null and orgName != '' ">
                AND organization_name like CONCAT(CONCAT('%', #{orgName, jdbcType=NVARCHAR}),'%')
            </if>
            <if test="salesChannel != null and salesChannel != '' and salesChannel != '-1' ">
                AND fund_ownership=#{salesChannel, jdbcType=NVARCHAR}
            </if>
            <if test="fundType != null and fundType != '' and fundType !='-1' ">
                AND fund_type=#{fundType, jdbcType=NVARCHAR}
            </if>
            <if test="activityType != null and activityType != '' and activityType !='-1' ">
                AND activity_type=#{activityType, jdbcType=NVARCHAR}
            </if>
        </where>
    </select>

    <select id="getRebateAppyGroupByCreateBy" resultType="java.lang.Long">
	select ra.create_by
	from wx_t_rebate_apply_v2 ra
	LEFT JOIN wx_t_rebate_related rr on ra.id = rr.rebate_id
	where rr.audit_id = #{auditId, jdbcType=BIGINT}
	GROUP BY create_by
</select>

    <select id="getRebateApplyGroupByOrgId" resultType="java.lang.Long">
	select ra.organization_id
	from wx_t_rebate_apply_v2 ra
	LEFT JOIN wx_t_rebate_related rr on ra.id = rr.rebate_id
	where rr.audit_id = #{auditId, jdbcType=BIGINT}
	GROUP BY ra.organization_id
</select>

    <select id="getSumUsedFund" resultType="java.math.BigDecimal">
        select isnull(sum(ram.used_fund_cdm + ram.used_fund_cio), 0)
        from wx_t_rebate_apply_v2 ram
        <where>
            ram.process = #{offlineProcess, jdbcType=NVARCHAR} AND ram.organization_id = #{orgId, jdbcType=BIGINT}
        </where>
    </select>

    <select id="getSumApplyFund" resultType="java.math.BigDecimal">
        select sum(ram.apply_fund_cdm + ram.apply_fund_cio)
        from wx_t_rebate_apply_v2 ram
        left join (
        select ra.* from wx_t_rebate_apply_v2 ra
        left join wx_t_rebate_related rr on ra.id = rr.rebate_id
        where rr.audit_id = #{auditId, jdbcType=BIGINT} and organization_id = #{orgId, jdbcType=BIGINT}) a on ram.id =
        a.id
        <where>ram.process is null
            and ram.status = '14'
            and ram.organization_id = #{orgId, jdbcType=BIGINT}
        </where>
    </select>

    <select id="getExportRemarkByOrgId" resultType="java.lang.String">
	select export_remark
	from wx_t_rebate_apply_v2
	where organization_id = #{orgId, jdbcType=BIGINT}
</select>

    <select id="selectApplyForExport" parameterType="map" resultMap="BaseResultMap2">
        select ap.id, crss.customer_name_cn organization_name, crss.region_name, crss.sales_name, ap.apply_fund,
        (select top 1 rh.operation_time from wx_t_rebate_history_v2 rh where rh.rebate_apply_id=ap.id order by rh.step,
        rh.operation_time) apply_time,
        ap.fund_type, ap.status,
        (select u1.ch_name from wx_t_rebate_flow_v2 fn left join wx_t_user u1 on u1.user_id=fn.operator where
        ap.id=fn.rebate_id and fn.step=ap.current_step) current_operator_name,
        (select top 1 u1.ch_name from wx_t_rebate_flow_v2 fn left join wx_t_user u1 on u1.user_id=fn.operator where
        ap.id=fn.rebate_id and fn.step>ap.current_step order by fn.step) next_operator_name
        from wx_t_rebate_apply_v2 ap
        left join dw_customer_region_sales_supervisor_rel crss on crss.distributor_id=ap.organization_id
        left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
        where ap.fund_ownership=rsc.sales_channel_name
        <if test="salesChannel != null">
            and rsc.sales_channel_name=#{salesChannel}
        </if>
        <if test="orgIds != null">
            and ap.organization_id in
            <foreach collection="orgIds" index="index" item="item" separator="," open="(" close=")">
                ${item}
            </foreach>
        </if>
    </select>

    <select id="selectRebateForExport" parameterType="map" resultMap="BaseResultMap2">
        select crss.distributor_id organization_id, crss.customer_name_cn organization_name, crss.region_name,
        crss.sales_name,
        rsc.sales_channel_name fund_ownership
        from dw_customer_region_sales_supervisor_rel crss
        left join dw_region_sales_channel_rel rsc on rsc.region_name=crss.region_name
        where 1=1
        <if test="salesChannel != null">
            and rsc.sales_channel_name=#{salesChannel}
        </if>
        <if test="orgIds != null">
            and crss.distributor_id in
            <foreach collection="orgIds" index="index" item="item" separator="," open="(" close=")">
                ${item}
            </foreach>
        </if>
    </select>

    <select id="getRebateApplyByIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from wx_t_rebate_apply_v2 ra
        <where>
            <if test="ids != null">
                ra.id in
                <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                    ${item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getRebateApplyByRelated" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wx_t_rebate_apply_v2 ra
        WHERE ra.id IN (select DISTINCT rebate_id
        from wx_t_rebate_related_v2)
    </select>
</mapper>