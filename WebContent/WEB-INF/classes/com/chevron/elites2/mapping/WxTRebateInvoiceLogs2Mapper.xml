<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.elites2.dao.WxTRebateInvoiceLogs2Mapper">
    <resultMap id="BaseResultMap" type="com.chevron.elites2.model.WxTRebateInvoiceLogs2">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount"/>
        <result column="invoice_used" jdbcType="DECIMAL" property="invoiceUsed"/>
        <result column="invoice_redundant" jdbcType="DECIMAL" property="invoiceRedundant"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="rebate_apply_id" jdbcType="BIGINT" property="rebateApplyId"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="activity_type" jdbcType="NVARCHAR" property="activityType" />
        <result column="channel" jdbcType="NVARCHAR" property="channel" />
        <result column="channel" jdbcType="NVARCHAR" property="channel"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, user_id, invoice_amount, balance, rebate_apply_id, created_by, created_at,activity_type,invoice_redundant,invoice_used
  </sql>
    <select id="getOrgInvoiceList" resultMap="BaseResultMap">
        select t1.*,t2.organization_id AS org_id,t2.fund_ownership as channel
        from wx_t_rebate_invoice_logs_v2 t1 left join wx_t_rebate_apply_v2 t2
        on t1.rebate_apply_id=t2.id
        <where>
            <if test="orgId != null and orgId != ''">
                t2.organization_id = #{orgId, jdbcType=BIGINT}
            </if>
            <if test="salesChannel != null and salesChannel != ''">
                t2.fund_ownership = #{salesChannel, jdbcType=NVARCHAR}
            </if>
        </where>
    </select>


    <select id="getOrgInvoiceBalance" resultType="java.lang.String">
        SELECT SUM(invoice_amount) FROM wx_t_rebate_invoice_logs_v2 t2
        <where>
            <if test="orgId != null and orgId != ''">
                t2.org_id = #{orgId, jdbcType=BIGINT}
            </if>
            <if test="salesChannel != null and salesChannel != ''">
                t2.channel = #{salesChannel, jdbcType=NVARCHAR}
            </if>
        </where>
    </select>

    <select id="getRemainInvoiceAmount" resultType="java.math.BigDecimal">
        SELECT SUM(invoice_amount-invoice_used) as remain FROM wx_t_rebate_invoice_logs_v2 t2
        <where>
            <if test="orgId != null and orgId != ''">
                t2.org_id = #{orgId, jdbcType=BIGINT}
            </if>
            <if test="salesChannel != null and salesChannel != ''">
               AND t2.channel = #{salesChannel, jdbcType=NVARCHAR}
            </if>
            <if test="activityType != null and activityType != ''">
               AND t2.activity_type = #{activityType, jdbcType=NVARCHAR}
            </if>
        </where>
    </select>
</mapper>