<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chevron.oilsampleapply.dao.OilSampleApplyMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.chevron.oilsampleapply.model.OilSampleApply">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="applicant" property="applicant" jdbcType="VARCHAR"/>
		<result column="applicant_date" property="applicantDate" jdbcType="TIMESTAMP"/>
		<result column="department" property="department" jdbcType="VARCHAR"/>
		<result column="warehouse" property="warehouse" jdbcType="VARCHAR"/>
		<result column="glNo" property="glno" jdbcType="VARCHAR"/>
		<result column="customerId" property="customerid" jdbcType="BIGINT"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
		<result column="responsible_csr" property="responsibleCsr" jdbcType="VARCHAR"/>
		<result column="ship_to_code" property="shipToCode" jdbcType="VARCHAR"/>
		<result column="delivery_dress" property="deliveryDress" jdbcType="VARCHAR"/>
		<result column="applay_oil_reason" property="applayOilReason" jdbcType="VARCHAR"/>
		<result column="customer_sap_code" property="customerSapCode" jdbcType="VARCHAR"/>
		<result column="cost_center" property="costCenter" jdbcType="VARCHAR"/>
		<result column="request_no" property="requestNo" jdbcType="VARCHAR"/>
		<result column="request_comment" property="requestComment" jdbcType="VARCHAR"/>
		<result column="form_status" property="formStatus" jdbcType="INTEGER"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
		<result column="company_code" property="companyCode" jdbcType="VARCHAR"/>
		<result column="contact_address" property="contactAddress" jdbcType="VARCHAR"/>
		<result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
		<result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
		<result column="delivery_type" property="deliveryType" jdbcType="INTEGER"/>
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC"/>
		<result column="online_amount" property="onlineAmount" jdbcType="NUMERIC"/>
		<result column="spark_amount" property="sparkAmount" jdbcType="NUMERIC"/>
        <result column="testing_content" property="testingContent" jdbcType="VARCHAR"/>
        <result column="test_result" property="testResult" jdbcType="INTEGER"/>
        <result column="after_testing_content" property="afterTestingContent" jdbcType="VARCHAR"/>
		<result column="brand" property="brand" jdbcType="INTEGER"/>
		<result column="region" property="region" jdbcType="VARCHAR"/>
		<result column="trial_status" property="trialStatus" jdbcType="INTEGER"/>
		<result column="trial_status_text" property="trialStatusText" jdbcType="VARCHAR"/>
		<result column="abm_submit_time" property="abmSubmitTime" jdbcType="TIMESTAMP"/>
		<result column="executor" property="executor" jdbcType="BIGINT"/>
		<result column="retailer_id" property="retailerId" jdbcType="BIGINT"/>
		<result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
		<association property="workflowInstance" column="form_key" resultMap="com.sys.workflow.dao.WorkflowInstanceMapper.BaseResultMap"/>
	</resultMap>
	<resultMap id="userResultMap" type="com.sys.auth.model.WxTUser" >
	<!--
	WARNING - @mbggenerated
	This element is automatically generated by MyBatis Generator, do not modify.
	This element was generated on Wed Jun 03 10:58:07 CST 2015.
	-->
	<id column="user_id" property="userId" jdbcType="BIGINT" />
	<result column="login_name" property="loginName" jdbcType="VARCHAR" />
	<result column="user_no" property="userNo" jdbcType="VARCHAR" />
	<result column="password" property="password" jdbcType="VARCHAR" />
	<result column="salt" property="salt" jdbcType="VARCHAR" />
	<result column="pwd_lasttime" property="pwdLasttime" jdbcType="TIMESTAMP" />
	<result column="ch_name" property="chName" jdbcType="VARCHAR" />
	<result column="cn_name" property="cnName" jdbcType="VARCHAR" />
	<result column="pinyin" property="pinyin" jdbcType="VARCHAR" />
	<result column="allow_login" property="allowLogin" jdbcType="VARCHAR" />
	<result column="sex" property="sex" jdbcType="VARCHAR" />
	<result column="birthday" property="birthday" jdbcType="TIMESTAMP" />
	<result column="address" property="address" jdbcType="VARCHAR" />
	<result column="email" property="email" jdbcType="VARCHAR" />
	<result column="mobile_tel" property="mobileTel" jdbcType="VARCHAR" />
	<result column="fixed_tel" property="fixedTel" jdbcType="VARCHAR" />
	<result column="photo_id" property="photoId" jdbcType="BIGINT" />
	<result column="org_id" property="orgId" jdbcType="BIGINT" />
	<result column="org_type" property="orgType" jdbcType="INTEGER" />
	<result column="org_name"   property="orgName" jdbcType="VARCHAR" />
	<result column="postion"   property="postion" jdbcType="VARCHAR"  />
	<result column="postion_name"   property="postionName" jdbcType="VARCHAR"  />
	<result column="branch_name"   property="branchName" />
	<result column="branch_id"   property="branchId" />
	<result column="user_intime" property="userIntime" jdbcType="TIMESTAMP" />
	<result column="user_outtime" property="userOuttime" jdbcType="TIMESTAMP" />
	<result column="description" property="description" jdbcType="VARCHAR" />
	<result column="device_id" property="deviceId" jdbcType="VARCHAR" />
	<result column="device_type" property="deviceType" jdbcType="VARCHAR" />
	<result column="receive_msg" property="receiveMsg" jdbcType="INTEGER" />
	<result column="post" property="post" jdbcType="BIGINT" />
	<result column="is_valid" property="isValid" jdbcType="VARCHAR" />
	<result column="xz_time" property="xzTime" jdbcType="TIMESTAMP" />
	<result column="xz_user" property="xzUser" jdbcType="VARCHAR" />
	<result column="xg_time" property="xgTime" jdbcType="TIMESTAMP" />
	<result column="xg_user" property="xgUser" jdbcType="VARCHAR" />
	<result column="status" property="status" jdbcType="INTEGER" />
	<result column="tenant_id" property="tenantId" jdbcType="BIGINT" />

	<result column="ch_role_name"   property="chRoleName" />
	<result column="type" property="type" jdbcType="VARCHAR" />
	<result column="change_pwd_permission" property="changePwdPermission" jdbcType="INTEGER" />
	<result column="reset_flag" property="resetFlag" jdbcType="INTEGER" />
	<result column="cai" property="cai" jdbcType="VARCHAR"/>
	<result column="sales_channel" property="salesChannel" jdbcType="VARCHAR"/>
	<!-- 针对C&I marketing region name -->
	<result column="region_name" property="regionName" jdbcType="VARCHAR"/>
	<result column="bu" property="bu" jdbcType="VARCHAR"/>
	<result column="user_model" property="userModel" jdbcType="VARCHAR"/>
	<result column="default_locale" property="defaultLocale" jdbcType="VARCHAR"/>
</resultMap>
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		id,applicant,applicant_date,department,warehouse,glNo,customerId,type,responsible_csr,ship_to_code,delivery_dress,
		applay_oil_reason,customer_sap_code,cost_center,request_no,request_comment,form_status,delete_flag,create_user_id,
		create_time,update_user_id,update_time,testing_content,test_result,after_testing_content,brand,region,update_time,trial_status,abm_submit_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.chevron.oilsampleapply.model.OilSampleApply">
		update wx_t_oil_sample_apply set
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.chevron.oilsampleapply.model.OilSampleApply">
		update wx_t_oil_sample_apply
		<set>
			<if test="applicant != null" >
				applicant = #{applicant,jdbcType=VARCHAR},
			</if>
			<if test="applicantDate != null" >
				applicant_date = #{applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="department != null" >
				department = #{department,jdbcType=VARCHAR},
			</if>
			<if test="warehouse != null" >
				warehouse = #{warehouse,jdbcType=VARCHAR},
			</if>
			<if test="glno != null" >
				glNo = #{glno,jdbcType=VARCHAR},
			</if>
			<if test="customerid != null" >
				customerId = #{customerid,jdbcType=BIGINT},
			</if>
			<if test="type != null" >
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="responsibleCsr != null" >
				responsible_csr = #{responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="shipToCode != null" >
				ship_to_code = #{shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDress != null" >
				delivery_dress = #{deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="applayOilReason != null" >
				applay_oil_reason = #{applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="customerSapCode != null" >
				customer_sap_code = #{customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="costCenter != null" >
				cost_center = #{costCenter,jdbcType=VARCHAR},
			</if>
			<if test="requestNo != null" >
				request_no = #{requestNo,jdbcType=VARCHAR},
			</if>
			<if test="requestComment != null" >
				request_comment = #{requestComment,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null" >
				form_status = #{formStatus,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="companyCode != null" >
				company_code = #{companyCode,jdbcType=VARCHAR},
			</if>
			<if test="contactAddress != null" >
				contact_address = #{contactAddress,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null" >
				contact_person = #{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="contactPhone != null" >
				contact_phone = #{contactPhone,jdbcType=VARCHAR},
			</if>
			<if test="deliveryType != null" >
				delivery_type = #{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="totalAmount != null" >
				total_amount = #{totalAmount,jdbcType=NUMERIC},
			</if>
			<if test="onlineAmount != null" >
				online_amount = #{onlineAmount,jdbcType=NUMERIC},
			</if>
			<if test="sparkAmount != null" >
				spark_amount = #{sparkAmount,jdbcType=NUMERIC},
			</if>
            <if test="testingContent != null" >
                testing_content = #{testingContent,jdbcType=VARCHAR},
            </if>
            <if test="testResult != null" >
                test_result = #{testResult,jdbcType=INTEGER},
            </if>
            <if test="afterTestingContent != null" >
                after_testing_content = #{afterTestingContent,jdbcType=VARCHAR},
            </if>
			<if test="brand != null" >
				brand = #{brand,jdbcType=INTEGER},
			</if>
			<if test="region != null and region != ''">
				region = #{region,jdbcType=VARCHAR},
			</if>
			<if test="trialStatus != null" >
				trial_status = #{trialStatus,jdbcType=INTEGER},
			</if>
			<if test="abmSubmitTime != null" >
				abm_submit_time = #{abmSubmitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="retailerId != null" >
				retailer_id = #{retailerId,jdbcType=BIGINT},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyExample">
    	delete from wx_t_oil_sample_apply
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.chevron.oilsampleapply.model.OilSampleApply" useGeneratedKeys="true" keyProperty="id">
		insert into wx_t_oil_sample_apply
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="applicant != null">
				applicant,
			</if>
			<if test="applicantDate != null">
				applicant_date,
			</if>
			<if test="department != null">
				department,
			</if>
			<if test="warehouse != null">
				warehouse,
			</if>
			<if test="glno != null">
				glNo,
			</if>
			<if test="customerid != null">
				customerId,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="responsibleCsr != null">
				responsible_csr,
			</if>
			<if test="shipToCode != null">
				ship_to_code,
			</if>
			<if test="deliveryDress != null">
				delivery_dress,
			</if>
			<if test="applayOilReason != null">
				applay_oil_reason,
			</if>
			<if test="customerSapCode != null">
				customer_sap_code,
			</if>
			<if test="costCenter != null">
				cost_center,
			</if>
			<if test="requestNo != null">
				request_no,
			</if>
			<if test="requestComment != null">
				request_comment,
			</if>
			<if test="formStatus != null">
				form_status,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="companyCode != null">
				company_code,
			</if>
			<if test="contactAddress != null">
				contact_address,
			</if>
			<if test="contactPerson != null">
				contact_person,
			</if>
			<if test="contactPhone != null">
				contact_phone,
			</if>
			<if test="deliveryType != null">
				delivery_type,
			</if>
			<if test="totalAmount != null" >
				total_amount,
			</if>
			<if test="onlineAmount != null" >
				online_amount,
			</if>
			<if test="sparkAmount != null" >
				spark_amount,
			</if>
            <if test="testingContent != null" >
                testing_content,
            </if>
            <if test="testResult != null" >
                test_result,
            </if>
            <if test="afterTestingContent != null" >
                after_testing_content,
            </if>
			<if test="brand != null">
				brand,
			</if>
			<if test="region != null">
				region,
			</if>
			<if test="trialStatus != null">
				trial_status,
			</if>
			<if test="abmSubmitTime != null">
				abm_submit_time,
			</if>
			<if test="retailerId != null">
				retailer_id,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="applicant != null">
				#{applicant,jdbcType=VARCHAR},
			</if>
			<if test="applicantDate != null">
				#{applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="department != null">
				#{department,jdbcType=VARCHAR},
			</if>
			<if test="warehouse != null">
				#{warehouse,jdbcType=VARCHAR},
			</if>
			<if test="glno != null">
				#{glno,jdbcType=VARCHAR},
			</if>
			<if test="customerid != null">
				#{customerid,jdbcType=BIGINT},
			</if>
			<if test="type != null">
				#{type,jdbcType=VARCHAR},
			</if>
			<if test="responsibleCsr != null">
				#{responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="shipToCode != null">
				#{shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDress != null">
				#{deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="applayOilReason != null">
				#{applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="customerSapCode != null">
				#{customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="costCenter != null">
				#{costCenter,jdbcType=VARCHAR},
			</if>
			<if test="requestNo != null">
				#{requestNo,jdbcType=VARCHAR},
			</if>
			<if test="requestComment != null">
				#{requestComment,jdbcType=VARCHAR},
			</if>
			<if test="formStatus != null">
				#{formStatus,jdbcType=INTEGER},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=VARCHAR},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="companyCode != null">
				#{companyCode,jdbcType=VARCHAR},
			</if>
			<if test="contactAddress != null">
				#{contactAddress,jdbcType=VARCHAR},
			</if>
			<if test="contactPerson != null">
				#{contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="contactPhone != null">
				#{contactPhone,jdbcType=VARCHAR},
			</if>
			<if test="deliveryType != null">
				#{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="totalAmount != null" >
				#{totalAmount,jdbcType=NUMERIC},
			</if>
			<if test="onlineAmount != null" >
				#{onlineAmount,jdbcType=NUMERIC},
			</if>
			<if test="sparkAmount != null" >
				#{sparkAmount,jdbcType=NUMERIC},
			</if>
            <if test="testingContent != null" >
                #{testingContent,jdbcType=VARCHAR},
            </if>
            <if test="testResult != null" >
                #{testResult,jdbcType=INTEGER},
            </if>
            <if test="afterTestingContent != null" >
                #{afterTestingContent,jdbcType=VARCHAR},
            </if>
			<if test="brand != null">
				#{brand,jdbcType=INTEGER},
			</if>
			<if test="region != null">
				#{region,jdbcType=VARCHAR},
			</if>
			<if test="trialStatus != null">
				#{trialStatus,jdbcType=INTEGER},
			</if>
			<if test="abmSubmitTime != null">
				#{abmSubmitTime,jdbcType=TIMESTAMP},
			</if>
			<if test="retailerId != null">
				#{retailerId,jdbcType=BIGINT},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_oil_sample_apply
		<set>
			<if test="record.applicant != null">
				applicant = #{record.applicant,jdbcType=VARCHAR},
			</if>
			<if test="record.applicantDate != null">
				applicant_date = #{record.applicantDate,jdbcType=TIMESTAMP},
			</if>
			<if test="record.department != null">
				department = #{record.department,jdbcType=VARCHAR},
			</if>
			<if test="record.warehouse != null">
				warehouse = #{record.warehouse,jdbcType=VARCHAR},
			</if>
			<if test="record.glno != null">
				glNo = #{record.glno,jdbcType=VARCHAR},
			</if>
			<if test="record.customerid != null">
				customerId = #{record.customerid,jdbcType=BIGINT},
			</if>
			<if test="record.type != null">
				type = #{record.type,jdbcType=VARCHAR},
			</if>
			<if test="record.responsibleCsr != null">
				responsible_csr = #{record.responsibleCsr,jdbcType=VARCHAR},
			</if>
			<if test="record.shipToCode != null">
				ship_to_code = #{record.shipToCode,jdbcType=VARCHAR},
			</if>
			<if test="record.deliveryDress != null">
				delivery_dress = #{record.deliveryDress,jdbcType=VARCHAR},
			</if>
			<if test="record.applayOilReason != null">
				applay_oil_reason = #{record.applayOilReason,jdbcType=VARCHAR},
			</if>
			<if test="record.customerSapCode != null">
				customer_sap_code = #{record.customerSapCode,jdbcType=VARCHAR},
			</if>
			<if test="record.costCenter != null">
				cost_center = #{record.costCenter,jdbcType=VARCHAR},
			</if>
			<if test="record.requestNo != null">
				request_no = #{record.requestNo,jdbcType=VARCHAR},
			</if>
			<if test="record.requestComment != null">
				request_comment = #{record.requestComment,jdbcType=VARCHAR},
			</if>
			<if test="record.formStatus != null">
				form_status = #{record.formStatus,jdbcType=INTEGER},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.companyCode != null">
				company_code = #{record.companyCode,jdbcType=VARCHAR},
			</if>
			<if test="record.contactAddress != null">
				contact_address = #{record.contactAddress,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPerson != null">
				contact_person = #{record.contactPerson,jdbcType=VARCHAR},
			</if>
			<if test="record.contactPhone != null">
				contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
			</if>
			<if test="record.deliveryType != null">
				delivery_type = #{record.deliveryType,jdbcType=INTEGER},
			</if>
			<if test="record.totalAmount != null" >
				total_amount = #{totalAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.onlineAmount != null" >
				online_amount = #{onlineAmount,jdbcType=NUMERIC},
			</if>
			<if test="record.sparkAmount != null" >
				spark_amount = #{sparkAmount,jdbcType=NUMERIC},
			</if>
            <if test="record.testingContent != null" >
                testing_content = #{testingContent,jdbcType=VARCHAR},
            </if>
            <if test="record.testResult != null" >
                test_result = #{testResult,jdbcType=INTEGER},
            </if>
            <if test="record.afterTestingContent != null" >
                after_testing_content = #{afterTestingContent,jdbcType=VARCHAR},
            </if>
			<if test="record.region != null">
				region = #{record.region,jdbcType=VARCHAR},
			</if>
			<if test="record.brand != null" >
				brand = #{record.brand,jdbcType=INTEGER},
			</if>
			<if test="record.trialStatus != null">
				trial_status = #{record.trialStatus,jdbcType=INTEGER},
			</if>
			<if test="record.trialStatus != null">
				trial_status = #{record.trialStatus,jdbcType=INTEGER},
			</if>
			<if test="record.retailerId != null">
				retailer_id = #{record.retailerId,jdbcType=BIGINT},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyExample">
		delete from wx_t_oil_sample_apply
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyExample" resultType="int">
		select count(1) from wx_t_oil_sample_apply
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_oil_sample_apply
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_oil_sample_apply
		where id = #{id,jdbcType=BIGINT}
	</select>
			
			
	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, o1.organization_name distributor_name,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount,t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region,t1.trial_status, t1.abm_submit_time,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text, t1.retailer_id
		  from wx_t_oil_sample_apply t1
		  left join wx_t_organization o1 on o1.id=t1.customerId
		 where t1.delete_flag=0
		 <choose>
		 	<when test="funFlag == 'NotifyFlsrUpdateTestStatus'">
		 	and t1.form_status=22 
		 	and exists (select 1
				from wx_t_dic_item di1 where di1.dic_type_code='Report.month12'
					and (select [date] from (select d.[date],row_number() over(order by d.[date] asc) rn from PP_MID.[dbo].[syn_dw_to_pp_calendar_date] d
				        where d.[date] >=  convert(date, dateadd(month, convert(int,di1.dic_item_code), t1.abm_submit_time)) and d.isworkday_cn=1 ) d1 where d1.rn=1)=convert(date, getdate()))
		 	</when>
		 	<when test="funFlag == 'CloseApplyByAuto'">
		 	and t1.form_status=22 
		 	and t1.abm_submit_time&lt;dateadd(year,-1,getdate())
		 	</when>
		 </choose>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, t1.abm_submit_time,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount,t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region,t1.trial_status, t1.retailer_id,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text
		  from wx_t_oil_sample_apply t1
		 where t1.delete_flag=0
	</select>
	
	<select id="queryDraftForPage" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, t1.abm_submit_time,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount,t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region, o.organization_name distributor_name,wf_wi1.instance_ext_property1,
		  from wx_t_oil_sample_apply t1
		  left join wx_t_organization o on t1.customerId=o.id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryDraftForPage"/>
		 where t1.delete_flag=0
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryDraftForPage"/>
		 <if test="distributorId != null">
		 and t1.distributor_id = #{distributorId,jdbcType=BIGINT}
		 </if>
		 <if test="distributorName != null and distributorName != ''">
		 and o.organization_name like '%' + #{distributorName,jdbcType=VARCHAR} + '%'
		 </if>
		 <if test="dateFrom != null and dateFrom != ''">
		 and t1.create_time > #{dateFrom,jdbcType=VARCHAR}+' 00:00:00'
		 </if>
		 <if test="dateTo != null and dateTo != ''">
		 <![CDATA[ and t1.create_time <= #{dateTo,jdbcType=VARCHAR}+' 23:59:59' ]]>
		 </if>
		<if test="brand != null">
			and t1.brand = #{brand,jdbcType=INTEGER}
		</if>
		<if test="region != null and region != ''">
			and t1.region = #{region,jdbcType=VARCHAR}
		</if>
	</select>
		<select id="queryTodoForPage" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, t1.abm_submit_time,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount,t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region, o.organization_name distributor_name,wf_wi1.instance_ext_property1,t1.trial_status,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text,o1.organization_name retailer_name,
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryTodoForPage"/>
		 from wx_t_oil_sample_apply t1
		  left join wx_t_organization o on t1.customerId=o.id
		left join wx_t_organization o1 on o1.id=t1.retailer_id
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryTodoForPage"/>
		 where t1.delete_flag=0
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryTodoForPage"/>
		 <if test="distributorId != null">
		 and t1.distributor_id = #{distributorId,jdbcType=BIGINT}
		 </if>
		 <if test="distributorName != null and distributorName != ''">
		 and o.organization_name like '%' + #{distributorName,jdbcType=VARCHAR} + '%'
		 </if>
		 <if test="dateFrom != null and dateFrom != ''">
		 and t1.create_time > #{dateFrom,jdbcType=VARCHAR}+' 00:00:00'
		 </if>
		 <if test="dateTo != null and dateTo != ''">
		 <![CDATA[ and t1.create_time <= #{dateTo,jdbcType=VARCHAR}+' 23:59:59' ]]>
		 </if>
		<if test="brand != null">
			and t1.brand = #{brand,jdbcType=INTEGER}
		</if>
		<if test="region != null and region != ''">
			and t1.region = #{region,jdbcType=VARCHAR}
		</if>
	</select>
	<select id="queryDoneForPage" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, t1.abm_submit_time,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount, t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region, o.organization_name distributor_name,wf_wi1.instance_ext_property1,t1.trial_status,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text,o1.organization_name retailer_name,
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryDoneForPage"/>
		  from wx_t_oil_sample_apply t1
		  left join wx_t_organization o on t1.customerId=o.id
		left join wx_t_organization o1 on o1.id=t1.retailer_id
		  <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryDoneForPage"/>
		 where t1.delete_flag=0 
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryDoneForPage"/>
		 <if test="distributorId != null">
		 and t1.distributor_id = #{distributorId,jdbcType=BIGINT}
		 </if>
		 <if test="distributorName != null and distributorName != ''">
		 and o.organization_name like '%' + #{distributorName,jdbcType=VARCHAR} + '%'
		 </if>
		 <if test="dateFrom != null and dateFrom != ''">
		 and t1.create_time > #{dateFrom,jdbcType=VARCHAR}+' 00:00:00'
		 </if>
		 <if test="dateTo != null and dateTo != ''">
		 <![CDATA[ and t1.create_time <= #{dateTo,jdbcType=VARCHAR}+' 23:59:59' ]]>
		 </if>
		<if test="brand != null">
			and t1.brand = #{brand,jdbcType=INTEGER}
		</if>
		<if test="region != null and region != ''">
			and t1.region = #{region,jdbcType=VARCHAR}
		</if>
	</select>
	<select id="selectPartnerDetail" resultMap="BaseResultMap" parameterType="java.lang.Long">
		SELECT
			poe.sap_code customer_sap_code,
			poe.ship_to_code,
			d.dic_item_name cost_center,
			c.company_code
		FROM
			wx_t_organization o
			LEFT JOIN wx_t_partner_o2o_enterprise poe ON o.id = poe.partner_id
			LEFT JOIN [PP_MID].DBO.syn_dw_to_pp_customer c on poe.sap_code = c.customer_code
			LEFT JOIN wx_t_dic_item d on c.company_code = d.dic_item_code and d.dic_type_code = 'oil_sample_apply_costcenter'
		WHERE
			o.id = #{partnerId,jdbcType=BIGINT}
	</select>
	
	<select id="selectDistributorIdByPartnerId" resultType="java.lang.Long">
		select distributor_id from wx_t_partner_o2o_enterprise where partner_id = #{partnerId}
	</select>
	
	<select id="selectDistributorNameByPartnerId" resultType="java.lang.String">
		select o.organization_name from wx_t_partner_o2o_enterprise pe
		left join wx_t_organization o on pe.partner_id=o.id
		where pe.partner_id = #{partnerId}
	</select>
	
	<select id="getWorkflowForm" resultMap="BaseResultMap" parameterType="map">
		select t1.*,o1.organization_name retailer_name,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text,
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_getWorkflowForm"/>
		from wx_t_oil_sample_apply t1
		left join wx_t_organization o1 on o1.id=t1.retailer_id
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_getWorkflowForm"/>
		where t1.id=#{id,jdbcType=BIGINT}
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_getWorkflowForm"/>
	</select>
	
	<select id="queryAllApplyForPage" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.*, o.organization_name distributor_name,o1.organization_name retailer_name,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text, 
			 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryAllForPage"/>
		from wx_t_oil_sample_apply t1
		left join wx_t_organization o on t1.customerId=o.id
		left join wx_t_organization o1 on o1.id=t1.retailer_id
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryAllForPage"/>
		 where t1.form_status != -1
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_cond_queryAllForPage" />
		 <if test="distributorId != null">
		 and t1.distributor_id = #{distributorId,jdbcType=BIGINT}
		 </if>
		 <if test="distributorName != null and distributorName != ''">
		 and o.organization_name like '%' + #{distributorName,jdbcType=VARCHAR} + '%'
		 </if>
		 <if test="dateFrom != null and dateFrom != ''">
		 and t1.create_time > #{dateFrom,jdbcType=VARCHAR}+' 00:00:00'
		 </if>
		 <if test="dateTo != null and dateTo != ''">
		 <![CDATA[ and t1.create_time <= #{dateTo,jdbcType=VARCHAR}+' 23:59:59' ]]>
		 </if>
		<if test="brand != null">
			and t1.brand = #{brand,jdbcType=INTEGER}
		</if>
		<if test="region != null and region != ''">
			and t1.region = #{region,jdbcType=VARCHAR}
		</if>
	</select>
	
	<select id ="selMktSpecialist" resultMap="userResultMap" parameterType="map">
	SELECT
		tuser.*
	FROM
		wx_t_user tuser
		LEFT JOIN wx_t_organization o ON o.id = tuser.org_id 
	WHERE
		tuser.status= 1 
		AND tuser.user_id != 1 
		AND ((
		EXISTS (
	select 1 from dw_access_control_customer_org_sales xx_cos001 
	  left join wx_t_partner_responsible_main xx_prm002 on xx_cos001.region=xx_prm002.region_name and xx_prm002.fun_flag='oil_apply_mkt'
	  left join wx_t_userrole xx_ur001 on xx_ur001.user_id=xx_prm002.user_id
	  left join wx_t_role xx_r001 on xx_r001.role_id=xx_ur001.role_id
	 where xx_cos001.distributor_id=#{distributorId,jdbcType=BIGINT} and xx_prm002.user_id=tuser.user_id and xx_r001.ch_role_name=<![CDATA['Chevron_C&I_Marketing_Region_Manager']]>
		))) 
		AND ( tuser.type IS NULL OR tuser.type != 1 ) 
		
		AND tuser.org_id != 1
	</select>
		<select id="queryTodoList" resultMap="BaseResultMap" parameterType="com.chevron.oilsampleapply.model.OilSampleApplyParams">
		select t1.id, t1.applicant, t1.applicant_date, t1.department, t1.warehouse, t1.glNo, t1.customerId, t1.type,
			 t1.responsible_csr, t1.ship_to_code, t1.delivery_dress, t1.applay_oil_reason, t1.customer_sap_code, t1.cost_center,
			 t1.request_no, t1.request_comment, t1.form_status, t1.delete_flag, t1.create_user_id, t1.create_time, t1.abm_submit_time,
			 t1.update_user_id, t1.update_time,t1.company_code,t1.contact_address,t1.contact_person,t1.contact_phone,t1.delivery_type,t1.total_amount
			 ,t1.online_amount,t1.spark_amount,t1.testing_content,t1.test_result,t1.after_testing_content,t1.brand,t1.region, o.organization_name distributor_name,wf_wi1.instance_ext_property1,t1.trial_status,
			 (select di1.dic_item_name from wx_t_dic_item di1 where di1.dic_type_code='OilSampleApply.trialStatus' and di1.dic_item_code=t1.trial_status) trial_status_text,
			 wf_wiel1.executor,o1.organization_name retailer_name,
		<include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_field_queryTodoForPage"/>
		 from wx_t_oil_sample_apply t1
		  left join wx_t_organization o on t1.customerId=o.id
		left join wx_t_organization o1 on o1.id=t1.retailer_id
		 <include refid="com.sys.workflow.dao.WorkflowInstanceMapper.sql_join_queryTodoForPage"/>
		 where t1.delete_flag=0
		 <if test="distributorId != null">
		 and t1.distributor_id = #{distributorId,jdbcType=BIGINT}
		 </if>
		 <if test="distributorName != null and distributorName != ''">
		 and o.organization_name like '%' + #{distributorName,jdbcType=VARCHAR} + '%'
		 </if>
		 <if test="dateFrom != null and dateFrom != ''">
		 and t1.create_time > #{dateFrom,jdbcType=VARCHAR}+' 00:00:00'
		 </if>
		 <if test="dateTo != null and dateTo != ''">
		 <![CDATA[ and t1.create_time <= #{dateTo,jdbcType=VARCHAR}+' 23:59:59' ]]>
		 </if>
		<if test="brand != null">
			and t1.brand = #{brand,jdbcType=INTEGER}
		</if>
		<if test="region != null and region != ''">
			and t1.region = #{region,jdbcType=VARCHAR}
		</if>
		<if test="formStatus != null and formStatus >= 0">
		and t1.form_status=#{formStatus}
		</if>
		 <choose>
		 	<when test="funFlag == 'CloseApplyByAuto'">
		 	and t1.form_status=22 
		 	and wf_wi1.apply_time&lt;dateadd(year,-1,getdate())
		 	</when>
		 </choose>
		 <if test="ids != null">
			and t1.id in
			<foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		 </if>
	</select>
</mapper>
