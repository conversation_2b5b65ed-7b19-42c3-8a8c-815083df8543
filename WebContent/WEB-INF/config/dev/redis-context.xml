<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
            http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:property-placeholder location="/WEB-INF/config/dev/redis.properties" />

    <bean id="redisSentinelConfiguration"
          class="org.springframework.data.redis.connection.RedisSentinelConfiguration"><!-- 这里是配置哨兵模式 -->
        <constructor-arg name="master" value="mymaster"/>
        <constructor-arg name="sentinelHostAndPorts">
            <set>
               <!-- <value>${redis.sentinel1}</value>-->
                <value>${redis.sentinel2}</value>
                <value>${redis.sentinel3}</value>
            </set>
        </constructor-arg>
    </bean>
    <!-- jedis 配置 -->
    <bean id="poolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="${redis.maxTotal}"/>
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="minIdle" value="${redis.minIdle}"/>
        <property name="maxWaitMillis" value="${redis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}"/>
        <property name="testOnReturn" value="${redis.testOnReturn}"/>
        <property name="testWhileIdle" value="${redis.testWhileIdle}"/>
        <property name="timeBetweenEvictionRunsMillis" value="${redis.timeBetweenEvictionRunsMillis}"/>
        <property name="numTestsPerEvictionRun" value="${redis.numTestsPerEvictionRun}"/>
        <property name="minEvictableIdleTimeMillis" value="${redis.minEvictableIdleTimeMillis}"/>
    </bean>
    <!-- redis服务器中心 -->
    <bean id="connectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <constructor-arg name="sentinelConfig" ref="redisSentinelConfiguration"/>
        <constructor-arg name="poolConfig" ref="poolConfig"/>
        <!--          <property name="poolConfig" ref="poolConfig" />   如果不是哨兵模式，把这行放开，注释掉上面两行的构造方法注入-->
<!--        <property name="port" value="${redis.port}"/>-->
<!--        <property name="hostName" value="${redis.host}"/>-->
        <property name="password" value="${redis.password}"/>
        <property name="usePool" value="true" />
        <property name="timeout" value="${redis.timeout}"></property>
    </bean>

    <bean id="redisTemplate"
          class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="connectionFactory"/>
    </bean>

    <bean id="dataSource" destroy-method="close" init-method="init" class="com.alibaba.druid.pool.DruidDataSource" name="dataSource">
        <property name="url" value="jdbc:sqlserver://**********:1433;databaseName=pmpdb0313"/>
        <property name="username" value="pmp03"/>
        <property name="password" value="J8WQff/KkGzESM8T7PCuax6A7qZkrAWbo1sXsaS1XO//XUVwnxD8mISUEkOoW5yw+hn1EuUDBRTtBtG6rNKXOw=="/>
        <property name="driverClassName" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>
        <property name="connectionProperties" value="config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAK/C2H5QIsWGTMRBpNjngeEO2ve+KPIOmIWrBXrKCuFEGIgQSKCDaMxF8VsetICLFw2hRXeyU4GX8I3eVMkSpf0CAwEAAQ==" />
        <!-- 初始化连接大小 -->
        <!-- <property name="initialSize" value="5"/>-->
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="100"/>
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="30"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="10000"/>
        <!-- <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>-->
        <!-- 用来检测有效sql -->
        <!-- <property name="validationQuery" value="SELECT 1"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>-->
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <!-- <property name="timeBetweenEvictionRunsMillis" value="60000"/>-->
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
       <!--  <property name="minEvictableIdleTimeMillis" value="25200000"/>-->
        <!-- 打开removeAbandoned功能 -->
        <!-- <property name="removeAbandoned" value="true"/>-->
        <!-- 1800秒，也就是30分钟 -->
        <!-- <property name="removeAbandonedTimeout" value="1800"/>-->
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <property name="filters" value="config,stat,slf4j"/>
    </bean>

    <bean id="wxJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
    </bean>

    <bean id="qrCodeDataSource" destroy-method="close" init-method="init" class="com.alibaba.druid.pool.DruidDataSource" name="qrCodeDataSource">
        <property name="url" value="******************************************************"/>
        <property name="username" value="pmp03"/>
        <property name="password" value="J8WQff/KkGzESM8T7PCuax6A7qZkrAWbo1sXsaS1XO//XUVwnxD8mISUEkOoW5yw+hn1EuUDBRTtBtG6rNKXOw=="/>
        <property name="driverClassName" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>
        <property name="connectionProperties" value="config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAK/C2H5QIsWGTMRBpNjngeEO2ve+KPIOmIWrBXrKCuFEGIgQSKCDaMxF8VsetICLFw2hRXeyU4GX8I3eVMkSpf0CAwEAAQ==" />
        <!-- 初始化连接大小 -->
        <!-- <property name="initialSize" value="5"/>-->
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="100"/>
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="30"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="10000"/>
        <!-- <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>-->
        <!-- 用来检测有效sql -->
        <!-- <property name="validationQuery" value="SELECT 1"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>-->
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <!-- <property name="timeBetweenEvictionRunsMillis" value="60000"/>-->
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <!-- <property name="minEvictableIdleTimeMillis" value="25200000"/>-->
        <!-- 打开removeAbandoned功能 -->
        <!-- <property name="removeAbandoned" value="true"/>-->
        <!-- 1800秒，也就是30分钟 -->
        <!-- <property name="removeAbandonedTimeout" value="1800"/>-->
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <property name="filters" value="config,stat,slf4j"/>
    </bean>

    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="qrCodeDataSource" />
    </bean>
    <!-- chongqi -->


</beans>