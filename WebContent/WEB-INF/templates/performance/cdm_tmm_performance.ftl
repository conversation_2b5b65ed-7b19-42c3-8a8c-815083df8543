<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Author>Administrator</Author>
  <LastAuthor>Ian <PERSON></LastAuthor>
  <LastPrinted>2020-01-11T11:58:00Z</LastPrinted>
  <Created>1998-08-28T08:17:00Z</Created>
  <LastSaved>2020-05-30T03:11:54Z</LastSaved>
  <Version>16.00</Version>
 </DocumentProperties>
 <CustomDocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <_NewReviewCycle dt:dt="string"></_NewReviewCycle>
  <KSOProductBuildVer dt:dt="string">2052-10.1.0.7698</KSOProductBuildVer>
 </CustomDocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <AllowPNG/>
  <Colors>
   <Color>
    <Index>16</Index>
    <RGB>#8080FF</RGB>
   </Color>
   <Color>
    <Index>17</Index>
    <RGB>#802060</RGB>
   </Color>
   <Color>
    <Index>18</Index>
    <RGB>#FFFFC0</RGB>
   </Color>
   <Color>
    <Index>19</Index>
    <RGB>#A0E0E0</RGB>
   </Color>
   <Color>
    <Index>20</Index>
    <RGB>#600080</RGB>
   </Color>
   <Color>
    <Index>22</Index>
    <RGB>#0080C0</RGB>
   </Color>
   <Color>
    <Index>23</Index>
    <RGB>#C0C0FF</RGB>
   </Color>
   <Color>
    <Index>33</Index>
    <RGB>#69FFFF</RGB>
   </Color>
   <Color>
    <Index>36</Index>
    <RGB>#A6CAF0</RGB>
   </Color>
   <Color>
    <Index>37</Index>
    <RGB>#CC9CCC</RGB>
   </Color>
   <Color>
    <Index>39</Index>
    <RGB>#E3E3E3</RGB>
   </Color>
   <Color>
    <Index>42</Index>
    <RGB>#339933</RGB>
   </Color>
   <Color>
    <Index>43</Index>
    <RGB>#999933</RGB>
   </Color>
   <Color>
    <Index>44</Index>
    <RGB>#996633</RGB>
   </Color>
   <Color>
    <Index>45</Index>
    <RGB>#996666</RGB>
   </Color>
   <Color>
    <Index>48</Index>
    <RGB>#3333CC</RGB>
   </Color>
   <Color>
    <Index>49</Index>
    <RGB>#336666</RGB>
   </Color>
   <Color>
    <Index>52</Index>
    <RGB>#663300</RGB>
   </Color>
   <Color>
    <Index>55</Index>
    <RGB>#424242</RGB>
   </Color>
  </Colors>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>10537</WindowHeight>
  <WindowWidth>26585</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <TabRatio>790</TabRatio>
  <ActiveSheet>0</ActiveSheet>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Size="12"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s20" ss:Name="百分比">
   <NumberFormat ss:Format="0%"/>
  </Style>
  <Style ss:ID="s16" ss:Name="千位分隔">
   <NumberFormat ss:Format="_(* #,##0.00_);_(* \(#,##0.00\);_(* &quot;-&quot;??_);_(@_)"/>
  </Style>
  <Style ss:ID="m668663864">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665320">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665340">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665360">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665380">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665400">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668665420">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668667192">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668667212">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668667232">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668480">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668500">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668520">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668540">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668669064">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668669084">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668669104">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668896">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668916">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668936">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668668956">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668678216">
   <Alignment ss:Horizontal="Right" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#FFFF00" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668678236">
   <Alignment ss:Horizontal="Right" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668678256">
   <Alignment ss:Horizontal="Right" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#FFFF00" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="m668678276">
   <Alignment ss:Horizontal="Right" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
  </Style>
  <Style ss:ID="s63">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
  </Style>
  <Style ss:ID="s64">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#000000"
    ss:Bold="1"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s72">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s73">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s74">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF"
    ss:Bold="1"/>
   <Interior ss:Color="#538DD5" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s75">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior/>
  </Style>
  <Style ss:ID="s76">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s77">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s78">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s79">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s81" ss:Parent="s20">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="0.0%"/>
  </Style>
  <Style ss:ID="s82">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
  </Style>
  <Style ss:ID="s83">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s84">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s85">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s86">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s87">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s89">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="Short Date"/>
  </Style>
  <Style ss:ID="s90">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s92">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="Short Date"/>
  </Style>
  <Style ss:ID="s93">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior/>
  </Style>
  <Style ss:ID="s94">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior/>
  </Style>
  <Style ss:ID="s95" ss:Parent="s20">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
  </Style>
  <Style ss:ID="s96">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s97">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11" ss:Color="#FF0000"
    ss:Bold="1"/>
  </Style>
  <Style ss:ID="s98">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s99">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
   <Borders/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="#,##0_ "/>
  </Style>
  <Style ss:ID="s100">
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
  </Style>
  <Style ss:ID="s107">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#FFFF00" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_(* #,##0.00_);_(* \(#,##0.00\);_(* &quot;-&quot;??_);_(@_)"/>
  </Style>
  <Style ss:ID="s114">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;??_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s115">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#FFFF00" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s116">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Bold="1"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s117">
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s118">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s120" ss:Parent="s16">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s121">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s122" ss:Parent="s16">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_-* #,##0.00_-;\-* #,##0.00_-;_-* &quot;-&quot;??_-;_-@_-"/>
  </Style>
  <Style ss:ID="s123">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;??_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s124">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s125" ss:Parent="s16">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <NumberFormat ss:Format="_-* #,##0.00_-;\-* #,##0.00_-;_-* &quot;-&quot;??_-;_-@_-"/>
  </Style>
  <Style ss:ID="s126">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;??_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s127">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s128">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
  </Style>
  <Style ss:ID="s129" ss:Parent="s16">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
  </Style>
  <Style ss:ID="s130">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s131" ss:Parent="s16">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_-* #,##0.00_-;\-* #,##0.00_-;_-* &quot;-&quot;??_-;_-@_-"/>
  </Style>
  <Style ss:ID="s132">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;??_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s133">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
  </Style>
  <Style ss:ID="s134" ss:Parent="s16">
   <Alignment ss:Horizontal="Right" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <NumberFormat ss:Format="_-* #,##0.00_-;\-* #,##0.00_-;_-* &quot;-&quot;??_-;_-@_-"/>
  </Style>
  <Style ss:ID="s135">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <NumberFormat ss:Format="_ * #,##0.00_ ;_ * \-#,##0.00_ ;_ * &quot;-&quot;??_ ;_ @_ "/>
  </Style>
  <Style ss:ID="s136">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s137">
   <Alignment ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
  </Style>
  <Style ss:ID="s138">
   <Alignment ss:Vertical="Center" ss:WrapText="1"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman" ss:Color="#FF0000"/>
  </Style>
  <Style ss:ID="s139" ss:Parent="s16">
   <Alignment ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s140">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Times New Roman" x:Family="Roman"/>
   <NumberFormat ss:Format="_-* #,##0.00_-;\-* #,##0.00_-;_-* &quot;-&quot;??_-;_-@_-"/>
  </Style>
  <Style ss:ID="s146">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/>
   <Font ss:FontName="微软雅黑" x:Family="Swiss" ss:Size="11"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="2020年度KPI总览">
  <Table ss:ExpandedColumnCount="300" ss:ExpandedRowCount="3000" x:FullColumns="1"
   x:FullRows="1" ss:StyleID="s62" ss:DefaultColumnWidth="56.07692307692308"
   ss:DefaultRowHeight="15.923076923076923">
   <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="13.153846153846153"/>
   <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="152.30769230769232"/>
   <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="67.84615384615384"
    ss:Span="3"/>
   <Column ss:Index="7" ss:StyleID="s63" ss:AutoFitWidth="0"
    ss:Width="67.84615384615384"/>
   <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="67.84615384615384"
    ss:Span="7"/>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234"/>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目单月KPI目标</Data></Cell>
    <Cell ss:MergeAcross="12" ss:StyleID="m668665320"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s73"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
    <Cell ss:StyleID="s73"><Data ss:Type="String">累计</Data></Cell>
   </Row>

      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMEntry'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMOrder'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">销量（升</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMSales'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMSNAbove'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
              </#if>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目当月累计KPI目标</Data></Cell>
    <Cell ss:MergeAcross="11" ss:StyleID="m668665340"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目YTD目标</Data></Cell>
          <Cell ss:MergeAcross="11" ss:StyleID="m668665340"><Data ss:Type="String">${year}年</Data></Cell>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
          <#list 1..12 as month>
              <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiTarget!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="7" ss:StyleID="s62"/>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目单月KPI实际完成</Data></Cell>
    <Cell ss:MergeAcross="12" ss:StyleID="m668665360"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s73"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
    <Cell ss:StyleID="s73"><Data ss:Type="String">累计</Data></Cell>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMEntry'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMOrder'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMSales'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
              </#if>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
          <#list data.collectData as collect>
              <#if collect.kpiType == 'settingTMMSNAbove'>
                  <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
              </#if>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目YTD实际</Data></Cell>
    <Cell ss:MergeAcross="11" ss:StyleID="m668665380"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>

      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="7" ss:StyleID="s62"/>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">YTD完成比例</Data></Cell>
          <Cell ss:MergeAcross="11" ss:StyleID="m668665420"><Data ss:Type="String">${year}年</Data></Cell>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
          <#list 1..12 as month>
              <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.ytdKpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.ytdKpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.ytdKpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.ytdKpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目当月累计KPI完成</Data></Cell>
    <Cell ss:MergeAcross="11" ss:StyleID="m668665400"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">项目当月累计KPI完成比例</Data></Cell>
    <Cell ss:MergeAcross="11" ss:StyleID="m668665420"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="7" ss:StyleID="s62"/>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">绩效工资</Data></Cell>
    <Cell ss:MergeAcross="11" ss:StyleID="m668663864"><Data ss:Type="String">${year}年</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
    <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
       <#list 1..12 as month>
           <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
       </#list>
   </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmDatas as tmmData>
                  <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                      <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s82"><Data ss:Type="String">总额</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmTotalAmount as totalAmount>
                  <#if totalAmount.month == month>
                      <Cell ss:StyleID="s77"><Data ss:Type="Number">${totalAmount.value!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="7" ss:StyleID="s62"/>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">绩效费用</Data></Cell>
          <Cell ss:MergeAcross="11" ss:StyleID="m668669084"><Data ss:Type="String">${year}年</Data></Cell>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">城市</Data></Cell>
          <#list 1..12 as month>
              <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
          </#list>
      </Row>
      <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
          <Cell ss:Index="2" ss:StyleID="s82"><Data ss:Type="String">总额</Data></Cell>
          <#list 1..12 as month>
              <#list data.tmmTotalFee as totalAmount>
                  <#if totalAmount.month == month>
                      <Cell ss:StyleID="s77"><Data ss:Type="Number">${totalAmount.value!}</Data></Cell>
                  </#if>
              </#list>
          </#list>
      </Row>
      <#list citys as city>
          <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
              <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">${city}</Data></Cell>
              <#list 1..12 as month>
                  <#list data.cityTotalAmount as cityData>
                      <#if cityData.month == month && cityData.city == city>
                          <Cell ss:StyleID="s79"><Data ss:Type="Number">${cityData.value!}</Data></Cell>
                      </#if>
                  </#list>
              </#list>
          </Row>
      </#list>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.25" x:Right="0.25" x:Top="0.75"/>
   </PageSetup>
   <Unsynced/>
   <FitToPage/>
   <Print>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <Scale>62</Scale>
    <HorizontalResolution>600</HorizontalResolution>
    <VerticalResolution>600</VerticalResolution>
   </Print>
   <DoNotDisplayGridlines/>
   <TopRowVisible>27</TopRowVisible>
   <Panes>
    <Pane>
     <Number>1</Number>
     <ActiveRow>1</ActiveRow>
     <ActiveCol>1</ActiveCol>
     <RangeSelection>R30C2:R35C14</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
    <#list tmms as data>
    <#assign baseInfo=data.tmmBaseInfo>
        <Worksheet ss:Name="${baseInfo.city!} ${baseInfo.dsrName!}">
          <Table ss:ExpandedColumnCount="200" ss:ExpandedRowCount="3000" x:FullColumns="1"
           x:FullRows="1" ss:StyleID="s62" ss:DefaultColumnWidth="56.07692307692308"
           ss:DefaultRowHeight="15.923076923076923">
           <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="13.153846153846153"/>
           <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="161.30769230769232"/>
           <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="71.307692307692307"
            ss:Span="3"/>
           <Column ss:Index="7" ss:StyleID="s63" ss:AutoFitWidth="0"
            ss:Width="71.307692307692307"/>
           <Column ss:StyleID="s62" ss:AutoFitWidth="0" ss:Width="71.307692307692307"
            ss:Span="7"/>
           <Column ss:Index="16" ss:StyleID="s62" ss:AutoFitWidth="0"
            ss:Width="77.538461538461533"/>
           <Column ss:Index="18" ss:StyleID="s62" ss:AutoFitWidth="0"
            ss:Width="58.846153846153847" ss:Span="1"/>
           <Column ss:Index="20" ss:StyleID="s62" ss:AutoFitWidth="0"
            ss:Width="65.07692307692308" ss:Span="1"/>
           <Row ss:AutoFitHeight="0" ss:Height="16.442307692307693">
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s84"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s85"/>
            <Cell ss:StyleID="s85"/>
            <Cell ss:StyleID="s83"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="16.442307692307693">
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">TMM项目KPI完成报告</Data></Cell>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">城市</Data></Cell>
            <Cell ss:StyleID="s90"><Data ss:Type="String">${baseInfo.city!}</Data></Cell>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">绩效工资</Data></Cell>
            <Cell ss:StyleID="s90"><Data ss:Type="Number">${baseInfo.performancePay!}</Data></Cell>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">入职日期</Data></Cell>
            <Cell ss:MergeAcross="1" ss:StyleID="s89"><Data ss:Type="DateTime">${baseInfo.entryDate?string("yyyy-MM-dd")}</Data></Cell>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="16.442307692307693">
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">TMM</Data></Cell>
            <Cell ss:StyleID="s90"><Data ss:Type="String">${baseInfo.dsrName!}</Data></Cell>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">绩效费用基准</Data></Cell>
            <Cell ss:StyleID="s90"><Data ss:Type="Number">${baseInfo.performanceBase!}</Data></Cell>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s86"><Data ss:Type="String">KPI起始月</Data></Cell>
            <Cell ss:MergeAcross="1" ss:StyleID="s92"><Data ss:Type="DateTime">${baseInfo.kpiStartDate?string("yyyy-MM-dd")}</Data></Cell>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="16.442307692307693">
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s90"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s84"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s85"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s85"/>
            <Cell ss:StyleID="s83"/>
            <Cell ss:StyleID="s83"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人单月KPI目标</Data></Cell>
            <Cell ss:MergeAcross="12" ss:StyleID="m668668896"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s73"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
            <Cell ss:StyleID="s73"><Data ss:Type="String">累计</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMEntry'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMOrder'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">销量（升</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMSales'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMSNAbove'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthTarget!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人当月累计KPI目标</Data></Cell>
            <Cell ss:MergeAcross="11" ss:StyleID="m668668916"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
               <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiTarget!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s97"><Data ss:Type="String">注：自第13个月（即${baseInfo.entryDate?string("MM")}月）起，目标清零重新计算</Data></Cell>
            <Cell ss:StyleID="s98"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人单月KPI实际</Data></Cell>
            <Cell ss:MergeAcross="12" ss:StyleID="m668668936"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s73"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
            <Cell ss:StyleID="s73"><Data ss:Type="String">累计</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMEntry'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMOrder'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMSales'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s76"><Data ss:Type="Number">${tmmData.kpiMonthValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
               <#list data.collectData as collect>
                   <#if collect.kpiType == 'settingTMMSNAbove'>
                       <Cell ss:StyleID="s77"><Data ss:Type="Number">${collect.kpiMonthValue!}</Data></Cell>
                   </#if>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人YTD实际</Data></Cell>
            <Cell ss:MergeAcross="11" ss:StyleID="m668668956"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD订货客户数累计</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">YTD高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.ytdKpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人当月累计KPI完成</Data></Cell>
            <Cell ss:MergeAcross="11" ss:StyleID="m668669064"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.kpiValue!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s97"><Data ss:Type="String">注：自第13个月（即${baseInfo.entryDate?string("MM")}月）起，累计数清零重新计算</Data></Cell>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
            <Cell ss:StyleID="s99"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">个人当月累计KPI完成比例</Data></Cell>
            <Cell ss:MergeAcross="11" ss:StyleID="m668669084"><Data ss:Type="String">${year}年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s81"><Data ss:Type="Number">${tmmData.kpiPercent!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">绩效工资</Data></Cell>
            <Cell ss:MergeAcross="11" ss:StyleID="m668669104"><Data ss:Type="String">2020年</Data></Cell>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
               <#list 1..12 as month>
                   <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmDatas as tmmData>
                       <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                           <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.performance!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
            <Cell ss:Index="2" ss:StyleID="s82"><Data ss:Type="String">总额</Data></Cell>
               <#list 1..12 as month>
                   <#list data.tmmTotalAmount as totalAmount>
                       <#if totalAmount.month == month>
                           <Cell ss:StyleID="s77"><Data ss:Type="Number">${totalAmount.value!}</Data></Cell>
                       </#if>
                   </#list>
               </#list>
           </Row>
           <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
            <Cell ss:Index="7" ss:StyleID="s94"/>
            <Cell ss:Index="16" ss:StyleID="s95"/>
           </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s64"><Data ss:Type="String">绩效费用</Data></Cell>
                  <Cell ss:MergeAcross="11" ss:StyleID="m668669104"><Data ss:Type="String">2020年</Data></Cell>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s72"><Data ss:Type="String">KPI项目</Data></Cell>
                  <#list 1..12 as month>
                      <Cell ss:StyleID="s74"><Data ss:Type="String">${month}月</Data></Cell>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计新客户数</Data></Cell>
                  <#list 1..12 as month>
                      <#list data.tmmDatas as tmmData>
                          <#if tmmData.kpiType == 'settingTMMEntry' && tmmData.month == month>
                              <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.fee!}</Data></Cell>
                          </#if>
                      </#list>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">订货客户数累计</Data></Cell>
                  <#list 1..12 as month>
                      <#list data.tmmDatas as tmmData>
                          <#if tmmData.kpiType == 'settingTMMOrder' && tmmData.month == month>
                              <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.fee!}</Data></Cell>
                          </#if>
                      </#list>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计销量（升）</Data></Cell>
                  <#list 1..12 as month>
                      <#list data.tmmDatas as tmmData>
                          <#if tmmData.kpiType == 'settingTMMSales' && tmmData.month == month>
                              <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.fee!}</Data></Cell>
                          </#if>
                      </#list>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s75"><Data ss:Type="String">累计高端销量（升）</Data></Cell>
                  <#list 1..12 as month>
                      <#list data.tmmDatas as tmmData>
                          <#if tmmData.kpiType == 'settingTMMSNAbove' && tmmData.month == month>
                              <Cell ss:StyleID="s79"><Data ss:Type="Number">${tmmData.fee!}</Data></Cell>
                          </#if>
                      </#list>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234">
                  <Cell ss:Index="2" ss:StyleID="s82"><Data ss:Type="String">总额</Data></Cell>
                  <#list 1..12 as month>
                      <#list data.tmmTotalFee as totalAmount>
                          <#if totalAmount.month == month>
                              <Cell ss:StyleID="s77"><Data ss:Type="Number">${totalAmount.value!}</Data></Cell>
                          </#if>
                      </#list>
                  </#list>
              </Row>
              <Row ss:AutoFitHeight="0" ss:Height="21.980769230769234" ss:StyleID="s93">
                  <Cell ss:Index="7" ss:StyleID="s94"/>
                  <Cell ss:Index="16" ss:StyleID="s95"/>
              </Row>
          </Table>
          <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
           <PageSetup>
            <Header x:Margin="0.3"/>
            <Footer x:Margin="0.3"/>
            <PageMargins x:Bottom="0.75" x:Left="0.25" x:Right="0.25" x:Top="0.75"/>
           </PageSetup>
           <Unsynced/>
           <FitToPage/>
           <Print>
            <ValidPrinterInfo/>
            <PaperSizeIndex>9</PaperSizeIndex>
            <Scale>52</Scale>
            <HorizontalResolution>600</HorizontalResolution>
            <VerticalResolution>600</VerticalResolution>
           </Print>
           <Selected/>
           <DoNotDisplayGridlines/>
           <TopRowVisible>3</TopRowVisible>
           <Panes>
            <Pane>
             <Number>1</Number>
             <ActiveRow>1</ActiveRow>
             <ActiveCol>1</ActiveCol>
            </Pane>
           </Panes>
           <ProtectObjects>False</ProtectObjects>
           <ProtectScenarios>False</ProtectScenarios>
          </WorksheetOptions>
         </Worksheet>
    </#list>
</Workbook>
