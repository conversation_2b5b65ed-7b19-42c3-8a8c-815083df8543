<html>
<head>
    <title></title>
    <style>
        body {
            font-family: SimSun;
            font-size: 14px;
            font-style: italic;
            font-weight: 500;
        }

        .head {
            font-size: 20px;
            font-weight: 900;
            text-align: center;
        }

        label.field-label {
            width: 140px;
            display: inline-block;
            text-align: right;
            line-height: 30px;
            margin-right: 10px;
            vertical-align: top;
        }

        .textarea-field-label {
            line-height: 30px;
            margin-right: 10px;
            vertical-align: top;
            font-weight: bold;
        }

        .textarea-box{
            border:1px dotted #DDD;
            padding:6px;
            word-wrap:break-word;
            word-break:break-all;
            /* overflow: hidden;*/
            /* width:960px;*/
            width:99%;
            height: auto;
        }

        .activity-detail {
            display: none
        }

        .activity-detail > .field-group {
            width: 100%;
        }

        .cols-2 > .field-group {
            width: 49.2%;
            display: inline-block;
        }

        .cols-3 > .field-group, .cols-3 > .col-group {
            width: 32.5%;
            display: inline-block;
        }

        .field-group {
            background-color: #f9f9f9;
            margin-bottom: 4px;
            border: 1px solid #ddd;
            overflow: hidden;
            margin-right: -1px;
            white-space: nowrap;
            vertical-align: top;
        }

        .field-group .field-label {
            background-color: #e9e9e9;
            border-right: 1px solid #ddd;
            margin-right: 0px;
        }

        .field-group .value-label {
            max-height: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: auto;
            line-height: 18px;
            width: calc(100% - 136px);
            padding-left: 10px;
            display: inline-block;
            vertical-align: top;
            color: #666;
            padding: 6px 0;
            white-space: normal;
        }

        .fileDiv > .field-label {
            text-align: left;
        }

        .fileDiv > .control-group {
            display: block;
        }

        .content-panel {
            margin-top: 15px;
            position: relative;
            border-width: 1px;
            background-color: #fff;
        }

        table .file-table {
            width: 100%;
            color: #333333;
            border:1px solid #DDD;
            border-collapse: collapse;
        }

        table.file-table th {
            border:1px solid #ddd;
            border-collapse: collapse;
            padding: 4px;
            font-weight: bold;
        }

        table.file-table td {
            border: 1px solid #DDD;
            border-collapse: collapse;
            padding: 4px;

        }

        .img-box {
            width: 100px;
            height: 100px;
        }

        .form-title {
            padding: 8px 10px;
            margin-top: 20px;
            font-weight: 500;
            /*background-color: #DDDDDD;*/
            background-color: #267BB9;
            color:#FFF;
            font-weight: bold;
        }
        .content-panel{
            margin-top:4px;
        }
        .head-title {
            font-size: 20px;
            margin-left: 0px;
        }
        .title-label{
            line-height: 20px;
        }
    </style>
</head>

<body>
<div class="head">
    ${distributorName}-${signType}申请
</div>
<div class="head-title">
    <label class="title-label">申请号：${reqNo}</label>
</div>
<div>
    <div class="content-panel cols-2">
        <div class="form-title">基本信息</div>
        <div class="field-group">
            <label class="field-label">品牌：</label>
            <label class="value-label" id="brandName">${brandName}</label>
        </div>
        <div class="field-group">
            <label class="field-label">经销商名称：</label>
            <label class="value-label" id="distributorName">${distributorName}</label>
        </div>
        <div class="field-group">
            <label class="field-label">申请人：</label>
            <label class="value-label" id="createUserName">${createUserName}</label>
        </div>
        <div class="field-group">
            <label class="field-label">申请时间：</label>
            <label class="value-label" id="createTime">${createTime}</label>
        </div>
        <div class="field-group">
            <label class="field-label">申请类型：</label>
            <label class="value-label" id="signType">${signType}</label>
        </div>
        <div class="field-group">
            <label class="field-label">Vendor Code：</label>
            <label class="value-label" id="vendorCode">${vendorCode}</label>
        </div>
        <div class="field-group">
            <label class="field-label">成本中心：</label>
            <label class="value-label" id="costCenter">${costCenter}</label>
        </div>
        <div class="field-group">
            <label class="field-label">是否14A制作：</label>
            <#if localMake == "Y">
                <label class="value-label" id="localMake">是</label>
            </#if>
            <#if localMake == "N">
                <label class="value-label" id="localMake">否</label>
            </#if>
        </div>
    </div>

    <div class="content-panel cols-2">
        <div class="form-title">促销信息</div>
        <div class="field-group">
            <label class="field-label">促销金额(元)：</label>
            <label class="value-label" id="promotionAmount">${promotionAmount!''}</label>
        </div>

        <div class="">
            <label class="textarea-field-label">促销目的：</label>
            <div class="textarea-box" id="promotionGoal">
                <div style="white-space: pre-wrap;">${promotionGoal!''}</div>
            </div>
        </div>
        <div class="">
            <label class="textarea-field-label">预计效果：</label>
            <div class="textarea-box" id="promotionExpectEffect">
                <div style="white-space: pre-wrap;">${promotionExpectEffect!''}</div>
            </div>
        </div>
        <div class="">
            <label class="textarea-field-label">促销内容：</label>
            <div class="textarea-box" id="promotionContent">
                <div style="white-space: pre-wrap;">${promotionContent!''}</div>
            </div>
        </div>
    </div>

    <div class="content-panel cols-2">
        <div class="field-group">
            <label class="field-label">增值税发票类型：</label>
                <label class="value-label" id="localMake">${vatInvoiceType}</label>
        </div>
    </div>

    <div class="content-panel" >
        <table class="file-table" width="100%" cellspacing="0" cellpadding="4" border="1" bordercolor="#DDD" style="border-collapse: collapse;">
            <tr>
                <th width="60%">上传资料列表</th>
                <th width="15%">是否已上传（Y/N）</th>
            </tr>
            <tr>
                <td>促销方案</td>
                <td>${isPromotionPlanFiles}</td>
            </tr>
            <tr>
                <td>付款申请单</td>
                <td>${isApplyFormFiles}</td>
            </tr>
            <tr>
                <td>三方协议</td>
                <td>${isTripleAgreementFiles}</td>
            </tr>
            <tr>
                <td>合规发票</td>
                <td>${isInvoiceFiles}</td>
            </tr>
            <tr>
                <td>费用明细单/报价单</td>
                <td>${isStampedQuote}</td>
            </tr>
            <tr>
                <td>物料分发明细</td>
                <td>${isMaterialDetail}</td>
            </tr>
            <tr>
                <td>实物照片</td>
                <td>${isPhysicalPhoto}</td>
            </tr>
            <tr>
                <td>发票核验</td>
                <td>${isInvoiceConfirm}</td>
            </tr>
            <tr>
                <td>付款证明</td>
                <td>${isPaymentProofFiles}</td>
            </tr>
        </table>
    </div>

    <div class="form-title">审批历史记录</div>
    <div class="content-panel" id="historyGrid">
        <table class="file-table" width="100%" cellspacing="0" cellpadding="4" border="1" bordercolor="#DDD" style="border-collapse: collapse;">
            <tr>
                <th width="65px">序号</th>
                <th width="15%">处理人</th>
                <th width="40%">处理过程</th>
                <th width="20%">处理结果</th>
                <th width="25%">处理时间</th>
            </tr>
            <#assign num=0>
            <#if historyList??>
                <#list historyList as history>
                    <#if history.approveStatus==30>
                        <#assign num=num+1 />
                        <tr>
                            <td align="right">
                                ${num}
                            </td>
                            <td>
                                ${history.actualExecutorName!''}
                            </td>
                            <td>
                                <![CDATA[${history.workflowStep.stepName!}]]>
                            </td>
                            <td>
                                ${history.approveStatusText!}
                            </td>
                            <td>
                                ${history.executeTime?string("yyyy-MM-dd HH:mm:ss")}
                            </td>
                        </tr>
                    </#if>

                </#list>
            </#if>
        </table>
    </div>

</div>
</body>
</html>
