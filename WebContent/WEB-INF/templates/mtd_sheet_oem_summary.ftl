<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://purl.oclc.org/ooxml/spreadsheetml/main" xmlns:r="http://purl.oclc.org/ooxml/officeDocument/relationships" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><dimension ref="C2:Q${(4 + dataLength)?string['0']}"/><sheetViews><sheetView showGridLines="0" workbookViewId="0"><selection activeCell="C2" sqref="C2:D3"/></sheetView></sheetViews><sheetFormatPr defaultColWidth="8.88671875" defaultRowHeight="13.2"/><cols><col min="1" max="4" width="8.88671875" style="67"/><col min="5" max="5" width="10.6640625" style="67" customWidth="1"/><col min="6" max="6" width="11.33203125" style="67" customWidth="1"/><col min="7" max="7" width="13.44140625" style="67" customWidth="1"/><col min="8" max="8" width="12.33203125" style="67" customWidth="1"/><col min="9" max="9" width="13.33203125" style="67" customWidth="1"/><col min="10" max="10" width="12" style="67" customWidth="1"/><col min="11" max="11" width="12.5546875" style="67" customWidth="1"/><col min="12" max="12" width="10.88671875" style="67" customWidth="1"/><col min="13" max="13" width="12.33203125" style="67" customWidth="1"/><col min="14" max="14" width="10.5546875" style="67" customWidth="1"/><col min="15" max="15" width="12.109375" style="67" customWidth="1"/><col min="16" max="16" width="11.44140625" style="67" customWidth="1"/><col min="17" max="17" width="15.33203125" style="67" customWidth="1"/><col min="18" max="16384" width="8.88671875" style="67"/></cols><sheetData><row r="2" spans="3:17" ht="14.4" customHeight="1"><c r="C2" s="170" t="s"><v>24</v></c><c r="D2" s="171"/><c r="E2" s="174" t="s"><v>25</v></c><c r="F2" s="176" t="s"><v>26</v></c><c r="G2" s="177"/><c r="H2" s="176" t="s"><v>27</v></c><c r="I2" s="177"/><c r="J2" s="164" t="s"><v>46</v></c><c r="K2" s="164" t="s"><v>39</v></c><c r="L2" s="164" t="s"><v>47</v></c><c r="M2" s="166" t="s"><v>28</v></c><c r="N2" s="164" t="s"><v>29</v></c><c r="O2" s="166" t="s"><v>30</v></c><c r="P2" s="164" t="s"><v>48</v></c><c r="Q2" s="164" t="s"><v>49</v></c></row><row r="3" spans="3:17" ht="35.4" customHeight="1" thickBot="1"><c r="C3" s="172"/><c r="D3" s="173"/><c r="E3" s="175"/><c r="F3" s="153" t="s"><v>44</v></c><c r="G3" s="107" t="s"><v>45</v></c><c r="H3" s="153" t="s"><v>44</v></c><c r="I3" s="106" t="s"><v>45</v></c><c r="J3" s="178"/><c r="K3" s="165"/><c r="L3" s="165"/><c r="M3" s="167"/><c r="N3" s="165"/><c r="O3" s="167"/><c r="P3" s="165"/><c r="Q3" s="165"/></row>
<row r="4" spans="3:17" ht="15" customHeight="1" thickTop="1"><c r="C4" s="179" t="str">
<v><![CDATA[${firstItem.sc4c}]]></v></c><c r="D4" s="180"></c><c r="E4" s="38"><v>${firstItem.c4e?string["0.######"]}</v></c><c r="F4" s="39"><v>${firstItem.c4f?string["0.######"]}</v></c><c r="G4" s="30"><v>${firstItem.c4g?string["0.######"]}</v></c><c r="H4" s="40"><v>${firstItem.c4h?string["0.######"]}</v></c><c r="I4" s="30"><v>${firstItem.c4i?string["0.######"]}</v></c><c r="J4" s="31"><f>E4+F4+H4</f><v>${(firstItem.c4e+firstItem.c4f+firstItem.c4h)?string["0.######"]}</v></c><c r="K4" s="32"><f>SUM(E4:I4)</f><v>${(firstItem.c4e+firstItem.c4f+firstItem.c4g+firstItem.c4h+firstItem.c4i)?string["0.######"]}</v></c><c r="L4" s="43"><v>${firstItem.c4l?string["0.######"]}</v></c>
<#if firstItem.c4l!=0><c r="M4" s="74"><f>IF(L4=0,"//",E4/L4)</f><v>${(firstItem.c4e/firstItem.c4l)?string["0.######"]}</v></c><#else><c r="M4" s="74" t="str"><f>IF(L4=0,"//",E4/L4)</f><v>//</v></c></#if>
<c r="N4" s="43"><v>${firstItem.c4n?string["0.######"]}</v></c>
<#if firstItem.c4n!=0><c r="O4" s="34"><f>IF(N4=0,"//",E4/N4)</f><v>${(firstItem.c4e/firstItem.c4n)?string["0.######"]}</v></c><#else><c r="O4" s="34" t="str"><f>IF(N4=0,"//",E4/N4)</f><v>//</v></c></#if>
<c r="P4" s="76"><v>${firstItem.c4p?string["0.######"]}</v></c><c r="Q4" s="76"><v>${firstItem.c4q?string["0.######"]}</v></c></row>
			<#if dataList??>
			<#list dataList as data>
<row r="${(data_index + 5)?string['0']}" spans="3:17" ht="14.4" customHeight="1"><c r="C${(data_index + 5)?string['0']}" s="181" t="str">
<v><![CDATA[${data.sc4c}]]></v></c><c r="D${(data_index + 5)?string['0']}" s="181"/><c r="E${(data_index + 5)?string['0']}" s="38"><v>${data.c4e?string["0.######"]}</v></c><c r="F${(data_index + 5)?string['0']}" s="39"><v>${data.c4f?string["0.######"]}</v></c><c r="G${(data_index + 5)?string['0']}" s="30"><v>${data.c4g?string["0.######"]}</v></c><c r="H${(data_index + 5)?string['0']}" s="40"><v>${data.c4h?string["0.######"]}</v></c><c r="I${(data_index + 5)?string['0']}" s="30"><v>${data.c4i?string["0.######"]}</v></c><c r="J${(data_index + 5)?string['0']}" s="41"><f>E${(data_index + 5)?string['0']}+F${(data_index + 5)?string['0']}+H${(data_index + 5)?string['0']}</f><v>${(data.c4e+data.c4f+data.c4h)?string["0.######"]}</v></c><c r="K${(data_index + 5)?string['0']}" s="42"><f>SUM(E${(data_index + 5)?string['0']}:I${(data_index + 5)?string['0']})</f><v>${(data.c4e+data.c4f+data.c4g+data.c4h+data.c4i)?string["0.######"]}</v></c><c r="L${(data_index + 5)?string['0']}" s="43"><v>${data.c4l?string["0.######"]}</v></c>
<#if data.c4l!=0><c r="M${(data_index + 5)?string['0']}" s="75"><f>IF(L${(data_index + 5)?string['0']}=0,"//",E${(data_index + 5)?string['0']}/L${(data_index + 5)?string['0']})</f><v>${(data.c4e/data.c4l)?string["0.######"]}</v></c><#else><c r="M${(data_index + 5)?string['0']}" s="75" t="str"><f>IF(L${(data_index + 5)?string['0']}=0,"//",E${(data_index + 5)?string['0']}/L${(data_index + 5)?string['0']})</f><v>//</v></c></#if>
<c r="N${(data_index + 5)?string['0']}" s="43"><v>${data.c4n?string["0.######"]}</v></c>
<#if data.c4n!=0><c r="O${(data_index + 5)?string['0']}" s="44"><f>IF(N${(data_index + 5)?string['0']}=0,"//",E${(data_index + 5)?string['0']}/N${(data_index + 5)?string['0']})</f><v>${(data.c4e/data.c4n)?string["0.######"]}</v></c><#else><c r="O${(data_index + 5)?string['0']}" s="44" t="str"><f>IF(N${(data_index + 5)?string['0']}=0,"//",E${(data_index + 5)?string['0']}/N${(data_index + 5)?string['0']})</f><v>//</v></c></#if>
<c r="P${(data_index + 5)?string['0']}" s="76"><v>${data.c4p?string["0.######"]}</v></c><c r="Q${(data_index + 5)?string['0']}" s="76"><v>${data.c4q?string["0.######"]}</v></c></row>
			</#list>
			</#if>
<row r="${(4 + dataLength)?string['0']}" spans="3:17"><c r="C${(4 + dataLength)?string['0']}" s="182" t="s"><v>31</v></c><c r="D${(4 + dataLength)?string['0']}" s="183"/><c r="E${(4 + dataLength)?string['0']}" s="116"><f>SUM(E4:E${(3 + dataLength)?string['0']})</f><v>${total.c4e?string["0.######"]}</v></c><c r="F${(4 + dataLength)?string['0']}" s="117"><f>SUM(F4:F${(3 + dataLength)?string['0']})</f><v>${total.c4f?string["0.######"]}</v></c><c r="G${(4 + dataLength)?string['0']}" s="56"><f>SUM(G4:G${(3 + dataLength)?string['0']})</f><v>${total.c4g?string["0.######"]}</v></c><c r="H${(4 + dataLength)?string['0']}" s="118"><f>SUM(H4:H${(3 + dataLength)?string['0']})</f><v>${total.c4h?string["0.######"]}</v></c><c r="I${(4 + dataLength)?string['0']}" s="56"><f>SUM(I4:I${(3 + dataLength)?string['0']})</f><v>${total.c4i?string["0.######"]}</v></c><c r="J${(4 + dataLength)?string['0']}" s="119"><f>E${(4 + dataLength)?string['0']}+F${(4 + dataLength)?string['0']}+H${(4 + dataLength)?string['0']}</f><v>${(total.c4e+total.c4f+total.c4h)?string["0.######"]}</v></c><c r="K${(4 + dataLength)?string['0']}" s="57"><f>SUM(E${(4 + dataLength)?string['0']}:I${(4 + dataLength)?string['0']})</f><v>${(total.c4e+total.c4f+total.c4g+total.c4h+total.c4i)?string["0.######"]}</v></c><c r="L${(4 + dataLength)?string['0']}" s="58"><f>SUM(L4:L${(3 + dataLength)?string['0']})</f><v>${total.c4l?string["0.######"]}</v></c>
<#if total.c4l!=0><c r="M${(4 + dataLength)?string['0']}" s="81"><f>IF(L${(4 + dataLength)?string['0']}=0,"//",E${(4 + dataLength)?string['0']}/L${(4 + dataLength)?string['0']})</f><v>${(total.c4e/total.c4l)?string["0.######"]}</v></c><#else><c r="M${(4 + dataLength)?string['0']}" s="81" t="str"><f>IF(L${(4 + dataLength)?string['0']}=0,"//",E${(4 + dataLength)?string['0']}/L${(4 + dataLength)?string['0']})</f><v>//</v></c></#if>
<c r="N${(4 + dataLength)?string['0']}" s="58"><f>SUM(N4:N${(3 + dataLength)?string['0']})</f><v>${total.c4n?string["0.######"]}</v></c>
<#if total.c4n!=0><c r="O${(4 + dataLength)?string['0']}" s="59"><f>IF(N${(4 + dataLength)?string['0']}=0,"//",E${(4 + dataLength)?string['0']}/N${(4 + dataLength)?string['0']})</f><v>${(total.c4e/total.c4n)?string["0.######"]}</v></c><#else><c r="O${(4 + dataLength)?string['0']}" s="59" t="str"><f>IF(N${(4 + dataLength)?string['0']}=0,"//",E${(4 + dataLength)?string['0']}/N${(4 + dataLength)?string['0']})</f><v>//</v></c></#if>
<c r="P${(4 + dataLength)?string['0']}" s="82"><f>SUM(P4:P${(3 + dataLength)?string['0']})</f><v>${total.c4p?string["0.######"]}</v></c><c r="Q${(4 + dataLength)?string['0']}" s="82"><f>SUM(Q4:Q${(3 + dataLength)?string['0']})</f><v>${total.c4q?string["0.######"]}</v></c></row>
</sheetData><mergeCells count="16"><mergeCell ref="M2:M3"/><mergeCell ref="N2:N3"/><mergeCell ref="O2:O3"/><mergeCell ref="P2:P3"/><mergeCell ref="Q2:Q3"/><mergeCell ref="C4:D4"/><mergeCell ref="C5:D5"/>
			<#if dataList??>
			<#list dataList as data>
<mergeCell ref="C${(data_index + 6)?string['0']}:D${(data_index + 6)?string['0']}"/>
			</#list>
			</#if>
<mergeCell ref="L2:L3"/><mergeCell ref="C2:D3"/><mergeCell ref="E2:E3"/><mergeCell ref="F2:G2"/><mergeCell ref="H2:I2"/><mergeCell ref="J2:J3"/><mergeCell ref="K2:K3"/></mergeCells><pageMargins left="0.75" right="0.75" top="1" bottom="1" header="0.5" footer="0.5"/><pageSetup paperSize="9" orientation="portrait" verticalDpi="0" r:id="rId1"/><headerFooter alignWithMargins="0"/></worksheet>