<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
	<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
pageContext.setAttribute("initParams", "true".equals(request.getParameter("cacheParams")) ? 
		session.getAttribute("com.chevron.partnerorder.controller.PartnerInventoryController.params") : null);
%>
		<!DOCTYPE html>
		<%@page import="com.common.util.ContextUtil"%>
			<html>

			<head>
				<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
				<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
				<title>合伙人库存</title>
				<%@include file="/common/jsp/common.jsp"%>
				<style>
				#partnerId_c input[type=text]{
					width: 250px;
				}
				</style>
				<script type='text/javascript'>
				var initParams = {
						start: '${initParams.start}',
						limit: '${initParams.limit}',
						field: '${initParams.field}',
						direction: '${initParams.direction}'
					};
				</script>
				<script type='text/javascript' src="${ctx }partnerorder/partnerinventory/js/partnerinventoryPage.js?v=20170323"></script>
			</head>

			<body class="gray-bg">
				<div class="content-wrapper">
					<c:set var="inventoryType" value="sp"></c:set>
					<%@include file="/partnerorder/partnerinventory/inventoryNav.jsp"%>
					<div class="content-panel query-panel">
						<div class="field-group">
							<label class="field-label width-auto">合伙人：</label>
							<div class="control-group" id="partnerId_c">
								<input id="partnerId" name="partnerId" value="${initParams.partnerId }" text="${initParams.partnerName }" type="hidden" />
							</div>
						</div>
						<div class="query-btns">
							<div class="query-btn field-label">
								<button onclick="queryProductsByParam()" class="btn-query">查询</button>
							</div>
						</div>
					</div>

					<div class="content-panel" id="partnerinventory_grid"></div>
				</div>
			</body>

			</html>