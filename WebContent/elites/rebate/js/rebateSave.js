; 'use strict';

$(function() {
	save_widget.init();
});

var save_widget = (function($) {
	; 'use strict';
	
	var activityTypeCtrls = null,
	fundTypeCtrls = null,
	fundOwnershipCtrls = null,
	billboardTypeCtrls = null,
	
	splitTypeCtrls = null,
	splitMode = null,
	
	orgSelect = null,
	orgStore = null,
	
	fundGrid = null,
	fundStore = null,
	
	totalFundGrid = null,
	totalFundStore = null,
	
	usedFundGrid = null,
	usedFundStore = null,
	
	actualFundGrid = null,
	actualFundStore = null,
	
	remainInvoiceFundGrid = null,
	remainInvoiceFundStore = null,
	
	orgList = null,
	selectedOrgInfo = null,
	validFund = 0,
	
	imgInvoiceAddDialog = null,
	
	billboardPhoto = [],
	
	managerMailFile = [],
	scenePhotoFile = [],
	invoiceFile = [],
	marketingMailFile = [],
	customerInteractionFile = [],
	signInFile = [],
	otherFile = [],
	marketPlanFile = [],
	promotionReceiptFile = [],
	splitMailFile = [],
	
	
	requireFileList = [],
	
	form = null,
	
	detailData = null,
	
	loader = {},
	util = {},
	action = {};
	
	var foo = new $.JsonRpcClient({
		ajaxUrl: '/wxPublicRpc.do'
	});
	
	var req = {
			getDicItemCode: function(dicCode, callback) {
				foo.call("dicService.getDicItemByDicTypeCode", [dicCode], function(data){
					if(data.code == 'success'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getFileList: function(fileTypeCode, callback) {
				LoadMask.show();
				foo.call("rebateApplyService.getRebateFileList", [fileTypeCode], function(data){
					LoadMask.hide();
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getOrgList: function(callback) {
				LoadMask.show();
				foo.call("rebateApplyService.getOrgList", [], function(data){
					LoadMask.hide();
					if(data.code == '0000'){
						orgList = data.data;
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			saveRebate: function(saveRequest, callback) {
				LoadMask.show();
				$.ajax({
					dataType: 'json',
					type: 'post',
					contentType: 'application/json',
					url: '/elites/rebate/save.do',
					data: JSON.stringify(saveRequest),
					success : function (data) {
						LoadMask.hide();
						$.isFunction(callback) && callback(data);
	                },
				})
			},
			submitRebate: function(saveRequest, callback) {
				LoadMask.show();
				$.ajax({
					dataType: 'json',
					type: 'post',
					contentType: 'application/json',
					url: '/elites/rebate/submit.do',
					data: JSON.stringify(saveRequest),
					success : function (data) {
						LoadMask.hide();
						$.isFunction(callback) && callback(data);
	                },
				})
			},
			getDetail: function(id, callback) {
				LoadMask.show();
				foo.call("rebateApplyService.detail", [id], function(data){
					LoadMask.hide();
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getActivityTypeByFundCode: function(fundCode, callback) {
				LoadMask.show();
				foo.call("rebateApplyService.getActivityTypeByFundCode", [fundCode], function(data){
					LoadMask.hide();
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
	},
	util = {
		getFundGridData: function(data) {
			var gridData = [];
			for(var i = 0; i <= 1; i++) {
				var channel = "";
				var bdFund;
				var mktFund;
				if(i == 0) {
					channel = "CDM";
					bdFund = data.bdFundCDM;
					mktFund =  data.mktFundCDM;
				} else {
					channel = "C&I";
					bdFund = data.bdFundCI;
					mktFund = data.mktFundCI;
				}
				
				gridData.push({
					channel: channel,
					bdFund: util.transferMoneyDeciaml(bdFund),
					mktFund: util.transferMoneyDeciaml(mktFund)
				});
			}
			return gridData;
		},
		getTotalFundGridData: function(data) {
			var gridData = [];
			for(var i = 0; i <= 1; i++) {
				var channel = "";
				var bdFund;
				var mktFund;
				if(i == 0) {
					channel = "CDM";
					bdFund = data.totalBdFundCDM;
					mktFund =  data.totalMktFundCDM;
				} else {
					channel = "C&I";
					bdFund = data.totalBdFundCI;
					mktFund = data.totalMktFundCI;
				}
				
				gridData.push({
					channel: channel,
					bdFund: util.transferMoneyDeciaml(bdFund),
					mktFund: util.transferMoneyDeciaml(mktFund)
				});
			}
			return gridData;
		},
		getUsedFundGridData: function(data) {
			var gridData = [];
			for(var i = 0; i <= 1; i++) {
				var channel = "";
				var bdFund;
				var mktFund;
				if(i == 0) {
					channel = "CDM";
					bdFund = data.usedBdFundCDM;
					mktFund =  data.usedMktFundCDM;
				} else {
					channel = "C&I";
					bdFund = data.usedBdFundCI;
					mktFund = data.usedMktFundCI;
				}
				
				gridData.push({
					channel: channel,
					bdFund: util.transferMoneyDeciaml(bdFund),
					mktFund: util.transferMoneyDeciaml(mktFund)
				});
			}
			return gridData;
		},
		getActualFundGridData: function(data) {
			var gridData = [];
			for(var i = 0; i <= 1; i++) {
				var channel = "";
				var bdFund;
				var mktFund;
				if(i == 0) {
					channel = "CDM";
					bdFund = data.actualBdFundCDM;
					mktFund =  data.actualMktFundCDM;
				} else {
					channel = "C&I";
					bdFund = data.actualBdFundCIO;
					mktFund = data.actualMktFundCIO;
				}
				
				gridData.push({
					channel: channel,
					bdFund: util.transferMoneyDeciaml(bdFund),
					mktFund: util.transferMoneyDeciaml(mktFund)
				});
			}
			return gridData;
		},
		getRemainInvoiceFundGridData: function(data) {
			var gridData = [];
			for(var i = 0; i <= 1; i++) {
				var channel = "";
				var bdFund;
				var mktFund;
				if(i == 0) {
					channel = "CDM";
					bdFund = data.remainInvoiceBdFundCDM;
					mktFund =  data.remainInvoiceMktFundCDM;
				} else {
					channel = "C&I";
					bdFund = data.remainInvoiceBdFundCIO;
					mktFund = data.remainInvoiceMktFundCIO;
				}
				
				gridData.push({
					channel: channel,
					bdFund: util.transferMoneyDeciaml(bdFund),
					mktFund: util.transferMoneyDeciaml(mktFund)
				});
			}
			return gridData;
		},
		validApplyFund: function(applyFund) {
			var invoiceAmount = $("#invoiceAmountTotal").val();
			if("" != invoiceAmount && parseFloat(applyFund) != parseFloat(invoiceAmount)) {
                common.alertMes('申请金额必须等于发票金额，请检查！', null, 'error');
                return false;
            }
		},
		transferMoneyDeciaml: function(fund) {
			return parseFloat(fund).toFixed(2) * 1;
		},
		validFileAudit: function() {
			for(var billBoarIndex in billboardPhoto) {
				if(billboardPhoto[billBoarIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var managerIndex in managerMailFile) {
				if(managerMailFile[managerIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var sceneIndex in scenePhotoFile) {
				if(scenePhotoFile[sceneIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var invoiceIndex in invoiceFile) {
				if(invoiceFile[invoiceIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var marketingIndex in marketingMailFile) {
				if(marketingMailFile[marketingIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var customerBoarIndex in customerInteractionFile) {
				if(customerInteractionFile[customerBoarIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var signInIndex in signInFile) {
				if(signInFile[signInIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var otherIndex in otherFile) {
				if(otherFile[otherIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var marketPlanIndex in marketPlanFile) {
				if(marketPlanFile[marketPlanIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var promotionReceiptIndex in promotionReceiptFile) {
				if(promotionReceiptFile[promotionReceiptIndex].auditStatus == "0") {
					return false;
				}
			}
			
			for(var splitMailIndex in splitMailFile) {
				if(splitMailFile[splitMailIndex].auditStatus == "0") {
					return false;
				}
			}
			return true;
		},
		getChannelText: function(channel) {
			if("CDM" == channel) {
				return "金富力";
			} else if("C&I" == channel) {
				return "德乐";
			}
		},
		initValidFund: function(data) {
			if(data == null) {
				return;
			}
			
			validFund = 0;
			var fundType = $("#fundType").val();
			if(data==undefined) return;
			
			if(splitMode == "-1" || splitMode == null) {//正常模式
				if(loginUserChannel == 'CDM') {
					if(data && data.specialApply) {//特殊企业
						if(fundType == '1') {//业务发展基金
							validFund = util.transferMoneyDeciaml(data.bdFundCDM + data.bdFundCI);
						} else {//市场营销基金
							validFund = util.transferMoneyDeciaml(data.mktFundCDM + data.mktFundCI);
						}
					} else {//非特殊企业
						if(fundType == '2') {//市场营销基金
							validFund = util.transferMoneyDeciaml(data.mktFundCDM);
						}
					}
				} else if(loginUserChannel == 'C&I') {
					if(!data.specialApply) {//非特殊企业
						if(fundType == '1') {//业务发展基金 
							validFund = util.transferMoneyDeciaml(data.bdFundCDM + data.bdFundCI);
						} else {//市场营销基金
							validFund = util.transferMoneyDeciaml(data.mktFundCI);
						}
					}
				}
			} else if(splitMode == "1") {//代为报销-不拆分
				if(loginUserChannel == 'CDM') {
					if(fundType == '1') {//业务发展基金
						validFund = util.transferMoneyDeciaml(data.bdFundCI);
					} else {
						validFund = util.transferMoneyDeciaml(data.mktFundCI);
					}
				} else {
					if(fundType == '1') {//业务发展基金
						validFund = util.transferMoneyDeciaml(data.bdFundCDM);
					} else {
						validFund = util.transferMoneyDeciaml(data.mktFundCDM);
					}
				}
			} else if(splitMode == "2") {//代为报销-拆分
				if(fundType == '1') {//业务发展基金 
					validFund = util.transferMoneyDeciaml(data.bdFundCDM + data.bdFundCI);
				} else {//市场营销基金
					validFund = util.transferMoneyDeciaml(data.mktFundCDM + data.mktFundCI);
				}
			}
		},
		getImgAmountByImgId: function(imgId) {
			for(var i in invoiceFile) {
				if(invoiceFile[i].attId == imgId) {
					return invoiceFile[i].invoiceAmount;
				}
			}
			return "";
		},
		verifyAmount: function(amount) {
			var reg = /^((?:-?0)|(?:-?[1-9]\d*))(?:\.\d{1,2})?$/;
			if(reg.test(amount)) {
				return true;
			} else {
				return false;
			}
		},
		addAmountToImg: function(imgId, amount) {
			for(var i in invoiceFile) {
				if(invoiceFile[i].attId == imgId) {
					if(false == util.verifyAmount(amount)) {
						common.alertMes("请检查发票金额！", 'error');
						return false;
					}
					invoiceFile[i].invoiceAmount = amount;
					break;
				}
			}
			return true;
		},
		getInvoiceAmountTotal: function() {
			var totalAmout = 0;
			if(invoiceFile.length > 0) {
				for(var i in invoiceFile) {
					totalAmout += parseFloat(invoiceFile[i].invoiceAmount);
				}
			}
			return totalAmout.toFixed(2);
		},
		validFileInvoiceAmount: function() {
			for(var i in invoiceFile) {
				if(invoiceFile[i].invoiceAmount == "" || invoiceFile[i].invoiceAmount == null) {
					return false;
				}
			}
		},
		restImgAuditStatus: function(imgId) {
			for(var i in invoiceFile) {
				if(invoiceFile[i].attId == imgId) {
					invoiceFile[i].auditStatus = null;
					break;
				}
			}
		},
		initTooltipTop : function(target, alignType) {
			BUI.use('bui/tooltip',function (Tooltip) {
				var _tips = new Tooltip.Tips({
					tip : {
						trigger : target,
						alignType : alignType,
						elCls : 'fundTooltip',
						offset : 10,
						width: 750,
						titleTpl : '<p>{title}</p>'
					}
				 });
				_tips.render();
			});
		},
	},
	loader = {
			initFundInput: function() {
				if(loginUserChannel == 'CDM') {
					$("#applyFundCDMGroup").show();
					$("#applyFundCIOGroup").hide();
				} else if(loginUserChannel == 'C&I') {
					$("#applyFundCDMGroup").hide();
					$("#applyFundCIOGroup").show();
				}
			},
			initRebateSave: function() {
				loader.initFundInput();
				util.initTooltipTop('#noSplitModeTooltip', 'bottom-right');
				util.initTooltipTop('#splitModeTooltip', 'bottom-right');
				
				
				if(rebateId) {
					req.getDetail(rebateId, function(data) {
						detailData = data;
						loader.initSplitType();
						loader.initFundType();
						loader.initOrgList();
						loader.renderForm();
					});
				} else {
					loader.initSplitType();
					loader.initFundType();
					loader.initOrgList();
					loader.renderForm();
				}
			},
			renderForm: function () {
				BUI && BUI.use('bui/form', function(Form) {
					//设置全局表单对象
					form = new Form.Form({
						srcNode: '#saveRebateForm',
					}).render();
					
					if(detailData) {
						form.setFieldValue("organizationName", detailData.organizationName);
						form.setFieldValue("applyFundCdm", detailData.applyFundCdmStr);
						form.setFieldValue("applyFundCio", detailData.applyFundCioStr);
						form.setFieldValue("activityDesc", detailData.activityDesc);
						form.setFieldValue("fundOwnership", detailData.fundOwnership);
						form.setFieldValue("customerInteractionDesc", detailData.customerInteractionDesc);
						form.setFieldValue("invoiceAmountTotal", detailData.invoiceAmountTotalStr);
						form.setFieldValue("splitDesc", detailData.splitDesc);
						$("#invoiceAmountTotal").val(detailData.invoiceAmountTotalStr);
						$("#fundOwnershipText").val(util.getChannelText(detailData.fundOwnership));
					}
				});
			},
			initActivityType: function(data) {
				if(!activityTypeCtrls) {
					if(!data) {
						data = [{"value": "-1", "text": "请选择申报项目"}];
					}
					
					BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function(Select, Picker, List, Data){
						activityTypeCtrls = new Select.Select({
					    	render: '#activityTypeDiv',
						    items: data,
						    valueField: '#activityType',
						    width: 230
					    });
						activityTypeCtrls.render();
						
						activityTypeCtrls.on('change', function(ev) {
							//隐藏所有证明文件上传域
							$(".requireFile").each(function(){
								$(this).hide();
							});
							//隐藏广告牌文件上传域和文本框
							$("#billboardGroup").hide();
							$("#billboardUploader").hide();
							//隐藏广告牌说明
							$("#billboardTypeDesc").html("");
							
							if(ev.value == -1) {
								$("#activityType").val(null);
								$("#customerInteractionDescDiv").hide();
							} else { 
								$("#activityType").val(ev.value);
								if(ev.value == 2) {//客户互动
									$("#customerInteractionDescDiv").show();
								} else {
									$("#customerInteractionDescDiv").hide();
								}
								
								if(ev.value == 3) {//广告牌
									$("#billboardGroup").show();
									loader.initBillboardType();
									//更改实物/会议文本说明
									$("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物照：');
								} else {
									//更改实物/会议文本说明
									if(ev.value == 5) {
										$("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>活动图片：');
									} else if(ev.value == 6 || ev.value == 7 || ev.value == 8 || ev.value == 9 || ev.value == 10){
										$("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物照片：');
									} else if(ev.value == 11 || ev.value == 12) {
										$("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>会议照片：');
									} else {
										$("#scenePhotoUploader").find("label.field-label").html('<span class="required-flag">*</span>实物/会议现场照：');
									}
									
								}
								//加载证明文件
								req.getFileList(ev.value, function(data) {
									for(var i in data) {
										var uploaderDom = data[i].fileTypeCode + "Uploader";
										var pickerDom = data[i].fileTypeCode + "Picker";
										var fileListDom = data[i].fileTypeCode + "List";
										var fileList = eval(data[i].fileTypeCode + "File");
										
										$("#" + fileListDom).empty();
										var uploadBtnDiv = '<div class="uploadBtn" id=' + pickerDom + '></div>';
										$("#" + fileListDom).html(uploadBtnDiv);
										fileList.splice(0,fileList.length);
										
										if(detailData) {
											var detailDataList = data[i].fileTypeCode + "FileList";
											if(detailData[detailDataList].length != 0) {
												var detailFileList = detailData[detailDataList];
												for(var key in detailFileList) {
													fileList.push({
														attId: detailFileList[key].attId,
														auditStatus: detailFileList[key].auditStatus,
														opinion: detailFileList[key].opinion,
														invoiceAmount: detailFileList[key].invoiceAmount,
														downloadType: detailFileList[key].downloadType,
														fileName: detailFileList[key].fileName,
														storageName: detailFileList[key].storageName,
													});
												}
											}
										}
										
										$("#" + uploaderDom).show();
										if(data[i].fileTypeCode != "other") {
											requireFileList.push(fileList);
										}
										loader.initFileUpload(data[i].fileTypeCode, pickerDom, fileListDom, "21", fileList);
									}
								});
							}
						});
						
						if(detailData) {
							activityTypeCtrls.setSelectedValue(detailData.activityType);
						} else {
							activityTypeCtrls.setSelectedValue('-1');
						}
					});
				} else {
					activityTypeCtrls.set("items", data);
					activityTypeCtrls.setSelectedValue('-1');
				}
			},
			initFundType: function() {
				BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function(Select, Picker, List, Data){
					req.getDicItemCode("elites.fundType", function(data){
						var items = [{"value": "-1", "text": "请选择基金类型"}];
						for (var i = 0; i < data.length; i++) {
							items.push({ 
								text: data[i].dicItemName, 
								value: data[i].dicItemCode 
							});
						}
						fundTypeCtrls = new Select.Select({
					    	render: '#fundTypeDiv',
						    items: items,
						    valueField: '#fundType',
						    width: 230
					    });
						fundTypeCtrls.render();
						fundTypeCtrls.on('change', function(ev){
							$("#fundType").val(ev.value);
							var data;
							if(ev.value == -1) {
								data = [{"value": "-1", "text": "请选择申报项目"}];
								$("#fundType").val(null);
								loader.initActivityType(data);
								validFund = 0;
								loader.initFundInput();
								return;
							} else {
								req.getActivityTypeByFundCode(ev.value, function(data){
									data.unshift({
										value: "-1",
										text: "请选择申报项目"
									});
									loader.initActivityType(data);
								});
							}
							
							loader.renderFundApplyInput(-1);
					    });
						
						if(detailData) {
							fundTypeCtrls.setSelectedValue(detailData.fundType);
						} else {
							fundTypeCtrls.setSelectedValue("-1");
						}
						
					});
				});
			},
			renderFundApplyInput: function(type) {
				
				if(-1 == type) {
					$("#applyFundCdm").val(0);
					$("#applyFundCio").val(0);
					$("#sumFund").html("0元");
				}
				
				$("#applyFundCdm").removeAttr("disabled");
				$("#applyFundCio").removeAttr("disabled");
				
				$("#applyFundCdmTip").html("申报金额可以大于剩余可用基金（含税）");
				$("#applyFundCioTip").html("申报金额可以大于剩余可用基金（含税）");
				//代为报销模式-不拆分
				if(splitMode == 1) {
					if(loginUserChannel == "CDM") {
						loader.changeCioApplyFund();
						$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报德乐金额（元）-含税：');
						$("#applyFundCIOGroup").show();
						
						$("#applyFundCDMGroup").hide();
						$("#applyFundCdm").attr("data-rules", "{}");
						
						
						$("#fundOwnership").val("C&I");
						$("#fundOwnershipText").val(util.getChannelText("C&I"));
						$("#fundOnwershipDiv").show();
					} else if(loginUserChannel == 'C&I') {
						loader.changeCdmApplyFund();
						$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金富力金额（元）-含税：');
						$("#applyFundCDMGroup").show();
						
						$("#applyFundCIOGroup").hide();
						$("#applyFundCio").attr("data-rules", "{}");
						
						$("#fundOwnership").val("CDM");
						$("#fundOwnershipText").val(util.getChannelText("CDM"));
						$("#fundOnwershipDiv").show();
					}
				}
				//代为报销模式-拆分
				if(splitMode == 2) {
					loader.changeCioApplyFund();
					$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报德乐金额（元）-含税：');
					$("#applyFundCIOGroup").show();
					
					loader.changeCdmApplyFund();
					$("#applyFundCDMGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金富力金额（元）-含税：');
					$("#applyFundCDMGroup").show();
					
					$("#fundOnwershipDiv").hide();
				}
				//正常模式
				if(splitMode == null || splitMode == -1) {
					if(null != selectedOrgInfo) {
						$("#fundOwnership").val(selectedOrgInfo.salesChannel);
						$("#fundOwnershipText").val(util.getChannelText(selectedOrgInfo.salesChannel));
					}
					if(loginUserChannel == "CDM") {
						//CDM无法申请特殊企业MKT
						if(selectedOrgInfo && selectedOrgInfo.specialApply != "1" && fundTypeCtrls.getSelectedValue() == 1) {
							$("#applyFundCdmTip").html("无法申请基金");
							$("#applyFundCdm").attr("disabled", "disabled");
							$("#fundOnwershipDiv").hide();
							common.alertMes("无法申请基金, 请重新选择基金类型或经销商！", 'error');
							return;
						}
						//CDM申请特殊企业BD+MKT都是双选框
						loader.changeCdmApplyFund();
						loader.changeCioApplyFund();
						$("#applyFundCDMGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额（元）-含税：');
						$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额（元）-含税：');
						if(selectedOrgInfo && selectedOrgInfo.specialApply == "1") {
							$("#fundOnwershipDiv").hide();
							
							$("#applyFundCDMGroup").show();
							$("#applyFundCIOGroup").show();
							
							$("#applyFundCDMGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金富力金额（元）-含税：');
							$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报德乐金额（元）-含税：');
						}
						//CDM申请非特殊企业MKT单选框
						if(selectedOrgInfo && selectedOrgInfo.specialApply != "1" && fundTypeCtrls.getSelectedValue() == 2) {
							$("#fundOnwershipDiv").show();
							
							$("#applyFundCDMGroup").show();
							$("#applyFundCIOGroup").hide();
							
							$("#applyFundCio").attr("data-rules", "{}");
						}
					} else if(loginUserChannel == "C&I") {
						//CIO无法申请特殊企业任何基金
						if(selectedOrgInfo.specialApply == "1") {
							$("#applyFundCioTip").html("无法申请基金");
							$("#applyFundCio").attr("disabled", "disabled");
							$("#fundOnwershipDiv").hide();
							common.alertMes("无法申请基金, 请重新选择基金类型或经销商！", 'error');
							return;
						}
						loader.changeCdmApplyFund();
						loader.changeCioApplyFund();
						$("#applyFundCDMGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额（元）-含税：');
						$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金额（元）-含税：');
						//CIO申请非特殊企业BD双选框
						if(selectedOrgInfo.specialApply != "1" && fundTypeCtrls.getSelectedValue() == 1) {
							$("#fundOnwershipDiv").hide();
							
							$("#applyFundCDMGroup").show();
							$("#applyFundCIOGroup").show();
							
							$("#applyFundCDMGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报金富力金额（元）-含税：');
							$("#applyFundCIOGroup label[class='field-label']").html('<span class="required-flag">*</span>本次申报德乐金额（元）-含税：');
						}
						//CIO申请非特殊企业MKT单选框
						if(selectedOrgInfo.specialApply != "1" && fundTypeCtrls.getSelectedValue() == 2) {
							$("#fundOnwershipDiv").show();
							
							$("#applyFundCIOGroup").show();
							$("#applyFundCDMGroup").hide();
							
							$("#applyFundCdm").attr("data-rules", "{}");
						}
					}
				}
				
				util.initValidFund(selectedOrgInfo);
			},
			changeCdmApplyFund: function() {
				$("#applyFundCdm").on("blur", function(e) {
					var total = util.transferMoneyDeciaml($("#applyFundCdm").val()) + util.transferMoneyDeciaml($("#applyFundCio").val());
					$("#sumFund").html(total + "元");
				});
			},
			changeCioApplyFund: function() {
				$("#applyFundCio").on("blur", function(e) {
					var total = util.transferMoneyDeciaml($("#applyFundCdm").val()) + util.transferMoneyDeciaml($("#applyFundCio").val());
					$("#sumFund").html(total+ "元");
				});
			},
			initOrgList: function() {
				BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function(Select, Picker, List, Data){
					req.getOrgList(function(data) {
						if(orgSelect) {
							orgStore.setResult(data);
							return;
						}
						orgStore = new Data.Store({
				    		data: data,
				    		autoload: true
				    	});
					    orgSelect = new Select.Select({
					    	render: '#orgListDiv',
							list: {
						          itemTpl: '<li>{orgName}</li>',
						          idField: 'orgId',
						          elCls: 'bui-select-list'
						    },
						    store: orgStore,
						    valueField: '#orgList',
						    width: 230
					    });
					    orgSelect.render();
					    orgSelect.on('change', function(ev){
					        var item = ev.item;
					        $("#orgName").val(item.orgName);
					        $("#fundOnwershipDiv").hide();
					        $("#fundOwnership").val(item.salesChannel);
					        $("#fundOwnershipText").val(util.getChannelText(item.salesChannel));
					        
					        loader.initFundGrid(item);
					        util.initTooltipTop('#surplusToolTip', 'bottom-right');
					        $("#fundGridDiv").show();
					        
					        loader.initTotalFundGrid(item);
					        util.initTooltipTop('#totalToolTip', 'bottom-left');
					        $("#totalfundGridDiv").show();
					        
					        loader.initUsedFundGrid(item);
					        util.initTooltipTop('#declaredToolTip', 'bottom-right');
					        $("#usedfundGridDiv").show();
					        
					        loader.initActualFundGrid(item);
					        util.initTooltipTop('#actualFundToolTip', 'bottom-left');
					        $("#actualfundGridDiv").show();
					        
//					        loader.initRemainInvoiceFundGrid(item);
//					        $("#remainInvoicefundGridDiv").show();
					        
					        $(".tableDiv").show().css("display", "inline-block");
					        
					        //初始化基金类型
					        if(detailData) {
					        	fundTypeCtrls.setSelectedValue(detailData.fundType);
					        } else {
					        	fundTypeCtrls.setSelectedValue("-1");
					        }
					        
					        selectedOrgInfo = item;
					        
					    });
					    
					    if(detailData) {
					    	orgSelect.setSelectedValue(detailData.organizationId);
					    	loader.renderFundApplyInput()
					    	$("#applyFundCdm").val(detailData.applyFundCdmStr);
							$("#applyFundCio").val(detailData.applyFundCioStr);
							$("#sumFund").html(util.transferMoneyDeciaml(detailData.applyFundCdmStr) + util.transferMoneyDeciaml(detailData.applyFundCioStr) + "元");
					    } 
					});
				});
			},
			initBillboardType: function() {
				
				if(!billboardTypeCtrls) {
					var items = [{"value": "-1", "text": "请选择门头类型"}, {"value": "0", "text": "全包"}, {"value": "1", "text": "安装费"}];
					
					BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function(Select, Picker, List, Data){
						billboardTypeCtrls = new Select.Select({
					    	render: '#billboardTypeDiv',
						    items: items,
						    valueField: '#billboardType',
						    width: 230
					    });
						billboardTypeCtrls.render();
						billboardTypeCtrls.on('change', function(ev){
							if(ev.value == -1) {
								$("#billboardType").val(null)
							} else {
								$("#billboardType").val(ev.value);
								
								var billboardDesc = "";
								if(ev.value == 0) {//全包
									billboardDesc = "设计及安装全包";
								} else {//安装费
									billboardDesc = "市场部已经负担门头制作费用";
								}
								
								if($("#billboardTypeDesc").length > 0) {
									$("#billboardTypeDesc").html(billboardDesc);
								} else {
									$billboardTypeDesc = $('<label id="billboardTypeDesc" style="margin-left:-15px;">' + billboardDesc + ' </label>');
									$("#billboardGroup").append($billboardTypeDesc);
								}
							}
					    });
						
						
						if(detailData) {
							billboardTypeCtrls.setSelectedValue(detailData.billboardType);
						} else {
							billboardTypeCtrls.setSelectedValue('-1');
						}
					});
				} else {
					billboardTypeCtrls.setSelectedValue('-1');
				}
			},
			initFileUpload: function(fileType, pickerDom, listDom, sourceType, photoList) {
				var srouceId = 1;
				if(detailData) {
					srouceId = rebateId;
				}
				//针对发票每次只能上传单张图片
				var imgMultiple = true;
				if("invoice" == fileType) {
					imgMultiple = false;
				}
				//针对excel和图片不同处理, //liyu,不使用了放开权限，让后台去判断
				var accept = {};
				if(fileType == "marketPlan" || fileType == "promotionReceipt") {
					accept = {
							title: 'Applications',
							extensions : 'xls,xlsx,msg,pdf,gif,jpg,jpeg,bmp,png',
		                    mimeTypes : 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf,application/vnd.ms-outlook,image/*'
					};
				} else {
					accept = {
							title: 'Images/PDF',
					        extensions: 'gif,jpg,jpeg,bmp,png,pdf,msg',
					        mimeTypes: 'image/*,application/pdf,application/vnd.ms-outlook'
						};
				}
				var uploader = WebUploader.create({
				    auto: true,
				    server: common.ctx + 'uploadForAppAttchmentFile.do',
				    pick: {
				    	id: '#' + pickerDom,
				    	multiple: imgMultiple,
				    },
				    fileVal: 'myfiles',
				    formData: {
				    	sourceType: sourceType,
				    	sourceId: srouceId
				    }
				    //,accept: accept
				});
				//文件上传队列
				uploader.on('fileQueued', function(file) {
				    var $li = $(
				            '<div id="' + file.id + '" class="file-item thumbnail">' +
				            '<img>' +
				            '</div>'
				            );
				    var $img = $li.find('img');
				    $("#" + listDom).prepend($li);
				    
				    thumbnailWidth = 100;
				    thumbnailHeight = 100;
				    uploader.makeThumb( file, function( error, src ) {
				        if ( error ) {
				            $img.replaceWith('<span>不能预览</span>');
				            return;
				        }

				        $img.attr( 'src', src );
				    }, thumbnailWidth, thumbnailHeight );

				});
				//上传进度
				uploader.on('uploadProgress', function(file, percentage) {
				    var $li = $('#' + file.id), 
				    $percent = $li.find('.progress span');

				    // 避免重复创建
				    if (!$percent.length) {
				        $percent = $('<p class="progress"><span></span></p>').appendTo($li).find('span');
				    }

				    $percent.css('width', percentage * 100 + '%');
				});
				//上传成功
				uploader.on('uploadSuccess', function(file, response) {
					if(response.code == "success") {
						$("#" + file.id).remove();
						uploader.removeFile(file);
						
						var fileAttId = response.attachmentFileList[0].attId;
						var responseFileType = response.attachmentFileList[0].fileType;
						var storageName=response.attachmentFileList[0].storageName;
						var fileName=response.attachmentFileList[0].fileName;
						var extension="";
						if(storageName!=undefined && storageName!=""){
							var i = storageName.lastIndexOf(".");
							if(i > -1) extension = storageName.substring(i+1);
						}
					    
						var $imgDom = null;
						if(responseFileType.indexOf("application") != -1) {//针对EXCEL,PDF
							var imgSrc="/images/"+extension+".png";
							/*var mimeTypeSuffix=responseFileType.substring(12);
							if(mimeTypeSuffix=="pdf"){
								imgSrc = "/images/pdf.png";
							}else if(mimeTypeSuffix=="vnd.ms-outlook"|| mimeTypeSuffix=="octet-stream"){
								imgSrc = "/images/outlook-msg.png";
							}else{
								imgSrc = "/images/excel-l.png";
							}*/
							$imgDom = $(
					            '<div id="' + fileAttId + '" class="file-item thumbnail">' +
					                '<img src="'+imgSrc+'"' + 
					                //'onclick="rebate_common.showLargerImg(' + fileAttId + ')"' +
					                '>'+'<span class="desc">' +fileName+'</span>'+
					            '</div>'
					            );
						} else {//针对图片
							$imgDom = $(
						            '<div id="' + fileAttId + '" class="file-item thumbnail">' +
						                '<img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=' + 
					                	sourceType + '&attId=' + fileAttId + '" ' + 
						            	'onclick="rebate_common.showLargerImg(' + fileAttId + ')"' +
						                'onerror="rebate_common.imageError(this)">' +fileName+
						            '</div>'
						            );
						}
						
						var $actionDom = $(
								'<div class="file-panel">' +
									'<span class="cancel" title="删除图片" onclick="save_widget.removeImg(' + fileAttId + ',\'' + fileType + '\')">删除</span>' +
								'</div>'
								);
						
						//针对发票需要录入发票金额
						if("invoice" == fileType) {
							var $amountInput = $(
									'<span class="addAmount" title="添加发票金额" onclick="save_widget.addInvoiceAmount(' + fileAttId + ')">添加发票金额</span>'
									);
							$actionDom.append($amountInput);
						}
						
						//针对EXCEL需要下载文件
						if(responseFileType.indexOf("application") != -1) {
							var $download = $(
									'<a class="downloadFile" title="下载文件" href="/downloadAttachmentFile.do?attId=' + fileAttId + '"></a>'
									);
							$actionDom.append($download);
						}
						
						$imgDom.append($actionDom);
						
						$("#" + listDom).prepend($imgDom);	
						
					    photoList.push({
					    	attId: response.attachmentFileList[0].attId,
					    	auditStatus: null,
					    	opinion: null,
					    	invoiceAmount: null,
					    	downloadType: responseFileType
					    });
						
					    $('#' + fileAttId).addClass('upload-state-done');
					    
					    if("invoice" == fileType) {
					    	loader.showAddInvoiceAmountDialog(fileAttId);
					    }
					    
					} else {
						var $li = $('#' + file.id),
					    $error = $li.find('div.error');
				        $error = $('<div class="error"></div>').appendTo($li);
				        $error.attr("onclick", "$('#"  + file.id + "').remove();");
					    $error.text('上传失败，点击删除图片');
					}
				});
				//上传失败
				uploader.on('uploadError', function(file) {
				    var $li = $('#' + file.id),
				    $error = $li.find('div.error');
				    // 避免重复创建
				    if (!$error.length) {
				        $error = $('<div class="error"></div>').appendTo($li);
				        $error.attr("onclick", "$('#"  + file.id + "').remove();");
				    }
				    $error.text('上传失败，点击删除图片');
				});
				///上传完成
				uploader.on('uploadComplete', function(file) {
				    $('#' + file.id).find('.progress').remove();
				});
				//预加载数据
				if(detailData) {
					var imgSrc="";
					for(var i in photoList) {
						var fileAttId = photoList[i].attId;
						var fileAuditStatus = photoList[i].auditStatus;
						var fileOpinion = photoList[i].opinion;
						var downloadType = photoList[i].downloadType;
						var fileName= photoList[i].fileName;
						var storageName= photoList[i].storageName;
						var extension="";
						if(storageName!=undefined && storageName!=""){
							var fileNameI = storageName.lastIndexOf(".");
							if(fileNameI > -1) extension = storageName.substring(fileNameI+1);
						}
						var $imgBoxDom = $('<div id="' + fileAttId + '" class="file-item thumbnail"></div>');
						
						var $imgDom = null;
						if(downloadType.indexOf("application") != -1) {//针对下载的文件类型
							var imgSrc="/images/"+extension+".png";
							$imgDom = $(
						            '<div id="' + fileAttId + '" class="file-item thumbnail">' +
						                '<img src="'+imgSrc+'"' +
						                ' onclick="rebate_common.showLargerImg(' + fileAttId + ',' + fileAuditStatus + ',\'' + fileOpinion +'\');">' +
						                '<span class="desc">' +fileName+'</span>'+
						            '</div>'
						            );
						} else {//针对图片
							$imgDom = $(
						            '<div id="' + fileAttId + '" class="file-item thumbnail">' +
						                '<img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=' + 
					                	sourceType + '&attId=' + fileAttId + '" ' + 
					                	'onclick="rebate_common.showLargerImg(' + fileAttId + ',' + fileAuditStatus + ',\'' + fileOpinion +'\');"' +
						                'onerror="rebate_common.imageError(this)">' +
						            '</div>'
						            );
						}
						
						var $operationDom = $(
								'<div class="file-panel">' + 
			                	'<span class="cancel" onclick="save_widget.removeImg(' + fileAttId + ',\'' + fileType + '\')">删除</span>' +
			                	'</div>'
								); 
						//发票需要有发票金额
						if(fileType == "invoice") {
							var $amountInput = $(
									'<span class="addAmount" title="添加发票金额" onclick="save_widget.addInvoiceAmount(' + fileAttId + ')">添加发票金额</span>'
									);
							$operationDom.append($amountInput);
							
						}
						
						//针对EXCEL需要下载文件
						if(downloadType.indexOf("application") != -1) {
							var $download = $(
									'<a class="downloadFile" title="下载文件" href="/downloadAttachmentFile.do?attId=' + fileAttId + '"></a>'
									);
							$operationDom.append($download);
						}
						
						if(fileAuditStatus == "0") {//审核不通过
							var $rejectSpan = $(
									'<span class="auditReject" id="' + fileAttId + 'reject">拒绝</span>'
									);
							$rejectSpan.addClass("selected");
							$operationDom.attr("style", "display:block");
							$operationDom.append($rejectSpan);
						}
						$imgDom.append($operationDom);
						
						//发票金额
						if(fileType == "invoice") {
							var $invoiceAmount = $('<div id="' + fileAttId + 'amount">' + photoList[i].invoiceAmount + '元</div>');
	                    	$imgDom.find("div.file-panel").animate({bottom:"+=18px"});
	                    	$imgDom.append($invoiceAmount);
						}
						//审核意见
						if(fileAuditStatus == "0") {//审核不通过
							var $rejectOpinion = $('<div id="' + fileAttId + 'opinion" style="color:red">点击查看审核意见</div>');
							$operationDom.animate({bottom:"+=18px"});
							$imgDom.append($rejectOpinion);
							var onclickStr = "rebate_common.showLargerImg(" + fileAttId + ",0,'" + fileOpinion + "');"
							$rejectOpinion.attr("onclick", onclickStr);
						}
						
						$("#" + listDom).prepend($imgDom);
					}
				}
			},
			initFundGrid: function(data) {
				if(data.orgId == "-1") {
					return;
				}
				
				BUI.use(['bui/grid','bui/data'], function(Grid, Data){
					
					var gridData = util.getFundGridData(data);
					if(fundGrid) {
						fundStore.setResult(gridData);
						return;
					}
					
					fundStore = new Data.Store({
						data: gridData,
						autoLoad: true
					});
					
					fundGrid = new Grid.Grid({
						render : '#fundGrid',
						columns: [{
							title: '渠道', 
							sortable: false,
							dataIndex: 'channel', 
							width: '20%',
							renderer: function(value,obj,index) {
								return util.getChannelText(value);
							}
						}, {
							title: '业务发展基金', 
							sortable: false,
							dataIndex: 'bdFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						},{
							title: '市场营销基金', 
							sortable: false,
							dataIndex: 'mktFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						}],
						store: fundStore,
						bbar: false,
						width: '100%'
					});
					fundGrid.render();
				});
			},
			initTotalFundGrid: function(data) {
				if(data.orgId == "-1") {
					return;
				}
				
				BUI.use(['bui/grid','bui/data'], function(Grid, Data){
					
					var gridData = util.getTotalFundGridData(data);
					if(totalFundGrid) {
						totalFundStore.setResult(gridData);
						return;
					}
					
					totalFundStore = new Data.Store({
						data: gridData,
						autoLoad: true
					});
					
					totalFundGrid = new Grid.Grid({
						render : '#totalfundGrid',
						columns: [{
							title: '渠道', 
							sortable: false,
							dataIndex: 'channel', 
							width: '20%',
							renderer: function(value,obj,index) {
								return util.getChannelText(value);
							}
						}, {
							title: '业务发展基金', 
							sortable: false,
							dataIndex: 'bdFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						},{
							title: '市场营销基金', 
							sortable: false,
							dataIndex: 'mktFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						}],
						store: totalFundStore,
						bbar: false,
						width: '100%'
					});
					totalFundGrid.render();
				});
			},
			initUsedFundGrid: function(data) {
				if(data.orgId == "-1") {
					return;
				}
				
				BUI.use(['bui/grid','bui/data'], function(Grid, Data){
					
					var gridData = util.getUsedFundGridData(data);
					if(usedFundGrid) {
						usedFundStore.setResult(gridData);
						return;
					}
					
					usedFundStore = new Data.Store({
						data: gridData,
						autoLoad: true
					});
					
					usedFundGrid = new Grid.Grid({
						render : '#usedfundGrid',
						columns: [{
							title: '渠道', 
							sortable: false,
							dataIndex: 'channel', 
							width: '20%',
							renderer: function(value,obj,index) {
								return util.getChannelText(value);
							}
						}, {
							title: '业务发展基金', 
							sortable: false,
							dataIndex: 'bdFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						},{
							title: '市场营销基金', 
							sortable: false,
							dataIndex: 'mktFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						}],
						store: usedFundStore,
						bbar: false,
						width: '100%'
					});
					usedFundGrid.render();
				});
			},
			initActualFundGrid: function(data) {
				if(data.orgId == "-1") {
					return;
				}
				
				BUI.use(['bui/grid','bui/data'], function(Grid, Data){
					
					var gridData = util.getActualFundGridData(data);
					if(actualFundGrid) {
						actualFundStore.setResult(gridData);
						return;
					}
					
					actualFundStore = new Data.Store({
						data: gridData,
						autoLoad: true
					});
					
					actualFundGrid = new Grid.Grid({
						render : '#actualfundGrid',
						columns: [{
							title: '渠道', 
							sortable: false,
							dataIndex: 'channel', 
							width: '20%',
							renderer: function(value,obj,index) {
								return util.getChannelText(value);
							}
						}, {
							title: '业务发展基金', 
							sortable: false,
							dataIndex: 'bdFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						},{
							title: '市场营销基金', 
							sortable: false,
							dataIndex: 'mktFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						}],
						store: actualFundStore,
						bbar: false,
						width: '100%'
					});
					actualFundGrid.render();
				});
			},
			initRemainInvoiceFundGrid: function(data) {
				if(data.orgId == "-1") {
					return;
				}
				
				BUI.use(['bui/grid','bui/data'], function(Grid, Data){
					
					var gridData = util.getRemainInvoiceFundGridData(data);
					if(remainInvoiceFundGrid) {
						remainInvoiceFundStore.setResult(gridData);
						return;
					}
					
					remainInvoiceFundStore = new Data.Store({
						data: gridData,
						autoLoad: true
					});
					
					remainInvoiceFundGrid = new Grid.Grid({
						render : '#remainInvoicefundGrid',
						columns: [{
							title: '渠道', 
							sortable: false,
							dataIndex: 'channel', 
							width: '20%',
							renderer: function(value,obj,index) {
								return util.getChannelText(value);
							}
						}, {
							title: '业务发展基金', 
							sortable: false,
							dataIndex: 'bdFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						},{
							title: '市场营销基金', 
							sortable: false,
							dataIndex: 'mktFund', 
							width: '40%',
							renderer: function(value,obj,index) {
								if("" != value) {
									return value + "元";
								} else {
									return "0元";
								}
							}
						}],
						store: remainInvoiceFundStore,
						bbar: false,
						width: '100%'
					});
					remainInvoiceFundGrid.render();
				});
			},
			showAddInvoiceAmountDialog: function(imgId) {
				$("#imgIdForAmount").val(imgId);
				$("#invoiceAmount").val(util.getImgAmountByImgId(imgId));
				if(imgInvoiceAddDialog == null) {
					BUI.use("bui/overlay", function(Overlay) {
						imgInvoiceAddDialog = new Overlay.Dialog({
							title:'发票金额',
			                width:400,
			                height:200,
			                mask:true,  //设置是否模态
			                contentId:"invoiceAmountDialog",
			                buttons: [{
			                	text: '确认',
			                    elCls: 'btn-create',
			                    handler: function() {
			                      var imgIdCurrent = $("#imgIdForAmount").val();
			                      var invoiceAmount = $("#invoiceAmount").val();
			                      var result = util.addAmountToImg(imgIdCurrent, invoiceAmount);
			                      if(result) {
			                    	  $imgDom = $("#"+ imgIdCurrent);
			                    	  //添加说明文字
			                    	  if($("#" + imgIdCurrent + "amount").length <= 0) {
			                    		  var $invoiceAmount = $('<div id="' + imgIdCurrent + 'amount">' + invoiceAmount + '元</div>');
				                    	  $imgDom.find("div.file-panel").css("bottom", "18px");
				                    	  $imgDom.append($invoiceAmount);
			                    	  } else {
			                    		  $("#" + imgIdCurrent + "amount").html(invoiceAmount + "元");
			                    	  }
			                    	  //计算发票总额
			                    	  $("#invoiceAmountTotal").val(util.getInvoiceAmountTotal());
			                    	  //如果是审核不通过，则更改为审核通过
			                    	  $("#" + imgIdCurrent + "reject").remove();
			                    	  util.restImgAuditStatus(imgIdCurrent);
			                    	  this.close();
			                      } else {
			                    	  return;
			                      }
			                    }
			                }, {
			                	text:'取消',
			                    elCls : 'btn-cancel',
			                    handler : function(){
			                      this.close();
			                    }
			                }]
						});
					});
					imgInvoiceAddDialog.show();
				} else {
					$("#invoiceAmount").val(util.getImgAmountByImgId(imgId));
					imgInvoiceAddDialog.show();
				}
			},
			initSplitType: function() {
				BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function(Select, Picker, List, Data){
					req.getDicItemCode("elites.split.type", function(data){
						var items = [{"value": "-1", "text": "无"}];
						for (var i = 0; i < data.length; i++) {
							items.push({ 
								text: data[i].dicItemName, 
								value: data[i].dicItemCode 
							});
						}
						splitTypeCtrls = new Select.Select({
					    	render: '#splitTypeDiv',
						    items: items,
						    valueField: '#splitType',
						    width: 230
					    });
						splitTypeCtrls.render();
						splitTypeCtrls.on('change', function(ev){
							splitMode = ev.value;
							
							if(null != fundTypeCtrls) {
								fundTypeCtrls.setSelectedValue("-1");
							}
							
							$("#fundOnwershipDiv").hide();
							$(".splitModeTip").hide();
							if(splitMode == -1) {
								$("#splitDescDiv").hide();
								$("#splitMailUploader").hide();
							} else {
								$("#splitDescDiv").show();
								if(detailData) {
									var detailFileList = detailData["splitMailFileList"];
									for(var key in detailFileList) {
										splitMailFile.push({
											attId: detailFileList[key].attId,
											auditStatus: detailFileList[key].auditStatus,
											opinion: detailFileList[key].opinion,
											invoiceAmount: detailFileList[key].invoiceAmount,
											downloadType: detailFileList[key].downloadType,
											fileName: detailFileList[key].fileName,
											storageName: detailFileList[key].storageName,
										});
									}
								}
								loader.initFileUpload("splitMail", "splitMailPicker", "splitMailList", "21", splitMailFile);
								$("#splitMailUploader").show();
								
								if(ev.value == "1") {
									$("#noSplitModeTooltip").show();
								} else {
									$("#splitModeTooltip").show();
								}
								
							}
					    });
						
						if(detailData) {
							splitTypeCtrls.setSelectedValue(detailData.splitType);
						} else {
							splitTypeCtrls.setSelectedValue("-1");
						}
					});
				});
			}
	},
	action = {
			beforeSaveRequest: function(type) {
				var saveRequest = $.extend({}, form.getRecord(), {});
				var invoiceAmountTotal = $("#invoiceAmountTotal").val();
				if(type == 2) {//提交数据，需要校验结果
					
					if(saveRequest.fundType == '2' && saveRequest.activityType == '2' ) {
						var customerInteractionDesc = form.getField('customerInteractionDesc');
						customerInteractionDesc.addRule('required', true);
					}
					
					form.valid();
					if(!form.isValid()) {
						return false
					}
					//验证文件上传
					for(var i in requireFileList) {
						if(requireFileList[i].length == 0) {
							common.alertMes("缺少上传文件，请检查！", 'error');
							return false;
						}
					}
					//验证文件审核状态
					result = util.validFileAudit();
					if(result == false) {
						common.alertMes("文件未通过审核，请检查！", 'error');
						return false;
					}
					result = util.validFileInvoiceAmount();
					if(result == false) {
						common.alertMes("发票未填写金额，请检查！", 'error');
						return false;
					}
					//验证发票金额
					if(invoiceAmountTotal != util.getInvoiceAmountTotal()) {
						common.alertMes("本次发票累加金额验证不通过，请检查！", 'error');
						return false;
					}
					//验证申请金额
					if(!saveRequest.applyFundCdm) {
						saveRequest.applyFundCdm = 0;
					}
					if(!saveRequest.applyFundCio) {
						saveRequest.applyFundCio = 0;
					}
					var result = util.validApplyFund(parseFloat(saveRequest.applyFundCdm) + parseFloat(saveRequest.applyFundCio));
					if(result == false) {
						return false;
					}
				}
				
				if(detailData) {
					saveRequest["id"] = rebateId;
				}
				
				saveRequest["invoiceAmountTotal"] = invoiceAmountTotal;
				saveRequest["billboardFileList"] = billboardPhoto;
				saveRequest["managerMailFileList"] = managerMailFile;
				saveRequest["scenePhotoFileList"] = scenePhotoFile;
				saveRequest["invoiceFileList"] = invoiceFile;
				saveRequest["marketingMailFileList"] = marketingMailFile;
				saveRequest["customerInteractionFileList"] = customerInteractionFile;
				saveRequest["signInFileList"] = signInFile;
				saveRequest["otherFileList"] = otherFile;
				saveRequest["marketPlanFileList"] = marketPlanFile;
				saveRequest["promotionReceiptFileList"] = promotionReceiptFile;
				saveRequest["splitMailFileList"] = splitMailFile;
				if($("#orgName").val() != null) {
					saveRequest["organizationName"] = $("#orgName").val();
				}
				if($("#fundOwnership").val() != null) {
					saveRequest["fundOwnership"] = $("#fundOwnership").val();
				}
				delete saveRequest["undefined"];
				delete saveRequest["file"];
				return saveRequest;
			},
			save: function() {
				var saveRequest = action.beforeSaveRequest(1);
				if(false == saveRequest) {
					return false;
				}
				
				req.saveRebate(saveRequest, function(data) {
					if(data.code == '0000') {
						common.alertMes("处理成功",'success');
						window.history.go(-1);
					} else {
						common.alertMes(data.message, 'error');
					}
				});
			},
			submit: function() {
				var saveRequest = action.beforeSaveRequest(2);
				if(false == saveRequest) {
					return false;
				}
					
				var confMsg = '请确认是否提交数据？';
				common.confirmMes(confMsg, function() {
					req.submitRebate(saveRequest, function(data) {
						if(data.code == '0000') {
							common.alertMes("处理成功",'success');
							window.history.go(-1);
						} else {
							common.alertMes(data.message, 'error');
						}
					});
				});
			},
			removeImg: function(attId, fileType) {
				$("#"+ attId).remove();
				var photoList = [];
				if(fileType == "billboardPhoto") {
					photoList = eval(fileType);
				} else {
					photoList = eval(fileType + "File");
				}
				
				var removeIndex = -1;
				for(var i in photoList) {
					if(photoList[i].attId == attId) {
						removeIndex = i;
						break;
					}
				}
				photoList.splice(removeIndex, 1);
				
				if(fileType == "invoice") {
					$("#invoiceAmountTotal").val(util.getInvoiceAmountTotal());
				}
				
			},
			addInvoiceAmount: function(imgId) {
				loader.showAddInvoiceAmountDialog(imgId);
			}
	};
	
	return {
		init: function() {
			loader.initRebateSave();
		},
		returnListPage: function () {
			return window.history.go(-1);
		},
		save: function () {
			return action.save();
		},
		submit: function () {
			return action.submit();
		},
		removeImg: function(attId, fileType) {
			return action.removeImg(attId, fileType);
		},
		addInvoiceAmount: function(imgId) {
			return action.addInvoiceAmount(imgId);
		}
	}
}($));