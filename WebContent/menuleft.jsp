<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="com.common.util.ContextUtil"%>
<%
   String menuListJson = ContextUtil.getCurUser().getMenuListJson();
   String mid = request.getParameter("mid");
%>
<c:set var="menuListJson" value="<%=menuListJson %>"/>
<c:set var="menuId" value="<%=mid %>"/>
<link href="${ctx }common/build-bui/css/bs3/menuleft.css" rel="stylesheet">
<style>

</style>
<div id="menuleftredbox" class="menuleftredbox"></div>
<div id="menuleft" class="menuLeftDiv"></div>
<script type="text/javascript" src="${ctx }common/build-bui/bui-min.js"></script>
<script>
BUI.use('bui/menu',function(Menu){
	var selectedParentPrev;
	var sideMenu = new Menu.SideMenu({
        render:'#menuleft',
        width:200,
        idField : 'menuId',
        itemTplRender : function(item){
            return '<div class="bui-menu-title"><span class="bui-menu-title-text mid_'+item.menuId+'">'+item.text+'</span></div>';
        },
        items : ${menuListJson}
      });
      sideMenu.render();
      sideMenu.on('menuclick', function(e){
        window.location.href = e.item.get('href');
      });
      sideMenu.on('itemclick', function(e){
          if(e.domTarget.tagName == "SPAN"){
        	   //window.location.href = e.item.get('href');
               $('#menuleftredbox').animate({top:selectedParentPrev.offset().top-114},1000);
          }
      });
      sideMenu.on('itemselected', function(e){
          var target = $(e.domTarget);
          var targetEM = target.find("em");
          var targetParentPrev = target.parent().prev();
          selectedParentPrev = targetParentPrev;
          var targetParentPrevSpan = targetParentPrev.find("span");
          var parentSpanCSS =  targetParentPrevSpan.attr("class").split(" ")[1];
          var targetParentPrevTop = targetParentPrev.offset().top;
          targetEM.css("color","#e22b25");
          targetEM.removeClass();
          targetEM.addClass("mid_"+e.item.get('menuId')+"_selected");
          target.css("background-color","rgb(242,246,248)");
          targetParentPrevSpan.css("color","#e22b25");
          targetParentPrevSpan.addClass(parentSpanCSS+"_selected");
          targetParentPrev.css("background-color","rgb(242,246,249)");
          $('#menuleftredbox').animate({top:targetParentPrevTop-114},1000);
      });
	  sideMenu.setSelectedByField(${menuId});
  });

</script>


		
		
