<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>视频播放</title>
<script src="//api.html5media.info/1.1.8/html5media.min.js"></script>
<script type="text/javascript">
var getQueryString = function(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	var r = window.location.search.substr(1).match(reg);
	if (r != null) return r[2]; return "";
}
window.onload = function(){
	//alert(getQueryString("id"));
	var video = document.getElementById("video");
	video.src = "https://plus.anta.com/video/"+getQueryString("id")+".mp4";
	video.play();
}
</script>

</head>

<body>

<video id="video" controls="controls" height="450" width="800" style="text-align:center">
    <source id="video_source" src="" type="video/mp4">
    您的浏览器不支持此种视频格式。
</video>

</body>
</html>
