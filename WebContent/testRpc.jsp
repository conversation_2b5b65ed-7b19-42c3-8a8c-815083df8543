<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.common.util.ContextUtil"%>
<!DOCTYPE html>
<%
	Long userType = WxTUser.USER_MODEL_CHEVRON.equals(ContextUtil.getCurUser().getUserModel()) ? 1l : 0;
	Long orgId = ContextUtil.getCurUser().getOrgId();
%>

<%@page import="com.sys.auth.model.WxTUser"%><html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>新建订单</title>
<%@include file="/common/jsp/common.jsp"%>
<link href="${ctx }common/hplus/css/animate.css" rel="stylesheet">
<style type="text/css">

</style>
<script type="text/javascript">
	var userType = <%=userType%>;
	var orgId = <%=orgId%>;
	var rpcClient = common.rpcClient;
	function testRPC() {
		var items = [];

		rpcClient.call('messagePushService.sendMessageToSpecifiedPerson',['message send test',[118507,118484]],
				function (result) {
				
				},function(error){
					common.ajaxTimeout(error);
				});
	}
</script>
</head>
<body class="gray-bg">
    <button  id="createBtn" onclick="testRPC()">测试</button>
</body>
</html>