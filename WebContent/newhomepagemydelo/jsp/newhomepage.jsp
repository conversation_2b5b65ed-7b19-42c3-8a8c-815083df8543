<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.controller.ConfigIndexPage"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="com.sys.auth.model.WxTUser"%>
<%@page import="java.util.Calendar"%>
<%@page import="java.util.List"%>
<%
	int year = 0;
	int currentYear = 0;
	int currentMonth = 12;
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		currentYear = year + 1;
	}else{
		Calendar cal = Calendar.getInstance();
		currentYear = year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;

	WxTUser curUser = ContextUtil.getCurUser();
	String userName = curUser.getChName();
	Long userId = curUser.getUserId();
	Long orgId = curUser.getOrgId();
	
	List<WxTRole> roleList = curUser.getRoleList();
	
	boolean isDistributor = false;
	String userModel = curUser.getUserModel();
	if(WxTUser.USER_MODEL_SP.equals(userModel)) {
		isDistributor = true;
	}
	
	boolean isCdmDistributor = false;
	for(WxTRole role : roleList) {
		if(role.getChRoleName().equals("Service_Partner_Admin") 
				|| role.getChRoleName().equals("Service_Partner_Manager")) {
			isCdmDistributor = true;
			break;
		}
	}
	
	boolean isCioDistributor = false;
	for(WxTRole role : roleList) {
		if(role.getChRoleName().equals("Caltex_Dealer")) {
			isCioDistributor = true;
			break;
		}
	}
	boolean isMenuPermitted = false;
	isMenuPermitted = ConfigIndexPage.isMenuPermitted("/dms/CustomersaleReport.jsp", userId);
%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>我的德乐</title>
<%@include file="/common/jsp/common.jsp"%>
<link href="${ctx }newhomepage/css/newhomepage.css?v=${version}1" rel="stylesheet">
<script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
<script type="text/javascript" src="${ctx }newhomepagemydelo/js/newhomepage.js?v=${version}2"></script>
<script type="text/javascript">
	var orgId = <%=orgId%>;
	var isMenuPermitted = <%=isMenuPermitted%>;
	var currentYear = <%=year%>;
	var lastYear = <%=lastYear%>;
	var currentMonth = <%=currentMonth %>;
	var currentQuarter = parseInt((currentMonth + 2) / 3);
	newhomepage_init.get
</script>
</head>
<body class="gray-bg">
	<div class="not-top">
		<div class="row">
			<div class="block-col" style="width:30%;">
				<div class="content-box">
					<div class="content-panel block-header">
						<i class="fa fa-star"></i>
						<span>快捷入口</span>
					</div>
					<div class="row" style="padding: 0 10px;margin-top: 0;">
						<div class="short-cut-div">
							<ul>
								<li><i class="fa fa-gift fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/material/jsp/applicationNew.jsp?pointMode=true&pointType=caltex')">德乐进货积分兑换</a></li>
								<li><i class="fa fa-bank fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/SPA/store/index.jsp')">陈列之星门店管理</a></li>
								<li><i class="fa fa-server fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/promote/jsp/promotePointOrder.jsp')">德乐市场资源订单</a></li>		
								<li><i class="fa fa-car fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/delovehicle/oil-selector/index.html')">德乐选油助手</a></li>								
                           		<li id="count" style="display:none"><i class="fa fa-bank fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/SPA/store/index.jsp#/store','/SPA/DoorMap/index.jsp')"><span id="countNum" style="color:red;text-decoration: underline"></span>&nbsp;家待与DMS匹配门店</a></li>
                            </ul>
						</div>
						<div class="short-cut-div">
							<ul>
								<li><i class="fa fa-calendar-check-o fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/promote/jsp/promoteActivityFeedbackPage.jsp')">德乐市场资源活动反馈</a></li>
								<li><i class="fa fa-download fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/sys/utils/publishPhotoPage.jsp')">经销商高清图下载</a></li>
                                <li><i class="fa fa-clipboard fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('/elites2/rebate/jsp/rebateAuditListPage.jsp')">2019精英计划资料上传</a></li>
                                <li><i class="fa fa-clipboard fa-fw" aria-hidden="true"></i><a href="#" onclick="newhomepage_init.openMenu('SPA/resource-application/index.jsp#/cio/list')">德乐店招报销资料上传</a></li>
                            	<li><i class="fa fa-gift fa-fw" aria-hidden="true"></i><a href="#"  onclick="newhomepage_init.openMenu('SPA/dsr/index.jsp#/kpi/cio')">DSR群英会绩效查看</a></li>                       	                        	
              	           		<li><i class="fa fa-gift fa-fw" aria-hidden="true"></i><a href="#"   onclick="newhomepage_init.openMenu('SPA/dsr/index.jsp#/points/cio')">DSR群英会积分查看</a></li>    
                            </ul>
						</div>
					</div>
				</div>
				<div class="content-box sys-message" style="margin-top: 15px;">
					<div class="content-panel block-header">
						<i class="fa fa-commenting"></i><span>信息公告</span>
						<a class="more-btn" href="javascript: void(0);" onclick="newhomepage_init.openUrl('${ctx }sys/push/personalMessagePage.jsp', 'mymessage', '我的消息')">更多...</a>
					</div>
					<div class="content-panel" id="sys_message"></div>
				</div>
			</div>
			<div class="block-col2" style="width: 70%;">
				<div class="content-box grid-content-box high-content-box" style="margin-right: 15px;">
					<div class="content-panel" style="margin-top: 12px;">
						<!-- <div class="content-panel new-home-grid-div">
							<table class="ui-grid-table ui-grid new-home-grid ">
								<thead>
									<tr>
										<th colspan="9" class="grid-title">
											<div><p>总销量跟踪</p></div>
										</th>
									</tr>
								</thead>
								<tbody>
								<tr >
									<th>季度</th><th>第一季度(YTD)</th><th>第二季度(YTD)</th><th>第三季度(YTD)</th><th>第四季度(YTD)</th>
								</tr>
								<tr class="digital-tr">
									<td rowspan="2" style="background-color: rgb(229,229,229);">总销量(升)</td>
									<td >目标</td>
									<td >实际</td>
									<td >目标</td>
									<td >实际</td>
									<td >目标</td>
									<td >实际</td>
									<td >目标</td>
									<td >实际</td>
								</tr>								
								<tr class="digital-tr">
									<td id="targetValue-q1"></td>
									<td id="actualValue-q1"></td>
									<td id="targetValue-q2"></td>
									<td id="actualValue-q2"></td>
									<td id="targetValue-q3"></td>
									<td id="actualValue-q3"></td>
									<td id="targetValue-q4"></td>
									<td id="actualValue-q4"></td>
								</tr>								
								<tr class="digital-tr">
									<th>完成季度任务的%</th>
									<td colspan="2" id="percent-q1"><div class="normal-text"></div></td>
									<td colspan="2" id="percent-q2"><div class="normal-text"></div></td>
									<td colspan="2" id="percent-q3"><div class="normal-text"></div></td>
									<td colspan="2" id="percent-q4"><div class="normal-text"></div></td>
								</tr>
								</tbody>
							</table>
						</div> -->
						<div class="content-panel new-home-grid-div" id="totalPerformanceInfoGrid">
							<table class="ui-grid-table ui-grid new-home-grid">
								<thead>
								<tr>
									<th colspan="13" class="grid-title">
										<div><p>总销量目标 &nbsp;&nbsp;&nbsp;&nbsp;<span id="total-sales-target">&nbsp;&nbsp;升</span></p></div>
									</th>
								</tr>
								</thead>
								<tbody>
								<tr>
									<th>季度</th><th colspan="3">第一季度</th><th colspan="3">第二季度</th><th colspan="3">第三季度</th><th colspan="3">第四季度</th>
								</tr>
							<!-- 	<tr class="digital-tr">
									<th>进货量基数目标</th>
									<td id="target-q1" colspan="3">2.00</td>
									<td id="target-q2" colspan="3">2.50</td>
									<td id="target-q3" colspan="3">3.00</td>
									<td id="target-q4" colspan="3">2.50</td>
								</tr>
								<tr class="digital-tr">
									<th>月平均进货目标</th>
									<td id="target-m1">1</td>
									<td id="target-m2">2</td>
									<td id="target-m3">3</td>
									<td id="target-m4">4</td>
									<td id="target-m5">5</td>
									<td id="target-m6">6</td>
									<td id="target-m7">7</td>
									<td id="target-m8">8</td>
									<td id="target-m9">9</td>
									<td id="target-m10">10</td>
									<td id="target-m11">11</td>
									<td id="target-m12">12</td>
								</tr> -->
								<tr class="digital-tr">
									<th>月平均进货量</th>
									<td id="actual-m1"></td>
									<td id="actual-m2"></td>
									<td id="actual-m3"></td>
									<td id="actual-m4"></td>
									<td id="actual-m5"></td>
									<td id="actual-m6"></td>
									<td id="actual-m7"></td>
									<td id="actual-m8"></td>
									<td id="actual-m9"></td>
									<td id="actual-m10"></td>
									<td id="actual-m11"></td>
									<td id="actual-m12"></td>
								</tr>
								<tr class="digital-tr">
									<th>季度实际完成进货量</th>
									<!--绿色——<div class="up">2.00</div>-->
									<!--黄色——<div class="warning">2.00</div>-->
									<!--红色——<div class="not-up">2.00</div>-->
									<td colspan="3" id="actual-q1"><div class="normal-text"></div></td>
									<td colspan="3" id="actual-q2"><div class="normal-text"></div></td>
									<td colspan="3" id="actual-q3"><div class="normal-text"></div></td>
									<td colspan="3" id="actual-q4"><div class="normal-text"></div></td>
								</tr>
								<!-- <tr class="digital-tr">
									<th>季度实际完成比例</th>
									<td colspan="3" id="percent-q1"><div class="normal-text"></div></td>
									<td colspan="3" id="percent-q2"><div class="normal-text"></div></td>
									<td colspan="3" id="percent-q3"><div class="normal-text"></div></td>
									<td colspan="3" id="percent-q4"><div class="normal-text"></div></td>
								</tr> -->
								</tbody>
							</table>
						</div>
						<div class="content-panel new-home-grid-div">
							<table class="ui-grid-table ui-grid new-home-grid quarter-grid">
								<thead>
									<tr>
										<th colspan="5" class="grid-title">
											<div><p>DMS</p></div>
										</th>
									</tr>
								</thead>
								<tbody>
								<tr class="digital-tr">
									<th></th><td>第一季度</td><td>第二季度</td><td>第三季度</td><td>第四季度</td>
								</tr>
								<tr class="digital-tr">
									<th>DMS 经销商管理系统</th>
									<td id="dmsKpiEvaluationContent-q1"></td>
									<td id="dmsKpiEvaluationContent-q2"></td>
									<td id="dmsKpiEvaluationContent-q3"></td>
									<td id="dmsKpiEvaluationContent-q4"></td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="content-panel new-home-grid-div">
							<table class="ui-grid-table ui-grid new-home-grid quarter-grid">
								<thead>
									<tr>
										<th colspan="5" class="grid-title">
											<div><p>绩效考核</p></div>
										</th>
									</tr>
								</thead>
								<tbody>
								<tr class="digital-tr">
									<th></th><td>第一季度</td><td>第二季度</td><td>第三季度</td><td>第四季度</td>
								</tr>
								<tr>
								<tr class="digital-tr">
									<th>CIO门头</th>
									<td id="doorHeadEvaluation-q1"></td>
									<td id="doorHeadEvaluation-q2"></td>
									<td id="doorHeadEvaluation-q3"></td>
									<td id="doorHeadEvaluation-q4"></td>
								</tr>
								<tr class="digital-tr">
									<th>CIO门头总数据</th>
									<td id="totalDoorHead-q1"></td>
									<td id="totalDoorHead-q2"></td>
									<td id="totalDoorHead-q3"></td>
									<td id="totalDoorHead-q4"></td>
								</tr>
								<tr class="digital-tr">
									<th>CIO市场活动</th>
									<td id="activityEvaluation-q1"></td>
									<td id="activityEvaluation-q2"></td>
									<td id="activityEvaluation-q3"></td>
									<td id="activityEvaluation-q4"></td>
								</tr>
								<tr class="digital-tr">
									<th>重点行业新客户开发</th>
									<td id="bdEvaluation-q1"></td>
									<td id="bdEvaluation-q2"></td>
									<td id="bdEvaluation-q3"></td>
									<td id="bdEvaluation-q4"></td>
								</tr>
								<tr class="digital-tr">
									<th>培训</th>
									<td id="trainingEvaluation-q1"></td>
									<td id="trainingEvaluation-q2"></td>
									<td id="trainingEvaluation-q3"></td>
									<td id="trainingEvaluation-q4"></td>
								</tr>
								<tr class="digital-tr">
									<th>KPI(总分100)</th>
									<td id="totalPoint-q1"></td>
									<td id="totalPoint-q2"></td>
									<td id="ttotalPoint-q3"></td>
									<td id="totalPoint-q4"></td>
								</tr>
								</tbody>
							</table>
						</div>
						
						<div class="content-panel new-home-grid-div">
							<table class="ui-grid-table ui-grid new-home-grid quarter-grid">
								<thead>
									<tr>
										<th colspan="5" class="grid-title">
											<div><p>本地销售/营销</p></div>
										</th>
									</tr>
								</thead>
								<tbody>
								<tr class="digital-tr">
									<th>季度</th>
									<td>第一季度</td>
									<td>第二季度</td>
									<td>第三季度</td>
									<td>第四季度</td>
								</tr>
								<tr class="digital-tr">
									<th>本地销售营销</th>
									<td><div id="localMtkEvaluationContent-q1"></div></td>
									<td><div id="localMtkEvaluationContent-q2"></div></td>
									<td><div id="localMtkEvaluationContent-q3"></div></td>
									<td><div id="localMtkEvaluationContent-q4"></div></td>
								</tr>
								</tbody>
							</table>
						</div>
						
						<div class="content-panel grid-desc">
							温馨提示：上述信息每天更新一次，销量数据为信息更新时公司已发货的数字。此外，由于月末5个工作日为工厂发货及财务关账高峰，敬请经销商提前下单，尤其是季度末的最后一个月，以免订单无法发货并如期入账而影响当月（当季）销量统计。
						</div>
					</div>
				</div>
			</div>
			<div class="block-col" style="width:30%;">
				<div class="content-box system-support big-content-box" style="margin-top: 15px;">
					<div class="content-panel block-header">
						<i class="fa fa-question-circle"></i><span>系统支援</span>
					</div>
					<div class="content-panel support-item">
						<i class="fa fa-phone"></i>技术服务热线（系统报错及操作）：
						<a href="tel:************">************</a>
					</div>
					<div class="content-panel support-item">
						<i class="fa fa-phone"></i>积分服务热线（礼品及订单咨询）：
						<a href="tel:************">************</a>
					</div>
					<div class="content-panel support-item">
						服务热线工作时间：工作日 9:00 - 17:00
					</div>
				</div>
			</div>
			<div class="block-col2" style="width: 70%;">
				<div class="content-box big-content-box" style="margin-top: 15px;width: calc(57% - 15px);float: left;">
					<div class="content-panel block-header">
						<i class="fa fa-bar-chart"></i>
						<span><%=year%> 营销基金及IVI奖励</span>
					</div>
					<div class="" style="height:245px;">
						<div class="echarts" id="marketing-ivi-bar" style="height:100%;"></div>
					</div>
					<div class="content-panel chart-text">
						<span id="month-total-text"></span>
					</div>
				</div>
				<div class="content-box big-content-box" style="margin-top: 15px;margin-left: 15px;width: calc(43% - 15px);clear:inherit;float: left;">
					<div class="content-panel block-header">
						<i class="fa fa-cny"></i><span><%=year%> 累计所得资源概况</span>
					</div>
					<div class="content-panel info-block themes-evaluation info-base">
					<table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table ui-grid resources-situation" style="width: 100%;">
						<thead class="ui-grid-data-row tr-row2">
						<tr>
							<th></th>
							<th class="td-title"><div class="td-title">累计</div></th>
							<th class="td-title"><div class="td-title">已兑换</div></th>
						</tr>
						</thead>
						<tbody>
						<tr class="ui-grid-data-row tr-row2">
							<td valign="middle" class="ui-grid-td plain-td text-center">进货积分（分）</td>
							<td id="caltexPoint-total-point" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							<td id="caltexPoint-left-point" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
						</tr>
						</tbody>
					</table>
				</div>
					<div class="content-panel info-block themes-evaluation info-base">
						<table cellspacing="0" cellpadding="0" border="0" class="ui-grid-table ui-grid resources-situation" style="width: 100%;">
							<thead class="ui-grid-data-row tr-row1">
							<tr>
								<th></th>
								<th class="td-title"><div class="td-title">累计(元)</div></th>
							</tr>
							</thead>
							<tbody>
							<tr class="ui-grid-data-row tr-row1">
								<td valign="middle" class="ui-grid-td plain-td text-center">POSM</td>
								<td id="cioPosm-num" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							</tr>	
							<tr class="ui-grid-data-row tr-row1">
								<td valign="middle" class="ui-grid-td plain-td text-center">店招</td>
								<td id="cioSignage-num" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							</tr>	
							<tr class="ui-grid-data-row tr-row1">
								<td valign="middle" class="ui-grid-td plain-td text-center">促销</td>
								<td id="cioPromotion-num" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							</tr>	
							<tr class="ui-grid-data-row tr-row1">
								<td valign="middle" class="ui-grid-td plain-td text-center">资源包</td>
								<td id="cioResourcePackage-num" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							</tr>	
							<tr id="cioPilotDiv" class="ui-grid-data-row tr-row1">
								<td valign="middle" class="ui-grid-td plain-td text-center">忠诚度计划</td>
								<td id="cioPilot-num" valign="middle" class="ui-grid-td plain-td text-center"><span></span></td>
							</tr>						
							</tbody>
						</table>
					</div>
					<!--  <div class="content-panel grid-desc" >-->
					<!--  	<p style="color:red;">由于线下数据收集同步滞后，其中POSM,促销支持和店招只更新到2019年4月底的数据。</p>-->
					<!--  </div>-->
				</div>
			</div>
	</div>
	
		<div class="subject-indexes-wrapper">
			<ul>
			<c:if test="<%=isDistributor && isCdmDistributor %>">
				<li onclick="newhomepage_init.openMenu('/newhomepage/index.do');">
					<%=currentYear %> 我的金富力
				</li>
				<li onclick="newhomepage_init.openMenu('/newhomepage/index.do', '/newhomepage/index.do?year=<%=currentYear - 1 %>', '');">
					<%=currentYear - 1 %> 我的金富力
				</li>
			</c:if>
			<c:if test="<%=isDistributor && isCioDistributor %>">
				<li onclick="newhomepage_init.openMenu('/newhomepagemydelo/jsp/newhomepage.jsp');" class='<%=currentYear == year ? "active" : "" %>'>
					<%=currentYear %> 我的德乐
				</li>
				<li onclick="newhomepage_init.openMenu('/newhomepagemydelo/jsp/newhomepage.jsp', '/newhomepagemydelo/jsp/newhomepage.jsp?year=<%=currentYear - 1 %>', '');" class='<%=currentYear > year ? "active" : "" %>'>
					<%=currentYear - 1 %> 我的德乐
				</li>
			</c:if>
			</ul>
		</div>
</body>
</html>