$(function () {
    save_widget.init();
});
function returnToList(){
	window.location = common.ctx + 'cdm_mkt/jsp/' + _fromPage + '.jsp?type=' + pageInitType + '&channel=' + salesChannel;
}
var storeMktTypeCtrl = null;


var save_widget = (function ($) {

    var salesChannel = 'C&I',

        detailData = null,
        workshopId = 0,
        partnerId = 0,
        contractYear = '-1',
        supplier = '',

        avgVolumeGrid = null,
        avgVolumeGridStore = null,
        targetVolumeGrid = null,
        targetVolumeGridStore = null,

        mktTypeSelect = null,
        trainingSelect = null,
        flagshipStoreSelect = null,
        applyStoreSignTypeSelect = null,
        signageStyleSelect = null,
        contractYearsSelect = null,
        supplierSelect = null,
        overBudgetSelect = null,

        signStylePic = null,

        storeSelectDialog = null,
        storeStore = null,
        storeGrid = null,

        orgSelect = null,
        orgStore = null,

        requireFileList = null,

        SearchProductList = [],
        ProductData = [],
        ProductDataStore = null,
        isDMSDoor = false, 
        newProducts = false,
        includeDmsWorkshop = null,
        storeMktTypeHandler = null

    req = {},
        util = {},
        loader = {},
        action = {};
    function updateDmsUnmatch(){
    	if(includeDmsWorkshop == 1 && $("#workShopId").val() && !isDMSDoor){
    		$('#dmsUnmatchedDescDiv').removeClass('hide-important');
    		util.formSetValidationRule(form, "dmsUnmatchedDesc", false);
    	}else{
    		$('#dmsUnmatchedDescDiv').addClass('hide-important');
    		util.formRemoveValidationRule(form, "dmsUnmatchedDesc", "required");
    	}
    }
    function emptyWorkshop(){
    	$('#workShopId').val('');
    	$('#storeNameDoor').val('');
    	$('#storeAddressDoor').val('');
    	$('#storeContectPersonDoor').val('');
    	$('#storeContectPhoneDoor').val('');
    	$('#storeCategory').val('');
    	$('#seatsNum').val('');
    	isDMSDoor = false;
    }
    req = {
        save: function (saveRequest, callback) {
            LoadMask.show();
            $.ajax({
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                url: common.ctx + 'mkt/cio/save.do',
                data: JSON.stringify(saveRequest),
                success: function (data) {
                    LoadMask.hide();
                    $.isFunction(callback) && callback(data);
                }
            });
        },
        submit: function (saveRequest, callback) {
            LoadMask.show();
            $.ajax({
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                url: common.ctx + 'mkt/cio/submit.do',
                data: JSON.stringify(saveRequest),
                success: function (data) {
                    LoadMask.hide();
                    $.isFunction(callback) && callback(data);
                }
            });
        },
        getDetail: function (id, callback) {
            var params = {
                mktId: id,
                t: (new Date()).getTime()
            };
            $.get(common.ctx + "mkt/cio/detail.do", params, function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');
                }
            });
        },
        getSotrePage: function (params, callback) {
            $.ajax({
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                url: common.ctx + 'workshop/data.do',
                data: JSON.stringify(params),
                success: function (data) {
                    $.isFunction(callback) && callback(data);
                },
            });
        },
        getOrgList: function (callback) {
            LoadMask.show();
            var params = {
                t: (new Date()).getTime(),
                salesChannel: salesChannel
            };
            $.get(common.ctx + "mkt/org-list.do", params, function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');

                }
            });
        },
        getAvgVolumeList: function (workshopId, callback) {
            LoadMask.show();
            var params = {
                workshopId: workshopId,
                t: (new Date()).getTime()
            };
            $.get(common.ctx + "mkt/get-avg-volume-list.do", params, function (data) {
                LoadMask.hide();
                if (data.code == '0000') {
                    $.isFunction(callback) && callback(data.data);
                } else {
                    common.alertMes(data.message, 'error');

                }
            });
        }
    },
        util = {
            getOilCategoryText: function (code) {
                if (code == "FullySyn") return "全合成";
                else if (code == "BlendSyn") return "半合成";
                else if (code == "Formula") return "方程式";
                else if (code == "ClassicExtra") return "超级精选";
                else return "其它";
            },
            formSetValidationRule: function (form, fieldName, isNumber) {
                var field = form.getField(fieldName);
                if (field) {
                    field.addRule('required', true, '必填项');
                    if (isNumber)
                        field.addRule('number', true, '请输入数字');
                }
                return field;
            },
            formRemoveValidationRule: function (form, fieldName, ruleName) {
                var field = form.getField(fieldName);
                if (field) {
                    field.removeRule(ruleName);
                }
                return field;
            },
            getFormFieldValue: function (saveRequest, fieldName) {
                if ($("#" + fieldName).val() != undefined && $.trim($("#" + fieldName).val()) != "")
                    saveRequest[fieldName] = $.trim($("#" + fieldName).val());
            },
            /*checkTargetVolumeInput: function () {
                var ordersCode = ["FullySyn", "BlendSyn", "Formula", "ClassicExtra", "Others"];
                var result = 0;
                for (var j = 0; j < ordersCode.length; j++) {
                    var pack = $('#smallPack' + ordersCode[j]).val();
                    if (pack == undefined || pack == '') {
                        common.alertMes("月预计销量小包装中有留空的信息，必须全部输入！", 'error');
                        result = -1;
                        break;
                    }
                    if (!util.isNumber(pack)) {
                        common.alertMes("月预计销量小包装中有非法的信息，必须全部为数值！", 'error');
                        result = -2;
                        break;
                    }
                    pack = $('#largePack' + ordersCode[j]).val();
                    if (pack == undefined || pack == '') {
                        common.alertMes("月预计销量大包装中有留空的信息，必须全部输入！", 'error');
                        result = -3;
                        break;
                    }
                    if (!util.isNumber(pack)) {
                        common.alertMes("月预计销量大包装中有非法的信息，必须全部为数值！", 'error');
                        result = -4;
                        break;
                    }
                }
                return result;
            },*/
            isNumber: function (val) {
                var regPos = /^\d+(\.\d+)?$/;
                if (regPos.test(val)) {
                    return true;
                } else {
                    return false;
                }
            },
            getPostData: function () {
                var saveRequest = $.extend({}, form.getRecord(), {});
                if(storeMktTypeHandler.save(saveRequest, util) === false){
                	return false;
                }
                //TODO
                $('#saveForm input,textarea').each(function(){
                	if(this.name){
                		saveRequest[this.name] = this.value;
                	}
                });
                util.getFormFieldValue(saveRequest, "orgId");
                util.getFormFieldValue(saveRequest, "orgName");
                util.getFormFieldValue(saveRequest, "workShopId");
                util.getFormFieldValue(saveRequest, "storeNameDoor");
                util.getFormFieldValue(saveRequest, "storeAddressDoor");
                util.getFormFieldValue(saveRequest, "storeContectPersonDoor");
                util.getFormFieldValue(saveRequest, "storeContectPhoneDoor");
                util.getFormFieldValue(saveRequest, "storeCategory");
                util.getFormFieldValue(saveRequest, "seatsNum");
                util.getFormFieldValue(saveRequest, "overBudget");
                util.getFormFieldValue(saveRequest, "materielMoney");
                util.getFormFieldValue(saveRequest, "overBudgetMoney");
                util.getFormFieldValue(saveRequest, "makespan");
                util.getFormFieldValue(saveRequest, "supplier");
                util.getFormFieldValue(saveRequest, "meetingStartDate");
//                util.getFormFieldValue(saveRequest, "storeMktType"); //TODO 保存店招类型
                saveRequest["storeMktTypeText"] = storeMktTypeCtrl.getText();
                
                var mktType = mktId == null ? saveRequest["mktType"] : detailData.mktType;
                var localMake = mktId == null ? saveRequest["localMake"] : util.getFieldValue(detailData.fieldList, "localMake");
                if (mktType == "STORE_FRONT" || mktType == "STORE_IN_STORE") {
                	if(includeDmsWorkshop != 1 || isDMSDoor){
                		//TODO
                		delete saveRequest.dmsUnmatchedDesc
                	}
                    if (ProductDataStore) {
                        var volumes = ProductDataStore.getResult();
                        volumes = volumes.filter(function (item) {
                            return item.productName !== '';
                        });
                        saveRequest['volumes'] = volumes;
                    }

                    if (localMake === '1') {
                        saveRequest.signLong = saveRequest.signLong || 0;
                        saveRequest.signWide = saveRequest.signWide || 0;
                        saveRequest.signArea = (parseFloat(saveRequest.signLong) * parseFloat(saveRequest.signWide)).toFixed(2) || 0;
                        saveRequest.moneyCost = saveRequest.moneyCost || 0;
                        delete saveRequest.supplier
                    } else if (localMake === '0') {
                        delete saveRequest.signLong;
                        delete saveRequest.signWide;
                        delete saveRequest.signArea;
                        delete saveRequest.moneyCost;
                    } else {
                        delete saveRequest.supplier;
                        delete saveRequest.signLong;
                        delete saveRequest.signWide;
                        delete saveRequest.signArea;
                        delete saveRequest.moneyCost;
                    }
                } else if (mktType == "STORE_FRONT_REIMBURSE") {

                } else if (mktType == "STORE_FRONT_OFFER") {
                    delete saveRequest.materielMoney;
                    delete saveRequest.contractYear;
                    if (localMake === '1') {
                        delete saveRequest.signLong;
                        delete saveRequest.signWide;
                        delete saveRequest.signArea;
                        delete saveRequest.moneyCost;
                        delete saveRequest.designDesc;
                    }
                }

                if (null != mktId) {
                    saveRequest["id"] = mktId;
                }

                if (detailData != null &&
                    (detailData.mktType == "STORE_FRONT_REIMBURSE"
                        || detailData.mktType == "SEMINAR_REIMBURSE"
                        || detailData.mktType == "STORE_FRONT_OFFER")) {
                    saveRequest["mktType"] = detailData.mktType;
                    if (saveRequest['overBudget'] == '1') {
                        var moneyCost = parseFloat(util.getFieldValue(detailData.fieldList, "moneyCost"));
                        saveRequest['materielMoney'] = (parseFloat(saveRequest['overBudgetMoney']) + moneyCost).toFixed(2);
                        if (isNaN(saveRequest['materielMoney'])) saveRequest['materielMoney'] = '0.0';
                    } else {
                        saveRequest['overBudgetMoney'] = '0.0';
                    }
                }

                saveRequest["salesChannel"] = salesChannel;
                delete saveRequest["undefined"];

                //生成字段JSON
                var fieldList = [];
                for (var i in saveRequest) {
                    if (i != 'id' && i != 'mktType' && i != 'salesChannel'
                        && i != 'orgId' && i != 'orgName') {
                        fieldList.push({
                            name: i,
                            value: saveRequest[i],
                        });
                    }
                }
                //移除多余字段
                for (var i in saveRequest) {
                    if (i != 'id' && i != 'mktType' && i != 'salesChannel'
                        && i != 'orgId' && i != 'orgName') {
                        delete saveRequest[i];
                    }
                }

                saveRequest["fieldList"] = fieldList;
                saveRequest["fileList"] = file_common.getFileList();

                return saveRequest;
            },
            beforeSaveRequest: function (checkValid) {
                if (checkValid) {
                    form.valid();
                    if (form.isValid()) {
                        return util.getPostData();
                    } else {
                        return false;
                    }
                } else {
                    return util.getPostData();
                }
            },
            initFieldList: function (fieldList) {
                for (var i = 0; i < fieldList.length; i++) {
                    var fieldName = fieldList[i].fieldName;
                    var fieldValue = fieldList[i].fieldValue;
                    if(fieldName == 'overBudget'){
                    	//TODO CIO不能超预算
                    	continue;
                    }
                    if (pageType != "update" || (detailData && (detailData.mktType != 'STORE_FRONT' && detailData.mktType != 'STORE_IN_STORE'))) {
                        if (fieldName == "installationRequirementBaseboard" ||
                            fieldName == "installationRequirementBackHolder" ||
                            fieldName == "installationRequirementSteelHolder" ||
                            fieldName == "installationRequirementObliqueHolder") {
                            fieldValue = fieldValue == 1 ? '需要' : '不需要'
                        }
//                        if (fieldName == "signageStyle") {
//                            fieldValue = dic_common.getDicFieldValue("cio.mkt.signType", fieldValue);
//                        }
                        if (fieldName == 'installationRequirementBaseboard' ||
                            fieldName == 'installationRequirementBackHolder' ||
                            fieldName == 'installationRequirementSteelHolder' ||
                            fieldName == 'installationRequirementObliqueHolder' ||
                            fieldName == 'signageStyle') {
                            $("#" + fieldName).html(fieldValue);
                            continue;
                        }
                    } else {
                        if (fieldName == 'viewSignType') {
                            $("#" + fieldName).val(fieldValue);
                            continue;
                        }
                    }

                    if ($("#" + fieldName).is('input, textarea')) {
                        $("#" + fieldName).val(fieldValue);
                        continue;
                    }
                        
                    if (fieldName != null && (fieldName.substring(0, 9) == "smallPack" || fieldName.substring(0, 9) == "largePack")) {
                        $("#" + fieldName).val(fieldValue);
                        if (fieldName == "workShopId") workShopId = fieldValue;
                        if (fieldValue == "contractYear") contractYear = fieldValue;
                        if (fieldValue == "supplier") supplier = fieldValue;
                    } else {
                        form.setFieldValue(fieldName, fieldValue);
                    }
                    //console.log($("#" + fieldName).val() + "      " + form.getFieldValue(fieldName));
                }
                updateDmsUnmatch();
            },
            getFieldValue: function (fieldList, fieldName) {
                var value = "";
                for (var i = 0; i < fieldList.length; i++) {
                    var field_name = fieldList[i].fieldName;
                    var fieldValue = fieldList[i].fieldValue;
                    if (field_name != null && fieldName == field_name) {
                        value = fieldList[i].fieldValue;
                        break;
                    }
                }
                return value;
            },

            getTargetVolumeTotal: function (type) {
                var sumSmallPack = 0.0, sumLargePack = 0.0;
                var fieldList = detailData != null ? detailData.fieldList : [];
                for (var i = 0; i < fieldList.length; i++) {
                    var fieldName = fieldList[i].fieldName;
                    var fieldValue = fieldList[i].fieldValue;
                    if (fieldName != null) {
                        if (type == "small" && fieldName.substring(0, 9) == "smallPack") {
                            sumSmallPack += parseFloat(fieldValue)
                        } else if (type == "large" && fieldName.substring(0, 9) == "largePack") {
                            sumLargePack += parseFloat(fieldValue)
                        }
                    }
                }
                var sumPack = type == "small" ? sumSmallPack : sumLargePack;
                if (isNaN(sumPack)) sumPack = 0;
                $("#" + type + "PackTotal").val(sumPack.toFixed(2));
                return sumPack;
            },
            getLoaderUri: function (mktType) {
                var uri = null;
                if ('STORE_FRONT' == mktType || 'STORE_IN_STORE' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/storeFront.jsp";
                } else if ('STORE_FRONT_OFFER' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/storeFrontOffer.jsp";
                } else if ('OPEN_GIFT' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/openGift.jsp";
                } else if ('SEMINAR' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/seminar.jsp";
                } else if ('TRAINING' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/training.jsp";
                } else if ('STORE_FRONT_REIMBURSE' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/storeFrontReimburse.jsp";
                } else if ('SEMINAR_REIMBURSE' == mktType) {
                    uri = common.ctx + "cdm_mkt/cio/save/seminarReimburse.jsp";
                }
                return uri + "?t=" + (new Date()).getTime();
            },
            initFieldDetail: function (fieldList) {
                for (var i = 0; i < fieldList.length; i++) {
                    var fieldName = fieldList[i].fieldName;
                    var fieldValue = fieldList[i].fieldValue;

//                    if (fieldName == "applyStoreSignType") {
//                        fieldValue = dic_common.getDicFieldValue("cio.mkt.sign.apply", fieldValue);
//                    }
                    if (fieldName == "flagshipStore") {
                        fieldValue = dic_common.getDicFieldValue("cio.mkt.flagship", fieldValue);
                    }
                    if (fieldName == "localMake") {
                        if (fieldValue == '1') {
//                            if (detailData && detailData.currentStep == 1) { // 供应商隐藏该元素
//                                $("#localMakeDescDiv").hide();
//                            } else {
//                                $("#localMakeDescDiv").show();
//                            }
                        } else {
                            $("#supplierDiv").show();
                        }
                        fieldValue = dic_common.getDicFieldValue("cio.mkt.flagship", fieldValue);
                    }
                    var el = $("#" + fieldName); //TODO
                    if(el.is('input,textarea')){
                    	el.val(fieldValue);
                    }else{
                        el.html(fieldValue);
                    }
                }
            },
            intRequiredFiled: function (mktType) {
                util.formSetValidationRule(form, 'orgId', false);
                util.formSetValidationRule(form, 'orgName', false);
                if (mktType == 'STORE_FRONT' || mktType == 'STORE_IN_STORE') {
                    util.formSetValidationRule(form, 'storeNameDoor', false);
                    util.formSetValidationRule(form, 'storeAddressDoor', false);
                    util.formSetValidationRule(form, 'storeCategory', false);
                    util.formSetValidationRule(form, 'storeContectPersonDoor', false);
                    util.formSetValidationRule(form, 'storeContectPhoneDoor', true);
                    util.formSetValidationRule(form, 'seatsNum', true);
                    // util.formSetValidationRule(form, 'flagshipStore', false);
                    util.formSetValidationRule(form, 'localMake', false);
                    util.formSetValidationRule(form, 'contractYear', false);
                    util.formSetValidationRule(form, 'smallPackFullySyn', true);
                    util.formSetValidationRule(form, 'applyReason', false);
//                    util.formSetValidationRule(form, 'supplier', false);

                    if (mktType == 'STORE_FRONT') {
                        util.formSetValidationRule(form, 'applyStoreSignType', false);
                        $("#applyStoreSignTypeGroup").show();
                    } else if (mktType == 'STORE_IN_STORE') {
                        $("#applyStoreSignTypeGroup").hide();
                        util.formRemoveValidationRule(form, 'applyStoreSignType', "required");
                    }

                    //liyu
                    /*var monthlyStock = form.getField('monthlyStock');
                    monthlyStock.addRule('required', true, '必填项');
                    monthlyStock.addRule('number', true, '请输入数字');

                    var snAboveStock = form.getField('snAboveStock');
                    snAboveStock.addRule('required', true, '必填项');
                    snAboveStock.addRule('number', true, '请输入数字');*/
                } else if ('STORE_FRONT_OFFER' == mktType) {
                    util.formSetValidationRule(form, 'moneyCost', true);
//                    util.formSetValidationRule(form, 'signLong', true);
//                    util.formSetValidationRule(form, 'signWide', true);
//                    util.formSetValidationRule(form, 'signArea', true);

                    if (detailData && detailData.storeInStore == 1) { //店中店申请不需要门头信息
//                        util.formRemoveValidationRule(form, "signLong", "required");
//                        util.formRemoveValidationRule(form, "signLong", "number");
//                        util.formRemoveValidationRule(form, "signWide", "required");
//                        util.formRemoveValidationRule(form, "signWide", "number");
//                        util.formRemoveValidationRule(form, "signArea", "required");
//                        util.formRemoveValidationRule(form, "signArea", "number");
                        util.formRemoveValidationRule(form, "applyStoreSignType", "required");
                        util.formSetValidationRule(form, 'designDesc', false);
                        $(".signDiv").hide();
                        $("#applyStoreSignTypeGroup").hide();
                        $("#designDescDiv").show();
//                        $("#originalDoorLabel").text("原店中店照片");
                    } else {
                        $("#applyStoreSignTypeGroup").show();
                        $(".signDiv").show();
                    }
                } else if ('STORE_FRONT_REIMBURSE' == mktType) {
                    util.formSetValidationRule(form, 'materielMoney', true);
                    util.formSetValidationRule(form, 'makespan', false);
                    util.formSetValidationRule(form, 'overBudget', false);
                    if (detailData && detailData.storeInStore == 1) {
                        $(".signDiv").hide();
//                        $("#originalDoorLabel").text("原店中店照片");
                    } else {
                        $(".signDiv").show();
                    }
                } else if ('SEMINAR' == mktType) {
                    util.formSetValidationRule(form, 'meetingStartDate', false);
                    util.formSetValidationRule(form, 'meetingEndDate', false);
                    util.formSetValidationRule(form, 'meetingPlace', false);
                    util.formSetValidationRule(form, 'meetingAddress', false);
                    util.formSetValidationRule(form, 'applyReason', false);
                    util.formSetValidationRule(form, 'customerNumer', true);
                    util.formSetValidationRule(form, 'moneyCost', true);
                    util.formSetValidationRule(form, 'purchasesVolume', true);
                    util.formSetValidationRule(form, 'purchasesSNVolume', true);
                } else if ('SEMINAR_REIMBURSE' == mktType) {
                    util.formSetValidationRule(form, 'actualVolume', true);
                }
            },
            getFileListByType: function (requireFileList, fileType) {
                var filelist = [];
                for (var i in requireFileList) {
                    if (requireFileList[i] && requireFileList[i].fileType == fileType) {
                        filelist.push(requireFileList[i]);
                    }
                }
                return filelist;
            }
        },
        loader = {
            initGrid: function (Grid, renderId, dataStore, headerTitle) {
                var dataGrid;
                var columns = [{
                    title: headerTitle,
                    sortable: false,
                    dataIndex: 'compositeType',
                    width: '40%',
                    renderer: function (value, obj, index) {
                        return value;
                    }
                }, {
                    title: '小包装（L）',
                    sortable: false,
                    dataIndex: 'smallPack',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return value;
                        } else {
                            return "0";
                        }
                    }
                }, {
                    title: '大包装（L）',
                    sortable: false,
                    dataIndex: 'largePack',
                    width: '28%',
                    renderer: function (value, obj, index) {
                        if (value != undefined && "" != value && !isNaN(value)) {
                            return value;
                        } else {
                            return "0";
                        }
                    }
                }
                ];

                dataGrid = new Grid.Grid({
                    render: renderId,
                    columns: columns,
                    store: dataStore,
                    bbar: false,
                    loadMask: true, // 加载数据时显示屏蔽层
                    width: '100%'
                });
                return dataGrid;
            },
            getVolumeGridData: function (data) {
                var result = [];
                var gridData = [];
                var smallPackSum = 0, largePackSum = 0;
                if (data.volumes) {
                    for (var i = 0; i < data.volumes.length; i++) {
                        var volume = data.volumes[i];
                        smallPackSum += volume.smallPack;
                        largePackSum += volume.largePack;
                        //volume.smallPack = (volume.smallPack ).toFixed(2);
                        //volume.largePack = (volume.largePack ).toFixed(2);
                        gridData.push(volume);
                    }
                    gridData = data.volumes;
                    var orders = ["全合成", "半合成", "方程式", "超级精选", "其它", "总计"];
                    for (var j = 0; j < orders.length; j++) {
                        result.push(loader.findVolumeDataGridRow(gridData, orders[j]));
                    }
                }
                /*result.push({
                    compositeType: "总计",
                    smallPack: smallPackSum.toFixed(2),
                    largePack: largePackSum.toFixed(2)
                });*/
                return result;
            },
            findVolumeDataGridRow: function (gridData, itemName) {
                var itemExsists = false;
                var result = [];
                for (var i = 0; i < gridData.length; i++) {
                    if (itemName == gridData[i].compositeType) {
                        result = gridData[i];
                        itemExsists = true;
                        break;
                    }
                }
                if (!itemExsists) {
                    result = {
                        compositeType: itemName,
                        smallPack: 0.00,
                        largePack: 0.00
                    };
                }
                return result;
            },
            /*getTargetVolumeSum: function (type) {
                var ordersCode = ["FullySyn", "BlendSyn", "Formula", "ClassicExtra", "Others"];
                var sumPack = 0;
                for (var j = 0; j < ordersCode.length; j++) {
                    var pack = $('#' + type + 'Pack' + ordersCode[j]).val();
                    if (pack != undefined && pack != "" && util.isNumber(pack)) {
                        //if (!util.isNumber(pack)) pack = 0;
                        sumPack += parseFloat(pack);
                    }
                }
                $("#" + type + "PackTotal").val(sumPack.toFixed(2));
                return sumPack;
            },
            getTargetVolumeGridData: function (data) {
                var result = [];
                var gridData = [];
                var orders = ["全合成", "半合成", "方程式", "超级精选", "其它"];
                var ordersCode = ["FullySyn", "BlendSyn", "Formula", "ClassicExtra", "Others"];
                for (var j = 0; j < orders.length; j++) {
                    result.push({
                        compositeType: orders[j],
                        smallPack: 'smallPack' + ordersCode[j],
                        largePack: 'largePack' + ordersCode[j]
                    });
                }
                result.push({
                    compositeType: "总计",
                    smallPack: 'smallPackTotal',
                    largePack: 'largePackTotal'
                });
                return result;
            },
            getTargetVolumeGridDataView: function (data) {
                var result = [];
                var gridData = [];
                var orders = ["全合成", "半合成", "方程式", "超级精选", "其它"];
                var ordersCode = ["FullySyn", "BlendSyn", "Formula", "ClassicExtra", "Others"];
                for (var j = 0; j < orders.length; j++) {
                    result.push({
                        compositeType: orders[j],
                        smallPack: loader.getTargetVolumeFieldValue(data, 'smallPack' + ordersCode[j]),
                        largePack: loader.getTargetVolumeFieldValue(data, 'largePack' + ordersCode[j])
                    });
                }

                var smallPackSum = 0, largePackSum = 0;
                for (var i = 0; i < result.length; i++) {
                    var volume = result[i];
                    smallPackSum += parseFloat(volume.smallPack);
                    largePackSum += parseFloat(volume.largePack);
                }
                if (smallPackSum == undefined || smallPackSum == null) smallPackSum = 0.0;
                if (largePackSum == undefined || largePackSum == null) largePackSum = 0.0;
                result.push({
                    compositeType: "总计",
                    smallPack: smallPackSum.toFixed(2),
                    largePack: largePackSum.toFixed(2)
                });
            },*/
            getTargetVolumeFieldValue: function (data, field) {
                var result = '';
                for (var i = 0; i < data.fieldList.length; i++) {
                    var fieldName = data.fieldList[i].fieldName;
                    var fieldValue = data.fieldList[i].fieldValue;
                    if (fieldName == field) {
                        result = fieldValue;
                        break;
                    }
                }
                return result;
            },
            initSave: function () {
                if (null != mktId) {
                    req.getDetail(mktId, function (data) {
                        if ("update" == pageType || "audit" == pageType) {
                            detailData = data;
                            file_common.setFileList(data.fileList);
                        }
                        loader.initMktTypeSelect();
                    });
                } else {
                    loader.renderForm();
                    loader.initMktTypeSelect();
                }
            },
            initStoreMktTypeCtrl: function(){//TODO
                var storeMktTypeDisabled = true;
                if(!detailData || detailData.mktType == "STORE_FRONT"){
                	storeMktTypeDisabled = false;
                }
                storeMktTypeCtrl = Ctrls.DicItem.init('#storeMktType', 'CIOStoreMkt.storeMktType',{
        			canEmpty: false,
        			_v: $('#storeMktType').val(),
            		ctrlOpts: {disabled: storeMktTypeDisabled},
            		events: {
            			change: function(e){
            				if(storeMktTypeCtrl._v != e.value){
            					emptyWorkshop();
            					storeMktTypeCtrl._v = e.value;
            					storeMktTypeHandler.changeFrom(util, form);
            					storeMktTypeHandler = StoreMktTypeHandlers[e.value];
            					storeMktTypeHandler.changeTo(util, form);
            				}
            			}
            		}
        		});
                storeMktTypeHandler = StoreMktTypeHandlers[$('#storeMktType').val()];
                storeMktTypeHandler.init(util, form, detailData);
            },
            initMktTypeSelect: function () {
                if (detailData != null) {
                    $("#mktType").val(detailData.mktType);
                    $("#orgId").val(detailData.orgId);
                    $("#orgName").val(detailData.orgName);
                    partnerId = detailData.partnerId;
                    if (orgSelect) orgSelect.setSelectedValue(detailData.orgId + "");
                    isDMSDoor = (detailData.dms&8) > 0
                }

                $('#mktTypeDiv').append('<span class="control-text bui-form-field" style="line-height: 28px;">店招</span>')
                $('#mktType').val('STORE_FRONT')


                var ev = {value: 'STORE_FRONT'}
                loader.renderForm();
                if (form)
                    form.clearRules();
                if (orgSelect) {
                    orgSelect.setSelectedValue(detailData ? detailData.orgId + "" : '-1');
                }
                avgVolumeGrid = null;
                targetVolumeGrid = null;

                if (!detailData) {
                    var loadUri = util.getLoaderUri(ev.value);
                    $("#divLoader").load(loadUri, function () {
                        loader.renderForm();
                        if (form) {
                            form.clearRules();
                        }

                        util.formRemoveValidationRule(form, "orgId", "required");
                        util.formRemoveValidationRule(form, "orgName", "required");
                        util.intRequiredFiled(ev.value);

                        if (ev.value == 'STORE_FRONT' || ev.value == 'STORE_IN_STORE') {
                            // loader.initFlagshipStoreSelect();
//                            loader.initApplyStoreSignTypeSelect();
//                            loader.initSignageStyleSelect();
                            loader.initLocalMakeSelect();

                            file_common.initFileUpload(mktId, "originalDoor", "originalDoorUploader", "originalDoorFileList");
                            file_common.initFileUpload(mktId, "quotation", "quotationUploader", "quotationFileList");
                            file_common.initFileUpload(mktId, "offerSeal", "offerSealUploader", "offerSealFileList");

                            if ($("#localMake").val() != undefined && $("#localMake").val() == "1") { //当地制作
                                // $("#localMakeApprovalMailDiv").show();
                                // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                            }
                            if (ev.value == "STORE_FRONT") {
//                                $("#originalDoorLabel").text("原店招照片");
                                $("#applyStoreSignTypeGroup").show();
                            }
                            /*if (ev.value == "STORE_IN_STORE") {
                                $("#originalDoorLabel").text("原店中店照片");
                                $("#applyStoreSignTypeGroup").hide();
                            }*/
                        } else if (ev.value == 'SEMINAR') {

                        } else if (ev.value == "SEMINAR_REIMBURSE") {
                            loader.initOverBudgetSelect();
                            if ($("#overBudget").val() != undefined && $("#overBudget").val() == "1") { //超过预算了
                                $("#overBudgetApprovalMailDiv").show();
                                file_common.initFileUpload(mktId, "overBudgetApprovalMail", "overBudgetApprovalMailUploader", "overBudgetApprovalMailFileList");
                            }
                        }
                        if (detailData != null) {
                            if (pageType != 'update') {
                                if (detailData.historyList && detailData.historyList.length > 0) {
                                    loader.initHistoryGrid(detailData.historyList);
                                    $("#titleFlow").show();
                                }
                            }
                            util.initFieldList(detailData.fieldList);
                            file_common.initFileList(detailData.fileList, 1);
                            action.calcArea();
                        }

                        loader.initContractYearsSelect();
                        loader.initInstallRequiredSelect('installationRequirementBaseboardDiv', 'installationRequirementBaseboard');
                        loader.initInstallRequiredSelect('installationRequirementBackHolderDiv', 'installationRequirementBackHolder');
                        loader.initInstallRequiredSelect('installationRequirementSteelHolderDiv', 'installationRequirementSteelHolder');
                        loader.initInstallRequiredSelect('installationRequirementObliqueHolderDiv', 'installationRequirementObliqueHolder');

                        if (detailData && detailData.supplier)
                            loader.initSupplierSelect(detailData.suppliers);
                        $(".activity-detail").show();

                        if (detailData) {
                            loader.renderProductTable();
                            $("#monthlyAvgVolumeGridDiv").show();
                        }
                        loader.initStoreMktTypeCtrl();
                    });
                }


                loader.initOrgList(function () {
                    if (orgSelect) {
                        //orgSelect.setSelectedValue(detailData?detailData.orgId + "":'-1');
                    }
                });
                if (detailData && (detailData.mktType == "STORE_FRONT" || detailData.mktType == "STORE_FRONT_OFFER" || detailData.mktType == "STORE_FRONT_REIMBURSE"
                    || detailData.mktType == "STORE_IN_STORE"
                    || detailData.mktType == "SEMINAR_REIMBURSE")) {

                    $("#mktTypeDiv").hide();
                    $("#mktTypeForReimburse").html(detailData.mktTypeName);
//                    $("#mktTypeForReimburse").css("display", "inline-block");

                    if (pageType == 'audit') {
                        $("#orgListDiv").hide();
                        $("#orgNameForReimburse").html(detailData.orgName);
                        $("#orgNameForReimburse").css("display", "inline-block");
                    }

                    var loadUri = util.getLoaderUri(detailData.mktType);
                    $("#divLoader").load(loadUri, function () {
                        loader.renderForm();
                        if (detailData.mktType == "STORE_FRONT_REIMBURSE") {
                            loader.initOverBudgetSelect();
                            var localMake = util.getFieldValue(detailData.fieldList, "localMake");

                            $('#orgNameForReimburse').parent().find('.required-flag').html('');

                            if (localMake == '1') {
                                $('#previewChartDiv').show();
                                
                                if (detailData.currentStep == 1) {
                                    $('#designChartDiv .required-flag').html('*');
                                    file_common.initFileUpload(mktId, "designChart", "designChartUploader", "designChartFileList");
                                    $('#applicationFormDiv').hide()
                                    $('#complianceInvoiceDiv').hide()
                                    $('#storeConfirmationDiv').hide()
                                    $('#tripleAgreementDiv').hide()
//                                    $('#localMakeDescDiv').hide();
                                    $('#paymentFromThirdPartySupplierDiv').hide()
                                } else if (detailData.currentStep == 2) {
                                    $('#designChartUploader').hide();
                                    file_common.initFileUpload(mktId, "applicationForm", "applicationFormUploader", "applicationFormFileList");
                                    file_common.initFileUpload(mktId, "complianceInvoice", "complianceInvoiceUploader", "complianceInvoiceFileList");
                                    file_common.initFileUpload(mktId, "storeConfirmation", "storeConfirmationUploader", "storeConfirmationFileList");
                                    file_common.initFileUpload(mktId, "tripleAgreement", "tripleAgreementUploader", "tripleAgreementFileList");
                                    file_common.initFileUpload(mktId, "paymentFromThirdPartySupplier", "paymentFromThirdPartySupplierUploader", "paymentFromThirdPartySupplierFileList");
                                    file_common.initFileUpload(mktId, "comparePic", "comparePicUploader", "comparePicFileList");
                                }

                                $('#signageContentRequirementDiv').show();
//                                $('#installationRequirementGroupDiv').show();
//                                $('#signageStyleGroupDiv').show();
                            } else {
                                $('#designChartUploader').hide();
                                $('#comparePicDiv').show();
                            	if(detailData.currentStep == 1){//TODO leozli
                                    file_common.initFileUpload(mktId, "applicationForm", "applicationFormUploader", "applicationFormFileList");
                                    file_common.initFileUpload(mktId, "complianceInvoice", "complianceInvoiceUploader", "complianceInvoiceFileList");
                                    file_common.initFileUpload(mktId, "storeConfirmation", "storeConfirmationUploader", "storeConfirmationFileList");
                                    file_common.initFileUpload(mktId, "tripleAgreement", "tripleAgreementUploader", "tripleAgreementFileList");
                                    file_common.initFileUpload(mktId, "comparePic", "comparePicUploader", "comparePicFileList");
                                    file_common.initFileUpload(mktId, "paymentFromThirdPartySupplier", "paymentFromThirdPartySupplierUploader", "paymentFromThirdPartySupplierFileList");
                            	}else{
                                    $('#applicationFormUploader').hide();
                                    $('#complianceInvoiceUploader').hide();
                                    $('#storeConfirmationUploader').hide();
                                    $('#tripleAgreementUploader').hide();
                                    $('#paymentFromThirdPartySupplierUploader').hide();
                            	}
                            }
                            if (null != detailData.realtedDetailVo.realtedDetailVo) {
                                util.initFieldDetail(detailData.realtedDetailVo.realtedDetailVo.fieldList);
                                file_common.initFileList(detailData.realtedDetailVo.realtedDetailVo.fileList, 0);
                            }
                            if (null != detailData.realtedDetailVo) {
                                util.initFieldDetail(detailData.realtedDetailVo.fieldList);
                                file_common.initFileList(detailData.realtedDetailVo.fileList, 0);
                            }
                            $("#orgListDiv").hide();
                            $("#orgNameForReimburse").html(detailData.orgName);
                            $("#orgNameForReimburse").css("display", "inline-block");
                            $("#makespan").val(moment().format('YYYY-MM-DD'));


                            if (localMake != undefined && localMake != "") {
                                // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                                // $("#localMakeApprovalMailDiv").show();
                            }

                        } else if (detailData.mktType == "STORE_FRONT_OFFER") {
                            var localMake = util.getFieldValue(detailData.fieldList, "localMake");
                            if (localMake == '1') {
                                file_common.initFileUpload(mktId, "previewChart", "previewChartUploader", "previewChartFileList");
                                $('#signLong').attr("readonly", "readonly").attr("disabled", "disabled");
                                $('#signWide').attr("readonly", "readonly").attr("disabled", "disabled");
                                $('#signArea').attr("readonly", "readonly").attr("disabled", "disabled");
                                $('#moneyCost').attr("readonly", "readonly").attr("disabled", "disabled");
                                $('#previewChartDiv').show();

                                $('#signageContentRequirementDiv').show();
//                                $('#installationRequirementGroupDiv').show();
//                                $('#signageStyleGroupDiv').show();

                                $('#quotationUploader').hide();
                                $('#offerSealUploader').hide();
                            } else {
                                file_common.initFileUpload(mktId, "designChart", "designChartUploader", "designChartFileList");
                                $('#designChartDiv').show();
                                $('#quotationDiv .required-flag').html('*');
                                $('#offerSealDiv .required-flag').html('*');
                                $('#previewChartDiv .required-flag').html('*');
                            }
                            file_common.initFileUpload(mktId, "quotation", "quotationUploader", "quotationFileList");
                            file_common.initFileUpload(mktId, "offerSeal", "offerSealUploader", "offerSealFileList");
                            $('#quotationDiv').show();
                            $('#offerSealDiv').show();

                            if (detailData.realtedDetailVo != null) {
                                util.initFieldDetail(detailData.realtedDetailVo.fieldList);
                                file_common.initFileList(detailData.realtedDetailVo.fileList, 0);
                            }
                            $("#moneyCostTip").show();
                            if (localMake != undefined && localMake != "") {
                                // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                                // $("#localMakeApprovalMailDiv").show();
                            }
                        } else if (detailData.mktType == "SEMINAR_REIMBURSE") {
                            file_common.initFileUpload(mktId, "checkInForm", "checkInFormUploader", "checkInFormFileList");
                            file_common.initFileUpload(mktId, "seminarFlow", "seminarFlowUploader", "seminarFlowFileList");
                            file_common.initFileUpload(mktId, "seminarPic", "seminarPicUploader", "seminarPicFileList");
                            file_common.initFileUpload(mktId, "settlement", "settlementUploader", "settlementFileList");

                            $("#orgListDiv").hide();
                            $("#orgNameForReimburse").html(detailData.orgName);
                            $("#orgNameForReimburse").css("display", "inline-block");

                        } else if (detailData.mktType == "STORE_IN_STORE") {
                        }

                        if (detailData.mktType == "STORE_FRONT_REIMBURSE" || detailData.mktType == "STORE_FRONT_OFFER") {
                            // 如果是供应商，则隐藏元素
                            if (detailData.currentStep == 1) {
//                                $('#mktTypeForReimburse').parent().hide();
                                $('#applyPersonName').parent().hide();
                                $('#createTime').parent().hide();
                                $('#storeAddressDoor').parent().hide();
                                $('#storeContectPersonDoor').parent().hide();
                                $('#storeContectPhoneDoor').parent().hide();
                                $('#storeCategory').parent().hide();
                                $('#seatsNum').parent().hide();
                                $('#contractYear').parent().hide();
                                if(detailData.channel != "C&I" || detailData.localMake == '1' || detailData.mktType != "STORE_FRONT_OFFER"){//TODO
//                                    $('#moneyCost').parent().parent().hide();
                                    $('#quotationDiv').hide();
                                    $('#offerSealDiv').hide();
                                }
                                $('#applyReason').parent().parent().hide();
                                $('#originalDoorDiv').hide();
                                
                                //设计供应商提供高清图
                                if(detailData.mktType == "STORE_FRONT_REIMBURSE"){
//                                    $('#hdImage').prop('disabled', false);
                                    util.formSetValidationRule(form, "hdImage", false);
                                    $('#hdImageField .required-flag').text('*');
                                }
                            }else if(detailData.mktType == "STORE_FRONT_REIMBURSE"){
                            	$('#hdImage').prop('disabled', true);
                            }
                        }

                        if (orgSelect) {
                            orgSelect.setSelectedValue(detailData ? detailData.orgId + "" : '-1');
                        }

                        util.initFieldDetail(detailData.fieldList);
                        $(".activity-detail").show();
                        //file_common.initFileList(detailData.fileList, 0);

                        loader.initContractYearsSelect();
                        loader.initInstallRequiredSelect('installationRequirementBaseboardDiv', 'installationRequirementBaseboard');
                        loader.initInstallRequiredSelect('installationRequirementBackHolderDiv', 'installationRequirementBackHolder');
                        loader.initInstallRequiredSelect('installationRequirementSteelHolderDiv', 'installationRequirementSteelHolder');
                        loader.initInstallRequiredSelect('installationRequirementObliqueHolderDiv', 'installationRequirementObliqueHolder');

                        if (detailData.mktType == 'STORE_FRONT' || detailData.mktType == 'STORE_IN_STORE') {
//                            loader.initFlagshipStoreSelect();
//                            loader.initApplyStoreSignTypeSelect();
//                            loader.initSignageStyleSelect();
                            loader.initLocalMakeSelect();
                            loader.initSupplierSelect(detailData.suppliers);
                            file_common.initFileUpload(mktId, "originalDoor", "originalDoorUploader", "originalDoorFileList");
                            file_common.initFileUpload(mktId, "quotation", "quotationUploader", "quotationFileList");
                            file_common.initFileUpload(mktId, "offerSeal", "offerSealUploader", "offerSealFileList");
                            var localMake = util.getFieldValue(detailData.fieldList, "localMake")
                            if (localMake == 1) { //当地制作
                                file_common.initFileUpload(mktId, "quotation", "quotationUploader", "quotationFileList");
                                file_common.initFileUpload(mktId, "offerSeal", "offerSealUploader", "offerSealFileList");
                                // $("#localMakeApprovalMailDiv").show();
                                // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                            }
                            if ($("#localMake").val() != undefined && $("#localMake").val() == "1") { //当地制作
                                // $("#localMakeApprovalMailDiv").show();
                                // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                            }
                            if (detailData.mktType == 'STORE_FRONT') {
                                $("#applyStoreSignTypeGroup").show();
                            } else if (detailData.mktType == 'STORE_IN_STORE') {
                                $("#applyStoreSignTypeGroup").hide();
                            }
                        } else if (detailData.mktType == "STORE_FRONT_REIMBURSE") {
                            if ($("#overBudget").val() != undefined && $("#overBudget").val() == "1") { //超过预算了
                                $("#overBudgetApprovalMailDiv").show();
                                file_common.initFileUpload(mktId, "overBudgetApprovalMail", "overBudgetApprovalMailUploader", "overBudgetApprovalMailFileList");
                            }
                        }
                        if (detailData.storeInStore == 1) {
                            $("#applyStoreSignTypeGroup").hide();
                        } else {
                            $("#applyStoreSignTypeGroup").show();
                        }

                        //加载流程
                        if (detailData.historyList && detailData.historyList.length > 0){
                            loader.initFlowDiv(detailData);
                        }

                        if (detailData.historyList && detailData.historyList.length > 0) {
                            loader.initHistoryGrid(detailData.historyList);
                            $("#titleFlow").show();
                        }
                        util.initFieldList(detailData.fieldList);
                        file_common.initFileList(detailData.fileList, 1);
                        $("#applyReason").val(util.getFieldValue(detailData.fieldList, "applyReason"));
//                        $("#localMakeDesc").val(util.getFieldValue(detailData.fieldList, "localMakeDesc"));


                        form.clearRules();
                        var field = form.getField('mktType');
                        field.remove('required');

                        util.intRequiredFiled(detailData.mktType);

                        if ((detailData.mktType == "STORE_FRONT_OFFER" || detailData.mktType == "STORE_FRONT_REIMBURSE") && detailData.currentStep == 1) {
                            $("#volumeTip").hide();
                            action.calcArea();
                        } else {
                            // action.getAvgVolumeList();
                            // loader.initTargetVolumeGrid(detailData);
                            loader.renderProductTable();
                            // $("#monthlyAvgVolumeGridDiv").show();
                            $("#volumeTip").show();
                        }

                        if (detailData.storeInStore == 1) {
//                            $("#originalDoorLabel").text("原店中店照片");
                        }

                        if (detailData && detailData.channel == "C&I" && detailData.sectionName == "报销") {
                            if (detailData.localMake == "1") {
                                if (detailData.currentStep == 1) {
                                    util.formRemoveValidationRule(form, "overBudget", "required");
                                    util.formRemoveValidationRule(form, "materielMoney", "required");
                                    util.formRemoveValidationRule(form, "materielMoney", "number");
                                } else {
                                    util.formSetValidationRule(form, 'overBudget', false);
                                    util.formSetValidationRule(form, 'materielMoney', true);
                                    $(".reimbursementDiv").show();
                                    $("#comparePicDiv").show();
                                }
                            } else {
                                util.formSetValidationRule(form, 'overBudget', false);
                                util.formSetValidationRule(form, 'materielMoney', true);
                                $(".reimbursementDiv").show();
                                $("#comparePicDiv").show();
                            }
                        }
                        loader.initStoreMktTypeCtrl();
                    });

                    if (mktTypeSelect) {
                        mktTypeSelect.ctrl.setSelectedValue(detailData ? detailData.mktType : "");
                    }
                }
                if (detailData) {
                    //loader.initTargetVolumeGrid(detailData);
                    //loader.renderProductTable();
                    //$("#monthlyAvgVolumeGridDiv").show();
                }
                //action.getAvgVolumeList();
           },
            renderForm: function () {
                BUI && BUI.use('bui/form', function (Form) {
                    //设置全局表单对象
                    form = new Form.Form({
                        srcNode: '#saveForm',
                    }).render();
                });
            },
            showStoreSelectDialog: function (type) {
                $("#storeType").val(type);
                if (storeSelectDialog == null) {
                    BUI.use(["bui/overlay", "bui/grid", "bui/data"], function (Overlay, Grid, Data) {
                        storeSelectDialog = new Overlay.Dialog({
                            title: "门店选择",
                            contentId: "storeSelectDialog",
                            width: 1000,
                            height: 400,
                            mask: true,
                            buttons: []
                        });
                        loader.initStoreTable(type);
                        storeSelectDialog.render();
                        storeSelectDialog.show();
                    });
                } else {
                    action.searchStore();
                    storeSelectDialog.show();
                }
            },
            initStoreTable: function (type) {
            	var self = this;
            	this._workshopId = $("#workShopId").val();
                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    var params = {
                        start: 0,
                        limit: 10,
                        queryType: '2',
                        workshopName: $("#workshopName").val(),
                        partnerId: $("#partnerId").val() ? $("#partnerId").val() : partnerId,
                        //shopRecruitment: 0,//非店招店
                        fromSource: 2,
                        status: 3,//已录店
                        resourceId: "cdmMktApplySave",
                        funFlag: 'CioMktApply',
                        mktKey: 'STORE_FRONT/' + $('#storeMktType').val()
                    };

                    var columns = [{
                        title: '门店名称',
                        dataIndex: 'workshopName',
                        sortable: false,
                        width: '25%'
                    }, {
                        title: '门店地址',
                        dataIndex: 'workshopAddress',
                        sortable: false,
                        width: '40%',
                        renderer: function (value, item, index) {
                            var str = value || '';
                            if (item.distName) {
                                str = item.distName + ' ' + str;
                            }
                            if (item.cityName) {
                                str = item.cityName + ' ' + str;
                            }
                            if (item.provinceName) {
                                str = item.provinceName + ' ' + str;
                            }
                            return str;
                        }
                    },
                        /*{
                            title: '门店类型',
                            sortable: false,
                            dataIndex: 'type',
                            width: '15%'
                        },*/
                        {
                            title: '联系人',
                            sortable: false,
                            dataIndex: 'contactPerson',
                            width: '10%'
                        }, {
                            title: '联系方式',
                            sortable: false,
                            dataIndex: 'contactPersonTel',
                            width: '15%'
                        },
                        {
                            title: '是否已加入店招',
                            sortable: false,
                            dataIndex: 'shopRecruitment',
                            width: '15%',
                            renderer: function (value, obj, index) {
                                return value != undefined && value == 1 ? "是" : "否";
                            }
                        }/*,
                        {
                            title: '加入店招时间',
                            sortable: false,
                            dataIndex: 'shopRecruitmentUpdateTime',
                            width: '15%',
                            renderer: function (value, obj, index) {
                                return value != null ? moment(value).format('YYYY-MM-DD') : "";
                            }
                        }*/,
                        {
                            title: '户外广告',
                            sortable: false,
                            dataIndex: 'extFlag',
                            width: '15%',
                            renderer: function (value, obj, index) {
                                return value != undefined && (value & 32) > 0 ? "已申请" : "未申请";
                            }
                        }/*,
                        {
                            title: '是否已申请车身广告',
                            sortable: false,
                            dataIndex: 'extFlag',
                            width: '15%',
                            renderer: function (value, obj, index) {
                                return value != undefined && (value & 64) > 0 ? "是" : "否";
                            }
                        }*/,
                        {
                            title: "操作",
                            sortable: false,
                            width: "10%",
                            renderer: function (value, obj, index) {
                            	if($('#storeMktType').val() == 'STORE_SIGN' && obj.shopRecruitment == 1){
                            		return '';
                            	}
                            	if($('#storeMktType').val() == 'OUTSIDE_AD' && (obj.extFlag & 32) >0){
                            		return '';
                            	}
                            	if($('#storeMktType').val() == 'CAR_AD' && (obj.extFlag & 32) >0){
                            		return '';
                            	}
                            	if((obj.operationFlag & 1) == 0){
                            		return '<span class="comment">申请中</span>';
                            	}
                                return '<button class="btn-submit store-select-btn" onclick="this">选择</button>';
                            }
                        }];

                    storeStore = new Data.Store({
                        url: '/workshopmaster/data.do',
                        autoLoad: true,
                        root: 'resultLst',
                        totalProperty: 'total',
                        remoteSort: true,
                        params: params,
                        sortField: 'id',
                        sortDirection: 'DESC',
                        proxy: {
                            method: 'post'
                        },
                        pageSize: 10
                    });

                    storeGrid = new Grid.Grid({
                        render: "#storeGrid",
                        columns: columns,
                        width: "100%",
                        height: 350,
                        store: storeStore,
                        loadMask: true,
                        bbar: {
                            pagingBar: true
                        },
                        emptyDataTpl: '<div class="centered"><h4>查询的门店数据不存在！请确认人合伙人已将门店录入到系统中。</h4></div>'
                    });

                    storeGrid.on('cellclick', function (ev) {
                        var record = ev.record;
                        var target = $(ev.domTarget);
                        var type = $("#storeType").val();
                        if (target.hasClass('store-select-btn')) {
                            $("#storeName" + type).val(record.workshopName);
                            $("#storeCode" + type).val(record.workShopCode);
                            $("#storeAddress" + type).val(record.workshopAddress);
                            $("#storeContectPerson" + type).val(record.contactPerson);
                            $("#storeContectPhone" + type).val(record.contactPersonTel);
                            $("#storeCategory").val(record.type);
                            var liftingMachineNum = 0;
                            if (record.fourColumnElevatorNumb != null) {
                                liftingMachineNum = record.fourColumnElevatorNumb;
                            }
                            $("#liftingMachineNum").val(liftingMachineNum);
                            $("#seatsNum").val(record.seatsNum);
                            $("#workShopId").val(record.id);

                            if (record.workshopAddress == undefined || record.workshopAddress == '')
                                $("#storeAddressDoor").removeAttr("readonly");
                            if (record.contactPerson == undefined || record.contactPerson == '')
                                $("#storeContectPersonDoor").removeAttr("readonly");
                            if (record.contactPersonTel == undefined || record.contactPersonTel == '')
                                $("#storeContectPhoneDoor").removeAttr("readonly");
                            if (record.type == undefined || record.type == '')
                                $("#storeCategory").removeAttr("readonly");
                            if (record.seatsNum == undefined || record.seatsNum == '')
                                $("#seatsNum").removeAttr("readonly");

                            //$("#workShopId").val(149844);
                            storeSelectDialog.close();
                            if (record.workshopProperty) {
                                isDMSDoor = (record.workshopProperty & 8) > 0
                            }
                            if (ProductDataStore) {
                                var records = ProductDataStore.getResult();
                                for (var index = 0; index < records.length; index++) {
                                    records[index].isDMSDoor = isDMSDoor;
                                    ProductDataStore.update(records[index]);
                                    loader.initSearchProduct(records[index]);
                                }
                            }
                            newProducts = true;
                            // action.getAvgVolumeList();
                            loader.renderProductTable();
                            updateDmsUnmatch();
                        }
                    });

                    storeGrid.render();
                    $('#storeGrid .bui-grid-hd-empty').css('width', '17px');
                });
            },
            /*initTrainingTypeSelect: function () {
                trainingSelect = Ctrls.DicItem.init('#trainingType', 'cdm.mkt.trainingType', {
                    emptyValue: '',
                    emptyText: ''
                });
            },
          initFlagshipStoreSelect: function () {
               flagshipStoreSelect = Ctrls.DicItem.init('#flagshipStore', 'cdm.mkt.flagship', {
                   emptyValue: '',
                   emptyText: ''
               });
           },*/
            initOverBudgetSelect: function () {
//                overBudgetSelect = Ctrls.DicItem.init('#overBudget', 'cio.mkt.flagship', {
//                    emptyValue: '',
//                    emptyText: ''
//                });
                $("#budgetCost").text(util.getFieldValue(detailData.fieldList, "moneyCost"));
//                $("#overBudget").on("change", function () {
//                    if ($(this).val() == '1') { //超过预算时
//                        $("#materielMoneyDiv").hide();
//                        $("#overBudgetMoneyDiv").show();
//                        //$("#budgetCostDiv").show();
//                        $("#overBudgetApprovalMailDiv").show();
//                        file_common.initFileUpload(mktId, "overBudgetApprovalMail", "overBudgetApprovalMailUploader", "overBudgetApprovalMailFileList");
//                        util.formRemoveValidationRule(form, 'materielMoney', 'required');
//                        util.formRemoveValidationRule(form, 'materielMoney', 'number');
//                        util.formSetValidationRule(form, 'overBudgetMoney', true);
//                    } else {
//                        $("#materielMoneyDiv").show();
//                        $("#overBudgetMoneyDiv").hide();
//                        //$("#budgetCostDiv").hide();
//                        $("#overBudgetApprovalMailDiv").hide();
//                        util.formRemoveValidationRule(form, 'overBudgetMoney', 'required');
//                        util.formRemoveValidationRule(form, 'overBudgetMoney', 'number');
//                        util.formSetValidationRule(form, 'materielMoney', true);
//                    }
//                });
                $('#overBudget').val('0');
                $("#materielMoneyDiv").show();
                util.formSetValidationRule(form, 'materielMoney', true);
            },
            initApplyStoreSignTypeSelect: function () {
                applyStoreSignTypeSelect = Ctrls.DicItem.init('#applyStoreSignType', 'cio.mkt.sign.apply', {
                    emptyValue: '',
                    emptyText: ''
                });
            },
            initSignageStyleSelect: function () {
                signageStyleSelect = Ctrls.DicItem.init('#signageStyle', 'cio.mkt.signType', {
                    emptyValue: '',
                    emptyText: ''
                });
            },
            initLocalMakeSelect: function () {
//                applyStoreSignTypeSelect = Ctrls.DicItem.init('#localMake', 'cio.mkt.flagship', {
//                    emptyValue: '',
//                    emptyText: '',
//            		ctrlOpts: {disabled: true}
//                });

//                var localMakeDesc = form.getField('localMakeDesc');
                $("#localMake").val('1');
                //TODO
                $("#signageContentRequirementDiv").show();
                form.getField('signageContentRequirement').addRule('required', true, '必填项');
                // localMakeDesc.addRule('required', true, '必填项');
                $("#supplierDiv").hide();
//                util.formRemoveValidationRule(form, 'supplier', 'required');
                if (detailData == null) {
                    //common.alertMes("您选择了当地制作，请确认是否已得到市场部线下批准邮件！", "info");
                }
//                util.formSetValidationRule(form, 'signageStyle', true);

                $("#moneyCostGroupDiv").show();
                $("#quotationDiv").show();
                $("#offerSealDiv").show();
                util.formSetValidationRule(form, 'moneyCost', true);

                /*$("#localMake").on("change", function () {
                    if ($(this).val() == '1') { //当地制作
//                        $("#localMakeDescDiv").show();
                        $("#signageContentRequirementDiv").show();
                        form.getField('signageContentRequirement').addRule('required', true, '必填项');
                        // localMakeDesc.addRule('required', true, '必填项');
                        $("#supplierDiv").hide();
                        util.formRemoveValidationRule(form, 'supplier', 'required');
                        if (detailData == null) {
                            //common.alertMes("您选择了当地制作，请确认是否已得到市场部线下批准邮件！", "info");
                        }
                        // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                        // $("#localMakeApprovalMailDiv").show();

//                        $("#installationRequirementGroupDiv").show();
//                        util.formSetValidationRule(form, 'installationRequirementBaseboard', true);
//                        util.formSetValidationRule(form, 'installationRequirementBackHolder', true);
//                        util.formSetValidationRule(form, 'installationRequirementSteelHolder', true);
//                        util.formSetValidationRule(form, 'installationRequirementObliqueHolder', true);

//                        $("#signageStyleGroupDiv").show();
                        util.formSetValidationRule(form, 'signageStyle', true);

//                        $("#signLongGroupDiv").show();
//                        $("#signWideGroupDiv").show();
//                        $("#signAreaGroupDiv").show();
                        $("#moneyCostGroupDiv").show();
                        $("#quotationDiv").show();
                        $("#offerSealDiv").show();

//                        util.formSetValidationRule(form, 'signLong', true);
//                        util.formSetValidationRule(form, 'signWide', true);
//                        util.formSetValidationRule(form, 'signArea', true);
                        util.formSetValidationRule(form, 'moneyCost', true);
                    } else if ($(this).val() == '0') {
//                        $("#localMakeDescDiv").hide();
                        // localMakeDesc.removeRule('required');
                        $("#signageContentRequirementDiv").hide();
                        form.getField('signageContentRequirement').removeRule('required');

                        $("#supplierDiv").show();
                        util.formSetValidationRule(form, 'supplier', false);
                        // $("#localMakeApprovalMailDiv").hide();

//                        $("#installationRequirementGroupDiv").hide();
//                        util.formRemoveValidationRule(form, 'installationRequirementBaseboard', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementBackHolder', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementSteelHolder', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementObliqueHolder', 'required');

//                        $("#signageStyleGroupDiv").hide();
                        util.formRemoveValidationRule(form, 'signageStyle', 'required');

//                        $("#signLongGroupDiv").hide();
//                        $("#signWideGroupDiv").hide();
//                        $("#signAreaGroupDiv").hide();
                        $("#moneyCostGroupDiv").hide();
                        $("#quotationDiv").hide();
                        $("#offerSealDiv").hide();

//                        util.formRemoveValidationRule(form, 'signLong', 'required');
//                        util.formRemoveValidationRule(form, 'signWide', 'required');
//                        util.formRemoveValidationRule(form, 'signArea', 'required');
                        util.formRemoveValidationRule(form, 'moneyCost', 'required');
                    } else {
//                        $("#localMakeDescDiv").hide();
                        // localMakeDesc.removeRule('required');
                        $("#signageContentRequirementDiv").hide();
                        form.getField('signageContentRequirement').removeRule('required');

                        $("#supplierDiv").hide();

//                        $("#installationRequirementGroupDiv").hide();
//                        util.formRemoveValidationRule(form, 'installationRequirementBaseboard', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementBackHolder', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementSteelHolder', 'required');
//                        util.formRemoveValidationRule(form, 'installationRequirementObliqueHolder', 'required');

//                        $("#signageStyleGroupDiv").hide();
                        util.formRemoveValidationRule(form, 'signageStyle', 'required');

//                        $("#signLongGroupDiv").hide();
//                        $("#signWideGroupDiv").hide();
//                        $("#signAreaGroupDiv").hide();
                        $("#moneyCostGroupDiv").hide();
                        $("#quotationDiv").hide();
                        $("#offerSealDiv").hide();

//                        util.formRemoveValidationRule(form, 'signLong', 'required');
//                        util.formRemoveValidationRule(form, 'signWide', 'required');
//                        util.formRemoveValidationRule(form, 'signArea', 'required');
                        util.formRemoveValidationRule(form, 'moneyCost', 'required');
                    }
                });*/
            },
            initSupplierSelect: function (data) {
                var items = [];//{"value": "", "text": "请选择"}
                for (var i = 0; i < data.length; i++) {
                    items.push({"value": data[i], "text": data[i]});
                }
                if (!supplierSelect) {
                    BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                        //基金类型下拉框初始化
                        supplierSelect = new Select.Select({
                            render: '#supplierDiv',
                            items: items,
                            valueField: '#supplier'
                        });
                        supplierSelect.render();

                        supplierSelect.on('change', function (ev) {
                            var data;
                            if (ev.value == -1) {

                            } else {
                            }
                        });

                        if (mktId && detailData) {
                            supplier = util.getFieldValue(detailData.fieldList, "supplier");
                            supplierSelect.setSelectedValue(supplier + "");
                        } else {
                            supplierSelect.setSelectedValue("");
                        }
                    });
                } else {
                    supplierSelect.set("items", items);
                }
            },
            initContractYearsSelect: function () {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    var items = [{"value": "", "text": "请选择"}];
                    for (var i = 1; i < 11; i++) {
                        items.push({"value": i + "", "text": i + "年"});
                    }

                    //基金类型下拉框初始化
                    contractYearsSelect = new Select.Select({
                        render: '#contractYearDiv',
                        items: items,
                        valueField: '#contractYear'
                    });
                    contractYearsSelect.render();

                    contractYearsSelect.on('change', function (ev) {
                        var data;
                        if (ev.value == -1) {

                        } else {
                        }
                    });

                    if (mktId && detailData) {
                        contractYearsSelect.setSelectedValue(detailData.contractYear + "");
                    } else {
                        contractYearsSelect.setSelectedValue("-1");
                    }
                });
            },
            initInstallRequiredSelect: function (container, field) {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    var items = [{value: "1", text: '需要'}, {value: "0", text: '不需要'}];

                    //基金类型下拉框初始化
                    var selectComponents = new Select.Select({
                        render: '#' + container,
                        items: items,
                        valueField: '#' + field
                    });
                    selectComponents.render();

                    selectComponents.on('change', function (ev) {
                        var data;
                        if (ev.value == -1) {

                        } else {
                        }
                    });

                    if (mktId && detailData) {
                        selectComponents.setSelectedValue(util.getFieldValue(detailData.fieldList, field) + "");
                    } else {
                        selectComponents.setSelectedValue("1");
                    }
                });
            },

            initFlowDiv: function (detailData) {
                var flowData = detailData.flowNodeList;
                var flowList = [];
                var section2Node2 = [];
                var section2StepHtml = "";
                var section2Node2Status = "0";
                var isCioLocalmake = (detailData.channel == "C&I" && detailData.localMake == "1" && detailData.mktType.indexOf("STORE") == 0);
                if (isCioLocalmake) {
                    for (var i = 0; i < flowData.length; i++) {
                        if (flowData[i].sectionName == "报价" && flowData[i].step == 2) { //&& flowData[i].mktApplyId == detailData.offerId
                            section2Node2.push(flowData[i]);
                        }
                    }

//                    for (var i = 0; i < flowData.length; i++) {
//                        if (flowData[i].sectionName == "报价" && flowData[i].step == 2) {//&& flowData[i].mktApplyId == detailData.offerId
//                            delete flowData[i];
//                            break;
//                        }
//                    }
                    var preStepKey = null;
                    for (var i = 0; i < flowData.length; i++) {
                    	var key = flowData[i].sectionName + '/' + flowData[i].step;
                        if (preStepKey != key) {
                            flowList.push(flowData[i]);
                        }
                        preStepKey = key;
                    }


                    for (var i = 0; i < section2Node2.length; i++) {
                        var nodeName = (section2Node2[i].operatorName ? section2Node2[i].operatorName : "");
                        section2StepHtml += '<span class="step-name">';

                        if (section2Node2[i].status == "1") {
                            section2StepHtml += '<i class="fa fa-check fa-fw"></i>';
                        } else {
                            section2StepHtml += '<i class="fa fa-check fa-fw icon-white"></i>';
                        }
                        section2StepHtml += nodeName + "<br/>";
                        section2StepHtml += '</span>';
                    }

                    if(section2Node2[0] && section2Node2[1]) {
                        if (section2Node2[0].status && section2Node2[0].status == "1" && section2Node2[1].status && section2Node2[1].status == "1") {
                            section2Node2Status = "1";
                        }
                    }
                    flowData=flowList;
                }

                $flowDiv = $("#flowDiv");
                $steps = $('<ul class="ystep-container-steps"></ul>');
                var eachWidth = (99 - 10) / (flowData.length - 1);

                for (var i = 0; i < flowData.length; i++) {
                    //步骤
                    var nodeName = (flowData[i].operatorName ? flowData[i].operatorName : "");

                    if (isCioLocalmake && flowData[i].sectionName == "报价" && flowData[i].step == 2) {
                        var $nameSpan = $(section2StepHtml);
                        var doneClass = "ystep-step-undone";
                        if (section2Node2Status == "1") {
                            $nameSpan.find("i").removeClass("icon-white");
                            doneClass = "ystep-step-done";
                        }
                    } else {
                        var $nameSpan = $('<span class="step-name"><i class="fa fa-check fa-fw icon-white"></i>' + nodeName + '</span>');
                        var doneClass = "ystep-step-undone";
                        if (flowData[i].status == "1") {
                            $nameSpan.find("i").removeClass("icon-white");
                            doneClass = "ystep-step-done";
                        }
                    }

                    if (i == flowData.length - 1) {
                        eachWidth = 10;
                    }

                    var $li = $('<li id="step' + flowData[i].step + '" class= ' + doneClass + ' style="width: ' + eachWidth + '%;margin-top: 25px;"></li>');
                    $li.append($nameSpan);
                    $steps.append($li);
                }
                $flowDiv.append($steps);
                //横线
                $progressBar = $('<div class="ystep-progress"><p class="ystep-progress-bar"><span class="ystep-progress-highlight" style="width:0%"></span></p></div>');
                $flowDiv.append($progressBar);
            },

            initHistoryGrid: function (historyData) {
                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
                    var Grid = Grid,
                        Store = Data.Store,
                        columns = [{
                            title: '序号',
                            dataIndex: '',
                            sortable: false,
                            width: 65,
                            renderer: function (value, obj, index) {
                                return index + 1;
                            }
                        }, {
                            title: '处理人',
                            dataIndex: 'operatorName',
                            sortable: false,
                            width: '15%'
                        }, {
                            title: '处理结果',
                            sortable: false,
                            dataIndex: 'auditStatusName',
                            width: '20%',
                            renderer: function (value, obj, index) {
                                return (obj.operatorRoleName ? obj.operatorRoleName : "") + value + (obj.stepName ? obj.stepName : "");
                            }
                        }, {
                            title: '处理意见',
                            sortable: false,
                            dataIndex: 'opinion',
                            width: '50%'
                        }, {
                            title: '处理时间',
                            dataIndex: 'auditTime',
                            sortable: false,
                            width: '15%',
                            renderer: function (value, obj, index) {
                                return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : "";
                            }
                        }],
                        store = new Store({
                            data: historyData,
                            autoLoad: true
                        }),
                        historyGrid = new Grid.Grid({
                            render: '#historyGrid',
                            columns: columns,
                            loadMask: true, // 加载数据时显示屏蔽层
                            store: store,
                            bbar: false,
                            width: '100%'
                        });
                    historyGrid.render();
                });
            },
            initOrgList: function (callback) {
                BUI.use(['bui/select', 'bui/picker', 'bui/list', 'bui/data'], function (Select, Picker, List, Data) {
                    req.getOrgList(function (data) {
                        if (orgSelect) {
                            orgStore.setResult(data);
                            return;
                        }
                        orgStore = new Data.Store({
                            data: data,
                            autoLoad: true,
                            autoSync: true
                        });
                        orgSelect = new Select.Select({
                            render: '#orgListDiv',
                            list: {
                                itemTpl: '<li>{orgName}</li>',
                                idField: 'orgId',
                                elCls: 'bui-select-list'
                            },
                            store: orgStore,
                            valueField: '#orgId',
                            width: 345
                        });
                        //TODO 初始选中经销商，显示未匹配DMS说明
                        var pid = $('#orgId').val();
                        if(pid){
                        	for(var i = 0; i < data.length; i++){
                        		var item = data[i];
                        		if(item.orgId == pid){
                        			includeDmsWorkshop = item.includeDmsWorkshop;
                        			updateDmsUnmatch();
                        			break;
                        		}
                        	}
                        }

                        orgSelect.on('change', function (ev) {
                            var item = ev.item;
                            $("#orgName").val(item.orgName);
                            partnerId = item.partnerId;
                            $("#partnerId").val(item.partnerId);
                            //TODO 判断经销商是否安装DMS
                            includeDmsWorkshop = item.includeDmsWorkshop;
                            emptyWorkshop();
                            updateDmsUnmatch();

                            //loader.initAvgVolumeGrid(item);
                            //$("#monthlyAvgVolumeGridDiv").show();

                            // loader.initTargetVolumeGrid(item);
                            // loader.renderProductTable();
                            // util.getTargetVolumeTotal('small');
                            // util.getTargetVolumeTotal('large');

                            if ($("#localMake").val() != undefined && $("#localMake").val() != "") {
                                if ($("#localMake").val() == "0") {//非当地制作
                                    $("#supplierDiv").show();
                                } else {
                                    $("#supplierDiv").hide();
                                    // $("#localMakeApprovalMailDiv").show();
                                    // file_common.initFileUpload(mktId, "localMakeApprovalMail", "localMakeApprovalMailUploader", "localMakeApprovalMailFileList");
                                }
                            }
                            loader.initSupplierSelect(item.suppliers);

                        });

                        orgSelect.render();

                        if (orgSelect) {
                            //orgSelect.setSelectedValue(detailData?detailData.orgId + "":"-1");
                        }

                    });

                    $.isFunction(callback) && callback();
                });
            },
            searchProductAutoSelect: $.extend({}, Ctrls.AutoSelect, {
                init: function (valueField, opts) {
                    opts = $.extend({placeholder: '请输入产品名称关键字并点击选择产品'}, opts);
                    opts.ctrlOpts = $.extend(opts.ctrlOpts, {
                        contentType: 'application/json',
                        processData: function (json) { // url 获取数据时，对数据的处理，作为 getData 的回调函数
                            if (!json || !json.data) {
                                return false;
                            }

                            var len, data = {
                                value: []
                            };
                            len = json.data.length;

                            SearchProductList = json.data
                            for (var i = 0; i < len; i++) {
                                data.value.push({
                                    value: json.data[i].id,
                                    text: json.data[i].name
                                });
                            }

                            //字符串转化为 js 对象
                            return data;
                        }
                    });
                    return Ctrls.AutoSelect.init.call(this, 'mkt/cio/getProductByName.do', valueField, 'name', opts);
                }
            }),
            initSearchProduct: function (payload) {
            	var initedFlag = $('#product' + payload.index).attr('initflag');
            	if(initedFlag == '1'){
            		return;
            	}
            	$('#product' + payload.index).attr('initflag', '1');
                var product = ProductDataStore.findByIndex(payload.index);
                var searchProductCtrl = loader.searchProductAutoSelect.init('#product' + payload.index, {
//                    placeholder: product.productName || '',
                    onchange: function (data) {
                        var product = null;
                        SearchProductList.map(function (item) {
                            if (item.id == data) {
                                product = item;
                            }
                            return;
                        })
                        action.updateProductItem(product, payload.index);
                        if (product) {
                            loader.getProductById({
                                workshopCode: $("#storeCodeDoor").val(),
                                chevronProductCode: product.sku
                            }, function (data) {
                                action.updateProductItem({
                                    volumeLitre: data.data
                                }, payload.index);
                            })
                        }
                    }
                });
            },
            getProductById: function (data, callback) {
                $.ajax({
                    dataType: 'json',
                    type: 'post',
                    contentType: 'application/json',
                    url: common.ctx + 'mkt/cio/actualSellThroughAverage.do',
                    data: JSON.stringify(data),
                    success: function (data) {
                        $.isFunction(callback) && callback(data);
                    },
                    error: function (data) {

                    }
                });

            },
            initProductTable: function () {
                ProductData = [];
                for (var i = 0; i < 5; i++) {
                    ProductData.push({
                        index: i,
                        productName: '',
                        productSKU: '',
                        packingSpec: '',
                        targetVolumeBucket: '',
                        targetVolumeLitre: '',
                        volumeBucket: '',
                        volumeLitre: ''
                    });
                }
            },
            renderProductTable: function () {
            	var self = this;
                if(includeDmsWorkshop == null && (self._times == null || self._times < 30)){
                	//TODO 延迟加载
                	setTimeout(function(){
                		self.renderProductTable();
                	}, 1000);
                	self._times = self._times ? (self._times + 1) : 1;
                	return;
                }
                if (detailData && detailData.orgId && detailData.orgId == "-1") {
                    return;
                }

                if (detailData && detailData.mktType == "STORE_FRONT_OFFER") { // 如果是供应商就不渲染
                    return;
                }

                if (ProductData.length) { // 如果已经渲染过，就不再渲染
                    return false
                }

                if (detailData && !newProducts) {
                    var volumes = loader.getTargetVolumeFieldValue(detailData, 'volumes');
                    ProductData = volumes && JSON.parse(volumes);
                    if (!ProductData || !ProductData.length) {
                        loader.initProductTable();
                    }
                } else {
                	newProducts = false;
                    if (ProductDataStore) {
                        loader.initProductTable();
                        ProductDataStore.setResult(ProductData);
                        ProductData.map(function (item) {
                            loader.initSearchProduct(item);
                        });
                        return;
                    }
                    loader.initProductTable();
                }

                BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {

                    ProductDataStore = new Data.Store({
                        data: ProductData,
                        autoLoad: true,
                        autoSync: true
                    });

                    var columns = [{
                        title: '<span class="required-flag">*</span>产品中文名',
                        sortable: false,
                        dataIndex: 'productName',
                        width: '50%',
                        renderer: function (value, obj, index) {
                            if (pageType == "audit") {
                                return value
                            } else {
                                if (detailData && detailData.historyList.length > 0) {
                                    return value;
                                } else {
                                    var inputName = 'product' + obj.index
                                    return '<input type="text" text="' + value + '" id="' + inputName + '" name="' + inputName + '" ' + ' value ="' + obj.productSKU + '"'
                                        + 'style="display: none;"/>';
                                }
                            }
                        }
                    }, {
                        title: '产品编码 SKU',
                        sortable: false,
                        dataIndex: 'productSKU',
                        width: '20%',
                        renderer: function (value, obj, index) {
                            return value;
                        }
                    }, {
                        title: '包装规格（L）',
                        sortable: false,
                        dataIndex: 'packingSpec',
                        width: '20%',
                        renderer: function (value, obj, index) {
                            return value;
                        }
                    }, {
                        title: '月预计销量（箱/桶）',
                        sortable: false,
                        dataIndex: 'targetVolumeBucket',
                        width: '30%',
                        renderer: function (value, obj, index) {
                            if (pageType == "audit") {
                                return value
                            } else {
                                if (detailData && detailData.historyList.length > 0) {
                                    return value;
                                } else {
                                    var inputName = 'productVolumeLiter' + obj.index
                                    return '<input type="text" '
                                        + 'id="' + inputName + '" name="' + inputName + '" value="' + value + '" '
                                        + 'onblur="save_widget.updateProductItem(this, ' + obj.index + ')" '
                                        + 'class="control-text bui-form-field packSize-large" style="width:100px;text-align: right;"/>';
                                }
                            }
                        }
                    }, {
                        title: '月预计销量（升）',
                        sortable: false,
                        dataIndex: 'targetVolumeLitre',
                        width: '25%',
                        renderer: function (value, obj, index) {
                            return obj.packingSpec * obj.targetVolumeBucket || '';
                        }
                    }, {
                        title: '过去 12 个月销量（箱/桶）',
                        sortable: false,
                        dataIndex: 'volumeBucket',
                        width: '30%',
                        renderer: function (value, obj, index) {
                            return obj.productName ? (value || 0) : '';
                        }
                    }, {
                        title: '过去 12 个月月平均实际销量（升）',
                        sortable: false,
                        dataIndex: 'volumeLitre',
                        width: '40%',
                        renderer: function (value, obj, index) {
                            return obj.productName ? (value || 0) : '';
                        }
                    }
                    ];
                    if (!detailData || detailData.mktType == "STORE_FRONT") {
                        columns.push({
                            title: 'DMS 门店',
                            sortable: false,
                            dataIndex: 'isDMSDoor',
                            width: '20%',
                            renderer: function (value, obj, index) {
                                if (obj.productName) {
                                    if (isDMSDoor) {
                                        return '<span>是</span>';
                                    } else if(includeDmsWorkshop) {
                                        return '<span>否（<a href="#" onclick="save_widget.goDoorMap()">门店绑定</a>）</span>';
                                    }else{
                                    	return '<span>否</span>';
                                    }
                                } else {
                                    return ''
                                }
                            }
                        });
                        if (!detailData || detailData.historyList.length == 0){
                            columns.push({
                                title: '操作',
                                sortable: false,
                                dataIndex: 'operater',
                                width: 70,
                                renderer: function (value, obj, index) {
                                    var text = '删除';

                                    if (index < 5) {
                                        text = '清空';
                                    }

                                    return '<button type="button" class="btn-create btn-small" onclick="save_widget.deleteProductItem(' + index + ');">' + text + '</button>';
                                }
                            });
                        }
                    }

                    $('#monthlyTargetVolumeGrid').parent().show();

                    var grid = new Grid.Grid({
                        render: "#monthlyTargetVolumeGrid",
                        columns: columns,
                        store: ProductDataStore,
                        bbar: false,
                        width: '100%'
                    });
                    grid.render();

                    ProductData.map(function (item) {
                        loader.initSearchProduct(item);
                    })
                });
            }
        },
        action = {
            save: function () {
                var saveRequest = util.beforeSaveRequest(false);
                if (false == saveRequest) {
                    return false;
                }

                req.save(saveRequest, function (data) {
                    if (data.code == '0000') {
                        common.alertMes("处理成功", 'success');
                        returnToList();
                    } else {
                        common.alertMes(data.message, 'error');
                    }
                });
            },
            submit: function () {
                var saveRequest = util.beforeSaveRequest(true);
                if (false == saveRequest) {
                    return false;
                }

                if (ProductDataStore) {
                    var volumes = ProductDataStore.getResult();
                    volumes = volumes.filter(function (item) {
                        return item.productName !== '';
                    });

                    var hasVoidValue = false;
                    volumes.map(function (item) {
                        if (item.targetVolumeBucket == '') {
                            hasVoidValue = true;
                        }
                    });

                    if (hasVoidValue) {
                        common.alertMes("请填写对应产品的月预计销量！", 'error');
                        return false;
                    }
                }
                //文件必填写
                var originalDoorFilelist = [];
                var quotationFilelist = [];
                var offerSealFilelist = [];
                requireFileList = file_common.getFileList();
                /*if (requireFileList.length == 0) {
                    common.alertMes("缺少上传文件，请检查！", 'error');
                    return false;
                }*/

                var mktType = mktId == null ? saveRequest["mktType"] : detailData.mktType;
                if (mktType == 'STORE_FRONT' || mktType == "STORE_IN_STORE") {
                	if(storeMktTypeHandler.validate(saveRequest, util, requireFileList) === false){
                		return false;
                	}
//                    filelist = util.getFileListByType(requireFileList, 'originalDoor');
//                    if (filelist.length == 0) {
//                        common.alertMes("缺少原店招(店中店)照片文件，请上传提供！", 'error');
//                        return false;
//                    }
                    if ($("#localMake").val() == '1') {
//                        filelist = util.getFileListByType(requireFileList, 'quotation');
//                        if (filelist.length == 0) {
//                            common.alertMes("缺少报价单照片文件，请上传提供！", 'error');
//                            return false;
//                        }
                        filelist = util.getFileListByType(requireFileList, 'offerSeal');
                        if (filelist.length == 0) {
                            common.alertMes("缺少报价单(带章)照片文件，请上传提供！", 'error');
                            return false;
                        }
                    }


                    // var localMake;
                    // for (var i = 0; i < saveRequest['fieldList'].length; i++) {
                    //     if (saveRequest['fieldList'][i].name == 'localMake') {
                    //         localMake = saveRequest['fieldList'][i].value;
                    //         break;
                    //     }
                    // }
                    // if (localMake != undefined && localMake == '1') {
                    //     filelist = util.getFileListByType(requireFileList, 'localMakeApprovalMail');
                    //     if (filelist.length == 0) {
                    //         common.alertMes("缺少本地制作批准邮件文件，请上传提供！", 'error');
                    //         return false;
                    //     }
                    // }
                }
                if (mktType == 'STORE_FRONT_OFFER') {
                    var localMake = util.getFieldValue(detailData.fieldList, "localMake");
                    if (localMake == '1') {
                        filelist = util.getFileListByType(requireFileList, 'previewChart');
                        if (filelist.length == 0) {
                            common.alertMes("缺少预览图文件，请上传提供！", 'error');
                            return false;
                        }
                    } else {
                        filelist = util.getFileListByType(requireFileList, 'designChart');
                        if (filelist.length == 0) {
                            common.alertMes("缺少设计图文件，请上传提供！", 'error');
                            return false;
                        }
//                        filelist = util.getFileListByType(requireFileList, 'quotation');
//                        if (filelist.length == 0) {
//                            common.alertMes("缺少报价单文件，请上传提供！", 'error');
//                            return false;
//                        }

                        filelist = util.getFileListByType(requireFileList, 'offerSeal');
                        if (filelist.length == 0) {
                            common.alertMes("缺少报价单(带章)文件，请上传提供！", 'error');
                            return false;
                        }
                    }

                } else if (mktType == "STORE_FRONT_REIMBURSE") {
                    var localMake = util.getFieldValue(detailData.fieldList, "localMake");
                    if (localMake == '1') {
                        if (detailData.currentStep == 1) {
                            filelist = util.getFileListByType(requireFileList, 'designChart');
                            if (filelist.length == 0) {
                                common.alertMes("缺少设计图文件，请上传提供！", 'error');
                                return false;
                            }
                        } else {
//                            filelist = util.getFileListByType(requireFileList, 'applicationForm');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少14A 申请表文件，请上传提供！", 'error');
//                                return false;
//                            }
                            filelist = util.getFileListByType(requireFileList, 'complianceInvoice');
                            if (filelist.length == 0) {
                                common.alertMes("缺少合规发票文件，请上传提供！", 'error');
                                return false;
                            }
//                            filelist = util.getFileListByType(requireFileList, 'storeConfirmation');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少店面收货确认证明文件，请上传提供！", 'error');
//                                return false;
//                            }
//                            filelist = util.getFileListByType(requireFileList, 'tripleAgreement');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少三方协议证明文件，请上传提供！", 'error');
//                                return false;
//                            }
//                            filelist = util.getFileListByType(requireFileList, 'paymentFromThirdPartySupplier');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少第三方供应商的付款证明文件，请上传提供！", 'error');
//                                return false;
//                            }
                        }
                    } else {
                    	if(detailData.currentStep == 1){//TODO leozli
//                            filelist = util.getFileListByType(requireFileList, 'applicationForm');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少14A 申请表文件，请上传提供！", 'error');
//                                return false;
//                            }
                            filelist = util.getFileListByType(requireFileList, 'complianceInvoice');
                            if (filelist.length == 0) {
                                common.alertMes("缺少合规发票文件，请上传提供！", 'error');
                                return false;
                            }
//                            filelist = util.getFileListByType(requireFileList, 'storeConfirmation');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少店面收货确认证明文件，请上传提供！", 'error');
//                                return false;
//                            }
//                            filelist = util.getFileListByType(requireFileList, 'tripleAgreement');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少三方协议证明文件，请上传提供！", 'error');
//                                return false;
//                            }
//                            filelist = util.getFileListByType(requireFileList, 'paymentFromThirdPartySupplier');
//                            if (filelist.length == 0) {
//                                common.alertMes("缺少第三方供应商的付款证明文件，请上传提供！", 'error');
//                                return false;
//                            }
                        	filelist = util.getFileListByType(requireFileList, 'comparePic');
                            if (filelist.length == 0) {
                                common.alertMes("缺少完工对比图文件，请上传提供！", 'error');
                                return false;
                            }
                    	}
                    }


                    var overBudget;
                    for (var i = 0; i < saveRequest['fieldList'].length; i++) {
                        if (saveRequest['fieldList'][i].name == 'overBudget') {
                            overBudget = saveRequest['fieldList'][i].value;
                            break;
                        }
                    }
                    if (overBudget != undefined && overBudget == '1') {
                        filelist = util.getFileListByType(requireFileList, 'overBudgetApprovalMail');
                        if (filelist.length == 0) {
                            common.alertMes("缺少超过预算价市场部批准邮件文件，请上传提供！", 'error');
                            return false;
                        }
                    }
                }

                //完工结算钱必须小于报价
                if ($("#overBudget").val() != undefined && $("#overBudget").val() == "0"
                ) {
                    if ($("#materielMoney").val() != undefined && $("#materielMoney").val() != '') {
                        var moneyCost = parseFloat(util.getFieldValue(detailData.fieldList, "moneyCost"));
                        if (parseFloat($.trim($("#materielMoney").val())) > moneyCost) {
                            common.alertMes("完工结算金额必须小于等于预算报价金额 (" + moneyCost + ")！", "error");
                            return false;
                        }
                    }
                }
                if ($("#overBudget").val() != undefined && $("#overBudget").val() == "1") {

                }

                //预计销量必填
                // if (mktType == 'STORE_FRONT' || mktType == "STORE_IN_STORE") {
                //     //if ((detailData == null || (pageType == "update")) && util.checkTargetVolumeInput() != 0) return false;
                // }
                var confMsg = "";
                /*if (detailData && detailData.returnYears > 1 && detailData.roles == "Chevron_BD") {
                    confMsg += "请注意：当前投资回报年期超过1年(为：\" + returnYears + \"年)！";
                }*/
                confMsg += '请确认是否提交数据？';
                common.confirmMes(confMsg, function () {
                    req.submit(saveRequest, function (data) {
                        if (data.code == '0000') {
                            common.alertMes("操作成功!", 'success');
                            returnToList();
                        } else {
                            common.alertMes(data.message, 'error');
                        }
                    });
                });
            },
            selectStore: function (type) {
                loader.showStoreSelectDialog(type);
            }
            ,
            searchStore: function () {
                var params = {
                    start: 0,
                    limit: 10,
                    queryType: '2',
                    workshopName: $("#workshopName").val(),
                    funFlag: 'CioMktApply',
                    mktKey: 'STORE_FRONT/' + $('#storeMktType').val(),
                    partnerId: $("#partnerId").val() ? $("#partnerId").val() : partnerId
                };
                storeStore.load(params);
            }
            ,
            calcArea: function () {
                var width = $("#signLong").val();
                if (width == undefined || width == '') width = 0;
                var height = $("#signWide").val();
                if (height == undefined || height == '') height = 0;
                var area = parseFloat(width) * parseFloat(height);
                $("#signArea").val(area.toFixed(2));
                return area;
            },
            addProductItem: function () {
                var index = ProductDataStore.getCount();
                ProductDataStore.add({
                    index: index,
                    productName: '',
                    productSKU: '',
                    packingSpec: '',
                    targetVolumeBucket: '',
                    targetVolumeLitre: '',
                    volumeBucket: '',
                    volumeLitre: ''
                });
                loader.initSearchProduct({index: index});
            },
            deleteProductItem: function (index) {
                if (index < 5) {
                    var record = ProductDataStore.findByIndex(index);
                    if (record) {
                        record.productName = '';
                        record.productSKU = '';
                        record.packingSpec = '';
                        record.targetVolumeBucket = '';
                        record.targetVolumeLitre = '';
                        record.volumeBucket = '';
                        record.volumeLitre = '';
                        ProductDataStore.update(record);
                        if (!$('#product' + index).parent().find('.empty-btn-hide').length) {
                            loader.initSearchProduct({index: index});
                        }
                    }
                } else {
                    ProductDataStore.remove(ProductDataStore.findByIndex(index));
                }
            },
            updateProductItem: function (data, index) {
                var product = ProductDataStore.findByIndex(index);

                if (data && data.name) ProductDataStore.setValue(product, 'productName', data.name);
                if (data && data.sku) ProductDataStore.setValue(product, 'productSKU', data.sku);
                if (data && data.pack) ProductDataStore.setValue(product, 'packingSpec', data.pack);
                if (data && data.targetVolumeBucket) {
                    ProductDataStore.setValue(product, 'targetVolumeBucket', data.targetVolumeBucket);
                    ProductDataStore.setValue(product, 'targetVolumeLitre', data.targetVolumeBucket * product.packingSpec);
                }
                if (data && data.volumeLitre) {
                    ProductDataStore.setValue(product, 'volumeLitre', parseFloat(data.volumeLitre).toFixed(2));
                    ProductDataStore.setValue(product, 'volumeBucket', parseFloat(data.volumeLitre * 12 / product.packingSpec).toFixed(2));
                }

                if (!$('#product' + index).parent().find('.empty-btn-hide').length) {
                    loader.initSearchProduct({index: index});
                }
            },
            showSignStylePic: function () {
                // 显示选择样式的弹出窗前，渲染选择区域，并选中下拉框中的对应的选择区域
                selectStyle_widget.changeStyleBySelect();
                BUI.use('bui/overlay', function (Overlay) {
                    if (signStylePic == null) {
                        signStylePic = new Overlay.Dialog({
                            title: storeMktTypeHandler.demoTitle,
//                            width: 900,
//                            height: 1200,
                            contentId: 'signStylePicView',
                            buttons: [{
                                text: '确定',
                                elCls: 'btn-confirm',
                                handler: function () {
                                    this.close();
                                }
                            }]
                        });
                    }else{
                    	signStylePic.set('title', storeMktTypeHandler.demoTitle);
                    }
                    signStylePic.show();
                });
            }

        };

    return {
        init: function () {
            dic_common.initDicList(function () {
                loader.initSave();
            });
        },
        save: function () {
            return action.save();
        },
        submit: function () {
            return action.submit();
        },
        returnListPage: function () {
            return returnToList();
        },
        selectStore: function (type) {
            return action.selectStore(type);
        },
        searchStore: function () {
            return action.searchStore();
        },
        getTargetVolumeSum: function (type) {
            return loader.getTargetVolumeSum(type);
        },
        getAvgVolumeList: function () {
            return action.getAvgVolumeList();
        },
        calcArea: function () {
            return action.calcArea();
        },
        addProductItem: function () {
            return action.addProductItem();
        },
        deleteProductItem: function (index) {
            return action.deleteProductItem(index);
        },
        updateProductItem: function (_this, index) {
            return action.updateProductItem({targetVolumeBucket: $(_this).val()}, index);
        },
        showSignStylePic: function () {
            return action.showSignStylePic();
        },
        goDoorMap: function () {
            var saveRequest = util.beforeSaveRequest(false);
            if (false == saveRequest) {
                return false;
            }

            req.save(saveRequest, function (data) {
                if (data.code == '0000') {
                	var tu = '/cdm_mkt/jsp/mktApplyListPage.jsp?type=1&channel=2',
                		nu = '/cdm_mkt/cio/mktApplySave.jsp?type=update&fromPage=' + _fromPage + '&id='+data.data.id+
                			'&channel=2&pageInitType=' + pageInitType;
                    if (top.openMenuTab) {
                        var url = '/SPA/DoorMap/index.jsp#/?workshopId='+$("#workShopId").val() + '&pageType=ctrl&newUrl=' + encodeURIComponent(encodeURIComponent(nu)) 
                        		+ '&urlTemplate=' + encodeURIComponent(encodeURIComponent(tu));
                        top.openMenu(url, url, '门店关联')
                    }
                    window.location.href = nu;
               } else {
                    common.alertMes(data.message, 'error');
                }
            });
        }
    }
}($));