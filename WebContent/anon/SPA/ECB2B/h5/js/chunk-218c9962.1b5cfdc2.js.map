{"version": 3, "sources": ["webpack:///./node_modules/vue-awesome-swiper/dist/vue-awesome-swiper.js", "webpack:///./node_modules/swiper/dist/js/swiper.js"], "names": ["e", "t", "module", "exports", "this", "i", "n", "s", "l", "call", "m", "c", "d", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "__esModule", "default", "prototype", "hasOwnProperty", "p", "r", "a", "u", "options", "render", "staticRenderFns", "_compiled", "functional", "_scopeId", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "_registeredComponents", "add", "_ssrRegister", "beforeCreate", "_injectStyles", "concat", "esModule", "value", "install", "swiperSlide", "swiper", "Swiper", "window", "f", "props", "globalOptions", "component", "name", "h", "data", "slideClass", "ready", "update", "mounted", "$parent", "updated", "attached", "methods", "assign", "TypeError", "arguments", "length", "writable", "type", "required", "classes", "wrapperClass", "mountInstance", "$nextTick", "activated", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "navigation", "pagination", "$el", "bindEvents", "$emit", "for<PERSON>ach", "on", "apply", "Array", "slice", "replace", "toLowerCase", "$createElement", "_self", "_c", "staticClass", "_t", "_v", "class", "global", "factory", "doc", "document", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "location", "hash", "win", "navigator", "userAgent", "history", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "Dom7", "arr", "self", "$", "selector", "context", "els", "tempParent", "html", "trim", "indexOf", "toCreate", "innerHTML", "push", "match", "split", "nodeType", "unique", "uniqueArray", "addClass", "className", "j", "classList", "removeClass", "remove", "hasClass", "contains", "toggleClass", "toggle", "attr", "attrs", "arguments$1", "getAttribute", "attrName", "removeAttr", "removeAttribute", "key", "el", "dom7ElementDataStorage", "dataKey", "transform", "elStyle", "webkitTransform", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "args", "len", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "target", "eventData", "dom7EventData", "unshift", "is", "parents", "k", "handleEvent", "undefined", "events", "event$1", "dom7LiveListeners", "proxyListener", "event", "dom7Listeners", "off", "handlers", "handler", "splice", "dom7proxy", "trigger", "evt", "detail", "bubbles", "cancelable", "filter", "dataIndex", "dispatchEvent", "transitionEnd", "callback", "dom", "fireCallBack", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "prop", "each", "text", "textContent", "compareWith", "matches", "webkitMatchesSelector", "msMatchesSelector", "index", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "fn", "Class", "Methods", "keys", "methodName", "Utils", "deleteProps", "obj", "object", "nextTick", "delay", "now", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "WebKitCSSMatrix", "map", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "m42", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "params", "param", "query", "urlToParse", "href", "paramsPart", "decodeURIComponent", "isObject", "constructor", "extend", "len$1", "to", "nextSource", "keysArray", "nextIndex", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "Support", "testDiv", "touch", "Modernizr", "maxTouchPoints", "DocumentTouch", "pointerEvents", "pointer<PERSON><PERSON>bled", "PointerEvent", "prefixedPointerEvents", "msPointer<PERSON><PERSON><PERSON>", "transforms3d", "csstransforms3d", "flexbox", "observer", "passiveListener", "supportsPassive", "opts", "gestures", "Browser", "<PERSON><PERSON><PERSON><PERSON>", "ua", "isIE", "isEdge", "isUiWebView", "test", "SwiperClass", "eventsListeners", "eventName", "staticAccessors", "components", "updateSize", "width", "height", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "size", "updateSlides", "$wrapperEl", "swiperSize", "rtl", "rtlTranslate", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slidesNumberEvenToRows", "slideSize", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesPerColumn", "Math", "floor", "ceil", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumnFill", "max", "newSlidesGrid", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "slidesPerGroup", "groupIndex", "order", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "paddingTop", "paddingBottom", "boxSizing$1", "swiperSlideSize", "centeredSlides", "abs", "effect", "setWrapperSize", "i$1", "slidesGridItem", "i$2", "slidesGridItem$1", "centerInsufficientSlides", "allSlidesSize", "slideSizeValue", "allSlidesOffset", "snap", "snapIndex", "emit", "watchOverflow", "checkOverflow", "watchSlidesProgress", "watchSlidesVisibility", "updateSlidesOffset", "updateAutoHeight", "speed", "activeSlides", "newHeight", "setTransition", "activeIndex", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "translate", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "visibleSlides", "slideProgress", "minTranslate", "slideBefore", "slideAfter", "isVisible", "progress", "updateProgress", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "wasBeginning", "wasEnd", "updateSlidesClasses", "activeSlide", "realIndex", "slideActiveClass", "loop", "slideDuplicateClass", "slideDuplicateActiveClass", "nextSlide", "slideNextClass", "prevSlide", "slidePrevClass", "slideDuplicateNextClass", "slideDuplicatePrevClass", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "normalizeSlideIndex", "initialized", "runCallbacksOnInit", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "priority", "method", "once", "once<PERSON><PERSON><PERSON>", "f7proxy", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "eventsArray", "useModulesParams", "instanceParams", "instance", "modules", "moduleName", "useModules", "modulesParams", "moduleParams", "modulePropName", "moduleProp", "bind", "moduleEventName", "create", "set", "use", "installModule", "proto", "static", "defineProperties", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "newProgress", "x", "y", "z", "previousTranslate", "transitionStart", "runCallbacks", "direction", "autoHeight", "dir", "transitionEnd$1", "animating", "transition$1", "slideTo", "internal", "slideIndex", "preventInteractionOnTransition", "initialSlide", "allowSlideNext", "allowSlidePrev", "onSlideToWrapperTransitionEnd", "destroyed", "slideToLoop", "newIndex", "loopedSlides", "slideNext", "loopFix", "_clientLeft", "slidePrev", "normalize", "val", "prevIndex", "normalizedTranslate", "normalizedSnapGrid", "prevSnap", "slideReset", "slideToClosest", "currentSnap", "nextSnap", "slidesPerViewDynamic", "slideToIndex", "loopCreate", "loopFillGroupWithBlank", "blankSlidesNum", "blankNode", "loopAdditionalSlides", "prependSlides", "appendSlides", "cloneNode", "snapTranslate", "diff", "slideChanged", "slideChanged$1", "loop<PERSON><PERSON><PERSON>", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "cursor", "unsetGrabCursor", "grabCursor", "appendSlide", "prependSlide", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "manipulation", "<PERSON><PERSON>", "device", "ios", "android", "androidChrome", "desktop", "windows", "iphone", "ipod", "ipad", "<PERSON><PERSON>", "phonegap", "os", "osVersion", "webView", "osVersionArr", "metaViewport", "minimalUi", "pixelRatio", "devicePixelRatio", "onTouchStart", "touchEventsData", "touches", "originalEvent", "isTouchEvent", "which", "button", "isTouched", "isMoved", "noSwiping", "noSwipingSelector", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "threshold", "allowThresholdMove", "preventDefault", "formElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "onTouchMove", "preventedByNestedSwiper", "touchReleaseOnEdges", "diffX", "diffY", "sqrt", "pow", "touchAngle", "atan2", "PI", "touchMoveStopPropagation", "nested", "stopPropagation", "startTranslate", "allowMomentumBounce", "touchRatio", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "freeMode", "velocities", "position", "time", "onTouchEnd", "currentPos", "touchEndTime", "timeDiff", "lastClickTime", "clickTimeout", "freeModeMomentum", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "freeModeMinimumVelocity", "freeModeMomentumVelocityRatio", "momentumDuration", "freeModeMomentumRatio", "momentumDistance", "newPosition", "afterBouncePosition", "needsLoopFix", "doBounce", "bounceAmount", "freeModeMomentumBounceRatio", "freeModeMomentumBounce", "freeModeSticky", "longSwipesMs", "stopIndex", "groupSize", "ratio", "longSwipes", "longSwipesRatio", "shortSwipes", "onResize", "breakpoints", "setBreakpoint", "newTranslate", "min", "autoplay", "running", "paused", "run", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "attachEvents", "touchEvents", "wrapperEl", "touchEventsTarget", "start", "passiveListeners", "passive", "move", "end", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "paramValue", "breakpointP<PERSON>ms", "originalParams", "directionChanged", "needsReLoop", "changeDirection", "points", "point", "sort", "b", "breakpointsInverse", "innerWidth", "addClasses", "classNames", "suffixes", "suffix", "containerModifierClass", "removeClasses", "loadImage", "imageEl", "src", "srcset", "sizes", "checkForComplete", "image", "onReady", "complete", "onload", "onerror", "preloadImages", "imagesLoaded", "imagesToLoad", "updateOnImagesReady", "currentSrc", "images", "wasLocked", "checkOverflow$1", "defaults", "init", "uniqueNavElements", "noSwipingClass", "slideBlankClass", "prototypes", "extendedDefaults", "prototypeGroup", "protoMethod", "moduleParamName", "swiperParams", "passedParams", "swipers", "containerEl", "newParams", "touchEventsTouch", "touchEventsDesktop", "__proto__", "spv", "breakLoop", "translated", "translateValue", "newDirection", "needUpdate", "currentDirection", "slideEl", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "Device$1", "Support$1", "support", "Browser$1", "browser", "Resize", "resize", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "Observer", "func", "MutationObserver", "WebkitMutationObserver", "attach", "ObserverFunc", "mutations", "observerUpdate", "requestAnimationFrame", "observe", "attributes", "childList", "characterData", "observers", "observeParents", "containerParents", "observeSlideChildren", "disconnect", "Observer$1", "Virtual", "force", "ref", "ref$1", "addSlidesBefore", "addSlidesAfter", "ref$2", "previousFrom", "from", "previousTo", "previousSlidesGrid", "renderSlide", "previousOffset", "offsetProp", "slidesAfter", "slidesBefore", "onRendered", "lazy", "load", "renderExternal", "slidesToRender", "prependIndexes", "appendIndexes", "cache", "$slideEl", "numberOfNewSlides", "newCache", "cachedIndex", "Virtual$1", "beforeInit", "overwriteParams", "Keyboard", "handle", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "keyboard", "onlyInViewport", "inView", "windowWidth", "windowHeight", "innerHeight", "swiperOffset", "swiperCoord", "returnValue", "enable", "disable", "Keyboard$1", "isEventSupported", "isSupported", "element", "implementation", "hasFeature", "Mousewheel", "lastScrollTime", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "mousewheel", "releaseOnEdges", "delta", "rtlFactor", "forceToAxis", "invert", "sensitivity", "timeout", "autoplayDisableOnInteraction", "stop", "getTime", "eventsTarged", "Mousewheel$1", "Navigation", "$nextEl", "$prevEl", "disabledClass", "lockClass", "onPrevClick", "onNextClick", "nextEl", "prevEl", "Navigation$1", "hideOnClick", "hiddenClass", "toEdge", "fromEdge", "click", "isHidden", "Pagination", "current", "total", "paginationType", "bullets", "firstIndex", "lastIndex", "midIndex", "dynamicBullets", "bulletSize", "dynamicMainBullets", "dynamicBulletIndex", "bullet", "$bullet", "bulletIndex", "bulletActiveClass", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "formatFractionCurrent", "formatFractionTotal", "progressbarDirection", "progressbarOpposite", "scale", "scaleX", "scaleY", "renderCustom", "paginationHTML", "numberOfBullets", "renderBullet", "bulletClass", "renderFraction", "currentClass", "totalClass", "renderProgressbar", "progressbarFillClass", "clickable", "clickableClass", "modifierClass", "progressbarOppositeClass", "Pagination$1", "bulletElement", "number", "activeIndexChange", "snapIndexChange", "slidesLengthChange", "snapGridLengthChange", "Sc<PERSON><PERSON>", "scrollbar", "dragSize", "trackSize", "$dragEl", "newSize", "newPos", "hide", "opacity", "divider", "moveDivider", "display", "getPointerPosition", "clientX", "clientY", "setDragPosition", "positionRatio", "dragStartPos", "onDragStart", "dragTimeout", "onDragMove", "onDragEnd", "snapOnRelease", "enableDraggable", "activeListener", "disableDraggable", "$swiperEl", "dragEl", "draggable", "Scrollbar$1", "dragClass", "Parallax", "setTransform", "currentOpacity", "currentScale", "parallax", "parallaxEl", "$parallaxEl", "parallaxDuration", "Parallax$1", "Zoom", "getDistanceBetweenTouches", "x1", "y1", "x2", "y2", "onGestureStart", "zoom", "gesture", "fakeGestureTouched", "fakeGestureMoved", "scaleStart", "$imageEl", "$imageWrapEl", "maxRatio", "isScaling", "onGestureChange", "scaleMove", "minRatio", "onGestureEnd", "changedTouches", "touchesStart", "slideWidth", "slideHeight", "scaledWidth", "scaledHeight", "minX", "maxX", "minY", "maxY", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "onTransitionEnd", "out", "in", "touchX", "touchY", "offsetX", "offsetY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "Zoom$1", "containerClass", "zoomedSlideClass", "touchStart", "touchEnd", "doubleTap", "Lazy", "loadInSlide", "loadInDuplicate", "$images", "elementClass", "loadedClass", "loadingClass", "imageIndex", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "slideExist", "initialImageLoaded", "elIndex", "loadPrevNext", "loadPrevNextAmount", "amount", "maxIndex", "minIndex", "Lazy$1", "loadOnTransitionStart", "preloaderClass", "scroll", "scrollbarDragMove", "Controller", "LinearSpline", "i1", "i3", "binarySearch", "guess", "array", "interpolate", "getInterpolateFunction", "controller", "spline", "setTranslate$1", "multiplier", "controlledTranslate", "controlled", "control", "setControlledTranslate", "by", "inverse", "setControlledTransition", "Controller$1", "a11y", "makeElFocusable", "addElRole", "role", "addElLabel", "label", "disableEl", "enableEl", "onEnterKey", "$targetEl", "notify", "lastSlideMessage", "nextSlideMessage", "firstSlideMessage", "prevSlideMessage", "message", "notification", "liveRegion", "updateNavigation", "updatePagination", "bulletEl", "$bulletEl", "paginationBulletMessage", "A11y", "notificationClass", "paginationUpdate", "History", "pushState", "hashNavigation", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "scrollToSlide", "replaceState", "setHistoryPopState", "pathArray", "pathname", "part", "setHistory", "slugify", "includes", "currentState", "state", "slideHistory", "History$1", "HashNavigation", "onHashCange", "newHash", "activeSlideHash", "setHash", "slideHash", "watchState", "HashNavigation$1", "Autoplay", "$activeSlideEl", "reverseDirection", "stopOnLastSlide", "pause", "waitForTransition", "Autoplay$1", "disableOnInteraction", "beforeTransitionStart", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fade", "tx", "ty", "slideOpacity", "fadeEffect", "crossFade", "eventTriggered", "triggerEvents", "EffectFade", "C<PERSON>", "$cubeShadowEl", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "cubeEffect", "wrapperRotate", "shadow", "slideAngle", "round", "tz", "slideShadows", "shadowBefore", "shadowAfter", "shadowOffset", "shadowAngle", "sin", "cos", "scale1", "shadowScale", "scale2", "zFactor", "EffectCube", "Flip", "flipEffect", "limitRotation", "rotate", "rotateY", "rotateX", "zIndex", "EffectFlip", "Coverflow", "coverflowEffect", "center", "depth", "slideOffset", "offsetMultiplier", "modifier", "translateZ", "stretch", "slideTransform", "$shadowBeforeEl", "$shadowAfterEl", "ws", "<PERSON><PERSON><PERSON><PERSON>", "EffectCoverflow", "Thumbs", "thumbsParams", "thumbs", "swiperCreated", "thumbsContainerClass", "onThumbClick", "thumbsSwiper", "slideThumbActiveClass", "currentIndex", "initial", "newThumbsIndex", "currentThumbsIndex", "prevThumbsIndex", "nextThumbsIndex", "thumbsToActivate", "thumbActiveClass", "Thumbs$1", "slideChange"], "mappings": "oGAAC,SAASA,EAAEC,GAAqDC,EAAOC,QAAQF,EAAE,EAAQ,SAAzF,CAA6TG,GAAK,SAASJ,GAAG,OAAO,SAASA,GAAG,SAASC,EAAEI,GAAG,GAAGC,EAAED,GAAG,OAAOC,EAAED,GAAGF,QAAQ,IAAII,EAAED,EAAED,GAAG,CAACA,EAAEA,EAAEG,GAAE,EAAGL,QAAQ,IAAI,OAAOH,EAAEK,GAAGI,KAAKF,EAAEJ,QAAQI,EAAEA,EAAEJ,QAAQF,GAAGM,EAAEC,GAAE,EAAGD,EAAEJ,QAAQ,IAAIG,EAAE,GAAG,OAAOL,EAAES,EAAEV,EAAEC,EAAEU,EAAEL,EAAEL,EAAEI,EAAE,SAASL,GAAG,OAAOA,GAAGC,EAAEW,EAAE,SAASZ,EAAEM,EAAED,GAAGJ,EAAEY,EAAEb,EAAEM,IAAIQ,OAAOC,eAAef,EAAEM,EAAE,CAACU,cAAa,EAAGC,YAAW,EAAGC,IAAIb,KAAKJ,EAAEK,EAAE,SAASN,GAAG,IAAIM,EAAEN,GAAGA,EAAEmB,WAAW,WAAW,OAAOnB,EAAEoB,SAAS,WAAW,OAAOpB,GAAG,OAAOC,EAAEW,EAAEN,EAAE,IAAIA,GAAGA,GAAGL,EAAEY,EAAE,SAASb,EAAEC,GAAG,OAAOa,OAAOO,UAAUC,eAAeb,KAAKT,EAAEC,IAAIA,EAAEsB,EAAE,IAAItB,EAAEA,EAAEM,EAAE,GAAze,CAA6e,CAAC,SAASN,EAAEK,GAAGL,EAAEE,QAAQH,GAAG,SAASA,EAAEC,GAAGD,EAAEG,QAAQ,SAASH,EAAEC,EAAEK,EAAED,EAAEE,EAAEiB,GAAG,IAAIX,EAAEY,EAAEzB,EAAEA,GAAG,GAAG0B,SAAS1B,EAAEoB,QAAQ,WAAWM,GAAG,aAAaA,IAAIb,EAAEb,EAAEyB,EAAEzB,EAAEoB,SAAS,IAA4JZ,EAAxJe,EAAE,mBAAmBE,EAAEA,EAAEE,QAAQF,EAAyH,GAAvHxB,IAAIsB,EAAEK,OAAO3B,EAAE2B,OAAOL,EAAEM,gBAAgB5B,EAAE4B,gBAAgBN,EAAEO,WAAU,GAAIxB,IAAIiB,EAAEQ,YAAW,GAAIxB,IAAIgB,EAAES,SAASzB,GAAYiB,GAAGhB,EAAE,SAASR,GAAGA,EAAEA,GAAGI,KAAK6B,QAAQ7B,KAAK6B,OAAOC,YAAY9B,KAAK+B,QAAQ/B,KAAK+B,OAAOF,QAAQ7B,KAAK+B,OAAOF,OAAOC,WAAWlC,GAAG,oBAAoBoC,sBAAsBpC,EAAEoC,qBAAqB/B,GAAGA,EAAEI,KAAKL,KAAKJ,GAAGA,GAAGA,EAAEqC,uBAAuBrC,EAAEqC,sBAAsBC,IAAId,IAAID,EAAEgB,aAAa/B,GAAGH,IAAIG,EAAEH,GAAGG,EAAE,CAAC,IAAIG,EAAEY,EAAEQ,WAAWnB,EAAED,EAAEY,EAAEK,OAAOL,EAAEiB,aAAa7B,GAAGY,EAAEkB,cAAcjC,EAAEe,EAAEK,OAAO,SAAS5B,EAAEC,GAAG,OAAOO,EAAEC,KAAKR,GAAGW,EAAEZ,EAAEC,KAAKsB,EAAEiB,aAAa5B,EAAE,GAAG8B,OAAO9B,EAAEJ,GAAG,CAACA,GAAG,MAAM,CAACmC,SAAS9B,EAAEV,QAAQsB,EAAEE,QAAQJ,KAAK,SAASvB,EAAEC,EAAEK,GAAG,aAAaQ,OAAOC,eAAed,EAAE,aAAa,CAAC2C,OAAM,IAAK,IAAIvC,EAAEC,EAAE,GAAGC,EAAED,EAAEA,EAAED,GAAGmB,EAAElB,EAAE,GAAGO,EAAEP,EAAE,GAAGmB,EAAEZ,EAAEN,EAAEkB,EAAED,EAAEC,GAAE,EAAG,KAAK,KAAK,MAAMxB,EAAEmB,QAAQK,EAAEtB,SAAS,SAASH,EAAEC,EAAEK,GAAG,aAAaQ,OAAOC,eAAed,EAAE,aAAa,CAAC2C,OAAM,IAAK,IAAIvC,EAAEC,EAAE,GAAGC,EAAED,EAAEA,EAAED,GAAGmB,EAAElB,EAAE,GAAGO,EAAEP,EAAE,GAAGmB,EAAEZ,EAAEN,EAAEkB,EAAED,EAAEC,GAAE,EAAG,KAAK,KAAK,MAAMxB,EAAEmB,QAAQK,EAAEtB,SAAS,SAASH,EAAEC,EAAEK,GAAG,aAAa,SAASD,EAAEL,GAAG,OAAOA,GAAGA,EAAEmB,WAAWnB,EAAE,CAACoB,QAAQpB,GAAGc,OAAOC,eAAed,EAAE,aAAa,CAAC2C,OAAM,IAAK3C,EAAE4C,QAAQ5C,EAAE6C,YAAY7C,EAAE8C,OAAO9C,EAAE+C,YAAO,EAAO,IAAIzC,EAAED,EAAE,GAAGkB,EAAEnB,EAAEE,GAAGM,EAAEP,EAAE,GAAGmB,EAAEpB,EAAEQ,GAAGa,EAAEpB,EAAE,GAAGiB,EAAElB,EAAEqB,GAAGlB,EAAEyC,OAAOD,QAAQxB,EAAEJ,QAAQT,EAAEY,EAAEH,QAAQR,EAAEa,EAAEL,QAAQ8B,EAAE,SAASlD,EAAEC,GAAGA,IAAIsB,EAAEH,QAAQ+B,MAAMC,cAAchC,QAAQ,WAAW,OAAOnB,IAAID,EAAEqD,UAAU9B,EAAEH,QAAQkC,KAAK/B,EAAEH,SAASpB,EAAEqD,UAAU5B,EAAEL,QAAQkC,KAAK7B,EAAEL,UAAUmC,EAAE,CAACP,OAAOxC,EAAEuC,OAAOpC,EAAEmC,YAAYlC,EAAEiC,QAAQK,GAAGjD,EAAEmB,QAAQmC,EAAEtD,EAAE+C,OAAOxC,EAAEP,EAAE8C,OAAOpC,EAAEV,EAAE6C,YAAYlC,EAAEX,EAAE4C,QAAQK,GAAG,SAASlD,EAAEC,EAAEK,GAAG,aAAaQ,OAAOC,eAAed,EAAE,aAAa,CAAC2C,OAAM,IAAK3C,EAAEmB,QAAQ,CAACkC,KAAK,eAAeE,KAAK,WAAW,MAAM,CAACC,WAAW,iBAAiBC,MAAM,WAAWtD,KAAKuD,UAAUC,QAAQ,WAAWxD,KAAKuD,SAASvD,KAAKyD,SAASzD,KAAKyD,QAAQlC,SAASvB,KAAKyD,QAAQlC,QAAQ8B,aAAarD,KAAKqD,WAAWrD,KAAKyD,QAAQlC,QAAQ8B,aAAaK,QAAQ,WAAW1D,KAAKuD,UAAUI,SAAS,WAAW3D,KAAKuD,UAAUK,QAAQ,CAACL,OAAO,WAAWvD,KAAKyD,SAASzD,KAAKyD,QAAQd,QAAQ3C,KAAKyD,QAAQF,aAAa,SAAS3D,EAAEC,EAAEK,GAAG,aAAaQ,OAAOC,eAAed,EAAE,aAAa,CAAC2C,OAAM,IAAK,IAAIvC,EAAEC,EAAE,GAAGC,EAAE,SAASP,GAAG,OAAOA,GAAGA,EAAEmB,WAAWnB,EAAE,CAACoB,QAAQpB,GAA9C,CAAkDK,GAAGmB,EAAEyB,OAAOD,QAAQzC,EAAEa,QAAQ,mBAAmBN,OAAOmD,QAAQnD,OAAOC,eAAeD,OAAO,SAAS,CAAC8B,MAAM,SAAS5C,EAAEC,GAAG,GAAG,MAAMD,EAAE,MAAM,IAAIkE,UAAU,8CAA8C,IAAI,IAAI5D,EAAEQ,OAAOd,GAAGK,EAAE,EAAEA,EAAE8D,UAAUC,OAAO/D,IAAI,CAAC,IAAIE,EAAE4D,UAAU9D,GAAG,GAAG,MAAME,EAAE,IAAI,IAAIiB,KAAKjB,EAAEO,OAAOO,UAAUC,eAAeb,KAAKF,EAAEiB,KAAKlB,EAAEkB,GAAGjB,EAAEiB,IAAI,OAAOlB,GAAG+D,UAAS,EAAGrD,cAAa,IAAK,IAAIH,EAAE,CAAC,gBAAgB,cAAc,6BAA6B,2BAA2B,2BAA2B,yBAAyB,2BAA2B,yBAAyB,kBAAkB,gBAAgB,aAAa,YAAY,oBAAoB,aAAa,WAAW,QAAQ,MAAM,YAAY,cAAc,WAAW,iBAAiB,WAAW,WAAW,eAAe,gBAAgB,UAAUZ,EAAEmB,QAAQ,CAACkC,KAAK,SAASH,MAAM,CAACxB,QAAQ,CAAC2C,KAAKxD,OAAOM,QAAQ,WAAW,MAAM,KAAKgC,cAAc,CAACkB,KAAKxD,OAAOyD,UAAS,EAAGnD,QAAQ,WAAW,MAAM,MAAMoC,KAAK,WAAW,MAAM,CAACT,OAAO,KAAKyB,QAAQ,CAACC,aAAa,oBAAoBf,MAAM,WAAWtD,KAAK2C,QAAQ3C,KAAKsE,iBAAiBd,QAAQ,WAAW,IAAIxD,KAAK2C,OAAO,CAAC,IAAI/C,GAAE,EAAG,IAAI,IAAIC,KAAKG,KAAKoE,QAAQpE,KAAKoE,QAAQlD,eAAerB,IAAIG,KAAKuB,QAAQ1B,KAAKD,GAAE,EAAGI,KAAKoE,QAAQvE,GAAGG,KAAKuB,QAAQ1B,IAAID,EAAEI,KAAKuE,UAAUvE,KAAKsE,eAAetE,KAAKsE,kBAAkBE,UAAU,WAAWxE,KAAKuD,UAAUG,QAAQ,WAAW1D,KAAKuD,UAAUkB,cAAc,WAAWzE,KAAKuE,WAAU,WAAWvE,KAAK2C,SAAS3C,KAAK2C,OAAO+B,SAAS1E,KAAK2C,OAAO+B,iBAAiB1E,KAAK2C,YAAWiB,QAAQ,CAACL,OAAO,WAAWvD,KAAK2C,SAAS3C,KAAK2C,OAAOY,QAAQvD,KAAK2C,OAAOY,SAASvD,KAAK2C,OAAOgC,YAAY3E,KAAK2C,OAAOgC,WAAWpB,SAASvD,KAAK2C,OAAOiC,YAAY5E,KAAK2C,OAAOiC,WAAWpD,SAASxB,KAAK2C,OAAOiC,YAAY5E,KAAK2C,OAAOiC,WAAWrB,WAAWe,cAAc,WAAW,IAAI1E,EAAEc,OAAOmD,OAAO,GAAG7D,KAAKgD,cAAchD,KAAKuB,SAASvB,KAAK2C,OAAO,IAAIvB,EAAEpB,KAAK6E,IAAIjF,GAAGI,KAAK8E,aAAa9E,KAAK+E,MAAM,QAAQ/E,KAAK2C,SAASmC,WAAW,WAAW,IAAIlF,EAAEI,KAAKH,EAAEG,KAAKS,EAAEuE,SAAQ,SAAS9E,GAAGN,EAAE+C,OAAOsC,GAAG/E,GAAE,WAAWL,EAAEkF,MAAMG,MAAMrF,EAAE,CAACK,GAAGoC,OAAO6C,MAAMlE,UAAUmE,MAAM/E,KAAK0D,aAAalE,EAAEkF,MAAMG,MAAMrF,EAAE,CAACK,EAAEmF,QAAQ,WAAW,OAAOC,eAAehD,OAAO6C,MAAMlE,UAAUmE,MAAM/E,KAAK0D,uBAAqB,SAASnE,EAAEC,EAAEK,GAAG,aAAa,IAAID,EAAE,WAAW,IAAIL,EAAEI,KAAKH,EAAED,EAAE2F,eAAerF,EAAEN,EAAE4F,MAAMC,IAAI5F,EAAE,OAAOK,EAAE,MAAM,CAACwF,YAAY,oBAAoB,CAAC9F,EAAE+F,GAAG,eAAe/F,EAAEgG,GAAG,KAAK1F,EAAE,MAAM,CAAC2F,MAAMjG,EAAEwE,QAAQC,cAAc,CAACzE,EAAE+F,GAAG,YAAY,GAAG/F,EAAEgG,GAAG,KAAKhG,EAAE+F,GAAG,cAAc/F,EAAEgG,GAAG,KAAKhG,EAAE+F,GAAG,eAAe/F,EAAEgG,GAAG,KAAKhG,EAAE+F,GAAG,eAAe/F,EAAEgG,GAAG,KAAKhG,EAAE+F,GAAG,cAAc,IAAIxF,EAAE,GAAGiB,EAAE,CAACI,OAAOvB,EAAEwB,gBAAgBtB,GAAGN,EAAEwB,EAAED,GAAG,SAASxB,EAAEC,EAAEK,GAAG,aAAa,IAAID,EAAE,WAAW,IAAIL,EAAEI,KAAKH,EAAED,EAAE2F,eAAe,OAAO3F,EAAE4F,MAAMC,IAAI5F,GAAG,MAAM,CAACgG,MAAMjG,EAAEyD,YAAY,CAACzD,EAAE+F,GAAG,YAAY,IAAIxF,EAAE,GAAGiB,EAAE,CAACI,OAAOvB,EAAEwB,gBAAgBtB,GAAGN,EAAEwB,EAAED,S,sBCYvzL,SAAU0E,EAAQC,GAC8CjG,EAAOC,QAAUgG,KADlF,CAIE/F,GAAM,WAAc,aAapB,IAAIgG,EAA2B,qBAAbC,SAA4B,CAC5CC,KAAM,GACNC,iBAAkB,aAClBC,oBAAqB,aACrBC,cAAe,CACbC,KAAM,aACNC,SAAU,IAEZC,cAAe,WACb,OAAO,MAETC,iBAAkB,WAChB,MAAO,IAETC,eAAgB,WACd,OAAO,MAETC,YAAa,WACX,MAAO,CACLC,UAAW,eAGfC,cAAe,WACb,MAAO,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAc,aACdC,qBAAsB,WACpB,MAAO,MAIbC,SAAU,CAAEC,KAAM,KAChBnB,SAEAoB,EAAyB,qBAAXxE,OAA0B,CAC1CoD,SAAUD,EACVsB,UAAW,CACTC,UAAW,IAEbJ,SAAU,GACVK,QAAS,GACTC,YAAa,WACX,OAAOzH,MAETmG,iBAAkB,aAClBC,oBAAqB,aACrBsB,iBAAkB,WAChB,MAAO,CACLC,iBAAkB,WAChB,MAAO,MAIbC,MAAO,aACPC,KAAM,aACNC,OAAQ,GACRC,WAAY,aACZC,aAAc,cACZnF,OAgBAoF,EAAO,SAAcC,GAGvB,IAFA,IAAIC,EAAOnI,KAEFC,EAAI,EAAGA,EAAIiI,EAAIlE,OAAQ/D,GAAK,EACnCkI,EAAKlI,GAAKiI,EAAIjI,GAIhB,OAFAkI,EAAKnE,OAASkE,EAAIlE,OAEXhE,MAGT,SAASoI,EAAEC,EAAUC,GACnB,IAAIJ,EAAM,GACNjI,EAAI,EACR,GAAIoI,IAAaC,GACXD,aAAoBJ,EACtB,OAAOI,EAGX,GAAIA,EAEF,GAAwB,kBAAbA,EAAuB,CAChC,IAAIE,EACAC,EACAC,EAAOJ,EAASK,OACpB,GAAID,EAAKE,QAAQ,MAAQ,GAAKF,EAAKE,QAAQ,MAAQ,EAAG,CACpD,IAAIC,EAAW,MAQf,IAP4B,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,MAChB,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,SAChB,IAAxBH,EAAKE,QAAQ,QAAwC,IAAxBF,EAAKE,QAAQ,SAAgBC,EAAW,MAC1C,IAA3BH,EAAKE,QAAQ,YAAmBC,EAAW,SACf,IAA5BH,EAAKE,QAAQ,aAAoBC,EAAW,UAChDJ,EAAaxC,EAAIa,cAAc+B,GAC/BJ,EAAWK,UAAYJ,EAClBxI,EAAI,EAAGA,EAAIuI,EAAWzB,WAAW/C,OAAQ/D,GAAK,EACjDiI,EAAIY,KAAKN,EAAWzB,WAAW9G,SAUjC,IAFEsI,EALGD,GAA2B,MAAhBD,EAAS,IAAeA,EAASU,MAAM,aAK9CT,GAAWtC,GAAKS,iBAAiB4B,EAASK,QAH3C,CAAC1C,EAAIU,eAAe2B,EAASK,OAAOM,MAAM,KAAK,KAKlD/I,EAAI,EAAGA,EAAIsI,EAAIvE,OAAQ/D,GAAK,EAC3BsI,EAAItI,IAAMiI,EAAIY,KAAKP,EAAItI,SAG1B,GAAIoI,EAASY,UAAYZ,IAAahB,GAAOgB,IAAarC,EAE/DkC,EAAIY,KAAKT,QACJ,GAAIA,EAASrE,OAAS,GAAKqE,EAAS,GAAGY,SAE5C,IAAKhJ,EAAI,EAAGA,EAAIoI,EAASrE,OAAQ/D,GAAK,EACpCiI,EAAIY,KAAKT,EAASpI,IAIxB,OAAO,IAAIgI,EAAKC,GAOlB,SAASgB,EAAOhB,GAEd,IADA,IAAIiB,EAAc,GACTlJ,EAAI,EAAGA,EAAIiI,EAAIlE,OAAQ/D,GAAK,GACE,IAAjCkJ,EAAYR,QAAQT,EAAIjI,KAAckJ,EAAYL,KAAKZ,EAAIjI,IAEjE,OAAOkJ,EAIT,SAASC,EAASC,GAChB,GAAyB,qBAAdA,EACT,OAAOrJ,KAGT,IADA,IAAIoE,EAAUiF,EAAUL,MAAM,KACrB/I,EAAI,EAAGA,EAAImE,EAAQJ,OAAQ/D,GAAK,EACvC,IAAK,IAAIqJ,EAAI,EAAGA,EAAItJ,KAAKgE,OAAQsF,GAAK,EACb,qBAAZtJ,KAAKsJ,IAAmD,qBAAtBtJ,KAAKsJ,GAAGC,WAA6BvJ,KAAKsJ,GAAGC,UAAUrH,IAAIkC,EAAQnE,IAGpH,OAAOD,KAET,SAASwJ,EAAYH,GAEnB,IADA,IAAIjF,EAAUiF,EAAUL,MAAM,KACrB/I,EAAI,EAAGA,EAAImE,EAAQJ,OAAQ/D,GAAK,EACvC,IAAK,IAAIqJ,EAAI,EAAGA,EAAItJ,KAAKgE,OAAQsF,GAAK,EACb,qBAAZtJ,KAAKsJ,IAAmD,qBAAtBtJ,KAAKsJ,GAAGC,WAA6BvJ,KAAKsJ,GAAGC,UAAUE,OAAOrF,EAAQnE,IAGvH,OAAOD,KAET,SAAS0J,EAASL,GAChB,QAAKrJ,KAAK,IACHA,KAAK,GAAGuJ,UAAUI,SAASN,GAEpC,SAASO,EAAYP,GAEnB,IADA,IAAIjF,EAAUiF,EAAUL,MAAM,KACrB/I,EAAI,EAAGA,EAAImE,EAAQJ,OAAQ/D,GAAK,EACvC,IAAK,IAAIqJ,EAAI,EAAGA,EAAItJ,KAAKgE,OAAQsF,GAAK,EACb,qBAAZtJ,KAAKsJ,IAAmD,qBAAtBtJ,KAAKsJ,GAAGC,WAA6BvJ,KAAKsJ,GAAGC,UAAUM,OAAOzF,EAAQnE,IAGvH,OAAOD,KAET,SAAS8J,EAAKC,EAAOvH,GACnB,IAAIwH,EAAcjG,UAElB,GAAyB,IAArBA,UAAUC,QAAiC,kBAAV+F,EAEnC,OAAI/J,KAAK,GAAaA,KAAK,GAAGiK,aAAaF,QAC3C,EAIF,IAAK,IAAI9J,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpC,GAA2B,IAAvB+J,EAAYhG,OAEdhE,KAAKC,GAAGgH,aAAa8C,EAAOvH,QAI5B,IAAK,IAAI0H,KAAYH,EACnB/J,KAAKC,GAAGiK,GAAYH,EAAMG,GAC1BlK,KAAKC,GAAGgH,aAAaiD,EAAUH,EAAMG,IAI3C,OAAOlK,KAGT,SAASmK,EAAWL,GAClB,IAAK,IAAI7J,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpCD,KAAKC,GAAGmK,gBAAgBN,GAE1B,OAAO9J,KAET,SAASoD,EAAKiH,EAAK7H,GACjB,IAAI8H,EACJ,GAAqB,qBAAV9H,EAAX,CAkBA,IAAK,IAAIvC,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpCqK,EAAKtK,KAAKC,GACLqK,EAAGC,yBAA0BD,EAAGC,uBAAyB,IAC9DD,EAAGC,uBAAuBF,GAAO7H,EAEnC,OAAOxC,KApBL,GAFAsK,EAAKtK,KAAK,GAENsK,EAAI,CACN,GAAIA,EAAGC,wBAA2BF,KAAOC,EAAGC,uBAC1C,OAAOD,EAAGC,uBAAuBF,GAGnC,IAAIG,EAAUF,EAAGL,aAAc,QAAUI,GACzC,OAAIG,QAGJ,GAeN,SAASC,EAAUA,GACjB,IAAK,IAAIxK,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAAG,CACvC,IAAIyK,EAAU1K,KAAKC,GAAG+G,MACtB0D,EAAQC,gBAAkBF,EAC1BC,EAAQD,UAAYA,EAEtB,OAAOzK,KAET,SAAS4K,EAAWC,GACM,kBAAbA,IACTA,GAAsB,MAExB,IAAK,IAAI5K,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAAG,CACvC,IAAIyK,EAAU1K,KAAKC,GAAG+G,MACtB0D,EAAQI,yBAA2BD,EACnCH,EAAQK,mBAAqBF,EAE/B,OAAO7K,KAGT,SAASiF,IACP,IAAIpB,EAEAmH,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GAOnB,SAASM,EAAgB1L,GACvB,IAAI2L,EAAS3L,EAAE2L,OACf,GAAKA,EAAL,CACA,IAAIC,EAAY5L,EAAE2L,OAAOE,eAAiB,GAI1C,GAHID,EAAU7C,QAAQ/I,GAAK,GACzB4L,EAAUE,QAAQ9L,GAEhBwI,EAAEmD,GAAQI,GAAGR,GAAmBC,EAASlG,MAAMqG,EAAQC,QAGzD,IADA,IAAII,EAAUxD,EAAEmD,GAAQK,UACfC,EAAI,EAAGA,EAAID,EAAQ5H,OAAQ6H,GAAK,EACnCzD,EAAEwD,EAAQC,IAAIF,GAAGR,IAAmBC,EAASlG,MAAM0G,EAAQC,GAAIL,IAIzE,SAASM,EAAYlM,GACnB,IAAI4L,EAAY5L,GAAKA,EAAE2L,QAAS3L,EAAE2L,OAAOE,eAAsB,GAC3DD,EAAU7C,QAAQ/I,GAAK,GACzB4L,EAAUE,QAAQ9L,GAEpBwL,EAASlG,MAAMlF,KAAMwL,GA1BA,oBAAZR,EAAK,KACbnH,EAASmH,EAAME,EAAYrH,EAAO,GAAIuH,EAAWvH,EAAO,GAAIwH,EAAUxH,EAAO,GAC9EsH,OAAiBY,GAEdV,IAAWA,GAAU,GA0B1B,IAFA,IACI/B,EADA0C,EAASd,EAAUlC,MAAM,KAEpB/I,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAAG,CACvC,IAAIqK,EAAKtK,KAAKC,GACd,GAAKkL,EAaH,IAAK7B,EAAI,EAAGA,EAAI0C,EAAOhI,OAAQsF,GAAK,EAAG,CACrC,IAAI2C,EAAUD,EAAO1C,GAChBgB,EAAG4B,oBAAqB5B,EAAG4B,kBAAoB,IAC/C5B,EAAG4B,kBAAkBD,KAAY3B,EAAG4B,kBAAkBD,GAAW,IACtE3B,EAAG4B,kBAAkBD,GAASnD,KAAK,CACjCsC,SAAUA,EACVe,cAAeb,IAEjBhB,EAAGnE,iBAAiB8F,EAASX,EAAiBD,QApBhD,IAAK/B,EAAI,EAAGA,EAAI0C,EAAOhI,OAAQsF,GAAK,EAAG,CACrC,IAAI8C,EAAQJ,EAAO1C,GACdgB,EAAG+B,gBAAiB/B,EAAG+B,cAAgB,IACvC/B,EAAG+B,cAAcD,KAAU9B,EAAG+B,cAAcD,GAAS,IAC1D9B,EAAG+B,cAAcD,GAAOtD,KAAK,CAC3BsC,SAAUA,EACVe,cAAeL,IAEjBxB,EAAGnE,iBAAiBiG,EAAON,EAAaT,IAgB9C,OAAOrL,KAET,SAASsM,IACP,IAAIzI,EAEAmH,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GACI,oBAAZA,EAAK,KACbnH,EAASmH,EAAME,EAAYrH,EAAO,GAAIuH,EAAWvH,EAAO,GAAIwH,EAAUxH,EAAO,GAC9EsH,OAAiBY,GAEdV,IAAWA,GAAU,GAG1B,IADA,IAAIW,EAASd,EAAUlC,MAAM,KACpB/I,EAAI,EAAGA,EAAI+L,EAAOhI,OAAQ/D,GAAK,EAEtC,IADA,IAAImM,EAAQJ,EAAO/L,GACVqJ,EAAI,EAAGA,EAAItJ,KAAKgE,OAAQsF,GAAK,EAAG,CACvC,IAAIgB,EAAKtK,KAAKsJ,GACViD,OAAW,EAMf,IALKpB,GAAkBb,EAAG+B,cACxBE,EAAWjC,EAAG+B,cAAcD,GACnBjB,GAAkBb,EAAG4B,oBAC9BK,EAAWjC,EAAG4B,kBAAkBE,IAE9BG,GAAYA,EAASvI,OACvB,IAAK,IAAI6H,EAAIU,EAASvI,OAAS,EAAG6H,GAAK,EAAGA,GAAK,EAAG,CAChD,IAAIW,EAAUD,EAASV,GACnBT,GAAYoB,EAAQpB,WAAaA,GACnCd,EAAGlE,oBAAoBgG,EAAOI,EAAQL,cAAed,GACrDkB,EAASE,OAAOZ,EAAG,IACVT,GAAYoB,EAAQpB,UAAYoB,EAAQpB,SAASsB,WAAaF,EAAQpB,SAASsB,YAActB,GACtGd,EAAGlE,oBAAoBgG,EAAOI,EAAQL,cAAed,GACrDkB,EAASE,OAAOZ,EAAG,IACTT,IACVd,EAAGlE,oBAAoBgG,EAAOI,EAAQL,cAAed,GACrDkB,EAASE,OAAOZ,EAAG,KAM7B,OAAO7L,KAET,SAAS2M,IACP,IAAI3B,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAIzC,IAFA,IAAIe,EAAShB,EAAK,GAAGhC,MAAM,KACvBwC,EAAYR,EAAK,GACZ/K,EAAI,EAAGA,EAAI+L,EAAOhI,OAAQ/D,GAAK,EAEtC,IADA,IAAImM,EAAQJ,EAAO/L,GACVqJ,EAAI,EAAGA,EAAItJ,KAAKgE,OAAQsF,GAAK,EAAG,CACvC,IAAIgB,EAAKtK,KAAKsJ,GACVsD,OAAM,EACV,IACEA,EAAM,IAAIvF,EAAII,YAAY2E,EAAO,CAC/BS,OAAQrB,EACRsB,SAAS,EACTC,YAAY,IAEd,MAAOnN,GACPgN,EAAM5G,EAAIW,YAAY,SACtBiG,EAAIhG,UAAUwF,GAAO,GAAM,GAC3BQ,EAAIC,OAASrB,EAGflB,EAAGmB,cAAgBT,EAAKgC,QAAO,SAAU5J,EAAM6J,GAAa,OAAOA,EAAY,KAC/E3C,EAAG4C,cAAcN,GACjBtC,EAAGmB,cAAgB,UACZnB,EAAGmB,cAGd,OAAOzL,KAET,SAASmN,EAAcC,GACrB,IAEInN,EAFA+L,EAAS,CAAC,sBAAuB,iBACjCqB,EAAMrN,KAEV,SAASsN,EAAa1N,GAEpB,GAAIA,EAAE2L,SAAWvL,KAEjB,IADAoN,EAAS/M,KAAKL,KAAMJ,GACfK,EAAI,EAAGA,EAAI+L,EAAOhI,OAAQ/D,GAAK,EAClCoN,EAAIf,IAAIN,EAAO/L,GAAIqN,GAGvB,GAAIF,EACF,IAAKnN,EAAI,EAAGA,EAAI+L,EAAOhI,OAAQ/D,GAAK,EAClCoN,EAAIpI,GAAG+G,EAAO/L,GAAIqN,GAGtB,OAAOtN,KAET,SAASuN,EAAWC,GAClB,GAAIxN,KAAKgE,OAAS,EAAG,CACnB,GAAIwJ,EAAgB,CAElB,IAAIC,EAASzN,KAAKyN,SAClB,OAAOzN,KAAK,GAAG0N,YAAcC,WAAWF,EAAO9F,iBAAiB,iBAAmBgG,WAAWF,EAAO9F,iBAAiB,gBAExH,OAAO3H,KAAK,GAAG0N,YAEjB,OAAO,KAET,SAASE,EAAYJ,GACnB,GAAIxN,KAAKgE,OAAS,EAAG,CACnB,GAAIwJ,EAAgB,CAElB,IAAIC,EAASzN,KAAKyN,SAClB,OAAOzN,KAAK,GAAG6N,aAAeF,WAAWF,EAAO9F,iBAAiB,eAAiBgG,WAAWF,EAAO9F,iBAAiB,kBAEvH,OAAO3H,KAAK,GAAG6N,aAEjB,OAAO,KAET,SAASC,IACP,GAAI9N,KAAKgE,OAAS,EAAG,CACnB,IAAIsG,EAAKtK,KAAK,GACV+N,EAAMzD,EAAG0D,wBACT9H,EAAOF,EAAIE,KACX+H,EAAY3D,EAAG2D,WAAa/H,EAAK+H,WAAa,EAC9CC,EAAa5D,EAAG4D,YAAchI,EAAKgI,YAAc,EACjDC,EAAY7D,IAAOjD,EAAMA,EAAI+G,QAAU9D,EAAG6D,UAC1CE,EAAa/D,IAAOjD,EAAMA,EAAIiH,QAAUhE,EAAG+D,WAC/C,MAAO,CACLE,IAAMR,EAAIQ,IAAMJ,EAAaF,EAC7BO,KAAOT,EAAIS,KAAOH,EAAcH,GAIpC,OAAO,KAET,SAAST,IACP,OAAIzN,KAAK,GAAaqH,EAAIK,iBAAiB1H,KAAK,GAAI,MAC7C,GAET,SAASyO,EAAI1L,EAAOP,GAClB,IAAIvC,EACJ,GAAyB,IAArB8D,UAAUC,OAAc,CAC1B,GAAqB,kBAAVjB,EAEJ,CACL,IAAK9C,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAEhC,IAAK,IAAIyO,KAAQ3L,EACf/C,KAAKC,GAAG+G,MAAM0H,GAAQ3L,EAAM2L,GAGhC,OAAO1O,KARP,GAAIA,KAAK,GAAM,OAAOqH,EAAIK,iBAAiB1H,KAAK,GAAI,MAAM2H,iBAAiB5E,GAW/E,GAAyB,IAArBgB,UAAUC,QAAiC,kBAAVjB,EAAoB,CACvD,IAAK9C,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAChCD,KAAKC,GAAG+G,MAAMjE,GAASP,EAEzB,OAAOxC,KAET,OAAOA,KAGT,SAAS2O,EAAKvB,GAEZ,IAAKA,EAAY,OAAOpN,KAExB,IAAK,IAAIC,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAEpC,IAA2C,IAAvCmN,EAAS/M,KAAKL,KAAKC,GAAIA,EAAGD,KAAKC,IAEjC,OAAOD,KAIX,OAAOA,KAGT,SAASyI,EAAKA,GACZ,GAAoB,qBAATA,EACT,OAAOzI,KAAK,GAAKA,KAAK,GAAG6I,eAAYkD,EAGvC,IAAK,IAAI9L,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpCD,KAAKC,GAAG4I,UAAYJ,EAEtB,OAAOzI,KAGT,SAAS4O,EAAKA,GACZ,GAAoB,qBAATA,EACT,OAAI5O,KAAK,GACAA,KAAK,GAAG6O,YAAYnG,OAEtB,KAGT,IAAK,IAAIzI,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpCD,KAAKC,GAAG4O,YAAcD,EAExB,OAAO5O,KAET,SAAS2L,EAAGtD,GACV,IACIyG,EACA7O,EAFAqK,EAAKtK,KAAK,GAGd,IAAKsK,GAA0B,qBAAbjC,EAA4B,OAAO,EACrD,GAAwB,kBAAbA,EAAuB,CAChC,GAAIiC,EAAGyE,QAAW,OAAOzE,EAAGyE,QAAQ1G,GAC/B,GAAIiC,EAAG0E,sBAAyB,OAAO1E,EAAG0E,sBAAsB3G,GAChE,GAAIiC,EAAG2E,kBAAqB,OAAO3E,EAAG2E,kBAAkB5G,GAG7D,IADAyG,EAAc1G,EAAEC,GACXpI,EAAI,EAAGA,EAAI6O,EAAY9K,OAAQ/D,GAAK,EACvC,GAAI6O,EAAY7O,KAAOqK,EAAM,OAAO,EAEtC,OAAO,EACF,GAAIjC,IAAarC,EAAO,OAAOsE,IAAOtE,EACxC,GAAIqC,IAAahB,EAAO,OAAOiD,IAAOjD,EAE3C,GAAIgB,EAASY,UAAYZ,aAAoBJ,EAAM,CAEjD,IADA6G,EAAczG,EAASY,SAAW,CAACZ,GAAYA,EAC1CpI,EAAI,EAAGA,EAAI6O,EAAY9K,OAAQ/D,GAAK,EACvC,GAAI6O,EAAY7O,KAAOqK,EAAM,OAAO,EAEtC,OAAO,EAET,OAAO,EAET,SAAS4E,IACP,IACIjP,EADAkP,EAAQnP,KAAK,GAEjB,GAAImP,EAAO,CACTlP,EAAI,EAEJ,MAA2C,QAAnCkP,EAAQA,EAAMC,iBACG,IAAnBD,EAAMlG,WAAkBhJ,GAAK,GAEnC,OAAOA,GAKX,SAASoP,EAAGH,GACV,GAAqB,qBAAVA,EAAyB,OAAOlP,KAC3C,IACIsP,EADAtL,EAAShE,KAAKgE,OAElB,OAAIkL,EAAQlL,EAAS,EACZ,IAAIiE,EAAK,IAEdiH,EAAQ,GACVI,EAActL,EAASkL,EACO,IAAIjH,EAA9BqH,EAAc,EAAqB,GACvB,CAACtP,KAAKsP,MAEjB,IAAIrH,EAAK,CAACjI,KAAKkP,KAExB,SAASK,IACP,IAGIC,EAHAxE,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAIzC,IAAK,IAAIY,EAAI,EAAGA,EAAIb,EAAKhH,OAAQ6H,GAAK,EAAG,CACvC2D,EAAWxE,EAAKa,GAChB,IAAK,IAAI5L,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACpC,GAAwB,kBAAbuP,EAAuB,CAChC,IAAIC,EAAUzJ,EAAIa,cAAc,OAChC4I,EAAQ5G,UAAY2G,EACpB,MAAOC,EAAQC,WACb1P,KAAKC,GAAG0P,YAAYF,EAAQC,iBAEzB,GAAIF,aAAoBvH,EAC7B,IAAK,IAAIqB,EAAI,EAAGA,EAAIkG,EAASxL,OAAQsF,GAAK,EACxCtJ,KAAKC,GAAG0P,YAAYH,EAASlG,SAG/BtJ,KAAKC,GAAG0P,YAAYH,GAK1B,OAAOxP,KAET,SAAS4P,EAAQJ,GACf,IAAIvP,EACAqJ,EACJ,IAAKrJ,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAChC,GAAwB,kBAAbuP,EAAuB,CAChC,IAAIC,EAAUzJ,EAAIa,cAAc,OAEhC,IADA4I,EAAQ5G,UAAY2G,EACflG,EAAImG,EAAQ1I,WAAW/C,OAAS,EAAGsF,GAAK,EAAGA,GAAK,EACnDtJ,KAAKC,GAAG4P,aAAaJ,EAAQ1I,WAAWuC,GAAItJ,KAAKC,GAAG8G,WAAW,SAE5D,GAAIyI,aAAoBvH,EAC7B,IAAKqB,EAAI,EAAGA,EAAIkG,EAASxL,OAAQsF,GAAK,EACpCtJ,KAAKC,GAAG4P,aAAaL,EAASlG,GAAItJ,KAAKC,GAAG8G,WAAW,SAGvD/G,KAAKC,GAAG4P,aAAaL,EAAUxP,KAAKC,GAAG8G,WAAW,IAGtD,OAAO/G,KAET,SAAS8P,EAAKzH,GACZ,OAAIrI,KAAKgE,OAAS,EACZqE,EACErI,KAAK,GAAG+P,oBAAsB3H,EAAEpI,KAAK,GAAG+P,oBAAoBpE,GAAGtD,GAC1D,IAAIJ,EAAK,CAACjI,KAAK,GAAG+P,qBAEpB,IAAI9H,EAAK,IAGdjI,KAAK,GAAG+P,mBAA6B,IAAI9H,EAAK,CAACjI,KAAK,GAAG+P,qBACpD,IAAI9H,EAAK,IAEX,IAAIA,EAAK,IAElB,SAAS+H,EAAQ3H,GACf,IAAI4H,EAAU,GACV3F,EAAKtK,KAAK,GACd,IAAKsK,EAAM,OAAO,IAAIrC,EAAK,IAC3B,MAAOqC,EAAGyF,mBAAoB,CAC5B,IAAID,EAAOxF,EAAGyF,mBACV1H,EACED,EAAE0H,GAAMnE,GAAGtD,IAAa4H,EAAQnH,KAAKgH,GAClCG,EAAQnH,KAAKgH,GACtBxF,EAAKwF,EAEP,OAAO,IAAI7H,EAAKgI,GAElB,SAASC,EAAK7H,GACZ,GAAIrI,KAAKgE,OAAS,EAAG,CACnB,IAAIsG,EAAKtK,KAAK,GACd,OAAIqI,EACEiC,EAAG6F,wBAA0B/H,EAAEkC,EAAG6F,wBAAwBxE,GAAGtD,GACxD,IAAIJ,EAAK,CAACqC,EAAG6F,yBAEf,IAAIlI,EAAK,IAGdqC,EAAG6F,uBAAiC,IAAIlI,EAAK,CAACqC,EAAG6F,yBAC9C,IAAIlI,EAAK,IAElB,OAAO,IAAIA,EAAK,IAElB,SAASmI,EAAQ/H,GACf,IAAIgI,EAAU,GACV/F,EAAKtK,KAAK,GACd,IAAKsK,EAAM,OAAO,IAAIrC,EAAK,IAC3B,MAAOqC,EAAG6F,uBAAwB,CAChC,IAAID,EAAO5F,EAAG6F,uBACV9H,EACED,EAAE8H,GAAMvE,GAAGtD,IAAagI,EAAQvH,KAAKoH,GAClCG,EAAQvH,KAAKoH,GACtB5F,EAAK4F,EAEP,OAAO,IAAIjI,EAAKoI,GAElB,SAAStO,EAAOsG,GAEd,IADA,IAAIuD,EAAU,GACL3L,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EACT,OAAvBD,KAAKC,GAAGqQ,aACNjI,EACED,EAAEpI,KAAKC,GAAGqQ,YAAY3E,GAAGtD,IAAauD,EAAQ9C,KAAK9I,KAAKC,GAAGqQ,YAE/D1E,EAAQ9C,KAAK9I,KAAKC,GAAGqQ,aAI3B,OAAOlI,EAAEc,EAAO0C,IAElB,SAASA,EAAQvD,GAEf,IADA,IAAIuD,EAAU,GACL3L,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAAG,CACvC,IAAI8B,EAAS/B,KAAKC,GAAGqQ,WACrB,MAAOvO,EACDsG,EACED,EAAErG,GAAQ4J,GAAGtD,IAAauD,EAAQ9C,KAAK/G,GAE3C6J,EAAQ9C,KAAK/G,GAEfA,EAASA,EAAOuO,WAGpB,OAAOlI,EAAEc,EAAO0C,IAElB,SAAS2E,EAAQlI,GACf,IAAIkI,EAAUvQ,KACd,MAAwB,qBAAbqI,EACF,IAAIJ,EAAK,KAEbsI,EAAQ5E,GAAGtD,KACdkI,EAAUA,EAAQ3E,QAAQvD,GAAUgH,GAAG,IAElCkB,GAET,SAASC,EAAKnI,GAEZ,IADA,IAAIoI,EAAgB,GACXxQ,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAEpC,IADA,IAAIyQ,EAAQ1Q,KAAKC,GAAGwG,iBAAiB4B,GAC5BiB,EAAI,EAAGA,EAAIoH,EAAM1M,OAAQsF,GAAK,EACrCmH,EAAc3H,KAAK4H,EAAMpH,IAG7B,OAAO,IAAIrB,EAAKwI,GAElB,SAAS3J,EAASuB,GAEhB,IADA,IAAIvB,EAAW,GACN7G,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAGpC,IAFA,IAAI8G,EAAa/G,KAAKC,GAAG8G,WAEhBuC,EAAI,EAAGA,EAAIvC,EAAW/C,OAAQsF,GAAK,EACrCjB,EAEiC,IAA3BtB,EAAWuC,GAAGL,UAAkBb,EAAErB,EAAWuC,IAAIqC,GAAGtD,IAC7DvB,EAASgC,KAAK/B,EAAWuC,IAFM,IAA3BvC,EAAWuC,GAAGL,UAAkBnC,EAASgC,KAAK/B,EAAWuC,IAMnE,OAAO,IAAIrB,EAAKiB,EAAOpC,IAEzB,SAAS2C,IACP,IAAK,IAAIxJ,EAAI,EAAGA,EAAID,KAAKgE,OAAQ/D,GAAK,EAChCD,KAAKC,GAAGqQ,YAActQ,KAAKC,GAAGqQ,WAAWK,YAAY3Q,KAAKC,IAEhE,OAAOD,KAET,SAASkC,IACP,IAAI8I,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAEzC,IACIhL,EACAqJ,EAFA+D,EAAMrN,KAGV,IAAKC,EAAI,EAAGA,EAAI+K,EAAKhH,OAAQ/D,GAAK,EAAG,CACnC,IAAI2Q,EAAQxI,EAAE4C,EAAK/K,IACnB,IAAKqJ,EAAI,EAAGA,EAAIsH,EAAM5M,OAAQsF,GAAK,EACjC+D,EAAIA,EAAIrJ,QAAU4M,EAAMtH,GACxB+D,EAAIrJ,QAAU,EAGlB,OAAOqJ,EA7nBTjF,EAAEyI,GAAK5I,EAAKhH,UACZmH,EAAE0I,MAAQ7I,EACVG,EAAEH,KAAOA,EA8nBT,IAAI8I,EAAU,CACZ3H,SAAUA,EACVI,YAAaA,EACbE,SAAUA,EACVE,YAAaA,EACbE,KAAMA,EACNK,WAAYA,EACZ/G,KAAMA,EACNqH,UAAWA,EACXG,WAAYA,EACZ3F,GAAIA,EACJqH,IAAKA,EACLK,QAASA,EACTQ,cAAeA,EACfI,WAAYA,EACZK,YAAaA,EACbE,OAAQA,EACRW,IAAKA,EACLE,KAAMA,EACNlG,KAAMA,EACNmG,KAAMA,EACNjD,GAAIA,EACJuD,MAAOA,EACPG,GAAIA,EACJE,OAAQA,EACRK,QAASA,EACTE,KAAMA,EACNE,QAASA,EACTE,KAAMA,EACNE,QAASA,EACTrO,OAAQA,EACR6J,QAASA,EACT2E,QAASA,EACTC,KAAMA,EACN1J,SAAUA,EACV2C,OAAQA,EACRvH,IAAKA,EACLuL,OAAQA,GAGV/M,OAAOsQ,KAAKD,GAAS/L,SAAQ,SAAUiM,GACrC7I,EAAEyI,GAAGI,GAAc7I,EAAEyI,GAAGI,IAAeF,EAAQE,MAGjD,IAAIC,EAAQ,CACVC,YAAa,SAAqBC,GAChC,IAAIC,EAASD,EACb1Q,OAAOsQ,KAAKK,GAAQrM,SAAQ,SAAUqF,GACpC,IACEgH,EAAOhH,GAAO,KACd,MAAOzK,IAGT,WACSyR,EAAOhH,GACd,MAAOzK,SAKb0R,SAAU,SAAkBlE,EAAUmE,GAGpC,YAFe,IAAVA,IAAmBA,EAAQ,GAEzBxJ,WAAWqF,EAAUmE,IAE9BC,IAAK,WACH,OAAO3J,KAAK2J,OAEdC,aAAc,SAAsBnH,EAAIoH,GAGtC,IAAIC,EACAC,EACAC,OAJU,IAATH,IAAkBA,EAAO,KAM9B,IAAII,EAAWzK,EAAIK,iBAAiB4C,EAAI,MA+BxC,OA7BIjD,EAAI0K,iBACNH,EAAeE,EAASrH,WAAaqH,EAASnH,gBAC1CiH,EAAa5I,MAAM,KAAKhF,OAAS,IACnC4N,EAAeA,EAAa5I,MAAM,MAAMgJ,KAAI,SAAU3Q,GAAK,OAAOA,EAAEgE,QAAQ,IAAK,QAAS4M,KAAK,OAIjGJ,EAAkB,IAAIxK,EAAI0K,gBAAiC,SAAjBH,EAA0B,GAAKA,KAEzEC,EAAkBC,EAASI,cAAgBJ,EAASK,YAAcL,EAASM,aAAeN,EAASO,aAAeP,EAASrH,WAAaqH,EAASnK,iBAAiB,aAAatC,QAAQ,aAAc,sBACrMsM,EAASE,EAAgBS,WAAWtJ,MAAM,MAG/B,MAAT0I,IAEyBE,EAAvBvK,EAAI0K,gBAAkCF,EAAgBU,IAE/B,KAAlBZ,EAAO3N,OAAgC2J,WAAWgE,EAAO,KAE5ChE,WAAWgE,EAAO,KAE7B,MAATD,IAEyBE,EAAvBvK,EAAI0K,gBAAkCF,EAAgBW,IAE/B,KAAlBb,EAAO3N,OAAgC2J,WAAWgE,EAAO,KAE5ChE,WAAWgE,EAAO,KAEnCC,GAAgB,GAEzBa,cAAe,SAAuBC,GACpC,IAEIzS,EACA0S,EACAC,EACA5O,EALA6O,EAAQ,GACRC,EAAaJ,GAAOrL,EAAIF,SAAS4L,KAKrC,GAA0B,kBAAfD,GAA2BA,EAAW9O,OAK/C,IAJA8O,EAAaA,EAAWnK,QAAQ,MAAQ,EAAImK,EAAWzN,QAAQ,QAAS,IAAM,GAC9EsN,EAASG,EAAW9J,MAAM,KAAKgE,QAAO,SAAUgG,GAAc,MAAsB,KAAfA,KACrEhP,EAAS2O,EAAO3O,OAEX/D,EAAI,EAAGA,EAAI+D,EAAQ/D,GAAK,EAC3B2S,EAAQD,EAAO1S,GAAGoF,QAAQ,QAAS,IAAI2D,MAAM,KAC7C6J,EAAMI,mBAAmBL,EAAM,KAA2B,qBAAbA,EAAM,QAAqB7G,EAAYkH,mBAAmBL,EAAM,KAAO,GAGxH,OAAOC,GAETK,SAAU,SAAkBzS,GAC1B,MAAoB,kBAANA,GAAwB,OAANA,GAAcA,EAAE0S,aAAe1S,EAAE0S,cAAgBzS,QAEnF0S,OAAQ,WACN,IAAIpI,EAAO,GAAIqI,EAAQtP,UAAUC,OACjC,MAAQqP,IAAUrI,EAAMqI,GAAUtP,UAAWsP,GAG7C,IADA,IAAIC,EAAK5S,OAAOsK,EAAK,IACZ/K,EAAI,EAAGA,EAAI+K,EAAKhH,OAAQ/D,GAAK,EAAG,CACvC,IAAIsT,EAAavI,EAAK/K,GACtB,QAAmB8L,IAAfwH,GAA2C,OAAfA,EAE9B,IADA,IAAIC,EAAY9S,OAAOsQ,KAAKtQ,OAAO6S,IAC1BE,EAAY,EAAGxI,EAAMuI,EAAUxP,OAAQyP,EAAYxI,EAAKwI,GAAa,EAAG,CAC/E,IAAIC,EAAUF,EAAUC,GACpBE,EAAOjT,OAAOkT,yBAAyBL,EAAYG,QAC1C3H,IAAT4H,GAAsBA,EAAK9S,aACzBqQ,EAAMgC,SAASI,EAAGI,KAAaxC,EAAMgC,SAASK,EAAWG,IAC3DxC,EAAMkC,OAAOE,EAAGI,GAAUH,EAAWG,KAC3BxC,EAAMgC,SAASI,EAAGI,KAAaxC,EAAMgC,SAASK,EAAWG,KACnEJ,EAAGI,GAAW,GACdxC,EAAMkC,OAAOE,EAAGI,GAAUH,EAAWG,KAErCJ,EAAGI,GAAWH,EAAWG,KAMnC,OAAOJ,IAIPO,EAAW,WACb,IAAIC,EAAU9N,EAAIa,cAAc,OAChC,MAAO,CACLkN,MAAQ1M,EAAI2M,YAAqC,IAAxB3M,EAAI2M,UAAUD,OAAoB,WACzD,SAAW1M,EAAIC,UAAU2M,eAAiB,GAAO,iBAAkB5M,GAASA,EAAI6M,eAAiBlO,aAAeqB,EAAI6M,eAD5D,GAI1DC,iBAAkB9M,EAAIC,UAAU8M,gBAAkB/M,EAAIgN,cAAiB,mBAAoBhN,EAAIC,WAAaD,EAAIC,UAAU2M,eAAiB,GAC3IK,wBAAyBjN,EAAIC,UAAUiN,iBAEvC3J,WAAa,WACX,IAAI5D,EAAQ8M,EAAQ9M,MACpB,MAAQ,eAAgBA,GAAS,qBAAsBA,GAAS,kBAAmBA,EAFzE,GAIZwN,aAAenN,EAAI2M,YAA+C,IAAlC3M,EAAI2M,UAAUS,iBAA8B,WAC1E,IAAIzN,EAAQ8M,EAAQ9M,MACpB,MAAQ,sBAAuBA,GAAS,mBAAoBA,GAAS,iBAAkBA,GAAS,kBAAmBA,GAAS,gBAAiBA,EAFpE,GAK3E0N,QAAU,WAGR,IAFA,IAAI1N,EAAQ8M,EAAQ9M,MAChByG,EAAS,yKAA2KzE,MAAM,KACrL/I,EAAI,EAAGA,EAAIwN,EAAOzJ,OAAQ/D,GAAK,EACtC,GAAIwN,EAAOxN,KAAM+G,EAAS,OAAO,EAEnC,OAAO,EANA,GAST2N,SAAW,WACT,MAAQ,qBAAsBtN,GAAO,2BAA4BA,EADzD,GAIVuN,gBAAkB,WAChB,IAAIC,GAAkB,EACtB,IACE,IAAIC,EAAOpU,OAAOC,eAAe,GAAI,UAAW,CAE9CG,IAAK,WACH+T,GAAkB,KAGtBxN,EAAIlB,iBAAiB,sBAAuB,KAAM2O,GAClD,MAAOlV,IAGT,OAAOiV,EAbQ,GAgBjBE,SAAW,WACT,MAAO,mBAAoB1N,EADnB,IAhDA,GAsDV2N,EAAW,WACb,SAASC,IACP,IAAIC,EAAK7N,EAAIC,UAAUC,UAAUjC,cACjC,OAAQ4P,EAAGvM,QAAQ,WAAa,GAAKuM,EAAGvM,QAAQ,UAAY,GAAKuM,EAAGvM,QAAQ,WAAa,EAE3F,MAAO,CACLwM,OAAQ9N,EAAIC,UAAUC,UAAUwB,MAAM,eAAiB1B,EAAIC,UAAUC,UAAUwB,MAAM,SACrFqM,SAAU/N,EAAIC,UAAUC,UAAUwB,MAAM,SACxCkM,SAAUA,IACVI,YAAa,+CAA+CC,KAAKjO,EAAIC,UAAUC,YATrE,GAaVgO,EAAc,SAAqB5C,QACrB,IAAXA,IAAoBA,EAAS,IAElC,IAAIxK,EAAOnI,KACXmI,EAAKwK,OAASA,EAGdxK,EAAKqN,gBAAkB,GAEnBrN,EAAKwK,QAAUxK,EAAKwK,OAAO1N,IAC7BvE,OAAOsQ,KAAK7I,EAAKwK,OAAO1N,IAAID,SAAQ,SAAUyQ,GAC5CtN,EAAKlD,GAAGwQ,EAAWtN,EAAKwK,OAAO1N,GAAGwQ,QAKpCC,EAAkB,CAAEC,WAAY,CAAE/U,cAAc,IA4KpD,SAASgV,IACP,IACIC,EACAC,EAFAnT,EAAS3C,KAGT6E,EAAMlC,EAAOkC,IAEfgR,EADiC,qBAAxBlT,EAAOgQ,OAAOkD,MACflT,EAAOgQ,OAAOkD,MAEdhR,EAAI,GAAGkR,YAGfD,EADkC,qBAAzBnT,EAAOgQ,OAAOmD,OACdnT,EAAOgQ,OAAOmD,OAEdjR,EAAI,GAAGmR,aAEH,IAAVH,GAAelT,EAAOsT,gBAA+B,IAAXH,GAAgBnT,EAAOuT,eAKtEL,EAAQA,EAAQM,SAAStR,EAAI4J,IAAI,gBAAiB,IAAM0H,SAAStR,EAAI4J,IAAI,iBAAkB,IAC3FqH,EAASA,EAASK,SAAStR,EAAI4J,IAAI,eAAgB,IAAM0H,SAAStR,EAAI4J,IAAI,kBAAmB,IAE7FyC,EAAMkC,OAAOzQ,EAAQ,CACnBkT,MAAOA,EACPC,OAAQA,EACRM,KAAMzT,EAAOsT,eAAiBJ,EAAQC,KAI1C,SAASO,IACP,IAAI1T,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAEhB2D,EAAa3T,EAAO2T,WACpBC,EAAa5T,EAAOyT,KACpBI,EAAM7T,EAAO8T,aACbC,EAAW/T,EAAO+T,SAClBC,EAAYhU,EAAOiU,SAAWjE,EAAOiE,QAAQC,QAC7CC,EAAuBH,EAAYhU,EAAOiU,QAAQG,OAAO/S,OAASrB,EAAOoU,OAAO/S,OAChF+S,EAAST,EAAWxP,SAAU,IAAOnE,EAAOgQ,OAAiB,YAC7DqE,EAAeL,EAAYhU,EAAOiU,QAAQG,OAAO/S,OAAS+S,EAAO/S,OACjEiT,EAAW,GACXC,EAAa,GACbC,EAAkB,GAElBC,EAAezE,EAAO0E,mBACE,oBAAjBD,IACTA,EAAezE,EAAO0E,mBAAmBhX,KAAKsC,IAGhD,IAAI2U,EAAc3E,EAAO4E,kBACE,oBAAhBD,IACTA,EAAc3E,EAAO4E,kBAAkBlX,KAAKsC,IAG9C,IAAI6U,EAAyB7U,EAAOsU,SAASjT,OACzCyT,EAA2B9U,EAAOsU,SAASjT,OAE3C0T,EAAe/E,EAAO+E,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChB1I,EAAQ,EACZ,GAA0B,qBAAfqH,EAAX,CAaA,IAAIsB,EAaAC,EAvBwB,kBAAjBJ,GAA6BA,EAAa/O,QAAQ,MAAQ,IACnE+O,EAAgB/J,WAAW+J,EAAarS,QAAQ,IAAK,KAAO,IAAOkR,GAGrE5T,EAAOoV,aAAeL,EAGlBlB,EAAOO,EAAOtI,IAAI,CAAEuJ,WAAY,GAAIC,UAAW,KAC5ClB,EAAOtI,IAAI,CAAEyJ,YAAa,GAAIC,aAAc,KAG/CxF,EAAOyF,gBAAkB,IAEzBP,EADEQ,KAAKC,MAAMtB,EAAerE,EAAOyF,mBAAqBpB,EAAerU,EAAOgQ,OAAOyF,gBAC5DpB,EAEAqB,KAAKE,KAAKvB,EAAerE,EAAOyF,iBAAmBzF,EAAOyF,gBAExD,SAAzBzF,EAAO6F,eAA2D,QAA/B7F,EAAO8F,sBAC5CZ,EAAyBQ,KAAKK,IAAIb,EAAwBlF,EAAO6F,cAAgB7F,EAAOyF,mBAS5F,IAHA,IA8IIO,EA9IAP,EAAkBzF,EAAOyF,gBACzBQ,EAAef,EAAyBO,EACxCS,EAAiBR,KAAKC,MAAMtB,EAAerE,EAAOyF,iBAC7CnY,EAAI,EAAGA,EAAI+W,EAAc/W,GAAK,EAAG,CACxC6X,EAAY,EACZ,IAAIgB,EAAQ/B,EAAO1H,GAAGpP,GACtB,GAAI0S,EAAOyF,gBAAkB,EAAG,CAE9B,IAAIW,OAAqB,EACrBC,OAAS,EACTC,OAAM,EACV,GACkC,WAA/BtG,EAAO8F,qBAC2B,QAA/B9F,EAAO8F,qBAAiC9F,EAAOuG,eAAiB,EACpE,CACA,GAAmC,WAA/BvG,EAAO8F,oBACTO,EAASX,KAAKC,MAAMrY,EAAImY,GACxBa,EAAMhZ,EAAK+Y,EAASZ,GAChBY,EAASH,GAAmBG,IAAWH,GAAkBI,IAAQb,EAAkB,KACrFa,GAAO,EACHA,GAAOb,IACTa,EAAM,EACND,GAAU,QAGT,CACL,IAAIG,EAAad,KAAKC,MAAMrY,EAAI0S,EAAOuG,gBACvCD,EAAMZ,KAAKC,MAAMrY,EAAI0S,EAAO6F,eAAiBW,EAAaxG,EAAOyF,gBACjEY,EAAS/Y,EAAIgZ,EAAMtG,EAAO6F,cAAgBW,EAAaxG,EAAO6F,cAEhEO,EAAqBC,EAAWC,EAAMpB,EAA0BO,EAChEU,EACGrK,IAAI,CACH,4BAA6BsK,EAC7B,yBAA0BA,EAC1B,iBAAkBA,EAClB,gBAAiBA,EACjBK,MAAOL,SAGXE,EAAMZ,KAAKC,MAAMrY,EAAI2Y,GACrBI,EAAS/Y,EAAKgZ,EAAML,EAEtBE,EACGrK,IACE,WAAa9L,EAAOsT,eAAiB,MAAQ,QACrC,IAARgD,GAAatG,EAAO+E,cAAoB/E,EAAmB,aAAI,MAEjE7I,KAAK,qBAAsBkP,GAC3BlP,KAAK,kBAAmBmP,GAE7B,GAA6B,SAAzBH,EAAMrK,IAAI,WAAd,CAEA,GAA6B,SAAzBkE,EAAO6F,cAA0B,CACnC,IAAIa,EAAchS,EAAIK,iBAAiBoR,EAAM,GAAI,MAC7CQ,EAAmBR,EAAM,GAAG9R,MAAMyD,UAClC8O,EAAyBT,EAAM,GAAG9R,MAAM2D,gBAO5C,GANI2O,IACFR,EAAM,GAAG9R,MAAMyD,UAAY,QAEzB8O,IACFT,EAAM,GAAG9R,MAAM2D,gBAAkB,QAE/BgI,EAAO6G,aACT1B,EAAYnV,EAAOsT,eACf6C,EAAMvL,YAAW,GACjBuL,EAAMlL,aAAY,QAGtB,GAAIjL,EAAOsT,eAAgB,CACzB,IAAIJ,EAAQlI,WAAW0L,EAAY1R,iBAAiB,UAChD8R,EAAc9L,WAAW0L,EAAY1R,iBAAiB,iBACtD+R,EAAe/L,WAAW0L,EAAY1R,iBAAiB,kBACvDqQ,EAAarK,WAAW0L,EAAY1R,iBAAiB,gBACrDuQ,EAAcvK,WAAW0L,EAAY1R,iBAAiB,iBACtDgS,EAAYN,EAAY1R,iBAAiB,cAE3CmQ,EADE6B,GAA2B,eAAdA,IAA+B3E,EAAQG,KAC1CU,EAAQmC,EAAaE,EAErBrC,EAAQ4D,EAAcC,EAAe1B,EAAaE,MAE3D,CACL,IAAIpC,EAASnI,WAAW0L,EAAY1R,iBAAiB,WACjDiS,EAAajM,WAAW0L,EAAY1R,iBAAiB,gBACrDkS,EAAgBlM,WAAW0L,EAAY1R,iBAAiB,mBACxDsQ,EAAYtK,WAAW0L,EAAY1R,iBAAiB,eACpDwQ,EAAexK,WAAW0L,EAAY1R,iBAAiB,kBACvDmS,EAAcT,EAAY1R,iBAAiB,cAE7CmQ,EADEgC,GAA+B,eAAhBA,IAAiC9E,EAAQG,KAC9CW,EAASmC,EAAYE,EAErBrC,EAAS8D,EAAaC,EAAgB5B,EAAYE,EAIhEmB,IACFR,EAAM,GAAG9R,MAAMyD,UAAY6O,GAEzBC,IACFT,EAAM,GAAG9R,MAAM2D,gBAAkB4O,GAE/B5G,EAAO6G,eAAgB1B,EAAYO,KAAKC,MAAMR,SAElDA,GAAavB,GAAe5D,EAAO6F,cAAgB,GAAKd,GAAiB/E,EAAO6F,cAC5E7F,EAAO6G,eAAgB1B,EAAYO,KAAKC,MAAMR,IAE9Cf,EAAO9W,KACL0C,EAAOsT,eACTc,EAAO9W,GAAG+G,MAAM6O,MAAQiC,EAAY,KAEpCf,EAAO9W,GAAG+G,MAAM8O,OAASgC,EAAY,MAIvCf,EAAO9W,KACT8W,EAAO9W,GAAG8Z,gBAAkBjC,GAE9BX,EAAgBrO,KAAKgP,GAGjBnF,EAAOqH,gBACTrC,EAAgBA,EAAiBG,EAAY,EAAMF,EAAgB,EAAKF,EAClD,IAAlBE,GAA6B,IAAN3X,IAAW0X,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC/E,IAANzX,IAAW0X,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC9DW,KAAK4B,IAAItC,GAAiB,OAAYA,EAAgB,GACtDhF,EAAO6G,eAAgB7B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhF,EAAOuG,iBAAmB,GAAKjC,EAASnO,KAAK6O,GAC3DT,EAAWpO,KAAK6O,KAEZhF,EAAO6G,eAAgB7B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUhF,EAAOuG,iBAAmB,GAAKjC,EAASnO,KAAK6O,GAC3DT,EAAWpO,KAAK6O,GAChBA,EAAgBA,EAAgBG,EAAYJ,GAG9C/U,EAAOoV,aAAeD,EAAYJ,EAElCE,EAAgBE,EAEhB5I,GAAS,GAcX,GAZAvM,EAAOoV,YAAcM,KAAKK,IAAI/V,EAAOoV,YAAaxB,GAAce,EAI9Dd,GAAOE,IAA+B,UAAlB/D,EAAOuH,QAAwC,cAAlBvH,EAAOuH,SACxD5D,EAAW7H,IAAI,CAAEoH,MAASlT,EAAOoV,YAAcpF,EAAO+E,aAAgB,OAEnE7D,EAAQa,UAAW/B,EAAOwH,iBACzBxX,EAAOsT,eAAkBK,EAAW7H,IAAI,CAAEoH,MAASlT,EAAOoV,YAAcpF,EAAO+E,aAAgB,OAC5FpB,EAAW7H,IAAI,CAAEqH,OAAUnT,EAAOoV,YAAcpF,EAAO+E,aAAgB,QAG5E/E,EAAOyF,gBAAkB,IAC3BzV,EAAOoV,aAAeD,EAAYnF,EAAO+E,cAAgBG,EACzDlV,EAAOoV,YAAcM,KAAKE,KAAK5V,EAAOoV,YAAcpF,EAAOyF,iBAAmBzF,EAAO+E,aACjF/U,EAAOsT,eAAkBK,EAAW7H,IAAI,CAAEoH,MAASlT,EAAOoV,YAAcpF,EAAO+E,aAAgB,OAC5FpB,EAAW7H,IAAI,CAAEqH,OAAUnT,EAAOoV,YAAcpF,EAAO+E,aAAgB,OAC1E/E,EAAOqH,gBAAgB,CACzBrB,EAAgB,GAChB,IAAK,IAAIyB,EAAM,EAAGA,EAAMnD,EAASjT,OAAQoW,GAAO,EAAG,CACjD,IAAIC,EAAiBpD,EAASmD,GAC1BzH,EAAO6G,eAAgBa,EAAiBhC,KAAKC,MAAM+B,IACnDpD,EAASmD,GAAOzX,EAAOoV,YAAcd,EAAS,IAAM0B,EAAc7P,KAAKuR,GAE7EpD,EAAW0B,EAKf,IAAKhG,EAAOqH,eAAgB,CAC1BrB,EAAgB,GAChB,IAAK,IAAI2B,GAAM,EAAGA,GAAMrD,EAASjT,OAAQsW,IAAO,EAAG,CACjD,IAAIC,GAAmBtD,EAASqD,IAC5B3H,EAAO6G,eAAgBe,GAAmBlC,KAAKC,MAAMiC,KACrDtD,EAASqD,KAAQ3X,EAAOoV,YAAcxB,GACxCoC,EAAc7P,KAAKyR,IAGvBtD,EAAW0B,EACPN,KAAKC,MAAM3V,EAAOoV,YAAcxB,GAAc8B,KAAKC,MAAMrB,EAASA,EAASjT,OAAS,IAAM,GAC5FiT,EAASnO,KAAKnG,EAAOoV,YAAcxB,GAYvC,GATwB,IAApBU,EAASjT,SAAgBiT,EAAW,CAAC,IAEb,IAAxBtE,EAAO+E,eACL/U,EAAOsT,eACLO,EAAOO,EAAOtI,IAAI,CAAEuJ,WAAaN,EAAe,OAC7CX,EAAOtI,IAAI,CAAEyJ,YAAcR,EAAe,OAC1CX,EAAOtI,IAAI,CAAE0J,aAAeT,EAAe,QAGlD/E,EAAO6H,yBAA0B,CACnC,IAAIC,GAAgB,EAKpB,GAJAtD,EAAgBnS,SAAQ,SAAU0V,GAChCD,IAAiBC,GAAkB/H,EAAO+E,aAAe/E,EAAO+E,aAAe,MAEjF+C,IAAiB9H,EAAO+E,aACpB+C,GAAgBlE,EAAY,CAC9B,IAAIoE,IAAmBpE,EAAakE,IAAiB,EACrDxD,EAASjS,SAAQ,SAAU4V,EAAMC,GAC/B5D,EAAS4D,GAAaD,EAAOD,MAE/BzD,EAAWlS,SAAQ,SAAU4V,EAAMC,GACjC3D,EAAW2D,GAAaD,EAAOD,OAKrCzJ,EAAMkC,OAAOzQ,EAAQ,CACnBoU,OAAQA,EACRE,SAAUA,EACVC,WAAYA,EACZC,gBAAiBA,IAGfH,IAAiBF,GACnBnU,EAAOmY,KAAK,sBAEV7D,EAASjT,SAAWwT,IAClB7U,EAAOgQ,OAAOoI,eAAiBpY,EAAOqY,gBAC1CrY,EAAOmY,KAAK,yBAEV5D,EAAWlT,SAAWyT,GACxB9U,EAAOmY,KAAK,2BAGVnI,EAAOsI,qBAAuBtI,EAAOuI,wBACvCvY,EAAOwY,sBAIX,SAASC,EAAkBC,GACzB,IAGIpb,EAHA0C,EAAS3C,KACTsb,EAAe,GACfC,EAAY,EAQhB,GANqB,kBAAVF,EACT1Y,EAAO6Y,cAAcH,IACF,IAAVA,GACT1Y,EAAO6Y,cAAc7Y,EAAOgQ,OAAO0I,OAGD,SAAhC1Y,EAAOgQ,OAAO6F,eAA4B7V,EAAOgQ,OAAO6F,cAAgB,EAC1E,IAAKvY,EAAI,EAAGA,EAAIoY,KAAKE,KAAK5V,EAAOgQ,OAAO6F,eAAgBvY,GAAK,EAAG,CAC9D,IAAIiP,EAAQvM,EAAO8Y,YAAcxb,EACjC,GAAIiP,EAAQvM,EAAOoU,OAAO/S,OAAU,MACpCsX,EAAaxS,KAAKnG,EAAOoU,OAAO1H,GAAGH,GAAO,SAG5CoM,EAAaxS,KAAKnG,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAAa,IAIzD,IAAKxb,EAAI,EAAGA,EAAIqb,EAAatX,OAAQ/D,GAAK,EACxC,GAA+B,qBAApBqb,EAAarb,GAAoB,CAC1C,IAAI6V,EAASwF,EAAarb,GAAG4N,aAC7B0N,EAAYzF,EAASyF,EAAYzF,EAASyF,EAK1CA,GAAa5Y,EAAO2T,WAAW7H,IAAI,SAAW8M,EAAY,MAGhE,SAASJ,IAGP,IAFA,IAAIxY,EAAS3C,KACT+W,EAASpU,EAAOoU,OACX9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EACtC8W,EAAO9W,GAAGyb,kBAAoB/Y,EAAOsT,eAAiBc,EAAO9W,GAAG0b,WAAa5E,EAAO9W,GAAG2b,UAI3F,SAASC,EAAsBC,QACV,IAAdA,IAAuBA,EAAa9b,MAAQA,KAAK8b,WAAc,GAEpE,IAAInZ,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAEhBoE,EAASpU,EAAOoU,OAChBP,EAAM7T,EAAO8T,aAEjB,GAAsB,IAAlBM,EAAO/S,OAAX,CAC2C,qBAAhC+S,EAAO,GAAG2E,mBAAqC/Y,EAAOwY,qBAEjE,IAAIY,GAAgBD,EAChBtF,IAAOuF,EAAeD,GAG1B/E,EAAOvN,YAAYmJ,EAAOqJ,mBAE1BrZ,EAAOsZ,qBAAuB,GAC9BtZ,EAAOuZ,cAAgB,GAEvB,IAAK,IAAIjc,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAAG,CACzC,IAAI6Y,EAAQ/B,EAAO9W,GACfkc,GACDJ,GAAgBpJ,EAAOqH,eAAiBrX,EAAOyZ,eAAiB,GAAMtD,EAAM4C,oBAC1E5C,EAAMiB,gBAAkBpH,EAAO+E,cACpC,GAAI/E,EAAOuI,sBAAuB,CAChC,IAAImB,IAAgBN,EAAejD,EAAM4C,mBACrCY,EAAaD,EAAc1Z,EAAOwU,gBAAgBlX,GAClDsc,EAAaF,GAAe,GAAKA,EAAc1Z,EAAOyT,KAAO,GACnDkG,EAAa,GAAKA,GAAc3Z,EAAOyT,MACvCiG,GAAe,GAAKC,GAAc3Z,EAAOyT,KACnDmG,IACF5Z,EAAOuZ,cAAcpT,KAAKgQ,GAC1BnW,EAAOsZ,qBAAqBnT,KAAK7I,GACjC8W,EAAO1H,GAAGpP,GAAGmJ,SAASuJ,EAAOqJ,oBAGjClD,EAAM0D,SAAWhG,GAAO2F,EAAgBA,EAE1CxZ,EAAOuZ,cAAgB9T,EAAEzF,EAAOuZ,gBAGlC,SAASO,EAAgBX,QACJ,IAAdA,IAAuBA,EAAa9b,MAAQA,KAAK8b,WAAc,GAEpE,IAAInZ,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAEhB+J,EAAiB/Z,EAAOga,eAAiBha,EAAOyZ,eAChDI,EAAW7Z,EAAO6Z,SAClBI,EAAcja,EAAOia,YACrBC,EAAQla,EAAOka,MACfC,EAAeF,EACfG,EAASF,EACU,IAAnBH,GACFF,EAAW,EACXI,GAAc,EACdC,GAAQ,IAERL,GAAYV,EAAYnZ,EAAOyZ,gBAAkB,EACjDQ,EAAcJ,GAAY,EAC1BK,EAAQL,GAAY,GAEtBtL,EAAMkC,OAAOzQ,EAAQ,CACnB6Z,SAAUA,EACVI,YAAaA,EACbC,MAAOA,KAGLlK,EAAOsI,qBAAuBtI,EAAOuI,wBAAyBvY,EAAOkZ,qBAAqBC,GAE1Fc,IAAgBE,GAClBna,EAAOmY,KAAK,yBAEV+B,IAAUE,GACZpa,EAAOmY,KAAK,oBAETgC,IAAiBF,GAAiBG,IAAWF,IAChDla,EAAOmY,KAAK,YAGdnY,EAAOmY,KAAK,WAAY0B,GAG1B,SAASQ,KACP,IAWIC,EAXAta,EAAS3C,KAET+W,EAASpU,EAAOoU,OAChBpE,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WACpBmF,EAAc9Y,EAAO8Y,YACrByB,EAAYva,EAAOua,UACnBvG,EAAYhU,EAAOiU,SAAWjE,EAAOiE,QAAQC,QAEjDE,EAAOvN,YAAcmJ,EAAuB,iBAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAgC,0BAAI,IAAOA,EAA8B,wBAAI,IAAOA,EAA8B,yBAIvOsK,EADEtG,EACYhU,EAAO2T,WAAW9F,KAAM,IAAOmC,EAAiB,WAAI,6BAAgC8I,EAAc,MAElG1E,EAAO1H,GAAGoM,GAI1BwB,EAAY7T,SAASuJ,EAAOwK,kBAExBxK,EAAOyK,OAELH,EAAYvT,SAASiJ,EAAO0K,qBAC9B/G,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAiCuK,EAAY,MAC7H9T,SAASuJ,EAAO2K,2BAEnBhH,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAgCuK,EAAY,MACvH9T,SAASuJ,EAAO2K,4BAIvB,IAAIC,EAAYN,EAAYjN,QAAS,IAAO2C,EAAiB,YAAItD,GAAG,GAAGjG,SAASuJ,EAAO6K,gBACnF7K,EAAOyK,MAA6B,IAArBG,EAAUvZ,SAC3BuZ,EAAYxG,EAAO1H,GAAG,GACtBkO,EAAUnU,SAASuJ,EAAO6K,iBAG5B,IAAIC,EAAYR,EAAY7M,QAAS,IAAOuC,EAAiB,YAAItD,GAAG,GAAGjG,SAASuJ,EAAO+K,gBACnF/K,EAAOyK,MAA6B,IAArBK,EAAUzZ,SAC3ByZ,EAAY1G,EAAO1H,IAAI,GACvBoO,EAAUrU,SAASuJ,EAAO+K,iBAExB/K,EAAOyK,OAELG,EAAU7T,SAASiJ,EAAO0K,qBAC5B/G,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkC4K,EAAUzT,KAAK,2BAA8B,MAC/JV,SAASuJ,EAAOgL,yBAEnBrH,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiC4K,EAAUzT,KAAK,2BAA8B,MACzJV,SAASuJ,EAAOgL,yBAEjBF,EAAU/T,SAASiJ,EAAO0K,qBAC5B/G,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkC8K,EAAU3T,KAAK,2BAA8B,MAC/JV,SAASuJ,EAAOiL,yBAEnBtH,EACGxP,SAAU,IAAO6L,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiC8K,EAAU3T,KAAK,2BAA8B,MACzJV,SAASuJ,EAAOiL,0BAKzB,SAASC,GAAmBC,GAC1B,IASIjD,EATAlY,EAAS3C,KACT8b,EAAYnZ,EAAO8T,aAAe9T,EAAOmZ,WAAanZ,EAAOmZ,UAC7D5E,EAAavU,EAAOuU,WACpBD,EAAWtU,EAAOsU,SAClBtE,EAAShQ,EAAOgQ,OAChBoL,EAAgBpb,EAAO8Y,YACvBuC,EAAoBrb,EAAOua,UAC3Be,EAAoBtb,EAAOkY,UAC3BY,EAAcqC,EAElB,GAA2B,qBAAhBrC,EAA6B,CACtC,IAAK,IAAIxb,EAAI,EAAGA,EAAIiX,EAAWlT,OAAQ/D,GAAK,EACT,qBAAtBiX,EAAWjX,EAAI,GACpB6b,GAAa5E,EAAWjX,IAAM6b,EAAY5E,EAAWjX,EAAI,IAAOiX,EAAWjX,EAAI,GAAKiX,EAAWjX,IAAM,EACvGwb,EAAcxb,EACL6b,GAAa5E,EAAWjX,IAAM6b,EAAY5E,EAAWjX,EAAI,KAClEwb,EAAcxb,EAAI,GAEX6b,GAAa5E,EAAWjX,KACjCwb,EAAcxb,GAId0S,EAAOuL,sBACLzC,EAAc,GAA4B,qBAAhBA,KAA+BA,EAAc,GAS/E,GALEZ,EADE5D,EAAStO,QAAQmT,IAAc,EACrB7E,EAAStO,QAAQmT,GAEjBzD,KAAKC,MAAMmD,EAAc9I,EAAOuG,gBAE1C2B,GAAa5D,EAASjT,SAAU6W,EAAY5D,EAASjT,OAAS,GAC9DyX,IAAgBsC,EAApB,CASA,IAAIb,EAAY/G,SAASxT,EAAOoU,OAAO1H,GAAGoM,GAAa3R,KAAK,4BAA8B2R,EAAa,IAEvGvK,EAAMkC,OAAOzQ,EAAQ,CACnBkY,UAAWA,EACXqC,UAAWA,EACXa,cAAeA,EACftC,YAAaA,IAEf9Y,EAAOmY,KAAK,qBACZnY,EAAOmY,KAAK,mBACRkD,IAAsBd,GACxBva,EAAOmY,KAAK,oBAEVnY,EAAOwb,aAAexb,EAAOyb,qBAC/Bzb,EAAOmY,KAAK,oBAtBRD,IAAcoD,IAChBtb,EAAOkY,UAAYA,EACnBlY,EAAOmY,KAAK,oBAwBlB,SAASuD,GAAoBze,GAC3B,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChBmG,EAAQ1Q,EAAExI,EAAE2L,QAAQgF,QAAS,IAAOoC,EAAiB,YAAI,GACzD2L,GAAa,EACjB,GAAIxF,EACF,IAAK,IAAI7Y,EAAI,EAAGA,EAAI0C,EAAOoU,OAAO/S,OAAQ/D,GAAK,EACzC0C,EAAOoU,OAAO9W,KAAO6Y,IAASwF,GAAa,GAInD,IAAIxF,IAASwF,EAUX,OAFA3b,EAAO4b,kBAAexS,OACtBpJ,EAAO6b,kBAAezS,GARtBpJ,EAAO4b,aAAezF,EAClBnW,EAAOiU,SAAWjU,EAAOgQ,OAAOiE,QAAQC,QAC1ClU,EAAO6b,aAAerI,SAAS/N,EAAE0Q,GAAOhP,KAAK,2BAA4B,IAEzEnH,EAAO6b,aAAepW,EAAE0Q,GAAO5J,QAO/ByD,EAAO8L,0BAA+C1S,IAAxBpJ,EAAO6b,cAA8B7b,EAAO6b,eAAiB7b,EAAO8Y,aACpG9Y,EAAO8b,sBArwBXlJ,EAAYtU,UAAUgE,GAAK,SAAa+G,EAAQQ,EAASkS,GACvD,IAAIvW,EAAOnI,KACX,GAAuB,oBAAZwM,EAA0B,OAAOrE,EAC5C,IAAIwW,EAASD,EAAW,UAAY,OAKpC,OAJA1S,EAAOhD,MAAM,KAAKhE,SAAQ,SAAUoH,GAC7BjE,EAAKqN,gBAAgBpJ,KAAUjE,EAAKqN,gBAAgBpJ,GAAS,IAClEjE,EAAKqN,gBAAgBpJ,GAAOuS,GAAQnS,MAE/BrE,GAGToN,EAAYtU,UAAU2d,KAAO,SAAe5S,EAAQQ,EAASkS,GAC3D,IAAIvW,EAAOnI,KACX,GAAuB,oBAAZwM,EAA0B,OAAOrE,EAC5C,SAAS0W,IACL,IAAI7T,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAE3CuB,EAAQtH,MAAMiD,EAAM6C,GACpB7C,EAAKmE,IAAIN,EAAQ6S,GACbA,EAAYC,gBACPD,EAAYC,QAIvB,OADAD,EAAYC,QAAUtS,EACfrE,EAAKlD,GAAG+G,EAAQ6S,EAAaH,IAGtCnJ,EAAYtU,UAAUqL,IAAM,SAAcN,EAAQQ,GAChD,IAAIrE,EAAOnI,KACX,OAAKmI,EAAKqN,iBACVxJ,EAAOhD,MAAM,KAAKhE,SAAQ,SAAUoH,GACX,qBAAZI,EACTrE,EAAKqN,gBAAgBpJ,GAAS,GACrBjE,EAAKqN,gBAAgBpJ,IAAUjE,EAAKqN,gBAAgBpJ,GAAOpI,QACpEmE,EAAKqN,gBAAgBpJ,GAAOpH,SAAQ,SAAU+Z,EAAc7P,IACtD6P,IAAiBvS,GAAYuS,EAAaD,SAAWC,EAAaD,UAAYtS,IAChFrE,EAAKqN,gBAAgBpJ,GAAOK,OAAOyC,EAAO,SAK3C/G,GAZ6BA,GAetCoN,EAAYtU,UAAU6Z,KAAO,WACzB,IAAI9P,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAE3C,IAEIe,EACA5I,EACAkF,EAJAH,EAAOnI,KACX,IAAKmI,EAAKqN,gBAAmB,OAAOrN,EAIb,kBAAZ6C,EAAK,IAAmB7F,MAAM6Z,QAAQhU,EAAK,KACpDgB,EAAShB,EAAK,GACd5H,EAAO4H,EAAK5F,MAAM,EAAG4F,EAAKhH,QAC1BsE,EAAUH,IAEV6D,EAAShB,EAAK,GAAGgB,OACjB5I,EAAO4H,EAAK,GAAG5H,KACfkF,EAAU0C,EAAK,GAAG1C,SAAWH,GAE/B,IAAI8W,EAAc9Z,MAAM6Z,QAAQhT,GAAUA,EAASA,EAAOhD,MAAM,KAYhE,OAXAiW,EAAYja,SAAQ,SAAUoH,GAC5B,GAAIjE,EAAKqN,iBAAmBrN,EAAKqN,gBAAgBpJ,GAAQ,CACvD,IAAIG,EAAW,GACfpE,EAAKqN,gBAAgBpJ,GAAOpH,SAAQ,SAAU+Z,GAC5CxS,EAASzD,KAAKiW,MAEhBxS,EAASvH,SAAQ,SAAU+Z,GACzBA,EAAa7Z,MAAMoD,EAASlF,UAI3B+E,GAGToN,EAAYtU,UAAUie,iBAAmB,SAA2BC,GAClE,IAAIC,EAAWpf,KACVof,EAASC,SACd3e,OAAOsQ,KAAKoO,EAASC,SAASra,SAAQ,SAAUsa,GAC9C,IAAIxf,EAASsf,EAASC,QAAQC,GAE1Bxf,EAAO6S,QACTzB,EAAMkC,OAAO+L,EAAgBrf,EAAO6S,YAK1C4C,EAAYtU,UAAUse,WAAa,SAAqBC,QAC7B,IAAlBA,IAA2BA,EAAgB,IAElD,IAAIJ,EAAWpf,KACVof,EAASC,SACd3e,OAAOsQ,KAAKoO,EAASC,SAASra,SAAQ,SAAUsa,GAC9C,IAAIxf,EAASsf,EAASC,QAAQC,GAC1BG,EAAeD,EAAcF,IAAe,GAE5Cxf,EAAOsf,UACT1e,OAAOsQ,KAAKlR,EAAOsf,UAAUpa,SAAQ,SAAU0a,GAC7C,IAAIC,EAAa7f,EAAOsf,SAASM,GAE/BN,EAASM,GADe,oBAAfC,EACkBA,EAAWC,KAAKR,GAEhBO,KAK7B7f,EAAOmF,IAAMma,EAASna,IACxBvE,OAAOsQ,KAAKlR,EAAOmF,IAAID,SAAQ,SAAU6a,GACvCT,EAASna,GAAG4a,EAAiB/f,EAAOmF,GAAG4a,OAKvC/f,EAAOggB,QACThgB,EAAOggB,OAAOF,KAAKR,EAAnBtf,CAA6B2f,OAKnC/J,EAAgBC,WAAWoK,IAAM,SAAUpK,GACzC,IAAI7E,EAAQ9Q,KACP8Q,EAAMkP,KACXlP,EAAMkP,IAAIrK,IAGZJ,EAAY0K,cAAgB,SAAwBngB,GAChD,IAAI6S,EAAS,GAAI1H,EAAMlH,UAAUC,OAAS,EAC1C,MAAQiH,KAAQ,EAAI0H,EAAQ1H,GAAQlH,UAAWkH,EAAM,GAEvD,IAAI6F,EAAQ9Q,KACP8Q,EAAM7P,UAAUoe,UAAWvO,EAAM7P,UAAUoe,QAAU,IAC1D,IAAInc,EAAOpD,EAAOoD,MAAWxC,OAAOsQ,KAAKF,EAAM7P,UAAUoe,SAAe,OAAI,IAAOnO,EAAMM,MAkBzF,OAjBAV,EAAM7P,UAAUoe,QAAQnc,GAAQpD,EAE5BA,EAAOogB,OACTxf,OAAOsQ,KAAKlR,EAAOogB,OAAOlb,SAAQ,SAAUqF,GAC1CyG,EAAM7P,UAAUoJ,GAAOvK,EAAOogB,MAAM7V,MAIpCvK,EAAOqgB,QACTzf,OAAOsQ,KAAKlR,EAAOqgB,QAAQnb,SAAQ,SAAUqF,GAC3CyG,EAAMzG,GAAOvK,EAAOqgB,OAAO9V,MAI3BvK,EAAO2C,SACT3C,EAAO2C,QAAQyC,MAAM4L,EAAO6B,GAEvB7B,GAGTyE,EAAYyK,IAAM,SAAclgB,GAC5B,IAAI6S,EAAS,GAAI1H,EAAMlH,UAAUC,OAAS,EAC1C,MAAQiH,KAAQ,EAAI0H,EAAQ1H,GAAQlH,UAAWkH,EAAM,GAEvD,IAAI6F,EAAQ9Q,KACZ,OAAImF,MAAM6Z,QAAQlf,IAChBA,EAAOkF,SAAQ,SAAU1E,GAAK,OAAOwQ,EAAMmP,cAAc3f,MAClDwQ,GAEFA,EAAMmP,cAAc/a,MAAM4L,EAAO,CAAEhR,GAASwC,OAAQqQ,KAG7DjS,OAAO0f,iBAAkB7K,EAAaG,GAimBtC,IAAInS,GAAS,CACXqS,WAAYA,EACZS,aAAcA,EACd+E,iBAAkBA,EAClBD,mBAAoBA,EACpBU,qBAAsBA,EACtBY,eAAgBA,EAChBO,oBAAqBA,GACrBa,kBAAmBA,GACnBQ,mBAAoBA,IAGtB,SAAS5M,GAAcC,QACP,IAATA,IAAkBA,EAAO1R,KAAKiW,eAAiB,IAAM,KAE1D,IAAItT,EAAS3C,KAET2S,EAAShQ,EAAOgQ,OAChB6D,EAAM7T,EAAO8T,aACbqF,EAAYnZ,EAAOmZ,UACnBxF,EAAa3T,EAAO2T,WAExB,GAAI3D,EAAO0N,iBACT,OAAO7J,GAAOsF,EAAYA,EAG5B,IAAIwE,EAAmBpP,EAAMO,aAAa6E,EAAW,GAAI5E,GAGzD,OAFI8E,IAAO8J,GAAoBA,GAExBA,GAAoB,EAG7B,SAASC,GAAczE,EAAW0E,GAChC,IA4BIC,EA5BA9d,EAAS3C,KACTwW,EAAM7T,EAAO8T,aACb9D,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WACpBkG,EAAW7Z,EAAO6Z,SAClBkE,EAAI,EACJC,EAAI,EACJC,EAAI,EAEJje,EAAOsT,eACTyK,EAAIlK,GAAOsF,EAAYA,EAEvB6E,EAAI7E,EAGFnJ,EAAO6G,eACTkH,EAAIrI,KAAKC,MAAMoI,GACfC,EAAItI,KAAKC,MAAMqI,IAGZhO,EAAO0N,mBACNxM,EAAQW,aAAgB8B,EAAW7L,UAAW,eAAiBiW,EAAI,OAASC,EAAI,OAASC,EAAI,OAC1FtK,EAAW7L,UAAW,aAAeiW,EAAI,OAASC,EAAI,QAE/Dhe,EAAOke,kBAAoBle,EAAOmZ,UAClCnZ,EAAOmZ,UAAYnZ,EAAOsT,eAAiByK,EAAIC,EAI/C,IAAIjE,EAAiB/Z,EAAOga,eAAiBha,EAAOyZ,eAElDqE,EADqB,IAAnB/D,EACY,GAECZ,EAAYnZ,EAAOyZ,gBAAkB,EAElDqE,IAAgBjE,GAClB7Z,EAAO8Z,eAAeX,GAGxBnZ,EAAOmY,KAAK,eAAgBnY,EAAOmZ,UAAW0E,GAGhD,SAASpE,KACP,OAASpc,KAAKiX,SAAS,GAGzB,SAAS0F,KACP,OAAS3c,KAAKiX,SAASjX,KAAKiX,SAASjT,OAAS,GAGhD,IAAI8X,GAAY,CACdrK,aAAcA,GACd8O,aAAcA,GACdnE,aAAcA,GACdO,aAAcA,IAGhB,SAASnB,GAAe3Q,EAAU2V,GAChC,IAAI7d,EAAS3C,KAEb2C,EAAO2T,WAAW1L,WAAWC,GAE7BlI,EAAOmY,KAAK,gBAAiBjQ,EAAU2V,GAGzC,SAASM,GAAiBC,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACTyb,EAAc9Y,EAAO8Y,YACrB9I,EAAShQ,EAAOgQ,OAChBoL,EAAgBpb,EAAOob,cACvBpL,EAAOsO,YACTte,EAAOyY,mBAGT,IAAI8F,EAAMF,EASV,GARKE,IACgCA,EAA/BzF,EAAcsC,EAAuB,OAChCtC,EAAcsC,EAAuB,OACjC,SAGfpb,EAAOmY,KAAK,mBAERiG,GAAgBtF,IAAgBsC,EAAe,CACjD,GAAY,UAARmD,EAEF,YADAve,EAAOmY,KAAK,6BAGdnY,EAAOmY,KAAK,8BACA,SAARoG,EACFve,EAAOmY,KAAK,4BAEZnY,EAAOmY,KAAK,6BAKlB,SAASqG,GAAiBJ,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACTyb,EAAc9Y,EAAO8Y,YACrBsC,EAAgBpb,EAAOob,cAC3Bpb,EAAOye,WAAY,EACnBze,EAAO6Y,cAAc,GAErB,IAAI0F,EAAMF,EASV,GARKE,IACgCA,EAA/BzF,EAAcsC,EAAuB,OAChCtC,EAAcsC,EAAuB,OACjC,SAGfpb,EAAOmY,KAAK,iBAERiG,GAAgBtF,IAAgBsC,EAAe,CACjD,GAAY,UAARmD,EAEF,YADAve,EAAOmY,KAAK,2BAGdnY,EAAOmY,KAAK,4BACA,SAARoG,EACFve,EAAOmY,KAAK,0BAEZnY,EAAOmY,KAAK,2BAKlB,IAAIuG,GAAe,CACjB7F,cAAeA,GACfsF,gBAAiBA,GACjB3T,cAAegU,IAGjB,SAASG,GAASpS,EAAOmM,EAAO0F,EAAcQ,QAC7B,IAAVrS,IAAmBA,EAAQ,QACjB,IAAVmM,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACTwhB,EAAatS,EACbsS,EAAa,IAAKA,EAAa,GAEnC,IAAI7O,EAAShQ,EAAOgQ,OAChBsE,EAAWtU,EAAOsU,SAClBC,EAAavU,EAAOuU,WACpB6G,EAAgBpb,EAAOob,cACvBtC,EAAc9Y,EAAO8Y,YACrBjF,EAAM7T,EAAO8T,aACjB,GAAI9T,EAAOye,WAAazO,EAAO8O,+BAC7B,OAAO,EAGT,IAAI5G,EAAYxC,KAAKC,MAAMkJ,EAAa7O,EAAOuG,gBAC3C2B,GAAa5D,EAASjT,SAAU6W,EAAY5D,EAASjT,OAAS,IAE7DyX,GAAe9I,EAAO+O,cAAgB,MAAQ3D,GAAiB,IAAMgD,GACxEpe,EAAOmY,KAAK,0BAGd,IAuBIkG,EAvBAlF,GAAa7E,EAAS4D,GAM1B,GAHAlY,EAAO8Z,eAAeX,GAGlBnJ,EAAOuL,oBACT,IAAK,IAAIje,EAAI,EAAGA,EAAIiX,EAAWlT,OAAQ/D,GAAK,GACrCoY,KAAKC,MAAkB,IAAZwD,IAAoBzD,KAAKC,MAAsB,IAAhBpB,EAAWjX,MACxDuhB,EAAavhB,GAKnB,GAAI0C,EAAOwb,aAAeqD,IAAe/F,EAAa,CACpD,IAAK9Y,EAAOgf,gBAAkB7F,EAAYnZ,EAAOmZ,WAAaA,EAAYnZ,EAAOyZ,eAC/E,OAAO,EAET,IAAKzZ,EAAOif,gBAAkB9F,EAAYnZ,EAAOmZ,WAAaA,EAAYnZ,EAAOga,iBAC1ElB,GAAe,KAAO+F,EAAc,OAAO,EAWpD,OANgCR,EAA5BQ,EAAa/F,EAA2B,OACnC+F,EAAa/F,EAA2B,OAC9B,QAIdjF,IAAQsF,IAAcnZ,EAAOmZ,YAAgBtF,GAAOsF,IAAcnZ,EAAOmZ,WAC5EnZ,EAAOkb,kBAAkB2D,GAErB7O,EAAOsO,YACTte,EAAOyY,mBAETzY,EAAOqa,sBACe,UAAlBrK,EAAOuH,QACTvX,EAAO4d,aAAazE,GAEJ,UAAdkF,IACFre,EAAOme,gBAAgBC,EAAcC,GACrCre,EAAOwK,cAAc4T,EAAcC,KAE9B,IAGK,IAAV3F,GAAgBxH,EAAQjJ,YAS1BjI,EAAO6Y,cAAcH,GACrB1Y,EAAO4d,aAAazE,GACpBnZ,EAAOkb,kBAAkB2D,GACzB7e,EAAOqa,sBACPra,EAAOmY,KAAK,wBAAyBO,EAAOkG,GAC5C5e,EAAOme,gBAAgBC,EAAcC,GAChCre,EAAOye,YACVze,EAAOye,WAAY,EACdze,EAAOkf,gCACVlf,EAAOkf,8BAAgC,SAAuBjiB,GACvD+C,IAAUA,EAAOmf,WAClBliB,EAAE2L,SAAWvL,OACjB2C,EAAO2T,WAAW,GAAGlQ,oBAAoB,gBAAiBzD,EAAOkf,+BACjElf,EAAO2T,WAAW,GAAGlQ,oBAAoB,sBAAuBzD,EAAOkf,+BACvElf,EAAOkf,8BAAgC,YAChClf,EAAOkf,8BACdlf,EAAOwK,cAAc4T,EAAcC,MAGvCre,EAAO2T,WAAW,GAAGnQ,iBAAiB,gBAAiBxD,EAAOkf,+BAC9Dlf,EAAO2T,WAAW,GAAGnQ,iBAAiB,sBAAuBxD,EAAOkf,kCA5BtElf,EAAO6Y,cAAc,GACrB7Y,EAAO4d,aAAazE,GACpBnZ,EAAOkb,kBAAkB2D,GACzB7e,EAAOqa,sBACPra,EAAOmY,KAAK,wBAAyBO,EAAOkG,GAC5C5e,EAAOme,gBAAgBC,EAAcC,GACrCre,EAAOwK,cAAc4T,EAAcC,KA0B9B,GAGT,SAASe,GAAa7S,EAAOmM,EAAO0F,EAAcQ,QACjC,IAAVrS,IAAmBA,EAAQ,QACjB,IAAVmM,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACTgiB,EAAW9S,EAKf,OAJIvM,EAAOgQ,OAAOyK,OAChB4E,GAAYrf,EAAOsf,cAGdtf,EAAO2e,QAAQU,EAAU3G,EAAO0F,EAAcQ,GAIvD,SAASW,GAAW7G,EAAO0F,EAAcQ,QACxB,IAAVlG,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChByO,EAAYze,EAAOye,UACvB,OAAIzO,EAAOyK,MACLgE,IACJze,EAAOwf,UAEPxf,EAAOyf,YAAczf,EAAO2T,WAAW,GAAGpI,WACnCvL,EAAO2e,QAAQ3e,EAAO8Y,YAAc9I,EAAOuG,eAAgBmC,EAAO0F,EAAcQ,IAElF5e,EAAO2e,QAAQ3e,EAAO8Y,YAAc9I,EAAOuG,eAAgBmC,EAAO0F,EAAcQ,GAIzF,SAASc,GAAWhH,EAAO0F,EAAcQ,QACxB,IAAVlG,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChByO,EAAYze,EAAOye,UACnBnK,EAAWtU,EAAOsU,SAClBC,EAAavU,EAAOuU,WACpBT,EAAe9T,EAAO8T,aAE1B,GAAI9D,EAAOyK,KAAM,CACf,GAAIgE,EAAa,OAAO,EACxBze,EAAOwf,UAEPxf,EAAOyf,YAAczf,EAAO2T,WAAW,GAAGpI,WAE5C,IAAI4N,EAAYrF,EAAe9T,EAAOmZ,WAAanZ,EAAOmZ,UAC1D,SAASwG,EAAUC,GACjB,OAAIA,EAAM,GAAalK,KAAKC,MAAMD,KAAK4B,IAAIsI,IACpClK,KAAKC,MAAMiK,GAEpB,IAMIC,EANAC,EAAsBH,EAAUxG,GAChC4G,EAAqBzL,EAASjF,KAAI,SAAUuQ,GAAO,OAAOD,EAAUC,MAIpEI,GAHuBzL,EAAWlF,KAAI,SAAUuQ,GAAO,OAAOD,EAAUC,MAE1DtL,EAASyL,EAAmB/Z,QAAQ8Z,IACvCxL,EAASyL,EAAmB/Z,QAAQ8Z,GAAuB,IAM1E,MAJwB,qBAAbE,IACTH,EAAYtL,EAAWvO,QAAQga,GAC3BH,EAAY,IAAKA,EAAY7f,EAAO8Y,YAAc,IAEjD9Y,EAAO2e,QAAQkB,EAAWnH,EAAO0F,EAAcQ,GAIxD,SAASqB,GAAYvH,EAAO0F,EAAcQ,QACzB,IAAVlG,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACb,OAAO2C,EAAO2e,QAAQ3e,EAAO8Y,YAAaJ,EAAO0F,EAAcQ,GAIjE,SAASsB,GAAgBxH,EAAO0F,EAAcQ,QAC7B,IAAVlG,IAAmBA,EAAQrb,KAAK2S,OAAO0I,YACtB,IAAjB0F,IAA0BA,GAAe,GAE9C,IAAIpe,EAAS3C,KACTkP,EAAQvM,EAAO8Y,YACfZ,EAAYxC,KAAKC,MAAMpJ,EAAQvM,EAAOgQ,OAAOuG,gBAEjD,GAAI2B,EAAYlY,EAAOsU,SAASjT,OAAS,EAAG,CAC1C,IAAI8X,EAAYnZ,EAAO8T,aAAe9T,EAAOmZ,WAAanZ,EAAOmZ,UAE7DgH,EAAcngB,EAAOsU,SAAS4D,GAC9BkI,EAAWpgB,EAAOsU,SAAS4D,EAAY,GAEtCiB,EAAYgH,GAAgBC,EAAWD,GAAe,IACzD5T,EAAQvM,EAAOgQ,OAAOuG,gBAI1B,OAAOvW,EAAO2e,QAAQpS,EAAOmM,EAAO0F,EAAcQ,GAGpD,SAAS9C,KACP,IAMIvB,EANAva,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WAEpBkC,EAAyC,SAAzB7F,EAAO6F,cAA2B7V,EAAOqgB,uBAAyBrQ,EAAO6F,cACzFyK,EAAetgB,EAAO6b,aAE1B,GAAI7L,EAAOyK,KAAM,CACf,GAAIza,EAAOye,UAAa,OACxBlE,EAAY/G,SAAS/N,EAAEzF,EAAO4b,cAAczU,KAAK,2BAA4B,IACzE6I,EAAOqH,eAENiJ,EAAetgB,EAAOsf,aAAgBzJ,EAAgB,GACnDyK,EAAgBtgB,EAAOoU,OAAO/S,OAASrB,EAAOsf,aAAiBzJ,EAAgB,GAEnF7V,EAAOwf,UACPc,EAAe3M,EACZxP,SAAU,IAAO6L,EAAiB,WAAI,6BAAgCuK,EAAY,WAAevK,EAA0B,oBAAI,KAC/HtD,GAAG,GACHH,QAEHgC,EAAMI,UAAS,WACb3O,EAAO2e,QAAQ2B,OAGjBtgB,EAAO2e,QAAQ2B,GAERA,EAAetgB,EAAOoU,OAAO/S,OAASwU,GAC/C7V,EAAOwf,UACPc,EAAe3M,EACZxP,SAAU,IAAO6L,EAAiB,WAAI,6BAAgCuK,EAAY,WAAevK,EAA0B,oBAAI,KAC/HtD,GAAG,GACHH,QAEHgC,EAAMI,UAAS,WACb3O,EAAO2e,QAAQ2B,OAGjBtgB,EAAO2e,QAAQ2B,QAGjBtgB,EAAO2e,QAAQ2B,GAInB,IAAInK,GAAQ,CACVwI,QAASA,GACTS,YAAaA,GACbG,UAAWA,GACXG,UAAWA,GACXO,WAAYA,GACZC,eAAgBA,GAChBpE,oBAAqBA,IAGvB,SAASyE,KACP,IAAIvgB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WAExBA,EAAWxP,SAAU,IAAO6L,EAAiB,WAAI,IAAOA,EAA0B,qBAAIlJ,SAEtF,IAAIsN,EAAST,EAAWxP,SAAU,IAAO6L,EAAiB,YAE1D,GAAIA,EAAOwQ,uBAAwB,CACjC,IAAIC,EAAiBzQ,EAAOuG,eAAkBnC,EAAO/S,OAAS2O,EAAOuG,eACrE,GAAIkK,IAAmBzQ,EAAOuG,eAAgB,CAC5C,IAAK,IAAIjZ,EAAI,EAAGA,EAAImjB,EAAgBnjB,GAAK,EAAG,CAC1C,IAAIojB,EAAYjb,EAAEpC,EAAIa,cAAc,QAAQuC,SAAWuJ,EAAiB,WAAI,IAAOA,EAAsB,iBACzG2D,EAAW/G,OAAO8T,GAEpBtM,EAAST,EAAWxP,SAAU,IAAO6L,EAAiB,aAI7B,SAAzBA,EAAO6F,eAA6B7F,EAAOsP,eAAgBtP,EAAOsP,aAAelL,EAAO/S,QAE5FrB,EAAOsf,aAAe9L,SAASxD,EAAOsP,cAAgBtP,EAAO6F,cAAe,IAC5E7V,EAAOsf,cAAgBtP,EAAO2Q,qBAC1B3gB,EAAOsf,aAAelL,EAAO/S,SAC/BrB,EAAOsf,aAAelL,EAAO/S,QAG/B,IAAIuf,EAAgB,GAChBC,EAAe,GACnBzM,EAAOpI,MAAK,SAAUO,EAAO5E,GAC3B,IAAIwO,EAAQ1Q,EAAEkC,GACV4E,EAAQvM,EAAOsf,cAAgBuB,EAAa1a,KAAKwB,GACjD4E,EAAQ6H,EAAO/S,QAAUkL,GAAS6H,EAAO/S,OAASrB,EAAOsf,cAAgBsB,EAAcza,KAAKwB,GAChGwO,EAAMhP,KAAK,0BAA2BoF,MAExC,IAAK,IAAIkL,EAAM,EAAGA,EAAMoJ,EAAaxf,OAAQoW,GAAO,EAClD9D,EAAW/G,OAAOnH,EAAEob,EAAapJ,GAAKqJ,WAAU,IAAOra,SAASuJ,EAAO0K,sBAEzE,IAAK,IAAI/C,EAAMiJ,EAAcvf,OAAS,EAAGsW,GAAO,EAAGA,GAAO,EACxDhE,EAAW1G,QAAQxH,EAAEmb,EAAcjJ,GAAKmJ,WAAU,IAAOra,SAASuJ,EAAO0K,sBAI7E,SAAS8E,KACP,IASIH,EATArf,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB8I,EAAc9Y,EAAO8Y,YACrB1E,EAASpU,EAAOoU,OAChBkL,EAAetf,EAAOsf,aACtBL,EAAiBjf,EAAOif,eACxBD,EAAiBhf,EAAOgf,eACxB1K,EAAWtU,EAAOsU,SAClBT,EAAM7T,EAAO8T,aAEjB9T,EAAOif,gBAAiB,EACxBjf,EAAOgf,gBAAiB,EAExB,IAAI+B,GAAiBzM,EAASwE,GAC1BkI,EAAOD,EAAgB/gB,EAAO8O,eAIlC,GAAIgK,EAAcwG,EAAc,CAC9BD,EAAYjL,EAAO/S,OAAyB,EAAfie,EAAqBxG,EAClDuG,GAAYC,EACZ,IAAI2B,EAAejhB,EAAO2e,QAAQU,EAAU,GAAG,GAAO,GAClD4B,GAAyB,IAATD,GAClBhhB,EAAO4d,cAAc/J,GAAO7T,EAAOmZ,UAAYnZ,EAAOmZ,WAAa6H,QAEhE,GAA8B,SAAzBhR,EAAO6F,eAA4BiD,GAA8B,EAAfwG,GAAsBxG,GAAe1E,EAAO/S,OAASie,EAAe,CAEhID,GAAYjL,EAAO/S,OAASyX,EAAcwG,EAC1CD,GAAYC,EACZ,IAAI4B,EAAiBlhB,EAAO2e,QAAQU,EAAU,GAAG,GAAO,GACpD6B,GAA2B,IAATF,GACpBhhB,EAAO4d,cAAc/J,GAAO7T,EAAOmZ,UAAYnZ,EAAOmZ,WAAa6H,GAGvEhhB,EAAOif,eAAiBA,EACxBjf,EAAOgf,eAAiBA,EAG1B,SAASmC,KACP,IAAInhB,EAAS3C,KACTsW,EAAa3T,EAAO2T,WACpB3D,EAAShQ,EAAOgQ,OAChBoE,EAASpU,EAAOoU,OACpBT,EAAWxP,SAAU,IAAO6L,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,KAAQA,EAAiB,WAAI,IAAOA,EAAsB,iBAAIlJ,SACpJsN,EAAO5M,WAAW,2BAGpB,IAAIiT,GAAO,CACT8F,WAAYA,GACZf,QAASA,GACT2B,YAAaA,IAGf,SAASC,GAAeC,GACtB,IAAIrhB,EAAS3C,KACb,KAAI6T,EAAQE,QAAUpR,EAAOgQ,OAAOsR,eAAkBthB,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,UAA5F,CACA,IAAI5Z,EAAK3H,EAAO2H,GAChBA,EAAGtD,MAAMmd,OAAS,OAClB7Z,EAAGtD,MAAMmd,OAASH,EAAS,mBAAqB,eAChD1Z,EAAGtD,MAAMmd,OAASH,EAAS,eAAiB,YAC5C1Z,EAAGtD,MAAMmd,OAASH,EAAS,WAAa,QAG1C,SAASI,KACP,IAAIzhB,EAAS3C,KACT6T,EAAQE,OAAUpR,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,WAC5DvhB,EAAO2H,GAAGtD,MAAMmd,OAAS,IAG3B,IAAIE,GAAa,CACfN,cAAeA,GACfK,gBAAiBA,IAGnB,SAASE,GAAavN,GACpB,IAAIpU,EAAS3C,KACTsW,EAAa3T,EAAO2T,WACpB3D,EAAShQ,EAAOgQ,OAIpB,GAHIA,EAAOyK,MACTza,EAAOmhB,cAEa,kBAAX/M,GAAuB,WAAYA,EAC5C,IAAK,IAAI9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAClC8W,EAAO9W,IAAMqW,EAAW/G,OAAOwH,EAAO9W,SAG5CqW,EAAW/G,OAAOwH,GAEhBpE,EAAOyK,MACTza,EAAOugB,aAEHvQ,EAAOgC,UAAYd,EAAQc,UAC/BhS,EAAOY,SAIX,SAASghB,GAAcxN,GACrB,IAAIpU,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WACpBmF,EAAc9Y,EAAO8Y,YAErB9I,EAAOyK,MACTza,EAAOmhB,cAET,IAAIhG,EAAiBrC,EAAc,EACnC,GAAsB,kBAAX1E,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAI9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAClC8W,EAAO9W,IAAMqW,EAAW1G,QAAQmH,EAAO9W,IAE7C6d,EAAiBrC,EAAc1E,EAAO/S,YAEtCsS,EAAW1G,QAAQmH,GAEjBpE,EAAOyK,MACTza,EAAOugB,aAEHvQ,EAAOgC,UAAYd,EAAQc,UAC/BhS,EAAOY,SAETZ,EAAO2e,QAAQxD,EAAgB,GAAG,GAGpC,SAAS0G,GAAUtV,EAAO6H,GACxB,IAAIpU,EAAS3C,KACTsW,EAAa3T,EAAO2T,WACpB3D,EAAShQ,EAAOgQ,OAChB8I,EAAc9Y,EAAO8Y,YACrBgJ,EAAoBhJ,EACpB9I,EAAOyK,OACTqH,GAAqB9hB,EAAOsf,aAC5Btf,EAAOmhB,cACPnhB,EAAOoU,OAAST,EAAWxP,SAAU,IAAO6L,EAAiB,aAE/D,IAAI+R,EAAa/hB,EAAOoU,OAAO/S,OAC/B,GAAIkL,GAAS,EACXvM,EAAO4hB,aAAaxN,QAGtB,GAAI7H,GAASwV,EACX/hB,EAAO2hB,YAAYvN,OADrB,CAOA,IAHA,IAAI+G,EAAiB2G,EAAoBvV,EAAQuV,EAAoB,EAAIA,EAErEE,EAAe,GACV1kB,EAAIykB,EAAa,EAAGzkB,GAAKiP,EAAOjP,GAAK,EAAG,CAC/C,IAAI2kB,EAAejiB,EAAOoU,OAAO1H,GAAGpP,GACpC2kB,EAAanb,SACbkb,EAAajZ,QAAQkZ,GAGvB,GAAsB,kBAAX7N,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIqD,EAAM,EAAGA,EAAMrD,EAAO/S,OAAQoW,GAAO,EACxCrD,EAAOqD,IAAQ9D,EAAW/G,OAAOwH,EAAOqD,IAE9C0D,EAAiB2G,EAAoBvV,EAAQuV,EAAoB1N,EAAO/S,OAASygB,OAEjFnO,EAAW/G,OAAOwH,GAGpB,IAAK,IAAIuD,EAAM,EAAGA,EAAMqK,EAAa3gB,OAAQsW,GAAO,EAClDhE,EAAW/G,OAAOoV,EAAarK,IAG7B3H,EAAOyK,MACTza,EAAOugB,aAEHvQ,EAAOgC,UAAYd,EAAQc,UAC/BhS,EAAOY,SAELoP,EAAOyK,KACTza,EAAO2e,QAAQxD,EAAiBnb,EAAOsf,aAAc,GAAG,GAExDtf,EAAO2e,QAAQxD,EAAgB,GAAG,IAItC,SAAS+G,GAAaC,GACpB,IAAIniB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB2D,EAAa3T,EAAO2T,WACpBmF,EAAc9Y,EAAO8Y,YAErBgJ,EAAoBhJ,EACpB9I,EAAOyK,OACTqH,GAAqB9hB,EAAOsf,aAC5Btf,EAAOmhB,cACPnhB,EAAOoU,OAAST,EAAWxP,SAAU,IAAO6L,EAAiB,aAE/D,IACIoS,EADAjH,EAAiB2G,EAGrB,GAA6B,kBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAI7kB,EAAI,EAAGA,EAAI6kB,EAAc9gB,OAAQ/D,GAAK,EAC7C8kB,EAAgBD,EAAc7kB,GAC1B0C,EAAOoU,OAAOgO,IAAkBpiB,EAAOoU,OAAO1H,GAAG0V,GAAetb,SAChEsb,EAAgBjH,IAAkBA,GAAkB,GAE1DA,EAAiBzF,KAAKK,IAAIoF,EAAgB,QAE1CiH,EAAgBD,EACZniB,EAAOoU,OAAOgO,IAAkBpiB,EAAOoU,OAAO1H,GAAG0V,GAAetb,SAChEsb,EAAgBjH,IAAkBA,GAAkB,GACxDA,EAAiBzF,KAAKK,IAAIoF,EAAgB,GAGxCnL,EAAOyK,MACTza,EAAOugB,aAGHvQ,EAAOgC,UAAYd,EAAQc,UAC/BhS,EAAOY,SAELoP,EAAOyK,KACTza,EAAO2e,QAAQxD,EAAiBnb,EAAOsf,aAAc,GAAG,GAExDtf,EAAO2e,QAAQxD,EAAgB,GAAG,GAItC,SAASkH,KAIP,IAHA,IAAIriB,EAAS3C,KAET8kB,EAAgB,GACX7kB,EAAI,EAAGA,EAAI0C,EAAOoU,OAAO/S,OAAQ/D,GAAK,EAC7C6kB,EAAchc,KAAK7I,GAErB0C,EAAOkiB,YAAYC,GAGrB,IAAIG,GAAe,CACjBX,YAAaA,GACbC,aAAcA,GACdC,SAAUA,GACVK,YAAaA,GACbG,gBAAiBA,IAGfE,GAAU,WACZ,IAAIhQ,EAAK7N,EAAIC,UAAUC,UAEnB4d,EAAS,CACXC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,QAASve,EAAIue,SAAWve,EAAIwe,SAC5BA,SAAUxe,EAAIue,SAAWve,EAAIwe,UAG3BL,EAAUtQ,EAAGnM,MAAM,qCACnBsc,EAAUnQ,EAAGnM,MAAM,+BACnB4c,EAAOzQ,EAAGnM,MAAM,wBAChB2c,EAAOxQ,EAAGnM,MAAM,2BAChB0c,GAAUE,GAAQzQ,EAAGnM,MAAM,8BA+C/B,GA3CIyc,IACFL,EAAOW,GAAK,UACZX,EAAOY,UAAYP,EAAQ,GAC3BL,EAAOK,SAAU,GAGfH,IAAYG,IACdL,EAAOW,GAAK,UACZX,EAAOY,UAAYV,EAAQ,GAC3BF,EAAOE,SAAU,EACjBF,EAAOG,cAAgBpQ,EAAG5P,cAAcqD,QAAQ,WAAa,IAE3Dgd,GAAQF,GAAUC,KACpBP,EAAOW,GAAK,MACZX,EAAOC,KAAM,GAGXK,IAAWC,IACbP,EAAOY,UAAYN,EAAO,GAAGpgB,QAAQ,KAAM,KAC3C8f,EAAOM,QAAS,GAEdE,IACFR,EAAOY,UAAYJ,EAAK,GAAGtgB,QAAQ,KAAM,KACzC8f,EAAOQ,MAAO,GAEZD,IACFP,EAAOY,UAAYL,EAAK,GAAKA,EAAK,GAAGrgB,QAAQ,KAAM,KAAO,KAC1D8f,EAAOM,QAAS,GAGdN,EAAOC,KAAOD,EAAOY,WAAa7Q,EAAGvM,QAAQ,aAAe,GACvB,OAAnCwc,EAAOY,UAAU/c,MAAM,KAAK,KAC9Bmc,EAAOY,UAAY7Q,EAAG5P,cAAc0D,MAAM,YAAY,GAAGA,MAAM,KAAK,IAKxEmc,EAAOI,UAAYJ,EAAOW,IAAMX,EAAOE,SAAWF,EAAOa,SAGzDb,EAAOa,SAAWP,GAAUE,GAAQD,IAASxQ,EAAGnM,MAAM,8BAGlDoc,EAAOW,IAAoB,QAAdX,EAAOW,GAAc,CACpC,IAAIG,EAAed,EAAOY,UAAU/c,MAAM,KACtCkd,EAAelgB,EAAIQ,cAAc,yBACrC2e,EAAOgB,WAAahB,EAAOa,UACrBN,GAAQD,KACU,EAAlBQ,EAAa,KAAW,EAAsB,EAAlBA,EAAa,IAAU,EAAsB,EAAlBA,EAAa,GAAS,IAC9EC,GAAgBA,EAAajc,aAAa,WAAWtB,QAAQ,eAAiB,EAOrF,OAHAwc,EAAOiB,WAAa/e,EAAIgf,kBAAoB,EAGrClB,EAhFI,GAmFb,SAASmB,GAAcla,GACrB,IAAIzJ,EAAS3C,KACToD,EAAOT,EAAO4jB,gBACd5T,EAAShQ,EAAOgQ,OAChB6T,EAAU7jB,EAAO6jB,QACrB,IAAI7jB,EAAOye,YAAazO,EAAO8O,+BAA/B,CAGA,IAAI7hB,EAAIwM,EAGR,GAFIxM,EAAE6mB,gBAAiB7mB,EAAIA,EAAE6mB,eAC7BrjB,EAAKsjB,aAA0B,eAAX9mB,EAAEsE,MACjBd,EAAKsjB,gBAAgB,UAAW9mB,IAAiB,IAAZA,EAAE+mB,WACvCvjB,EAAKsjB,cAAgB,WAAY9mB,GAAKA,EAAEgnB,OAAS,MAClDxjB,EAAKyjB,YAAazjB,EAAK0jB,SAC3B,GAAInU,EAAOoU,WAAa3e,EAAExI,EAAE2L,QAAQgF,QAAQoC,EAAOqU,kBAAoBrU,EAAOqU,kBAAqB,IAAOrU,EAAqB,gBAAI,GACjIhQ,EAAOskB,YAAa,OAGtB,IAAItU,EAAOuU,cACJ9e,EAAExI,GAAG2Q,QAAQoC,EAAOuU,cAAc,GADzC,CAIAV,EAAQW,SAAsB,eAAXvnB,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGC,MAAQznB,EAAEynB,MAC1Eb,EAAQc,SAAsB,eAAX1nB,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGG,MAAQ3nB,EAAE2nB,MAC1E,IAAIC,EAAShB,EAAQW,SACjBM,EAASjB,EAAQc,SAIjBI,EAAqB/U,EAAO+U,oBAAsB/U,EAAOgV,sBACzDC,EAAqBjV,EAAOiV,oBAAsBjV,EAAOkV,sBAC7D,IACEH,KACKF,GAAUI,GACXJ,GAAUngB,EAAIS,OAAO+N,MAAQ+R,GAHnC,CAuBA,GAfA1W,EAAMkC,OAAOhQ,EAAM,CACjByjB,WAAW,EACXC,SAAS,EACTgB,qBAAqB,EACrBC,iBAAahc,EACbic,iBAAajc,IAGfya,EAAQgB,OAASA,EACjBhB,EAAQiB,OAASA,EACjBrkB,EAAK6kB,eAAiB/W,EAAMM,MAC5B7O,EAAOskB,YAAa,EACpBtkB,EAAOiT,aACPjT,EAAOulB,oBAAiBnc,EACpB4G,EAAOwV,UAAY,IAAK/kB,EAAKglB,oBAAqB,GACvC,eAAXxoB,EAAEsE,KAAuB,CAC3B,IAAImkB,GAAiB,EACjBjgB,EAAExI,EAAE2L,QAAQI,GAAGvI,EAAKklB,gBAAiBD,GAAiB,GAExDriB,EAAIK,eACD+B,EAAEpC,EAAIK,eAAesF,GAAGvI,EAAKklB,eAC7BtiB,EAAIK,gBAAkBzG,EAAE2L,QAE3BvF,EAAIK,cAAcC,OAGpB,IAAIiiB,EAAuBF,GAAkB1lB,EAAO6lB,gBAAkB7V,EAAO8V,0BACzE9V,EAAO+V,+BAAiCH,IAC1C3oB,EAAEyoB,iBAGN1lB,EAAOmY,KAAK,aAAclb,MAG5B,SAAS+oB,GAAavc,GACpB,IAAIzJ,EAAS3C,KACToD,EAAOT,EAAO4jB,gBACd5T,EAAShQ,EAAOgQ,OAChB6T,EAAU7jB,EAAO6jB,QACjBhQ,EAAM7T,EAAO8T,aACb7W,EAAIwM,EAER,GADIxM,EAAE6mB,gBAAiB7mB,EAAIA,EAAE6mB,eACxBrjB,EAAKyjB,WAMV,IAAIzjB,EAAKsjB,cAA2B,cAAX9mB,EAAEsE,KAA3B,CACA,IAAImjB,EAAmB,cAAXznB,EAAEsE,KAAuBtE,EAAEwnB,cAAc,GAAGC,MAAQznB,EAAEynB,MAC9DE,EAAmB,cAAX3nB,EAAEsE,KAAuBtE,EAAEwnB,cAAc,GAAGG,MAAQ3nB,EAAE2nB,MAClE,GAAI3nB,EAAEgpB,wBAGJ,OAFApC,EAAQgB,OAASH,OACjBb,EAAQiB,OAASF,GAGnB,IAAK5kB,EAAO6lB,eAYV,OAVA7lB,EAAOskB,YAAa,OAChB7jB,EAAKyjB,YACP3V,EAAMkC,OAAOoT,EAAS,CACpBgB,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZnkB,EAAK6kB,eAAiB/W,EAAMM,QAIhC,GAAIpO,EAAKsjB,cAAgB/T,EAAOkW,sBAAwBlW,EAAOyK,KAC7D,GAAIza,EAAOuT,cAET,GACGqR,EAAQf,EAAQiB,QAAU9kB,EAAOmZ,WAAanZ,EAAOga,gBAClD4K,EAAQf,EAAQiB,QAAU9kB,EAAOmZ,WAAanZ,EAAOyZ,eAIzD,OAFAhZ,EAAKyjB,WAAY,OACjBzjB,EAAK0jB,SAAU,QAGZ,GACJO,EAAQb,EAAQgB,QAAU7kB,EAAOmZ,WAAanZ,EAAOga,gBAClD0K,EAAQb,EAAQgB,QAAU7kB,EAAOmZ,WAAanZ,EAAOyZ,eAEzD,OAGJ,GAAIhZ,EAAKsjB,cAAgB1gB,EAAIK,eACvBzG,EAAE2L,SAAWvF,EAAIK,eAAiB+B,EAAExI,EAAE2L,QAAQI,GAAGvI,EAAKklB,cAGxD,OAFAllB,EAAK0jB,SAAU,OACfnkB,EAAOskB,YAAa,GAOxB,GAHI7jB,EAAK0kB,qBACPnlB,EAAOmY,KAAK,YAAalb,KAEvBA,EAAEwnB,eAAiBxnB,EAAEwnB,cAAcpjB,OAAS,GAAhD,CAEAwiB,EAAQW,SAAWE,EACnBb,EAAQc,SAAWC,EAEnB,IAAIuB,EAAQtC,EAAQW,SAAWX,EAAQgB,OACnCuB,EAAQvC,EAAQc,SAAWd,EAAQiB,OACvC,KAAI9kB,EAAOgQ,OAAOwV,WAAa9P,KAAK2Q,KAAM3Q,KAAK4Q,IAAKH,EAAO,GAAQzQ,KAAK4Q,IAAKF,EAAO,IAAQpmB,EAAOgQ,OAAOwV,WAA1G,CAGE,IAAIe,EADN,GAAgC,qBAArB9lB,EAAK2kB,YAETplB,EAAOsT,gBAAkBuQ,EAAQc,WAAad,EAAQiB,QAAY9kB,EAAOuT,cAAgBsQ,EAAQW,WAAaX,EAAQgB,OACzHpkB,EAAK2kB,aAAc,EAGde,EAAQA,EAAUC,EAAQA,GAAU,KACvCG,EAA6D,IAA/C7Q,KAAK8Q,MAAM9Q,KAAK4B,IAAI8O,GAAQ1Q,KAAK4B,IAAI6O,IAAiBzQ,KAAK+Q,GACzEhmB,EAAK2kB,YAAcplB,EAAOsT,eAAiBiT,EAAavW,EAAOuW,WAAc,GAAKA,EAAavW,EAAOuW,YAY5G,GARI9lB,EAAK2kB,aACPplB,EAAOmY,KAAK,oBAAqBlb,GAEH,qBAArBwD,EAAK4kB,cACVxB,EAAQW,WAAaX,EAAQgB,QAAUhB,EAAQc,WAAad,EAAQiB,SACtErkB,EAAK4kB,aAAc,IAGnB5kB,EAAK2kB,YACP3kB,EAAKyjB,WAAY,OAGnB,GAAKzjB,EAAK4kB,YAAV,CAGArlB,EAAOskB,YAAa,EACpBrnB,EAAEyoB,iBACE1V,EAAO0W,2BAA6B1W,EAAO2W,QAC7C1pB,EAAE2pB,kBAGCnmB,EAAK0jB,UACJnU,EAAOyK,MACTza,EAAOwf,UAET/e,EAAKomB,eAAiB7mB,EAAO8O,eAC7B9O,EAAO6Y,cAAc,GACjB7Y,EAAOye,WACTze,EAAO2T,WAAW3J,QAAQ,qCAE5BvJ,EAAKqmB,qBAAsB,GAEvB9W,EAAO0R,aAAyC,IAA1B1hB,EAAOgf,iBAAqD,IAA1Bhf,EAAOif,gBACjEjf,EAAOohB,eAAc,GAEvBphB,EAAOmY,KAAK,kBAAmBlb,IAEjC+C,EAAOmY,KAAK,aAAclb,GAC1BwD,EAAK0jB,SAAU,EAEf,IAAInD,EAAOhhB,EAAOsT,eAAiB6S,EAAQC,EAC3CvC,EAAQ7C,KAAOA,EAEfA,GAAQhR,EAAO+W,WACXlT,IAAOmN,GAAQA,GAEnBhhB,EAAOulB,eAAiBvE,EAAO,EAAI,OAAS,OAC5CvgB,EAAKkd,iBAAmBqD,EAAOvgB,EAAKomB,eAEpC,IAAIG,GAAsB,EACtBC,EAAkBjX,EAAOiX,gBA0B7B,GAzBIjX,EAAOkW,sBACTe,EAAkB,GAEfjG,EAAO,GAAKvgB,EAAKkd,iBAAmB3d,EAAOyZ,gBAC9CuN,GAAsB,EAClBhX,EAAOkX,aAAczmB,EAAKkd,iBAAoB3d,EAAOyZ,eAAiB,EAAM/D,KAAK4Q,KAAOtmB,EAAOyZ,eAAiBhZ,EAAKomB,eAAiB7F,EAAOiG,KACxIjG,EAAO,GAAKvgB,EAAKkd,iBAAmB3d,EAAOga,iBACpDgN,GAAsB,EAClBhX,EAAOkX,aAAczmB,EAAKkd,iBAAoB3d,EAAOga,eAAiB,EAAMtE,KAAK4Q,IAAMtmB,EAAOga,eAAiBvZ,EAAKomB,eAAiB7F,EAAOiG,KAG9ID,IACF/pB,EAAEgpB,yBAA0B,IAIzBjmB,EAAOgf,gBAA4C,SAA1Bhf,EAAOulB,gBAA6B9kB,EAAKkd,iBAAmBld,EAAKomB,iBAC7FpmB,EAAKkd,iBAAmBld,EAAKomB,iBAE1B7mB,EAAOif,gBAA4C,SAA1Bjf,EAAOulB,gBAA6B9kB,EAAKkd,iBAAmBld,EAAKomB,iBAC7FpmB,EAAKkd,iBAAmBld,EAAKomB,gBAK3B7W,EAAOwV,UAAY,EAAG,CACxB,KAAI9P,KAAK4B,IAAI0J,GAAQhR,EAAOwV,WAAa/kB,EAAKglB,oBAW5C,YADAhlB,EAAKkd,iBAAmBld,EAAKomB,gBAT7B,IAAKpmB,EAAKglB,mBAMR,OALAhlB,EAAKglB,oBAAqB,EAC1B5B,EAAQgB,OAAShB,EAAQW,SACzBX,EAAQiB,OAASjB,EAAQc,SACzBlkB,EAAKkd,iBAAmBld,EAAKomB,oBAC7BhD,EAAQ7C,KAAOhhB,EAAOsT,eAAiBuQ,EAAQW,SAAWX,EAAQgB,OAAShB,EAAQc,SAAWd,EAAQiB,QASvG9U,EAAOmX,gBAGRnX,EAAOoX,UAAYpX,EAAOsI,qBAAuBtI,EAAOuI,yBAC1DvY,EAAOkb,oBACPlb,EAAOqa,uBAELrK,EAAOoX,WAEsB,IAA3B3mB,EAAK4mB,WAAWhmB,QAClBZ,EAAK4mB,WAAWlhB,KAAK,CACnBmhB,SAAUzD,EAAQ7jB,EAAOsT,eAAiB,SAAW,UACrDiU,KAAM9mB,EAAK6kB,iBAGf7kB,EAAK4mB,WAAWlhB,KAAK,CACnBmhB,SAAUzD,EAAQ7jB,EAAOsT,eAAiB,WAAa,YACvDiU,KAAMhZ,EAAMM,SAIhB7O,EAAO8Z,eAAerZ,EAAKkd,kBAE3B3d,EAAO4d,aAAand,EAAKkd,4BA/LnBld,EAAK4kB,aAAe5kB,EAAK2kB,aAC3BplB,EAAOmY,KAAK,oBAAqBlb,GAiMvC,SAASuqB,GAAY/d,GACnB,IAAIzJ,EAAS3C,KACToD,EAAOT,EAAO4jB,gBAEd5T,EAAShQ,EAAOgQ,OAChB6T,EAAU7jB,EAAO6jB,QACjBhQ,EAAM7T,EAAO8T,aACbH,EAAa3T,EAAO2T,WACpBY,EAAavU,EAAOuU,WACpBD,EAAWtU,EAAOsU,SAClBrX,EAAIwM,EAMR,GALIxM,EAAE6mB,gBAAiB7mB,EAAIA,EAAE6mB,eACzBrjB,EAAK0kB,qBACPnlB,EAAOmY,KAAK,WAAYlb,GAE1BwD,EAAK0kB,qBAAsB,GACtB1kB,EAAKyjB,UAMR,OALIzjB,EAAK0jB,SAAWnU,EAAO0R,YACzB1hB,EAAOohB,eAAc,GAEvB3gB,EAAK0jB,SAAU,OACf1jB,EAAK4kB,aAAc,GAIjBrV,EAAO0R,YAAcjhB,EAAK0jB,SAAW1jB,EAAKyjB,aAAwC,IAA1BlkB,EAAOgf,iBAAqD,IAA1Bhf,EAAOif,iBACnGjf,EAAOohB,eAAc,GAIvB,IAmCIqG,EAnCAC,EAAenZ,EAAMM,MACrB8Y,EAAWD,EAAejnB,EAAK6kB,eAwBnC,GArBItlB,EAAOskB,aACTtkB,EAAO0b,mBAAmBze,GAC1B+C,EAAOmY,KAAK,MAAOlb,GACf0qB,EAAW,KAAQD,EAAejnB,EAAKmnB,cAAiB,MACtDnnB,EAAKonB,cAAgBxiB,aAAa5E,EAAKonB,cAC3CpnB,EAAKonB,aAAetZ,EAAMI,UAAS,WAC5B3O,IAAUA,EAAOmf,WACtBnf,EAAOmY,KAAK,QAASlb,KACpB,MAED0qB,EAAW,KAAQD,EAAejnB,EAAKmnB,cAAiB,MACtDnnB,EAAKonB,cAAgBxiB,aAAa5E,EAAKonB,cAC3C7nB,EAAOmY,KAAK,YAAalb,KAI7BwD,EAAKmnB,cAAgBrZ,EAAMM,MAC3BN,EAAMI,UAAS,WACR3O,EAAOmf,YAAanf,EAAOskB,YAAa,OAG1C7jB,EAAKyjB,YAAczjB,EAAK0jB,UAAYnkB,EAAOulB,gBAAmC,IAAjB1B,EAAQ7C,MAAcvgB,EAAKkd,mBAAqBld,EAAKomB,eAIrH,OAHApmB,EAAKyjB,WAAY,EACjBzjB,EAAK0jB,SAAU,OACf1jB,EAAK4kB,aAAc,GAcrB,GAXA5kB,EAAKyjB,WAAY,EACjBzjB,EAAK0jB,SAAU,EACf1jB,EAAK4kB,aAAc,EAIjBoC,EADEzX,EAAOmX,aACItT,EAAM7T,EAAOmZ,WAAanZ,EAAOmZ,WAEhC1Y,EAAKkd,iBAGjB3N,EAAOoX,SAAX,CACE,GAAIK,GAAcznB,EAAOyZ,eAEvB,YADAzZ,EAAO2e,QAAQ3e,EAAO8Y,aAGxB,GAAI2O,GAAcznB,EAAOga,eAMvB,YALIha,EAAOoU,OAAO/S,OAASiT,EAASjT,OAClCrB,EAAO2e,QAAQrK,EAASjT,OAAS,GAEjCrB,EAAO2e,QAAQ3e,EAAOoU,OAAO/S,OAAS,IAK1C,GAAI2O,EAAO8X,iBAAkB,CAC3B,GAAIrnB,EAAK4mB,WAAWhmB,OAAS,EAAG,CAC9B,IAAI0mB,EAAgBtnB,EAAK4mB,WAAWW,MAChCC,EAAgBxnB,EAAK4mB,WAAWW,MAEhCE,EAAWH,EAAcT,SAAWW,EAAcX,SAClDC,EAAOQ,EAAcR,KAAOU,EAAcV,KAC9CvnB,EAAOmoB,SAAWD,EAAWX,EAC7BvnB,EAAOmoB,UAAY,EACfzS,KAAK4B,IAAItX,EAAOmoB,UAAYnY,EAAOoY,0BACrCpoB,EAAOmoB,SAAW,IAIhBZ,EAAO,KAAQhZ,EAAMM,MAAQkZ,EAAcR,KAAQ,OACrDvnB,EAAOmoB,SAAW,QAGpBnoB,EAAOmoB,SAAW,EAEpBnoB,EAAOmoB,UAAYnY,EAAOqY,8BAE1B5nB,EAAK4mB,WAAWhmB,OAAS,EACzB,IAAIinB,EAAmB,IAAOtY,EAAOuY,sBACjCC,EAAmBxoB,EAAOmoB,SAAWG,EAErCG,EAAczoB,EAAOmZ,UAAYqP,EACjC3U,IAAO4U,GAAeA,GAE1B,IACIC,EAEAC,EAHAC,GAAW,EAEXC,EAA2C,GAA5BnT,KAAK4B,IAAItX,EAAOmoB,UAAiBnY,EAAO8Y,4BAE3D,GAAIL,EAAczoB,EAAOga,eACnBhK,EAAO+Y,wBACLN,EAAczoB,EAAOga,gBAAkB6O,IACzCJ,EAAczoB,EAAOga,eAAiB6O,GAExCH,EAAsB1oB,EAAOga,eAC7B4O,GAAW,EACXnoB,EAAKqmB,qBAAsB,GAE3B2B,EAAczoB,EAAOga,eAEnBhK,EAAOyK,MAAQzK,EAAOqH,iBAAkBsR,GAAe,QACtD,GAAIF,EAAczoB,EAAOyZ,eAC1BzJ,EAAO+Y,wBACLN,EAAczoB,EAAOyZ,eAAiBoP,IACxCJ,EAAczoB,EAAOyZ,eAAiBoP,GAExCH,EAAsB1oB,EAAOyZ,eAC7BmP,GAAW,EACXnoB,EAAKqmB,qBAAsB,GAE3B2B,EAAczoB,EAAOyZ,eAEnBzJ,EAAOyK,MAAQzK,EAAOqH,iBAAkBsR,GAAe,QACtD,GAAI3Y,EAAOgZ,eAAgB,CAEhC,IADA,IAAIpO,EACKjU,EAAI,EAAGA,EAAI2N,EAASjT,OAAQsF,GAAK,EACxC,GAAI2N,EAAS3N,IAAM8hB,EAAa,CAC9B7N,EAAYjU,EACZ,MAKF8hB,EADE/S,KAAK4B,IAAIhD,EAASsG,GAAa6N,GAAe/S,KAAK4B,IAAIhD,EAASsG,EAAY,GAAK6N,IAA0C,SAA1BzoB,EAAOulB,eAC5FjR,EAASsG,GAETtG,EAASsG,EAAY,GAErC6N,GAAeA,EAQjB,GANIE,GACF3oB,EAAOic,KAAK,iBAAiB,WAC3Bjc,EAAOwf,aAIa,IAApBxf,EAAOmoB,SAEPG,EADEzU,EACiB6B,KAAK4B,MAAMmR,EAAczoB,EAAOmZ,WAAanZ,EAAOmoB,UAEpDzS,KAAK4B,KAAKmR,EAAczoB,EAAOmZ,WAAanZ,EAAOmoB,eAEnE,GAAInY,EAAOgZ,eAEhB,YADAhpB,EAAOkgB,iBAILlQ,EAAO+Y,wBAA0BH,GACnC5oB,EAAO8Z,eAAe4O,GACtB1oB,EAAO6Y,cAAcyP,GACrBtoB,EAAO4d,aAAa6K,GACpBzoB,EAAOme,iBAAgB,EAAMne,EAAOulB,gBACpCvlB,EAAOye,WAAY,EACnB9K,EAAWnJ,eAAc,WAClBxK,IAAUA,EAAOmf,WAAc1e,EAAKqmB,sBACzC9mB,EAAOmY,KAAK,kBAEZnY,EAAO6Y,cAAc7I,EAAO0I,OAC5B1Y,EAAO4d,aAAa8K,GACpB/U,EAAWnJ,eAAc,WAClBxK,IAAUA,EAAOmf,WACtBnf,EAAOwK,wBAGFxK,EAAOmoB,UAChBnoB,EAAO8Z,eAAe2O,GACtBzoB,EAAO6Y,cAAcyP,GACrBtoB,EAAO4d,aAAa6K,GACpBzoB,EAAOme,iBAAgB,EAAMne,EAAOulB,gBAC/BvlB,EAAOye,YACVze,EAAOye,WAAY,EACnB9K,EAAWnJ,eAAc,WAClBxK,IAAUA,EAAOmf,WACtBnf,EAAOwK,qBAIXxK,EAAO8Z,eAAe2O,GAGxBzoB,EAAOkb,oBACPlb,EAAOqa,2BACF,GAAIrK,EAAOgZ,eAEhB,YADAhpB,EAAOkgB,mBAIJlQ,EAAO8X,kBAAoBH,GAAY3X,EAAOiZ,gBACjDjpB,EAAO8Z,iBACP9Z,EAAOkb,oBACPlb,EAAOqa,2BAnJX,CA2JA,IAFA,IAAI6O,EAAY,EACZC,EAAYnpB,EAAOwU,gBAAgB,GAC9BlX,EAAI,EAAGA,EAAIiX,EAAWlT,OAAQ/D,GAAK0S,EAAOuG,eACI,qBAA1ChC,EAAWjX,EAAI0S,EAAOuG,gBAC3BkR,GAAclT,EAAWjX,IAAMmqB,EAAalT,EAAWjX,EAAI0S,EAAOuG,kBACpE2S,EAAY5rB,EACZ6rB,EAAY5U,EAAWjX,EAAI0S,EAAOuG,gBAAkBhC,EAAWjX,IAExDmqB,GAAclT,EAAWjX,KAClC4rB,EAAY5rB,EACZ6rB,EAAY5U,EAAWA,EAAWlT,OAAS,GAAKkT,EAAWA,EAAWlT,OAAS,IAKnF,IAAI+nB,GAAS3B,EAAalT,EAAW2U,IAAcC,EAEnD,GAAIxB,EAAW3X,EAAOiZ,aAAc,CAElC,IAAKjZ,EAAOqZ,WAEV,YADArpB,EAAO2e,QAAQ3e,EAAO8Y,aAGM,SAA1B9Y,EAAOulB,iBACL6D,GAASpZ,EAAOsZ,gBAAmBtpB,EAAO2e,QAAQuK,EAAYlZ,EAAOuG,gBAClEvW,EAAO2e,QAAQuK,IAEM,SAA1BlpB,EAAOulB,iBACL6D,EAAS,EAAIpZ,EAAOsZ,gBAAoBtpB,EAAO2e,QAAQuK,EAAYlZ,EAAOuG,gBACvEvW,EAAO2e,QAAQuK,QAEnB,CAEL,IAAKlZ,EAAOuZ,YAEV,YADAvpB,EAAO2e,QAAQ3e,EAAO8Y,aAGM,SAA1B9Y,EAAOulB,gBACTvlB,EAAO2e,QAAQuK,EAAYlZ,EAAOuG,gBAEN,SAA1BvW,EAAOulB,gBACTvlB,EAAO2e,QAAQuK,KAKrB,SAASM,KACP,IAAIxpB,EAAS3C,KAET2S,EAAShQ,EAAOgQ,OAChBrI,EAAK3H,EAAO2H,GAEhB,IAAIA,GAAyB,IAAnBA,EAAGoD,YAAb,CAGIiF,EAAOyZ,aACTzpB,EAAO0pB,gBAIT,IAAI1K,EAAiBhf,EAAOgf,eACxBC,EAAiBjf,EAAOif,eACxB3K,EAAWtU,EAAOsU,SAStB,GANAtU,EAAOgf,gBAAiB,EACxBhf,EAAOif,gBAAiB,EAExBjf,EAAOiT,aACPjT,EAAO0T,eAEH1D,EAAOoX,SAAU,CACnB,IAAIuC,EAAejU,KAAKkU,IAAIlU,KAAKK,IAAI/V,EAAOmZ,UAAWnZ,EAAOga,gBAAiBha,EAAOyZ,gBACtFzZ,EAAO4d,aAAa+L,GACpB3pB,EAAOkb,oBACPlb,EAAOqa,sBAEHrK,EAAOsO,YACTte,EAAOyY,wBAGTzY,EAAOqa,uBACuB,SAAzBrK,EAAO6F,eAA4B7F,EAAO6F,cAAgB,IAAM7V,EAAOka,QAAUla,EAAOgQ,OAAOqH,eAClGrX,EAAO2e,QAAQ3e,EAAOoU,OAAO/S,OAAS,EAAG,GAAG,GAAO,GAEnDrB,EAAO2e,QAAQ3e,EAAO8Y,YAAa,GAAG,GAAO,GAG7C9Y,EAAO6pB,UAAY7pB,EAAO6pB,SAASC,SAAW9pB,EAAO6pB,SAASE,QAChE/pB,EAAO6pB,SAASG,MAGlBhqB,EAAOif,eAAiBA,EACxBjf,EAAOgf,eAAiBA,EAEpBhf,EAAOgQ,OAAOoI,eAAiB9D,IAAatU,EAAOsU,UACrDtU,EAAOqY,iBAIX,SAAS4R,GAAShtB,GAChB,IAAI+C,EAAS3C,KACR2C,EAAOskB,aACNtkB,EAAOgQ,OAAOka,eAAiBjtB,EAAEyoB,iBACjC1lB,EAAOgQ,OAAOma,0BAA4BnqB,EAAOye,YACnDxhB,EAAE2pB,kBACF3pB,EAAEmtB,6BAKR,SAASC,KACP,IAAIrqB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChBsa,EAActqB,EAAOsqB,YACrB3iB,EAAK3H,EAAO2H,GACZ4iB,EAAYvqB,EAAOuqB,UAGrBvqB,EAAO2jB,aAAeA,GAAa1G,KAAKjd,GACxCA,EAAOgmB,YAAcA,GAAY/I,KAAKjd,GACtCA,EAAOwnB,WAAaA,GAAWvK,KAAKjd,GAGtCA,EAAOiqB,QAAUA,GAAQhN,KAAKjd,GAE9B,IAAI4I,EAAsC,cAA7BoH,EAAOwa,kBAAoC7iB,EAAK4iB,EACzD7hB,IAAYsH,EAAO2W,OAIrB,GAAKzV,EAAQE,QAAUF,EAAQM,gBAAiBN,EAAQS,sBAIjD,CACL,GAAIT,EAAQE,MAAO,CACjB,IAAIa,IAAwC,eAAtBqY,EAAYG,QAA0BvZ,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAC3IE,EAAOpF,iBAAiB8mB,EAAYG,MAAOzqB,EAAO2jB,aAAc1R,GAChErJ,EAAOpF,iBAAiB8mB,EAAYM,KAAM5qB,EAAOgmB,YAAa9U,EAAQe,gBAAkB,CAAE0Y,SAAS,EAAOjiB,QAASA,GAAYA,GAC/HE,EAAOpF,iBAAiB8mB,EAAYO,IAAK7qB,EAAOwnB,WAAYvV,IAEzDjC,EAAOsR,gBAAkBiB,GAAOE,MAAQF,GAAOG,SAAa1S,EAAOsR,gBAAkBpQ,EAAQE,OAASmR,GAAOE,OAChH7Z,EAAOpF,iBAAiB,YAAaxD,EAAO2jB,cAAc,GAC1DtgB,EAAIG,iBAAiB,YAAaxD,EAAOgmB,YAAatd,GACtDrF,EAAIG,iBAAiB,UAAWxD,EAAOwnB,YAAY,SAbrD5e,EAAOpF,iBAAiB8mB,EAAYG,MAAOzqB,EAAO2jB,cAAc,GAChEtgB,EAAIG,iBAAiB8mB,EAAYM,KAAM5qB,EAAOgmB,YAAatd,GAC3DrF,EAAIG,iBAAiB8mB,EAAYO,IAAK7qB,EAAOwnB,YAAY,IAevDxX,EAAOka,eAAiBla,EAAOma,2BACjCvhB,EAAOpF,iBAAiB,QAASxD,EAAOiqB,SAAS,GAKrDjqB,EAAOsC,GAAIigB,GAAOE,KAAOF,GAAOG,QAAU,0CAA4C,wBAA0B8G,IAAU,GAG5H,SAASsB,KACP,IAAI9qB,EAAS3C,KAET2S,EAAShQ,EAAOgQ,OAChBsa,EAActqB,EAAOsqB,YACrB3iB,EAAK3H,EAAO2H,GACZ4iB,EAAYvqB,EAAOuqB,UAEnB3hB,EAAsC,cAA7BoH,EAAOwa,kBAAoC7iB,EAAK4iB,EACzD7hB,IAAYsH,EAAO2W,OAIrB,GAAKzV,EAAQE,QAAUF,EAAQM,gBAAiBN,EAAQS,sBAIjD,CACL,GAAIT,EAAQE,MAAO,CACjB,IAAIa,IAAwC,iBAAtBqY,EAAYG,QAA4BvZ,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAC7IE,EAAOnF,oBAAoB6mB,EAAYG,MAAOzqB,EAAO2jB,aAAc1R,GACnErJ,EAAOnF,oBAAoB6mB,EAAYM,KAAM5qB,EAAOgmB,YAAatd,GACjEE,EAAOnF,oBAAoB6mB,EAAYO,IAAK7qB,EAAOwnB,WAAYvV,IAE5DjC,EAAOsR,gBAAkBiB,GAAOE,MAAQF,GAAOG,SAAa1S,EAAOsR,gBAAkBpQ,EAAQE,OAASmR,GAAOE,OAChH7Z,EAAOnF,oBAAoB,YAAazD,EAAO2jB,cAAc,GAC7DtgB,EAAII,oBAAoB,YAAazD,EAAOgmB,YAAatd,GACzDrF,EAAII,oBAAoB,UAAWzD,EAAOwnB,YAAY,SAbxD5e,EAAOnF,oBAAoB6mB,EAAYG,MAAOzqB,EAAO2jB,cAAc,GACnEtgB,EAAII,oBAAoB6mB,EAAYM,KAAM5qB,EAAOgmB,YAAatd,GAC9DrF,EAAII,oBAAoB6mB,EAAYO,IAAK7qB,EAAOwnB,YAAY,IAe1DxX,EAAOka,eAAiBla,EAAOma,2BACjCvhB,EAAOnF,oBAAoB,QAASzD,EAAOiqB,SAAS,GAKxDjqB,EAAO2J,IAAK4Y,GAAOE,KAAOF,GAAOG,QAAU,0CAA4C,wBAA0B8G,IAGnH,IAAIngB,GAAS,CACXghB,aAAcA,GACdS,aAAcA,IAGhB,SAASpB,KACP,IAAI1pB,EAAS3C,KACTyb,EAAc9Y,EAAO8Y,YACrB0C,EAAcxb,EAAOwb,YACrB8D,EAAetf,EAAOsf,kBAAoC,IAAjBA,IAA0BA,EAAe,GACtF,IAAItP,EAAShQ,EAAOgQ,OAChByZ,EAAczZ,EAAOyZ,YACzB,GAAKA,KAAgBA,GAAmD,IAApC1rB,OAAOsQ,KAAKob,GAAapoB,QAA7D,CAGA,IAAI0pB,EAAa/qB,EAAOgrB,cAAcvB,GAEtC,GAAIsB,GAAc/qB,EAAOirB,oBAAsBF,EAAY,CACzD,IAAIG,EAAuBH,KAActB,EAAcA,EAAYsB,QAAc3hB,EAC7E8hB,GACF,CAAC,gBAAiB,eAAgB,kBAAkB7oB,SAAQ,SAAU4N,GACpE,IAAIkb,EAAaD,EAAqBjb,GACZ,qBAAfkb,IAITD,EAAqBjb,GAHT,kBAAVA,GAA6C,SAAfkb,GAAwC,SAAfA,EAEtC,kBAAVlb,EACqBjF,WAAWmgB,GAEX3X,SAAS2X,EAAY,IAJrB,WASpC,IAAIC,EAAmBF,GAAwBlrB,EAAOqrB,eAClDC,EAAmBF,EAAiB/M,WAAa+M,EAAiB/M,YAAcrO,EAAOqO,UACvFkN,EAAcvb,EAAOyK,OAAS2Q,EAAiBvV,gBAAkB7F,EAAO6F,eAAiByV,GAEzFA,GAAoB9P,GACtBxb,EAAOwrB,kBAGTjd,EAAMkC,OAAOzQ,EAAOgQ,OAAQob,GAE5B7c,EAAMkC,OAAOzQ,EAAQ,CACnB6lB,eAAgB7lB,EAAOgQ,OAAO6V,eAC9B7G,eAAgBhf,EAAOgQ,OAAOgP,eAC9BC,eAAgBjf,EAAOgQ,OAAOiP,iBAGhCjf,EAAOirB,kBAAoBF,EAEvBQ,GAAe/P,IACjBxb,EAAOmhB,cACPnhB,EAAOugB,aACPvgB,EAAO0T,eACP1T,EAAO2e,QAAS7F,EAAcwG,EAAgBtf,EAAOsf,aAAc,GAAG,IAGxEtf,EAAOmY,KAAK,aAAciT,KAI9B,SAASJ,GAAevB,GACtB,IAAIzpB,EAAS3C,KAEb,GAAKosB,EAAL,CACA,IAAIsB,GAAa,EACbU,EAAS,GACb1tB,OAAOsQ,KAAKob,GAAapnB,SAAQ,SAAUqpB,GACzCD,EAAOtlB,KAAKulB,MAEdD,EAAOE,MAAK,SAAUjtB,EAAGktB,GAAK,OAAOpY,SAAS9U,EAAG,IAAM8U,SAASoY,EAAG,OACnE,IAAK,IAAItuB,EAAI,EAAGA,EAAImuB,EAAOpqB,OAAQ/D,GAAK,EAAG,CACzC,IAAIouB,EAAQD,EAAOnuB,GACf0C,EAAOgQ,OAAO6b,mBACZH,GAAShnB,EAAIonB,aACff,EAAaW,GAENA,GAAShnB,EAAIonB,aAAef,IACrCA,EAAaW,GAGjB,OAAOX,GAAc,OAGvB,IAAItB,GAAc,CAAEC,cAAeA,GAAesB,cAAeA,IAEjE,SAASe,KACP,IAAI/rB,EAAS3C,KACT2uB,EAAahsB,EAAOgsB,WACpBhc,EAAShQ,EAAOgQ,OAChB6D,EAAM7T,EAAO6T,IACb3R,EAAMlC,EAAOkC,IACb+pB,EAAW,GAEfA,EAAS9lB,KAAK,eACd8lB,EAAS9lB,KAAK6J,EAAOqO,WAEjBrO,EAAOoX,UACT6E,EAAS9lB,KAAK,aAEX+K,EAAQa,SACXka,EAAS9lB,KAAK,cAEZ6J,EAAOsO,YACT2N,EAAS9lB,KAAK,cAEZ0N,GACFoY,EAAS9lB,KAAK,OAEZ6J,EAAOyF,gBAAkB,GAC3BwW,EAAS9lB,KAAK,YAEZoc,GAAOG,SACTuJ,EAAS9lB,KAAK,WAEZoc,GAAOE,KACTwJ,EAAS9lB,KAAK,QAGXkM,EAAQG,MAAQH,EAAQI,UAAYvB,EAAQM,eAAiBN,EAAQS,wBACxEsa,EAAS9lB,KAAM,OAAU6J,EAAgB,WAG3Cic,EAAS5pB,SAAQ,SAAU6pB,GACzBF,EAAW7lB,KAAK6J,EAAOmc,uBAAyBD,MAGlDhqB,EAAIuE,SAASulB,EAAW1c,KAAK,MAG/B,SAAS8c,KACP,IAAIpsB,EAAS3C,KACT6E,EAAMlC,EAAOkC,IACb8pB,EAAahsB,EAAOgsB,WAExB9pB,EAAI2E,YAAYmlB,EAAW1c,KAAK,MAGlC,IAAI7N,GAAU,CAAEsqB,WAAYA,GAAYK,cAAeA,IAEvD,SAASC,GAAWC,EAASC,EAAKC,EAAQC,EAAOC,EAAkBjiB,GACjE,IAAIkiB,EACJ,SAASC,IACHniB,GAAYA,IAEb6hB,EAAQO,UAAaH,EAmBxBE,IAlBIL,GACFI,EAAQ,IAAIjoB,EAAIO,MAChB0nB,EAAMG,OAASF,EACfD,EAAMI,QAAUH,EACZH,IACFE,EAAMF,MAAQA,GAEZD,IACFG,EAAMH,OAASA,GAEbD,IACFI,EAAMJ,IAAMA,IAGdK,IAQN,SAASI,KACP,IAAIhtB,EAAS3C,KAEb,SAASuvB,IACe,qBAAX5sB,GAAqC,OAAXA,GAAoBA,IAAUA,EAAOmf,iBAC9C/V,IAAxBpJ,EAAOitB,eAA8BjtB,EAAOitB,cAAgB,GAC5DjtB,EAAOitB,eAAiBjtB,EAAOktB,aAAa7rB,SAC1CrB,EAAOgQ,OAAOmd,qBAAuBntB,EAAOY,SAChDZ,EAAOmY,KAAK,iBANhBnY,EAAOktB,aAAeltB,EAAOkC,IAAI2L,KAAK,OAStC,IAAK,IAAIvQ,EAAI,EAAGA,EAAI0C,EAAOktB,aAAa7rB,OAAQ/D,GAAK,EAAG,CACtD,IAAIgvB,EAAUtsB,EAAOktB,aAAa5vB,GAClC0C,EAAOqsB,UACLC,EACAA,EAAQc,YAAcd,EAAQhlB,aAAa,OAC3CglB,EAAQE,QAAUF,EAAQhlB,aAAa,UACvCglB,EAAQG,OAASH,EAAQhlB,aAAa,UACtC,EACAslB,IAKN,IAAIS,GAAS,CACXhB,UAAWA,GACXW,cAAeA,IAGjB,SAAS3U,KACP,IAAIrY,EAAS3C,KACTiwB,EAAYttB,EAAOuhB,SAEvBvhB,EAAOuhB,SAAsC,IAA3BvhB,EAAOsU,SAASjT,OAClCrB,EAAOgf,gBAAkBhf,EAAOuhB,SAChCvhB,EAAOif,gBAAkBjf,EAAOuhB,SAG5B+L,IAActtB,EAAOuhB,UAAYvhB,EAAOmY,KAAKnY,EAAOuhB,SAAW,OAAS,UAExE+L,GAAaA,IAActtB,EAAOuhB,WACpCvhB,EAAOka,OAAQ,EACfla,EAAOgC,WAAWpB,UAItB,IAAI2sB,GAAkB,CAAElV,cAAeA,IAEnCmV,GAAW,CACbC,MAAM,EACNpP,UAAW,aACXmM,kBAAmB,YACnBzL,aAAc,EACdrG,MAAO,IAEPoG,gCAAgC,EAGhCiG,oBAAoB,EACpBE,mBAAoB,GAGpBmC,UAAU,EACVU,kBAAkB,EAClBS,sBAAuB,EACvBQ,wBAAwB,EACxBD,4BAA6B,EAC7BT,8BAA+B,EAC/BW,gBAAgB,EAChBZ,wBAAyB,IAGzB9J,YAAY,EAGZ9G,gBAAgB,EAGhBkG,kBAAkB,EAGlBnG,OAAQ,QAGRkS,iBAAargB,EACbyiB,oBAAoB,EAGpB9W,aAAc,EACdc,cAAe,EACfJ,gBAAiB,EACjBK,oBAAqB,SACrBS,eAAgB,EAChBc,gBAAgB,EAChB3C,mBAAoB,EACpBE,kBAAmB,EACnB2G,qBAAqB,EACrB1D,0BAA0B,EAG1BO,eAAe,EAGfvB,cAAc,EAGdkQ,WAAY,EACZR,WAAY,GACZjF,eAAe,EACfiI,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBL,aAAc,IACd9B,cAAc,EACdtB,gBAAgB,EAChBL,UAAW,EACXkB,0BAA0B,EAC1BZ,0BAA0B,EAC1BC,+BAA+B,EAC/BG,qBAAqB,EAGrBwH,mBAAmB,EAGnBxG,YAAY,EACZD,gBAAiB,IAGjB3O,qBAAqB,EACrBC,uBAAuB,EAGvBmJ,YAAY,EAGZwI,eAAe,EACfC,0BAA0B,EAC1BrO,qBAAqB,EAGrBkR,eAAe,EACfG,qBAAqB,EAGrB1S,MAAM,EACNkG,qBAAsB,EACtBrB,aAAc,KACdkB,wBAAwB,EAGxBvB,gBAAgB,EAChBD,gBAAgB,EAChBuF,aAAc,KACdH,WAAW,EACXuJ,eAAgB,oBAChBtJ,kBAAmB,KAGnBqG,kBAAkB,EAGlByB,uBAAwB,oBACxBzrB,WAAY,eACZktB,gBAAiB,+BACjBpT,iBAAkB,sBAClBG,0BAA2B,gCAC3BtB,kBAAmB,uBACnBqB,oBAAqB,yBACrBG,eAAgB,oBAChBG,wBAAyB,8BACzBD,eAAgB,oBAChBE,wBAAyB,8BACzBvZ,aAAc,iBAGd+Z,oBAAoB,GAKlBoS,GAAa,CACfjtB,OAAQA,GACRuY,UAAWA,GACXlR,WAAYyW,GACZvI,MAAOA,GACPsE,KAAMA,GACNiH,WAAYA,GACZY,aAAcA,GACdjZ,OAAQA,GACRogB,YAAaA,GACbpR,cAAekV,GACf9rB,QAASA,GACT4rB,OAAQA,IAGNS,GAAmB,GAEnB7tB,GAAuB,SAAU2S,GACnC,SAAS3S,IACP,IAAIiB,EAIAyG,EACAqI,EAHA3H,EAAO,GAAIC,EAAMlH,UAAUC,OAC/B,MAAQiH,IAAQD,EAAMC,GAAQlH,UAAWkH,GAGrB,IAAhBD,EAAKhH,QAAgBgH,EAAK,GAAGmI,aAAenI,EAAK,GAAGmI,cAAgBzS,OACtEiS,EAAS3H,EAAK,IAEbnH,EAASmH,EAAMV,EAAKzG,EAAO,GAAI8O,EAAS9O,EAAO,IAE7C8O,IAAUA,EAAS,IAExBA,EAASzB,EAAMkC,OAAO,GAAIT,GACtBrI,IAAOqI,EAAOrI,KAAMqI,EAAOrI,GAAKA,GAEpCiL,EAAYlV,KAAKL,KAAM2S,GAEvBjS,OAAOsQ,KAAKwf,IAAYxrB,SAAQ,SAAU0rB,GACxChwB,OAAOsQ,KAAKwf,GAAWE,IAAiB1rB,SAAQ,SAAU2rB,GACnD/tB,EAAO3B,UAAU0vB,KACpB/tB,EAAO3B,UAAU0vB,GAAeH,GAAWE,GAAgBC,UAMjE,IAAIhuB,EAAS3C,KACiB,qBAAnB2C,EAAO0c,UAChB1c,EAAO0c,QAAU,IAEnB3e,OAAOsQ,KAAKrO,EAAO0c,SAASra,SAAQ,SAAUsa,GAC5C,IAAIxf,EAAS6C,EAAO0c,QAAQC,GAC5B,GAAIxf,EAAO6S,OAAQ,CACjB,IAAIie,EAAkBlwB,OAAOsQ,KAAKlR,EAAO6S,QAAQ,GAC7C8M,EAAe3f,EAAO6S,OAAOie,GACjC,GAA4B,kBAAjBnR,GAA8C,OAAjBA,EAAyB,OACjE,KAAMmR,KAAmBje,GAAU,YAAa8M,GAAiB,QACjC,IAA5B9M,EAAOie,KACTje,EAAOie,GAAmB,CAAE/Z,SAAS,IAGF,kBAA5BlE,EAAOie,IACT,YAAaje,EAAOie,KAEzBje,EAAOie,GAAiB/Z,SAAU,GAE/BlE,EAAOie,KAAoBje,EAAOie,GAAmB,CAAE/Z,SAAS,QAKzE,IAAIga,EAAe3f,EAAMkC,OAAO,GAAI+c,IACpCxtB,EAAOuc,iBAAiB2R,GAGxBluB,EAAOgQ,OAASzB,EAAMkC,OAAO,GAAIyd,EAAcJ,GAAkB9d,GACjEhQ,EAAOqrB,eAAiB9c,EAAMkC,OAAO,GAAIzQ,EAAOgQ,QAChDhQ,EAAOmuB,aAAe5f,EAAMkC,OAAO,GAAIT,GAGvChQ,EAAOyF,EAAIA,EAGX,IAAIvD,EAAMuD,EAAEzF,EAAOgQ,OAAOrI,IAG1B,GAFAA,EAAKzF,EAAI,GAEJyF,EAAL,CAIA,GAAIzF,EAAIb,OAAS,EAAG,CAClB,IAAI+sB,EAAU,GAKd,OAJAlsB,EAAI8J,MAAK,SAAUO,EAAO8hB,GACxB,IAAIC,EAAY/f,EAAMkC,OAAO,GAAIT,EAAQ,CAAErI,GAAI0mB,IAC/CD,EAAQjoB,KAAK,IAAIlG,EAAOquB,OAEnBF,EAGTzmB,EAAG3H,OAASA,EACZkC,EAAIzB,KAAK,SAAUT,GAGnB,IAAI2T,EAAazR,EAAIiC,SAAU,IAAOnE,EAAOgQ,OAAmB,cAwHhE,OArHAzB,EAAMkC,OAAOzQ,EAAQ,CACnBkC,IAAKA,EACLyF,GAAIA,EACJgM,WAAYA,EACZ4W,UAAW5W,EAAW,GAGtBqY,WAAY,GAGZ5X,OAAQ3O,IACR8O,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBlB,aAAc,WACZ,MAAmC,eAA5BtT,EAAOgQ,OAAOqO,WAEvB9K,WAAY,WACV,MAAmC,aAA5BvT,EAAOgQ,OAAOqO,WAGvBxK,IAA+B,QAAzBlM,EAAG4W,IAAI5b,eAAoD,QAAzBT,EAAI4J,IAAI,aAChDgI,aAA0C,eAA5B9T,EAAOgQ,OAAOqO,YAAwD,QAAzB1W,EAAG4W,IAAI5b,eAAoD,QAAzBT,EAAI4J,IAAI,cACrGiI,SAAwC,gBAA9BJ,EAAW7H,IAAI,WAGzBgN,YAAa,EACbyB,UAAW,EAGXN,aAAa,EACbC,OAAO,EAGPf,UAAW,EACX+E,kBAAmB,EACnBrE,SAAU,EACVsO,SAAU,EACV1J,WAAW,EAGXO,eAAgBhf,EAAOgQ,OAAOgP,eAC9BC,eAAgBjf,EAAOgQ,OAAOiP,eAG9BqL,YAAc,WACZ,IAAIlZ,EAAQ,CAAC,aAAc,YAAa,YACpCwR,EAAU,CAAC,YAAa,YAAa,WAgBzC,OAfI1R,EAAQM,cACVoR,EAAU,CAAC,cAAe,cAAe,aAChC1R,EAAQS,wBACjBiR,EAAU,CAAC,gBAAiB,gBAAiB,gBAE/C5iB,EAAOuuB,iBAAmB,CACxB9D,MAAOrZ,EAAM,GACbwZ,KAAMxZ,EAAM,GACZyZ,IAAKzZ,EAAM,IAEbpR,EAAOwuB,mBAAqB,CAC1B/D,MAAO7H,EAAQ,GACfgI,KAAMhI,EAAQ,GACdiI,IAAKjI,EAAQ,IAER1R,EAAQE,QAAUpR,EAAOgQ,OAAOsR,cAAgBthB,EAAOuuB,iBAAmBvuB,EAAOwuB,mBAlB7E,GAoBb5K,gBAAiB,CACfM,eAAW9a,EACX+a,aAAS/a,EACT+b,yBAAqB/b,EACrBkc,oBAAgBlc,EAChBgc,iBAAahc,EACbuU,sBAAkBvU,EAClByd,oBAAgBzd,EAChBqc,wBAAoBrc,EAEpBuc,aAAc,iDAEdiC,cAAerZ,EAAMM,MACrBgZ,kBAAcze,EAEdie,WAAY,GACZP,yBAAqB1d,EACrB2a,kBAAc3a,EACdic,iBAAajc,GAIfkb,YAAY,EAGZuB,eAAgB7lB,EAAOgQ,OAAO6V,eAE9BhC,QAAS,CACPgB,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACV3D,KAAM,GAIRkM,aAAc,GACdD,aAAc,IAKhBjtB,EAAO4c,aAGH5c,EAAOgQ,OAAOyd,MAChBztB,EAAOytB,OAIFztB,GAGJ4S,IAAc3S,EAAOwuB,UAAY7b,GACtC3S,EAAO3B,UAAYP,OAAOof,OAAQvK,GAAeA,EAAYtU,WAC7D2B,EAAO3B,UAAUkS,YAAcvQ,EAE/B,IAAI8S,EAAkB,CAAE+a,iBAAkB,CAAE7vB,cAAc,GAAOuvB,SAAU,CAAEvvB,cAAc,GAAOkQ,MAAO,CAAElQ,cAAc,GAAOwH,EAAG,CAAExH,cAAc,IAiQnJ,OA/PAgC,EAAO3B,UAAU+hB,qBAAuB,WACtC,IAAIrgB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChBoE,EAASpU,EAAOoU,OAChBG,EAAavU,EAAOuU,WACpBX,EAAa5T,EAAOyT,KACpBqF,EAAc9Y,EAAO8Y,YACrB4V,EAAM,EACV,GAAI1e,EAAOqH,eAAgB,CAGzB,IAFA,IACIsX,EADAxZ,EAAYf,EAAO0E,GAAa1B,gBAE3B9Z,EAAIwb,EAAc,EAAGxb,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAChD8W,EAAO9W,KAAOqxB,IAChBxZ,GAAaf,EAAO9W,GAAG8Z,gBACvBsX,GAAO,EACHvZ,EAAYvB,IAAc+a,GAAY,IAG9C,IAAK,IAAIlX,EAAMqB,EAAc,EAAGrB,GAAO,EAAGA,GAAO,EAC3CrD,EAAOqD,KAASkX,IAClBxZ,GAAaf,EAAOqD,GAAKL,gBACzBsX,GAAO,EACHvZ,EAAYvB,IAAc+a,GAAY,SAI9C,IAAK,IAAIhX,EAAMmB,EAAc,EAAGnB,EAAMvD,EAAO/S,OAAQsW,GAAO,EACtDpD,EAAWoD,GAAOpD,EAAWuE,GAAelF,IAC9C8a,GAAO,GAIb,OAAOA,GAGTzuB,EAAO3B,UAAUsC,OAAS,WACxB,IAAIZ,EAAS3C,KACb,GAAK2C,IAAUA,EAAOmf,UAAtB,CACA,IAkBIyP,EAlBAta,EAAWtU,EAAOsU,SAClBtE,EAAShQ,EAAOgQ,OAEhBA,EAAOyZ,aACTzpB,EAAO0pB,gBAET1pB,EAAOiT,aACPjT,EAAO0T,eACP1T,EAAO8Z,iBACP9Z,EAAOqa,sBAUHra,EAAOgQ,OAAOoX,UAChBxJ,IACI5d,EAAOgQ,OAAOsO,YAChBte,EAAOyY,qBAIPmW,GADmC,SAAhC5uB,EAAOgQ,OAAO6F,eAA4B7V,EAAOgQ,OAAO6F,cAAgB,IAAM7V,EAAOka,QAAUla,EAAOgQ,OAAOqH,eACnGrX,EAAO2e,QAAQ3e,EAAOoU,OAAO/S,OAAS,EAAG,GAAG,GAAO,GAEnDrB,EAAO2e,QAAQ3e,EAAO8Y,YAAa,GAAG,GAAO,GAEvD8V,GACHhR,KAGA5N,EAAOoI,eAAiB9D,IAAatU,EAAOsU,UAC9CtU,EAAOqY,gBAETrY,EAAOmY,KAAK,UA1BZ,SAASyF,IACP,IAAIiR,EAAiB7uB,EAAO8T,cAAmC,EAApB9T,EAAOmZ,UAAiBnZ,EAAOmZ,UACtEwQ,EAAejU,KAAKkU,IAAIlU,KAAKK,IAAI8Y,EAAgB7uB,EAAOga,gBAAiBha,EAAOyZ,gBACpFzZ,EAAO4d,aAAa+L,GACpB3pB,EAAOkb,oBACPlb,EAAOqa,wBAwBXpa,EAAO3B,UAAUktB,gBAAkB,SAA0BsD,EAAcC,QACrD,IAAfA,IAAwBA,GAAa,GAE1C,IAAI/uB,EAAS3C,KACT2xB,EAAmBhvB,EAAOgQ,OAAOqO,UAKrC,OAJKyQ,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAE7DF,IAAiBE,GAAuC,eAAjBF,GAAkD,aAAjBA,EACpE9uB,GAGTA,EAAOkC,IACJ2E,YAAa,GAAM7G,EAAOgQ,OAA6B,uBAAIgf,EAAmB,QAAUA,GACxFvoB,SAAU,GAAMzG,EAAOgQ,OAA6B,uBAAI8e,IAEtDzc,EAAQG,MAAQH,EAAQI,UAAYvB,EAAQM,eAAiBN,EAAQS,wBACxE3R,EAAOkC,IAAIuE,SAAWzG,EAAOgQ,OAA6B,uBAAI,OAAS8e,GAGzE9uB,EAAOgQ,OAAOqO,UAAYyQ,EAE1B9uB,EAAOoU,OAAOpI,MAAK,SAAU6S,EAAYoQ,GAClB,aAAjBH,EACFG,EAAQ5qB,MAAM6O,MAAQ,GAEtB+b,EAAQ5qB,MAAM8O,OAAS,MAI3BnT,EAAOmY,KAAK,mBACR4W,GAAc/uB,EAAOY,SAElBZ,IAGTC,EAAO3B,UAAUmvB,KAAO,WACtB,IAAIztB,EAAS3C,KACT2C,EAAOwb,cAEXxb,EAAOmY,KAAK,cAGRnY,EAAOgQ,OAAOyZ,aAChBzpB,EAAO0pB,gBAIT1pB,EAAO+rB,aAGH/rB,EAAOgQ,OAAOyK,MAChBza,EAAOugB,aAITvgB,EAAOiT,aAGPjT,EAAO0T,eAEH1T,EAAOgQ,OAAOoI,eAChBpY,EAAOqY,gBAILrY,EAAOgQ,OAAO0R,YAChB1hB,EAAOohB,gBAGLphB,EAAOgQ,OAAOgd,eAChBhtB,EAAOgtB,gBAILhtB,EAAOgQ,OAAOyK,KAChBza,EAAO2e,QAAQ3e,EAAOgQ,OAAO+O,aAAe/e,EAAOsf,aAAc,EAAGtf,EAAOgQ,OAAOyL,oBAElFzb,EAAO2e,QAAQ3e,EAAOgQ,OAAO+O,aAAc,EAAG/e,EAAOgQ,OAAOyL,oBAI9Dzb,EAAOqqB,eAGPrqB,EAAOwb,aAAc,EAGrBxb,EAAOmY,KAAK,UAGdlY,EAAO3B,UAAUyD,QAAU,SAAkBmtB,EAAgBC,QACnC,IAAnBD,IAA4BA,GAAiB,QAC7B,IAAhBC,IAAyBA,GAAc,GAE5C,IAAInvB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAChB9N,EAAMlC,EAAOkC,IACbyR,EAAa3T,EAAO2T,WACpBS,EAASpU,EAAOoU,OAEpB,MAA6B,qBAAlBpU,EAAOgQ,QAA0BhQ,EAAOmf,UAC1C,MAGTnf,EAAOmY,KAAK,iBAGZnY,EAAOwb,aAAc,EAGrBxb,EAAO8qB,eAGH9a,EAAOyK,MACTza,EAAOmhB,cAILgO,IACFnvB,EAAOosB,gBACPlqB,EAAIsF,WAAW,SACfmM,EAAWnM,WAAW,SAClB4M,GAAUA,EAAO/S,QACnB+S,EACGvN,YAAY,CACXmJ,EAAOqJ,kBACPrJ,EAAOwK,iBACPxK,EAAO6K,eACP7K,EAAO+K,gBAAiBzL,KAAK,MAC9B9H,WAAW,SACXA,WAAW,2BACXA,WAAW,sBACXA,WAAW,oBAIlBxH,EAAOmY,KAAK,WAGZpa,OAAOsQ,KAAKrO,EAAO6S,iBAAiBxQ,SAAQ,SAAUyQ,GACpD9S,EAAO2J,IAAImJ,OAGU,IAAnBoc,IACFlvB,EAAOkC,IAAI,GAAGlC,OAAS,KACvBA,EAAOkC,IAAIzB,KAAK,SAAU,MAC1B8N,EAAMC,YAAYxO,IAEpBA,EAAOmf,WAAY,EAEZ,OAGTlf,EAAOmvB,eAAiB,SAAyBC,GAC/C9gB,EAAMkC,OAAOqd,GAAkBuB,IAGjCtc,EAAgB+a,iBAAiB3vB,IAAM,WACrC,OAAO2vB,IAGT/a,EAAgBya,SAASrvB,IAAM,WAC7B,OAAOqvB,IAGTza,EAAgB5E,MAAMhQ,IAAM,WAC1B,OAAOyU,GAGTG,EAAgBtN,EAAEtH,IAAM,WACtB,OAAOsH,GAGT1H,OAAO0f,iBAAkBxd,EAAQ8S,GAE1B9S,EAtdiB,CAudxB2S,GAEE0c,GAAW,CACb/uB,KAAM,SACNgd,MAAO,CACLiF,OAAQD,IAEV/E,OAAQ,CACNgF,OAAQD,KAIRgN,GAAY,CACdhvB,KAAM,UACNgd,MAAO,CACLiS,QAASte,GAEXsM,OAAQ,CACNgS,QAASte,IAITue,GAAY,CACdlvB,KAAM,UACNgd,MAAO,CACLmS,QAASrd,GAEXmL,OAAQ,CACNkS,QAASrd,IAITsd,GAAS,CACXpvB,KAAM,SACN4c,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB4vB,OAAQ,CACNC,cAAe,WACR7vB,IAAUA,EAAOmf,WAAcnf,EAAOwb,cAC3Cxb,EAAOmY,KAAK,gBACZnY,EAAOmY,KAAK,YAEd2X,yBAA0B,WACnB9vB,IAAUA,EAAOmf,WAAcnf,EAAOwb,aAC3Cxb,EAAOmY,KAAK,0BAKpB7V,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KAEbqH,EAAIlB,iBAAiB,SAAUxD,EAAO4vB,OAAOC,eAG7CnrB,EAAIlB,iBAAiB,oBAAqBxD,EAAO4vB,OAAOE,2BAE1D/tB,QAAS,WACP,IAAI/B,EAAS3C,KACbqH,EAAIjB,oBAAoB,SAAUzD,EAAO4vB,OAAOC,eAChDnrB,EAAIjB,oBAAoB,oBAAqBzD,EAAO4vB,OAAOE,6BAK7DC,GAAW,CACbC,KAAMtrB,EAAIurB,kBAAoBvrB,EAAIwrB,uBAClCC,OAAQ,SAAgBvnB,EAAQhK,QACb,IAAZA,IAAqBA,EAAU,IAEpC,IAAIoB,EAAS3C,KAET+yB,EAAeL,GAASC,KACxBhe,EAAW,IAAIoe,GAAa,SAAUC,GAIxC,GAAyB,IAArBA,EAAUhvB,OAAd,CAIA,IAAIivB,EAAiB,WACnBtwB,EAAOmY,KAAK,iBAAkBkY,EAAU,KAGtC3rB,EAAI6rB,sBACN7rB,EAAI6rB,sBAAsBD,GAE1B5rB,EAAIU,WAAWkrB,EAAgB,QAV/BtwB,EAAOmY,KAAK,iBAAkBkY,EAAU,OAc5Cre,EAASwe,QAAQ5nB,EAAQ,CACvB6nB,WAA0C,qBAAvB7xB,EAAQ6xB,YAAoC7xB,EAAQ6xB,WACvEC,UAAwC,qBAAtB9xB,EAAQ8xB,WAAmC9xB,EAAQ8xB,UACrEC,cAAgD,qBAA1B/xB,EAAQ+xB,eAAuC/xB,EAAQ+xB,gBAG/E3wB,EAAOgS,SAAS4e,UAAUzqB,KAAK6L,IAEjCyb,KAAM,WACJ,IAAIztB,EAAS3C,KACb,GAAK6T,EAAQc,UAAahS,EAAOgQ,OAAOgC,SAAxC,CACA,GAAIhS,EAAOgQ,OAAO6gB,eAEhB,IADA,IAAIC,EAAmB9wB,EAAOkC,IAAI+G,UACzB3L,EAAI,EAAGA,EAAIwzB,EAAiBzvB,OAAQ/D,GAAK,EAChD0C,EAAOgS,SAASme,OAAOW,EAAiBxzB,IAI5C0C,EAAOgS,SAASme,OAAOnwB,EAAOkC,IAAI,GAAI,CAAEwuB,UAAW1wB,EAAOgQ,OAAO+gB,uBAGjE/wB,EAAOgS,SAASme,OAAOnwB,EAAO2T,WAAW,GAAI,CAAE8c,YAAY,MAE7D1uB,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOgS,SAAS4e,UAAUvuB,SAAQ,SAAU2P,GAC1CA,EAASgf,gBAEXhxB,EAAOgS,SAAS4e,UAAY,KAI5BK,GAAa,CACf1wB,KAAM,WACNyP,OAAQ,CACNgC,UAAU,EACV6e,gBAAgB,EAChBE,sBAAsB,GAExB5T,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBgS,SAAU,CACRyb,KAAMsC,GAAStC,KAAKxQ,KAAKjd,GACzBmwB,OAAQJ,GAASI,OAAOlT,KAAKjd,GAC7B+B,QAASguB,GAAShuB,QAAQkb,KAAKjd,GAC/B4wB,UAAW,OAIjBtuB,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACb2C,EAAOgS,SAASyb,QAElB1rB,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOgS,SAASjQ,aAKlBmvB,GAAU,CACZtwB,OAAQ,SAAgBuwB,GACtB,IAAInxB,EAAS3C,KACT+zB,EAAMpxB,EAAOgQ,OACb6F,EAAgBub,EAAIvb,cACpBU,EAAiB6a,EAAI7a,eACrBc,EAAiB+Z,EAAI/Z,eACrBga,EAAQrxB,EAAOgQ,OAAOiE,QACtBqd,EAAkBD,EAAMC,gBACxBC,EAAiBF,EAAME,eACvBC,EAAQxxB,EAAOiU,QACfwd,EAAeD,EAAME,KACrBC,EAAaH,EAAM7gB,GACnByD,EAASod,EAAMpd,OACfwd,EAAqBJ,EAAMjd,WAC3Bsd,EAAcL,EAAMK,YACpBC,EAAiBN,EAAMrmB,OAC3BnL,EAAOkb,oBACP,IAEI6W,EAIAC,EACAC,EAPAnZ,EAAc9Y,EAAO8Y,aAAe,EAGbiZ,EAAvB/xB,EAAO8T,aAA6B,QACpB9T,EAAOsT,eAAiB,OAAS,MAIjD+D,GACF2a,EAActc,KAAKC,MAAME,EAAgB,GAAKU,EAAiB+a,EAC/DW,EAAevc,KAAKC,MAAME,EAAgB,GAAKU,EAAiBgb,IAEhES,EAAcnc,GAAiBU,EAAiB,GAAK+a,EACrDW,EAAe1b,EAAiBgb,GAElC,IAAIG,EAAOhc,KAAKK,KAAK+C,GAAe,GAAKmZ,EAAc,GACnDthB,EAAK+E,KAAKkU,KAAK9Q,GAAe,GAAKkZ,EAAa5d,EAAO/S,OAAS,GAChE8J,GAAUnL,EAAOuU,WAAWmd,IAAS,IAAM1xB,EAAOuU,WAAW,IAAM,GASvE,SAAS2d,IACPlyB,EAAO0T,eACP1T,EAAO8Z,iBACP9Z,EAAOqa,sBACHra,EAAOmyB,MAAQnyB,EAAOgQ,OAAOmiB,KAAKje,SACpClU,EAAOmyB,KAAKC,OAIhB,GAhBA7jB,EAAMkC,OAAOzQ,EAAOiU,QAAS,CAC3Byd,KAAMA,EACN/gB,GAAIA,EACJxF,OAAQA,EACRoJ,WAAYvU,EAAOuU,aAYjBkd,IAAiBC,GAAQC,IAAehhB,IAAOwgB,EAKjD,OAJInxB,EAAOuU,aAAeqd,GAAsBzmB,IAAW2mB,GACzD9xB,EAAOoU,OAAOtI,IAAIimB,EAAa5mB,EAAS,WAE1CnL,EAAO8Z,iBAGT,GAAI9Z,EAAOgQ,OAAOiE,QAAQoe,eAcxB,OAbAryB,EAAOgQ,OAAOiE,QAAQoe,eAAe30B,KAAKsC,EAAQ,CAChDmL,OAAQA,EACRumB,KAAMA,EACN/gB,GAAIA,EACJyD,OAAS,WAEP,IADA,IAAIke,EAAiB,GACZh1B,EAAIo0B,EAAMp0B,GAAKqT,EAAIrT,GAAK,EAC/Bg1B,EAAensB,KAAKiO,EAAO9W,IAE7B,OAAOg1B,EALD,UAQVJ,IAGF,IAAIK,EAAiB,GACjBC,EAAgB,GACpB,GAAIrB,EACFnxB,EAAO2T,WAAW9F,KAAM,IAAO7N,EAAOgQ,OAAiB,YAAIlJ,cAE3D,IAAK,IAAIxJ,EAAIm0B,EAAcn0B,GAAKq0B,EAAYr0B,GAAK,GAC3CA,EAAIo0B,GAAQp0B,EAAIqT,IAClB3Q,EAAO2T,WAAW9F,KAAM,IAAO7N,EAAOgQ,OAAiB,WAAI,6BAAgC1S,EAAI,MAAQwJ,SAI7G,IAAK,IAAI2Q,EAAM,EAAGA,EAAMrD,EAAO/S,OAAQoW,GAAO,EACxCA,GAAOia,GAAQja,GAAO9G,IACE,qBAAfghB,GAA8BR,EACvCqB,EAAcrsB,KAAKsR,IAEfA,EAAMka,GAAca,EAAcrsB,KAAKsR,GACvCA,EAAMga,GAAgBc,EAAepsB,KAAKsR,KAIpD+a,EAAcnwB,SAAQ,SAAUkK,GAC9BvM,EAAO2T,WAAW/G,OAAOilB,EAAYzd,EAAO7H,GAAQA,OAEtDgmB,EAAe5G,MAAK,SAAUjtB,EAAGktB,GAAK,OAAOA,EAAIltB,KAAM2D,SAAQ,SAAUkK,GACvEvM,EAAO2T,WAAW1G,QAAQ4kB,EAAYzd,EAAO7H,GAAQA,OAEvDvM,EAAO2T,WAAWxP,SAAS,iBAAiB2H,IAAIimB,EAAa5mB,EAAS,MACtE+mB,KAEFL,YAAa,SAAqB1b,EAAO5J,GACvC,IAAIvM,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOiE,QAC3B,GAAIjE,EAAOyiB,OAASzyB,EAAOiU,QAAQwe,MAAMlmB,GACvC,OAAOvM,EAAOiU,QAAQwe,MAAMlmB,GAE9B,IAAImmB,EAAW1iB,EAAO6hB,YAClBpsB,EAAEuK,EAAO6hB,YAAYn0B,KAAKsC,EAAQmW,EAAO5J,IACzC9G,EAAG,eAAmBzF,EAAOgQ,OAAiB,WAAI,8BAAkCzD,EAAQ,KAAQ4J,EAAQ,UAGhH,OAFKuc,EAASvrB,KAAK,4BAA8BurB,EAASvrB,KAAK,0BAA2BoF,GACtFyD,EAAOyiB,QAASzyB,EAAOiU,QAAQwe,MAAMlmB,GAASmmB,GAC3CA,GAET/Q,YAAa,SAAqBvN,GAChC,IAAIpU,EAAS3C,KACb,GAAsB,kBAAX+W,GAAuB,WAAYA,EAC5C,IAAK,IAAI9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAClC8W,EAAO9W,IAAM0C,EAAOiU,QAAQG,OAAOjO,KAAKiO,EAAO9W,SAGrD0C,EAAOiU,QAAQG,OAAOjO,KAAKiO,GAE7BpU,EAAOiU,QAAQrT,QAAO,IAExBghB,aAAc,SAAsBxN,GAClC,IAAIpU,EAAS3C,KACTyb,EAAc9Y,EAAO8Y,YACrBqC,EAAiBrC,EAAc,EAC/B6Z,EAAoB,EAExB,GAAInwB,MAAM6Z,QAAQjI,GAAS,CACzB,IAAK,IAAI9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAClC8W,EAAO9W,IAAM0C,EAAOiU,QAAQG,OAAOrL,QAAQqL,EAAO9W,IAExD6d,EAAiBrC,EAAc1E,EAAO/S,OACtCsxB,EAAoBve,EAAO/S,YAE3BrB,EAAOiU,QAAQG,OAAOrL,QAAQqL,GAEhC,GAAIpU,EAAOgQ,OAAOiE,QAAQwe,MAAO,CAC/B,IAAIA,EAAQzyB,EAAOiU,QAAQwe,MACvBG,EAAW,GACf70B,OAAOsQ,KAAKokB,GAAOpwB,SAAQ,SAAUwwB,GACnCD,EAASpf,SAASqf,EAAa,IAAMF,GAAqBF,EAAMI,MAElE7yB,EAAOiU,QAAQwe,MAAQG,EAEzB5yB,EAAOiU,QAAQrT,QAAO,GACtBZ,EAAO2e,QAAQxD,EAAgB,IAEjC+G,YAAa,SAAqBC,GAChC,IAAIniB,EAAS3C,KACb,GAA6B,qBAAlB8kB,GAAmD,OAAlBA,EAA5C,CACA,IAAIrJ,EAAc9Y,EAAO8Y,YACzB,GAAItW,MAAM6Z,QAAQ8F,GAChB,IAAK,IAAI7kB,EAAI6kB,EAAc9gB,OAAS,EAAG/D,GAAK,EAAGA,GAAK,EAClD0C,EAAOiU,QAAQG,OAAOtK,OAAOqY,EAAc7kB,GAAI,GAC3C0C,EAAOgQ,OAAOiE,QAAQwe,cACjBzyB,EAAOiU,QAAQwe,MAAMtQ,EAAc7kB,IAExC6kB,EAAc7kB,GAAKwb,IAAeA,GAAe,GACrDA,EAAcpD,KAAKK,IAAI+C,EAAa,QAGtC9Y,EAAOiU,QAAQG,OAAOtK,OAAOqY,EAAe,GACxCniB,EAAOgQ,OAAOiE,QAAQwe,cACjBzyB,EAAOiU,QAAQwe,MAAMtQ,GAE1BA,EAAgBrJ,IAAeA,GAAe,GAClDA,EAAcpD,KAAKK,IAAI+C,EAAa,GAEtC9Y,EAAOiU,QAAQrT,QAAO,GACtBZ,EAAO2e,QAAQ7F,EAAa,KAE9BuJ,gBAAiB,WACf,IAAIriB,EAAS3C,KACb2C,EAAOiU,QAAQG,OAAS,GACpBpU,EAAOgQ,OAAOiE,QAAQwe,QACxBzyB,EAAOiU,QAAQwe,MAAQ,IAEzBzyB,EAAOiU,QAAQrT,QAAO,GACtBZ,EAAO2e,QAAQ,EAAG,KAIlBmU,GAAY,CACdvyB,KAAM,UACNyP,OAAQ,CACNiE,QAAS,CACPC,SAAS,EACTE,OAAQ,GACRqe,OAAO,EACPZ,YAAa,KACbQ,eAAgB,KAChBf,gBAAiB,EACjBC,eAAgB,IAGpBpU,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBiU,QAAS,CACPrT,OAAQswB,GAAQtwB,OAAOqc,KAAKjd,GAC5B2hB,YAAauP,GAAQvP,YAAY1E,KAAKjd,GACtC4hB,aAAcsP,GAAQtP,aAAa3E,KAAKjd,GACxCkiB,YAAagP,GAAQhP,YAAYjF,KAAKjd,GACtCqiB,gBAAiB6O,GAAQ7O,gBAAgBpF,KAAKjd,GAC9C6xB,YAAaX,GAAQW,YAAY5U,KAAKjd,GACtCoU,OAAQpU,EAAOgQ,OAAOiE,QAAQG,OAC9Bqe,MAAO,OAIbnwB,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOiE,QAAQC,QAA3B,CACAlU,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,WACjE,IAAIgjB,EAAkB,CACpB1a,qBAAqB,GAEvB/J,EAAMkC,OAAOzQ,EAAOgQ,OAAQgjB,GAC5BzkB,EAAMkC,OAAOzQ,EAAOqrB,eAAgB2H,GAE/BhzB,EAAOgQ,OAAO+O,cACjB/e,EAAOiU,QAAQrT,WAGnBgd,aAAc,WACZ,IAAI5d,EAAS3C,KACR2C,EAAOgQ,OAAOiE,QAAQC,SAC3BlU,EAAOiU,QAAQrT,YAKjBqyB,GAAW,CACbC,OAAQ,SAAgBzpB,GACtB,IAAIzJ,EAAS3C,KACTwW,EAAM7T,EAAO8T,aACb7W,EAAIwM,EACJxM,EAAE6mB,gBAAiB7mB,EAAIA,EAAE6mB,eAC7B,IAAIqP,EAAKl2B,EAAEm2B,SAAWn2B,EAAEo2B,SAExB,IAAKrzB,EAAOgf,iBAAoBhf,EAAOsT,gBAAyB,KAAP6f,GAAenzB,EAAOuT,cAAuB,KAAP4f,GAAqB,KAAPA,GAC3G,OAAO,EAET,IAAKnzB,EAAOif,iBAAoBjf,EAAOsT,gBAAyB,KAAP6f,GAAenzB,EAAOuT,cAAuB,KAAP4f,GAAqB,KAAPA,GAC3G,OAAO,EAET,KAAIl2B,EAAEq2B,UAAYr2B,EAAEs2B,QAAUt2B,EAAEu2B,SAAWv2B,EAAEw2B,YAGzCpwB,EAAIK,gBAAiBL,EAAIK,cAAcE,UAA0D,UAA7CP,EAAIK,cAAcE,SAASjB,eAA0E,aAA7CU,EAAIK,cAAcE,SAASjB,eAA3I,CAGA,GAAI3C,EAAOgQ,OAAO0jB,SAASC,iBAA0B,KAAPR,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAY,CACzH,IAAIS,GAAS,EAEb,GAAI5zB,EAAOkC,IAAI+G,QAAS,IAAOjJ,EAAOgQ,OAAiB,YAAI3O,OAAS,GAA6E,IAAxErB,EAAOkC,IAAI+G,QAAS,IAAOjJ,EAAOgQ,OAAuB,kBAAI3O,OACpI,OAEF,IAAIwyB,EAAcnvB,EAAIonB,WAClBgI,EAAepvB,EAAIqvB,YACnBC,EAAeh0B,EAAOkC,IAAIiJ,SAC1B0I,IAAOmgB,EAAanoB,MAAQ7L,EAAOkC,IAAI,GAAGwJ,YAM9C,IALA,IAAIuoB,EAAc,CAChB,CAACD,EAAanoB,KAAMmoB,EAAapoB,KACjC,CAACooB,EAAanoB,KAAO7L,EAAOkT,MAAO8gB,EAAapoB,KAChD,CAACooB,EAAanoB,KAAMmoB,EAAapoB,IAAM5L,EAAOmT,QAC9C,CAAC6gB,EAAanoB,KAAO7L,EAAOkT,MAAO8gB,EAAapoB,IAAM5L,EAAOmT,SACtD7V,EAAI,EAAGA,EAAI22B,EAAY5yB,OAAQ/D,GAAK,EAAG,CAC9C,IAAIouB,EAAQuI,EAAY32B,GAEtBouB,EAAM,IAAM,GAAKA,EAAM,IAAMmI,GAC1BnI,EAAM,IAAM,GAAKA,EAAM,IAAMoI,IAEhCF,GAAS,GAGb,IAAKA,EAAU,OAEb5zB,EAAOsT,gBACE,KAAP6f,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,IACrCl2B,EAAEyoB,eAAkBzoB,EAAEyoB,iBACnBzoB,EAAEi3B,aAAc,IAEZ,KAAPf,GAAoB,KAAPA,GAAetf,KAAiB,KAAPsf,GAAoB,KAAPA,IAActf,IAAQ7T,EAAOuf,aACzE,KAAP4T,GAAoB,KAAPA,GAAetf,KAAiB,KAAPsf,GAAoB,KAAPA,IAActf,IAAQ7T,EAAO0f,cAE3E,KAAPyT,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,IACrCl2B,EAAEyoB,eAAkBzoB,EAAEyoB,iBACnBzoB,EAAEi3B,aAAc,GAEd,KAAPf,GAAoB,KAAPA,GAAanzB,EAAOuf,YAC1B,KAAP4T,GAAoB,KAAPA,GAAanzB,EAAO0f,aAEvC1f,EAAOmY,KAAK,WAAYgb,KAG1BgB,OAAQ,WACN,IAAIn0B,EAAS3C,KACT2C,EAAO0zB,SAASxf,UACpBzO,EAAEpC,GAAKf,GAAG,UAAWtC,EAAO0zB,SAASR,QACrClzB,EAAO0zB,SAASxf,SAAU,IAE5BkgB,QAAS,WACP,IAAIp0B,EAAS3C,KACR2C,EAAO0zB,SAASxf,UACrBzO,EAAEpC,GAAKsG,IAAI,UAAW3J,EAAO0zB,SAASR,QACtClzB,EAAO0zB,SAASxf,SAAU,KAI1BmgB,GAAa,CACf9zB,KAAM,WACNyP,OAAQ,CACN0jB,SAAU,CACRxf,SAAS,EACTyf,gBAAgB,IAGpBxW,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB0zB,SAAU,CACRxf,SAAS,EACTigB,OAAQlB,GAASkB,OAAOlX,KAAKjd,GAC7Bo0B,QAASnB,GAASmB,QAAQnX,KAAKjd,GAC/BkzB,OAAQD,GAASC,OAAOjW,KAAKjd,OAInCsC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAO0jB,SAASxf,SACzBlU,EAAO0zB,SAASS,UAGpBpyB,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAO0zB,SAASxf,SAClBlU,EAAO0zB,SAASU,aAMxB,SAASE,KACP,IAAIxhB,EAAY,UACZyhB,EAAczhB,KAAazP,EAE/B,IAAKkxB,EAAa,CAChB,IAAIC,EAAUnxB,EAAIa,cAAc,OAChCswB,EAAQlwB,aAAawO,EAAW,WAChCyhB,EAA4C,oBAAvBC,EAAQ1hB,GAc/B,OAXKyhB,GACAlxB,EAAIoxB,gBACJpxB,EAAIoxB,eAAeC,aAGuB,IAA1CrxB,EAAIoxB,eAAeC,WAAW,GAAI,MAGrCH,EAAclxB,EAAIoxB,eAAeC,WAAW,eAAgB,QAGvDH,EAET,IAAII,GAAa,CACfC,eAAgBrmB,EAAMM,MACtBpF,MAAQ,WACN,OAAI/E,EAAIC,UAAUC,UAAUoB,QAAQ,YAAc,EAAY,iBACvDsuB,KAAqB,QAAU,aAFjC,GAIP3U,UAAW,SAAmB1iB,GAE5B,IAAI43B,EAAa,GACbC,EAAc,GACdC,EAAc,IAEdC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAkDT,MA/CI,WAAYl4B,IACdg4B,EAAKh4B,EAAEiN,QAEL,eAAgBjN,IAClBg4B,GAAMh4B,EAAEm4B,WAAa,KAEnB,gBAAiBn4B,IACnBg4B,GAAMh4B,EAAEo4B,YAAc,KAEpB,gBAAiBp4B,IACnB+3B,GAAM/3B,EAAEq4B,YAAc,KAIpB,SAAUr4B,GAAKA,EAAE8R,OAAS9R,EAAEs4B,kBAC9BP,EAAKC,EACLA,EAAK,GAGPC,EAAKF,EAAKH,EACVM,EAAKF,EAAKJ,EAEN,WAAY53B,IACdk4B,EAAKl4B,EAAEu4B,QAEL,WAAYv4B,IACdi4B,EAAKj4B,EAAEw4B,SAGJP,GAAMC,IAAOl4B,EAAEy4B,YACE,IAAhBz4B,EAAEy4B,WACJR,GAAMJ,EACNK,GAAML,IAENI,GAAMH,EACNI,GAAMJ,IAKNG,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAEnBC,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAGhB,CACLQ,MAAOX,EACPY,MAAOX,EACPY,OAAQX,EACRY,OAAQX,IAGZY,iBAAkB,WAChB,IAAI/1B,EAAS3C,KACb2C,EAAOg2B,cAAe,GAExBC,iBAAkB,WAChB,IAAIj2B,EAAS3C,KACb2C,EAAOg2B,cAAe,GAExB9C,OAAQ,SAAgBzpB,GACtB,IAAIxM,EAAIwM,EACJzJ,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOkmB,WAE3B,IAAKl2B,EAAOg2B,eAAiBhmB,EAAOmmB,eAAkB,OAAO,EAEzDl5B,EAAE6mB,gBAAiB7mB,EAAIA,EAAE6mB,eAC7B,IAAIsS,EAAQ,EACRC,EAAYr2B,EAAO8T,cAAgB,EAAI,EAEvCrT,EAAOk0B,GAAWhV,UAAU1iB,GAEhC,GAAI+S,EAAOsmB,YACT,GAAIt2B,EAAOsT,eAAgB,CACzB,KAAIoC,KAAK4B,IAAI7W,EAAKo1B,QAAUngB,KAAK4B,IAAI7W,EAAKq1B,SACnC,OAAO,EADuCM,EAAQ31B,EAAKo1B,OAASQ,MAEtE,MAAI3gB,KAAK4B,IAAI7W,EAAKq1B,QAAUpgB,KAAK4B,IAAI7W,EAAKo1B,SAC1C,OAAO,EAD8CO,EAAQ31B,EAAKq1B,YAGzEM,EAAQ1gB,KAAK4B,IAAI7W,EAAKo1B,QAAUngB,KAAK4B,IAAI7W,EAAKq1B,SAAWr1B,EAAKo1B,OAASQ,GAAa51B,EAAKq1B,OAG3F,GAAc,IAAVM,EAAe,OAAO,EAI1B,GAFIpmB,EAAOumB,SAAUH,GAASA,GAEzBp2B,EAAOgQ,OAAOoX,SAaZ,CAEDpnB,EAAOgQ,OAAOyK,MAChBza,EAAOwf,UAET,IAAI8H,EAAWtnB,EAAO8O,eAAkBsnB,EAAQpmB,EAAOwmB,YACnDrc,EAAena,EAAOia,YACtBG,EAASpa,EAAOka,MA2BpB,GAzBIoN,GAAYtnB,EAAOyZ,iBAAkB6N,EAAWtnB,EAAOyZ,gBACvD6N,GAAYtnB,EAAOga,iBAAkBsN,EAAWtnB,EAAOga,gBAE3Dha,EAAO6Y,cAAc,GACrB7Y,EAAO4d,aAAa0J,GACpBtnB,EAAO8Z,iBACP9Z,EAAOkb,oBACPlb,EAAOqa,wBAEDF,GAAgBna,EAAOia,cAAkBG,GAAUpa,EAAOka,QAC9Dla,EAAOqa,sBAGLra,EAAOgQ,OAAOgZ,iBAChB3jB,aAAarF,EAAOk2B,WAAWO,SAC/Bz2B,EAAOk2B,WAAWO,QAAUloB,EAAMI,UAAS,WACzC3O,EAAOkgB,mBACN,MAGLlgB,EAAOmY,KAAK,SAAUlb,GAGlB+C,EAAOgQ,OAAO6Z,UAAY7pB,EAAOgQ,OAAO0mB,8BAAgC12B,EAAO6pB,SAAS8M,OAExFrP,IAAatnB,EAAOyZ,gBAAkB6N,IAAatnB,EAAOga,eAAkB,OAAO,MA/C5D,CAC3B,GAAIzL,EAAMM,MAAQ7O,EAAOk2B,WAAWtB,eAAiB,GACnD,GAAIwB,EAAQ,EACV,GAAMp2B,EAAOka,QAASla,EAAOgQ,OAAOyK,MAAUza,EAAOye,WAG9C,GAAIzO,EAAOmmB,eAAkB,OAAO,OAFzCn2B,EAAOuf,YACPvf,EAAOmY,KAAK,SAAUlb,QAEnB,GAAM+C,EAAOia,cAAeja,EAAOgQ,OAAOyK,MAAUza,EAAOye,WAG3D,GAAIzO,EAAOmmB,eAAkB,OAAO,OAFzCn2B,EAAO0f,YACP1f,EAAOmY,KAAK,SAAUlb,GAG1B+C,EAAOk2B,WAAWtB,gBAAiB,IAAKlwB,EAAIQ,MAAQ0xB,UAwCtD,OAFI35B,EAAEyoB,eAAkBzoB,EAAEyoB,iBACnBzoB,EAAEi3B,aAAc,GAChB,GAETC,OAAQ,WACN,IAAIn0B,EAAS3C,KACb,IAAKs3B,GAAWlrB,MAAS,OAAO,EAChC,GAAIzJ,EAAOk2B,WAAWhiB,QAAW,OAAO,EACxC,IAAItL,EAAS5I,EAAOkC,IAQpB,MAP8C,cAA1ClC,EAAOgQ,OAAOkmB,WAAWW,eAC3BjuB,EAASnD,EAAEzF,EAAOgQ,OAAOkmB,WAAWW,eAEtCjuB,EAAOtG,GAAG,aAActC,EAAOk2B,WAAWH,kBAC1CntB,EAAOtG,GAAG,aAActC,EAAOk2B,WAAWD,kBAC1CrtB,EAAOtG,GAAGqyB,GAAWlrB,MAAOzJ,EAAOk2B,WAAWhD,QAC9ClzB,EAAOk2B,WAAWhiB,SAAU,GACrB,GAETkgB,QAAS,WACP,IAAIp0B,EAAS3C,KACb,IAAKs3B,GAAWlrB,MAAS,OAAO,EAChC,IAAKzJ,EAAOk2B,WAAWhiB,QAAW,OAAO,EACzC,IAAItL,EAAS5I,EAAOkC,IAMpB,MAL8C,cAA1ClC,EAAOgQ,OAAOkmB,WAAWW,eAC3BjuB,EAASnD,EAAEzF,EAAOgQ,OAAOkmB,WAAWW,eAEtCjuB,EAAOe,IAAIgrB,GAAWlrB,MAAOzJ,EAAOk2B,WAAWhD,QAC/ClzB,EAAOk2B,WAAWhiB,SAAU,GACrB,IAIP4iB,GAAe,CACjBv2B,KAAM,aACNyP,OAAQ,CACNkmB,WAAY,CACVhiB,SAAS,EACTiiB,gBAAgB,EAChBI,QAAQ,EACRD,aAAa,EACbE,YAAa,EACbK,aAAc,cAGlB1Z,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBk2B,WAAY,CACVhiB,SAAS,EACTigB,OAAQQ,GAAWR,OAAOlX,KAAKjd,GAC/Bo0B,QAASO,GAAWP,QAAQnX,KAAKjd,GACjCkzB,OAAQyB,GAAWzB,OAAOjW,KAAKjd,GAC/B+1B,iBAAkBpB,GAAWoB,iBAAiB9Y,KAAKjd,GACnDi2B,iBAAkBtB,GAAWsB,iBAAiBhZ,KAAKjd,GACnD40B,eAAgBrmB,EAAMM,UAI5BvM,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAOkmB,WAAWhiB,SAAWlU,EAAOk2B,WAAW/B,UAE5DpyB,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAOk2B,WAAWhiB,SAAWlU,EAAOk2B,WAAW9B,aAKrD2C,GAAa,CACfn2B,OAAQ,WAEN,IAAIZ,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOhO,WAE3B,IAAIhC,EAAOgQ,OAAOyK,KAAlB,CACA,IAAI2W,EAAMpxB,EAAOgC,WACbg1B,EAAU5F,EAAI4F,QACdC,EAAU7F,EAAI6F,QAEdA,GAAWA,EAAQ51B,OAAS,IAC1BrB,EAAOia,YACTgd,EAAQxwB,SAASuJ,EAAOknB,eAExBD,EAAQpwB,YAAYmJ,EAAOknB,eAE7BD,EAAQj3B,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,SAAW,WAAa,eAAevR,EAAOmnB,YAE1FH,GAAWA,EAAQ31B,OAAS,IAC1BrB,EAAOka,MACT8c,EAAQvwB,SAASuJ,EAAOknB,eAExBF,EAAQnwB,YAAYmJ,EAAOknB,eAE7BF,EAAQh3B,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,SAAW,WAAa,eAAevR,EAAOmnB,cAGhGC,YAAa,SAAqBn6B,GAChC,IAAI+C,EAAS3C,KACbJ,EAAEyoB,iBACE1lB,EAAOia,cAAgBja,EAAOgQ,OAAOyK,MACzCza,EAAO0f,aAET2X,YAAa,SAAqBp6B,GAChC,IAAI+C,EAAS3C,KACbJ,EAAEyoB,iBACE1lB,EAAOka,QAAUla,EAAOgQ,OAAOyK,MACnCza,EAAOuf,aAETkO,KAAM,WACJ,IAIIuJ,EACAC,EALAj3B,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOhO,YACrBgO,EAAOsnB,QAAUtnB,EAAOunB,UAI1BvnB,EAAOsnB,SACTN,EAAUvxB,EAAEuK,EAAOsnB,QAEjBt3B,EAAOgQ,OAAO0d,mBACc,kBAAlB1d,EAAOsnB,QACdN,EAAQ31B,OAAS,GACyB,IAA1CrB,EAAOkC,IAAI2L,KAAKmC,EAAOsnB,QAAQj2B,SAElC21B,EAAUh3B,EAAOkC,IAAI2L,KAAKmC,EAAOsnB,UAGjCtnB,EAAOunB,SACTN,EAAUxxB,EAAEuK,EAAOunB,QAEjBv3B,EAAOgQ,OAAO0d,mBACc,kBAAlB1d,EAAOunB,QACdN,EAAQ51B,OAAS,GACyB,IAA1CrB,EAAOkC,IAAI2L,KAAKmC,EAAOunB,QAAQl2B,SAElC41B,EAAUj3B,EAAOkC,IAAI2L,KAAKmC,EAAOunB,UAIjCP,GAAWA,EAAQ31B,OAAS,GAC9B21B,EAAQ10B,GAAG,QAAStC,EAAOgC,WAAWq1B,aAEpCJ,GAAWA,EAAQ51B,OAAS,GAC9B41B,EAAQ30B,GAAG,QAAStC,EAAOgC,WAAWo1B,aAGxC7oB,EAAMkC,OAAOzQ,EAAOgC,WAAY,CAC9Bg1B,QAASA,EACTM,OAAQN,GAAWA,EAAQ,GAC3BC,QAASA,EACTM,OAAQN,GAAWA,EAAQ,OAG/Bl1B,QAAS,WACP,IAAI/B,EAAS3C,KACT+zB,EAAMpxB,EAAOgC,WACbg1B,EAAU5F,EAAI4F,QACdC,EAAU7F,EAAI6F,QACdD,GAAWA,EAAQ31B,SACrB21B,EAAQrtB,IAAI,QAAS3J,EAAOgC,WAAWq1B,aACvCL,EAAQnwB,YAAY7G,EAAOgQ,OAAOhO,WAAWk1B,gBAE3CD,GAAWA,EAAQ51B,SACrB41B,EAAQttB,IAAI,QAAS3J,EAAOgC,WAAWo1B,aACvCH,EAAQpwB,YAAY7G,EAAOgQ,OAAOhO,WAAWk1B,kBAK/CM,GAAe,CACjBj3B,KAAM,aACNyP,OAAQ,CACNhO,WAAY,CACVs1B,OAAQ,KACRC,OAAQ,KAERE,aAAa,EACbP,cAAe,yBACfQ,YAAa,uBACbP,UAAW,uBAGfha,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBgC,WAAY,CACVyrB,KAAMsJ,GAAWtJ,KAAKxQ,KAAKjd,GAC3BY,OAAQm2B,GAAWn2B,OAAOqc,KAAKjd,GAC/B+B,QAASg1B,GAAWh1B,QAAQkb,KAAKjd,GACjCq3B,YAAaN,GAAWM,YAAYpa,KAAKjd,GACzCo3B,YAAaL,GAAWK,YAAYna,KAAKjd,OAI/CsC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACb2C,EAAOgC,WAAWyrB,OAClBztB,EAAOgC,WAAWpB,UAEpB+2B,OAAQ,WACN,IAAI33B,EAAS3C,KACb2C,EAAOgC,WAAWpB,UAEpBg3B,SAAU,WACR,IAAI53B,EAAS3C,KACb2C,EAAOgC,WAAWpB,UAEpBmB,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOgC,WAAWD,WAEpB81B,MAAO,SAAe56B,GACpB,IASM66B,EATF93B,EAAS3C,KACT+zB,EAAMpxB,EAAOgC,WACbg1B,EAAU5F,EAAI4F,QACdC,EAAU7F,EAAI6F,SAEhBj3B,EAAOgQ,OAAOhO,WAAWy1B,aACrBhyB,EAAExI,EAAE2L,QAAQI,GAAGiuB,IACfxxB,EAAExI,EAAE2L,QAAQI,GAAGguB,KAGfA,EACFc,EAAWd,EAAQjwB,SAAS/G,EAAOgQ,OAAOhO,WAAW01B,aAC5CT,IACTa,EAAWb,EAAQlwB,SAAS/G,EAAOgQ,OAAOhO,WAAW01B,eAEtC,IAAbI,EACF93B,EAAOmY,KAAK,iBAAkBnY,GAE9BA,EAAOmY,KAAK,iBAAkBnY,GAE5Bg3B,GACFA,EAAQ/vB,YAAYjH,EAAOgQ,OAAOhO,WAAW01B,aAE3CT,GACFA,EAAQhwB,YAAYjH,EAAOgQ,OAAOhO,WAAW01B,iBAOnDK,GAAa,CACfn3B,OAAQ,WAEN,IAAIZ,EAAS3C,KACTwW,EAAM7T,EAAO6T,IACb7D,EAAShQ,EAAOgQ,OAAO/N,WAC3B,GAAK+N,EAAOrI,IAAO3H,EAAOiC,WAAW0F,IAAO3H,EAAOiC,WAAWC,KAAwC,IAAjClC,EAAOiC,WAAWC,IAAIb,OAA3F,CACA,IAGI22B,EAHA3jB,EAAerU,EAAOiU,SAAWjU,EAAOgQ,OAAOiE,QAAQC,QAAUlU,EAAOiU,QAAQG,OAAO/S,OAASrB,EAAOoU,OAAO/S,OAC9Ga,EAAMlC,EAAOiC,WAAWC,IAGxB+1B,EAAQj4B,EAAOgQ,OAAOyK,KAAO/E,KAAKE,MAAMvB,EAAsC,EAAtBrU,EAAOsf,cAAqBtf,EAAOgQ,OAAOuG,gBAAkBvW,EAAOsU,SAASjT,OAcxI,GAbIrB,EAAOgQ,OAAOyK,MAChBud,EAAUtiB,KAAKE,MAAM5V,EAAO8Y,YAAc9Y,EAAOsf,cAAgBtf,EAAOgQ,OAAOuG,gBAC3EyhB,EAAU3jB,EAAe,EAA2B,EAAtBrU,EAAOsf,eACvC0Y,GAAY3jB,EAAsC,EAAtBrU,EAAOsf,cAEjC0Y,EAAUC,EAAQ,IAAKD,GAAWC,GAClCD,EAAU,GAAsC,YAAjCh4B,EAAOgQ,OAAOkoB,iBAAgCF,EAAUC,EAAQD,IAEnFA,EADqC,qBAArBh4B,EAAOkY,UACblY,EAAOkY,UAEPlY,EAAO8Y,aAAe,EAGd,YAAhB9I,EAAOzO,MAAsBvB,EAAOiC,WAAWk2B,SAAWn4B,EAAOiC,WAAWk2B,QAAQ92B,OAAS,EAAG,CAClG,IACI+2B,EACAC,EACAC,EAHAH,EAAUn4B,EAAOiC,WAAWk2B,QAoBhC,GAhBInoB,EAAOuoB,iBACTv4B,EAAOiC,WAAWu2B,WAAaL,EAAQzrB,GAAG,GAAG1M,EAAOsT,eAAiB,aAAe,gBAAe,GACnGpR,EAAI4J,IAAI9L,EAAOsT,eAAiB,QAAU,SAAYtT,EAAOiC,WAAWu2B,YAAcxoB,EAAOyoB,mBAAqB,GAAM,MACpHzoB,EAAOyoB,mBAAqB,QAA8BrvB,IAAzBpJ,EAAOob,gBAC1Cpb,EAAOiC,WAAWy2B,oBAAuBV,EAAUh4B,EAAOob,cACtDpb,EAAOiC,WAAWy2B,mBAAsB1oB,EAAOyoB,mBAAqB,EACtEz4B,EAAOiC,WAAWy2B,mBAAqB1oB,EAAOyoB,mBAAqB,EAC1Dz4B,EAAOiC,WAAWy2B,mBAAqB,IAChD14B,EAAOiC,WAAWy2B,mBAAqB,IAG3CN,EAAaJ,EAAUh4B,EAAOiC,WAAWy2B,mBACzCL,EAAYD,GAAc1iB,KAAKkU,IAAIuO,EAAQ92B,OAAQ2O,EAAOyoB,oBAAsB,GAChFH,GAAYD,EAAYD,GAAc,GAExCD,EAAQtxB,YAAcmJ,EAAwB,kBAAI,IAAOA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAC3P9N,EAAIb,OAAS,EACf82B,EAAQnsB,MAAK,SAAUO,EAAOosB,GAC5B,IAAIC,EAAUnzB,EAAEkzB,GACZE,EAAcD,EAAQrsB,QACtBssB,IAAgBb,GAClBY,EAAQnyB,SAASuJ,EAAO8oB,mBAEtB9oB,EAAOuoB,iBACLM,GAAeT,GAAcS,GAAeR,GAC9CO,EAAQnyB,SAAWuJ,EAAwB,kBAAI,SAE7C6oB,IAAgBT,GAClBQ,EACGrrB,OACA9G,SAAWuJ,EAAwB,kBAAI,SACvCzC,OACA9G,SAAWuJ,EAAwB,kBAAI,cAExC6oB,IAAgBR,GAClBO,EACGzrB,OACA1G,SAAWuJ,EAAwB,kBAAI,SACvC7C,OACA1G,SAAWuJ,EAAwB,kBAAI,sBAI3C,CACL,IAAI4oB,EAAUT,EAAQzrB,GAAGsrB,GAEzB,GADAY,EAAQnyB,SAASuJ,EAAO8oB,mBACpB9oB,EAAOuoB,eAAgB,CAGzB,IAFA,IAAIQ,EAAwBZ,EAAQzrB,GAAG0rB,GACnCY,EAAuBb,EAAQzrB,GAAG2rB,GAC7B/6B,EAAI86B,EAAY96B,GAAK+6B,EAAW/6B,GAAK,EAC5C66B,EAAQzrB,GAAGpP,GAAGmJ,SAAWuJ,EAAwB,kBAAI,SAEvD+oB,EACGxrB,OACA9G,SAAWuJ,EAAwB,kBAAI,SACvCzC,OACA9G,SAAWuJ,EAAwB,kBAAI,cAC1CgpB,EACG7rB,OACA1G,SAAWuJ,EAAwB,kBAAI,SACvC7C,OACA1G,SAAWuJ,EAAwB,kBAAI,eAG9C,GAAIA,EAAOuoB,eAAgB,CACzB,IAAIU,EAAuBvjB,KAAKkU,IAAIuO,EAAQ92B,OAAQ2O,EAAOyoB,mBAAqB,GAC5ES,GAAmBl5B,EAAOiC,WAAWu2B,WAAaS,EAAyBj5B,EAAOiC,WAAqB,YAAK,EAAMq2B,EAAWt4B,EAAOiC,WAAWu2B,WAC/IzG,EAAale,EAAM,QAAU,OACjCskB,EAAQrsB,IAAI9L,EAAOsT,eAAiBye,EAAa,MAAQmH,EAAgB,OAO7E,GAJoB,aAAhBlpB,EAAOzO,OACTW,EAAI2L,KAAM,IAAOmC,EAAmB,cAAI/D,KAAK+D,EAAOmpB,sBAAsBnB,EAAU,IACpF91B,EAAI2L,KAAM,IAAOmC,EAAiB,YAAI/D,KAAK+D,EAAOopB,oBAAoBnB,KAEpD,gBAAhBjoB,EAAOzO,KAAwB,CACjC,IAAI83B,EAEFA,EADErpB,EAAOspB,oBACct5B,EAAOsT,eAAiB,WAAa,aAErCtT,EAAOsT,eAAiB,aAAe,WAEhE,IAAIimB,GAASvB,EAAU,GAAKC,EACxBuB,EAAS,EACTC,EAAS,EACgB,eAAzBJ,EACFG,EAASD,EAETE,EAASF,EAEXr3B,EAAI2L,KAAM,IAAOmC,EAA2B,sBAAIlI,UAAW,6BAA+B0xB,EAAS,YAAcC,EAAS,KAAMxxB,WAAWjI,EAAOgQ,OAAO0I,OAEvI,WAAhB1I,EAAOzO,MAAqByO,EAAO0pB,cACrCx3B,EAAI4D,KAAKkK,EAAO0pB,aAAa15B,EAAQg4B,EAAU,EAAGC,IAClDj4B,EAAOmY,KAAK,mBAAoBnY,EAAQkC,EAAI,KAE5ClC,EAAOmY,KAAK,mBAAoBnY,EAAQkC,EAAI,IAE9CA,EAAIlC,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,SAAW,WAAa,eAAevR,EAAOmnB,aAE1Ft4B,OAAQ,WAEN,IAAImB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO/N,WAC3B,GAAK+N,EAAOrI,IAAO3H,EAAOiC,WAAW0F,IAAO3H,EAAOiC,WAAWC,KAAwC,IAAjClC,EAAOiC,WAAWC,IAAIb,OAA3F,CACA,IAAIgT,EAAerU,EAAOiU,SAAWjU,EAAOgQ,OAAOiE,QAAQC,QAAUlU,EAAOiU,QAAQG,OAAO/S,OAASrB,EAAOoU,OAAO/S,OAE9Ga,EAAMlC,EAAOiC,WAAWC,IACxBy3B,EAAiB,GACrB,GAAoB,YAAhB3pB,EAAOzO,KAAoB,CAE7B,IADA,IAAIq4B,EAAkB55B,EAAOgQ,OAAOyK,KAAO/E,KAAKE,MAAMvB,EAAsC,EAAtBrU,EAAOsf,cAAqBtf,EAAOgQ,OAAOuG,gBAAkBvW,EAAOsU,SAASjT,OACzI/D,EAAI,EAAGA,EAAIs8B,EAAiBt8B,GAAK,EACpC0S,EAAO6pB,aACTF,GAAkB3pB,EAAO6pB,aAAan8B,KAAKsC,EAAQ1C,EAAG0S,EAAO8pB,aAE7DH,GAAkB,IAAO3pB,EAAoB,cAAI,WAAeA,EAAkB,YAAI,OAAWA,EAAoB,cAAI,IAG7H9N,EAAI4D,KAAK6zB,GACT35B,EAAOiC,WAAWk2B,QAAUj2B,EAAI2L,KAAM,IAAOmC,EAAkB,aAE7C,aAAhBA,EAAOzO,OAEPo4B,EADE3pB,EAAO+pB,eACQ/pB,EAAO+pB,eAAer8B,KAAKsC,EAAQgQ,EAAOgqB,aAAchqB,EAAOiqB,YAE/D,gBAAoBjqB,EAAmB,aAAI,4BAEtCA,EAAiB,WAAI,YAE7C9N,EAAI4D,KAAK6zB,IAES,gBAAhB3pB,EAAOzO,OAEPo4B,EADE3pB,EAAOkqB,kBACQlqB,EAAOkqB,kBAAkBx8B,KAAKsC,EAAQgQ,EAAOmqB,sBAE7C,gBAAoBnqB,EAA2B,qBAAI,YAEtE9N,EAAI4D,KAAK6zB,IAES,WAAhB3pB,EAAOzO,MACTvB,EAAOmY,KAAK,mBAAoBnY,EAAOiC,WAAWC,IAAI,MAG1DurB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO/N,WAC3B,GAAK+N,EAAOrI,GAAZ,CAEA,IAAIzF,EAAMuD,EAAEuK,EAAOrI,IACA,IAAfzF,EAAIb,SAGNrB,EAAOgQ,OAAO0d,mBACU,kBAAd1d,EAAOrI,IACdzF,EAAIb,OAAS,GACyB,IAAtCrB,EAAOkC,IAAI2L,KAAKmC,EAAOrI,IAAItG,SAE9Ba,EAAMlC,EAAOkC,IAAI2L,KAAKmC,EAAOrI,KAGX,YAAhBqI,EAAOzO,MAAsByO,EAAOoqB,WACtCl4B,EAAIuE,SAASuJ,EAAOqqB,gBAGtBn4B,EAAIuE,SAASuJ,EAAOsqB,cAAgBtqB,EAAOzO,MAEvB,YAAhByO,EAAOzO,MAAsByO,EAAOuoB,iBACtCr2B,EAAIuE,SAAU,GAAMuJ,EAAoB,cAAKA,EAAW,KAAI,YAC5DhQ,EAAOiC,WAAWy2B,mBAAqB,EACnC1oB,EAAOyoB,mBAAqB,IAC9BzoB,EAAOyoB,mBAAqB,IAGZ,gBAAhBzoB,EAAOzO,MAA0ByO,EAAOspB,qBAC1Cp3B,EAAIuE,SAASuJ,EAAOuqB,0BAGlBvqB,EAAOoqB,WACTl4B,EAAII,GAAG,QAAU,IAAO0N,EAAkB,aAAI,SAAiB/S,GAC7DA,EAAEyoB,iBACF,IAAInZ,EAAQ9G,EAAEpI,MAAMkP,QAAUvM,EAAOgQ,OAAOuG,eACxCvW,EAAOgQ,OAAOyK,OAAQlO,GAASvM,EAAOsf,cAC1Ctf,EAAO2e,QAAQpS,MAInBgC,EAAMkC,OAAOzQ,EAAOiC,WAAY,CAC9BC,IAAKA,EACLyF,GAAIzF,EAAI,QAGZH,QAAS,WACP,IAAI/B,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO/N,WAC3B,GAAK+N,EAAOrI,IAAO3H,EAAOiC,WAAW0F,IAAO3H,EAAOiC,WAAWC,KAAwC,IAAjClC,EAAOiC,WAAWC,IAAIb,OAA3F,CACA,IAAIa,EAAMlC,EAAOiC,WAAWC,IAE5BA,EAAI2E,YAAYmJ,EAAO0nB,aACvBx1B,EAAI2E,YAAYmJ,EAAOsqB,cAAgBtqB,EAAOzO,MAC1CvB,EAAOiC,WAAWk2B,SAAWn4B,EAAOiC,WAAWk2B,QAAQtxB,YAAYmJ,EAAO8oB,mBAC1E9oB,EAAOoqB,WACTl4B,EAAIyH,IAAI,QAAU,IAAOqG,EAAkB,gBAK7CwqB,GAAe,CACjBj6B,KAAM,aACNyP,OAAQ,CACN/N,WAAY,CACV0F,GAAI,KACJ8yB,cAAe,OACfL,WAAW,EACX3C,aAAa,EACboC,aAAc,KACdK,kBAAmB,KACnBH,eAAgB,KAChBL,aAAc,KACdJ,qBAAqB,EACrB/3B,KAAM,UACNg3B,gBAAgB,EAChBE,mBAAoB,EACpBU,sBAAuB,SAAUuB,GAAU,OAAOA,GAClDtB,oBAAqB,SAAUsB,GAAU,OAAOA,GAChDZ,YAAa,2BACbhB,kBAAmB,kCACnBwB,cAAe,qBACfN,aAAc,4BACdC,WAAY,0BACZvC,YAAa,2BACbyC,qBAAsB,qCACtBI,yBAA0B,yCAC1BF,eAAgB,8BAChBlD,UAAW,2BAGfha,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBiC,WAAY,CACVwrB,KAAMsK,GAAWtK,KAAKxQ,KAAKjd,GAC3BnB,OAAQk5B,GAAWl5B,OAAOoe,KAAKjd,GAC/BY,OAAQm3B,GAAWn3B,OAAOqc,KAAKjd,GAC/B+B,QAASg2B,GAAWh2B,QAAQkb,KAAKjd,GACjC04B,mBAAoB,MAI1Bp2B,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACb2C,EAAOiC,WAAWwrB,OAClBztB,EAAOiC,WAAWpD,SAClBmB,EAAOiC,WAAWrB,UAEpB+5B,kBAAmB,WACjB,IAAI36B,EAAS3C,KACT2C,EAAOgQ,OAAOyK,KAChBza,EAAOiC,WAAWrB,SACmB,qBAArBZ,EAAOkY,WACvBlY,EAAOiC,WAAWrB,UAGtBg6B,gBAAiB,WACf,IAAI56B,EAAS3C,KACR2C,EAAOgQ,OAAOyK,MACjBza,EAAOiC,WAAWrB,UAGtBi6B,mBAAoB,WAClB,IAAI76B,EAAS3C,KACT2C,EAAOgQ,OAAOyK,OAChBza,EAAOiC,WAAWpD,SAClBmB,EAAOiC,WAAWrB,WAGtBk6B,qBAAsB,WACpB,IAAI96B,EAAS3C,KACR2C,EAAOgQ,OAAOyK,OACjBza,EAAOiC,WAAWpD,SAClBmB,EAAOiC,WAAWrB,WAGtBmB,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOiC,WAAWF,WAEpB81B,MAAO,SAAe56B,GACpB,IAAI+C,EAAS3C,KACb,GACE2C,EAAOgQ,OAAO/N,WAAW0F,IACtB3H,EAAOgQ,OAAO/N,WAAWw1B,aACzBz3B,EAAOiC,WAAWC,IAAIb,OAAS,IAC9BoE,EAAExI,EAAE2L,QAAQ7B,SAAS/G,EAAOgQ,OAAO/N,WAAW63B,aAClD,CACA,IAAIhC,EAAW93B,EAAOiC,WAAWC,IAAI6E,SAAS/G,EAAOgQ,OAAO/N,WAAWy1B,cACtD,IAAbI,EACF93B,EAAOmY,KAAK,iBAAkBnY,GAE9BA,EAAOmY,KAAK,iBAAkBnY,GAEhCA,EAAOiC,WAAWC,IAAI+E,YAAYjH,EAAOgQ,OAAO/N,WAAWy1B,iBAM/DqD,GAAY,CACdnd,aAAc,WACZ,IAAI5d,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOgrB,UAAUrzB,IAAO3H,EAAOg7B,UAAUrzB,GAArD,CACA,IAAIqzB,EAAYh7B,EAAOg7B,UACnBnnB,EAAM7T,EAAO8T,aACb+F,EAAW7Z,EAAO6Z,SAClBohB,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UACtBC,EAAUH,EAAUG,QACpBj5B,EAAM84B,EAAU94B,IAChB8N,EAAShQ,EAAOgQ,OAAOgrB,UAEvBI,EAAUH,EACVI,GAAUH,EAAYD,GAAYphB,EAClChG,GACFwnB,GAAUA,EACNA,EAAS,GACXD,EAAUH,EAAWI,EACrBA,EAAS,IACCA,EAASJ,EAAWC,IAC9BE,EAAUF,EAAYG,IAEfA,EAAS,GAClBD,EAAUH,EAAWI,EACrBA,EAAS,GACAA,EAASJ,EAAWC,IAC7BE,EAAUF,EAAYG,GAEpBr7B,EAAOsT,gBACLpC,EAAQW,aACVspB,EAAQrzB,UAAW,eAAiBuzB,EAAS,aAE7CF,EAAQrzB,UAAW,cAAgBuzB,EAAS,OAE9CF,EAAQ,GAAG92B,MAAM6O,MAAQkoB,EAAU,OAE/BlqB,EAAQW,aACVspB,EAAQrzB,UAAW,oBAAsBuzB,EAAS,UAElDF,EAAQrzB,UAAW,cAAgBuzB,EAAS,OAE9CF,EAAQ,GAAG92B,MAAM8O,OAASioB,EAAU,MAElCprB,EAAOsrB,OACTj2B,aAAarF,EAAOg7B,UAAUvE,SAC9Bv0B,EAAI,GAAGmC,MAAMk3B,QAAU,EACvBv7B,EAAOg7B,UAAUvE,QAAUrxB,YAAW,WACpClD,EAAI,GAAGmC,MAAMk3B,QAAU,EACvBr5B,EAAI+F,WAAW,OACd,QAGP4Q,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACR2C,EAAOgQ,OAAOgrB,UAAUrzB,IAAO3H,EAAOg7B,UAAUrzB,IACrD3H,EAAOg7B,UAAUG,QAAQlzB,WAAWC,IAEtC+K,WAAY,WACV,IAAIjT,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOgrB,UAAUrzB,IAAO3H,EAAOg7B,UAAUrzB,GAArD,CAEA,IAAIqzB,EAAYh7B,EAAOg7B,UACnBG,EAAUH,EAAUG,QACpBj5B,EAAM84B,EAAU94B,IAEpBi5B,EAAQ,GAAG92B,MAAM6O,MAAQ,GACzBioB,EAAQ,GAAG92B,MAAM8O,OAAS,GAC1B,IAII8nB,EAJAC,EAAYl7B,EAAOsT,eAAiBpR,EAAI,GAAG6I,YAAc7I,EAAI,GAAGgJ,aAEhEswB,EAAUx7B,EAAOyT,KAAOzT,EAAOoV,YAC/BqmB,EAAcD,GAAWN,EAAYl7B,EAAOyT,MAG9CwnB,EADuC,SAArCj7B,EAAOgQ,OAAOgrB,UAAUC,SACfC,EAAYM,EAEZhoB,SAASxT,EAAOgQ,OAAOgrB,UAAUC,SAAU,IAGpDj7B,EAAOsT,eACT6nB,EAAQ,GAAG92B,MAAM6O,MAAQ+nB,EAAW,KAEpCE,EAAQ,GAAG92B,MAAM8O,OAAS8nB,EAAW,KAIrC/4B,EAAI,GAAGmC,MAAMq3B,QADXF,GAAW,EACU,OAEA,GAErBx7B,EAAOgQ,OAAOgrB,UAAUM,OAC1Bp5B,EAAI,GAAGmC,MAAMk3B,QAAU,GAEzBhtB,EAAMkC,OAAOuqB,EAAW,CACtBE,UAAWA,EACXM,QAASA,EACTC,YAAaA,EACbR,SAAUA,IAEZD,EAAU94B,IAAIlC,EAAOgQ,OAAOoI,eAAiBpY,EAAOuhB,SAAW,WAAa,eAAevhB,EAAOgQ,OAAOgrB,UAAU7D,aAErHwE,mBAAoB,SAA4B1+B,GAC9C,IAAI+C,EAAS3C,KACb,OAAI2C,EAAOsT,eACW,eAAXrW,EAAEsE,MAAoC,cAAXtE,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGC,MAAQznB,EAAEynB,OAASznB,EAAE2+B,QAEpF,eAAX3+B,EAAEsE,MAAoC,cAAXtE,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGG,MAAQ3nB,EAAE2nB,OAAS3nB,EAAE4+B,SAExGC,gBAAiB,SAAyB7+B,GACxC,IAQI8+B,EARA/7B,EAAS3C,KACT29B,EAAYh7B,EAAOg7B,UACnBnnB,EAAM7T,EAAO8T,aACb5R,EAAM84B,EAAU94B,IAChB+4B,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UACtBc,EAAehB,EAAUgB,aAG7BD,GAAkBf,EAAUW,mBAAmB1+B,GAAMiF,EAAIiJ,SAASnL,EAAOsT,eAAiB,OAAS,QAC7E,OAAjB0oB,EAAwBA,EAAef,EAAW,KAAOC,EAAYD,GAC1Ec,EAAgBrmB,KAAKK,IAAIL,KAAKkU,IAAImS,EAAe,GAAI,GACjDloB,IACFkoB,EAAgB,EAAIA,GAGtB,IAAIzU,EAAWtnB,EAAOyZ,gBAAmBzZ,EAAOga,eAAiBha,EAAOyZ,gBAAkBsiB,EAE1F/7B,EAAO8Z,eAAewN,GACtBtnB,EAAO4d,aAAa0J,GACpBtnB,EAAOkb,oBACPlb,EAAOqa,uBAET4hB,YAAa,SAAqBh/B,GAChC,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOgrB,UACvBA,EAAYh7B,EAAOg7B,UACnBrnB,EAAa3T,EAAO2T,WACpBzR,EAAM84B,EAAU94B,IAChBi5B,EAAUH,EAAUG,QACxBn7B,EAAOg7B,UAAU9W,WAAY,EAC7BlkB,EAAOg7B,UAAUgB,aAAgB/+B,EAAE2L,SAAWuyB,EAAQ,IAAMl+B,EAAE2L,SAAWuyB,EACrEH,EAAUW,mBAAmB1+B,GAAKA,EAAE2L,OAAOyC,wBAAwBrL,EAAOsT,eAAiB,OAAS,OAAS,KACjHrW,EAAEyoB,iBACFzoB,EAAE2pB,kBAEFjT,EAAW1L,WAAW,KACtBkzB,EAAQlzB,WAAW,KACnB+yB,EAAUc,gBAAgB7+B,GAE1BoI,aAAarF,EAAOg7B,UAAUkB,aAE9Bh6B,EAAI+F,WAAW,GACX+H,EAAOsrB,MACTp5B,EAAI4J,IAAI,UAAW,GAErB9L,EAAOmY,KAAK,qBAAsBlb,IAEpCk/B,WAAY,SAAoBl/B,GAC9B,IAAI+C,EAAS3C,KACT29B,EAAYh7B,EAAOg7B,UACnBrnB,EAAa3T,EAAO2T,WACpBzR,EAAM84B,EAAU94B,IAChBi5B,EAAUH,EAAUG,QAEnBn7B,EAAOg7B,UAAU9W,YAClBjnB,EAAEyoB,eAAkBzoB,EAAEyoB,iBACnBzoB,EAAEi3B,aAAc,EACvB8G,EAAUc,gBAAgB7+B,GAC1B0W,EAAW1L,WAAW,GACtB/F,EAAI+F,WAAW,GACfkzB,EAAQlzB,WAAW,GACnBjI,EAAOmY,KAAK,oBAAqBlb,KAEnCm/B,UAAW,SAAmBn/B,GAC5B,IAAI+C,EAAS3C,KAET2S,EAAShQ,EAAOgQ,OAAOgrB,UACvBA,EAAYh7B,EAAOg7B,UACnB94B,EAAM84B,EAAU94B,IAEflC,EAAOg7B,UAAU9W,YACtBlkB,EAAOg7B,UAAU9W,WAAY,EACzBlU,EAAOsrB,OACTj2B,aAAarF,EAAOg7B,UAAUkB,aAC9Bl8B,EAAOg7B,UAAUkB,YAAc3tB,EAAMI,UAAS,WAC5CzM,EAAI4J,IAAI,UAAW,GACnB5J,EAAI+F,WAAW,OACd,MAELjI,EAAOmY,KAAK,mBAAoBlb,GAC5B+S,EAAOqsB,eACTr8B,EAAOkgB,mBAGXoc,gBAAiB,WACf,IAAIt8B,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOgrB,UAAUrzB,GAA7B,CACA,IAAIqzB,EAAYh7B,EAAOg7B,UACnBzM,EAAmBvuB,EAAOuuB,iBAC1BC,EAAqBxuB,EAAOwuB,mBAC5Bxe,EAAShQ,EAAOgQ,OAChB9N,EAAM84B,EAAU94B,IAChB0G,EAAS1G,EAAI,GACbq6B,KAAiBrrB,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAOjiB,SAAS,GACjGuJ,KAAkBf,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAChGwI,EAAQE,OAKXxI,EAAOpF,iBAAiB+qB,EAAiB9D,MAAOzqB,EAAOg7B,UAAUiB,YAAaM,GAC9E3zB,EAAOpF,iBAAiB+qB,EAAiB3D,KAAM5qB,EAAOg7B,UAAUmB,WAAYI,GAC5E3zB,EAAOpF,iBAAiB+qB,EAAiB1D,IAAK7qB,EAAOg7B,UAAUoB,UAAWnqB,KAN1ErJ,EAAOpF,iBAAiBgrB,EAAmB/D,MAAOzqB,EAAOg7B,UAAUiB,YAAaM,GAChFl5B,EAAIG,iBAAiBgrB,EAAmB5D,KAAM5qB,EAAOg7B,UAAUmB,WAAYI,GAC3El5B,EAAIG,iBAAiBgrB,EAAmB3D,IAAK7qB,EAAOg7B,UAAUoB,UAAWnqB,MAO7EuqB,iBAAkB,WAChB,IAAIx8B,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOgrB,UAAUrzB,GAA7B,CACA,IAAIqzB,EAAYh7B,EAAOg7B,UACnBzM,EAAmBvuB,EAAOuuB,iBAC1BC,EAAqBxuB,EAAOwuB,mBAC5Bxe,EAAShQ,EAAOgQ,OAChB9N,EAAM84B,EAAU94B,IAChB0G,EAAS1G,EAAI,GACbq6B,KAAiBrrB,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAOjiB,SAAS,GACjGuJ,KAAkBf,EAAQe,kBAAmBjC,EAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAChGwI,EAAQE,OAKXxI,EAAOnF,oBAAoB8qB,EAAiB9D,MAAOzqB,EAAOg7B,UAAUiB,YAAaM,GACjF3zB,EAAOnF,oBAAoB8qB,EAAiB3D,KAAM5qB,EAAOg7B,UAAUmB,WAAYI,GAC/E3zB,EAAOnF,oBAAoB8qB,EAAiB1D,IAAK7qB,EAAOg7B,UAAUoB,UAAWnqB,KAN7ErJ,EAAOnF,oBAAoB+qB,EAAmB/D,MAAOzqB,EAAOg7B,UAAUiB,YAAaM,GACnFl5B,EAAII,oBAAoB+qB,EAAmB5D,KAAM5qB,EAAOg7B,UAAUmB,WAAYI,GAC9El5B,EAAII,oBAAoB+qB,EAAmB3D,IAAK7qB,EAAOg7B,UAAUoB,UAAWnqB,MAOhFwb,KAAM,WACJ,IAAIztB,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOgrB,UAAUrzB,GAA7B,CACA,IAAIqzB,EAAYh7B,EAAOg7B,UACnByB,EAAYz8B,EAAOkC,IACnB8N,EAAShQ,EAAOgQ,OAAOgrB,UAEvB94B,EAAMuD,EAAEuK,EAAOrI,IACf3H,EAAOgQ,OAAO0d,mBAA0C,kBAAd1d,EAAOrI,IAAmBzF,EAAIb,OAAS,GAA0C,IAArCo7B,EAAU5uB,KAAKmC,EAAOrI,IAAItG,SAClHa,EAAMu6B,EAAU5uB,KAAKmC,EAAOrI,KAG9B,IAAIwzB,EAAUj5B,EAAI2L,KAAM,IAAO7N,EAAOgQ,OAAOgrB,UAAmB,WACzC,IAAnBG,EAAQ95B,SACV85B,EAAU11B,EAAG,eAAmBzF,EAAOgQ,OAAOgrB,UAAmB,UAAI,YACrE94B,EAAI0K,OAAOuuB,IAGb5sB,EAAMkC,OAAOuqB,EAAW,CACtB94B,IAAKA,EACLyF,GAAIzF,EAAI,GACRi5B,QAASA,EACTuB,OAAQvB,EAAQ,KAGdnrB,EAAO2sB,WACT3B,EAAUsB,oBAGdv6B,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOg7B,UAAUwB,qBAIjBI,GAAc,CAChBr8B,KAAM,YACNyP,OAAQ,CACNgrB,UAAW,CACTrzB,GAAI,KACJszB,SAAU,OACVK,MAAM,EACNqB,WAAW,EACXN,eAAe,EACflF,UAAW,wBACX0F,UAAW,0BAGf1f,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBg7B,UAAW,CACTvN,KAAMsN,GAAUtN,KAAKxQ,KAAKjd,GAC1B+B,QAASg5B,GAAUh5B,QAAQkb,KAAKjd,GAChCiT,WAAY8nB,GAAU9nB,WAAWgK,KAAKjd,GACtC4d,aAAcmd,GAAUnd,aAAaX,KAAKjd,GAC1C6Y,cAAekiB,GAAUliB,cAAcoE,KAAKjd,GAC5Cs8B,gBAAiBvB,GAAUuB,gBAAgBrf,KAAKjd,GAChDw8B,iBAAkBzB,GAAUyB,iBAAiBvf,KAAKjd,GAClD87B,gBAAiBf,GAAUe,gBAAgB7e,KAAKjd,GAChD27B,mBAAoBZ,GAAUY,mBAAmB1e,KAAKjd,GACtDi8B,YAAalB,GAAUkB,YAAYhf,KAAKjd,GACxCm8B,WAAYpB,GAAUoB,WAAWlf,KAAKjd,GACtCo8B,UAAWrB,GAAUqB,UAAUnf,KAAKjd,GACpCkkB,WAAW,EACXuS,QAAS,KACTyF,YAAa,SAInB55B,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACb2C,EAAOg7B,UAAUvN,OACjBztB,EAAOg7B,UAAU/nB,aACjBjT,EAAOg7B,UAAUpd,gBAEnBhd,OAAQ,WACN,IAAIZ,EAAS3C,KACb2C,EAAOg7B,UAAU/nB,cAEnB2c,OAAQ,WACN,IAAI5vB,EAAS3C,KACb2C,EAAOg7B,UAAU/nB,cAEnBqd,eAAgB,WACd,IAAItwB,EAAS3C,KACb2C,EAAOg7B,UAAU/nB,cAEnB2K,aAAc,WACZ,IAAI5d,EAAS3C,KACb2C,EAAOg7B,UAAUpd,gBAEnB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACb2C,EAAOg7B,UAAUniB,cAAc3Q,IAEjCnG,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAOg7B,UAAUj5B,aAKnB+6B,GAAW,CACbC,aAAc,SAAsBp1B,EAAIkS,GACtC,IAAI7Z,EAAS3C,KACTwW,EAAM7T,EAAO6T,IAEb3R,EAAMuD,EAAEkC,GACR0uB,EAAYxiB,GAAO,EAAI,EAEvBrV,EAAI0D,EAAIiF,KAAK,yBAA2B,IACxC4W,EAAI7b,EAAIiF,KAAK,0BACb6W,EAAI9b,EAAIiF,KAAK,0BACboyB,EAAQr3B,EAAIiF,KAAK,8BACjBo0B,EAAUr5B,EAAIiF,KAAK,gCAwBvB,GAtBI4W,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KACAhe,EAAOsT,gBAChByK,EAAIvf,EACJwf,EAAI,MAEJA,EAAIxf,EACJuf,EAAI,KAIJA,EADE,EAAI/X,QAAQ,MAAQ,EACjBwN,SAASuK,EAAG,IAAMlE,EAAWwc,EAAa,IAE1CtY,EAAIlE,EAAWwc,EAAa,KAGjCrY,EADE,EAAIhY,QAAQ,MAAQ,EACjBwN,SAASwK,EAAG,IAAMnE,EAAY,IAE9BmE,EAAInE,EAAY,KAGA,qBAAZ0hB,GAAuC,OAAZA,EAAkB,CACtD,IAAIyB,EAAiBzB,GAAYA,EAAU,IAAM,EAAI7lB,KAAK4B,IAAIuC,IAC9D3X,EAAI,GAAGmC,MAAMk3B,QAAUyB,EAEzB,GAAqB,qBAAVzD,GAAmC,OAAVA,EAClCr3B,EAAI4F,UAAW,eAAiBiW,EAAI,KAAOC,EAAI,cAC1C,CACL,IAAIif,EAAe1D,GAAUA,EAAQ,IAAM,EAAI7jB,KAAK4B,IAAIuC,IACxD3X,EAAI4F,UAAW,eAAiBiW,EAAI,KAAOC,EAAI,gBAAkBif,EAAe,OAGpFrf,aAAc,WACZ,IAAI5d,EAAS3C,KACT6E,EAAMlC,EAAOkC,IACbkS,EAASpU,EAAOoU,OAChByF,EAAW7Z,EAAO6Z,SAClBvF,EAAWtU,EAAOsU,SACtBpS,EAAIiC,SAAS,4IACV6H,MAAK,SAAUO,EAAO5E,GACrB3H,EAAOk9B,SAASH,aAAap1B,EAAIkS,MAErCzF,EAAOpI,MAAK,SAAU6S,EAAYoQ,GAChC,IAAIzV,EAAgByV,EAAQpV,SACxB7Z,EAAOgQ,OAAOuG,eAAiB,GAAqC,SAAhCvW,EAAOgQ,OAAO6F,gBACpD2D,GAAiB9D,KAAKE,KAAKiJ,EAAa,GAAMhF,GAAYvF,EAASjT,OAAS,IAE9EmY,EAAgB9D,KAAKkU,IAAIlU,KAAKK,IAAIyD,GAAgB,GAAI,GACtD/T,EAAEwpB,GAASphB,KAAK,4IACb7B,MAAK,SAAUO,EAAO5E,GACrB3H,EAAOk9B,SAASH,aAAap1B,EAAI6R,UAIzCX,cAAe,SAAuB3Q,QAClB,IAAbA,IAAsBA,EAAW7K,KAAK2S,OAAO0I,OAElD,IAAI1Y,EAAS3C,KACT6E,EAAMlC,EAAOkC,IACjBA,EAAI2L,KAAK,4IACN7B,MAAK,SAAUO,EAAO4wB,GACrB,IAAIC,EAAc33B,EAAE03B,GAChBE,EAAmB7pB,SAAS4pB,EAAYj2B,KAAK,iCAAkC,KAAOe,EACzE,IAAbA,IAAkBm1B,EAAmB,GACzCD,EAAYn1B,WAAWo1B,QAK3BC,GAAa,CACf/8B,KAAM,WACNyP,OAAQ,CACNktB,SAAU,CACRhpB,SAAS,IAGbiJ,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBk9B,SAAU,CACRH,aAAcD,GAASC,aAAa9f,KAAKjd,GACzC4d,aAAckf,GAASlf,aAAaX,KAAKjd,GACzC6Y,cAAeikB,GAASjkB,cAAcoE,KAAKjd,OAIjDsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACR2C,EAAOgQ,OAAOktB,SAAShpB,UAC5BlU,EAAOgQ,OAAOsI,qBAAsB,EACpCtY,EAAOqrB,eAAe/S,qBAAsB,IAE9CmV,KAAM,WACJ,IAAIztB,EAAS3C,KACR2C,EAAOgQ,OAAOktB,SAAShpB,SAC5BlU,EAAOk9B,SAAStf,gBAElBA,aAAc,WACZ,IAAI5d,EAAS3C,KACR2C,EAAOgQ,OAAOktB,SAAShpB,SAC5BlU,EAAOk9B,SAAStf,gBAElB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACR2C,EAAOgQ,OAAOktB,SAAShpB,SAC5BlU,EAAOk9B,SAASrkB,cAAc3Q,MAKhCq1B,GAAO,CAETC,0BAA2B,SAAmCvgC,GAC5D,GAAIA,EAAEwnB,cAAcpjB,OAAS,EAAK,OAAO,EACzC,IAAIo8B,EAAKxgC,EAAEwnB,cAAc,GAAGC,MACxBgZ,EAAKzgC,EAAEwnB,cAAc,GAAGG,MACxB+Y,EAAK1gC,EAAEwnB,cAAc,GAAGC,MACxBkZ,EAAK3gC,EAAEwnB,cAAc,GAAGG,MACxBsD,EAAWxS,KAAK2Q,KAAM3Q,KAAK4Q,IAAMqX,EAAKF,EAAK,GAAQ/nB,KAAK4Q,IAAMsX,EAAKF,EAAK,IAC5E,OAAOxV,GAGT2V,eAAgB,SAAwB5gC,GACtC,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO8tB,KACvBA,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QAGnB,GAFAD,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,GACnB/sB,EAAQkB,SAAU,CACrB,GAAe,eAAXnV,EAAEsE,MAAqC,eAAXtE,EAAEsE,MAAyBtE,EAAEwnB,cAAcpjB,OAAS,EAClF,OAEFy8B,EAAKE,oBAAqB,EAC1BD,EAAQG,WAAaX,GAAKC,0BAA0BvgC,GAEjD8gC,EAAQrL,UAAaqL,EAAQrL,SAASrxB,SACzC08B,EAAQrL,SAAWjtB,EAAExI,EAAE2L,QAAQgF,QAAQ,iBACP,IAA5BmwB,EAAQrL,SAASrxB,SAAgB08B,EAAQrL,SAAW1yB,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,cAChFilB,EAAQI,SAAWJ,EAAQrL,SAAS7kB,KAAK,oBACzCkwB,EAAQK,aAAeL,EAAQI,SAAS/+B,OAAQ,IAAO4Q,EAAqB,gBAC5E+tB,EAAQM,SAAWN,EAAQK,aAAaj3B,KAAK,qBAAuB6I,EAAOquB,SACvC,IAAhCN,EAAQK,aAAa/8B,SAK3B08B,EAAQI,SAASl2B,WAAW,GAC5BjI,EAAO89B,KAAKQ,WAAY,GALpBP,EAAQI,cAAW/0B,GAOzBm1B,gBAAiB,SAAyBthC,GACxC,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO8tB,KACvBA,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACnB,IAAK7sB,EAAQkB,SAAU,CACrB,GAAe,cAAXnV,EAAEsE,MAAoC,cAAXtE,EAAEsE,MAAwBtE,EAAEwnB,cAAcpjB,OAAS,EAChF,OAEFy8B,EAAKG,kBAAmB,EACxBF,EAAQS,UAAYjB,GAAKC,0BAA0BvgC,GAEhD8gC,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,SACtC6P,EAAQkB,SACV0rB,EAAKvE,MAAQt8B,EAAEs8B,MAAQuE,EAAKb,aAE5Ba,EAAKvE,MAASwE,EAAQS,UAAYT,EAAQG,WAAcJ,EAAKb,aAE3Da,EAAKvE,MAAQwE,EAAQM,WACvBP,EAAKvE,MAASwE,EAAQM,SAAW,EAAM3oB,KAAK4Q,IAAOwX,EAAKvE,MAAQwE,EAAQM,SAAY,EAAI,KAEtFP,EAAKvE,MAAQvpB,EAAOyuB,WACtBX,EAAKvE,MAASvpB,EAAOyuB,SAAW,EAAM/oB,KAAK4Q,IAAOtW,EAAOyuB,SAAWX,EAAKvE,MAAS,EAAI,KAExFwE,EAAQI,SAASr2B,UAAW,4BAA+Bg2B,EAAU,MAAI,OAE3EY,aAAc,SAAsBzhC,GAClC,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAO8tB,KACvBA,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACnB,IAAK7sB,EAAQkB,SAAU,CACrB,IAAK0rB,EAAKE,qBAAuBF,EAAKG,iBACpC,OAEF,GAAe,aAAXhhC,EAAEsE,MAAmC,aAAXtE,EAAEsE,MAAuBtE,EAAE0hC,eAAet9B,OAAS,IAAMkhB,GAAOG,QAC5F,OAEFob,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,EAErBF,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,SAC1Cy8B,EAAKvE,MAAQ7jB,KAAKK,IAAIL,KAAKkU,IAAIkU,EAAKvE,MAAOwE,EAAQM,UAAWruB,EAAOyuB,UACrEV,EAAQI,SAASl2B,WAAWjI,EAAOgQ,OAAO0I,OAAO5Q,UAAW,4BAA+Bg2B,EAAU,MAAI,KACzGA,EAAKb,aAAea,EAAKvE,MACzBuE,EAAKQ,WAAY,EACE,IAAfR,EAAKvE,QAAewE,EAAQrL,cAAWtpB,KAE7Cua,aAAc,SAAsB1mB,GAClC,IAAI+C,EAAS3C,KACTygC,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACfpR,EAAQmR,EAAKnR,MACZoR,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,SACtCsrB,EAAMzI,YACN3B,GAAOG,SAAWzlB,EAAEyoB,iBACxBiH,EAAMzI,WAAY,EAClByI,EAAMiS,aAAa7gB,EAAe,eAAX9gB,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGC,MAAQznB,EAAEynB,MAC9EiI,EAAMiS,aAAa5gB,EAAe,eAAX/gB,EAAEsE,KAAwBtE,EAAEwnB,cAAc,GAAGG,MAAQ3nB,EAAE2nB,SAEhFoB,YAAa,SAAqB/oB,GAChC,IAAI+C,EAAS3C,KACTygC,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACfpR,EAAQmR,EAAKnR,MACbxE,EAAW2V,EAAK3V,SACpB,GAAK4V,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,SAC1CrB,EAAOskB,YAAa,EACfqI,EAAMzI,WAAc6Z,EAAQrL,UAAjC,CAEK/F,EAAMxI,UACTwI,EAAMzZ,MAAQ6qB,EAAQI,SAAS,GAAGpzB,YAClC4hB,EAAMxZ,OAAS4qB,EAAQI,SAAS,GAAGjzB,aACnCyhB,EAAM9H,OAAStW,EAAMO,aAAaivB,EAAQK,aAAa,GAAI,MAAQ,EACnEzR,EAAM7H,OAASvW,EAAMO,aAAaivB,EAAQK,aAAa,GAAI,MAAQ,EACnEL,EAAQc,WAAad,EAAQrL,SAAS,GAAG3nB,YACzCgzB,EAAQe,YAAcf,EAAQrL,SAAS,GAAGxnB,aAC1C6yB,EAAQK,aAAan2B,WAAW,GAC5BjI,EAAO6T,MACT8Y,EAAM9H,QAAU8H,EAAM9H,OACtB8H,EAAM7H,QAAU6H,EAAM7H,SAI1B,IAAIia,EAAcpS,EAAMzZ,MAAQ4qB,EAAKvE,MACjCyF,EAAerS,EAAMxZ,OAAS2qB,EAAKvE,MAEvC,KAAIwF,EAAchB,EAAQc,YAAcG,EAAejB,EAAQe,aAA/D,CAUA,GARAnS,EAAMsS,KAAOvpB,KAAKkU,IAAMmU,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtEpS,EAAMuS,MAAQvS,EAAMsS,KACpBtS,EAAMwS,KAAOzpB,KAAKkU,IAAMmU,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxErS,EAAMyS,MAAQzS,EAAMwS,KAEpBxS,EAAM0S,eAAethB,EAAe,cAAX9gB,EAAEsE,KAAuBtE,EAAEwnB,cAAc,GAAGC,MAAQznB,EAAEynB,MAC/EiI,EAAM0S,eAAerhB,EAAe,cAAX/gB,EAAEsE,KAAuBtE,EAAEwnB,cAAc,GAAGG,MAAQ3nB,EAAE2nB,OAE1E+H,EAAMxI,UAAY2Z,EAAKQ,UAAW,CACrC,GACEt+B,EAAOsT,iBAEJoC,KAAKC,MAAMgX,EAAMsS,QAAUvpB,KAAKC,MAAMgX,EAAM9H,SAAW8H,EAAM0S,eAAethB,EAAI4O,EAAMiS,aAAa7gB,GAChGrI,KAAKC,MAAMgX,EAAMuS,QAAUxpB,KAAKC,MAAMgX,EAAM9H,SAAW8H,EAAM0S,eAAethB,EAAI4O,EAAMiS,aAAa7gB,GAIzG,YADA4O,EAAMzI,WAAY,GAElB,IACClkB,EAAOsT,iBAELoC,KAAKC,MAAMgX,EAAMwS,QAAUzpB,KAAKC,MAAMgX,EAAM7H,SAAW6H,EAAM0S,eAAerhB,EAAI2O,EAAMiS,aAAa5gB,GAChGtI,KAAKC,MAAMgX,EAAMyS,QAAU1pB,KAAKC,MAAMgX,EAAM7H,SAAW6H,EAAM0S,eAAerhB,EAAI2O,EAAMiS,aAAa5gB,GAIzG,YADA2O,EAAMzI,WAAY,GAItBjnB,EAAEyoB,iBACFzoB,EAAE2pB,kBAEF+F,EAAMxI,SAAU,EAChBwI,EAAMnI,SAAYmI,EAAM0S,eAAethB,EAAI4O,EAAMiS,aAAa7gB,EAAK4O,EAAM9H,OACzE8H,EAAMhI,SAAYgI,EAAM0S,eAAerhB,EAAI2O,EAAMiS,aAAa5gB,EAAK2O,EAAM7H,OAErE6H,EAAMnI,SAAWmI,EAAMsS,OACzBtS,EAAMnI,SAAYmI,EAAMsS,KAAO,EAAMvpB,KAAK4Q,IAAOqG,EAAMsS,KAAOtS,EAAMnI,SAAY,EAAI,KAElFmI,EAAMnI,SAAWmI,EAAMuS,OACzBvS,EAAMnI,SAAYmI,EAAMuS,KAAO,EAAMxpB,KAAK4Q,IAAOqG,EAAMnI,SAAWmI,EAAMuS,KAAQ,EAAI,KAGlFvS,EAAMhI,SAAWgI,EAAMwS,OACzBxS,EAAMhI,SAAYgI,EAAMwS,KAAO,EAAMzpB,KAAK4Q,IAAOqG,EAAMwS,KAAOxS,EAAMhI,SAAY,EAAI,KAElFgI,EAAMhI,SAAWgI,EAAMyS,OACzBzS,EAAMhI,SAAYgI,EAAMyS,KAAO,EAAM1pB,KAAK4Q,IAAOqG,EAAMhI,SAAWgI,EAAMyS,KAAQ,EAAI,KAIjFjX,EAASmX,gBAAiBnX,EAASmX,cAAgB3S,EAAM0S,eAAethB,GACxEoK,EAASoX,gBAAiBpX,EAASoX,cAAgB5S,EAAM0S,eAAerhB,GACxEmK,EAASqX,WAAYrX,EAASqX,SAAWt6B,KAAK2J,OACnDsZ,EAASpK,GAAK4O,EAAM0S,eAAethB,EAAIoK,EAASmX,gBAAkBp6B,KAAK2J,MAAQsZ,EAASqX,UAAY,EACpGrX,EAASnK,GAAK2O,EAAM0S,eAAerhB,EAAImK,EAASoX,gBAAkBr6B,KAAK2J,MAAQsZ,EAASqX,UAAY,EAChG9pB,KAAK4B,IAAIqV,EAAM0S,eAAethB,EAAIoK,EAASmX,eAAiB,IAAKnX,EAASpK,EAAI,GAC9ErI,KAAK4B,IAAIqV,EAAM0S,eAAerhB,EAAImK,EAASoX,eAAiB,IAAKpX,EAASnK,EAAI,GAClFmK,EAASmX,cAAgB3S,EAAM0S,eAAethB,EAC9CoK,EAASoX,cAAgB5S,EAAM0S,eAAerhB,EAC9CmK,EAASqX,SAAWt6B,KAAK2J,MAEzBkvB,EAAQK,aAAat2B,UAAW,eAAkB6kB,EAAc,SAAI,OAAUA,EAAc,SAAI,YAElGnF,WAAY,WACV,IAAIxnB,EAAS3C,KACTygC,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACfpR,EAAQmR,EAAKnR,MACbxE,EAAW2V,EAAK3V,SACpB,GAAK4V,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,OAA1C,CACA,IAAKsrB,EAAMzI,YAAcyI,EAAMxI,QAG7B,OAFAwI,EAAMzI,WAAY,OAClByI,EAAMxI,SAAU,GAGlBwI,EAAMzI,WAAY,EAClByI,EAAMxI,SAAU,EAChB,IAAIsb,EAAoB,IACpBC,EAAoB,IACpBC,EAAoBxX,EAASpK,EAAI0hB,EACjCG,EAAejT,EAAMnI,SAAWmb,EAChCE,EAAoB1X,EAASnK,EAAI0hB,EACjCI,EAAenT,EAAMhI,SAAWkb,EAGjB,IAAf1X,EAASpK,IAAW0hB,EAAoB/pB,KAAK4B,KAAKsoB,EAAejT,EAAMnI,UAAY2D,EAASpK,IAC7E,IAAfoK,EAASnK,IAAW0hB,EAAoBhqB,KAAK4B,KAAKwoB,EAAenT,EAAMhI,UAAYwD,EAASnK,IAChG,IAAIsK,EAAmB5S,KAAKK,IAAI0pB,EAAmBC,GAEnD/S,EAAMnI,SAAWob,EACjBjT,EAAMhI,SAAWmb,EAGjB,IAAIf,EAAcpS,EAAMzZ,MAAQ4qB,EAAKvE,MACjCyF,EAAerS,EAAMxZ,OAAS2qB,EAAKvE,MACvC5M,EAAMsS,KAAOvpB,KAAKkU,IAAMmU,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtEpS,EAAMuS,MAAQvS,EAAMsS,KACpBtS,EAAMwS,KAAOzpB,KAAKkU,IAAMmU,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxErS,EAAMyS,MAAQzS,EAAMwS,KACpBxS,EAAMnI,SAAW9O,KAAKK,IAAIL,KAAKkU,IAAI+C,EAAMnI,SAAUmI,EAAMuS,MAAOvS,EAAMsS,MACtEtS,EAAMhI,SAAWjP,KAAKK,IAAIL,KAAKkU,IAAI+C,EAAMhI,SAAUgI,EAAMyS,MAAOzS,EAAMwS,MAEtEpB,EAAQK,aAAan2B,WAAWqgB,GAAkBxgB,UAAW,eAAkB6kB,EAAc,SAAI,OAAUA,EAAc,SAAI,WAE/HoT,gBAAiB,WACf,IAAI//B,EAAS3C,KACTygC,EAAO99B,EAAO89B,KACdC,EAAUD,EAAKC,QACfA,EAAQrL,UAAY1yB,EAAOob,gBAAkBpb,EAAO8Y,cACtDilB,EAAQI,SAASr2B,UAAU,+BAC3Bi2B,EAAQK,aAAat2B,UAAU,sBAE/Bg2B,EAAKvE,MAAQ,EACbuE,EAAKb,aAAe,EAEpBc,EAAQrL,cAAWtpB,EACnB20B,EAAQI,cAAW/0B,EACnB20B,EAAQK,kBAAeh1B,IAI3BlC,OAAQ,SAAgBjK,GACtB,IAAI+C,EAAS3C,KACTygC,EAAO99B,EAAO89B,KAEdA,EAAKvE,OAAwB,IAAfuE,EAAKvE,MAErBuE,EAAKkC,MAGLlC,EAAKmC,GAAGhjC,IAGZgjC,GAAI,SAAchjC,GAChB,IAgBIijC,EACAC,EACAC,EACAC,EACAla,EACAC,EACAka,EACAC,EACAC,EACAC,EACA1B,EACAC,EACA0B,EACAC,EACAC,EACAC,EACAhC,EACAC,EAjCA9+B,EAAS3C,KAETygC,EAAO99B,EAAO89B,KACd9tB,EAAShQ,EAAOgQ,OAAO8tB,KACvBC,EAAUD,EAAKC,QACfpR,EAAQmR,EAAKnR,OAEZoR,EAAQrL,WACXqL,EAAQrL,SAAW1yB,EAAO4b,aAAenW,EAAEzF,EAAO4b,cAAgB5b,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAC1FilB,EAAQI,SAAWJ,EAAQrL,SAAS7kB,KAAK,oBACzCkwB,EAAQK,aAAeL,EAAQI,SAAS/+B,OAAQ,IAAO4Q,EAAqB,iBAEzE+tB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,UAE1C08B,EAAQrL,SAASjsB,SAAU,GAAMuJ,EAAuB,kBAqBpB,qBAAzB2c,EAAMiS,aAAa7gB,GAAqB9gB,GACjDijC,EAAoB,aAAXjjC,EAAEsE,KAAsBtE,EAAE0hC,eAAe,GAAGja,MAAQznB,EAAEynB,MAC/Dyb,EAAoB,aAAXljC,EAAEsE,KAAsBtE,EAAE0hC,eAAe,GAAG/Z,MAAQ3nB,EAAE2nB,QAE/Dsb,EAASvT,EAAMiS,aAAa7gB,EAC5BoiB,EAASxT,EAAMiS,aAAa5gB,GAG9B8f,EAAKvE,MAAQwE,EAAQK,aAAaj3B,KAAK,qBAAuB6I,EAAOquB,SACrEP,EAAKb,aAAec,EAAQK,aAAaj3B,KAAK,qBAAuB6I,EAAOquB,SACxEphC,GACF4hC,EAAad,EAAQrL,SAAS,GAAG3nB,YACjC+zB,EAAcf,EAAQrL,SAAS,GAAGxnB,aAClCk1B,EAAUrC,EAAQrL,SAASvnB,SAASU,KACpCw0B,EAAUtC,EAAQrL,SAASvnB,SAASS,IACpCua,EAASia,EAAWvB,EAAa,EAAMqB,EACvC9Z,EAASia,EAAWvB,EAAc,EAAMqB,EAExCK,EAAazC,EAAQI,SAAS,GAAGpzB,YACjC01B,EAAc1C,EAAQI,SAAS,GAAGjzB,aAClC6zB,EAAcyB,EAAa1C,EAAKvE,MAChCyF,EAAeyB,EAAc3C,EAAKvE,MAElCmH,EAAgBhrB,KAAKkU,IAAMiV,EAAa,EAAME,EAAc,EAAK,GACjE4B,EAAgBjrB,KAAKkU,IAAMkV,EAAc,EAAME,EAAe,EAAK,GACnE4B,GAAiBF,EACjBG,GAAiBF,EAEjBL,EAAana,EAAQ2X,EAAKvE,MAC1BgH,EAAana,EAAQ0X,EAAKvE,MAEtB+G,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,GAGXL,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,KAGfP,EAAa,EACbC,EAAa,GAEfxC,EAAQK,aAAan2B,WAAW,KAAKH,UAAW,eAAiBw4B,EAAa,OAASC,EAAa,SACpGxC,EAAQI,SAASl2B,WAAW,KAAKH,UAAW,4BAA+Bg2B,EAAU,MAAI,OAE3FkC,IAAK,WACH,IAAIhgC,EAAS3C,KAETygC,EAAO99B,EAAO89B,KACd9tB,EAAShQ,EAAOgQ,OAAO8tB,KACvBC,EAAUD,EAAKC,QAEdA,EAAQrL,WACXqL,EAAQrL,SAAW1yB,EAAO4b,aAAenW,EAAEzF,EAAO4b,cAAgB5b,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAC1FilB,EAAQI,SAAWJ,EAAQrL,SAAS7kB,KAAK,oBACzCkwB,EAAQK,aAAeL,EAAQI,SAAS/+B,OAAQ,IAAO4Q,EAAqB,iBAEzE+tB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAAS98B,SAE1Cy8B,EAAKvE,MAAQ,EACbuE,EAAKb,aAAe,EACpBc,EAAQK,aAAan2B,WAAW,KAAKH,UAAU,sBAC/Ci2B,EAAQI,SAASl2B,WAAW,KAAKH,UAAU,+BAC3Ci2B,EAAQrL,SAAS7rB,YAAa,GAAMmJ,EAAuB,kBAC3D+tB,EAAQrL,cAAWtpB,IAGrB+qB,OAAQ,WACN,IAAIn0B,EAAS3C,KACTygC,EAAO99B,EAAO89B,KAClB,IAAIA,EAAK5pB,QAAT,CACA4pB,EAAK5pB,SAAU,EAEf,IAAIjC,IAA+C,eAA7BjS,EAAOsqB,YAAYG,QAA0BvZ,EAAQe,kBAAmBjS,EAAOgQ,OAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAGrJwI,EAAQkB,UACVpS,EAAO2T,WAAWrR,GAAG,eAAgB,gBAAiBw7B,EAAKD,eAAgB5rB,GAC3EjS,EAAO2T,WAAWrR,GAAG,gBAAiB,gBAAiBw7B,EAAKS,gBAAiBtsB,GAC7EjS,EAAO2T,WAAWrR,GAAG,aAAc,gBAAiBw7B,EAAKY,aAAczsB,IACjC,eAA7BjS,EAAOsqB,YAAYG,QAC5BzqB,EAAO2T,WAAWrR,GAAGtC,EAAOsqB,YAAYG,MAAO,gBAAiBqT,EAAKD,eAAgB5rB,GACrFjS,EAAO2T,WAAWrR,GAAGtC,EAAOsqB,YAAYM,KAAM,gBAAiBkT,EAAKS,gBAAiBtsB,GACrFjS,EAAO2T,WAAWrR,GAAGtC,EAAOsqB,YAAYO,IAAK,gBAAiBiT,EAAKY,aAAczsB,IAInFjS,EAAO2T,WAAWrR,GAAGtC,EAAOsqB,YAAYM,KAAO,IAAO5qB,EAAOgQ,OAAO8tB,KAAmB,eAAIA,EAAK9X,eAElGoO,QAAS,WACP,IAAIp0B,EAAS3C,KACTygC,EAAO99B,EAAO89B,KAClB,GAAKA,EAAK5pB,QAAV,CAEAlU,EAAO89B,KAAK5pB,SAAU,EAEtB,IAAIjC,IAA+C,eAA7BjS,EAAOsqB,YAAYG,QAA0BvZ,EAAQe,kBAAmBjS,EAAOgQ,OAAO0a,mBAAmB,CAAEC,SAAS,EAAMjiB,SAAS,GAGrJwI,EAAQkB,UACVpS,EAAO2T,WAAWhK,IAAI,eAAgB,gBAAiBm0B,EAAKD,eAAgB5rB,GAC5EjS,EAAO2T,WAAWhK,IAAI,gBAAiB,gBAAiBm0B,EAAKS,gBAAiBtsB,GAC9EjS,EAAO2T,WAAWhK,IAAI,aAAc,gBAAiBm0B,EAAKY,aAAczsB,IAClC,eAA7BjS,EAAOsqB,YAAYG,QAC5BzqB,EAAO2T,WAAWhK,IAAI3J,EAAOsqB,YAAYG,MAAO,gBAAiBqT,EAAKD,eAAgB5rB,GACtFjS,EAAO2T,WAAWhK,IAAI3J,EAAOsqB,YAAYM,KAAM,gBAAiBkT,EAAKS,gBAAiBtsB,GACtFjS,EAAO2T,WAAWhK,IAAI3J,EAAOsqB,YAAYO,IAAK,gBAAiBiT,EAAKY,aAAczsB,IAIpFjS,EAAO2T,WAAWhK,IAAI3J,EAAOsqB,YAAYM,KAAO,IAAO5qB,EAAOgQ,OAAO8tB,KAAmB,eAAIA,EAAK9X,gBAIjG8a,GAAS,CACXvgC,KAAM,OACNyP,OAAQ,CACN8tB,KAAM,CACJ5pB,SAAS,EACTmqB,SAAU,EACVI,SAAU,EACVv3B,QAAQ,EACR65B,eAAgB,wBAChBC,iBAAkB,wBAGtB7jB,OAAQ,WACN,IAAInd,EAAS3C,KACTygC,EAAO,CACT5pB,SAAS,EACTqlB,MAAO,EACP0D,aAAc,EACdqB,WAAW,EACXP,QAAS,CACPrL,cAAUtpB,EACVy1B,gBAAYz1B,EACZ01B,iBAAa11B,EACb+0B,cAAU/0B,EACVg1B,kBAAch1B,EACdi1B,SAAU,GAEZ1R,MAAO,CACLzI,eAAW9a,EACX+a,aAAS/a,EACTob,cAAUpb,EACVub,cAAUvb,EACV61B,UAAM71B,EACN+1B,UAAM/1B,EACN81B,UAAM91B,EACNg2B,UAAMh2B,EACN8J,WAAO9J,EACP+J,YAAQ/J,EACRyb,YAAQzb,EACR0b,YAAQ1b,EACRw1B,aAAc,GACdS,eAAgB,IAElBlX,SAAU,CACRpK,OAAG3U,EACH4U,OAAG5U,EACHk2B,mBAAel2B,EACfm2B,mBAAen2B,EACfo2B,cAAUp2B,IAId,+HAAiI/C,MAAM,KAAKhE,SAAQ,SAAUiM,GAC5JwvB,EAAKxvB,GAAcivB,GAAKjvB,GAAY2O,KAAKjd,MAE3CuO,EAAMkC,OAAOzQ,EAAQ,CACnB89B,KAAMA,IAGR,IAAIvE,EAAQ,EACZx7B,OAAOC,eAAegC,EAAO89B,KAAM,QAAS,CAC1C3/B,IAAK,WACH,OAAOo7B,GAETnc,IAAK,SAAavd,GAChB,GAAI05B,IAAU15B,EAAO,CACnB,IAAIysB,EAAUtsB,EAAO89B,KAAKC,QAAQI,SAAWn+B,EAAO89B,KAAKC,QAAQI,SAAS,QAAK/0B,EAC3E6lB,EAAUjvB,EAAO89B,KAAKC,QAAQrL,SAAW1yB,EAAO89B,KAAKC,QAAQrL,SAAS,QAAKtpB,EAC/EpJ,EAAOmY,KAAK,aAActY,EAAOysB,EAAS2C,GAE5CsK,EAAQ15B,MAIdyC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAO8tB,KAAK5pB,SACrBlU,EAAO89B,KAAK3J,UAGhBpyB,QAAS,WACP,IAAI/B,EAAS3C,KACb2C,EAAO89B,KAAK1J,WAEd6M,WAAY,SAAoBhkC,GAC9B,IAAI+C,EAAS3C,KACR2C,EAAO89B,KAAK5pB,SACjBlU,EAAO89B,KAAKna,aAAa1mB,IAE3BikC,SAAU,SAAkBjkC,GAC1B,IAAI+C,EAAS3C,KACR2C,EAAO89B,KAAK5pB,SACjBlU,EAAO89B,KAAKtW,WAAWvqB,IAEzBkkC,UAAW,SAAmBlkC,GAC5B,IAAI+C,EAAS3C,KACT2C,EAAOgQ,OAAO8tB,KAAK5pB,SAAWlU,EAAO89B,KAAK5pB,SAAWlU,EAAOgQ,OAAO8tB,KAAK52B,QAC1ElH,EAAO89B,KAAK52B,OAAOjK,IAGvBuN,cAAe,WACb,IAAIxK,EAAS3C,KACT2C,EAAO89B,KAAK5pB,SAAWlU,EAAOgQ,OAAO8tB,KAAK5pB,SAC5ClU,EAAO89B,KAAKiC,qBAMhBqB,GAAO,CACTC,YAAa,SAAqB90B,EAAO+0B,QACd,IAApBA,IAA6BA,GAAkB,GAEpD,IAAIthC,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOmiB,KAC3B,GAAqB,qBAAV5lB,GACkB,IAAzBvM,EAAOoU,OAAO/S,OAAlB,CACA,IAAI2S,EAAYhU,EAAOiU,SAAWjU,EAAOgQ,OAAOiE,QAAQC,QAEpDwe,EAAW1e,EACXhU,EAAO2T,WAAWxP,SAAU,IAAOnE,EAAOgQ,OAAiB,WAAI,6BAAgCzD,EAAQ,MACvGvM,EAAOoU,OAAO1H,GAAGH,GAEjBg1B,EAAU7O,EAAS7kB,KAAM,IAAOmC,EAAmB,aAAI,SAAYA,EAAkB,YAAI,UAAaA,EAAmB,aAAI,MAC7H0iB,EAAS3rB,SAASiJ,EAAOwxB,eAAkB9O,EAAS3rB,SAASiJ,EAAOyxB,cAAiB/O,EAAS3rB,SAASiJ,EAAO0xB,gBAChHH,EAAUA,EAAQhiC,IAAImzB,EAAS,KAEV,IAAnB6O,EAAQlgC,QAEZkgC,EAAQv1B,MAAK,SAAU21B,EAAYrV,GACjC,IAAI6R,EAAW14B,EAAE6mB,GACjB6R,EAAS13B,SAASuJ,EAAO0xB,cAEzB,IAAIE,EAAazD,EAASh3B,KAAK,mBAC3BolB,EAAM4R,EAASh3B,KAAK,YACpBqlB,EAAS2R,EAASh3B,KAAK,eACvBslB,EAAQ0R,EAASh3B,KAAK,cAE1BnH,EAAOqsB,UAAU8R,EAAS,GAAK5R,GAAOqV,EAAapV,EAAQC,GAAO,GAAO,WACvE,GAAsB,qBAAXzsB,GAAqC,OAAXA,GAAoBA,KAAWA,GAAWA,EAAOgQ,UAAWhQ,EAAOmf,UAAxG,CAqBA,GApBIyiB,GACFzD,EAASryB,IAAI,mBAAqB,QAAW81B,EAAa,MAC1DzD,EAAS32B,WAAW,qBAEhBglB,IACF2R,EAASh3B,KAAK,SAAUqlB,GACxB2R,EAAS32B,WAAW,gBAElBilB,IACF0R,EAASh3B,KAAK,QAASslB,GACvB0R,EAAS32B,WAAW,eAElB+kB,IACF4R,EAASh3B,KAAK,MAAOolB,GACrB4R,EAAS32B,WAAW,cAIxB22B,EAAS13B,SAASuJ,EAAOyxB,aAAa56B,YAAYmJ,EAAO0xB,cACzDhP,EAAS7kB,KAAM,IAAOmC,EAAqB,gBAAIlJ,SAC3C9G,EAAOgQ,OAAOyK,MAAQ6mB,EAAiB,CACzC,IAAIO,EAAqBnP,EAASvrB,KAAK,2BACvC,GAAIurB,EAAS3rB,SAAS/G,EAAOgQ,OAAO0K,qBAAsB,CACxD,IAAIonB,EAAgB9hC,EAAO2T,WAAWxP,SAAU,6BAAgC09B,EAAqB,WAAe7hC,EAAOgQ,OAA0B,oBAAI,KACzJhQ,EAAOmyB,KAAKkP,YAAYS,EAAcv1B,SAAS,OAC1C,CACL,IAAIw1B,EAAkB/hC,EAAO2T,WAAWxP,SAAU,IAAOnE,EAAOgQ,OAA0B,oBAAI,6BAAgC6xB,EAAqB,MACnJ7hC,EAAOmyB,KAAKkP,YAAYU,EAAgBx1B,SAAS,IAGrDvM,EAAOmY,KAAK,iBAAkBua,EAAS,GAAIyL,EAAS,QAGtDn+B,EAAOmY,KAAK,gBAAiBua,EAAS,GAAIyL,EAAS,SAGvD/L,KAAM,WACJ,IAAIpyB,EAAS3C,KACTsW,EAAa3T,EAAO2T,WACpBua,EAAeluB,EAAOgQ,OACtBoE,EAASpU,EAAOoU,OAChB0E,EAAc9Y,EAAO8Y,YACrB9E,EAAYhU,EAAOiU,SAAWia,EAAaja,QAAQC,QACnDlE,EAASke,EAAaiE,KAEtBtc,EAAgBqY,EAAarY,cAKjC,SAASmsB,EAAWz1B,GAClB,GAAIyH,GACF,GAAIL,EAAWxP,SAAU,IAAO+pB,EAAuB,WAAI,6BAAgC3hB,EAAQ,MAAQlL,OACzG,OAAO,OAEJ,GAAI+S,EAAO7H,GAAU,OAAO,EACnC,OAAO,EAET,SAASsS,EAAWoQ,GAClB,OAAIjb,EACKvO,EAAEwpB,GAAS9nB,KAAK,2BAElB1B,EAAEwpB,GAAS1iB,QAIpB,GApBsB,SAAlBsJ,IACFA,EAAgB,GAkBb7V,EAAOmyB,KAAK8P,qBAAsBjiC,EAAOmyB,KAAK8P,oBAAqB,GACpEjiC,EAAOgQ,OAAOuI,sBAChB5E,EAAWxP,SAAU,IAAO+pB,EAA8B,mBAAIliB,MAAK,SAAUk2B,EAASjT,GACpF,IAAI1iB,EAAQyH,EAAYvO,EAAEwpB,GAAS9nB,KAAK,2BAA6B1B,EAAEwpB,GAAS1iB,QAChFvM,EAAOmyB,KAAKkP,YAAY90B,WAErB,GAAIsJ,EAAgB,EACzB,IAAK,IAAIvY,EAAIwb,EAAaxb,EAAIwb,EAAcjD,EAAevY,GAAK,EAC1D0kC,EAAW1kC,IAAM0C,EAAOmyB,KAAKkP,YAAY/jC,QAG/C0C,EAAOmyB,KAAKkP,YAAYvoB,GAE1B,GAAI9I,EAAOmyB,aACT,GAAItsB,EAAgB,GAAM7F,EAAOoyB,oBAAsBpyB,EAAOoyB,mBAAqB,EAAI,CAMrF,IALA,IAAIC,EAASryB,EAAOoyB,mBAChB1T,EAAM7Y,EACNysB,EAAW5sB,KAAKkU,IAAI9Q,EAAc4V,EAAMhZ,KAAKK,IAAIssB,EAAQ3T,GAAMta,EAAO/S,QACtEkhC,EAAW7sB,KAAKK,IAAI+C,EAAcpD,KAAKK,IAAI2Y,EAAK2T,GAAS,GAEpD5qB,EAAMqB,EAAcjD,EAAe4B,EAAM6qB,EAAU7qB,GAAO,EAC7DuqB,EAAWvqB,IAAQzX,EAAOmyB,KAAKkP,YAAY5pB,GAGjD,IAAK,IAAIE,EAAM4qB,EAAU5qB,EAAMmB,EAAanB,GAAO,EAC7CqqB,EAAWrqB,IAAQ3X,EAAOmyB,KAAKkP,YAAY1pB,OAE5C,CACL,IAAIiD,EAAYjH,EAAWxP,SAAU,IAAO+pB,EAA2B,gBACnEtT,EAAUvZ,OAAS,GAAKrB,EAAOmyB,KAAKkP,YAAYxiB,EAAWjE,IAE/D,IAAIE,EAAYnH,EAAWxP,SAAU,IAAO+pB,EAA2B,gBACnEpT,EAAUzZ,OAAS,GAAKrB,EAAOmyB,KAAKkP,YAAYxiB,EAAW/D,OAMnE0nB,GAAS,CACXjiC,KAAM,OACNyP,OAAQ,CACNmiB,KAAM,CACJje,SAAS,EACTiuB,cAAc,EACdC,mBAAoB,EACpBK,uBAAuB,EAEvBjB,aAAc,cACdE,aAAc,sBACdD,YAAa,qBACbiB,eAAgB,0BAGpBvlB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBmyB,KAAM,CACJ8P,oBAAoB,EACpB7P,KAAMgP,GAAKhP,KAAKnV,KAAKjd,GACrBqhC,YAAaD,GAAKC,YAAYpkB,KAAKjd,OAIzCsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,SAAWlU,EAAOgQ,OAAOgd,gBAC9ChtB,EAAOgQ,OAAOgd,eAAgB,IAGlCS,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,UAAYlU,EAAOgQ,OAAOyK,MAAuC,IAA/Bza,EAAOgQ,OAAO+O,cACrE/e,EAAOmyB,KAAKC,QAGhBuQ,OAAQ,WACN,IAAI3iC,EAAS3C,KACT2C,EAAOgQ,OAAOoX,WAAapnB,EAAOgQ,OAAOgZ,gBAC3ChpB,EAAOmyB,KAAKC,QAGhBxC,OAAQ,WACN,IAAI5vB,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,SACrBlU,EAAOmyB,KAAKC,QAGhBwQ,kBAAmB,WACjB,IAAI5iC,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,SACrBlU,EAAOmyB,KAAKC,QAGhBjU,gBAAiB,WACf,IAAIne,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,UACjBlU,EAAOgQ,OAAOmiB,KAAKsQ,wBAA2BziC,EAAOgQ,OAAOmiB,KAAKsQ,wBAA0BziC,EAAOmyB,KAAK8P,qBACzGjiC,EAAOmyB,KAAKC,QAIlB5nB,cAAe,WACb,IAAIxK,EAAS3C,KACT2C,EAAOgQ,OAAOmiB,KAAKje,UAAYlU,EAAOgQ,OAAOmiB,KAAKsQ,uBACpDziC,EAAOmyB,KAAKC,UAQhByQ,GAAa,CACfC,aAAc,SAAsB/kB,EAAGC,GACrC,IAwBI+kB,EACAC,EAzBAC,EAAgB,WAClB,IAAIX,EACAC,EACAW,EACJ,OAAO,SAAUC,EAAOvjB,GACtB2iB,GAAY,EACZD,EAAWa,EAAM9hC,OACjB,MAAOihC,EAAWC,EAAW,EAC3BW,EAAQZ,EAAWC,GAAY,EAC3BY,EAAMD,IAAUtjB,EAClB2iB,EAAWW,EAEXZ,EAAWY,EAGf,OAAOZ,GAfQ,GAsCnB,OApBAjlC,KAAK0gB,EAAIA,EACT1gB,KAAK2gB,EAAIA,EACT3gB,KAAKg7B,UAAYta,EAAE1c,OAAS,EAO5BhE,KAAK+lC,YAAc,SAAqBzF,GACtC,OAAKA,GAGLqF,EAAKC,EAAa5lC,KAAK0gB,EAAG4f,GAC1BoF,EAAKC,EAAK,GAIArF,EAAKtgC,KAAK0gB,EAAEglB,KAAQ1lC,KAAK2gB,EAAEglB,GAAM3lC,KAAK2gB,EAAE+kB,KAAS1lC,KAAK0gB,EAAEilB,GAAM3lC,KAAK0gB,EAAEglB,IAAQ1lC,KAAK2gB,EAAE+kB,IAR5E,GAUb1lC,MAGTgmC,uBAAwB,SAAgCzlC,GACtD,IAAIoC,EAAS3C,KACR2C,EAAOsjC,WAAWC,SACrBvjC,EAAOsjC,WAAWC,OAASvjC,EAAOgQ,OAAOyK,KACrC,IAAIooB,GAAWC,aAAa9iC,EAAOuU,WAAY3W,EAAE2W,YACjD,IAAIsuB,GAAWC,aAAa9iC,EAAOsU,SAAU1W,EAAE0W,YAGvDsJ,aAAc,SAAsB4lB,EAAgB3lB,GAClD,IAEI4lB,EACAC,EAHA1jC,EAAS3C,KACTsmC,EAAa3jC,EAAOsjC,WAAWM,QAGnC,SAASC,EAAuBjmC,GAK9B,IAAIub,EAAYnZ,EAAO8T,cAAgB9T,EAAOmZ,UAAYnZ,EAAOmZ,UAC7B,UAAhCnZ,EAAOgQ,OAAOszB,WAAWQ,KAC3B9jC,EAAOsjC,WAAWD,uBAAuBzlC,GAGzC8lC,GAAuB1jC,EAAOsjC,WAAWC,OAAOH,aAAajqB,IAG1DuqB,GAAuD,cAAhC1jC,EAAOgQ,OAAOszB,WAAWQ,KACnDL,GAAc7lC,EAAEoc,eAAiBpc,EAAE6b,iBAAmBzZ,EAAOga,eAAiBha,EAAOyZ,gBACrFiqB,GAAwBvqB,EAAYnZ,EAAOyZ,gBAAkBgqB,EAAc7lC,EAAE6b,gBAG3EzZ,EAAOgQ,OAAOszB,WAAWS,UAC3BL,EAAsB9lC,EAAEoc,eAAiB0pB,GAE3C9lC,EAAEkc,eAAe4pB,GACjB9lC,EAAEggB,aAAa8lB,EAAqB1jC,GACpCpC,EAAEsd,oBACFtd,EAAEyc,sBAEJ,GAAI7X,MAAM6Z,QAAQsnB,GAChB,IAAK,IAAIrmC,EAAI,EAAGA,EAAIqmC,EAAWtiC,OAAQ/D,GAAK,EACtCqmC,EAAWrmC,KAAOugB,GAAgB8lB,EAAWrmC,aAAc2C,IAC7D4jC,EAAuBF,EAAWrmC,SAG7BqmC,aAAsB1jC,IAAU4d,IAAiB8lB,GAC1DE,EAAuBF,IAG3B9qB,cAAe,SAAuB3Q,EAAU2V,GAC9C,IAEIvgB,EAFA0C,EAAS3C,KACTsmC,EAAa3jC,EAAOsjC,WAAWM,QAEnC,SAASI,EAAwBpmC,GAC/BA,EAAEib,cAAc3Q,EAAUlI,GACT,IAAbkI,IACFtK,EAAEugB,kBACEvgB,EAAEoS,OAAOsO,YACX/P,EAAMI,UAAS,WACb/Q,EAAE6a,sBAGN7a,EAAE+V,WAAWnJ,eAAc,WACpBm5B,IACD/lC,EAAEoS,OAAOyK,MAAwC,UAAhCza,EAAOgQ,OAAOszB,WAAWQ,IAC5ClmC,EAAE4hB,UAEJ5hB,EAAE4M,qBAIR,GAAIhI,MAAM6Z,QAAQsnB,GAChB,IAAKrmC,EAAI,EAAGA,EAAIqmC,EAAWtiC,OAAQ/D,GAAK,EAClCqmC,EAAWrmC,KAAOugB,GAAgB8lB,EAAWrmC,aAAc2C,IAC7D+jC,EAAwBL,EAAWrmC,SAG9BqmC,aAAsB1jC,IAAU4d,IAAiB8lB,GAC1DK,EAAwBL,KAI1BM,GAAe,CACjB1jC,KAAM,aACNyP,OAAQ,CACNszB,WAAY,CACVM,aAASx6B,EACT26B,SAAS,EACTD,GAAI,UAGR3mB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBsjC,WAAY,CACVM,QAAS5jC,EAAOgQ,OAAOszB,WAAWM,QAClCP,uBAAwBR,GAAWQ,uBAAuBpmB,KAAKjd,GAC/D4d,aAAcilB,GAAWjlB,aAAaX,KAAKjd,GAC3C6Y,cAAegqB,GAAWhqB,cAAcoE,KAAKjd,OAInDsC,GAAI,CACF1B,OAAQ,WACN,IAAIZ,EAAS3C,KACR2C,EAAOsjC,WAAWM,SACnB5jC,EAAOsjC,WAAWC,SACpBvjC,EAAOsjC,WAAWC,YAASn6B,SACpBpJ,EAAOsjC,WAAWC,SAG7B3T,OAAQ,WACN,IAAI5vB,EAAS3C,KACR2C,EAAOsjC,WAAWM,SACnB5jC,EAAOsjC,WAAWC,SACpBvjC,EAAOsjC,WAAWC,YAASn6B,SACpBpJ,EAAOsjC,WAAWC,SAG7BjT,eAAgB,WACd,IAAItwB,EAAS3C,KACR2C,EAAOsjC,WAAWM,SACnB5jC,EAAOsjC,WAAWC,SACpBvjC,EAAOsjC,WAAWC,YAASn6B,SACpBpJ,EAAOsjC,WAAWC,SAG7B3lB,aAAc,SAAsBzE,EAAW0E,GAC7C,IAAI7d,EAAS3C,KACR2C,EAAOsjC,WAAWM,SACvB5jC,EAAOsjC,WAAW1lB,aAAazE,EAAW0E,IAE5ChF,cAAe,SAAuB3Q,EAAU2V,GAC9C,IAAI7d,EAAS3C,KACR2C,EAAOsjC,WAAWM,SACvB5jC,EAAOsjC,WAAWzqB,cAAc3Q,EAAU2V,MAK5CqmB,GAAO,CACTC,gBAAiB,SAAyBjiC,GAExC,OADAA,EAAIiF,KAAK,WAAY,KACdjF,GAETkiC,UAAW,SAAmBliC,EAAKmiC,GAEjC,OADAniC,EAAIiF,KAAK,OAAQk9B,GACVniC,GAEToiC,WAAY,SAAoBpiC,EAAKqiC,GAEnC,OADAriC,EAAIiF,KAAK,aAAco9B,GAChBriC,GAETsiC,UAAW,SAAmBtiC,GAE5B,OADAA,EAAIiF,KAAK,iBAAiB,GACnBjF,GAETuiC,SAAU,SAAkBviC,GAE1B,OADAA,EAAIiF,KAAK,iBAAiB,GACnBjF,GAETwiC,WAAY,SAAoBznC,GAC9B,IAAI+C,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOk0B,KAC3B,GAAkB,KAAdjnC,EAAEm2B,QAAN,CACA,IAAIuR,EAAYl/B,EAAExI,EAAE2L,QAChB5I,EAAOgC,YAAchC,EAAOgC,WAAWg1B,SAAW2N,EAAU37B,GAAGhJ,EAAOgC,WAAWg1B,WAC7Eh3B,EAAOka,QAAUla,EAAOgQ,OAAOyK,MACnCza,EAAOuf,YAELvf,EAAOka,MACTla,EAAOkkC,KAAKU,OAAO50B,EAAO60B,kBAE1B7kC,EAAOkkC,KAAKU,OAAO50B,EAAO80B,mBAG1B9kC,EAAOgC,YAAchC,EAAOgC,WAAWi1B,SAAW0N,EAAU37B,GAAGhJ,EAAOgC,WAAWi1B,WAC7Ej3B,EAAOia,cAAgBja,EAAOgQ,OAAOyK,MACzCza,EAAO0f,YAEL1f,EAAOia,YACTja,EAAOkkC,KAAKU,OAAO50B,EAAO+0B,mBAE1B/kC,EAAOkkC,KAAKU,OAAO50B,EAAOg1B,mBAG1BhlC,EAAOiC,YAAc0iC,EAAU37B,GAAI,IAAOhJ,EAAOgQ,OAAO/N,WAAsB,cAChF0iC,EAAU,GAAG9M,UAGjB+M,OAAQ,SAAgBK,GACtB,IAAIjlC,EAAS3C,KACT6nC,EAAellC,EAAOkkC,KAAKiB,WACH,IAAxBD,EAAa7jC,SACjB6jC,EAAap/B,KAAK,IAClBo/B,EAAap/B,KAAKm/B,KAEpBG,iBAAkB,WAChB,IAAIplC,EAAS3C,KAEb,IAAI2C,EAAOgQ,OAAOyK,KAAlB,CACA,IAAI2W,EAAMpxB,EAAOgC,WACbg1B,EAAU5F,EAAI4F,QACdC,EAAU7F,EAAI6F,QAEdA,GAAWA,EAAQ51B,OAAS,IAC1BrB,EAAOia,YACTja,EAAOkkC,KAAKM,UAAUvN,GAEtBj3B,EAAOkkC,KAAKO,SAASxN,IAGrBD,GAAWA,EAAQ31B,OAAS,IAC1BrB,EAAOka,MACTla,EAAOkkC,KAAKM,UAAUxN,GAEtBh3B,EAAOkkC,KAAKO,SAASzN,MAI3BqO,iBAAkB,WAChB,IAAIrlC,EAAS3C,KACT2S,EAAShQ,EAAOgQ,OAAOk0B,KACvBlkC,EAAOiC,YAAcjC,EAAOgQ,OAAO/N,WAAWm4B,WAAap6B,EAAOiC,WAAWk2B,SAAWn4B,EAAOiC,WAAWk2B,QAAQ92B,QACpHrB,EAAOiC,WAAWk2B,QAAQnsB,MAAK,SAAU6sB,EAAayM,GACpD,IAAIC,EAAY9/B,EAAE6/B,GAClBtlC,EAAOkkC,KAAKC,gBAAgBoB,GAC5BvlC,EAAOkkC,KAAKE,UAAUmB,EAAW,UACjCvlC,EAAOkkC,KAAKI,WAAWiB,EAAWv1B,EAAOw1B,wBAAwB9iC,QAAQ,YAAa6iC,EAAUh5B,QAAU,QAIhHkhB,KAAM,WACJ,IAAIztB,EAAS3C,KAEb2C,EAAOkC,IAAI0K,OAAO5M,EAAOkkC,KAAKiB,YAG9B,IACInO,EACAC,EAFAjnB,EAAShQ,EAAOgQ,OAAOk0B,KAGvBlkC,EAAOgC,YAAchC,EAAOgC,WAAWg1B,UACzCA,EAAUh3B,EAAOgC,WAAWg1B,SAE1Bh3B,EAAOgC,YAAchC,EAAOgC,WAAWi1B,UACzCA,EAAUj3B,EAAOgC,WAAWi1B,SAE1BD,IACFh3B,EAAOkkC,KAAKC,gBAAgBnN,GAC5Bh3B,EAAOkkC,KAAKE,UAAUpN,EAAS,UAC/Bh3B,EAAOkkC,KAAKI,WAAWtN,EAAShnB,EAAO80B,kBACvC9N,EAAQ10B,GAAG,UAAWtC,EAAOkkC,KAAKQ,aAEhCzN,IACFj3B,EAAOkkC,KAAKC,gBAAgBlN,GAC5Bj3B,EAAOkkC,KAAKE,UAAUnN,EAAS,UAC/Bj3B,EAAOkkC,KAAKI,WAAWrN,EAASjnB,EAAOg1B,kBACvC/N,EAAQ30B,GAAG,UAAWtC,EAAOkkC,KAAKQ,aAIhC1kC,EAAOiC,YAAcjC,EAAOgQ,OAAO/N,WAAWm4B,WAAap6B,EAAOiC,WAAWk2B,SAAWn4B,EAAOiC,WAAWk2B,QAAQ92B,QACpHrB,EAAOiC,WAAWC,IAAII,GAAG,UAAY,IAAOtC,EAAOgQ,OAAO/N,WAAsB,YAAIjC,EAAOkkC,KAAKQ,aAGpG3iC,QAAS,WACP,IAGIi1B,EACAC,EAJAj3B,EAAS3C,KACT2C,EAAOkkC,KAAKiB,YAAcnlC,EAAOkkC,KAAKiB,WAAW9jC,OAAS,GAAKrB,EAAOkkC,KAAKiB,WAAWr+B,SAItF9G,EAAOgC,YAAchC,EAAOgC,WAAWg1B,UACzCA,EAAUh3B,EAAOgC,WAAWg1B,SAE1Bh3B,EAAOgC,YAAchC,EAAOgC,WAAWi1B,UACzCA,EAAUj3B,EAAOgC,WAAWi1B,SAE1BD,GACFA,EAAQrtB,IAAI,UAAW3J,EAAOkkC,KAAKQ,YAEjCzN,GACFA,EAAQttB,IAAI,UAAW3J,EAAOkkC,KAAKQ,YAIjC1kC,EAAOiC,YAAcjC,EAAOgQ,OAAO/N,WAAWm4B,WAAap6B,EAAOiC,WAAWk2B,SAAWn4B,EAAOiC,WAAWk2B,QAAQ92B,QACpHrB,EAAOiC,WAAWC,IAAIyH,IAAI,UAAY,IAAO3J,EAAOgQ,OAAO/N,WAAsB,YAAIjC,EAAOkkC,KAAKQ,cAInGe,GAAO,CACTllC,KAAM,OACNyP,OAAQ,CACNk0B,KAAM,CACJhwB,SAAS,EACTwxB,kBAAmB,sBACnBV,iBAAkB,iBAClBF,iBAAkB,aAClBC,kBAAmB,0BACnBF,iBAAkB,yBAClBW,wBAAyB,0BAG7BroB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBkkC,KAAM,CACJiB,WAAY1/B,EAAG,gBAAoBzF,EAAOgQ,OAAOk0B,KAAsB,kBAAI,yDAG/EnmC,OAAOsQ,KAAK61B,IAAM7hC,SAAQ,SAAUiM,GAClCtO,EAAOkkC,KAAK51B,GAAc41B,GAAK51B,GAAY2O,KAAKjd,OAGpDsC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACR2C,EAAOgQ,OAAOk0B,KAAKhwB,UACxBlU,EAAOkkC,KAAKzW,OACZztB,EAAOkkC,KAAKkB,qBAEdzN,OAAQ,WACN,IAAI33B,EAAS3C,KACR2C,EAAOgQ,OAAOk0B,KAAKhwB,SACxBlU,EAAOkkC,KAAKkB,oBAEdxN,SAAU,WACR,IAAI53B,EAAS3C,KACR2C,EAAOgQ,OAAOk0B,KAAKhwB,SACxBlU,EAAOkkC,KAAKkB,oBAEdO,iBAAkB,WAChB,IAAI3lC,EAAS3C,KACR2C,EAAOgQ,OAAOk0B,KAAKhwB,SACxBlU,EAAOkkC,KAAKmB,oBAEdtjC,QAAS,WACP,IAAI/B,EAAS3C,KACR2C,EAAOgQ,OAAOk0B,KAAKhwB,SACxBlU,EAAOkkC,KAAKniC,aAKd6jC,GAAU,CACZnY,KAAM,WACJ,IAAIztB,EAAS3C,KACb,GAAK2C,EAAOgQ,OAAOnL,QAAnB,CACA,IAAKH,EAAIG,UAAYH,EAAIG,QAAQghC,UAG/B,OAFA7lC,EAAOgQ,OAAOnL,QAAQqP,SAAU,OAChClU,EAAOgQ,OAAO81B,eAAe5xB,SAAU,GAGzC,IAAIrP,EAAU7E,EAAO6E,QACrBA,EAAQ2W,aAAc,EACtB3W,EAAQkhC,MAAQH,GAAQI,iBACnBnhC,EAAQkhC,MAAMr+B,KAAQ7C,EAAQkhC,MAAMlmC,SACzCgF,EAAQohC,cAAc,EAAGphC,EAAQkhC,MAAMlmC,MAAOG,EAAOgQ,OAAOyL,oBACvDzb,EAAOgQ,OAAOnL,QAAQqhC,cACzBxhC,EAAIlB,iBAAiB,WAAYxD,EAAO6E,QAAQshC,uBAGpDpkC,QAAS,WACP,IAAI/B,EAAS3C,KACR2C,EAAOgQ,OAAOnL,QAAQqhC,cACzBxhC,EAAIjB,oBAAoB,WAAYzD,EAAO6E,QAAQshC,qBAGvDA,mBAAoB,WAClB,IAAInmC,EAAS3C,KACb2C,EAAO6E,QAAQkhC,MAAQH,GAAQI,gBAC/BhmC,EAAO6E,QAAQohC,cAAcjmC,EAAOgQ,OAAO0I,MAAO1Y,EAAO6E,QAAQkhC,MAAMlmC,OAAO,IAEhFmmC,cAAe,WACb,IAAII,EAAY1hC,EAAIF,SAAS6hC,SAAS5jC,MAAM,GAAG4D,MAAM,KAAKgE,QAAO,SAAUi8B,GAAQ,MAAgB,KAATA,KACtFrO,EAAQmO,EAAU/kC,OAClBqG,EAAM0+B,EAAUnO,EAAQ,GACxBp4B,EAAQumC,EAAUnO,EAAQ,GAC9B,MAAO,CAAEvwB,IAAKA,EAAK7H,MAAOA,IAE5B0mC,WAAY,SAAoB7+B,EAAK6E,GACnC,IAAIvM,EAAS3C,KACb,GAAK2C,EAAO6E,QAAQ2W,aAAgBxb,EAAOgQ,OAAOnL,QAAQqP,QAA1D,CACA,IAAIiC,EAAQnW,EAAOoU,OAAO1H,GAAGH,GACzB1M,EAAQ+lC,GAAQY,QAAQrwB,EAAMhP,KAAK,iBAClCzC,EAAIF,SAAS6hC,SAASI,SAAS/+B,KAClC7H,EAAQ6H,EAAM,IAAM7H,GAEtB,IAAI6mC,EAAehiC,EAAIG,QAAQ8hC,MAC3BD,GAAgBA,EAAa7mC,QAAUA,IAGvCG,EAAOgQ,OAAOnL,QAAQqhC,aACxBxhC,EAAIG,QAAQqhC,aAAa,CAAErmC,MAAOA,GAAS,KAAMA,GAEjD6E,EAAIG,QAAQghC,UAAU,CAAEhmC,MAAOA,GAAS,KAAMA,MAGlD2mC,QAAS,SAAiBv6B,GACxB,OAAOA,EAAK0D,WACTjN,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAEpBujC,cAAe,SAAuBvtB,EAAO7Y,EAAOue,GAClD,IAAIpe,EAAS3C,KACb,GAAIwC,EACF,IAAK,IAAIvC,EAAI,EAAG+D,EAASrB,EAAOoU,OAAO/S,OAAQ/D,EAAI+D,EAAQ/D,GAAK,EAAG,CACjE,IAAI6Y,EAAQnW,EAAOoU,OAAO1H,GAAGpP,GACzBspC,EAAehB,GAAQY,QAAQrwB,EAAMhP,KAAK,iBAC9C,GAAIy/B,IAAiB/mC,IAAUsW,EAAMpP,SAAS/G,EAAOgQ,OAAO0K,qBAAsB,CAChF,IAAInO,EAAQ4J,EAAM5J,QAClBvM,EAAO2e,QAAQpS,EAAOmM,EAAO0F,SAIjCpe,EAAO2e,QAAQ,EAAGjG,EAAO0F,KAK3ByoB,GAAY,CACdtmC,KAAM,UACNyP,OAAQ,CACNnL,QAAS,CACPqP,SAAS,EACTgyB,cAAc,EACdx+B,IAAK,WAGTyV,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB6E,QAAS,CACP4oB,KAAMmY,GAAQnY,KAAKxQ,KAAKjd,GACxBumC,WAAYX,GAAQW,WAAWtpB,KAAKjd,GACpCmmC,mBAAoBP,GAAQO,mBAAmBlpB,KAAKjd,GACpDimC,cAAeL,GAAQK,cAAchpB,KAAKjd,GAC1C+B,QAAS6jC,GAAQ7jC,QAAQkb,KAAKjd,OAIpCsC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAOnL,QAAQqP,SACxBlU,EAAO6E,QAAQ4oB,QAGnB1rB,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAOgQ,OAAOnL,QAAQqP,SACxBlU,EAAO6E,QAAQ9C,WAGnByI,cAAe,WACb,IAAIxK,EAAS3C,KACT2C,EAAO6E,QAAQ2W,aACjBxb,EAAO6E,QAAQ0hC,WAAWvmC,EAAOgQ,OAAOnL,QAAQ6C,IAAK1H,EAAO8Y,gBAMhEguB,GAAiB,CACnBC,YAAa,WACX,IAAI/mC,EAAS3C,KACT2pC,EAAU3jC,EAAImB,SAASC,KAAK/B,QAAQ,IAAK,IACzCukC,EAAkBjnC,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAAa3R,KAAK,aAChE,GAAI6/B,IAAYC,EAAiB,CAC/B,IAAI5nB,EAAWrf,EAAO2T,WAAWxP,SAAU,IAAOnE,EAAOgQ,OAAiB,WAAI,eAAkBg3B,EAAU,MAAQz6B,QAClH,GAAwB,qBAAb8S,EAA4B,OACvCrf,EAAO2e,QAAQU,KAGnB6nB,QAAS,WACP,IAAIlnC,EAAS3C,KACb,GAAK2C,EAAO8lC,eAAetqB,aAAgBxb,EAAOgQ,OAAO81B,eAAe5xB,QACxE,GAAIlU,EAAOgQ,OAAO81B,eAAeI,cAAgBxhC,EAAIG,SAAWH,EAAIG,QAAQqhC,aAC1ExhC,EAAIG,QAAQqhC,aAAa,KAAM,KAAQ,IAAOlmC,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAAa3R,KAAK,eAAkB,OACrG,CACL,IAAIgP,EAAQnW,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aAChCrU,EAAO0R,EAAMhP,KAAK,cAAgBgP,EAAMhP,KAAK,gBACjD9D,EAAImB,SAASC,KAAOA,GAAQ,KAGhCgpB,KAAM,WACJ,IAAIztB,EAAS3C,KACb,MAAK2C,EAAOgQ,OAAO81B,eAAe5xB,SAAYlU,EAAOgQ,OAAOnL,SAAW7E,EAAOgQ,OAAOnL,QAAQqP,SAA7F,CACAlU,EAAO8lC,eAAetqB,aAAc,EACpC,IAAI/W,EAAOpB,EAAImB,SAASC,KAAK/B,QAAQ,IAAK,IAC1C,GAAI+B,EAEF,IADA,IAAIiU,EAAQ,EACHpb,EAAI,EAAG+D,EAASrB,EAAOoU,OAAO/S,OAAQ/D,EAAI+D,EAAQ/D,GAAK,EAAG,CACjE,IAAI6Y,EAAQnW,EAAOoU,OAAO1H,GAAGpP,GACzB6pC,EAAYhxB,EAAMhP,KAAK,cAAgBgP,EAAMhP,KAAK,gBACtD,GAAIggC,IAAc1iC,IAAS0R,EAAMpP,SAAS/G,EAAOgQ,OAAO0K,qBAAsB,CAC5E,IAAInO,EAAQ4J,EAAM5J,QAClBvM,EAAO2e,QAAQpS,EAAOmM,EAAO1Y,EAAOgQ,OAAOyL,oBAAoB,IAIjEzb,EAAOgQ,OAAO81B,eAAesB,YAC/B3hC,EAAEf,GAAKpC,GAAG,aAActC,EAAO8lC,eAAeiB,eAGlDhlC,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAOgQ,OAAO81B,eAAesB,YAC/B3hC,EAAEf,GAAKiF,IAAI,aAAc3J,EAAO8lC,eAAeiB,eAIjDM,GAAmB,CACrB9mC,KAAM,kBACNyP,OAAQ,CACN81B,eAAgB,CACd5xB,SAAS,EACTgyB,cAAc,EACdkB,YAAY,IAGhBjqB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB8lC,eAAgB,CACdtqB,aAAa,EACbiS,KAAMqZ,GAAerZ,KAAKxQ,KAAKjd,GAC/B+B,QAAS+kC,GAAe/kC,QAAQkb,KAAKjd,GACrCknC,QAASJ,GAAeI,QAAQjqB,KAAKjd,GACrC+mC,YAAaD,GAAeC,YAAY9pB,KAAKjd,OAInDsC,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAO81B,eAAe5xB,SAC/BlU,EAAO8lC,eAAerY,QAG1B1rB,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAOgQ,OAAO81B,eAAe5xB,SAC/BlU,EAAO8lC,eAAe/jC,WAG1ByI,cAAe,WACb,IAAIxK,EAAS3C,KACT2C,EAAO8lC,eAAetqB,aACxBxb,EAAO8lC,eAAeoB,aAQ1BI,GAAW,CACbtd,IAAK,WACH,IAAIhqB,EAAS3C,KACTkqC,EAAiBvnC,EAAOoU,OAAO1H,GAAG1M,EAAO8Y,aACzClK,EAAQ5O,EAAOgQ,OAAO6Z,SAASjb,MAC/B24B,EAAepgC,KAAK,0BACtByH,EAAQ24B,EAAepgC,KAAK,yBAA2BnH,EAAOgQ,OAAO6Z,SAASjb,OAEhFvJ,aAAarF,EAAO6pB,SAAS4M,SAC7Bz2B,EAAO6pB,SAAS4M,QAAUloB,EAAMI,UAAS,WACnC3O,EAAOgQ,OAAO6Z,SAAS2d,iBACrBxnC,EAAOgQ,OAAOyK,MAChBza,EAAOwf,UACPxf,EAAO0f,UAAU1f,EAAOgQ,OAAO0I,OAAO,GAAM,GAC5C1Y,EAAOmY,KAAK,aACFnY,EAAOia,YAGPja,EAAOgQ,OAAO6Z,SAAS4d,gBAIjCznC,EAAO6pB,SAAS8M,QAHhB32B,EAAO2e,QAAQ3e,EAAOoU,OAAO/S,OAAS,EAAGrB,EAAOgQ,OAAO0I,OAAO,GAAM,GACpE1Y,EAAOmY,KAAK,cAJZnY,EAAO0f,UAAU1f,EAAOgQ,OAAO0I,OAAO,GAAM,GAC5C1Y,EAAOmY,KAAK,aAOLnY,EAAOgQ,OAAOyK,MACvBza,EAAOwf,UACPxf,EAAOuf,UAAUvf,EAAOgQ,OAAO0I,OAAO,GAAM,GAC5C1Y,EAAOmY,KAAK,aACFnY,EAAOka,MAGPla,EAAOgQ,OAAO6Z,SAAS4d,gBAIjCznC,EAAO6pB,SAAS8M,QAHhB32B,EAAO2e,QAAQ,EAAG3e,EAAOgQ,OAAO0I,OAAO,GAAM,GAC7C1Y,EAAOmY,KAAK,cAJZnY,EAAOuf,UAAUvf,EAAOgQ,OAAO0I,OAAO,GAAM,GAC5C1Y,EAAOmY,KAAK,eAObvJ,IAEL6b,MAAO,WACL,IAAIzqB,EAAS3C,KACb,MAAuC,qBAA5B2C,EAAO6pB,SAAS4M,WACvBz2B,EAAO6pB,SAASC,UACpB9pB,EAAO6pB,SAASC,SAAU,EAC1B9pB,EAAOmY,KAAK,iBACZnY,EAAO6pB,SAASG,OACT,KAET2M,KAAM,WACJ,IAAI32B,EAAS3C,KACb,QAAK2C,EAAO6pB,SAASC,UACkB,qBAA5B9pB,EAAO6pB,SAAS4M,UAEvBz2B,EAAO6pB,SAAS4M,UAClBpxB,aAAarF,EAAO6pB,SAAS4M,SAC7Bz2B,EAAO6pB,SAAS4M,aAAUrtB,GAE5BpJ,EAAO6pB,SAASC,SAAU,EAC1B9pB,EAAOmY,KAAK,iBACL,KAETuvB,MAAO,SAAehvB,GACpB,IAAI1Y,EAAS3C,KACR2C,EAAO6pB,SAASC,UACjB9pB,EAAO6pB,SAASE,SAChB/pB,EAAO6pB,SAAS4M,SAAWpxB,aAAarF,EAAO6pB,SAAS4M,SAC5Dz2B,EAAO6pB,SAASE,QAAS,EACX,IAAVrR,GAAgB1Y,EAAOgQ,OAAO6Z,SAAS8d,mBAIzC3nC,EAAO2T,WAAW,GAAGnQ,iBAAiB,gBAAiBxD,EAAO6pB,SAASkW,iBACvE//B,EAAO2T,WAAW,GAAGnQ,iBAAiB,sBAAuBxD,EAAO6pB,SAASkW,mBAJ7E//B,EAAO6pB,SAASE,QAAS,EACzB/pB,EAAO6pB,SAASG,WAQlB4d,GAAa,CACfrnC,KAAM,WACNyP,OAAQ,CACN6Z,SAAU,CACR3V,SAAS,EACTtF,MAAO,IACP+4B,mBAAmB,EACnBE,sBAAsB,EACtBJ,iBAAiB,EACjBD,kBAAkB,IAGtBrqB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB6pB,SAAU,CACRC,SAAS,EACTC,QAAQ,EACRC,IAAKsd,GAAStd,IAAI/M,KAAKjd,GACvByqB,MAAO6c,GAAS7c,MAAMxN,KAAKjd,GAC3B22B,KAAM2Q,GAAS3Q,KAAK1Z,KAAKjd,GACzB0nC,MAAOJ,GAASI,MAAMzqB,KAAKjd,GAC3B+/B,gBAAiB,SAAyB9iC,GACnC+C,IAAUA,EAAOmf,WAAcnf,EAAO2T,YACvC1W,EAAE2L,SAAWvL,OACjB2C,EAAO2T,WAAW,GAAGlQ,oBAAoB,gBAAiBzD,EAAO6pB,SAASkW,iBAC1E//B,EAAO2T,WAAW,GAAGlQ,oBAAoB,sBAAuBzD,EAAO6pB,SAASkW,iBAChF//B,EAAO6pB,SAASE,QAAS,EACpB/pB,EAAO6pB,SAASC,QAGnB9pB,EAAO6pB,SAASG,MAFhBhqB,EAAO6pB,SAAS8M,aAQ1Br0B,GAAI,CACFmrB,KAAM,WACJ,IAAIztB,EAAS3C,KACT2C,EAAOgQ,OAAO6Z,SAAS3V,SACzBlU,EAAO6pB,SAASY,SAGpBqd,sBAAuB,SAA+BpvB,EAAOkG,GAC3D,IAAI5e,EAAS3C,KACT2C,EAAO6pB,SAASC,UACdlL,IAAa5e,EAAOgQ,OAAO6Z,SAASge,qBACtC7nC,EAAO6pB,SAAS6d,MAAMhvB,GAEtB1Y,EAAO6pB,SAAS8M,SAItBoR,gBAAiB,WACf,IAAI/nC,EAAS3C,KACT2C,EAAO6pB,SAASC,UACd9pB,EAAOgQ,OAAO6Z,SAASge,qBACzB7nC,EAAO6pB,SAAS8M,OAEhB32B,EAAO6pB,SAAS6d,UAItB3lC,QAAS,WACP,IAAI/B,EAAS3C,KACT2C,EAAO6pB,SAASC,SAClB9pB,EAAO6pB,SAAS8M,UAMpBqR,GAAO,CACTpqB,aAAc,WAGZ,IAFA,IAAI5d,EAAS3C,KACT+W,EAASpU,EAAOoU,OACX9W,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAAG,CACzC,IAAIo1B,EAAW1yB,EAAOoU,OAAO1H,GAAGpP,GAC5B6N,EAASunB,EAAS,GAAG3Z,kBACrBkvB,GAAM98B,EACLnL,EAAOgQ,OAAO0N,mBAAoBuqB,GAAMjoC,EAAOmZ,WACpD,IAAI+uB,EAAK,EACJloC,EAAOsT,iBACV40B,EAAKD,EACLA,EAAK,GAEP,IAAIE,EAAenoC,EAAOgQ,OAAOo4B,WAAWC,UACxC3yB,KAAKK,IAAI,EAAIL,KAAK4B,IAAIob,EAAS,GAAG7Y,UAAW,GAC7C,EAAInE,KAAKkU,IAAIlU,KAAKK,IAAI2c,EAAS,GAAG7Y,UAAW,GAAI,GACrD6Y,EACG5mB,IAAI,CACHyvB,QAAS4M,IAEVrgC,UAAW,eAAiBmgC,EAAK,OAASC,EAAK,cAGtDrvB,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACT+W,EAASpU,EAAOoU,OAChBT,EAAa3T,EAAO2T,WAExB,GADAS,EAAOnM,WAAWC,GACdlI,EAAOgQ,OAAO0N,kBAAiC,IAAbxV,EAAgB,CACpD,IAAIogC,GAAiB,EACrBl0B,EAAO5J,eAAc,WACnB,IAAI89B,GACCtoC,IAAUA,EAAOmf,UAAtB,CACAmpB,GAAiB,EACjBtoC,EAAOye,WAAY,EAEnB,IADA,IAAI8pB,EAAgB,CAAC,sBAAuB,iBACnCjrC,EAAI,EAAGA,EAAIirC,EAAclnC,OAAQ/D,GAAK,EAC7CqW,EAAW3J,QAAQu+B,EAAcjrC,WAOvCkrC,GAAa,CACfjoC,KAAM,cACNyP,OAAQ,CACNo4B,WAAY,CACVC,WAAW,IAGflrB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBooC,WAAY,CACVxqB,aAAcoqB,GAAKpqB,aAAaX,KAAKjd,GACrC6Y,cAAemvB,GAAKnvB,cAAcoE,KAAKjd,OAI7CsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACb,GAA6B,SAAzB2C,EAAOgQ,OAAOuH,OAAlB,CACAvX,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,QACjE,IAAIgjB,EAAkB,CACpBnd,cAAe,EACfJ,gBAAiB,EACjBc,eAAgB,EAChB+B,qBAAqB,EACrBvD,aAAc,EACd2I,kBAAkB,GAEpBnP,EAAMkC,OAAOzQ,EAAOgQ,OAAQgjB,GAC5BzkB,EAAMkC,OAAOzQ,EAAOqrB,eAAgB2H,KAEtCpV,aAAc,WACZ,IAAI5d,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOooC,WAAWxqB,gBAEpB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOooC,WAAWvvB,cAAc3Q,MAKlCugC,GAAO,CACT7qB,aAAc,WACZ,IAYI8qB,EAZA1oC,EAAS3C,KACT6E,EAAMlC,EAAOkC,IACbyR,EAAa3T,EAAO2T,WACpBS,EAASpU,EAAOoU,OAChBu0B,EAAc3oC,EAAOkT,MACrB01B,EAAe5oC,EAAOmT,OACtBU,EAAM7T,EAAO8T,aACbF,EAAa5T,EAAOyT,KACpBzD,EAAShQ,EAAOgQ,OAAO64B,WACvBv1B,EAAetT,EAAOsT,eACtBU,EAAYhU,EAAOiU,SAAWjU,EAAOgQ,OAAOiE,QAAQC,QACpD40B,EAAgB,EAEhB94B,EAAO+4B,SACLz1B,GACFo1B,EAAgB/0B,EAAW9F,KAAK,uBACH,IAAzB66B,EAAcrnC,SAChBqnC,EAAgBjjC,EAAE,0CAClBkO,EAAW/G,OAAO87B,IAEpBA,EAAc58B,IAAI,CAAEqH,OAASw1B,EAAc,SAE3CD,EAAgBxmC,EAAI2L,KAAK,uBACI,IAAzB66B,EAAcrnC,SAChBqnC,EAAgBjjC,EAAE,0CAClBvD,EAAI0K,OAAO87B,MAIjB,IAAK,IAAIprC,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAAG,CACzC,IAAIo1B,EAAWte,EAAO1H,GAAGpP,GACrBuhB,EAAavhB,EACb0W,IACF6K,EAAarL,SAASkf,EAASvrB,KAAK,2BAA4B,KAElE,IAAI6hC,EAA0B,GAAbnqB,EACboqB,EAAQvzB,KAAKC,MAAMqzB,EAAa,KAChCn1B,IACFm1B,GAAcA,EACdC,EAAQvzB,KAAKC,OAAOqzB,EAAa,MAEnC,IAAInvB,EAAWnE,KAAKK,IAAIL,KAAKkU,IAAI8I,EAAS,GAAG7Y,SAAU,IAAK,GACxDouB,EAAK,EACLC,EAAK,EACLgB,EAAK,EACLrqB,EAAa,IAAM,GACrBopB,EAAc,GAARgB,EAAYr1B,EAClBs1B,EAAK,IACKrqB,EAAa,GAAK,IAAM,GAClCopB,EAAK,EACLiB,EAAc,GAARD,EAAYr1B,IACRiL,EAAa,GAAK,IAAM,GAClCopB,EAAKr0B,EAAsB,EAARq1B,EAAYr1B,EAC/Bs1B,EAAKt1B,IACKiL,EAAa,GAAK,IAAM,IAClCopB,GAAMr0B,EACNs1B,EAAM,EAAIt1B,EAA4B,EAAbA,EAAiBq1B,GAExCp1B,IACFo0B,GAAMA,GAGH30B,IACH40B,EAAKD,EACLA,EAAK,GAGP,IAAIngC,EAAY,YAAcwL,EAAe,GAAK01B,GAAc,iBAAmB11B,EAAe01B,EAAa,GAAK,oBAAsBf,EAAK,OAASC,EAAK,OAASgB,EAAK,MAM3K,GALIrvB,GAAY,GAAKA,GAAY,IAC/BivB,EAA8B,GAAbjqB,EAA+B,GAAXhF,EACjChG,IAAOi1B,EAA+B,IAAbjqB,EAA+B,GAAXhF,IAEnD6Y,EAAS5qB,UAAUA,GACfkI,EAAOm5B,aAAc,CAEvB,IAAIC,EAAe91B,EAAeof,EAAS7kB,KAAK,6BAA+B6kB,EAAS7kB,KAAK,4BACzFw7B,EAAc/1B,EAAeof,EAAS7kB,KAAK,8BAAgC6kB,EAAS7kB,KAAK,+BACjE,IAAxBu7B,EAAa/nC,SACf+nC,EAAe3jC,EAAG,oCAAuC6N,EAAe,OAAS,OAAS,YAC1Fof,EAAS9lB,OAAOw8B,IAES,IAAvBC,EAAYhoC,SACdgoC,EAAc5jC,EAAG,oCAAuC6N,EAAe,QAAU,UAAY,YAC7Fof,EAAS9lB,OAAOy8B,IAEdD,EAAa/nC,SAAU+nC,EAAa,GAAG/kC,MAAMk3B,QAAU7lB,KAAKK,KAAK8D,EAAU,IAC3EwvB,EAAYhoC,SAAUgoC,EAAY,GAAGhlC,MAAMk3B,QAAU7lB,KAAKK,IAAI8D,EAAU,KAUhF,GAPAlG,EAAW7H,IAAI,CACb,2BAA6B,YAAe8H,EAAa,EAAK,KAC9D,wBAA0B,YAAeA,EAAa,EAAK,KAC3D,uBAAyB,YAAeA,EAAa,EAAK,KAC1D,mBAAqB,YAAeA,EAAa,EAAK,OAGpD5D,EAAO+4B,OACT,GAAIz1B,EACFo1B,EAAc5gC,UAAW,qBAAwB6gC,EAAc,EAAK34B,EAAOs5B,cAAgB,QAAWX,EAAc,EAAK,0CAA6C34B,EAAkB,YAAI,SACvL,CACL,IAAIu5B,EAAc7zB,KAAK4B,IAAIwxB,GAA6D,GAA3CpzB,KAAKC,MAAMD,KAAK4B,IAAIwxB,GAAiB,IAC9ErF,EAAa,KACd/tB,KAAK8zB,IAAmB,EAAdD,EAAkB7zB,KAAK+Q,GAAM,KAAO,EAC5C/Q,KAAK+zB,IAAmB,EAAdF,EAAkB7zB,KAAK+Q,GAAM,KAAO,GAE/CijB,EAAS15B,EAAO25B,YAChBC,EAAS55B,EAAO25B,YAAclG,EAC9Bt4B,EAAS6E,EAAOs5B,aACpBZ,EAAc5gC,UAAW,WAAa4hC,EAAS,QAAUE,EAAS,uBAA0BhB,EAAe,EAAKz9B,GAAU,QAAWy9B,EAAe,EAAIgB,EAAU,uBAGtK,IAAIC,EAAWx3B,EAAQC,UAAYD,EAAQK,aAAiBkB,EAAa,EAAK,EAC9ED,EACG7L,UAAW,qBAAuB+hC,EAAU,gBAAkB7pC,EAAOsT,eAAiB,EAAIw1B,GAAiB,iBAAmB9oC,EAAOsT,gBAAkBw1B,EAAgB,GAAK,SAEjLjwB,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACT6E,EAAMlC,EAAOkC,IACbkS,EAASpU,EAAOoU,OACpBA,EACGnM,WAAWC,GACX2F,KAAK,gHACL5F,WAAWC,GACVlI,EAAOgQ,OAAO64B,WAAWE,SAAW/oC,EAAOsT,gBAC7CpR,EAAI2L,KAAK,uBAAuB5F,WAAWC,KAK7C4hC,GAAa,CACfvpC,KAAM,cACNyP,OAAQ,CACN64B,WAAY,CACVM,cAAc,EACdJ,QAAQ,EACRO,aAAc,GACdK,YAAa,MAGjBxsB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnB6oC,WAAY,CACVjrB,aAAc6qB,GAAK7qB,aAAaX,KAAKjd,GACrC6Y,cAAe4vB,GAAK5vB,cAAcoE,KAAKjd,OAI7CsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACb,GAA6B,SAAzB2C,EAAOgQ,OAAOuH,OAAlB,CACAvX,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,QACjEhQ,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,MACjE,IAAIgjB,EAAkB,CACpBnd,cAAe,EACfJ,gBAAiB,EACjBc,eAAgB,EAChB+B,qBAAqB,EACrB2O,gBAAiB,EACjBlS,aAAc,EACdsC,gBAAgB,EAChBqG,kBAAkB,GAEpBnP,EAAMkC,OAAOzQ,EAAOgQ,OAAQgjB,GAC5BzkB,EAAMkC,OAAOzQ,EAAOqrB,eAAgB2H,KAEtCpV,aAAc,WACZ,IAAI5d,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAO6oC,WAAWjrB,gBAEpB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAO6oC,WAAWhwB,cAAc3Q,MAKlC6hC,GAAO,CACTnsB,aAAc,WAIZ,IAHA,IAAI5d,EAAS3C,KACT+W,EAASpU,EAAOoU,OAChBP,EAAM7T,EAAO8T,aACRxW,EAAI,EAAGA,EAAI8W,EAAO/S,OAAQ/D,GAAK,EAAG,CACzC,IAAIo1B,EAAWte,EAAO1H,GAAGpP,GACrBuc,EAAW6Y,EAAS,GAAG7Y,SACvB7Z,EAAOgQ,OAAOg6B,WAAWC,gBAC3BpwB,EAAWnE,KAAKK,IAAIL,KAAKkU,IAAI8I,EAAS,GAAG7Y,SAAU,IAAK,IAE1D,IAAI1O,EAASunB,EAAS,GAAG3Z,kBACrBmxB,GAAU,IAAMrwB,EAChBswB,EAAUD,EACVE,EAAU,EACVnC,GAAM98B,EACN+8B,EAAK,EAYT,GAXKloC,EAAOsT,eAKDO,IACTs2B,GAAWA,IALXjC,EAAKD,EACLA,EAAK,EACLmC,GAAWD,EACXA,EAAU,GAKZzX,EAAS,GAAGruB,MAAMgmC,QAAU30B,KAAK4B,IAAI5B,KAAKuzB,MAAMpvB,IAAazF,EAAO/S,OAEhErB,EAAOgQ,OAAOg6B,WAAWb,aAAc,CAEzC,IAAIC,EAAeppC,EAAOsT,eAAiBof,EAAS7kB,KAAK,6BAA+B6kB,EAAS7kB,KAAK,4BAClGw7B,EAAcrpC,EAAOsT,eAAiBof,EAAS7kB,KAAK,8BAAgC6kB,EAAS7kB,KAAK,+BAC1E,IAAxBu7B,EAAa/nC,SACf+nC,EAAe3jC,EAAG,oCAAuCzF,EAAOsT,eAAiB,OAAS,OAAS,YACnGof,EAAS9lB,OAAOw8B,IAES,IAAvBC,EAAYhoC,SACdgoC,EAAc5jC,EAAG,oCAAuCzF,EAAOsT,eAAiB,QAAU,UAAY,YACtGof,EAAS9lB,OAAOy8B,IAEdD,EAAa/nC,SAAU+nC,EAAa,GAAG/kC,MAAMk3B,QAAU7lB,KAAKK,KAAK8D,EAAU,IAC3EwvB,EAAYhoC,SAAUgoC,EAAY,GAAGhlC,MAAMk3B,QAAU7lB,KAAKK,IAAI8D,EAAU,IAE9E6Y,EACG5qB,UAAW,eAAiBmgC,EAAK,OAASC,EAAK,oBAAsBkC,EAAU,gBAAkBD,EAAU,UAGlHtxB,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACT+W,EAASpU,EAAOoU,OAChB0E,EAAc9Y,EAAO8Y,YACrBnF,EAAa3T,EAAO2T,WAKxB,GAJAS,EACGnM,WAAWC,GACX2F,KAAK,gHACL5F,WAAWC,GACVlI,EAAOgQ,OAAO0N,kBAAiC,IAAbxV,EAAgB,CACpD,IAAIogC,GAAiB,EAErBl0B,EAAO1H,GAAGoM,GAAatO,eAAc,WACnC,IAAI89B,GACCtoC,IAAUA,EAAOmf,UAAtB,CAEAmpB,GAAiB,EACjBtoC,EAAOye,WAAY,EAEnB,IADA,IAAI8pB,EAAgB,CAAC,sBAAuB,iBACnCjrC,EAAI,EAAGA,EAAIirC,EAAclnC,OAAQ/D,GAAK,EAC7CqW,EAAW3J,QAAQu+B,EAAcjrC,WAOvCgtC,GAAa,CACf/pC,KAAM,cACNyP,OAAQ,CACNg6B,WAAY,CACVb,cAAc,EACdc,eAAe,IAGnB9sB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBgqC,WAAY,CACVpsB,aAAcmsB,GAAKnsB,aAAaX,KAAKjd,GACrC6Y,cAAekxB,GAAKlxB,cAAcoE,KAAKjd,OAI7CsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACb,GAA6B,SAAzB2C,EAAOgQ,OAAOuH,OAAlB,CACAvX,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,QACjEhQ,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,MACjE,IAAIgjB,EAAkB,CACpBnd,cAAe,EACfJ,gBAAiB,EACjBc,eAAgB,EAChB+B,qBAAqB,EACrBvD,aAAc,EACd2I,kBAAkB,GAEpBnP,EAAMkC,OAAOzQ,EAAOgQ,OAAQgjB,GAC5BzkB,EAAMkC,OAAOzQ,EAAOqrB,eAAgB2H,KAEtCpV,aAAc,WACZ,IAAI5d,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOgqC,WAAWpsB,gBAEpB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACgB,SAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOgqC,WAAWnxB,cAAc3Q,MAKlCqiC,GAAY,CACd3sB,aAAc,WAcZ,IAbA,IAAI5d,EAAS3C,KACTsrC,EAAc3oC,EAAOkT,MACrB01B,EAAe5oC,EAAOmT,OACtBiB,EAASpU,EAAOoU,OAChBT,EAAa3T,EAAO2T,WACpBa,EAAkBxU,EAAOwU,gBACzBxE,EAAShQ,EAAOgQ,OAAOw6B,gBACvBl3B,EAAetT,EAAOsT,eACtBxL,EAAY9H,EAAOmZ,UACnBsxB,EAASn3B,EAA6Bq1B,EAAc,EAA3B7gC,EAA8C8gC,EAAe,EAA5B9gC,EAC1DoiC,EAAS52B,EAAetD,EAAOk6B,QAAUl6B,EAAOk6B,OAChD/wB,EAAYnJ,EAAO06B,MAEdptC,EAAI,EAAG+D,EAAS+S,EAAO/S,OAAQ/D,EAAI+D,EAAQ/D,GAAK,EAAG,CAC1D,IAAIo1B,EAAWte,EAAO1H,GAAGpP,GACrB6X,EAAYX,EAAgBlX,GAC5BqtC,EAAcjY,EAAS,GAAG3Z,kBAC1B6xB,GAAqBH,EAASE,EAAex1B,EAAY,GAAMA,EAAanF,EAAO66B,SAEnFV,EAAU72B,EAAe42B,EAASU,EAAmB,EACrDR,EAAU92B,EAAe,EAAI42B,EAASU,EAEtCE,GAAc3xB,EAAYzD,KAAK4B,IAAIszB,GAEnCrK,EAAajtB,EAAe,EAAItD,EAAO+6B,QAAU,EACjDzK,EAAahtB,EAAetD,EAAO+6B,QAAU,EAAqB,EAGlEr1B,KAAK4B,IAAIgpB,GAAc,OAASA,EAAa,GAC7C5qB,KAAK4B,IAAIipB,GAAc,OAASA,EAAa,GAC7C7qB,KAAK4B,IAAIwzB,GAAc,OAASA,EAAa,GAC7Cp1B,KAAK4B,IAAI6yB,GAAW,OAASA,EAAU,GACvCz0B,KAAK4B,IAAI8yB,GAAW,OAASA,EAAU,GAE3C,IAAIY,EAAiB,eAAiB1K,EAAa,MAAQC,EAAa,MAAQuK,EAAa,gBAAkBV,EAAU,gBAAkBD,EAAU,OAIrJ,GAFAzX,EAAS5qB,UAAUkjC,GACnBtY,EAAS,GAAGruB,MAAMgmC,OAAmD,EAAzC30B,KAAK4B,IAAI5B,KAAKuzB,MAAM2B,IAC5C56B,EAAOm5B,aAAc,CAEvB,IAAI8B,EAAkB33B,EAAeof,EAAS7kB,KAAK,6BAA+B6kB,EAAS7kB,KAAK,4BAC5Fq9B,EAAiB53B,EAAeof,EAAS7kB,KAAK,8BAAgC6kB,EAAS7kB,KAAK,+BACjE,IAA3Bo9B,EAAgB5pC,SAClB4pC,EAAkBxlC,EAAG,oCAAuC6N,EAAe,OAAS,OAAS,YAC7Fof,EAAS9lB,OAAOq+B,IAEY,IAA1BC,EAAe7pC,SACjB6pC,EAAiBzlC,EAAG,oCAAuC6N,EAAe,QAAU,UAAY,YAChGof,EAAS9lB,OAAOs+B,IAEdD,EAAgB5pC,SAAU4pC,EAAgB,GAAG5mC,MAAMk3B,QAAUqP,EAAmB,EAAIA,EAAmB,GACvGM,EAAe7pC,SAAU6pC,EAAe,GAAG7mC,MAAMk3B,SAAYqP,EAAoB,GAAKA,EAAmB,IAKjH,GAAI15B,EAAQM,eAAiBN,EAAQS,sBAAuB,CAC1D,IAAIw5B,EAAKx3B,EAAW,GAAGtP,MACvB8mC,EAAGC,kBAAoBX,EAAS,WAGpC5xB,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACb2C,EAAOoU,OACJnM,WAAWC,GACX2F,KAAK,gHACL5F,WAAWC,KAIdmjC,GAAkB,CACpB9qC,KAAM,mBACNyP,OAAQ,CACNw6B,gBAAiB,CACfN,OAAQ,GACRa,QAAS,EACTL,MAAO,IACPG,SAAU,EACV1B,cAAc,IAGlBhsB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBwqC,gBAAiB,CACf5sB,aAAc2sB,GAAU3sB,aAAaX,KAAKjd,GAC1C6Y,cAAe0xB,GAAU1xB,cAAcoE,KAAKjd,OAIlDsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACgB,cAAzB2C,EAAOgQ,OAAOuH,SAElBvX,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,aACjEhQ,EAAOgsB,WAAW7lB,KAAOnG,EAAOgQ,OAA6B,uBAAI,MAEjEhQ,EAAOgQ,OAAOsI,qBAAsB,EACpCtY,EAAOqrB,eAAe/S,qBAAsB,IAE9CsF,aAAc,WACZ,IAAI5d,EAAS3C,KACgB,cAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOwqC,gBAAgB5sB,gBAEzB/E,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACgB,cAAzB2C,EAAOgQ,OAAOuH,QAClBvX,EAAOwqC,gBAAgB3xB,cAAc3Q,MAKvCojC,GAAS,CACX7d,KAAM,WACJ,IAAIztB,EAAS3C,KACT+zB,EAAMpxB,EAAOgQ,OACbu7B,EAAena,EAAIoa,OACnB54B,EAAc5S,EAAOwQ,YACrB+6B,EAAavrC,kBAAkB4S,GACjC5S,EAAOwrC,OAAOxrC,OAASurC,EAAavrC,OACpCuO,EAAMkC,OAAOzQ,EAAOwrC,OAAOxrC,OAAOqrB,eAAgB,CAChD/S,qBAAqB,EACrBwD,qBAAqB,IAEvBvN,EAAMkC,OAAOzQ,EAAOwrC,OAAOxrC,OAAOgQ,OAAQ,CACxCsI,qBAAqB,EACrBwD,qBAAqB,KAEdvN,EAAMgC,SAASg7B,EAAavrC,UACrCA,EAAOwrC,OAAOxrC,OAAS,IAAI4S,EAAYrE,EAAMkC,OAAO,GAAI86B,EAAavrC,OAAQ,CAC3EuY,uBAAuB,EACvBD,qBAAqB,EACrBwD,qBAAqB,KAEvB9b,EAAOwrC,OAAOC,eAAgB,GAEhCzrC,EAAOwrC,OAAOxrC,OAAOkC,IAAIuE,SAASzG,EAAOgQ,OAAOw7B,OAAOE,sBACvD1rC,EAAOwrC,OAAOxrC,OAAOsC,GAAG,MAAOtC,EAAOwrC,OAAOG,eAE/CA,aAAc,WACZ,IAAI3rC,EAAS3C,KACTuuC,EAAe5rC,EAAOwrC,OAAOxrC,OACjC,GAAK4rC,EAAL,CACA,IAAI/vB,EAAe+vB,EAAa/vB,aAC5BD,EAAegwB,EAAahwB,aAChC,KAAIA,IAAgBnW,EAAEmW,GAAc7U,SAAS/G,EAAOgQ,OAAOw7B,OAAOK,yBACtC,qBAAjBhwB,GAAiD,OAAjBA,EAA3C,CACA,IAAIyE,EAMJ,GAJEA,EADEsrB,EAAa57B,OAAOyK,KACPjH,SAAS/N,EAAEmmC,EAAahwB,cAAczU,KAAK,2BAA4B,IAEvE0U,EAEb7b,EAAOgQ,OAAOyK,KAAM,CACtB,IAAIqxB,EAAe9rC,EAAO8Y,YACtB9Y,EAAOoU,OAAO1H,GAAGo/B,GAAc/kC,SAAS/G,EAAOgQ,OAAO0K,uBACxD1a,EAAOwf,UAEPxf,EAAOyf,YAAczf,EAAO2T,WAAW,GAAGpI,WAC1CugC,EAAe9rC,EAAO8Y,aAExB,IAAI+G,EAAY7f,EAAOoU,OAAO1H,GAAGo/B,GAAcr+B,QAAS,6BAAgC6S,EAAe,MAAQ5T,GAAG,GAAGH,QACjHuE,EAAY9Q,EAAOoU,OAAO1H,GAAGo/B,GAAcz+B,QAAS,6BAAgCiT,EAAe,MAAQ5T,GAAG,GAAGH,QAC7E+T,EAAf,qBAAdT,EAA4C/O,EACzB,qBAAdA,EAA4C+O,EACnD/O,EAAYg7B,EAAeA,EAAejsB,EAA4B/O,EACzD+O,EAExB7f,EAAO2e,QAAQ2B,MAEjB1f,OAAQ,SAAgBmrC,GACtB,IAAI/rC,EAAS3C,KACTuuC,EAAe5rC,EAAOwrC,OAAOxrC,OACjC,GAAK4rC,EAAL,CAEA,IAAI/1B,EAAsD,SAAtC+1B,EAAa57B,OAAO6F,cACpC+1B,EAAavrB,uBACburB,EAAa57B,OAAO6F,cAExB,GAAI7V,EAAOua,YAAcqxB,EAAarxB,UAAW,CAC/C,IACIyxB,EADAC,EAAqBL,EAAa9yB,YAEtC,GAAI8yB,EAAa57B,OAAOyK,KAAM,CACxBmxB,EAAax3B,OAAO1H,GAAGu/B,GAAoBllC,SAAS6kC,EAAa57B,OAAO0K,uBAC1EkxB,EAAapsB,UAEbosB,EAAansB,YAAcmsB,EAAaj4B,WAAW,GAAGpI,WACtD0gC,EAAqBL,EAAa9yB,aAGpC,IAAIozB,EAAkBN,EAAax3B,OAAO1H,GAAGu/B,GAAoBx+B,QAAS,6BAAiCzN,EAAgB,UAAI,MAAQ0M,GAAG,GAAGH,QACzI4/B,EAAkBP,EAAax3B,OAAO1H,GAAGu/B,GAAoB5+B,QAAS,6BAAiCrN,EAAgB,UAAI,MAAQ0M,GAAG,GAAGH,QAC/Fy/B,EAAf,qBAApBE,EAAoDC,EAC3B,qBAApBA,EAAoDD,EAC3DC,EAAkBF,IAAuBA,EAAqBC,EAAoCD,EAClGE,EAAkBF,EAAqBA,EAAqBC,EAAoCC,EACjFD,OAExBF,EAAiBhsC,EAAOua,UAEtBqxB,EAAatyB,sBAAwBsyB,EAAatyB,qBAAqBtT,QAAQgmC,GAAkB,IAC/FJ,EAAa57B,OAAOqH,eAEpB20B,EADEA,EAAiBC,EACFD,EAAiBt2B,KAAKC,MAAME,EAAgB,GAAK,EAEjDm2B,EAAiBt2B,KAAKC,MAAME,EAAgB,GAAK,EAE3Dm2B,EAAiBC,IAC1BD,EAAiBA,EAAiBn2B,EAAgB,GAEpD+1B,EAAajtB,QAAQqtB,EAAgBD,EAAU,OAAI3iC,IAKvD,IAAIgjC,EAAmB,EACnBC,EAAmBrsC,EAAOgQ,OAAOw7B,OAAOK,sBAO5C,GALI7rC,EAAOgQ,OAAO6F,cAAgB,IAAM7V,EAAOgQ,OAAOqH,iBACpD+0B,EAAmBpsC,EAAOgQ,OAAO6F,eAGnC+1B,EAAax3B,OAAOvN,YAAYwlC,GAC5BT,EAAa57B,OAAOyK,MAAQmxB,EAAa57B,OAAOiE,QAClD,IAAK,IAAI3W,EAAI,EAAGA,EAAI8uC,EAAkB9uC,GAAK,EACzCsuC,EAAaj4B,WAAWxP,SAAU,8BAAiCnE,EAAOua,UAAYjd,GAAK,MAAQmJ,SAAS4lC,QAG9G,IAAK,IAAI50B,EAAM,EAAGA,EAAM20B,EAAkB30B,GAAO,EAC/Cm0B,EAAax3B,OAAO1H,GAAG1M,EAAOua,UAAY9C,GAAKhR,SAAS4lC,MAK5DC,GAAW,CACb/rC,KAAM,SACNyP,OAAQ,CACNw7B,OAAQ,CACNxrC,OAAQ,KACR6rC,sBAAuB,4BACvBH,qBAAsB,4BAG1BvuB,OAAQ,WACN,IAAInd,EAAS3C,KACbkR,EAAMkC,OAAOzQ,EAAQ,CACnBwrC,OAAQ,CACNxrC,OAAQ,KACRytB,KAAM6d,GAAO7d,KAAKxQ,KAAKjd,GACvBY,OAAQ0qC,GAAO1qC,OAAOqc,KAAKjd,GAC3B2rC,aAAcL,GAAOK,aAAa1uB,KAAKjd,OAI7CsC,GAAI,CACFywB,WAAY,WACV,IAAI/yB,EAAS3C,KACT+zB,EAAMpxB,EAAOgQ,OACbw7B,EAASpa,EAAIoa,OACZA,GAAWA,EAAOxrC,SACvBA,EAAOwrC,OAAO/d,OACdztB,EAAOwrC,OAAO5qC,QAAO,KAEvB2rC,YAAa,WACX,IAAIvsC,EAAS3C,KACR2C,EAAOwrC,OAAOxrC,QACnBA,EAAOwrC,OAAO5qC,UAEhBA,OAAQ,WACN,IAAIZ,EAAS3C,KACR2C,EAAOwrC,OAAOxrC,QACnBA,EAAOwrC,OAAO5qC,UAEhBgvB,OAAQ,WACN,IAAI5vB,EAAS3C,KACR2C,EAAOwrC,OAAOxrC,QACnBA,EAAOwrC,OAAO5qC,UAEhB0vB,eAAgB,WACd,IAAItwB,EAAS3C,KACR2C,EAAOwrC,OAAOxrC,QACnBA,EAAOwrC,OAAO5qC,UAEhBiY,cAAe,SAAuB3Q,GACpC,IAAIlI,EAAS3C,KACTuuC,EAAe5rC,EAAOwrC,OAAOxrC,OAC5B4rC,GACLA,EAAa/yB,cAAc3Q,IAE7BpG,cAAe,WACb,IAAI9B,EAAS3C,KACTuuC,EAAe5rC,EAAOwrC,OAAOxrC,OAC5B4rC,GACD5rC,EAAOwrC,OAAOC,eAAiBG,GACjCA,EAAa7pC,aAQjBiR,GAAa,CACfsc,GACAC,GACAE,GACAE,GACAsB,GACA6B,GACAuB,GACAyC,GACAU,GACAgD,GACAoC,GACAU,GACAwD,GACA0B,GACAyB,GACAwB,GACAoB,GACAQ,GACAO,GACAY,GACAsB,GACAQ,GACAe,GACAiB,IAUF,MAP0B,qBAAfrsC,GAAOod,MAChBpd,GAAOod,IAAMpd,GAAOkO,MAAMkP,IAC1Bpd,GAAOqd,cAAgBrd,GAAOkO,MAAMmP,eAGtCrd,GAAOod,IAAIrK,IAEJ/S", "file": "h5/js/chunk-218c9962.1b5cfdc2.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t(require(\"swiper/dist/js/swiper.js\")):\"function\"==typeof define&&define.amd?define(\"VueAwesomeSwiper\",[\"swiper\"],t):\"object\"==typeof exports?exports.VueAwesomeSwiper=t(require(\"swiper/dist/js/swiper.js\")):e.VueAwesomeSwiper=t(e.Swiper)}(this,function(e){return function(e){function t(i){if(n[i])return n[i].exports;var s=n[i]={i:i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,t),s.l=!0,s.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:i})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,\"a\",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p=\"/\",t(t.s=4)}([function(t,n){t.exports=e},function(e,t){e.exports=function(e,t,n,i,s,r){var o,a=e=e||{},u=typeof e.default;\"object\"!==u&&\"function\"!==u||(o=e,a=e.default);var p=\"function\"==typeof a?a.options:a;t&&(p.render=t.render,p.staticRenderFns=t.staticRenderFns,p._compiled=!0),n&&(p.functional=!0),s&&(p._scopeId=s);var l;if(r?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||\"undefined\"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},p._ssrRegister=l):i&&(l=i),l){var c=p.functional,d=c?p.render:p.beforeCreate;c?(p._injectStyles=l,p.render=function(e,t){return l.call(t),d(e,t)}):p.beforeCreate=d?[].concat(d,l):[l]}return{esModule:o,exports:a,options:p}}},function(e,t,n){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});var i=n(5),s=n.n(i),r=n(8),o=n(1),a=o(s.a,r.a,!1,null,null,null);t.default=a.exports},function(e,t,n){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});var i=n(6),s=n.n(i),r=n(7),o=n(1),a=o(s.a,r.a,!1,null,null,null);t.default=a.exports},function(e,t,n){\"use strict\";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,\"__esModule\",{value:!0}),t.install=t.swiperSlide=t.swiper=t.Swiper=void 0;var s=n(0),r=i(s),o=n(2),a=i(o),u=n(3),p=i(u),l=window.Swiper||r.default,c=p.default,d=a.default,f=function(e,t){t&&(p.default.props.globalOptions.default=function(){return t}),e.component(p.default.name,p.default),e.component(a.default.name,a.default)},h={Swiper:l,swiper:c,swiperSlide:d,install:f};t.default=h,t.Swiper=l,t.swiper=c,t.swiperSlide=d,t.install=f},function(e,t,n){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.default={name:\"swiper-slide\",data:function(){return{slideClass:\"swiper-slide\"}},ready:function(){this.update()},mounted:function(){this.update(),this.$parent&&this.$parent.options&&this.$parent.options.slideClass&&(this.slideClass=this.$parent.options.slideClass)},updated:function(){this.update()},attached:function(){this.update()},methods:{update:function(){this.$parent&&this.$parent.swiper&&this.$parent.update()}}}},function(e,t,n){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});var i=n(0),s=function(e){return e&&e.__esModule?e:{default:e}}(i),r=window.Swiper||s.default;\"function\"!=typeof Object.assign&&Object.defineProperty(Object,\"assign\",{value:function(e,t){if(null==e)throw new TypeError(\"Cannot convert undefined or null to object\");for(var n=Object(e),i=1;i<arguments.length;i++){var s=arguments[i];if(null!=s)for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(n[r]=s[r])}return n},writable:!0,configurable:!0});var o=[\"beforeDestroy\",\"slideChange\",\"slideChangeTransitionStart\",\"slideChangeTransitionEnd\",\"slideNextTransitionStart\",\"slideNextTransitionEnd\",\"slidePrevTransitionStart\",\"slidePrevTransitionEnd\",\"transitionStart\",\"transitionEnd\",\"touchStart\",\"touchMove\",\"touchMoveOpposite\",\"sliderMove\",\"touchEnd\",\"click\",\"tap\",\"doubleTap\",\"imagesReady\",\"progress\",\"reachBeginning\",\"reachEnd\",\"fromEdge\",\"setTranslate\",\"setTransition\",\"resize\"];t.default={name:\"swiper\",props:{options:{type:Object,default:function(){return{}}},globalOptions:{type:Object,required:!1,default:function(){return{}}}},data:function(){return{swiper:null,classes:{wrapperClass:\"swiper-wrapper\"}}},ready:function(){this.swiper||this.mountInstance()},mounted:function(){if(!this.swiper){var e=!1;for(var t in this.classes)this.classes.hasOwnProperty(t)&&this.options[t]&&(e=!0,this.classes[t]=this.options[t]);e?this.$nextTick(this.mountInstance):this.mountInstance()}},activated:function(){this.update()},updated:function(){this.update()},beforeDestroy:function(){this.$nextTick(function(){this.swiper&&(this.swiper.destroy&&this.swiper.destroy(),delete this.swiper)})},methods:{update:function(){this.swiper&&(this.swiper.update&&this.swiper.update(),this.swiper.navigation&&this.swiper.navigation.update(),this.swiper.pagination&&this.swiper.pagination.render(),this.swiper.pagination&&this.swiper.pagination.update())},mountInstance:function(){var e=Object.assign({},this.globalOptions,this.options);this.swiper=new r(this.$el,e),this.bindEvents(),this.$emit(\"ready\",this.swiper)},bindEvents:function(){var e=this,t=this;o.forEach(function(n){e.swiper.on(n,function(){t.$emit.apply(t,[n].concat(Array.prototype.slice.call(arguments))),t.$emit.apply(t,[n.replace(/([A-Z])/g,\"-$1\").toLowerCase()].concat(Array.prototype.slice.call(arguments)))})})}}}},function(e,t,n){\"use strict\";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(\"div\",{staticClass:\"swiper-container\"},[e._t(\"parallax-bg\"),e._v(\" \"),n(\"div\",{class:e.classes.wrapperClass},[e._t(\"default\")],2),e._v(\" \"),e._t(\"pagination\"),e._v(\" \"),e._t(\"button-prev\"),e._v(\" \"),e._t(\"button-next\"),e._v(\" \"),e._t(\"scrollbar\")],2)},s=[],r={render:i,staticRenderFns:s};t.a=r},function(e,t,n){\"use strict\";var i=function(){var e=this,t=e.$createElement;return(e._self._c||t)(\"div\",{class:e.slideClass},[e._t(\"default\")],2)},s=[],r={render:i,staticRenderFns:s};t.a=r}])});", "/**\n * Swiper 4.5.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * http://www.idangero.us/swiper/\n *\n * Copyright 2014-2019 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: September 13, 2019\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = global || self, global.Swiper = factory());\n}(this, function () { 'use strict';\n\n  /**\n   * SSR Window 1.0.1\n   * Better handling for window object in SSR environment\n   * https://github.com/nolimits4web/ssr-window\n   *\n   * Copyright 2018, <PERSON>\n   *\n   * Licensed under MIT\n   *\n   * Released on: July 18, 2018\n   */\n  var doc = (typeof document === 'undefined') ? {\n    body: {},\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    activeElement: {\n      blur: function blur() {},\n      nodeName: '',\n    },\n    querySelector: function querySelector() {\n      return null;\n    },\n    querySelectorAll: function querySelectorAll() {\n      return [];\n    },\n    getElementById: function getElementById() {\n      return null;\n    },\n    createEvent: function createEvent() {\n      return {\n        initEvent: function initEvent() {},\n      };\n    },\n    createElement: function createElement() {\n      return {\n        children: [],\n        childNodes: [],\n        style: {},\n        setAttribute: function setAttribute() {},\n        getElementsByTagName: function getElementsByTagName() {\n          return [];\n        },\n      };\n    },\n    location: { hash: '' },\n  } : document; // eslint-disable-line\n\n  var win = (typeof window === 'undefined') ? {\n    document: doc,\n    navigator: {\n      userAgent: '',\n    },\n    location: {},\n    history: {},\n    CustomEvent: function CustomEvent() {\n      return this;\n    },\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    getComputedStyle: function getComputedStyle() {\n      return {\n        getPropertyValue: function getPropertyValue() {\n          return '';\n        },\n      };\n    },\n    Image: function Image() {},\n    Date: function Date() {},\n    screen: {},\n    setTimeout: function setTimeout() {},\n    clearTimeout: function clearTimeout() {},\n  } : window; // eslint-disable-line\n\n  /**\n   * Dom7 2.1.3\n   * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n   * http://framework7.io/docs/dom.html\n   *\n   * Copyright 2019, Vladimir Kharlampidi\n   * The iDangero.us\n   * http://www.idangero.us/\n   *\n   * Licensed under MIT\n   *\n   * Released on: February 11, 2019\n   */\n\n  var Dom7 = function Dom7(arr) {\n    var self = this;\n    // Create array-like object\n    for (var i = 0; i < arr.length; i += 1) {\n      self[i] = arr[i];\n    }\n    self.length = arr.length;\n    // Return collection with methods\n    return this;\n  };\n\n  function $(selector, context) {\n    var arr = [];\n    var i = 0;\n    if (selector && !context) {\n      if (selector instanceof Dom7) {\n        return selector;\n      }\n    }\n    if (selector) {\n        // String\n      if (typeof selector === 'string') {\n        var els;\n        var tempParent;\n        var html = selector.trim();\n        if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n          var toCreate = 'div';\n          if (html.indexOf('<li') === 0) { toCreate = 'ul'; }\n          if (html.indexOf('<tr') === 0) { toCreate = 'tbody'; }\n          if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) { toCreate = 'tr'; }\n          if (html.indexOf('<tbody') === 0) { toCreate = 'table'; }\n          if (html.indexOf('<option') === 0) { toCreate = 'select'; }\n          tempParent = doc.createElement(toCreate);\n          tempParent.innerHTML = html;\n          for (i = 0; i < tempParent.childNodes.length; i += 1) {\n            arr.push(tempParent.childNodes[i]);\n          }\n        } else {\n          if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n            // Pure ID selector\n            els = [doc.getElementById(selector.trim().split('#')[1])];\n          } else {\n            // Other selectors\n            els = (context || doc).querySelectorAll(selector.trim());\n          }\n          for (i = 0; i < els.length; i += 1) {\n            if (els[i]) { arr.push(els[i]); }\n          }\n        }\n      } else if (selector.nodeType || selector === win || selector === doc) {\n        // Node/element\n        arr.push(selector);\n      } else if (selector.length > 0 && selector[0].nodeType) {\n        // Array of elements or instance of Dom\n        for (i = 0; i < selector.length; i += 1) {\n          arr.push(selector[i]);\n        }\n      }\n    }\n    return new Dom7(arr);\n  }\n\n  $.fn = Dom7.prototype;\n  $.Class = Dom7;\n  $.Dom7 = Dom7;\n\n  function unique(arr) {\n    var uniqueArray = [];\n    for (var i = 0; i < arr.length; i += 1) {\n      if (uniqueArray.indexOf(arr[i]) === -1) { uniqueArray.push(arr[i]); }\n    }\n    return uniqueArray;\n  }\n\n  // Classes and attributes\n  function addClass(className) {\n    if (typeof className === 'undefined') {\n      return this;\n    }\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.add(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function removeClass(className) {\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.remove(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function hasClass(className) {\n    if (!this[0]) { return false; }\n    return this[0].classList.contains(className);\n  }\n  function toggleClass(className) {\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.toggle(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function attr(attrs, value) {\n    var arguments$1 = arguments;\n\n    if (arguments.length === 1 && typeof attrs === 'string') {\n      // Get attr\n      if (this[0]) { return this[0].getAttribute(attrs); }\n      return undefined;\n    }\n\n    // Set attrs\n    for (var i = 0; i < this.length; i += 1) {\n      if (arguments$1.length === 2) {\n        // String\n        this[i].setAttribute(attrs, value);\n      } else {\n        // Object\n        // eslint-disable-next-line\n        for (var attrName in attrs) {\n          this[i][attrName] = attrs[attrName];\n          this[i].setAttribute(attrName, attrs[attrName]);\n        }\n      }\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function removeAttr(attr) {\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].removeAttribute(attr);\n    }\n    return this;\n  }\n  function data(key, value) {\n    var el;\n    if (typeof value === 'undefined') {\n      el = this[0];\n      // Get value\n      if (el) {\n        if (el.dom7ElementDataStorage && (key in el.dom7ElementDataStorage)) {\n          return el.dom7ElementDataStorage[key];\n        }\n\n        var dataKey = el.getAttribute((\"data-\" + key));\n        if (dataKey) {\n          return dataKey;\n        }\n        return undefined;\n      }\n      return undefined;\n    }\n\n    // Set value\n    for (var i = 0; i < this.length; i += 1) {\n      el = this[i];\n      if (!el.dom7ElementDataStorage) { el.dom7ElementDataStorage = {}; }\n      el.dom7ElementDataStorage[key] = value;\n    }\n    return this;\n  }\n  // Transforms\n  // eslint-disable-next-line\n  function transform(transform) {\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this[i].style;\n      elStyle.webkitTransform = transform;\n      elStyle.transform = transform;\n    }\n    return this;\n  }\n  function transition(duration) {\n    if (typeof duration !== 'string') {\n      duration = duration + \"ms\"; // eslint-disable-line\n    }\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this[i].style;\n      elStyle.webkitTransitionDuration = duration;\n      elStyle.transitionDuration = duration;\n    }\n    return this;\n  }\n  // Events\n  function on() {\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    function handleLiveEvent(e) {\n      var target = e.target;\n      if (!target) { return; }\n      var eventData = e.target.dom7EventData || [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      if ($(target).is(targetSelector)) { listener.apply(target, eventData); }\n      else {\n        var parents = $(target).parents(); // eslint-disable-line\n        for (var k = 0; k < parents.length; k += 1) {\n          if ($(parents[k]).is(targetSelector)) { listener.apply(parents[k], eventData); }\n        }\n      }\n    }\n    function handleEvent(e) {\n      var eventData = e && e.target ? e.target.dom7EventData || [] : [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      listener.apply(this, eventData);\n    }\n    var events = eventType.split(' ');\n    var j;\n    for (var i = 0; i < this.length; i += 1) {\n      var el = this[i];\n      if (!targetSelector) {\n        for (j = 0; j < events.length; j += 1) {\n          var event = events[j];\n          if (!el.dom7Listeners) { el.dom7Listeners = {}; }\n          if (!el.dom7Listeners[event]) { el.dom7Listeners[event] = []; }\n          el.dom7Listeners[event].push({\n            listener: listener,\n            proxyListener: handleEvent,\n          });\n          el.addEventListener(event, handleEvent, capture);\n        }\n      } else {\n        // Live events\n        for (j = 0; j < events.length; j += 1) {\n          var event$1 = events[j];\n          if (!el.dom7LiveListeners) { el.dom7LiveListeners = {}; }\n          if (!el.dom7LiveListeners[event$1]) { el.dom7LiveListeners[event$1] = []; }\n          el.dom7LiveListeners[event$1].push({\n            listener: listener,\n            proxyListener: handleLiveEvent,\n          });\n          el.addEventListener(event$1, handleLiveEvent, capture);\n        }\n      }\n    }\n    return this;\n  }\n  function off() {\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    var events = eventType.split(' ');\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this[j];\n        var handlers = (void 0);\n        if (!targetSelector && el.dom7Listeners) {\n          handlers = el.dom7Listeners[event];\n        } else if (targetSelector && el.dom7LiveListeners) {\n          handlers = el.dom7LiveListeners[event];\n        }\n        if (handlers && handlers.length) {\n          for (var k = handlers.length - 1; k >= 0; k -= 1) {\n            var handler = handlers[k];\n            if (listener && handler.listener === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (!listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    return this;\n  }\n  function trigger() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var events = args[0].split(' ');\n    var eventData = args[1];\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this[j];\n        var evt = (void 0);\n        try {\n          evt = new win.CustomEvent(event, {\n            detail: eventData,\n            bubbles: true,\n            cancelable: true,\n          });\n        } catch (e) {\n          evt = doc.createEvent('Event');\n          evt.initEvent(event, true, true);\n          evt.detail = eventData;\n        }\n        // eslint-disable-next-line\n        el.dom7EventData = args.filter(function (data, dataIndex) { return dataIndex > 0; });\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n    return this;\n  }\n  function transitionEnd(callback) {\n    var events = ['webkitTransitionEnd', 'transitionend'];\n    var dom = this;\n    var i;\n    function fireCallBack(e) {\n      /* jshint validthis:true */\n      if (e.target !== this) { return; }\n      callback.call(this, e);\n      for (i = 0; i < events.length; i += 1) {\n        dom.off(events[i], fireCallBack);\n      }\n    }\n    if (callback) {\n      for (i = 0; i < events.length; i += 1) {\n        dom.on(events[i], fireCallBack);\n      }\n    }\n    return this;\n  }\n  function outerWidth(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n      }\n      return this[0].offsetWidth;\n    }\n    return null;\n  }\n  function outerHeight(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n      }\n      return this[0].offsetHeight;\n    }\n    return null;\n  }\n  function offset() {\n    if (this.length > 0) {\n      var el = this[0];\n      var box = el.getBoundingClientRect();\n      var body = doc.body;\n      var clientTop = el.clientTop || body.clientTop || 0;\n      var clientLeft = el.clientLeft || body.clientLeft || 0;\n      var scrollTop = el === win ? win.scrollY : el.scrollTop;\n      var scrollLeft = el === win ? win.scrollX : el.scrollLeft;\n      return {\n        top: (box.top + scrollTop) - clientTop,\n        left: (box.left + scrollLeft) - clientLeft,\n      };\n    }\n\n    return null;\n  }\n  function styles() {\n    if (this[0]) { return win.getComputedStyle(this[0], null); }\n    return {};\n  }\n  function css(props, value) {\n    var i;\n    if (arguments.length === 1) {\n      if (typeof props === 'string') {\n        if (this[0]) { return win.getComputedStyle(this[0], null).getPropertyValue(props); }\n      } else {\n        for (i = 0; i < this.length; i += 1) {\n          // eslint-disable-next-line\n          for (var prop in props) {\n            this[i].style[prop] = props[prop];\n          }\n        }\n        return this;\n      }\n    }\n    if (arguments.length === 2 && typeof props === 'string') {\n      for (i = 0; i < this.length; i += 1) {\n        this[i].style[props] = value;\n      }\n      return this;\n    }\n    return this;\n  }\n  // Iterate over the collection passing elements to `callback`\n  function each(callback) {\n    // Don't bother continuing without a callback\n    if (!callback) { return this; }\n    // Iterate over the current collection\n    for (var i = 0; i < this.length; i += 1) {\n      // If the callback returns false\n      if (callback.call(this[i], i, this[i]) === false) {\n        // End the loop early\n        return this;\n      }\n    }\n    // Return `this` to allow chained DOM operations\n    return this;\n  }\n  // eslint-disable-next-line\n  function html(html) {\n    if (typeof html === 'undefined') {\n      return this[0] ? this[0].innerHTML : undefined;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].innerHTML = html;\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function text(text) {\n    if (typeof text === 'undefined') {\n      if (this[0]) {\n        return this[0].textContent.trim();\n      }\n      return null;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].textContent = text;\n    }\n    return this;\n  }\n  function is(selector) {\n    var el = this[0];\n    var compareWith;\n    var i;\n    if (!el || typeof selector === 'undefined') { return false; }\n    if (typeof selector === 'string') {\n      if (el.matches) { return el.matches(selector); }\n      else if (el.webkitMatchesSelector) { return el.webkitMatchesSelector(selector); }\n      else if (el.msMatchesSelector) { return el.msMatchesSelector(selector); }\n\n      compareWith = $(selector);\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    } else if (selector === doc) { return el === doc; }\n    else if (selector === win) { return el === win; }\n\n    if (selector.nodeType || selector instanceof Dom7) {\n      compareWith = selector.nodeType ? [selector] : selector;\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    }\n    return false;\n  }\n  function index() {\n    var child = this[0];\n    var i;\n    if (child) {\n      i = 0;\n      // eslint-disable-next-line\n      while ((child = child.previousSibling) !== null) {\n        if (child.nodeType === 1) { i += 1; }\n      }\n      return i;\n    }\n    return undefined;\n  }\n  // eslint-disable-next-line\n  function eq(index) {\n    if (typeof index === 'undefined') { return this; }\n    var length = this.length;\n    var returnIndex;\n    if (index > length - 1) {\n      return new Dom7([]);\n    }\n    if (index < 0) {\n      returnIndex = length + index;\n      if (returnIndex < 0) { return new Dom7([]); }\n      return new Dom7([this[returnIndex]]);\n    }\n    return new Dom7([this[index]]);\n  }\n  function append() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var newChild;\n\n    for (var k = 0; k < args.length; k += 1) {\n      newChild = args[k];\n      for (var i = 0; i < this.length; i += 1) {\n        if (typeof newChild === 'string') {\n          var tempDiv = doc.createElement('div');\n          tempDiv.innerHTML = newChild;\n          while (tempDiv.firstChild) {\n            this[i].appendChild(tempDiv.firstChild);\n          }\n        } else if (newChild instanceof Dom7) {\n          for (var j = 0; j < newChild.length; j += 1) {\n            this[i].appendChild(newChild[j]);\n          }\n        } else {\n          this[i].appendChild(newChild);\n        }\n      }\n    }\n\n    return this;\n  }\n  function prepend(newChild) {\n    var i;\n    var j;\n    for (i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        var tempDiv = doc.createElement('div');\n        tempDiv.innerHTML = newChild;\n        for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n          this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (j = 0; j < newChild.length; j += 1) {\n          this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n        }\n      } else {\n        this[i].insertBefore(newChild, this[i].childNodes[0]);\n      }\n    }\n    return this;\n  }\n  function next(selector) {\n    if (this.length > 0) {\n      if (selector) {\n        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n          return new Dom7([this[0].nextElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (this[0].nextElementSibling) { return new Dom7([this[0].nextElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function nextAll(selector) {\n    var nextEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.nextElementSibling) {\n      var next = el.nextElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(next).is(selector)) { nextEls.push(next); }\n      } else { nextEls.push(next); }\n      el = next;\n    }\n    return new Dom7(nextEls);\n  }\n  function prev(selector) {\n    if (this.length > 0) {\n      var el = this[0];\n      if (selector) {\n        if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n          return new Dom7([el.previousElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (el.previousElementSibling) { return new Dom7([el.previousElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function prevAll(selector) {\n    var prevEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.previousElementSibling) {\n      var prev = el.previousElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(prev).is(selector)) { prevEls.push(prev); }\n      } else { prevEls.push(prev); }\n      el = prev;\n    }\n    return new Dom7(prevEls);\n  }\n  function parent(selector) {\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      if (this[i].parentNode !== null) {\n        if (selector) {\n          if ($(this[i].parentNode).is(selector)) { parents.push(this[i].parentNode); }\n        } else {\n          parents.push(this[i].parentNode);\n        }\n      }\n    }\n    return $(unique(parents));\n  }\n  function parents(selector) {\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var parent = this[i].parentNode; // eslint-disable-line\n      while (parent) {\n        if (selector) {\n          if ($(parent).is(selector)) { parents.push(parent); }\n        } else {\n          parents.push(parent);\n        }\n        parent = parent.parentNode;\n      }\n    }\n    return $(unique(parents));\n  }\n  function closest(selector) {\n    var closest = this; // eslint-disable-line\n    if (typeof selector === 'undefined') {\n      return new Dom7([]);\n    }\n    if (!closest.is(selector)) {\n      closest = closest.parents(selector).eq(0);\n    }\n    return closest;\n  }\n  function find(selector) {\n    var foundElements = [];\n    for (var i = 0; i < this.length; i += 1) {\n      var found = this[i].querySelectorAll(selector);\n      for (var j = 0; j < found.length; j += 1) {\n        foundElements.push(found[j]);\n      }\n    }\n    return new Dom7(foundElements);\n  }\n  function children(selector) {\n    var children = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var childNodes = this[i].childNodes;\n\n      for (var j = 0; j < childNodes.length; j += 1) {\n        if (!selector) {\n          if (childNodes[j].nodeType === 1) { children.push(childNodes[j]); }\n        } else if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) {\n          children.push(childNodes[j]);\n        }\n      }\n    }\n    return new Dom7(unique(children));\n  }\n  function remove() {\n    for (var i = 0; i < this.length; i += 1) {\n      if (this[i].parentNode) { this[i].parentNode.removeChild(this[i]); }\n    }\n    return this;\n  }\n  function add() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var dom = this;\n    var i;\n    var j;\n    for (i = 0; i < args.length; i += 1) {\n      var toAdd = $(args[i]);\n      for (j = 0; j < toAdd.length; j += 1) {\n        dom[dom.length] = toAdd[j];\n        dom.length += 1;\n      }\n    }\n    return dom;\n  }\n\n  var Methods = {\n    addClass: addClass,\n    removeClass: removeClass,\n    hasClass: hasClass,\n    toggleClass: toggleClass,\n    attr: attr,\n    removeAttr: removeAttr,\n    data: data,\n    transform: transform,\n    transition: transition,\n    on: on,\n    off: off,\n    trigger: trigger,\n    transitionEnd: transitionEnd,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    offset: offset,\n    css: css,\n    each: each,\n    html: html,\n    text: text,\n    is: is,\n    index: index,\n    eq: eq,\n    append: append,\n    prepend: prepend,\n    next: next,\n    nextAll: nextAll,\n    prev: prev,\n    prevAll: prevAll,\n    parent: parent,\n    parents: parents,\n    closest: closest,\n    find: find,\n    children: children,\n    remove: remove,\n    add: add,\n    styles: styles,\n  };\n\n  Object.keys(Methods).forEach(function (methodName) {\n    $.fn[methodName] = $.fn[methodName] || Methods[methodName];\n  });\n\n  var Utils = {\n    deleteProps: function deleteProps(obj) {\n      var object = obj;\n      Object.keys(object).forEach(function (key) {\n        try {\n          object[key] = null;\n        } catch (e) {\n          // no getter for object\n        }\n        try {\n          delete object[key];\n        } catch (e) {\n          // something got wrong\n        }\n      });\n    },\n    nextTick: function nextTick(callback, delay) {\n      if ( delay === void 0 ) delay = 0;\n\n      return setTimeout(callback, delay);\n    },\n    now: function now() {\n      return Date.now();\n    },\n    getTranslate: function getTranslate(el, axis) {\n      if ( axis === void 0 ) axis = 'x';\n\n      var matrix;\n      var curTransform;\n      var transformMatrix;\n\n      var curStyle = win.getComputedStyle(el, null);\n\n      if (win.WebKitCSSMatrix) {\n        curTransform = curStyle.transform || curStyle.webkitTransform;\n        if (curTransform.split(',').length > 6) {\n          curTransform = curTransform.split(', ').map(function (a) { return a.replace(',', '.'); }).join(', ');\n        }\n        // Some old versions of Webkit choke when 'none' is passed; pass\n        // empty string instead in this case\n        transformMatrix = new win.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n      } else {\n        transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n        matrix = transformMatrix.toString().split(',');\n      }\n\n      if (axis === 'x') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m41; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[12]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[4]); }\n      }\n      if (axis === 'y') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m42; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[13]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[5]); }\n      }\n      return curTransform || 0;\n    },\n    parseUrlQuery: function parseUrlQuery(url) {\n      var query = {};\n      var urlToParse = url || win.location.href;\n      var i;\n      var params;\n      var param;\n      var length;\n      if (typeof urlToParse === 'string' && urlToParse.length) {\n        urlToParse = urlToParse.indexOf('?') > -1 ? urlToParse.replace(/\\S*\\?/, '') : '';\n        params = urlToParse.split('&').filter(function (paramsPart) { return paramsPart !== ''; });\n        length = params.length;\n\n        for (i = 0; i < length; i += 1) {\n          param = params[i].replace(/#\\S+/g, '').split('=');\n          query[decodeURIComponent(param[0])] = typeof param[1] === 'undefined' ? undefined : decodeURIComponent(param[1]) || '';\n        }\n      }\n      return query;\n    },\n    isObject: function isObject(o) {\n      return typeof o === 'object' && o !== null && o.constructor && o.constructor === Object;\n    },\n    extend: function extend() {\n      var args = [], len$1 = arguments.length;\n      while ( len$1-- ) args[ len$1 ] = arguments[ len$1 ];\n\n      var to = Object(args[0]);\n      for (var i = 1; i < args.length; i += 1) {\n        var nextSource = args[i];\n        if (nextSource !== undefined && nextSource !== null) {\n          var keysArray = Object.keys(Object(nextSource));\n          for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n            var nextKey = keysArray[nextIndex];\n            var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n            if (desc !== undefined && desc.enumerable) {\n              if (Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else if (!Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                to[nextKey] = {};\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else {\n                to[nextKey] = nextSource[nextKey];\n              }\n            }\n          }\n        }\n      }\n      return to;\n    },\n  };\n\n  var Support = (function Support() {\n    var testDiv = doc.createElement('div');\n    return {\n      touch: (win.Modernizr && win.Modernizr.touch === true) || (function checkTouch() {\n        return !!((win.navigator.maxTouchPoints > 0) || ('ontouchstart' in win) || (win.DocumentTouch && doc instanceof win.DocumentTouch));\n      }()),\n\n      pointerEvents: !!(win.navigator.pointerEnabled || win.PointerEvent || ('maxTouchPoints' in win.navigator && win.navigator.maxTouchPoints > 0)),\n      prefixedPointerEvents: !!win.navigator.msPointerEnabled,\n\n      transition: (function checkTransition() {\n        var style = testDiv.style;\n        return ('transition' in style || 'webkitTransition' in style || 'MozTransition' in style);\n      }()),\n      transforms3d: (win.Modernizr && win.Modernizr.csstransforms3d === true) || (function checkTransforms3d() {\n        var style = testDiv.style;\n        return ('webkitPerspective' in style || 'MozPerspective' in style || 'OPerspective' in style || 'MsPerspective' in style || 'perspective' in style);\n      }()),\n\n      flexbox: (function checkFlexbox() {\n        var style = testDiv.style;\n        var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n        for (var i = 0; i < styles.length; i += 1) {\n          if (styles[i] in style) { return true; }\n        }\n        return false;\n      }()),\n\n      observer: (function checkObserver() {\n        return ('MutationObserver' in win || 'WebkitMutationObserver' in win);\n      }()),\n\n      passiveListener: (function checkPassiveListener() {\n        var supportsPassive = false;\n        try {\n          var opts = Object.defineProperty({}, 'passive', {\n            // eslint-disable-next-line\n            get: function get() {\n              supportsPassive = true;\n            },\n          });\n          win.addEventListener('testPassiveListener', null, opts);\n        } catch (e) {\n          // No support\n        }\n        return supportsPassive;\n      }()),\n\n      gestures: (function checkGestures() {\n        return 'ongesturestart' in win;\n      }()),\n    };\n  }());\n\n  var Browser = (function Browser() {\n    function isSafari() {\n      var ua = win.navigator.userAgent.toLowerCase();\n      return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n    }\n    return {\n      isIE: !!win.navigator.userAgent.match(/Trident/g) || !!win.navigator.userAgent.match(/MSIE/g),\n      isEdge: !!win.navigator.userAgent.match(/Edge/g),\n      isSafari: isSafari(),\n      isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(win.navigator.userAgent),\n    };\n  }());\n\n  var SwiperClass = function SwiperClass(params) {\n    if ( params === void 0 ) params = {};\n\n    var self = this;\n    self.params = params;\n\n    // Events\n    self.eventsListeners = {};\n\n    if (self.params && self.params.on) {\n      Object.keys(self.params.on).forEach(function (eventName) {\n        self.on(eventName, self.params.on[eventName]);\n      });\n    }\n  };\n\n  var staticAccessors = { components: { configurable: true } };\n\n  SwiperClass.prototype.on = function on (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    var method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(function (event) {\n      if (!self.eventsListeners[event]) { self.eventsListeners[event] = []; }\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.once = function once (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    function onceHandler() {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n      handler.apply(self, args);\n      self.off(events, onceHandler);\n      if (onceHandler.f7proxy) {\n        delete onceHandler.f7proxy;\n      }\n    }\n    onceHandler.f7proxy = handler;\n    return self.on(events, onceHandler, priority);\n  };\n\n  SwiperClass.prototype.off = function off (events, handler) {\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    events.split(' ').forEach(function (event) {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event] && self.eventsListeners[event].length) {\n        self.eventsListeners[event].forEach(function (eventHandler, index) {\n          if (eventHandler === handler || (eventHandler.f7proxy && eventHandler.f7proxy === handler)) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.emit = function emit () {\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    var events;\n    var data;\n    var context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    var eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(function (event) {\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        var handlers = [];\n        self.eventsListeners[event].forEach(function (eventHandler) {\n          handlers.push(eventHandler);\n        });\n        handlers.forEach(function (eventHandler) {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.useModulesParams = function useModulesParams (instanceParams) {\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      // Extend params\n      if (module.params) {\n        Utils.extend(instanceParams, module.params);\n      }\n    });\n  };\n\n  SwiperClass.prototype.useModules = function useModules (modulesParams) {\n      if ( modulesParams === void 0 ) modulesParams = {};\n\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      var moduleParams = modulesParams[moduleName] || {};\n      // Extend instance methods and props\n      if (module.instance) {\n        Object.keys(module.instance).forEach(function (modulePropName) {\n          var moduleProp = module.instance[modulePropName];\n          if (typeof moduleProp === 'function') {\n            instance[modulePropName] = moduleProp.bind(instance);\n          } else {\n            instance[modulePropName] = moduleProp;\n          }\n        });\n      }\n      // Add event listeners\n      if (module.on && instance.on) {\n        Object.keys(module.on).forEach(function (moduleEventName) {\n          instance.on(moduleEventName, module.on[moduleEventName]);\n        });\n      }\n\n      // Module create callback\n      if (module.create) {\n        module.create.bind(instance)(moduleParams);\n      }\n    });\n  };\n\n  staticAccessors.components.set = function (components) {\n    var Class = this;\n    if (!Class.use) { return; }\n    Class.use(components);\n  };\n\n  SwiperClass.installModule = function installModule (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (!Class.prototype.modules) { Class.prototype.modules = {}; }\n    var name = module.name || (((Object.keys(Class.prototype.modules).length) + \"_\" + (Utils.now())));\n    Class.prototype.modules[name] = module;\n    // Prototype\n    if (module.proto) {\n      Object.keys(module.proto).forEach(function (key) {\n        Class.prototype[key] = module.proto[key];\n      });\n    }\n    // Class\n    if (module.static) {\n      Object.keys(module.static).forEach(function (key) {\n        Class[key] = module.static[key];\n      });\n    }\n    // Callback\n    if (module.install) {\n      module.install.apply(Class, params);\n    }\n    return Class;\n  };\n\n  SwiperClass.use = function use (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (Array.isArray(module)) {\n      module.forEach(function (m) { return Class.installModule(m); });\n      return Class;\n    }\n    return Class.installModule.apply(Class, [ module ].concat( params ));\n  };\n\n  Object.defineProperties( SwiperClass, staticAccessors );\n\n  function updateSize () {\n    var swiper = this;\n    var width;\n    var height;\n    var $el = swiper.$el;\n    if (typeof swiper.params.width !== 'undefined') {\n      width = swiper.params.width;\n    } else {\n      width = $el[0].clientWidth;\n    }\n    if (typeof swiper.params.height !== 'undefined') {\n      height = swiper.params.height;\n    } else {\n      height = $el[0].clientHeight;\n    }\n    if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n      return;\n    }\n\n    // Subtract paddings\n    width = width - parseInt($el.css('padding-left'), 10) - parseInt($el.css('padding-right'), 10);\n    height = height - parseInt($el.css('padding-top'), 10) - parseInt($el.css('padding-bottom'), 10);\n\n    Utils.extend(swiper, {\n      width: width,\n      height: height,\n      size: swiper.isHorizontal() ? width : height,\n    });\n  }\n\n  function updateSlides () {\n    var swiper = this;\n    var params = swiper.params;\n\n    var $wrapperEl = swiper.$wrapperEl;\n    var swiperSize = swiper.size;\n    var rtl = swiper.rtlTranslate;\n    var wrongRTL = swiper.wrongRTL;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n    var previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n    var slides = $wrapperEl.children((\".\" + (swiper.params.slideClass)));\n    var slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n    var snapGrid = [];\n    var slidesGrid = [];\n    var slidesSizesGrid = [];\n\n    var offsetBefore = params.slidesOffsetBefore;\n    if (typeof offsetBefore === 'function') {\n      offsetBefore = params.slidesOffsetBefore.call(swiper);\n    }\n\n    var offsetAfter = params.slidesOffsetAfter;\n    if (typeof offsetAfter === 'function') {\n      offsetAfter = params.slidesOffsetAfter.call(swiper);\n    }\n\n    var previousSnapGridLength = swiper.snapGrid.length;\n    var previousSlidesGridLength = swiper.snapGrid.length;\n\n    var spaceBetween = params.spaceBetween;\n    var slidePosition = -offsetBefore;\n    var prevSlideSize = 0;\n    var index = 0;\n    if (typeof swiperSize === 'undefined') {\n      return;\n    }\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n    }\n\n    swiper.virtualSize = -spaceBetween;\n\n    // reset margins\n    if (rtl) { slides.css({ marginLeft: '', marginTop: '' }); }\n    else { slides.css({ marginRight: '', marginBottom: '' }); }\n\n    var slidesNumberEvenToRows;\n    if (params.slidesPerColumn > 1) {\n      if (Math.floor(slidesLength / params.slidesPerColumn) === slidesLength / swiper.params.slidesPerColumn) {\n        slidesNumberEvenToRows = slidesLength;\n      } else {\n        slidesNumberEvenToRows = Math.ceil(slidesLength / params.slidesPerColumn) * params.slidesPerColumn;\n      }\n      if (params.slidesPerView !== 'auto' && params.slidesPerColumnFill === 'row') {\n        slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, params.slidesPerView * params.slidesPerColumn);\n      }\n    }\n\n    // Calc slides\n    var slideSize;\n    var slidesPerColumn = params.slidesPerColumn;\n    var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n    var numFullColumns = Math.floor(slidesLength / params.slidesPerColumn);\n    for (var i = 0; i < slidesLength; i += 1) {\n      slideSize = 0;\n      var slide = slides.eq(i);\n      if (params.slidesPerColumn > 1) {\n        // Set slides order\n        var newSlideOrderIndex = (void 0);\n        var column = (void 0);\n        var row = (void 0);\n        if (\n          (params.slidesPerColumnFill === 'column')\n          || (params.slidesPerColumnFill === 'row' && params.slidesPerGroup > 1)\n        ) {\n          if (params.slidesPerColumnFill === 'column') {\n            column = Math.floor(i / slidesPerColumn);\n            row = i - (column * slidesPerColumn);\n            if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn - 1)) {\n              row += 1;\n              if (row >= slidesPerColumn) {\n                row = 0;\n                column += 1;\n              }\n            }\n          } else {\n            var groupIndex = Math.floor(i / params.slidesPerGroup);\n            row = Math.floor(i / params.slidesPerView) - groupIndex * params.slidesPerColumn;\n            column = i - row * params.slidesPerView - groupIndex * params.slidesPerView;\n          }\n          newSlideOrderIndex = column + ((row * slidesNumberEvenToRows) / slidesPerColumn);\n          slide\n            .css({\n              '-webkit-box-ordinal-group': newSlideOrderIndex,\n              '-moz-box-ordinal-group': newSlideOrderIndex,\n              '-ms-flex-order': newSlideOrderIndex,\n              '-webkit-order': newSlideOrderIndex,\n              order: newSlideOrderIndex,\n            });\n        } else {\n          row = Math.floor(i / slidesPerRow);\n          column = i - (row * slidesPerRow);\n        }\n        slide\n          .css(\n            (\"margin-\" + (swiper.isHorizontal() ? 'top' : 'left')),\n            (row !== 0 && params.spaceBetween) && (((params.spaceBetween) + \"px\"))\n          )\n          .attr('data-swiper-column', column)\n          .attr('data-swiper-row', row);\n      }\n      if (slide.css('display') === 'none') { continue; } // eslint-disable-line\n\n      if (params.slidesPerView === 'auto') {\n        var slideStyles = win.getComputedStyle(slide[0], null);\n        var currentTransform = slide[0].style.transform;\n        var currentWebKitTransform = slide[0].style.webkitTransform;\n        if (currentTransform) {\n          slide[0].style.transform = 'none';\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = 'none';\n        }\n        if (params.roundLengths) {\n          slideSize = swiper.isHorizontal()\n            ? slide.outerWidth(true)\n            : slide.outerHeight(true);\n        } else {\n          // eslint-disable-next-line\n          if (swiper.isHorizontal()) {\n            var width = parseFloat(slideStyles.getPropertyValue('width'));\n            var paddingLeft = parseFloat(slideStyles.getPropertyValue('padding-left'));\n            var paddingRight = parseFloat(slideStyles.getPropertyValue('padding-right'));\n            var marginLeft = parseFloat(slideStyles.getPropertyValue('margin-left'));\n            var marginRight = parseFloat(slideStyles.getPropertyValue('margin-right'));\n            var boxSizing = slideStyles.getPropertyValue('box-sizing');\n            if (boxSizing && boxSizing === 'border-box' && !Browser.isIE) {\n              slideSize = width + marginLeft + marginRight;\n            } else {\n              slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight;\n            }\n          } else {\n            var height = parseFloat(slideStyles.getPropertyValue('height'));\n            var paddingTop = parseFloat(slideStyles.getPropertyValue('padding-top'));\n            var paddingBottom = parseFloat(slideStyles.getPropertyValue('padding-bottom'));\n            var marginTop = parseFloat(slideStyles.getPropertyValue('margin-top'));\n            var marginBottom = parseFloat(slideStyles.getPropertyValue('margin-bottom'));\n            var boxSizing$1 = slideStyles.getPropertyValue('box-sizing');\n            if (boxSizing$1 && boxSizing$1 === 'border-box' && !Browser.isIE) {\n              slideSize = height + marginTop + marginBottom;\n            } else {\n              slideSize = height + paddingTop + paddingBottom + marginTop + marginBottom;\n            }\n          }\n        }\n        if (currentTransform) {\n          slide[0].style.transform = currentTransform;\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = currentWebKitTransform;\n        }\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n      } else {\n        slideSize = (swiperSize - ((params.slidesPerView - 1) * spaceBetween)) / params.slidesPerView;\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n\n        if (slides[i]) {\n          if (swiper.isHorizontal()) {\n            slides[i].style.width = slideSize + \"px\";\n          } else {\n            slides[i].style.height = slideSize + \"px\";\n          }\n        }\n      }\n      if (slides[i]) {\n        slides[i].swiperSlideSize = slideSize;\n      }\n      slidesSizesGrid.push(slideSize);\n\n\n      if (params.centeredSlides) {\n        slidePosition = slidePosition + (slideSize / 2) + (prevSlideSize / 2) + spaceBetween;\n        if (prevSlideSize === 0 && i !== 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (i === 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (Math.abs(slidePosition) < 1 / 1000) { slidePosition = 0; }\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n      } else {\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n        slidePosition = slidePosition + slideSize + spaceBetween;\n      }\n\n      swiper.virtualSize += slideSize + spaceBetween;\n\n      prevSlideSize = slideSize;\n\n      index += 1;\n    }\n    swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n    var newSlidesGrid;\n\n    if (\n      rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n      $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") });\n    }\n    if (!Support.flexbox || params.setWrapperSize) {\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n    }\n\n    if (params.slidesPerColumn > 1) {\n      swiper.virtualSize = (slideSize + params.spaceBetween) * slidesNumberEvenToRows;\n      swiper.virtualSize = Math.ceil(swiper.virtualSize / params.slidesPerColumn) - params.spaceBetween;\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      if (params.centeredSlides) {\n        newSlidesGrid = [];\n        for (var i$1 = 0; i$1 < snapGrid.length; i$1 += 1) {\n          var slidesGridItem = snapGrid[i$1];\n          if (params.roundLengths) { slidesGridItem = Math.floor(slidesGridItem); }\n          if (snapGrid[i$1] < swiper.virtualSize + snapGrid[0]) { newSlidesGrid.push(slidesGridItem); }\n        }\n        snapGrid = newSlidesGrid;\n      }\n    }\n\n    // Remove last grid elements depending on width\n    if (!params.centeredSlides) {\n      newSlidesGrid = [];\n      for (var i$2 = 0; i$2 < snapGrid.length; i$2 += 1) {\n        var slidesGridItem$1 = snapGrid[i$2];\n        if (params.roundLengths) { slidesGridItem$1 = Math.floor(slidesGridItem$1); }\n        if (snapGrid[i$2] <= swiper.virtualSize - swiperSize) {\n          newSlidesGrid.push(slidesGridItem$1);\n        }\n      }\n      snapGrid = newSlidesGrid;\n      if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n        snapGrid.push(swiper.virtualSize - swiperSize);\n      }\n    }\n    if (snapGrid.length === 0) { snapGrid = [0]; }\n\n    if (params.spaceBetween !== 0) {\n      if (swiper.isHorizontal()) {\n        if (rtl) { slides.css({ marginLeft: (spaceBetween + \"px\") }); }\n        else { slides.css({ marginRight: (spaceBetween + \"px\") }); }\n      } else { slides.css({ marginBottom: (spaceBetween + \"px\") }); }\n    }\n\n    if (params.centerInsufficientSlides) {\n      var allSlidesSize = 0;\n      slidesSizesGrid.forEach(function (slideSizeValue) {\n        allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n      });\n      allSlidesSize -= params.spaceBetween;\n      if (allSlidesSize < swiperSize) {\n        var allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n        snapGrid.forEach(function (snap, snapIndex) {\n          snapGrid[snapIndex] = snap - allSlidesOffset;\n        });\n        slidesGrid.forEach(function (snap, snapIndex) {\n          slidesGrid[snapIndex] = snap + allSlidesOffset;\n        });\n      }\n    }\n\n    Utils.extend(swiper, {\n      slides: slides,\n      snapGrid: snapGrid,\n      slidesGrid: slidesGrid,\n      slidesSizesGrid: slidesSizesGrid,\n    });\n\n    if (slidesLength !== previousSlidesLength) {\n      swiper.emit('slidesLengthChange');\n    }\n    if (snapGrid.length !== previousSnapGridLength) {\n      if (swiper.params.watchOverflow) { swiper.checkOverflow(); }\n      swiper.emit('snapGridLengthChange');\n    }\n    if (slidesGrid.length !== previousSlidesGridLength) {\n      swiper.emit('slidesGridLengthChange');\n    }\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateSlidesOffset();\n    }\n  }\n\n  function updateAutoHeight (speed) {\n    var swiper = this;\n    var activeSlides = [];\n    var newHeight = 0;\n    var i;\n    if (typeof speed === 'number') {\n      swiper.setTransition(speed);\n    } else if (speed === true) {\n      swiper.setTransition(swiper.params.speed);\n    }\n    // Find slides currently in view\n    if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        var index = swiper.activeIndex + i;\n        if (index > swiper.slides.length) { break; }\n        activeSlides.push(swiper.slides.eq(index)[0]);\n      }\n    } else {\n      activeSlides.push(swiper.slides.eq(swiper.activeIndex)[0]);\n    }\n\n    // Find new height from highest slide in view\n    for (i = 0; i < activeSlides.length; i += 1) {\n      if (typeof activeSlides[i] !== 'undefined') {\n        var height = activeSlides[i].offsetHeight;\n        newHeight = height > newHeight ? height : newHeight;\n      }\n    }\n\n    // Update Height\n    if (newHeight) { swiper.$wrapperEl.css('height', (newHeight + \"px\")); }\n  }\n\n  function updateSlidesOffset () {\n    var swiper = this;\n    var slides = swiper.slides;\n    for (var i = 0; i < slides.length; i += 1) {\n      slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n    }\n  }\n\n  function updateSlidesProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var slides = swiper.slides;\n    var rtl = swiper.rtlTranslate;\n\n    if (slides.length === 0) { return; }\n    if (typeof slides[0].swiperSlideOffset === 'undefined') { swiper.updateSlidesOffset(); }\n\n    var offsetCenter = -translate;\n    if (rtl) { offsetCenter = translate; }\n\n    // Visible Slides\n    slides.removeClass(params.slideVisibleClass);\n\n    swiper.visibleSlidesIndexes = [];\n    swiper.visibleSlides = [];\n\n    for (var i = 0; i < slides.length; i += 1) {\n      var slide = slides[i];\n      var slideProgress = (\n        (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0)) - slide.swiperSlideOffset\n      ) / (slide.swiperSlideSize + params.spaceBetween);\n      if (params.watchSlidesVisibility) {\n        var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n        var slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n        var isVisible = (slideBefore >= 0 && slideBefore < swiper.size - 1)\n                  || (slideAfter > 1 && slideAfter <= swiper.size)\n                  || (slideBefore <= 0 && slideAfter >= swiper.size);\n        if (isVisible) {\n          swiper.visibleSlides.push(slide);\n          swiper.visibleSlidesIndexes.push(i);\n          slides.eq(i).addClass(params.slideVisibleClass);\n        }\n      }\n      slide.progress = rtl ? -slideProgress : slideProgress;\n    }\n    swiper.visibleSlides = $(swiper.visibleSlides);\n  }\n\n  function updateProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    var progress = swiper.progress;\n    var isBeginning = swiper.isBeginning;\n    var isEnd = swiper.isEnd;\n    var wasBeginning = isBeginning;\n    var wasEnd = isEnd;\n    if (translatesDiff === 0) {\n      progress = 0;\n      isBeginning = true;\n      isEnd = true;\n    } else {\n      progress = (translate - swiper.minTranslate()) / (translatesDiff);\n      isBeginning = progress <= 0;\n      isEnd = progress >= 1;\n    }\n    Utils.extend(swiper, {\n      progress: progress,\n      isBeginning: isBeginning,\n      isEnd: isEnd,\n    });\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) { swiper.updateSlidesProgress(translate); }\n\n    if (isBeginning && !wasBeginning) {\n      swiper.emit('reachBeginning toEdge');\n    }\n    if (isEnd && !wasEnd) {\n      swiper.emit('reachEnd toEdge');\n    }\n    if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n      swiper.emit('fromEdge');\n    }\n\n    swiper.emit('progress', progress);\n  }\n\n  function updateSlidesClasses () {\n    var swiper = this;\n\n    var slides = swiper.slides;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n    var realIndex = swiper.realIndex;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n\n    slides.removeClass(((params.slideActiveClass) + \" \" + (params.slideNextClass) + \" \" + (params.slidePrevClass) + \" \" + (params.slideDuplicateActiveClass) + \" \" + (params.slideDuplicateNextClass) + \" \" + (params.slideDuplicatePrevClass)));\n\n    var activeSlide;\n    if (isVirtual) {\n      activeSlide = swiper.$wrapperEl.find((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + activeIndex + \"\\\"]\"));\n    } else {\n      activeSlide = slides.eq(activeIndex);\n    }\n\n    // Active classes\n    activeSlide.addClass(params.slideActiveClass);\n\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (activeSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      }\n    }\n    // Next Slide\n    var nextSlide = activeSlide.nextAll((\".\" + (params.slideClass))).eq(0).addClass(params.slideNextClass);\n    if (params.loop && nextSlide.length === 0) {\n      nextSlide = slides.eq(0);\n      nextSlide.addClass(params.slideNextClass);\n    }\n    // Prev Slide\n    var prevSlide = activeSlide.prevAll((\".\" + (params.slideClass))).eq(0).addClass(params.slidePrevClass);\n    if (params.loop && prevSlide.length === 0) {\n      prevSlide = slides.eq(-1);\n      prevSlide.addClass(params.slidePrevClass);\n    }\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (nextSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      }\n      if (prevSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      }\n    }\n  }\n\n  function updateActiveIndex (newActiveIndex) {\n    var swiper = this;\n    var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var params = swiper.params;\n    var previousIndex = swiper.activeIndex;\n    var previousRealIndex = swiper.realIndex;\n    var previousSnapIndex = swiper.snapIndex;\n    var activeIndex = newActiveIndex;\n    var snapIndex;\n    if (typeof activeIndex === 'undefined') {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (typeof slidesGrid[i + 1] !== 'undefined') {\n          if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - ((slidesGrid[i + 1] - slidesGrid[i]) / 2)) {\n            activeIndex = i;\n          } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n            activeIndex = i + 1;\n          }\n        } else if (translate >= slidesGrid[i]) {\n          activeIndex = i;\n        }\n      }\n      // Normalize slideIndex\n      if (params.normalizeSlideIndex) {\n        if (activeIndex < 0 || typeof activeIndex === 'undefined') { activeIndex = 0; }\n      }\n    }\n    if (snapGrid.indexOf(translate) >= 0) {\n      snapIndex = snapGrid.indexOf(translate);\n    } else {\n      snapIndex = Math.floor(activeIndex / params.slidesPerGroup);\n    }\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n    if (activeIndex === previousIndex) {\n      if (snapIndex !== previousSnapIndex) {\n        swiper.snapIndex = snapIndex;\n        swiper.emit('snapIndexChange');\n      }\n      return;\n    }\n\n    // Get real index\n    var realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n\n    Utils.extend(swiper, {\n      snapIndex: snapIndex,\n      realIndex: realIndex,\n      previousIndex: previousIndex,\n      activeIndex: activeIndex,\n    });\n    swiper.emit('activeIndexChange');\n    swiper.emit('snapIndexChange');\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    if (swiper.initialized || swiper.runCallbacksOnInit) {\n      swiper.emit('slideChange');\n    }\n  }\n\n  function updateClickedSlide (e) {\n    var swiper = this;\n    var params = swiper.params;\n    var slide = $(e.target).closest((\".\" + (params.slideClass)))[0];\n    var slideFound = false;\n    if (slide) {\n      for (var i = 0; i < swiper.slides.length; i += 1) {\n        if (swiper.slides[i] === slide) { slideFound = true; }\n      }\n    }\n\n    if (slide && slideFound) {\n      swiper.clickedSlide = slide;\n      if (swiper.virtual && swiper.params.virtual.enabled) {\n        swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n      } else {\n        swiper.clickedIndex = $(slide).index();\n      }\n    } else {\n      swiper.clickedSlide = undefined;\n      swiper.clickedIndex = undefined;\n      return;\n    }\n    if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n      swiper.slideToClickedSlide();\n    }\n  }\n\n  var update = {\n    updateSize: updateSize,\n    updateSlides: updateSlides,\n    updateAutoHeight: updateAutoHeight,\n    updateSlidesOffset: updateSlidesOffset,\n    updateSlidesProgress: updateSlidesProgress,\n    updateProgress: updateProgress,\n    updateSlidesClasses: updateSlidesClasses,\n    updateActiveIndex: updateActiveIndex,\n    updateClickedSlide: updateClickedSlide,\n  };\n\n  function getTranslate (axis) {\n    if ( axis === void 0 ) axis = this.isHorizontal() ? 'x' : 'y';\n\n    var swiper = this;\n\n    var params = swiper.params;\n    var rtl = swiper.rtlTranslate;\n    var translate = swiper.translate;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    if (params.virtualTranslate) {\n      return rtl ? -translate : translate;\n    }\n\n    var currentTranslate = Utils.getTranslate($wrapperEl[0], axis);\n    if (rtl) { currentTranslate = -currentTranslate; }\n\n    return currentTranslate || 0;\n  }\n\n  function setTranslate (translate, byController) {\n    var swiper = this;\n    var rtl = swiper.rtlTranslate;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var progress = swiper.progress;\n    var x = 0;\n    var y = 0;\n    var z = 0;\n\n    if (swiper.isHorizontal()) {\n      x = rtl ? -translate : translate;\n    } else {\n      y = translate;\n    }\n\n    if (params.roundLengths) {\n      x = Math.floor(x);\n      y = Math.floor(y);\n    }\n\n    if (!params.virtualTranslate) {\n      if (Support.transforms3d) { $wrapperEl.transform((\"translate3d(\" + x + \"px, \" + y + \"px, \" + z + \"px)\")); }\n      else { $wrapperEl.transform((\"translate(\" + x + \"px, \" + y + \"px)\")); }\n    }\n    swiper.previousTranslate = swiper.translate;\n    swiper.translate = swiper.isHorizontal() ? x : y;\n\n    // Check if we need to update progress\n    var newProgress;\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    if (translatesDiff === 0) {\n      newProgress = 0;\n    } else {\n      newProgress = (translate - swiper.minTranslate()) / (translatesDiff);\n    }\n    if (newProgress !== progress) {\n      swiper.updateProgress(translate);\n    }\n\n    swiper.emit('setTranslate', swiper.translate, byController);\n  }\n\n  function minTranslate () {\n    return (-this.snapGrid[0]);\n  }\n\n  function maxTranslate () {\n    return (-this.snapGrid[this.snapGrid.length - 1]);\n  }\n\n  var translate = {\n    getTranslate: getTranslate,\n    setTranslate: setTranslate,\n    minTranslate: minTranslate,\n    maxTranslate: maxTranslate,\n  };\n\n  function setTransition (duration, byController) {\n    var swiper = this;\n\n    swiper.$wrapperEl.transition(duration);\n\n    swiper.emit('setTransition', duration, byController);\n  }\n\n  function transitionStart (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var params = swiper.params;\n    var previousIndex = swiper.previousIndex;\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionStart');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionStart');\n        return;\n      }\n      swiper.emit('slideChangeTransitionStart');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionStart');\n      } else {\n        swiper.emit('slidePrevTransitionStart');\n      }\n    }\n  }\n\n  function transitionEnd$1 (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var previousIndex = swiper.previousIndex;\n    swiper.animating = false;\n    swiper.setTransition(0);\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionEnd');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionEnd');\n        return;\n      }\n      swiper.emit('slideChangeTransitionEnd');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionEnd');\n      } else {\n        swiper.emit('slidePrevTransitionEnd');\n      }\n    }\n  }\n\n  var transition$1 = {\n    setTransition: setTransition,\n    transitionStart: transitionStart,\n    transitionEnd: transitionEnd$1,\n  };\n\n  function slideTo (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var slideIndex = index;\n    if (slideIndex < 0) { slideIndex = 0; }\n\n    var params = swiper.params;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var previousIndex = swiper.previousIndex;\n    var activeIndex = swiper.activeIndex;\n    var rtl = swiper.rtlTranslate;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return false;\n    }\n\n    var snapIndex = Math.floor(slideIndex / params.slidesPerGroup);\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n\n    if ((activeIndex || params.initialSlide || 0) === (previousIndex || 0) && runCallbacks) {\n      swiper.emit('beforeSlideChangeStart');\n    }\n\n    var translate = -snapGrid[snapIndex];\n\n    // Update progress\n    swiper.updateProgress(translate);\n\n    // Normalize slideIndex\n    if (params.normalizeSlideIndex) {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (-Math.floor(translate * 100) >= Math.floor(slidesGrid[i] * 100)) {\n          slideIndex = i;\n        }\n      }\n    }\n    // Directions locks\n    if (swiper.initialized && slideIndex !== activeIndex) {\n      if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n        if ((activeIndex || 0) !== slideIndex) { return false; }\n      }\n    }\n\n    var direction;\n    if (slideIndex > activeIndex) { direction = 'next'; }\n    else if (slideIndex < activeIndex) { direction = 'prev'; }\n    else { direction = 'reset'; }\n\n\n    // Update Index\n    if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n      swiper.updateActiveIndex(slideIndex);\n      // Update Height\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n      swiper.updateSlidesClasses();\n      if (params.effect !== 'slide') {\n        swiper.setTranslate(translate);\n      }\n      if (direction !== 'reset') {\n        swiper.transitionStart(runCallbacks, direction);\n        swiper.transitionEnd(runCallbacks, direction);\n      }\n      return false;\n    }\n\n    if (speed === 0 || !Support.transition) {\n      swiper.setTransition(0);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    } else {\n      swiper.setTransition(speed);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      if (!swiper.animating) {\n        swiper.animating = true;\n        if (!swiper.onSlideToWrapperTransitionEnd) {\n          swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n            if (!swiper || swiper.destroyed) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n            swiper.onSlideToWrapperTransitionEnd = null;\n            delete swiper.onSlideToWrapperTransitionEnd;\n            swiper.transitionEnd(runCallbacks, direction);\n          };\n        }\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n      }\n    }\n\n    return true;\n  }\n\n  function slideToLoop (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var newIndex = index;\n    if (swiper.params.loop) {\n      newIndex += swiper.loopedSlides;\n    }\n\n    return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideNext (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n      return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n    }\n    return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slidePrev (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var rtlTranslate = swiper.rtlTranslate;\n\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n    }\n    var translate = rtlTranslate ? swiper.translate : -swiper.translate;\n    function normalize(val) {\n      if (val < 0) { return -Math.floor(Math.abs(val)); }\n      return Math.floor(val);\n    }\n    var normalizedTranslate = normalize(translate);\n    var normalizedSnapGrid = snapGrid.map(function (val) { return normalize(val); });\n    var normalizedSlidesGrid = slidesGrid.map(function (val) { return normalize(val); });\n\n    var currentSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate)];\n    var prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n    var prevIndex;\n    if (typeof prevSnap !== 'undefined') {\n      prevIndex = slidesGrid.indexOf(prevSnap);\n      if (prevIndex < 0) { prevIndex = swiper.activeIndex - 1; }\n    }\n    return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideReset (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideToClosest (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var index = swiper.activeIndex;\n    var snapIndex = Math.floor(index / swiper.params.slidesPerGroup);\n\n    if (snapIndex < swiper.snapGrid.length - 1) {\n      var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n      var currentSnap = swiper.snapGrid[snapIndex];\n      var nextSnap = swiper.snapGrid[snapIndex + 1];\n\n      if ((translate - currentSnap) > (nextSnap - currentSnap) / 2) {\n        index = swiper.params.slidesPerGroup;\n      }\n    }\n\n    return swiper.slideTo(index, speed, runCallbacks, internal);\n  }\n\n  function slideToClickedSlide () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    var slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n    var slideToIndex = swiper.clickedIndex;\n    var realIndex;\n    if (params.loop) {\n      if (swiper.animating) { return; }\n      realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      if (params.centeredSlides) {\n        if (\n          (slideToIndex < swiper.loopedSlides - (slidesPerView / 2))\n          || (slideToIndex > (swiper.slides.length - swiper.loopedSlides) + (slidesPerView / 2))\n        ) {\n          swiper.loopFix();\n          slideToIndex = $wrapperEl\n            .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n            .eq(0)\n            .index();\n\n          Utils.nextTick(function () {\n            swiper.slideTo(slideToIndex);\n          });\n        } else {\n          swiper.slideTo(slideToIndex);\n        }\n      } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl\n          .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n          .eq(0)\n          .index();\n\n        Utils.nextTick(function () {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  var slide = {\n    slideTo: slideTo,\n    slideToLoop: slideToLoop,\n    slideNext: slideNext,\n    slidePrev: slidePrev,\n    slideReset: slideReset,\n    slideToClosest: slideToClosest,\n    slideToClickedSlide: slideToClickedSlide,\n  };\n\n  function loopCreate () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    // Remove duplicated slides\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n\n    var slides = $wrapperEl.children((\".\" + (params.slideClass)));\n\n    if (params.loopFillGroupWithBlank) {\n      var blankSlidesNum = params.slidesPerGroup - (slides.length % params.slidesPerGroup);\n      if (blankSlidesNum !== params.slidesPerGroup) {\n        for (var i = 0; i < blankSlidesNum; i += 1) {\n          var blankNode = $(doc.createElement('div')).addClass(((params.slideClass) + \" \" + (params.slideBlankClass)));\n          $wrapperEl.append(blankNode);\n        }\n        slides = $wrapperEl.children((\".\" + (params.slideClass)));\n      }\n    }\n\n    if (params.slidesPerView === 'auto' && !params.loopedSlides) { params.loopedSlides = slides.length; }\n\n    swiper.loopedSlides = parseInt(params.loopedSlides || params.slidesPerView, 10);\n    swiper.loopedSlides += params.loopAdditionalSlides;\n    if (swiper.loopedSlides > slides.length) {\n      swiper.loopedSlides = slides.length;\n    }\n\n    var prependSlides = [];\n    var appendSlides = [];\n    slides.each(function (index, el) {\n      var slide = $(el);\n      if (index < swiper.loopedSlides) { appendSlides.push(el); }\n      if (index < slides.length && index >= slides.length - swiper.loopedSlides) { prependSlides.push(el); }\n      slide.attr('data-swiper-slide-index', index);\n    });\n    for (var i$1 = 0; i$1 < appendSlides.length; i$1 += 1) {\n      $wrapperEl.append($(appendSlides[i$1].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n    for (var i$2 = prependSlides.length - 1; i$2 >= 0; i$2 -= 1) {\n      $wrapperEl.prepend($(prependSlides[i$2].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n  }\n\n  function loopFix () {\n    var swiper = this;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var slides = swiper.slides;\n    var loopedSlides = swiper.loopedSlides;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var allowSlideNext = swiper.allowSlideNext;\n    var snapGrid = swiper.snapGrid;\n    var rtl = swiper.rtlTranslate;\n    var newIndex;\n    swiper.allowSlidePrev = true;\n    swiper.allowSlideNext = true;\n\n    var snapTranslate = -snapGrid[activeIndex];\n    var diff = snapTranslate - swiper.getTranslate();\n\n\n    // Fix For Negative Oversliding\n    if (activeIndex < loopedSlides) {\n      newIndex = (slides.length - (loopedSlides * 3)) + activeIndex;\n      newIndex += loopedSlides;\n      var slideChanged = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    } else if ((params.slidesPerView === 'auto' && activeIndex >= loopedSlides * 2) || (activeIndex >= slides.length - loopedSlides)) {\n      // Fix For Positive Oversliding\n      newIndex = -slides.length + activeIndex + loopedSlides;\n      newIndex += loopedSlides;\n      var slideChanged$1 = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged$1 && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n  }\n\n  function loopDestroy () {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var slides = swiper.slides;\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \",.\" + (params.slideClass) + \".\" + (params.slideBlankClass))).remove();\n    slides.removeAttr('data-swiper-slide-index');\n  }\n\n  var loop = {\n    loopCreate: loopCreate,\n    loopFix: loopFix,\n    loopDestroy: loopDestroy,\n  };\n\n  function setGrabCursor (moving) {\n    var swiper = this;\n    if (Support.touch || !swiper.params.simulateTouch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    var el = swiper.el;\n    el.style.cursor = 'move';\n    el.style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n    el.style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n    el.style.cursor = moving ? 'grabbing' : 'grab';\n  }\n\n  function unsetGrabCursor () {\n    var swiper = this;\n    if (Support.touch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    swiper.el.style.cursor = '';\n  }\n\n  var grabCursor = {\n    setGrabCursor: setGrabCursor,\n    unsetGrabCursor: unsetGrabCursor,\n  };\n\n  function appendSlide (slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.append(slides[i]); }\n      }\n    } else {\n      $wrapperEl.append(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n  }\n\n  function prependSlide (slides) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    var newActiveIndex = activeIndex + 1;\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.prepend(slides[i]); }\n      }\n      newActiveIndex = activeIndex + slides.length;\n    } else {\n      $wrapperEl.prepend(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n\n  function addSlide (index, slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var baseLength = swiper.slides.length;\n    if (index <= 0) {\n      swiper.prependSlide(slides);\n      return;\n    }\n    if (index >= baseLength) {\n      swiper.appendSlide(slides);\n      return;\n    }\n    var newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n    var slidesBuffer = [];\n    for (var i = baseLength - 1; i >= index; i -= 1) {\n      var currentSlide = swiper.slides.eq(i);\n      currentSlide.remove();\n      slidesBuffer.unshift(currentSlide);\n    }\n\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (slides[i$1]) { $wrapperEl.append(slides[i$1]); }\n      }\n      newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n    } else {\n      $wrapperEl.append(slides);\n    }\n\n    for (var i$2 = 0; i$2 < slidesBuffer.length; i$2 += 1) {\n      $wrapperEl.append(slidesBuffer[i$2]);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeSlide (slidesIndexes) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var newActiveIndex = activeIndexBuffer;\n    var indexToRemove;\n\n    if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n      for (var i = 0; i < slidesIndexes.length; i += 1) {\n        indexToRemove = slidesIndexes[i];\n        if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n        if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    } else {\n      indexToRemove = slidesIndexes;\n      if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n      if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeAllSlides () {\n    var swiper = this;\n\n    var slidesIndexes = [];\n    for (var i = 0; i < swiper.slides.length; i += 1) {\n      slidesIndexes.push(i);\n    }\n    swiper.removeSlide(slidesIndexes);\n  }\n\n  var manipulation = {\n    appendSlide: appendSlide,\n    prependSlide: prependSlide,\n    addSlide: addSlide,\n    removeSlide: removeSlide,\n    removeAllSlides: removeAllSlides,\n  };\n\n  var Device = (function Device() {\n    var ua = win.navigator.userAgent;\n\n    var device = {\n      ios: false,\n      android: false,\n      androidChrome: false,\n      desktop: false,\n      windows: false,\n      iphone: false,\n      ipod: false,\n      ipad: false,\n      cordova: win.cordova || win.phonegap,\n      phonegap: win.cordova || win.phonegap,\n    };\n\n    var windows = ua.match(/(Windows Phone);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n    var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n    var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n\n\n    // Windows\n    if (windows) {\n      device.os = 'windows';\n      device.osVersion = windows[2];\n      device.windows = true;\n    }\n    // Android\n    if (android && !windows) {\n      device.os = 'android';\n      device.osVersion = android[2];\n      device.android = true;\n      device.androidChrome = ua.toLowerCase().indexOf('chrome') >= 0;\n    }\n    if (ipad || iphone || ipod) {\n      device.os = 'ios';\n      device.ios = true;\n    }\n    // iOS\n    if (iphone && !ipod) {\n      device.osVersion = iphone[2].replace(/_/g, '.');\n      device.iphone = true;\n    }\n    if (ipad) {\n      device.osVersion = ipad[2].replace(/_/g, '.');\n      device.ipad = true;\n    }\n    if (ipod) {\n      device.osVersion = ipod[3] ? ipod[3].replace(/_/g, '.') : null;\n      device.iphone = true;\n    }\n    // iOS 8+ changed UA\n    if (device.ios && device.osVersion && ua.indexOf('Version/') >= 0) {\n      if (device.osVersion.split('.')[0] === '10') {\n        device.osVersion = ua.toLowerCase().split('version/')[1].split(' ')[0];\n      }\n    }\n\n    // Desktop\n    device.desktop = !(device.os || device.android || device.webView);\n\n    // Webview\n    device.webView = (iphone || ipad || ipod) && ua.match(/.*AppleWebKit(?!.*Safari)/i);\n\n    // Minimal UI\n    if (device.os && device.os === 'ios') {\n      var osVersionArr = device.osVersion.split('.');\n      var metaViewport = doc.querySelector('meta[name=\"viewport\"]');\n      device.minimalUi = !device.webView\n        && (ipod || iphone)\n        && (osVersionArr[0] * 1 === 7 ? osVersionArr[1] * 1 >= 1 : osVersionArr[0] * 1 > 7)\n        && metaViewport && metaViewport.getAttribute('content').indexOf('minimal-ui') >= 0;\n    }\n\n    // Pixel Ratio\n    device.pixelRatio = win.devicePixelRatio || 1;\n\n    // Export object\n    return device;\n  }());\n\n  function onTouchStart (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return;\n    }\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    data.isTouchEvent = e.type === 'touchstart';\n    if (!data.isTouchEvent && 'which' in e && e.which === 3) { return; }\n    if (!data.isTouchEvent && 'button' in e && e.button > 0) { return; }\n    if (data.isTouched && data.isMoved) { return; }\n    if (params.noSwiping && $(e.target).closest(params.noSwipingSelector ? params.noSwipingSelector : (\".\" + (params.noSwipingClass)))[0]) {\n      swiper.allowClick = true;\n      return;\n    }\n    if (params.swipeHandler) {\n      if (!$(e).closest(params.swipeHandler)[0]) { return; }\n    }\n\n    touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n    touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    var startX = touches.currentX;\n    var startY = touches.currentY;\n\n    // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n\n    var edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n    var edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n    if (\n      edgeSwipeDetection\n      && ((startX <= edgeSwipeThreshold)\n      || (startX >= win.screen.width - edgeSwipeThreshold))\n    ) {\n      return;\n    }\n\n    Utils.extend(data, {\n      isTouched: true,\n      isMoved: false,\n      allowTouchCallbacks: true,\n      isScrolling: undefined,\n      startMoving: undefined,\n    });\n\n    touches.startX = startX;\n    touches.startY = startY;\n    data.touchStartTime = Utils.now();\n    swiper.allowClick = true;\n    swiper.updateSize();\n    swiper.swipeDirection = undefined;\n    if (params.threshold > 0) { data.allowThresholdMove = false; }\n    if (e.type !== 'touchstart') {\n      var preventDefault = true;\n      if ($(e.target).is(data.formElements)) { preventDefault = false; }\n      if (\n        doc.activeElement\n        && $(doc.activeElement).is(data.formElements)\n        && doc.activeElement !== e.target\n      ) {\n        doc.activeElement.blur();\n      }\n\n      var shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n      if (params.touchStartForcePreventDefault || shouldPreventDefault) {\n        e.preventDefault();\n      }\n    }\n    swiper.emit('touchStart', e);\n  }\n\n  function onTouchMove (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (!data.isTouched) {\n      if (data.startMoving && data.isScrolling) {\n        swiper.emit('touchMoveOpposite', e);\n      }\n      return;\n    }\n    if (data.isTouchEvent && e.type === 'mousemove') { return; }\n    var pageX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n    var pageY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n    if (e.preventedByNestedSwiper) {\n      touches.startX = pageX;\n      touches.startY = pageY;\n      return;\n    }\n    if (!swiper.allowTouchMove) {\n      // isMoved = true;\n      swiper.allowClick = false;\n      if (data.isTouched) {\n        Utils.extend(touches, {\n          startX: pageX,\n          startY: pageY,\n          currentX: pageX,\n          currentY: pageY,\n        });\n        data.touchStartTime = Utils.now();\n      }\n      return;\n    }\n    if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n      if (swiper.isVertical()) {\n        // Vertical\n        if (\n          (pageY < touches.startY && swiper.translate <= swiper.maxTranslate())\n          || (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n        ) {\n          data.isTouched = false;\n          data.isMoved = false;\n          return;\n        }\n      } else if (\n        (pageX < touches.startX && swiper.translate <= swiper.maxTranslate())\n        || (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n      ) {\n        return;\n      }\n    }\n    if (data.isTouchEvent && doc.activeElement) {\n      if (e.target === doc.activeElement && $(e.target).is(data.formElements)) {\n        data.isMoved = true;\n        swiper.allowClick = false;\n        return;\n      }\n    }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchMove', e);\n    }\n    if (e.targetTouches && e.targetTouches.length > 1) { return; }\n\n    touches.currentX = pageX;\n    touches.currentY = pageY;\n\n    var diffX = touches.currentX - touches.startX;\n    var diffY = touches.currentY - touches.startY;\n    if (swiper.params.threshold && Math.sqrt((Math.pow( diffX, 2 )) + (Math.pow( diffY, 2 ))) < swiper.params.threshold) { return; }\n\n    if (typeof data.isScrolling === 'undefined') {\n      var touchAngle;\n      if ((swiper.isHorizontal() && touches.currentY === touches.startY) || (swiper.isVertical() && touches.currentX === touches.startX)) {\n        data.isScrolling = false;\n      } else {\n        // eslint-disable-next-line\n        if ((diffX * diffX) + (diffY * diffY) >= 25) {\n          touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n          data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : (90 - touchAngle > params.touchAngle);\n        }\n      }\n    }\n    if (data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    if (typeof data.startMoving === 'undefined') {\n      if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n        data.startMoving = true;\n      }\n    }\n    if (data.isScrolling) {\n      data.isTouched = false;\n      return;\n    }\n    if (!data.startMoving) {\n      return;\n    }\n    swiper.allowClick = false;\n    e.preventDefault();\n    if (params.touchMoveStopPropagation && !params.nested) {\n      e.stopPropagation();\n    }\n\n    if (!data.isMoved) {\n      if (params.loop) {\n        swiper.loopFix();\n      }\n      data.startTranslate = swiper.getTranslate();\n      swiper.setTransition(0);\n      if (swiper.animating) {\n        swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n      }\n      data.allowMomentumBounce = false;\n      // Grab Cursor\n      if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n        swiper.setGrabCursor(true);\n      }\n      swiper.emit('sliderFirstMove', e);\n    }\n    swiper.emit('sliderMove', e);\n    data.isMoved = true;\n\n    var diff = swiper.isHorizontal() ? diffX : diffY;\n    touches.diff = diff;\n\n    diff *= params.touchRatio;\n    if (rtl) { diff = -diff; }\n\n    swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n    data.currentTranslate = diff + data.startTranslate;\n\n    var disableParentSwiper = true;\n    var resistanceRatio = params.resistanceRatio;\n    if (params.touchReleaseOnEdges) {\n      resistanceRatio = 0;\n    }\n    if ((diff > 0 && data.currentTranslate > swiper.minTranslate())) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.minTranslate() - 1) + (Math.pow( (-swiper.minTranslate() + data.startTranslate + diff), resistanceRatio )); }\n    } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.maxTranslate() + 1) - (Math.pow( (swiper.maxTranslate() - data.startTranslate - diff), resistanceRatio )); }\n    }\n\n    if (disableParentSwiper) {\n      e.preventedByNestedSwiper = true;\n    }\n\n    // Directions locks\n    if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n    if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n\n\n    // Threshold\n    if (params.threshold > 0) {\n      if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n        if (!data.allowThresholdMove) {\n          data.allowThresholdMove = true;\n          touches.startX = touches.currentX;\n          touches.startY = touches.currentY;\n          data.currentTranslate = data.startTranslate;\n          touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n          return;\n        }\n      } else {\n        data.currentTranslate = data.startTranslate;\n        return;\n      }\n    }\n\n    if (!params.followFinger) { return; }\n\n    // Update active index in free mode\n    if (params.freeMode || params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    if (params.freeMode) {\n      // Velocity\n      if (data.velocities.length === 0) {\n        data.velocities.push({\n          position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n          time: data.touchStartTime,\n        });\n      }\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n        time: Utils.now(),\n      });\n    }\n    // Update progress\n    swiper.updateProgress(data.currentTranslate);\n    // Update translate\n    swiper.setTranslate(data.currentTranslate);\n  }\n\n  function onTouchEnd (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var $wrapperEl = swiper.$wrapperEl;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchEnd', e);\n    }\n    data.allowTouchCallbacks = false;\n    if (!data.isTouched) {\n      if (data.isMoved && params.grabCursor) {\n        swiper.setGrabCursor(false);\n      }\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    // Return Grab Cursor\n    if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(false);\n    }\n\n    // Time diff\n    var touchEndTime = Utils.now();\n    var timeDiff = touchEndTime - data.touchStartTime;\n\n    // Tap, doubleTap, Click\n    if (swiper.allowClick) {\n      swiper.updateClickedSlide(e);\n      swiper.emit('tap', e);\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) > 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        data.clickTimeout = Utils.nextTick(function () {\n          if (!swiper || swiper.destroyed) { return; }\n          swiper.emit('click', e);\n        }, 300);\n      }\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) < 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        swiper.emit('doubleTap', e);\n      }\n    }\n\n    data.lastClickTime = Utils.now();\n    Utils.nextTick(function () {\n      if (!swiper.destroyed) { swiper.allowClick = true; }\n    });\n\n    if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n      data.isTouched = false;\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n\n    var currentPos;\n    if (params.followFinger) {\n      currentPos = rtl ? swiper.translate : -swiper.translate;\n    } else {\n      currentPos = -data.currentTranslate;\n    }\n\n    if (params.freeMode) {\n      if (currentPos < -swiper.minTranslate()) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (currentPos > -swiper.maxTranslate()) {\n        if (swiper.slides.length < snapGrid.length) {\n          swiper.slideTo(snapGrid.length - 1);\n        } else {\n          swiper.slideTo(swiper.slides.length - 1);\n        }\n        return;\n      }\n\n      if (params.freeModeMomentum) {\n        if (data.velocities.length > 1) {\n          var lastMoveEvent = data.velocities.pop();\n          var velocityEvent = data.velocities.pop();\n\n          var distance = lastMoveEvent.position - velocityEvent.position;\n          var time = lastMoveEvent.time - velocityEvent.time;\n          swiper.velocity = distance / time;\n          swiper.velocity /= 2;\n          if (Math.abs(swiper.velocity) < params.freeModeMinimumVelocity) {\n            swiper.velocity = 0;\n          }\n          // this implies that the user stopped moving a finger then released.\n          // There would be no events with distance zero, so the last event is stale.\n          if (time > 150 || (Utils.now() - lastMoveEvent.time) > 300) {\n            swiper.velocity = 0;\n          }\n        } else {\n          swiper.velocity = 0;\n        }\n        swiper.velocity *= params.freeModeMomentumVelocityRatio;\n\n        data.velocities.length = 0;\n        var momentumDuration = 1000 * params.freeModeMomentumRatio;\n        var momentumDistance = swiper.velocity * momentumDuration;\n\n        var newPosition = swiper.translate + momentumDistance;\n        if (rtl) { newPosition = -newPosition; }\n\n        var doBounce = false;\n        var afterBouncePosition;\n        var bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeModeMomentumBounceRatio;\n        var needsLoopFix;\n        if (newPosition < swiper.maxTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n              newPosition = swiper.maxTranslate() - bounceAmount;\n            }\n            afterBouncePosition = swiper.maxTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.maxTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (newPosition > swiper.minTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition - swiper.minTranslate() > bounceAmount) {\n              newPosition = swiper.minTranslate() + bounceAmount;\n            }\n            afterBouncePosition = swiper.minTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.minTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (params.freeModeSticky) {\n          var nextSlide;\n          for (var j = 0; j < snapGrid.length; j += 1) {\n            if (snapGrid[j] > -newPosition) {\n              nextSlide = j;\n              break;\n            }\n          }\n\n          if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n            newPosition = snapGrid[nextSlide];\n          } else {\n            newPosition = snapGrid[nextSlide - 1];\n          }\n          newPosition = -newPosition;\n        }\n        if (needsLoopFix) {\n          swiper.once('transitionEnd', function () {\n            swiper.loopFix();\n          });\n        }\n        // Fix duration\n        if (swiper.velocity !== 0) {\n          if (rtl) {\n            momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n          } else {\n            momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n          }\n        } else if (params.freeModeSticky) {\n          swiper.slideToClosest();\n          return;\n        }\n\n        if (params.freeModeMomentumBounce && doBounce) {\n          swiper.updateProgress(afterBouncePosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          swiper.animating = true;\n          $wrapperEl.transitionEnd(function () {\n            if (!swiper || swiper.destroyed || !data.allowMomentumBounce) { return; }\n            swiper.emit('momentumBounce');\n\n            swiper.setTransition(params.speed);\n            swiper.setTranslate(afterBouncePosition);\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          });\n        } else if (swiper.velocity) {\n          swiper.updateProgress(newPosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          if (!swiper.animating) {\n            swiper.animating = true;\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          }\n        } else {\n          swiper.updateProgress(newPosition);\n        }\n\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      } else if (params.freeModeSticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (!params.freeModeMomentum || timeDiff >= params.longSwipesMs) {\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      return;\n    }\n\n    // Find current slide\n    var stopIndex = 0;\n    var groupSize = swiper.slidesSizesGrid[0];\n    for (var i = 0; i < slidesGrid.length; i += params.slidesPerGroup) {\n      if (typeof slidesGrid[i + params.slidesPerGroup] !== 'undefined') {\n        if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + params.slidesPerGroup]) {\n          stopIndex = i;\n          groupSize = slidesGrid[i + params.slidesPerGroup] - slidesGrid[i];\n        }\n      } else if (currentPos >= slidesGrid[i]) {\n        stopIndex = i;\n        groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n      }\n    }\n\n    // Find current slide size\n    var ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n\n    if (timeDiff > params.longSwipesMs) {\n      // Long touches\n      if (!params.longSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        if (ratio >= params.longSwipesRatio) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n      if (swiper.swipeDirection === 'prev') {\n        if (ratio > (1 - params.longSwipesRatio)) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n    } else {\n      // Short swipes\n      if (!params.shortSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(stopIndex + params.slidesPerGroup);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  }\n\n  function onResize () {\n    var swiper = this;\n\n    var params = swiper.params;\n    var el = swiper.el;\n\n    if (el && el.offsetWidth === 0) { return; }\n\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Save locks\n    var allowSlideNext = swiper.allowSlideNext;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var snapGrid = swiper.snapGrid;\n\n    // Disable locks on resize\n    swiper.allowSlideNext = true;\n    swiper.allowSlidePrev = true;\n\n    swiper.updateSize();\n    swiper.updateSlides();\n\n    if (params.freeMode) {\n      var newTranslate = Math.min(Math.max(swiper.translate, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      swiper.updateSlidesClasses();\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n    }\n    if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n      swiper.autoplay.run();\n    }\n    // Return locks after resize\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n\n    if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n  }\n\n  function onClick (e) {\n    var swiper = this;\n    if (!swiper.allowClick) {\n      if (swiper.params.preventClicks) { e.preventDefault(); }\n      if (swiper.params.preventClicksPropagation && swiper.animating) {\n        e.stopPropagation();\n        e.stopImmediatePropagation();\n      }\n    }\n  }\n\n  function attachEvents() {\n    var swiper = this;\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    {\n      swiper.onTouchStart = onTouchStart.bind(swiper);\n      swiper.onTouchMove = onTouchMove.bind(swiper);\n      swiper.onTouchEnd = onTouchEnd.bind(swiper);\n    }\n\n    swiper.onClick = onClick.bind(swiper);\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.addEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.addEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'touchstart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.addEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.addEventListener(touchEvents.move, swiper.onTouchMove, Support.passiveListener ? { passive: false, capture: capture } : capture);\n          target.addEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.onTouchStart, false);\n          doc.addEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.addEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.addEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.on((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize, true);\n  }\n\n  function detachEvents() {\n    var swiper = this;\n\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.removeEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'onTouchStart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.removeEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n          target.removeEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.onTouchStart, false);\n          doc.removeEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.removeEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.removeEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.off((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize);\n  }\n\n  var events = {\n    attachEvents: attachEvents,\n    detachEvents: detachEvents,\n  };\n\n  function setBreakpoint () {\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var initialized = swiper.initialized;\n    var loopedSlides = swiper.loopedSlides; if ( loopedSlides === void 0 ) loopedSlides = 0;\n    var params = swiper.params;\n    var breakpoints = params.breakpoints;\n    if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) { return; }\n\n    // Set breakpoint for window width and update parameters\n    var breakpoint = swiper.getBreakpoint(breakpoints);\n\n    if (breakpoint && swiper.currentBreakpoint !== breakpoint) {\n      var breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n      if (breakpointOnlyParams) {\n        ['slidesPerView', 'spaceBetween', 'slidesPerGroup'].forEach(function (param) {\n          var paramValue = breakpointOnlyParams[param];\n          if (typeof paramValue === 'undefined') { return; }\n          if (param === 'slidesPerView' && (paramValue === 'AUTO' || paramValue === 'auto')) {\n            breakpointOnlyParams[param] = 'auto';\n          } else if (param === 'slidesPerView') {\n            breakpointOnlyParams[param] = parseFloat(paramValue);\n          } else {\n            breakpointOnlyParams[param] = parseInt(paramValue, 10);\n          }\n        });\n      }\n\n      var breakpointParams = breakpointOnlyParams || swiper.originalParams;\n      var directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n      var needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n      if (directionChanged && initialized) {\n        swiper.changeDirection();\n      }\n\n      Utils.extend(swiper.params, breakpointParams);\n\n      Utils.extend(swiper, {\n        allowTouchMove: swiper.params.allowTouchMove,\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n      });\n\n      swiper.currentBreakpoint = breakpoint;\n\n      if (needsReLoop && initialized) {\n        swiper.loopDestroy();\n        swiper.loopCreate();\n        swiper.updateSlides();\n        swiper.slideTo((activeIndex - loopedSlides) + swiper.loopedSlides, 0, false);\n      }\n\n      swiper.emit('breakpoint', breakpointParams);\n    }\n  }\n\n  function getBreakpoint (breakpoints) {\n    var swiper = this;\n    // Get breakpoint for window width\n    if (!breakpoints) { return undefined; }\n    var breakpoint = false;\n    var points = [];\n    Object.keys(breakpoints).forEach(function (point) {\n      points.push(point);\n    });\n    points.sort(function (a, b) { return parseInt(a, 10) - parseInt(b, 10); });\n    for (var i = 0; i < points.length; i += 1) {\n      var point = points[i];\n      if (swiper.params.breakpointsInverse) {\n        if (point <= win.innerWidth) {\n          breakpoint = point;\n        }\n      } else if (point >= win.innerWidth && !breakpoint) {\n        breakpoint = point;\n      }\n    }\n    return breakpoint || 'max';\n  }\n\n  var breakpoints = { setBreakpoint: setBreakpoint, getBreakpoint: getBreakpoint };\n\n  function addClasses () {\n    var swiper = this;\n    var classNames = swiper.classNames;\n    var params = swiper.params;\n    var rtl = swiper.rtl;\n    var $el = swiper.$el;\n    var suffixes = [];\n\n    suffixes.push('initialized');\n    suffixes.push(params.direction);\n\n    if (params.freeMode) {\n      suffixes.push('free-mode');\n    }\n    if (!Support.flexbox) {\n      suffixes.push('no-flexbox');\n    }\n    if (params.autoHeight) {\n      suffixes.push('autoheight');\n    }\n    if (rtl) {\n      suffixes.push('rtl');\n    }\n    if (params.slidesPerColumn > 1) {\n      suffixes.push('multirow');\n    }\n    if (Device.android) {\n      suffixes.push('android');\n    }\n    if (Device.ios) {\n      suffixes.push('ios');\n    }\n    // WP8 Touch Events Fix\n    if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n      suffixes.push((\"wp8-\" + (params.direction)));\n    }\n\n    suffixes.forEach(function (suffix) {\n      classNames.push(params.containerModifierClass + suffix);\n    });\n\n    $el.addClass(classNames.join(' '));\n  }\n\n  function removeClasses () {\n    var swiper = this;\n    var $el = swiper.$el;\n    var classNames = swiper.classNames;\n\n    $el.removeClass(classNames.join(' '));\n  }\n\n  var classes = { addClasses: addClasses, removeClasses: removeClasses };\n\n  function loadImage (imageEl, src, srcset, sizes, checkForComplete, callback) {\n    var image;\n    function onReady() {\n      if (callback) { callback(); }\n    }\n    if (!imageEl.complete || !checkForComplete) {\n      if (src) {\n        image = new win.Image();\n        image.onload = onReady;\n        image.onerror = onReady;\n        if (sizes) {\n          image.sizes = sizes;\n        }\n        if (srcset) {\n          image.srcset = srcset;\n        }\n        if (src) {\n          image.src = src;\n        }\n      } else {\n        onReady();\n      }\n    } else {\n      // image already loaded...\n      onReady();\n    }\n  }\n\n  function preloadImages () {\n    var swiper = this;\n    swiper.imagesToLoad = swiper.$el.find('img');\n    function onReady() {\n      if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) { return; }\n      if (swiper.imagesLoaded !== undefined) { swiper.imagesLoaded += 1; }\n      if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n        if (swiper.params.updateOnImagesReady) { swiper.update(); }\n        swiper.emit('imagesReady');\n      }\n    }\n    for (var i = 0; i < swiper.imagesToLoad.length; i += 1) {\n      var imageEl = swiper.imagesToLoad[i];\n      swiper.loadImage(\n        imageEl,\n        imageEl.currentSrc || imageEl.getAttribute('src'),\n        imageEl.srcset || imageEl.getAttribute('srcset'),\n        imageEl.sizes || imageEl.getAttribute('sizes'),\n        true,\n        onReady\n      );\n    }\n  }\n\n  var images = {\n    loadImage: loadImage,\n    preloadImages: preloadImages,\n  };\n\n  function checkOverflow() {\n    var swiper = this;\n    var wasLocked = swiper.isLocked;\n\n    swiper.isLocked = swiper.snapGrid.length === 1;\n    swiper.allowSlideNext = !swiper.isLocked;\n    swiper.allowSlidePrev = !swiper.isLocked;\n\n    // events\n    if (wasLocked !== swiper.isLocked) { swiper.emit(swiper.isLocked ? 'lock' : 'unlock'); }\n\n    if (wasLocked && wasLocked !== swiper.isLocked) {\n      swiper.isEnd = false;\n      swiper.navigation.update();\n    }\n  }\n\n  var checkOverflow$1 = { checkOverflow: checkOverflow };\n\n  var defaults = {\n    init: true,\n    direction: 'horizontal',\n    touchEventsTarget: 'container',\n    initialSlide: 0,\n    speed: 300,\n    //\n    preventInteractionOnTransition: false,\n\n    // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n    edgeSwipeDetection: false,\n    edgeSwipeThreshold: 20,\n\n    // Free mode\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n\n    // Autoheight\n    autoHeight: false,\n\n    // Set wrapper width\n    setWrapperSize: false,\n\n    // Virtual Translate\n    virtualTranslate: false,\n\n    // Effects\n    effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n    // Breakpoints\n    breakpoints: undefined,\n    breakpointsInverse: false,\n\n    // Slides grid\n    spaceBetween: 0,\n    slidesPerView: 1,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0, // in px\n    slidesOffsetAfter: 0, // in px\n    normalizeSlideIndex: true,\n    centerInsufficientSlides: false,\n\n    // Disable swiper and hide navigation when container not overflow\n    watchOverflow: false,\n\n    // Round length\n    roundLengths: false,\n\n    // Touches\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    allowTouchMove: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchStartPreventDefault: true,\n    touchStartForcePreventDefault: false,\n    touchReleaseOnEdges: false,\n\n    // Unique Navigation Elements\n    uniqueNavElements: true,\n\n    // Resistance\n    resistance: true,\n    resistanceRatio: 0.85,\n\n    // Progress\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n\n    // Cursor\n    grabCursor: false,\n\n    // Clicks\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n\n    // Images\n    preloadImages: true,\n    updateOnImagesReady: true,\n\n    // loop\n    loop: false,\n    loopAdditionalSlides: 0,\n    loopedSlides: null,\n    loopFillGroupWithBlank: false,\n\n    // Swiping/no swiping\n    allowSlidePrev: true,\n    allowSlideNext: true,\n    swipeHandler: null, // '.swipe-handler',\n    noSwiping: true,\n    noSwipingClass: 'swiper-no-swiping',\n    noSwipingSelector: null,\n\n    // Passive Listeners\n    passiveListeners: true,\n\n    // NS\n    containerModifierClass: 'swiper-container-', // NEW\n    slideClass: 'swiper-slide',\n    slideBlankClass: 'swiper-slide-invisible-blank',\n    slideActiveClass: 'swiper-slide-active',\n    slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n    slideVisibleClass: 'swiper-slide-visible',\n    slideDuplicateClass: 'swiper-slide-duplicate',\n    slideNextClass: 'swiper-slide-next',\n    slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n    slidePrevClass: 'swiper-slide-prev',\n    slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n    wrapperClass: 'swiper-wrapper',\n\n    // Callbacks\n    runCallbacksOnInit: true,\n  };\n\n  /* eslint no-param-reassign: \"off\" */\n\n  var prototypes = {\n    update: update,\n    translate: translate,\n    transition: transition$1,\n    slide: slide,\n    loop: loop,\n    grabCursor: grabCursor,\n    manipulation: manipulation,\n    events: events,\n    breakpoints: breakpoints,\n    checkOverflow: checkOverflow$1,\n    classes: classes,\n    images: images,\n  };\n\n  var extendedDefaults = {};\n\n  var Swiper = /*@__PURE__*/(function (SwiperClass) {\n    function Swiper() {\n      var assign;\n\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n      var el;\n      var params;\n      if (args.length === 1 && args[0].constructor && args[0].constructor === Object) {\n        params = args[0];\n      } else {\n        (assign = args, el = assign[0], params = assign[1]);\n      }\n      if (!params) { params = {}; }\n\n      params = Utils.extend({}, params);\n      if (el && !params.el) { params.el = el; }\n\n      SwiperClass.call(this, params);\n\n      Object.keys(prototypes).forEach(function (prototypeGroup) {\n        Object.keys(prototypes[prototypeGroup]).forEach(function (protoMethod) {\n          if (!Swiper.prototype[protoMethod]) {\n            Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n          }\n        });\n      });\n\n      // Swiper Instance\n      var swiper = this;\n      if (typeof swiper.modules === 'undefined') {\n        swiper.modules = {};\n      }\n      Object.keys(swiper.modules).forEach(function (moduleName) {\n        var module = swiper.modules[moduleName];\n        if (module.params) {\n          var moduleParamName = Object.keys(module.params)[0];\n          var moduleParams = module.params[moduleParamName];\n          if (typeof moduleParams !== 'object' || moduleParams === null) { return; }\n          if (!(moduleParamName in params && 'enabled' in moduleParams)) { return; }\n          if (params[moduleParamName] === true) {\n            params[moduleParamName] = { enabled: true };\n          }\n          if (\n            typeof params[moduleParamName] === 'object'\n            && !('enabled' in params[moduleParamName])\n          ) {\n            params[moduleParamName].enabled = true;\n          }\n          if (!params[moduleParamName]) { params[moduleParamName] = { enabled: false }; }\n        }\n      });\n\n      // Extend defaults with modules params\n      var swiperParams = Utils.extend({}, defaults);\n      swiper.useModulesParams(swiperParams);\n\n      // Extend defaults with passed params\n      swiper.params = Utils.extend({}, swiperParams, extendedDefaults, params);\n      swiper.originalParams = Utils.extend({}, swiper.params);\n      swiper.passedParams = Utils.extend({}, params);\n\n      // Save Dom lib\n      swiper.$ = $;\n\n      // Find el\n      var $el = $(swiper.params.el);\n      el = $el[0];\n\n      if (!el) {\n        return undefined;\n      }\n\n      if ($el.length > 1) {\n        var swipers = [];\n        $el.each(function (index, containerEl) {\n          var newParams = Utils.extend({}, params, { el: containerEl });\n          swipers.push(new Swiper(newParams));\n        });\n        return swipers;\n      }\n\n      el.swiper = swiper;\n      $el.data('swiper', swiper);\n\n      // Find Wrapper\n      var $wrapperEl = $el.children((\".\" + (swiper.params.wrapperClass)));\n\n      // Extend Swiper\n      Utils.extend(swiper, {\n        $el: $el,\n        el: el,\n        $wrapperEl: $wrapperEl,\n        wrapperEl: $wrapperEl[0],\n\n        // Classes\n        classNames: [],\n\n        // Slides\n        slides: $(),\n        slidesGrid: [],\n        snapGrid: [],\n        slidesSizesGrid: [],\n\n        // isDirection\n        isHorizontal: function isHorizontal() {\n          return swiper.params.direction === 'horizontal';\n        },\n        isVertical: function isVertical() {\n          return swiper.params.direction === 'vertical';\n        },\n        // RTL\n        rtl: (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        wrongRTL: $wrapperEl.css('display') === '-webkit-box',\n\n        // Indexes\n        activeIndex: 0,\n        realIndex: 0,\n\n        //\n        isBeginning: true,\n        isEnd: false,\n\n        // Props\n        translate: 0,\n        previousTranslate: 0,\n        progress: 0,\n        velocity: 0,\n        animating: false,\n\n        // Locks\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n\n        // Touch Events\n        touchEvents: (function touchEvents() {\n          var touch = ['touchstart', 'touchmove', 'touchend'];\n          var desktop = ['mousedown', 'mousemove', 'mouseup'];\n          if (Support.pointerEvents) {\n            desktop = ['pointerdown', 'pointermove', 'pointerup'];\n          } else if (Support.prefixedPointerEvents) {\n            desktop = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp'];\n          }\n          swiper.touchEventsTouch = {\n            start: touch[0],\n            move: touch[1],\n            end: touch[2],\n          };\n          swiper.touchEventsDesktop = {\n            start: desktop[0],\n            move: desktop[1],\n            end: desktop[2],\n          };\n          return Support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n        }()),\n        touchEventsData: {\n          isTouched: undefined,\n          isMoved: undefined,\n          allowTouchCallbacks: undefined,\n          touchStartTime: undefined,\n          isScrolling: undefined,\n          currentTranslate: undefined,\n          startTranslate: undefined,\n          allowThresholdMove: undefined,\n          // Form elements to match\n          formElements: 'input, select, option, textarea, button, video',\n          // Last click time\n          lastClickTime: Utils.now(),\n          clickTimeout: undefined,\n          // Velocities\n          velocities: [],\n          allowMomentumBounce: undefined,\n          isTouchEvent: undefined,\n          startMoving: undefined,\n        },\n\n        // Clicks\n        allowClick: true,\n\n        // Touches\n        allowTouchMove: swiper.params.allowTouchMove,\n\n        touches: {\n          startX: 0,\n          startY: 0,\n          currentX: 0,\n          currentY: 0,\n          diff: 0,\n        },\n\n        // Images\n        imagesToLoad: [],\n        imagesLoaded: 0,\n\n      });\n\n      // Install Modules\n      swiper.useModules();\n\n      // Init\n      if (swiper.params.init) {\n        swiper.init();\n      }\n\n      // Return app instance\n      return swiper;\n    }\n\n    if ( SwiperClass ) Swiper.__proto__ = SwiperClass;\n    Swiper.prototype = Object.create( SwiperClass && SwiperClass.prototype );\n    Swiper.prototype.constructor = Swiper;\n\n    var staticAccessors = { extendedDefaults: { configurable: true },defaults: { configurable: true },Class: { configurable: true },$: { configurable: true } };\n\n    Swiper.prototype.slidesPerViewDynamic = function slidesPerViewDynamic () {\n      var swiper = this;\n      var params = swiper.params;\n      var slides = swiper.slides;\n      var slidesGrid = swiper.slidesGrid;\n      var swiperSize = swiper.size;\n      var activeIndex = swiper.activeIndex;\n      var spv = 1;\n      if (params.centeredSlides) {\n        var slideSize = slides[activeIndex].swiperSlideSize;\n        var breakLoop;\n        for (var i = activeIndex + 1; i < slides.length; i += 1) {\n          if (slides[i] && !breakLoop) {\n            slideSize += slides[i].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n        for (var i$1 = activeIndex - 1; i$1 >= 0; i$1 -= 1) {\n          if (slides[i$1] && !breakLoop) {\n            slideSize += slides[i$1].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n      } else {\n        for (var i$2 = activeIndex + 1; i$2 < slides.length; i$2 += 1) {\n          if (slidesGrid[i$2] - slidesGrid[activeIndex] < swiperSize) {\n            spv += 1;\n          }\n        }\n      }\n      return spv;\n    };\n\n    Swiper.prototype.update = function update () {\n      var swiper = this;\n      if (!swiper || swiper.destroyed) { return; }\n      var snapGrid = swiper.snapGrid;\n      var params = swiper.params;\n      // Breakpoints\n      if (params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n      swiper.updateSize();\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n\n      function setTranslate() {\n        var translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n        var newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n        swiper.setTranslate(newTranslate);\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      var translated;\n      if (swiper.params.freeMode) {\n        setTranslate();\n        if (swiper.params.autoHeight) {\n          swiper.updateAutoHeight();\n        }\n      } else {\n        if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n          translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n        } else {\n          translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n        }\n        if (!translated) {\n          setTranslate();\n        }\n      }\n      if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n        swiper.checkOverflow();\n      }\n      swiper.emit('update');\n    };\n\n    Swiper.prototype.changeDirection = function changeDirection (newDirection, needUpdate) {\n      if ( needUpdate === void 0 ) needUpdate = true;\n\n      var swiper = this;\n      var currentDirection = swiper.params.direction;\n      if (!newDirection) {\n        // eslint-disable-next-line\n        newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n      }\n      if ((newDirection === currentDirection) || (newDirection !== 'horizontal' && newDirection !== 'vertical')) {\n        return swiper;\n      }\n\n      swiper.$el\n        .removeClass((\"\" + (swiper.params.containerModifierClass) + currentDirection + \" wp8-\" + currentDirection))\n        .addClass((\"\" + (swiper.params.containerModifierClass) + newDirection));\n\n      if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        swiper.$el.addClass(((swiper.params.containerModifierClass) + \"wp8-\" + newDirection));\n      }\n\n      swiper.params.direction = newDirection;\n\n      swiper.slides.each(function (slideIndex, slideEl) {\n        if (newDirection === 'vertical') {\n          slideEl.style.width = '';\n        } else {\n          slideEl.style.height = '';\n        }\n      });\n\n      swiper.emit('changeDirection');\n      if (needUpdate) { swiper.update(); }\n\n      return swiper;\n    };\n\n    Swiper.prototype.init = function init () {\n      var swiper = this;\n      if (swiper.initialized) { return; }\n\n      swiper.emit('beforeInit');\n\n      // Set breakpoint\n      if (swiper.params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n\n      // Add Classes\n      swiper.addClasses();\n\n      // Create loop\n      if (swiper.params.loop) {\n        swiper.loopCreate();\n      }\n\n      // Update size\n      swiper.updateSize();\n\n      // Update slides\n      swiper.updateSlides();\n\n      if (swiper.params.watchOverflow) {\n        swiper.checkOverflow();\n      }\n\n      // Set Grab Cursor\n      if (swiper.params.grabCursor) {\n        swiper.setGrabCursor();\n      }\n\n      if (swiper.params.preloadImages) {\n        swiper.preloadImages();\n      }\n\n      // Slide To Initial Slide\n      if (swiper.params.loop) {\n        swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit);\n      } else {\n        swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit);\n      }\n\n      // Attach events\n      swiper.attachEvents();\n\n      // Init Flag\n      swiper.initialized = true;\n\n      // Emit\n      swiper.emit('init');\n    };\n\n    Swiper.prototype.destroy = function destroy (deleteInstance, cleanStyles) {\n      if ( deleteInstance === void 0 ) deleteInstance = true;\n      if ( cleanStyles === void 0 ) cleanStyles = true;\n\n      var swiper = this;\n      var params = swiper.params;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n\n      if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n        return null;\n      }\n\n      swiper.emit('beforeDestroy');\n\n      // Init Flag\n      swiper.initialized = false;\n\n      // Detach events\n      swiper.detachEvents();\n\n      // Destroy loop\n      if (params.loop) {\n        swiper.loopDestroy();\n      }\n\n      // Cleanup styles\n      if (cleanStyles) {\n        swiper.removeClasses();\n        $el.removeAttr('style');\n        $wrapperEl.removeAttr('style');\n        if (slides && slides.length) {\n          slides\n            .removeClass([\n              params.slideVisibleClass,\n              params.slideActiveClass,\n              params.slideNextClass,\n              params.slidePrevClass ].join(' '))\n            .removeAttr('style')\n            .removeAttr('data-swiper-slide-index')\n            .removeAttr('data-swiper-column')\n            .removeAttr('data-swiper-row');\n        }\n      }\n\n      swiper.emit('destroy');\n\n      // Detach emitter events\n      Object.keys(swiper.eventsListeners).forEach(function (eventName) {\n        swiper.off(eventName);\n      });\n\n      if (deleteInstance !== false) {\n        swiper.$el[0].swiper = null;\n        swiper.$el.data('swiper', null);\n        Utils.deleteProps(swiper);\n      }\n      swiper.destroyed = true;\n\n      return null;\n    };\n\n    Swiper.extendDefaults = function extendDefaults (newDefaults) {\n      Utils.extend(extendedDefaults, newDefaults);\n    };\n\n    staticAccessors.extendedDefaults.get = function () {\n      return extendedDefaults;\n    };\n\n    staticAccessors.defaults.get = function () {\n      return defaults;\n    };\n\n    staticAccessors.Class.get = function () {\n      return SwiperClass;\n    };\n\n    staticAccessors.$.get = function () {\n      return $;\n    };\n\n    Object.defineProperties( Swiper, staticAccessors );\n\n    return Swiper;\n  }(SwiperClass));\n\n  var Device$1 = {\n    name: 'device',\n    proto: {\n      device: Device,\n    },\n    static: {\n      device: Device,\n    },\n  };\n\n  var Support$1 = {\n    name: 'support',\n    proto: {\n      support: Support,\n    },\n    static: {\n      support: Support,\n    },\n  };\n\n  var Browser$1 = {\n    name: 'browser',\n    proto: {\n      browser: Browser,\n    },\n    static: {\n      browser: Browser,\n    },\n  };\n\n  var Resize = {\n    name: 'resize',\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        resize: {\n          resizeHandler: function resizeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('beforeResize');\n            swiper.emit('resize');\n          },\n          orientationChangeHandler: function orientationChangeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('orientationchange');\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        // Emit resize\n        win.addEventListener('resize', swiper.resize.resizeHandler);\n\n        // Emit orientationchange\n        win.addEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        win.removeEventListener('resize', swiper.resize.resizeHandler);\n        win.removeEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n    },\n  };\n\n  var Observer = {\n    func: win.MutationObserver || win.WebkitMutationObserver,\n    attach: function attach(target, options) {\n      if ( options === void 0 ) options = {};\n\n      var swiper = this;\n\n      var ObserverFunc = Observer.func;\n      var observer = new ObserverFunc(function (mutations) {\n        // The observerUpdate event should only be triggered\n        // once despite the number of mutations.  Additional\n        // triggers are redundant and are very costly\n        if (mutations.length === 1) {\n          swiper.emit('observerUpdate', mutations[0]);\n          return;\n        }\n        var observerUpdate = function observerUpdate() {\n          swiper.emit('observerUpdate', mutations[0]);\n        };\n\n        if (win.requestAnimationFrame) {\n          win.requestAnimationFrame(observerUpdate);\n        } else {\n          win.setTimeout(observerUpdate, 0);\n        }\n      });\n\n      observer.observe(target, {\n        attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n        childList: typeof options.childList === 'undefined' ? true : options.childList,\n        characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n      });\n\n      swiper.observer.observers.push(observer);\n    },\n    init: function init() {\n      var swiper = this;\n      if (!Support.observer || !swiper.params.observer) { return; }\n      if (swiper.params.observeParents) {\n        var containerParents = swiper.$el.parents();\n        for (var i = 0; i < containerParents.length; i += 1) {\n          swiper.observer.attach(containerParents[i]);\n        }\n      }\n      // Observe container\n      swiper.observer.attach(swiper.$el[0], { childList: swiper.params.observeSlideChildren });\n\n      // Observe wrapper\n      swiper.observer.attach(swiper.$wrapperEl[0], { attributes: false });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.observer.observers.forEach(function (observer) {\n        observer.disconnect();\n      });\n      swiper.observer.observers = [];\n    },\n  };\n\n  var Observer$1 = {\n    name: 'observer',\n    params: {\n      observer: false,\n      observeParents: false,\n      observeSlideChildren: false,\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        observer: {\n          init: Observer.init.bind(swiper),\n          attach: Observer.attach.bind(swiper),\n          destroy: Observer.destroy.bind(swiper),\n          observers: [],\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.observer.init();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.observer.destroy();\n      },\n    },\n  };\n\n  var Virtual = {\n    update: function update(force) {\n      var swiper = this;\n      var ref = swiper.params;\n      var slidesPerView = ref.slidesPerView;\n      var slidesPerGroup = ref.slidesPerGroup;\n      var centeredSlides = ref.centeredSlides;\n      var ref$1 = swiper.params.virtual;\n      var addSlidesBefore = ref$1.addSlidesBefore;\n      var addSlidesAfter = ref$1.addSlidesAfter;\n      var ref$2 = swiper.virtual;\n      var previousFrom = ref$2.from;\n      var previousTo = ref$2.to;\n      var slides = ref$2.slides;\n      var previousSlidesGrid = ref$2.slidesGrid;\n      var renderSlide = ref$2.renderSlide;\n      var previousOffset = ref$2.offset;\n      swiper.updateActiveIndex();\n      var activeIndex = swiper.activeIndex || 0;\n\n      var offsetProp;\n      if (swiper.rtlTranslate) { offsetProp = 'right'; }\n      else { offsetProp = swiper.isHorizontal() ? 'left' : 'top'; }\n\n      var slidesAfter;\n      var slidesBefore;\n      if (centeredSlides) {\n        slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n        slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      } else {\n        slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesBefore;\n        slidesBefore = slidesPerGroup + addSlidesAfter;\n      }\n      var from = Math.max((activeIndex || 0) - slidesBefore, 0);\n      var to = Math.min((activeIndex || 0) + slidesAfter, slides.length - 1);\n      var offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n\n      Utils.extend(swiper.virtual, {\n        from: from,\n        to: to,\n        offset: offset,\n        slidesGrid: swiper.slidesGrid,\n      });\n\n      function onRendered() {\n        swiper.updateSlides();\n        swiper.updateProgress();\n        swiper.updateSlidesClasses();\n        if (swiper.lazy && swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      }\n\n      if (previousFrom === from && previousTo === to && !force) {\n        if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n          swiper.slides.css(offsetProp, (offset + \"px\"));\n        }\n        swiper.updateProgress();\n        return;\n      }\n      if (swiper.params.virtual.renderExternal) {\n        swiper.params.virtual.renderExternal.call(swiper, {\n          offset: offset,\n          from: from,\n          to: to,\n          slides: (function getSlides() {\n            var slidesToRender = [];\n            for (var i = from; i <= to; i += 1) {\n              slidesToRender.push(slides[i]);\n            }\n            return slidesToRender;\n          }()),\n        });\n        onRendered();\n        return;\n      }\n      var prependIndexes = [];\n      var appendIndexes = [];\n      if (force) {\n        swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass))).remove();\n      } else {\n        for (var i = previousFrom; i <= previousTo; i += 1) {\n          if (i < from || i > to) {\n            swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + i + \"\\\"]\")).remove();\n          }\n        }\n      }\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (i$1 >= from && i$1 <= to) {\n          if (typeof previousTo === 'undefined' || force) {\n            appendIndexes.push(i$1);\n          } else {\n            if (i$1 > previousTo) { appendIndexes.push(i$1); }\n            if (i$1 < previousFrom) { prependIndexes.push(i$1); }\n          }\n        }\n      }\n      appendIndexes.forEach(function (index) {\n        swiper.$wrapperEl.append(renderSlide(slides[index], index));\n      });\n      prependIndexes.sort(function (a, b) { return b - a; }).forEach(function (index) {\n        swiper.$wrapperEl.prepend(renderSlide(slides[index], index));\n      });\n      swiper.$wrapperEl.children('.swiper-slide').css(offsetProp, (offset + \"px\"));\n      onRendered();\n    },\n    renderSlide: function renderSlide(slide, index) {\n      var swiper = this;\n      var params = swiper.params.virtual;\n      if (params.cache && swiper.virtual.cache[index]) {\n        return swiper.virtual.cache[index];\n      }\n      var $slideEl = params.renderSlide\n        ? $(params.renderSlide.call(swiper, slide, index))\n        : $((\"<div class=\\\"\" + (swiper.params.slideClass) + \"\\\" data-swiper-slide-index=\\\"\" + index + \"\\\">\" + slide + \"</div>\"));\n      if (!$slideEl.attr('data-swiper-slide-index')) { $slideEl.attr('data-swiper-slide-index', index); }\n      if (params.cache) { swiper.virtual.cache[index] = $slideEl; }\n      return $slideEl;\n    },\n    appendSlide: function appendSlide(slides) {\n      var swiper = this;\n      if (typeof slides === 'object' && 'length' in slides) {\n        for (var i = 0; i < slides.length; i += 1) {\n          if (slides[i]) { swiper.virtual.slides.push(slides[i]); }\n        }\n      } else {\n        swiper.virtual.slides.push(slides);\n      }\n      swiper.virtual.update(true);\n    },\n    prependSlide: function prependSlide(slides) {\n      var swiper = this;\n      var activeIndex = swiper.activeIndex;\n      var newActiveIndex = activeIndex + 1;\n      var numberOfNewSlides = 1;\n\n      if (Array.isArray(slides)) {\n        for (var i = 0; i < slides.length; i += 1) {\n          if (slides[i]) { swiper.virtual.slides.unshift(slides[i]); }\n        }\n        newActiveIndex = activeIndex + slides.length;\n        numberOfNewSlides = slides.length;\n      } else {\n        swiper.virtual.slides.unshift(slides);\n      }\n      if (swiper.params.virtual.cache) {\n        var cache = swiper.virtual.cache;\n        var newCache = {};\n        Object.keys(cache).forEach(function (cachedIndex) {\n          newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cache[cachedIndex];\n        });\n        swiper.virtual.cache = newCache;\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(newActiveIndex, 0);\n    },\n    removeSlide: function removeSlide(slidesIndexes) {\n      var swiper = this;\n      if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) { return; }\n      var activeIndex = swiper.activeIndex;\n      if (Array.isArray(slidesIndexes)) {\n        for (var i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n          swiper.virtual.slides.splice(slidesIndexes[i], 1);\n          if (swiper.params.virtual.cache) {\n            delete swiper.virtual.cache[slidesIndexes[i]];\n          }\n          if (slidesIndexes[i] < activeIndex) { activeIndex -= 1; }\n          activeIndex = Math.max(activeIndex, 0);\n        }\n      } else {\n        swiper.virtual.slides.splice(slidesIndexes, 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes];\n        }\n        if (slidesIndexes < activeIndex) { activeIndex -= 1; }\n        activeIndex = Math.max(activeIndex, 0);\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(activeIndex, 0);\n    },\n    removeAllSlides: function removeAllSlides() {\n      var swiper = this;\n      swiper.virtual.slides = [];\n      if (swiper.params.virtual.cache) {\n        swiper.virtual.cache = {};\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(0, 0);\n    },\n  };\n\n  var Virtual$1 = {\n    name: 'virtual',\n    params: {\n      virtual: {\n        enabled: false,\n        slides: [],\n        cache: true,\n        renderSlide: null,\n        renderExternal: null,\n        addSlidesBefore: 0,\n        addSlidesAfter: 0,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        virtual: {\n          update: Virtual.update.bind(swiper),\n          appendSlide: Virtual.appendSlide.bind(swiper),\n          prependSlide: Virtual.prependSlide.bind(swiper),\n          removeSlide: Virtual.removeSlide.bind(swiper),\n          removeAllSlides: Virtual.removeAllSlides.bind(swiper),\n          renderSlide: Virtual.renderSlide.bind(swiper),\n          slides: swiper.params.virtual.slides,\n          cache: {},\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"virtual\"));\n        var overwriteParams = {\n          watchSlidesProgress: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n\n        if (!swiper.params.initialSlide) {\n          swiper.virtual.update();\n        }\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.virtual.update();\n      },\n    },\n  };\n\n  var Keyboard = {\n    handle: function handle(event) {\n      var swiper = this;\n      var rtl = swiper.rtlTranslate;\n      var e = event;\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var kc = e.keyCode || e.charCode;\n      // Directions locks\n      if (!swiper.allowSlideNext && ((swiper.isHorizontal() && kc === 39) || (swiper.isVertical() && kc === 40) || kc === 34)) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && ((swiper.isHorizontal() && kc === 37) || (swiper.isVertical() && kc === 38) || kc === 33)) {\n        return false;\n      }\n      if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n        return undefined;\n      }\n      if (doc.activeElement && doc.activeElement.nodeName && (doc.activeElement.nodeName.toLowerCase() === 'input' || doc.activeElement.nodeName.toLowerCase() === 'textarea')) {\n        return undefined;\n      }\n      if (swiper.params.keyboard.onlyInViewport && (kc === 33 || kc === 34 || kc === 37 || kc === 39 || kc === 38 || kc === 40)) {\n        var inView = false;\n        // Check that swiper should be inside of visible area of window\n        if (swiper.$el.parents((\".\" + (swiper.params.slideClass))).length > 0 && swiper.$el.parents((\".\" + (swiper.params.slideActiveClass))).length === 0) {\n          return undefined;\n        }\n        var windowWidth = win.innerWidth;\n        var windowHeight = win.innerHeight;\n        var swiperOffset = swiper.$el.offset();\n        if (rtl) { swiperOffset.left -= swiper.$el[0].scrollLeft; }\n        var swiperCoord = [\n          [swiperOffset.left, swiperOffset.top],\n          [swiperOffset.left + swiper.width, swiperOffset.top],\n          [swiperOffset.left, swiperOffset.top + swiper.height],\n          [swiperOffset.left + swiper.width, swiperOffset.top + swiper.height] ];\n        for (var i = 0; i < swiperCoord.length; i += 1) {\n          var point = swiperCoord[i];\n          if (\n            point[0] >= 0 && point[0] <= windowWidth\n            && point[1] >= 0 && point[1] <= windowHeight\n          ) {\n            inView = true;\n          }\n        }\n        if (!inView) { return undefined; }\n      }\n      if (swiper.isHorizontal()) {\n        if (kc === 33 || kc === 34 || kc === 37 || kc === 39) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if (((kc === 34 || kc === 39) && !rtl) || ((kc === 33 || kc === 37) && rtl)) { swiper.slideNext(); }\n        if (((kc === 33 || kc === 37) && !rtl) || ((kc === 34 || kc === 39) && rtl)) { swiper.slidePrev(); }\n      } else {\n        if (kc === 33 || kc === 34 || kc === 38 || kc === 40) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if (kc === 34 || kc === 40) { swiper.slideNext(); }\n        if (kc === 33 || kc === 38) { swiper.slidePrev(); }\n      }\n      swiper.emit('keyPress', kc);\n      return undefined;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (swiper.keyboard.enabled) { return; }\n      $(doc).on('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!swiper.keyboard.enabled) { return; }\n      $(doc).off('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = false;\n    },\n  };\n\n  var Keyboard$1 = {\n    name: 'keyboard',\n    params: {\n      keyboard: {\n        enabled: false,\n        onlyInViewport: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        keyboard: {\n          enabled: false,\n          enable: Keyboard.enable.bind(swiper),\n          disable: Keyboard.disable.bind(swiper),\n          handle: Keyboard.handle.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.keyboard.enabled) {\n          swiper.keyboard.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.keyboard.enabled) {\n          swiper.keyboard.disable();\n        }\n      },\n    },\n  };\n\n  function isEventSupported() {\n    var eventName = 'onwheel';\n    var isSupported = eventName in doc;\n\n    if (!isSupported) {\n      var element = doc.createElement('div');\n      element.setAttribute(eventName, 'return;');\n      isSupported = typeof element[eventName] === 'function';\n    }\n\n    if (!isSupported\n      && doc.implementation\n      && doc.implementation.hasFeature\n      // always returns true in newer browsers as per the standard.\n      // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n      && doc.implementation.hasFeature('', '') !== true\n    ) {\n      // This is the only way to test support for the `wheel` event in IE9+.\n      isSupported = doc.implementation.hasFeature('Events.wheel', '3.0');\n    }\n\n    return isSupported;\n  }\n  var Mousewheel = {\n    lastScrollTime: Utils.now(),\n    event: (function getEvent() {\n      if (win.navigator.userAgent.indexOf('firefox') > -1) { return 'DOMMouseScroll'; }\n      return isEventSupported() ? 'wheel' : 'mousewheel';\n    }()),\n    normalize: function normalize(e) {\n      // Reasonable defaults\n      var PIXEL_STEP = 10;\n      var LINE_HEIGHT = 40;\n      var PAGE_HEIGHT = 800;\n\n      var sX = 0;\n      var sY = 0; // spinX, spinY\n      var pX = 0;\n      var pY = 0; // pixelX, pixelY\n\n      // Legacy\n      if ('detail' in e) {\n        sY = e.detail;\n      }\n      if ('wheelDelta' in e) {\n        sY = -e.wheelDelta / 120;\n      }\n      if ('wheelDeltaY' in e) {\n        sY = -e.wheelDeltaY / 120;\n      }\n      if ('wheelDeltaX' in e) {\n        sX = -e.wheelDeltaX / 120;\n      }\n\n      // side scrolling on FF with DOMMouseScroll\n      if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n        sX = sY;\n        sY = 0;\n      }\n\n      pX = sX * PIXEL_STEP;\n      pY = sY * PIXEL_STEP;\n\n      if ('deltaY' in e) {\n        pY = e.deltaY;\n      }\n      if ('deltaX' in e) {\n        pX = e.deltaX;\n      }\n\n      if ((pX || pY) && e.deltaMode) {\n        if (e.deltaMode === 1) { // delta in LINE units\n          pX *= LINE_HEIGHT;\n          pY *= LINE_HEIGHT;\n        } else { // delta in PAGE units\n          pX *= PAGE_HEIGHT;\n          pY *= PAGE_HEIGHT;\n        }\n      }\n\n      // Fall-back if spin cannot be determined\n      if (pX && !sX) {\n        sX = (pX < 1) ? -1 : 1;\n      }\n      if (pY && !sY) {\n        sY = (pY < 1) ? -1 : 1;\n      }\n\n      return {\n        spinX: sX,\n        spinY: sY,\n        pixelX: pX,\n        pixelY: pY,\n      };\n    },\n    handleMouseEnter: function handleMouseEnter() {\n      var swiper = this;\n      swiper.mouseEntered = true;\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      var swiper = this;\n      swiper.mouseEntered = false;\n    },\n    handle: function handle(event) {\n      var e = event;\n      var swiper = this;\n      var params = swiper.params.mousewheel;\n\n      if (!swiper.mouseEntered && !params.releaseOnEdges) { return true; }\n\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var delta = 0;\n      var rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n      var data = Mousewheel.normalize(e);\n\n      if (params.forceToAxis) {\n        if (swiper.isHorizontal()) {\n          if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) { delta = data.pixelX * rtlFactor; }\n          else { return true; }\n        } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) { delta = data.pixelY; }\n        else { return true; }\n      } else {\n        delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n      }\n\n      if (delta === 0) { return true; }\n\n      if (params.invert) { delta = -delta; }\n\n      if (!swiper.params.freeMode) {\n        if (Utils.now() - swiper.mousewheel.lastScrollTime > 60) {\n          if (delta < 0) {\n            if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n              swiper.slideNext();\n              swiper.emit('scroll', e);\n            } else if (params.releaseOnEdges) { return true; }\n          } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n            swiper.slidePrev();\n            swiper.emit('scroll', e);\n          } else if (params.releaseOnEdges) { return true; }\n        }\n        swiper.mousewheel.lastScrollTime = (new win.Date()).getTime();\n      } else {\n        // Freemode or scrollContainer:\n        if (swiper.params.loop) {\n          swiper.loopFix();\n        }\n        var position = swiper.getTranslate() + (delta * params.sensitivity);\n        var wasBeginning = swiper.isBeginning;\n        var wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) { position = swiper.minTranslate(); }\n        if (position <= swiper.maxTranslate()) { position = swiper.maxTranslate(); }\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n\n        if (swiper.params.freeModeSticky) {\n          clearTimeout(swiper.mousewheel.timeout);\n          swiper.mousewheel.timeout = Utils.nextTick(function () {\n            swiper.slideToClosest();\n          }, 300);\n        }\n        // Emit event\n        swiper.emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) { swiper.autoplay.stop(); }\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) { return true; }\n      }\n\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      return false;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.on('mouseenter', swiper.mousewheel.handleMouseEnter);\n      target.on('mouseleave', swiper.mousewheel.handleMouseLeave);\n      target.on(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = true;\n      return true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (!swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.off(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = false;\n      return true;\n    },\n  };\n\n  var Mousewheel$1 = {\n    name: 'mousewheel',\n    params: {\n      mousewheel: {\n        enabled: false,\n        releaseOnEdges: false,\n        invert: false,\n        forceToAxis: false,\n        sensitivity: 1,\n        eventsTarged: 'container',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        mousewheel: {\n          enabled: false,\n          enable: Mousewheel.enable.bind(swiper),\n          disable: Mousewheel.disable.bind(swiper),\n          handle: Mousewheel.handle.bind(swiper),\n          handleMouseEnter: Mousewheel.handleMouseEnter.bind(swiper),\n          handleMouseLeave: Mousewheel.handleMouseLeave.bind(swiper),\n          lastScrollTime: Utils.now(),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.mousewheel.enabled) { swiper.mousewheel.enable(); }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.mousewheel.enabled) { swiper.mousewheel.disable(); }\n      },\n    },\n  };\n\n  var Navigation = {\n    update: function update() {\n      // Update Navigation Buttons\n      var swiper = this;\n      var params = swiper.params.navigation;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          $prevEl.addClass(params.disabledClass);\n        } else {\n          $prevEl.removeClass(params.disabledClass);\n        }\n        $prevEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          $nextEl.addClass(params.disabledClass);\n        } else {\n          $nextEl.removeClass(params.disabledClass);\n        }\n        $nextEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n    },\n    onPrevClick: function onPrevClick(e) {\n      var swiper = this;\n      e.preventDefault();\n      if (swiper.isBeginning && !swiper.params.loop) { return; }\n      swiper.slidePrev();\n    },\n    onNextClick: function onNextClick(e) {\n      var swiper = this;\n      e.preventDefault();\n      if (swiper.isEnd && !swiper.params.loop) { return; }\n      swiper.slideNext();\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.navigation;\n      if (!(params.nextEl || params.prevEl)) { return; }\n\n      var $nextEl;\n      var $prevEl;\n      if (params.nextEl) {\n        $nextEl = $(params.nextEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.nextEl === 'string'\n          && $nextEl.length > 1\n          && swiper.$el.find(params.nextEl).length === 1\n        ) {\n          $nextEl = swiper.$el.find(params.nextEl);\n        }\n      }\n      if (params.prevEl) {\n        $prevEl = $(params.prevEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.prevEl === 'string'\n          && $prevEl.length > 1\n          && swiper.$el.find(params.prevEl).length === 1\n        ) {\n          $prevEl = swiper.$el.find(params.prevEl);\n        }\n      }\n\n      if ($nextEl && $nextEl.length > 0) {\n        $nextEl.on('click', swiper.navigation.onNextClick);\n      }\n      if ($prevEl && $prevEl.length > 0) {\n        $prevEl.on('click', swiper.navigation.onPrevClick);\n      }\n\n      Utils.extend(swiper.navigation, {\n        $nextEl: $nextEl,\n        nextEl: $nextEl && $nextEl[0],\n        $prevEl: $prevEl,\n        prevEl: $prevEl && $prevEl[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n      if ($nextEl && $nextEl.length) {\n        $nextEl.off('click', swiper.navigation.onNextClick);\n        $nextEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n      if ($prevEl && $prevEl.length) {\n        $prevEl.off('click', swiper.navigation.onPrevClick);\n        $prevEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n    },\n  };\n\n  var Navigation$1 = {\n    name: 'navigation',\n    params: {\n      navigation: {\n        nextEl: null,\n        prevEl: null,\n\n        hideOnClick: false,\n        disabledClass: 'swiper-button-disabled',\n        hiddenClass: 'swiper-button-hidden',\n        lockClass: 'swiper-button-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        navigation: {\n          init: Navigation.init.bind(swiper),\n          update: Navigation.update.bind(swiper),\n          destroy: Navigation.destroy.bind(swiper),\n          onNextClick: Navigation.onNextClick.bind(swiper),\n          onPrevClick: Navigation.onPrevClick.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.navigation.init();\n        swiper.navigation.update();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.navigation.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        var ref = swiper.navigation;\n        var $nextEl = ref.$nextEl;\n        var $prevEl = ref.$prevEl;\n        if (\n          swiper.params.navigation.hideOnClick\n          && !$(e.target).is($prevEl)\n          && !$(e.target).is($nextEl)\n        ) {\n          var isHidden;\n          if ($nextEl) {\n            isHidden = $nextEl.hasClass(swiper.params.navigation.hiddenClass);\n          } else if ($prevEl) {\n            isHidden = $prevEl.hasClass(swiper.params.navigation.hiddenClass);\n          }\n          if (isHidden === true) {\n            swiper.emit('navigationShow', swiper);\n          } else {\n            swiper.emit('navigationHide', swiper);\n          }\n          if ($nextEl) {\n            $nextEl.toggleClass(swiper.params.navigation.hiddenClass);\n          }\n          if ($prevEl) {\n            $prevEl.toggleClass(swiper.params.navigation.hiddenClass);\n          }\n        }\n      },\n    },\n  };\n\n  var Pagination = {\n    update: function update() {\n      // Render || Update Pagination bullets/items\n      var swiper = this;\n      var rtl = swiper.rtl;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n      var $el = swiper.pagination.$el;\n      // Current/Total\n      var current;\n      var total = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.loop) {\n        current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n        if (current > slidesLength - 1 - (swiper.loopedSlides * 2)) {\n          current -= (slidesLength - (swiper.loopedSlides * 2));\n        }\n        if (current > total - 1) { current -= total; }\n        if (current < 0 && swiper.params.paginationType !== 'bullets') { current = total + current; }\n      } else if (typeof swiper.snapIndex !== 'undefined') {\n        current = swiper.snapIndex;\n      } else {\n        current = swiper.activeIndex || 0;\n      }\n      // Types\n      if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n        var bullets = swiper.pagination.bullets;\n        var firstIndex;\n        var lastIndex;\n        var midIndex;\n        if (params.dynamicBullets) {\n          swiper.pagination.bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n          $el.css(swiper.isHorizontal() ? 'width' : 'height', ((swiper.pagination.bulletSize * (params.dynamicMainBullets + 4)) + \"px\"));\n          if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n            swiper.pagination.dynamicBulletIndex += (current - swiper.previousIndex);\n            if (swiper.pagination.dynamicBulletIndex > (params.dynamicMainBullets - 1)) {\n              swiper.pagination.dynamicBulletIndex = params.dynamicMainBullets - 1;\n            } else if (swiper.pagination.dynamicBulletIndex < 0) {\n              swiper.pagination.dynamicBulletIndex = 0;\n            }\n          }\n          firstIndex = current - swiper.pagination.dynamicBulletIndex;\n          lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n          midIndex = (lastIndex + firstIndex) / 2;\n        }\n        bullets.removeClass(((params.bulletActiveClass) + \" \" + (params.bulletActiveClass) + \"-next \" + (params.bulletActiveClass) + \"-next-next \" + (params.bulletActiveClass) + \"-prev \" + (params.bulletActiveClass) + \"-prev-prev \" + (params.bulletActiveClass) + \"-main\"));\n        if ($el.length > 1) {\n          bullets.each(function (index, bullet) {\n            var $bullet = $(bullet);\n            var bulletIndex = $bullet.index();\n            if (bulletIndex === current) {\n              $bullet.addClass(params.bulletActiveClass);\n            }\n            if (params.dynamicBullets) {\n              if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n                $bullet.addClass(((params.bulletActiveClass) + \"-main\"));\n              }\n              if (bulletIndex === firstIndex) {\n                $bullet\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev\"))\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n              }\n              if (bulletIndex === lastIndex) {\n                $bullet\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next\"))\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next-next\"));\n              }\n            }\n          });\n        } else {\n          var $bullet = bullets.eq(current);\n          $bullet.addClass(params.bulletActiveClass);\n          if (params.dynamicBullets) {\n            var $firstDisplayedBullet = bullets.eq(firstIndex);\n            var $lastDisplayedBullet = bullets.eq(lastIndex);\n            for (var i = firstIndex; i <= lastIndex; i += 1) {\n              bullets.eq(i).addClass(((params.bulletActiveClass) + \"-main\"));\n            }\n            $firstDisplayedBullet\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev\"))\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n            $lastDisplayedBullet\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next\"))\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next-next\"));\n          }\n        }\n        if (params.dynamicBullets) {\n          var dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n          var bulletsOffset = (((swiper.pagination.bulletSize * dynamicBulletsLength) - (swiper.pagination.bulletSize)) / 2) - (midIndex * swiper.pagination.bulletSize);\n          var offsetProp = rtl ? 'right' : 'left';\n          bullets.css(swiper.isHorizontal() ? offsetProp : 'top', (bulletsOffset + \"px\"));\n        }\n      }\n      if (params.type === 'fraction') {\n        $el.find((\".\" + (params.currentClass))).text(params.formatFractionCurrent(current + 1));\n        $el.find((\".\" + (params.totalClass))).text(params.formatFractionTotal(total));\n      }\n      if (params.type === 'progressbar') {\n        var progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        var scale = (current + 1) / total;\n        var scaleX = 1;\n        var scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        $el.find((\".\" + (params.progressbarFillClass))).transform((\"translate3d(0,0,0) scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\")).transition(swiper.params.speed);\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        $el.html(params.renderCustom(swiper, current + 1, total));\n        swiper.emit('paginationRender', swiper, $el[0]);\n      } else {\n        swiper.emit('paginationUpdate', swiper, $el[0]);\n      }\n      $el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    },\n    render: function render() {\n      // Render Container\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n\n      var $el = swiper.pagination.$el;\n      var paginationHTML = '';\n      if (params.type === 'bullets') {\n        var numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n        for (var i = 0; i < numberOfBullets; i += 1) {\n          if (params.renderBullet) {\n            paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n          } else {\n            paginationHTML += \"<\" + (params.bulletElement) + \" class=\\\"\" + (params.bulletClass) + \"\\\"></\" + (params.bulletElement) + \">\";\n          }\n        }\n        $el.html(paginationHTML);\n        swiper.pagination.bullets = $el.find((\".\" + (params.bulletClass)));\n      }\n      if (params.type === 'fraction') {\n        if (params.renderFraction) {\n          paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.currentClass) + \"\\\"></span>\"\n          + ' / '\n          + \"<span class=\\\"\" + (params.totalClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type === 'progressbar') {\n        if (params.renderProgressbar) {\n          paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.progressbarFillClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type !== 'custom') {\n        swiper.emit('paginationRender', swiper.pagination.$el[0]);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el) { return; }\n\n      var $el = $(params.el);\n      if ($el.length === 0) { return; }\n\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.el === 'string'\n        && $el.length > 1\n        && swiper.$el.find(params.el).length === 1\n      ) {\n        $el = swiper.$el.find(params.el);\n      }\n\n      if (params.type === 'bullets' && params.clickable) {\n        $el.addClass(params.clickableClass);\n      }\n\n      $el.addClass(params.modifierClass + params.type);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        $el.addClass((\"\" + (params.modifierClass) + (params.type) + \"-dynamic\"));\n        swiper.pagination.dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        $el.addClass(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        $el.on('click', (\".\" + (params.bulletClass)), function onClick(e) {\n          e.preventDefault();\n          var index = $(this).index() * swiper.params.slidesPerGroup;\n          if (swiper.params.loop) { index += swiper.loopedSlides; }\n          swiper.slideTo(index);\n        });\n      }\n\n      Utils.extend(swiper.pagination, {\n        $el: $el,\n        el: $el[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var $el = swiper.pagination.$el;\n\n      $el.removeClass(params.hiddenClass);\n      $el.removeClass(params.modifierClass + params.type);\n      if (swiper.pagination.bullets) { swiper.pagination.bullets.removeClass(params.bulletActiveClass); }\n      if (params.clickable) {\n        $el.off('click', (\".\" + (params.bulletClass)));\n      }\n    },\n  };\n\n  var Pagination$1 = {\n    name: 'pagination',\n    params: {\n      pagination: {\n        el: null,\n        bulletElement: 'span',\n        clickable: false,\n        hideOnClick: false,\n        renderBullet: null,\n        renderProgressbar: null,\n        renderFraction: null,\n        renderCustom: null,\n        progressbarOpposite: false,\n        type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n        dynamicBullets: false,\n        dynamicMainBullets: 1,\n        formatFractionCurrent: function (number) { return number; },\n        formatFractionTotal: function (number) { return number; },\n        bulletClass: 'swiper-pagination-bullet',\n        bulletActiveClass: 'swiper-pagination-bullet-active',\n        modifierClass: 'swiper-pagination-', // NEW\n        currentClass: 'swiper-pagination-current',\n        totalClass: 'swiper-pagination-total',\n        hiddenClass: 'swiper-pagination-hidden',\n        progressbarFillClass: 'swiper-pagination-progressbar-fill',\n        progressbarOppositeClass: 'swiper-pagination-progressbar-opposite',\n        clickableClass: 'swiper-pagination-clickable', // NEW\n        lockClass: 'swiper-pagination-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        pagination: {\n          init: Pagination.init.bind(swiper),\n          render: Pagination.render.bind(swiper),\n          update: Pagination.update.bind(swiper),\n          destroy: Pagination.destroy.bind(swiper),\n          dynamicBulletIndex: 0,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.pagination.init();\n        swiper.pagination.render();\n        swiper.pagination.update();\n      },\n      activeIndexChange: function activeIndexChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.update();\n        } else if (typeof swiper.snapIndex === 'undefined') {\n          swiper.pagination.update();\n        }\n      },\n      snapIndexChange: function snapIndexChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.update();\n        }\n      },\n      slidesLengthChange: function slidesLengthChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      snapGridLengthChange: function snapGridLengthChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.pagination.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        if (\n          swiper.params.pagination.el\n          && swiper.params.pagination.hideOnClick\n          && swiper.pagination.$el.length > 0\n          && !$(e.target).hasClass(swiper.params.pagination.bulletClass)\n        ) {\n          var isHidden = swiper.pagination.$el.hasClass(swiper.params.pagination.hiddenClass);\n          if (isHidden === true) {\n            swiper.emit('paginationShow', swiper);\n          } else {\n            swiper.emit('paginationHide', swiper);\n          }\n          swiper.pagination.$el.toggleClass(swiper.params.pagination.hiddenClass);\n        }\n      },\n    },\n  };\n\n  var Scrollbar = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var progress = swiper.progress;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n      var params = swiper.params.scrollbar;\n\n      var newSize = dragSize;\n      var newPos = (trackSize - dragSize) * progress;\n      if (rtl) {\n        newPos = -newPos;\n        if (newPos > 0) {\n          newSize = dragSize - newPos;\n          newPos = 0;\n        } else if (-newPos + dragSize > trackSize) {\n          newSize = trackSize + newPos;\n        }\n      } else if (newPos < 0) {\n        newSize = dragSize + newPos;\n        newPos = 0;\n      } else if (newPos + dragSize > trackSize) {\n        newSize = trackSize - newPos;\n      }\n      if (swiper.isHorizontal()) {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(\" + newPos + \"px, 0, 0)\"));\n        } else {\n          $dragEl.transform((\"translateX(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.width = newSize + \"px\";\n      } else {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(0px, \" + newPos + \"px, 0)\"));\n        } else {\n          $dragEl.transform((\"translateY(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.height = newSize + \"px\";\n      }\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.timeout);\n        $el[0].style.opacity = 1;\n        swiper.scrollbar.timeout = setTimeout(function () {\n          $el[0].style.opacity = 0;\n          $el.transition(400);\n        }, 1000);\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      swiper.scrollbar.$dragEl.transition(duration);\n    },\n    updateSize: function updateSize() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n\n      var scrollbar = swiper.scrollbar;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n\n      $dragEl[0].style.width = '';\n      $dragEl[0].style.height = '';\n      var trackSize = swiper.isHorizontal() ? $el[0].offsetWidth : $el[0].offsetHeight;\n\n      var divider = swiper.size / swiper.virtualSize;\n      var moveDivider = divider * (trackSize / swiper.size);\n      var dragSize;\n      if (swiper.params.scrollbar.dragSize === 'auto') {\n        dragSize = trackSize * divider;\n      } else {\n        dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n      }\n\n      if (swiper.isHorizontal()) {\n        $dragEl[0].style.width = dragSize + \"px\";\n      } else {\n        $dragEl[0].style.height = dragSize + \"px\";\n      }\n\n      if (divider >= 1) {\n        $el[0].style.display = 'none';\n      } else {\n        $el[0].style.display = '';\n      }\n      if (swiper.params.scrollbar.hide) {\n        $el[0].style.opacity = 0;\n      }\n      Utils.extend(scrollbar, {\n        trackSize: trackSize,\n        divider: divider,\n        moveDivider: moveDivider,\n        dragSize: dragSize,\n      });\n      scrollbar.$el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](swiper.params.scrollbar.lockClass);\n    },\n    getPointerPosition: function getPointerPosition(e) {\n      var swiper = this;\n      if (swiper.isHorizontal()) {\n        return ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX);\n      }\n      return ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY);\n    },\n    setDragPosition: function setDragPosition(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var $el = scrollbar.$el;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n      var dragStartPos = scrollbar.dragStartPos;\n\n      var positionRatio;\n      positionRatio = ((scrollbar.getPointerPosition(e)) - $el.offset()[swiper.isHorizontal() ? 'left' : 'top']\n        - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n      positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n      if (rtl) {\n        positionRatio = 1 - positionRatio;\n      }\n\n      var position = swiper.minTranslate() + ((swiper.maxTranslate() - swiper.minTranslate()) * positionRatio);\n\n      swiper.updateProgress(position);\n      swiper.setTranslate(position);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    },\n    onDragStart: function onDragStart(e) {\n      var swiper = this;\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n      swiper.scrollbar.isTouched = true;\n      swiper.scrollbar.dragStartPos = (e.target === $dragEl[0] || e.target === $dragEl)\n        ? scrollbar.getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n      e.preventDefault();\n      e.stopPropagation();\n\n      $wrapperEl.transition(100);\n      $dragEl.transition(100);\n      scrollbar.setDragPosition(e);\n\n      clearTimeout(swiper.scrollbar.dragTimeout);\n\n      $el.transition(0);\n      if (params.hide) {\n        $el.css('opacity', 1);\n      }\n      swiper.emit('scrollbarDragStart', e);\n    },\n    onDragMove: function onDragMove(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      scrollbar.setDragPosition(e);\n      $wrapperEl.transition(0);\n      $el.transition(0);\n      $dragEl.transition(0);\n      swiper.emit('scrollbarDragMove', e);\n    },\n    onDragEnd: function onDragEnd(e) {\n      var swiper = this;\n\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $el = scrollbar.$el;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      swiper.scrollbar.isTouched = false;\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.dragTimeout);\n        swiper.scrollbar.dragTimeout = Utils.nextTick(function () {\n          $el.css('opacity', 0);\n          $el.transition(400);\n        }, 1000);\n      }\n      swiper.emit('scrollbarDragEnd', e);\n      if (params.snapOnRelease) {\n        swiper.slideToClosest();\n      }\n    },\n    enableDraggable: function enableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEventsTouch = swiper.touchEventsTouch;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch) {\n        target.addEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.addEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.addEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        target.addEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n        target.addEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n        target.addEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n      }\n    },\n    disableDraggable: function disableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEventsTouch = swiper.touchEventsTouch;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch) {\n        target.removeEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.removeEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.removeEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        target.removeEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n        target.removeEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n        target.removeEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var $swiperEl = swiper.$el;\n      var params = swiper.params.scrollbar;\n\n      var $el = $(params.el);\n      if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1 && $swiperEl.find(params.el).length === 1) {\n        $el = $swiperEl.find(params.el);\n      }\n\n      var $dragEl = $el.find((\".\" + (swiper.params.scrollbar.dragClass)));\n      if ($dragEl.length === 0) {\n        $dragEl = $((\"<div class=\\\"\" + (swiper.params.scrollbar.dragClass) + \"\\\"></div>\"));\n        $el.append($dragEl);\n      }\n\n      Utils.extend(scrollbar, {\n        $el: $el,\n        el: $el[0],\n        $dragEl: $dragEl,\n        dragEl: $dragEl[0],\n      });\n\n      if (params.draggable) {\n        scrollbar.enableDraggable();\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.scrollbar.disableDraggable();\n    },\n  };\n\n  var Scrollbar$1 = {\n    name: 'scrollbar',\n    params: {\n      scrollbar: {\n        el: null,\n        dragSize: 'auto',\n        hide: false,\n        draggable: false,\n        snapOnRelease: true,\n        lockClass: 'swiper-scrollbar-lock',\n        dragClass: 'swiper-scrollbar-drag',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        scrollbar: {\n          init: Scrollbar.init.bind(swiper),\n          destroy: Scrollbar.destroy.bind(swiper),\n          updateSize: Scrollbar.updateSize.bind(swiper),\n          setTranslate: Scrollbar.setTranslate.bind(swiper),\n          setTransition: Scrollbar.setTransition.bind(swiper),\n          enableDraggable: Scrollbar.enableDraggable.bind(swiper),\n          disableDraggable: Scrollbar.disableDraggable.bind(swiper),\n          setDragPosition: Scrollbar.setDragPosition.bind(swiper),\n          getPointerPosition: Scrollbar.getPointerPosition.bind(swiper),\n          onDragStart: Scrollbar.onDragStart.bind(swiper),\n          onDragMove: Scrollbar.onDragMove.bind(swiper),\n          onDragEnd: Scrollbar.onDragEnd.bind(swiper),\n          isTouched: false,\n          timeout: null,\n          dragTimeout: null,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.scrollbar.init();\n        swiper.scrollbar.updateSize();\n        swiper.scrollbar.setTranslate();\n      },\n      update: function update() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      resize: function resize() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        swiper.scrollbar.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        swiper.scrollbar.setTransition(duration);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.scrollbar.destroy();\n      },\n    },\n  };\n\n  var Parallax = {\n    setTransform: function setTransform(el, progress) {\n      var swiper = this;\n      var rtl = swiper.rtl;\n\n      var $el = $(el);\n      var rtlFactor = rtl ? -1 : 1;\n\n      var p = $el.attr('data-swiper-parallax') || '0';\n      var x = $el.attr('data-swiper-parallax-x');\n      var y = $el.attr('data-swiper-parallax-y');\n      var scale = $el.attr('data-swiper-parallax-scale');\n      var opacity = $el.attr('data-swiper-parallax-opacity');\n\n      if (x || y) {\n        x = x || '0';\n        y = y || '0';\n      } else if (swiper.isHorizontal()) {\n        x = p;\n        y = '0';\n      } else {\n        y = p;\n        x = '0';\n      }\n\n      if ((x).indexOf('%') >= 0) {\n        x = (parseInt(x, 10) * progress * rtlFactor) + \"%\";\n      } else {\n        x = (x * progress * rtlFactor) + \"px\";\n      }\n      if ((y).indexOf('%') >= 0) {\n        y = (parseInt(y, 10) * progress) + \"%\";\n      } else {\n        y = (y * progress) + \"px\";\n      }\n\n      if (typeof opacity !== 'undefined' && opacity !== null) {\n        var currentOpacity = opacity - ((opacity - 1) * (1 - Math.abs(progress)));\n        $el[0].style.opacity = currentOpacity;\n      }\n      if (typeof scale === 'undefined' || scale === null) {\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px)\"));\n      } else {\n        var currentScale = scale - ((scale - 1) * (1 - Math.abs(progress)));\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px) scale(\" + currentScale + \")\"));\n      }\n    },\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      var progress = swiper.progress;\n      var snapGrid = swiper.snapGrid;\n      $el.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n        .each(function (index, el) {\n          swiper.parallax.setTransform(el, progress);\n        });\n      slides.each(function (slideIndex, slideEl) {\n        var slideProgress = slideEl.progress;\n        if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n          slideProgress += Math.ceil(slideIndex / 2) - (progress * (snapGrid.length - 1));\n        }\n        slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n        $(slideEl).find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n          .each(function (index, el) {\n            swiper.parallax.setTransform(el, slideProgress);\n          });\n      });\n    },\n    setTransition: function setTransition(duration) {\n      if ( duration === void 0 ) duration = this.params.speed;\n\n      var swiper = this;\n      var $el = swiper.$el;\n      $el.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n        .each(function (index, parallaxEl) {\n          var $parallaxEl = $(parallaxEl);\n          var parallaxDuration = parseInt($parallaxEl.attr('data-swiper-parallax-duration'), 10) || duration;\n          if (duration === 0) { parallaxDuration = 0; }\n          $parallaxEl.transition(parallaxDuration);\n        });\n    },\n  };\n\n  var Parallax$1 = {\n    name: 'parallax',\n    params: {\n      parallax: {\n        enabled: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        parallax: {\n          setTransform: Parallax.setTransform.bind(swiper),\n          setTranslate: Parallax.setTranslate.bind(swiper),\n          setTransition: Parallax.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTransition(duration);\n      },\n    },\n  };\n\n  var Zoom = {\n    // Calc Scale From Multi-touches\n    getDistanceBetweenTouches: function getDistanceBetweenTouches(e) {\n      if (e.targetTouches.length < 2) { return 1; }\n      var x1 = e.targetTouches[0].pageX;\n      var y1 = e.targetTouches[0].pageY;\n      var x2 = e.targetTouches[1].pageX;\n      var y2 = e.targetTouches[1].pageY;\n      var distance = Math.sqrt((Math.pow( (x2 - x1), 2 )) + (Math.pow( (y2 - y1), 2 )));\n      return distance;\n    },\n    // Events\n    onGestureStart: function onGestureStart(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      zoom.fakeGestureTouched = false;\n      zoom.fakeGestureMoved = false;\n      if (!Support.gestures) {\n        if (e.type !== 'touchstart' || (e.type === 'touchstart' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureTouched = true;\n        gesture.scaleStart = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$slideEl || !gesture.$slideEl.length) {\n        gesture.$slideEl = $(e.target).closest('.swiper-slide');\n        if (gesture.$slideEl.length === 0) { gesture.$slideEl = swiper.slides.eq(swiper.activeIndex); }\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n        gesture.maxRatio = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n        if (gesture.$imageWrapEl.length === 0) {\n          gesture.$imageEl = undefined;\n          return;\n        }\n      }\n      gesture.$imageEl.transition(0);\n      swiper.zoom.isScaling = true;\n    },\n    onGestureChange: function onGestureChange(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (e.type !== 'touchmove' || (e.type === 'touchmove' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureMoved = true;\n        gesture.scaleMove = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (Support.gestures) {\n        zoom.scale = e.scale * zoom.currentScale;\n      } else {\n        zoom.scale = (gesture.scaleMove / gesture.scaleStart) * zoom.currentScale;\n      }\n      if (zoom.scale > gesture.maxRatio) {\n        zoom.scale = (gesture.maxRatio - 1) + (Math.pow( ((zoom.scale - gesture.maxRatio) + 1), 0.5 ));\n      }\n      if (zoom.scale < params.minRatio) {\n        zoom.scale = (params.minRatio + 1) - (Math.pow( ((params.minRatio - zoom.scale) + 1), 0.5 ));\n      }\n      gesture.$imageEl.transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    onGestureEnd: function onGestureEnd(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (!zoom.fakeGestureTouched || !zoom.fakeGestureMoved) {\n          return;\n        }\n        if (e.type !== 'touchend' || (e.type === 'touchend' && e.changedTouches.length < 2 && !Device.android)) {\n          return;\n        }\n        zoom.fakeGestureTouched = false;\n        zoom.fakeGestureMoved = false;\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n      gesture.$imageEl.transition(swiper.params.speed).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n      zoom.currentScale = zoom.scale;\n      zoom.isScaling = false;\n      if (zoom.scale === 1) { gesture.$slideEl = undefined; }\n    },\n    onTouchStart: function onTouchStart(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (image.isTouched) { return; }\n      if (Device.android) { e.preventDefault(); }\n      image.isTouched = true;\n      image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    },\n    onTouchMove: function onTouchMove(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      swiper.allowClick = false;\n      if (!image.isTouched || !gesture.$slideEl) { return; }\n\n      if (!image.isMoved) {\n        image.width = gesture.$imageEl[0].offsetWidth;\n        image.height = gesture.$imageEl[0].offsetHeight;\n        image.startX = Utils.getTranslate(gesture.$imageWrapEl[0], 'x') || 0;\n        image.startY = Utils.getTranslate(gesture.$imageWrapEl[0], 'y') || 0;\n        gesture.slideWidth = gesture.$slideEl[0].offsetWidth;\n        gesture.slideHeight = gesture.$slideEl[0].offsetHeight;\n        gesture.$imageWrapEl.transition(0);\n        if (swiper.rtl) {\n          image.startX = -image.startX;\n          image.startY = -image.startY;\n        }\n      }\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n\n      if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) { return; }\n\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n\n      image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n\n      if (!image.isMoved && !zoom.isScaling) {\n        if (\n          swiper.isHorizontal()\n          && (\n            (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x)\n            || (Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        } if (\n          !swiper.isHorizontal()\n          && (\n            (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y)\n            || (Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        }\n      }\n      e.preventDefault();\n      e.stopPropagation();\n\n      image.isMoved = true;\n      image.currentX = (image.touchesCurrent.x - image.touchesStart.x) + image.startX;\n      image.currentY = (image.touchesCurrent.y - image.touchesStart.y) + image.startY;\n\n      if (image.currentX < image.minX) {\n        image.currentX = (image.minX + 1) - (Math.pow( ((image.minX - image.currentX) + 1), 0.8 ));\n      }\n      if (image.currentX > image.maxX) {\n        image.currentX = (image.maxX - 1) + (Math.pow( ((image.currentX - image.maxX) + 1), 0.8 ));\n      }\n\n      if (image.currentY < image.minY) {\n        image.currentY = (image.minY + 1) - (Math.pow( ((image.minY - image.currentY) + 1), 0.8 ));\n      }\n      if (image.currentY > image.maxY) {\n        image.currentY = (image.maxY - 1) + (Math.pow( ((image.currentY - image.maxY) + 1), 0.8 ));\n      }\n\n      // Velocity\n      if (!velocity.prevPositionX) { velocity.prevPositionX = image.touchesCurrent.x; }\n      if (!velocity.prevPositionY) { velocity.prevPositionY = image.touchesCurrent.y; }\n      if (!velocity.prevTime) { velocity.prevTime = Date.now(); }\n      velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n      velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n      if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) { velocity.x = 0; }\n      if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) { velocity.y = 0; }\n      velocity.prevPositionX = image.touchesCurrent.x;\n      velocity.prevPositionY = image.touchesCurrent.y;\n      velocity.prevTime = Date.now();\n\n      gesture.$imageWrapEl.transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTouchEnd: function onTouchEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (!image.isTouched || !image.isMoved) {\n        image.isTouched = false;\n        image.isMoved = false;\n        return;\n      }\n      image.isTouched = false;\n      image.isMoved = false;\n      var momentumDurationX = 300;\n      var momentumDurationY = 300;\n      var momentumDistanceX = velocity.x * momentumDurationX;\n      var newPositionX = image.currentX + momentumDistanceX;\n      var momentumDistanceY = velocity.y * momentumDurationY;\n      var newPositionY = image.currentY + momentumDistanceY;\n\n      // Fix duration\n      if (velocity.x !== 0) { momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x); }\n      if (velocity.y !== 0) { momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y); }\n      var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n      image.currentX = newPositionX;\n      image.currentY = newPositionY;\n\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n      image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n      image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n      gesture.$imageWrapEl.transition(momentumDuration).transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTransitionEnd: function onTransitionEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (gesture.$slideEl && swiper.previousIndex !== swiper.activeIndex) {\n        gesture.$imageEl.transform('translate3d(0,0,0) scale(1)');\n        gesture.$imageWrapEl.transform('translate3d(0,0,0)');\n\n        zoom.scale = 1;\n        zoom.currentScale = 1;\n\n        gesture.$slideEl = undefined;\n        gesture.$imageEl = undefined;\n        gesture.$imageWrapEl = undefined;\n      }\n    },\n    // Toggle Zoom\n    toggle: function toggle(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n\n      if (zoom.scale && zoom.scale !== 1) {\n        // Zoom Out\n        zoom.out();\n      } else {\n        // Zoom In\n        zoom.in(e);\n      }\n    },\n    in: function in$1(e) {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      gesture.$slideEl.addClass((\"\" + (params.zoomedSlideClass)));\n\n      var touchX;\n      var touchY;\n      var offsetX;\n      var offsetY;\n      var diffX;\n      var diffY;\n      var translateX;\n      var translateY;\n      var imageWidth;\n      var imageHeight;\n      var scaledWidth;\n      var scaledHeight;\n      var translateMinX;\n      var translateMinY;\n      var translateMaxX;\n      var translateMaxY;\n      var slideWidth;\n      var slideHeight;\n\n      if (typeof image.touchesStart.x === 'undefined' && e) {\n        touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n        touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n      } else {\n        touchX = image.touchesStart.x;\n        touchY = image.touchesStart.y;\n      }\n\n      zoom.scale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      zoom.currentScale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      if (e) {\n        slideWidth = gesture.$slideEl[0].offsetWidth;\n        slideHeight = gesture.$slideEl[0].offsetHeight;\n        offsetX = gesture.$slideEl.offset().left;\n        offsetY = gesture.$slideEl.offset().top;\n        diffX = (offsetX + (slideWidth / 2)) - touchX;\n        diffY = (offsetY + (slideHeight / 2)) - touchY;\n\n        imageWidth = gesture.$imageEl[0].offsetWidth;\n        imageHeight = gesture.$imageEl[0].offsetHeight;\n        scaledWidth = imageWidth * zoom.scale;\n        scaledHeight = imageHeight * zoom.scale;\n\n        translateMinX = Math.min(((slideWidth / 2) - (scaledWidth / 2)), 0);\n        translateMinY = Math.min(((slideHeight / 2) - (scaledHeight / 2)), 0);\n        translateMaxX = -translateMinX;\n        translateMaxY = -translateMinY;\n\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n\n        if (translateX < translateMinX) {\n          translateX = translateMinX;\n        }\n        if (translateX > translateMaxX) {\n          translateX = translateMaxX;\n        }\n\n        if (translateY < translateMinY) {\n          translateY = translateMinY;\n        }\n        if (translateY > translateMaxY) {\n          translateY = translateMaxY;\n        }\n      } else {\n        translateX = 0;\n        translateY = 0;\n      }\n      gesture.$imageWrapEl.transition(300).transform((\"translate3d(\" + translateX + \"px, \" + translateY + \"px,0)\"));\n      gesture.$imageEl.transition(300).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    out: function out() {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      zoom.scale = 1;\n      zoom.currentScale = 1;\n      gesture.$imageWrapEl.transition(300).transform('translate3d(0,0,0)');\n      gesture.$imageEl.transition(300).transform('translate3d(0,0,0) scale(1)');\n      gesture.$slideEl.removeClass((\"\" + (params.zoomedSlideClass)));\n      gesture.$slideEl = undefined;\n    },\n    // Attach/Detach Events\n    enable: function enable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (zoom.enabled) { return; }\n      zoom.enabled = true;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.on('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.on(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.on(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n    disable: function disable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (!zoom.enabled) { return; }\n\n      swiper.zoom.enabled = false;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.off('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.off(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.off(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n  };\n\n  var Zoom$1 = {\n    name: 'zoom',\n    params: {\n      zoom: {\n        enabled: false,\n        maxRatio: 3,\n        minRatio: 1,\n        toggle: true,\n        containerClass: 'swiper-zoom-container',\n        zoomedSlideClass: 'swiper-slide-zoomed',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      var zoom = {\n        enabled: false,\n        scale: 1,\n        currentScale: 1,\n        isScaling: false,\n        gesture: {\n          $slideEl: undefined,\n          slideWidth: undefined,\n          slideHeight: undefined,\n          $imageEl: undefined,\n          $imageWrapEl: undefined,\n          maxRatio: 3,\n        },\n        image: {\n          isTouched: undefined,\n          isMoved: undefined,\n          currentX: undefined,\n          currentY: undefined,\n          minX: undefined,\n          minY: undefined,\n          maxX: undefined,\n          maxY: undefined,\n          width: undefined,\n          height: undefined,\n          startX: undefined,\n          startY: undefined,\n          touchesStart: {},\n          touchesCurrent: {},\n        },\n        velocity: {\n          x: undefined,\n          y: undefined,\n          prevPositionX: undefined,\n          prevPositionY: undefined,\n          prevTime: undefined,\n        },\n      };\n\n      ('onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out').split(' ').forEach(function (methodName) {\n        zoom[methodName] = Zoom[methodName].bind(swiper);\n      });\n      Utils.extend(swiper, {\n        zoom: zoom,\n      });\n\n      var scale = 1;\n      Object.defineProperty(swiper.zoom, 'scale', {\n        get: function get() {\n          return scale;\n        },\n        set: function set(value) {\n          if (scale !== value) {\n            var imageEl = swiper.zoom.gesture.$imageEl ? swiper.zoom.gesture.$imageEl[0] : undefined;\n            var slideEl = swiper.zoom.gesture.$slideEl ? swiper.zoom.gesture.$slideEl[0] : undefined;\n            swiper.emit('zoomChange', value, imageEl, slideEl);\n          }\n          scale = value;\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.zoom.enabled) {\n          swiper.zoom.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.zoom.disable();\n      },\n      touchStart: function touchStart(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchStart(e);\n      },\n      touchEnd: function touchEnd(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchEnd(e);\n      },\n      doubleTap: function doubleTap(e) {\n        var swiper = this;\n        if (swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n          swiper.zoom.toggle(e);\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n          swiper.zoom.onTransitionEnd();\n        }\n      },\n    },\n  };\n\n  var Lazy = {\n    loadInSlide: function loadInSlide(index, loadInDuplicate) {\n      if ( loadInDuplicate === void 0 ) loadInDuplicate = true;\n\n      var swiper = this;\n      var params = swiper.params.lazy;\n      if (typeof index === 'undefined') { return; }\n      if (swiper.slides.length === 0) { return; }\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n      var $slideEl = isVirtual\n        ? swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\"))\n        : swiper.slides.eq(index);\n\n      var $images = $slideEl.find((\".\" + (params.elementClass) + \":not(.\" + (params.loadedClass) + \"):not(.\" + (params.loadingClass) + \")\"));\n      if ($slideEl.hasClass(params.elementClass) && !$slideEl.hasClass(params.loadedClass) && !$slideEl.hasClass(params.loadingClass)) {\n        $images = $images.add($slideEl[0]);\n      }\n      if ($images.length === 0) { return; }\n\n      $images.each(function (imageIndex, imageEl) {\n        var $imageEl = $(imageEl);\n        $imageEl.addClass(params.loadingClass);\n\n        var background = $imageEl.attr('data-background');\n        var src = $imageEl.attr('data-src');\n        var srcset = $imageEl.attr('data-srcset');\n        var sizes = $imageEl.attr('data-sizes');\n\n        swiper.loadImage($imageEl[0], (src || background), srcset, sizes, false, function () {\n          if (typeof swiper === 'undefined' || swiper === null || !swiper || (swiper && !swiper.params) || swiper.destroyed) { return; }\n          if (background) {\n            $imageEl.css('background-image', (\"url(\\\"\" + background + \"\\\")\"));\n            $imageEl.removeAttr('data-background');\n          } else {\n            if (srcset) {\n              $imageEl.attr('srcset', srcset);\n              $imageEl.removeAttr('data-srcset');\n            }\n            if (sizes) {\n              $imageEl.attr('sizes', sizes);\n              $imageEl.removeAttr('data-sizes');\n            }\n            if (src) {\n              $imageEl.attr('src', src);\n              $imageEl.removeAttr('data-src');\n            }\n          }\n\n          $imageEl.addClass(params.loadedClass).removeClass(params.loadingClass);\n          $slideEl.find((\".\" + (params.preloaderClass))).remove();\n          if (swiper.params.loop && loadInDuplicate) {\n            var slideOriginalIndex = $slideEl.attr('data-swiper-slide-index');\n            if ($slideEl.hasClass(swiper.params.slideDuplicateClass)) {\n              var originalSlide = swiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]:not(.\" + (swiper.params.slideDuplicateClass) + \")\"));\n              swiper.lazy.loadInSlide(originalSlide.index(), false);\n            } else {\n              var duplicatedSlide = swiper.$wrapperEl.children((\".\" + (swiper.params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]\"));\n              swiper.lazy.loadInSlide(duplicatedSlide.index(), false);\n            }\n          }\n          swiper.emit('lazyImageReady', $slideEl[0], $imageEl[0]);\n        });\n\n        swiper.emit('lazyImageLoad', $slideEl[0], $imageEl[0]);\n      });\n    },\n    load: function load() {\n      var swiper = this;\n      var $wrapperEl = swiper.$wrapperEl;\n      var swiperParams = swiper.params;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var isVirtual = swiper.virtual && swiperParams.virtual.enabled;\n      var params = swiperParams.lazy;\n\n      var slidesPerView = swiperParams.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = 0;\n      }\n\n      function slideExist(index) {\n        if (isVirtual) {\n          if ($wrapperEl.children((\".\" + (swiperParams.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\")).length) {\n            return true;\n          }\n        } else if (slides[index]) { return true; }\n        return false;\n      }\n      function slideIndex(slideEl) {\n        if (isVirtual) {\n          return $(slideEl).attr('data-swiper-slide-index');\n        }\n        return $(slideEl).index();\n      }\n\n      if (!swiper.lazy.initialImageLoaded) { swiper.lazy.initialImageLoaded = true; }\n      if (swiper.params.watchSlidesVisibility) {\n        $wrapperEl.children((\".\" + (swiperParams.slideVisibleClass))).each(function (elIndex, slideEl) {\n          var index = isVirtual ? $(slideEl).attr('data-swiper-slide-index') : $(slideEl).index();\n          swiper.lazy.loadInSlide(index);\n        });\n      } else if (slidesPerView > 1) {\n        for (var i = activeIndex; i < activeIndex + slidesPerView; i += 1) {\n          if (slideExist(i)) { swiper.lazy.loadInSlide(i); }\n        }\n      } else {\n        swiper.lazy.loadInSlide(activeIndex);\n      }\n      if (params.loadPrevNext) {\n        if (slidesPerView > 1 || (params.loadPrevNextAmount && params.loadPrevNextAmount > 1)) {\n          var amount = params.loadPrevNextAmount;\n          var spv = slidesPerView;\n          var maxIndex = Math.min(activeIndex + spv + Math.max(amount, spv), slides.length);\n          var minIndex = Math.max(activeIndex - Math.max(spv, amount), 0);\n          // Next Slides\n          for (var i$1 = activeIndex + slidesPerView; i$1 < maxIndex; i$1 += 1) {\n            if (slideExist(i$1)) { swiper.lazy.loadInSlide(i$1); }\n          }\n          // Prev Slides\n          for (var i$2 = minIndex; i$2 < activeIndex; i$2 += 1) {\n            if (slideExist(i$2)) { swiper.lazy.loadInSlide(i$2); }\n          }\n        } else {\n          var nextSlide = $wrapperEl.children((\".\" + (swiperParams.slideNextClass)));\n          if (nextSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(nextSlide)); }\n\n          var prevSlide = $wrapperEl.children((\".\" + (swiperParams.slidePrevClass)));\n          if (prevSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(prevSlide)); }\n        }\n      }\n    },\n  };\n\n  var Lazy$1 = {\n    name: 'lazy',\n    params: {\n      lazy: {\n        enabled: false,\n        loadPrevNext: false,\n        loadPrevNextAmount: 1,\n        loadOnTransitionStart: false,\n\n        elementClass: 'swiper-lazy',\n        loadingClass: 'swiper-lazy-loading',\n        loadedClass: 'swiper-lazy-loaded',\n        preloaderClass: 'swiper-lazy-preloader',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        lazy: {\n          initialImageLoaded: false,\n          load: Lazy.load.bind(swiper),\n          loadInSlide: Lazy.loadInSlide.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && swiper.params.preloadImages) {\n          swiper.params.preloadImages = false;\n        }\n      },\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.loop && swiper.params.initialSlide === 0) {\n          swiper.lazy.load();\n        }\n      },\n      scroll: function scroll() {\n        var swiper = this;\n        if (swiper.params.freeMode && !swiper.params.freeModeSticky) {\n          swiper.lazy.load();\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      scrollbarDragMove: function scrollbarDragMove() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      transitionStart: function transitionStart() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          if (swiper.params.lazy.loadOnTransitionStart || (!swiper.params.lazy.loadOnTransitionStart && !swiper.lazy.initialImageLoaded)) {\n            swiper.lazy.load();\n          }\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.lazy.loadOnTransitionStart) {\n          swiper.lazy.load();\n        }\n      },\n    },\n  };\n\n  /* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\n\n  var Controller = {\n    LinearSpline: function LinearSpline(x, y) {\n      var binarySearch = (function search() {\n        var maxIndex;\n        var minIndex;\n        var guess;\n        return function (array, val) {\n          minIndex = -1;\n          maxIndex = array.length;\n          while (maxIndex - minIndex > 1) {\n            guess = maxIndex + minIndex >> 1;\n            if (array[guess] <= val) {\n              minIndex = guess;\n            } else {\n              maxIndex = guess;\n            }\n          }\n          return maxIndex;\n        };\n      }());\n      this.x = x;\n      this.y = y;\n      this.lastIndex = x.length - 1;\n      // Given an x value (x2), return the expected y2 value:\n      // (x1,y1) is the known point before given value,\n      // (x3,y3) is the known point after given value.\n      var i1;\n      var i3;\n\n      this.interpolate = function interpolate(x2) {\n        if (!x2) { return 0; }\n\n        // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n        i3 = binarySearch(this.x, x2);\n        i1 = i3 - 1;\n\n        // We have our indexes i1 & i3, so we can calculate already:\n        // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n        return (((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1])) + this.y[i1];\n      };\n      return this;\n    },\n    // xxx: for now i will just save one spline function to to\n    getInterpolateFunction: function getInterpolateFunction(c) {\n      var swiper = this;\n      if (!swiper.controller.spline) {\n        swiper.controller.spline = swiper.params.loop\n          ? new Controller.LinearSpline(swiper.slidesGrid, c.slidesGrid)\n          : new Controller.LinearSpline(swiper.snapGrid, c.snapGrid);\n      }\n    },\n    setTranslate: function setTranslate(setTranslate$1, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var multiplier;\n      var controlledTranslate;\n      function setControlledTranslate(c) {\n        // this will create an Interpolate function based on the snapGrids\n        // x is the Grid of the scrolled scroller and y will be the controlled scroller\n        // it makes sense to create this only once and recall it for the interpolation\n        // the function does a lot of value caching for performance\n        var translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n        if (swiper.params.controller.by === 'slide') {\n          swiper.controller.getInterpolateFunction(c);\n          // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n          // but it did not work out\n          controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n        }\n\n        if (!controlledTranslate || swiper.params.controller.by === 'container') {\n          multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n          controlledTranslate = ((translate - swiper.minTranslate()) * multiplier) + c.minTranslate();\n        }\n\n        if (swiper.params.controller.inverse) {\n          controlledTranslate = c.maxTranslate() - controlledTranslate;\n        }\n        c.updateProgress(controlledTranslate);\n        c.setTranslate(controlledTranslate, swiper);\n        c.updateActiveIndex();\n        c.updateSlidesClasses();\n      }\n      if (Array.isArray(controlled)) {\n        for (var i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTranslate(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTranslate(controlled);\n      }\n    },\n    setTransition: function setTransition(duration, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var i;\n      function setControlledTransition(c) {\n        c.setTransition(duration, swiper);\n        if (duration !== 0) {\n          c.transitionStart();\n          if (c.params.autoHeight) {\n            Utils.nextTick(function () {\n              c.updateAutoHeight();\n            });\n          }\n          c.$wrapperEl.transitionEnd(function () {\n            if (!controlled) { return; }\n            if (c.params.loop && swiper.params.controller.by === 'slide') {\n              c.loopFix();\n            }\n            c.transitionEnd();\n          });\n        }\n      }\n      if (Array.isArray(controlled)) {\n        for (i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTransition(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTransition(controlled);\n      }\n    },\n  };\n  var Controller$1 = {\n    name: 'controller',\n    params: {\n      controller: {\n        control: undefined,\n        inverse: false,\n        by: 'slide', // or 'container'\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        controller: {\n          control: swiper.params.controller.control,\n          getInterpolateFunction: Controller.getInterpolateFunction.bind(swiper),\n          setTranslate: Controller.setTranslate.bind(swiper),\n          setTransition: Controller.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      update: function update() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      setTranslate: function setTranslate(translate, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTranslate(translate, byController);\n      },\n      setTransition: function setTransition(duration, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTransition(duration, byController);\n      },\n    },\n  };\n\n  var a11y = {\n    makeElFocusable: function makeElFocusable($el) {\n      $el.attr('tabIndex', '0');\n      return $el;\n    },\n    addElRole: function addElRole($el, role) {\n      $el.attr('role', role);\n      return $el;\n    },\n    addElLabel: function addElLabel($el, label) {\n      $el.attr('aria-label', label);\n      return $el;\n    },\n    disableEl: function disableEl($el) {\n      $el.attr('aria-disabled', true);\n      return $el;\n    },\n    enableEl: function enableEl($el) {\n      $el.attr('aria-disabled', false);\n      return $el;\n    },\n    onEnterKey: function onEnterKey(e) {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (e.keyCode !== 13) { return; }\n      var $targetEl = $(e.target);\n      if (swiper.navigation && swiper.navigation.$nextEl && $targetEl.is(swiper.navigation.$nextEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          swiper.a11y.notify(params.lastSlideMessage);\n        } else {\n          swiper.a11y.notify(params.nextSlideMessage);\n        }\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl && $targetEl.is(swiper.navigation.$prevEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          swiper.a11y.notify(params.firstSlideMessage);\n        } else {\n          swiper.a11y.notify(params.prevSlideMessage);\n        }\n      }\n      if (swiper.pagination && $targetEl.is((\".\" + (swiper.params.pagination.bulletClass)))) {\n        $targetEl[0].click();\n      }\n    },\n    notify: function notify(message) {\n      var swiper = this;\n      var notification = swiper.a11y.liveRegion;\n      if (notification.length === 0) { return; }\n      notification.html('');\n      notification.html(message);\n    },\n    updateNavigation: function updateNavigation() {\n      var swiper = this;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          swiper.a11y.disableEl($prevEl);\n        } else {\n          swiper.a11y.enableEl($prevEl);\n        }\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          swiper.a11y.disableEl($nextEl);\n        } else {\n          swiper.a11y.enableEl($nextEl);\n        }\n      }\n    },\n    updatePagination: function updatePagination() {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.bullets.each(function (bulletIndex, bulletEl) {\n          var $bulletEl = $(bulletEl);\n          swiper.a11y.makeElFocusable($bulletEl);\n          swiper.a11y.addElRole($bulletEl, 'button');\n          swiper.a11y.addElLabel($bulletEl, params.paginationBulletMessage.replace(/{{index}}/, $bulletEl.index() + 1));\n        });\n      }\n    },\n    init: function init() {\n      var swiper = this;\n\n      swiper.$el.append(swiper.a11y.liveRegion);\n\n      // Navigation\n      var params = swiper.params.a11y;\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        swiper.a11y.makeElFocusable($nextEl);\n        swiper.a11y.addElRole($nextEl, 'button');\n        swiper.a11y.addElLabel($nextEl, params.nextSlideMessage);\n        $nextEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        swiper.a11y.makeElFocusable($prevEl);\n        swiper.a11y.addElRole($prevEl, 'button');\n        swiper.a11y.addElLabel($prevEl, params.prevSlideMessage);\n        $prevEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.on('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.a11y.liveRegion && swiper.a11y.liveRegion.length > 0) { swiper.a11y.liveRegion.remove(); }\n\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        $nextEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        $prevEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.off('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n  };\n  var A11y = {\n    name: 'a11y',\n    params: {\n      a11y: {\n        enabled: true,\n        notificationClass: 'swiper-notification',\n        prevSlideMessage: 'Previous slide',\n        nextSlideMessage: 'Next slide',\n        firstSlideMessage: 'This is the first slide',\n        lastSlideMessage: 'This is the last slide',\n        paginationBulletMessage: 'Go to slide {{index}}',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        a11y: {\n          liveRegion: $((\"<span class=\\\"\" + (swiper.params.a11y.notificationClass) + \"\\\" aria-live=\\\"assertive\\\" aria-atomic=\\\"true\\\"></span>\")),\n        },\n      });\n      Object.keys(a11y).forEach(function (methodName) {\n        swiper.a11y[methodName] = a11y[methodName].bind(swiper);\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.init();\n        swiper.a11y.updateNavigation();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      paginationUpdate: function paginationUpdate() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updatePagination();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.destroy();\n      },\n    },\n  };\n\n  var History = {\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.history) { return; }\n      if (!win.history || !win.history.pushState) {\n        swiper.params.history.enabled = false;\n        swiper.params.hashNavigation.enabled = true;\n        return;\n      }\n      var history = swiper.history;\n      history.initialized = true;\n      history.paths = History.getPathValues();\n      if (!history.paths.key && !history.paths.value) { return; }\n      history.scrollToSlide(0, history.paths.value, swiper.params.runCallbacksOnInit);\n      if (!swiper.params.history.replaceState) {\n        win.addEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (!swiper.params.history.replaceState) {\n        win.removeEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    setHistoryPopState: function setHistoryPopState() {\n      var swiper = this;\n      swiper.history.paths = History.getPathValues();\n      swiper.history.scrollToSlide(swiper.params.speed, swiper.history.paths.value, false);\n    },\n    getPathValues: function getPathValues() {\n      var pathArray = win.location.pathname.slice(1).split('/').filter(function (part) { return part !== ''; });\n      var total = pathArray.length;\n      var key = pathArray[total - 2];\n      var value = pathArray[total - 1];\n      return { key: key, value: value };\n    },\n    setHistory: function setHistory(key, index) {\n      var swiper = this;\n      if (!swiper.history.initialized || !swiper.params.history.enabled) { return; }\n      var slide = swiper.slides.eq(index);\n      var value = History.slugify(slide.attr('data-history'));\n      if (!win.location.pathname.includes(key)) {\n        value = key + \"/\" + value;\n      }\n      var currentState = win.history.state;\n      if (currentState && currentState.value === value) {\n        return;\n      }\n      if (swiper.params.history.replaceState) {\n        win.history.replaceState({ value: value }, null, value);\n      } else {\n        win.history.pushState({ value: value }, null, value);\n      }\n    },\n    slugify: function slugify(text) {\n      return text.toString()\n        .replace(/\\s+/g, '-')\n        .replace(/[^\\w-]+/g, '')\n        .replace(/--+/g, '-')\n        .replace(/^-+/, '')\n        .replace(/-+$/, '');\n    },\n    scrollToSlide: function scrollToSlide(speed, value, runCallbacks) {\n      var swiper = this;\n      if (value) {\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHistory = History.slugify(slide.attr('data-history'));\n          if (slideHistory === value && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, runCallbacks);\n          }\n        }\n      } else {\n        swiper.slideTo(0, speed, runCallbacks);\n      }\n    },\n  };\n\n  var History$1 = {\n    name: 'history',\n    params: {\n      history: {\n        enabled: false,\n        replaceState: false,\n        key: 'slides',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        history: {\n          init: History.init.bind(swiper),\n          setHistory: History.setHistory.bind(swiper),\n          setHistoryPopState: History.setHistoryPopState.bind(swiper),\n          scrollToSlide: History.scrollToSlide.bind(swiper),\n          destroy: History.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.history.initialized) {\n          swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n        }\n      },\n    },\n  };\n\n  var HashNavigation = {\n    onHashCange: function onHashCange() {\n      var swiper = this;\n      var newHash = doc.location.hash.replace('#', '');\n      var activeSlideHash = swiper.slides.eq(swiper.activeIndex).attr('data-hash');\n      if (newHash !== activeSlideHash) {\n        var newIndex = swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-hash=\\\"\" + newHash + \"\\\"]\")).index();\n        if (typeof newIndex === 'undefined') { return; }\n        swiper.slideTo(newIndex);\n      }\n    },\n    setHash: function setHash() {\n      var swiper = this;\n      if (!swiper.hashNavigation.initialized || !swiper.params.hashNavigation.enabled) { return; }\n      if (swiper.params.hashNavigation.replaceState && win.history && win.history.replaceState) {\n        win.history.replaceState(null, null, ((\"#\" + (swiper.slides.eq(swiper.activeIndex).attr('data-hash'))) || ''));\n      } else {\n        var slide = swiper.slides.eq(swiper.activeIndex);\n        var hash = slide.attr('data-hash') || slide.attr('data-history');\n        doc.location.hash = hash || '';\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.hashNavigation.enabled || (swiper.params.history && swiper.params.history.enabled)) { return; }\n      swiper.hashNavigation.initialized = true;\n      var hash = doc.location.hash.replace('#', '');\n      if (hash) {\n        var speed = 0;\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n          if (slideHash === hash && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n          }\n        }\n      }\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).on('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).off('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n  };\n  var HashNavigation$1 = {\n    name: 'hash-navigation',\n    params: {\n      hashNavigation: {\n        enabled: false,\n        replaceState: false,\n        watchState: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        hashNavigation: {\n          initialized: false,\n          init: HashNavigation.init.bind(swiper),\n          destroy: HashNavigation.destroy.bind(swiper),\n          setHash: HashNavigation.setHash.bind(swiper),\n          onHashCange: HashNavigation.onHashCange.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.hashNavigation.initialized) {\n          swiper.hashNavigation.setHash();\n        }\n      },\n    },\n  };\n\n  /* eslint no-underscore-dangle: \"off\" */\n\n  var Autoplay = {\n    run: function run() {\n      var swiper = this;\n      var $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n      var delay = swiper.params.autoplay.delay;\n      if ($activeSlideEl.attr('data-swiper-autoplay')) {\n        delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n      }\n      clearTimeout(swiper.autoplay.timeout);\n      swiper.autoplay.timeout = Utils.nextTick(function () {\n        if (swiper.params.autoplay.reverseDirection) {\n          if (swiper.params.loop) {\n            swiper.loopFix();\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.isBeginning) {\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.params.autoplay.stopOnLastSlide) {\n            swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else {\n            swiper.autoplay.stop();\n          }\n        } else if (swiper.params.loop) {\n          swiper.loopFix();\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.isEnd) {\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else {\n          swiper.autoplay.stop();\n        }\n      }, delay);\n    },\n    start: function start() {\n      var swiper = this;\n      if (typeof swiper.autoplay.timeout !== 'undefined') { return false; }\n      if (swiper.autoplay.running) { return false; }\n      swiper.autoplay.running = true;\n      swiper.emit('autoplayStart');\n      swiper.autoplay.run();\n      return true;\n    },\n    stop: function stop() {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return false; }\n      if (typeof swiper.autoplay.timeout === 'undefined') { return false; }\n\n      if (swiper.autoplay.timeout) {\n        clearTimeout(swiper.autoplay.timeout);\n        swiper.autoplay.timeout = undefined;\n      }\n      swiper.autoplay.running = false;\n      swiper.emit('autoplayStop');\n      return true;\n    },\n    pause: function pause(speed) {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return; }\n      if (swiper.autoplay.paused) { return; }\n      if (swiper.autoplay.timeout) { clearTimeout(swiper.autoplay.timeout); }\n      swiper.autoplay.paused = true;\n      if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n        swiper.autoplay.paused = false;\n        swiper.autoplay.run();\n      } else {\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n      }\n    },\n  };\n\n  var Autoplay$1 = {\n    name: 'autoplay',\n    params: {\n      autoplay: {\n        enabled: false,\n        delay: 3000,\n        waitForTransition: true,\n        disableOnInteraction: true,\n        stopOnLastSlide: false,\n        reverseDirection: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        autoplay: {\n          running: false,\n          paused: false,\n          run: Autoplay.run.bind(swiper),\n          start: Autoplay.start.bind(swiper),\n          stop: Autoplay.stop.bind(swiper),\n          pause: Autoplay.pause.bind(swiper),\n          onTransitionEnd: function onTransitionEnd(e) {\n            if (!swiper || swiper.destroyed || !swiper.$wrapperEl) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n            swiper.autoplay.paused = false;\n            if (!swiper.autoplay.running) {\n              swiper.autoplay.stop();\n            } else {\n              swiper.autoplay.run();\n            }\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.autoplay.enabled) {\n          swiper.autoplay.start();\n        }\n      },\n      beforeTransitionStart: function beforeTransitionStart(speed, internal) {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (internal || !swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.pause(speed);\n          } else {\n            swiper.autoplay.stop();\n          }\n        }\n      },\n      sliderFirstMove: function sliderFirstMove() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.stop();\n          } else {\n            swiper.autoplay.pause();\n          }\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          swiper.autoplay.stop();\n        }\n      },\n    },\n  };\n\n  var Fade = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = swiper.slides.eq(i);\n        var offset = $slideEl[0].swiperSlideOffset;\n        var tx = -offset;\n        if (!swiper.params.virtualTranslate) { tx -= swiper.translate; }\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n        }\n        var slideOpacity = swiper.params.fadeEffect.crossFade\n          ? Math.max(1 - Math.abs($slideEl[0].progress), 0)\n          : 1 + Math.min(Math.max($slideEl[0].progress, -1), 0);\n        $slideEl\n          .css({\n            opacity: slideOpacity,\n          })\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides.transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        slides.transitionEnd(function () {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFade = {\n    name: 'effect-fade',\n    params: {\n      fadeEffect: {\n        crossFade: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        fadeEffect: {\n          setTranslate: Fade.setTranslate.bind(swiper),\n          setTransition: Fade.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"fade\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Cube = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var rtl = swiper.rtlTranslate;\n      var swiperSize = swiper.size;\n      var params = swiper.params.cubeEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      var wrapperRotate = 0;\n      var $cubeShadowEl;\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl = $wrapperEl.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $wrapperEl.append($cubeShadowEl);\n          }\n          $cubeShadowEl.css({ height: (swiperWidth + \"px\") });\n        } else {\n          $cubeShadowEl = $el.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $el.append($cubeShadowEl);\n          }\n        }\n      }\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideIndex = i;\n        if (isVirtual) {\n          slideIndex = parseInt($slideEl.attr('data-swiper-slide-index'), 10);\n        }\n        var slideAngle = slideIndex * 90;\n        var round = Math.floor(slideAngle / 360);\n        if (rtl) {\n          slideAngle = -slideAngle;\n          round = Math.floor(-slideAngle / 360);\n        }\n        var progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        var tx = 0;\n        var ty = 0;\n        var tz = 0;\n        if (slideIndex % 4 === 0) {\n          tx = -round * 4 * swiperSize;\n          tz = 0;\n        } else if ((slideIndex - 1) % 4 === 0) {\n          tx = 0;\n          tz = -round * 4 * swiperSize;\n        } else if ((slideIndex - 2) % 4 === 0) {\n          tx = swiperSize + (round * 4 * swiperSize);\n          tz = swiperSize;\n        } else if ((slideIndex - 3) % 4 === 0) {\n          tx = -swiperSize;\n          tz = (3 * swiperSize) + (swiperSize * 4 * round);\n        }\n        if (rtl) {\n          tx = -tx;\n        }\n\n        if (!isHorizontal) {\n          ty = tx;\n          tx = 0;\n        }\n\n        var transform = \"rotateX(\" + (isHorizontal ? 0 : -slideAngle) + \"deg) rotateY(\" + (isHorizontal ? slideAngle : 0) + \"deg) translate3d(\" + tx + \"px, \" + ty + \"px, \" + tz + \"px)\";\n        if (progress <= 1 && progress > -1) {\n          wrapperRotate = (slideIndex * 90) + (progress * 90);\n          if (rtl) { wrapperRotate = (-slideIndex * 90) - (progress * 90); }\n        }\n        $slideEl.transform(transform);\n        if (params.slideShadows) {\n          // Set shadows\n          var shadowBefore = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n      }\n      $wrapperEl.css({\n        '-webkit-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-moz-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-ms-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        'transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n      });\n\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl.transform((\"translate3d(0px, \" + ((swiperWidth / 2) + params.shadowOffset) + \"px, \" + (-swiperWidth / 2) + \"px) rotateX(90deg) rotateZ(0deg) scale(\" + (params.shadowScale) + \")\"));\n        } else {\n          var shadowAngle = Math.abs(wrapperRotate) - (Math.floor(Math.abs(wrapperRotate) / 90) * 90);\n          var multiplier = 1.5 - (\n            (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2)\n            + (Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2)\n          );\n          var scale1 = params.shadowScale;\n          var scale2 = params.shadowScale / multiplier;\n          var offset = params.shadowOffset;\n          $cubeShadowEl.transform((\"scale3d(\" + scale1 + \", 1, \" + scale2 + \") translate3d(0px, \" + ((swiperHeight / 2) + offset) + \"px, \" + (-swiperHeight / 2 / scale2) + \"px) rotateX(-90deg)\"));\n        }\n      }\n      var zFactor = (Browser.isSafari || Browser.isUiWebView) ? (-swiperSize / 2) : 0;\n      $wrapperEl\n        .transform((\"translate3d(0px,0,\" + zFactor + \"px) rotateX(\" + (swiper.isHorizontal() ? 0 : wrapperRotate) + \"deg) rotateY(\" + (swiper.isHorizontal() ? -wrapperRotate : 0) + \"deg)\"));\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n        $el.find('.swiper-cube-shadow').transition(duration);\n      }\n    },\n  };\n\n  var EffectCube = {\n    name: 'effect-cube',\n    params: {\n      cubeEffect: {\n        slideShadows: true,\n        shadow: true,\n        shadowOffset: 20,\n        shadowScale: 0.94,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        cubeEffect: {\n          setTranslate: Cube.setTranslate.bind(swiper),\n          setTransition: Cube.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"cube\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          resistanceRatio: 0,\n          spaceBetween: 0,\n          centeredSlides: false,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Flip = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      var rtl = swiper.rtlTranslate;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var progress = $slideEl[0].progress;\n        if (swiper.params.flipEffect.limitRotation) {\n          progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        }\n        var offset = $slideEl[0].swiperSlideOffset;\n        var rotate = -180 * progress;\n        var rotateY = rotate;\n        var rotateX = 0;\n        var tx = -offset;\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n          rotateX = -rotateY;\n          rotateY = 0;\n        } else if (rtl) {\n          rotateY = -rotateY;\n        }\n\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n        if (swiper.params.flipEffect.slideShadows) {\n          // Set shadows\n          var shadowBefore = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n        $slideEl\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px) rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        // eslint-disable-next-line\n        slides.eq(activeIndex).transitionEnd(function onTransitionEnd() {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          // if (!$(this).hasClass(swiper.params.slideActiveClass)) return;\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFlip = {\n    name: 'effect-flip',\n    params: {\n      flipEffect: {\n        slideShadows: true,\n        limitRotation: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        flipEffect: {\n          setTranslate: Flip.setTranslate.bind(swiper),\n          setTransition: Flip.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"flip\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Coverflow = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slidesSizesGrid = swiper.slidesSizesGrid;\n      var params = swiper.params.coverflowEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var transform = swiper.translate;\n      var center = isHorizontal ? -transform + (swiperWidth / 2) : -transform + (swiperHeight / 2);\n      var rotate = isHorizontal ? params.rotate : -params.rotate;\n      var translate = params.depth;\n      // Each slide offset from center\n      for (var i = 0, length = slides.length; i < length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideSize = slidesSizesGrid[i];\n        var slideOffset = $slideEl[0].swiperSlideOffset;\n        var offsetMultiplier = ((center - slideOffset - (slideSize / 2)) / slideSize) * params.modifier;\n\n        var rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n        var rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n        // var rotateZ = 0\n        var translateZ = -translate * Math.abs(offsetMultiplier);\n\n        var translateY = isHorizontal ? 0 : params.stretch * (offsetMultiplier);\n        var translateX = isHorizontal ? params.stretch * (offsetMultiplier) : 0;\n\n        // Fix for ultra small values\n        if (Math.abs(translateX) < 0.001) { translateX = 0; }\n        if (Math.abs(translateY) < 0.001) { translateY = 0; }\n        if (Math.abs(translateZ) < 0.001) { translateZ = 0; }\n        if (Math.abs(rotateY) < 0.001) { rotateY = 0; }\n        if (Math.abs(rotateX) < 0.001) { rotateX = 0; }\n\n        var slideTransform = \"translate3d(\" + translateX + \"px,\" + translateY + \"px,\" + translateZ + \"px)  rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\";\n\n        $slideEl.transform(slideTransform);\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n        if (params.slideShadows) {\n          // Set shadows\n          var $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if ($shadowBeforeEl.length === 0) {\n            $shadowBeforeEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append($shadowBeforeEl);\n          }\n          if ($shadowAfterEl.length === 0) {\n            $shadowAfterEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append($shadowAfterEl);\n          }\n          if ($shadowBeforeEl.length) { $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0; }\n          if ($shadowAfterEl.length) { $shadowAfterEl[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0; }\n        }\n      }\n\n      // Set correct perspective for IE10\n      if (Support.pointerEvents || Support.prefixedPointerEvents) {\n        var ws = $wrapperEl[0].style;\n        ws.perspectiveOrigin = center + \"px 50%\";\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      swiper.slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n    },\n  };\n\n  var EffectCoverflow = {\n    name: 'effect-coverflow',\n    params: {\n      coverflowEffect: {\n        rotate: 50,\n        stretch: 0,\n        depth: 100,\n        modifier: 1,\n        slideShadows: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        coverflowEffect: {\n          setTranslate: Coverflow.setTranslate.bind(swiper),\n          setTransition: Coverflow.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"coverflow\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Thumbs = {\n    init: function init() {\n      var swiper = this;\n      var ref = swiper.params;\n      var thumbsParams = ref.thumbs;\n      var SwiperClass = swiper.constructor;\n      if (thumbsParams.swiper instanceof SwiperClass) {\n        swiper.thumbs.swiper = thumbsParams.swiper;\n        Utils.extend(swiper.thumbs.swiper.originalParams, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n        Utils.extend(swiper.thumbs.swiper.params, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n      } else if (Utils.isObject(thumbsParams.swiper)) {\n        swiper.thumbs.swiper = new SwiperClass(Utils.extend({}, thumbsParams.swiper, {\n          watchSlidesVisibility: true,\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        }));\n        swiper.thumbs.swiperCreated = true;\n      }\n      swiper.thumbs.swiper.$el.addClass(swiper.params.thumbs.thumbsContainerClass);\n      swiper.thumbs.swiper.on('tap', swiper.thumbs.onThumbClick);\n    },\n    onThumbClick: function onThumbClick() {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n      var clickedIndex = thumbsSwiper.clickedIndex;\n      var clickedSlide = thumbsSwiper.clickedSlide;\n      if (clickedSlide && $(clickedSlide).hasClass(swiper.params.thumbs.slideThumbActiveClass)) { return; }\n      if (typeof clickedIndex === 'undefined' || clickedIndex === null) { return; }\n      var slideToIndex;\n      if (thumbsSwiper.params.loop) {\n        slideToIndex = parseInt($(thumbsSwiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      } else {\n        slideToIndex = clickedIndex;\n      }\n      if (swiper.params.loop) {\n        var currentIndex = swiper.activeIndex;\n        if (swiper.slides.eq(currentIndex).hasClass(swiper.params.slideDuplicateClass)) {\n          swiper.loopFix();\n          // eslint-disable-next-line\n          swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n          currentIndex = swiper.activeIndex;\n        }\n        var prevIndex = swiper.slides.eq(currentIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        var nextIndex = swiper.slides.eq(currentIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        if (typeof prevIndex === 'undefined') { slideToIndex = nextIndex; }\n        else if (typeof nextIndex === 'undefined') { slideToIndex = prevIndex; }\n        else if (nextIndex - currentIndex < currentIndex - prevIndex) { slideToIndex = nextIndex; }\n        else { slideToIndex = prevIndex; }\n      }\n      swiper.slideTo(slideToIndex);\n    },\n    update: function update(initial) {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n\n      var slidesPerView = thumbsSwiper.params.slidesPerView === 'auto'\n        ? thumbsSwiper.slidesPerViewDynamic()\n        : thumbsSwiper.params.slidesPerView;\n\n      if (swiper.realIndex !== thumbsSwiper.realIndex) {\n        var currentThumbsIndex = thumbsSwiper.activeIndex;\n        var newThumbsIndex;\n        if (thumbsSwiper.params.loop) {\n          if (thumbsSwiper.slides.eq(currentThumbsIndex).hasClass(thumbsSwiper.params.slideDuplicateClass)) {\n            thumbsSwiper.loopFix();\n            // eslint-disable-next-line\n            thumbsSwiper._clientLeft = thumbsSwiper.$wrapperEl[0].clientLeft;\n            currentThumbsIndex = thumbsSwiper.activeIndex;\n          }\n          // Find actual thumbs index to slide to\n          var prevThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          var nextThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          if (typeof prevThumbsIndex === 'undefined') { newThumbsIndex = nextThumbsIndex; }\n          else if (typeof nextThumbsIndex === 'undefined') { newThumbsIndex = prevThumbsIndex; }\n          else if (nextThumbsIndex - currentThumbsIndex === currentThumbsIndex - prevThumbsIndex) { newThumbsIndex = currentThumbsIndex; }\n          else if (nextThumbsIndex - currentThumbsIndex < currentThumbsIndex - prevThumbsIndex) { newThumbsIndex = nextThumbsIndex; }\n          else { newThumbsIndex = prevThumbsIndex; }\n        } else {\n          newThumbsIndex = swiper.realIndex;\n        }\n        if (thumbsSwiper.visibleSlidesIndexes && thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n          if (thumbsSwiper.params.centeredSlides) {\n            if (newThumbsIndex > currentThumbsIndex) {\n              newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n            } else {\n              newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n            }\n          } else if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n          }\n          thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n        }\n      }\n\n      // Activate thumbs\n      var thumbsToActivate = 1;\n      var thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n      if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n        thumbsToActivate = swiper.params.slidesPerView;\n      }\n\n      thumbsSwiper.slides.removeClass(thumbActiveClass);\n      if (thumbsSwiper.params.loop || thumbsSwiper.params.virtual) {\n        for (var i = 0; i < thumbsToActivate; i += 1) {\n          thumbsSwiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex + i) + \"\\\"]\")).addClass(thumbActiveClass);\n        }\n      } else {\n        for (var i$1 = 0; i$1 < thumbsToActivate; i$1 += 1) {\n          thumbsSwiper.slides.eq(swiper.realIndex + i$1).addClass(thumbActiveClass);\n        }\n      }\n    },\n  };\n  var Thumbs$1 = {\n    name: 'thumbs',\n    params: {\n      thumbs: {\n        swiper: null,\n        slideThumbActiveClass: 'swiper-slide-thumb-active',\n        thumbsContainerClass: 'swiper-container-thumbs',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        thumbs: {\n          swiper: null,\n          init: Thumbs.init.bind(swiper),\n          update: Thumbs.update.bind(swiper),\n          onThumbClick: Thumbs.onThumbClick.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        var ref = swiper.params;\n        var thumbs = ref.thumbs;\n        if (!thumbs || !thumbs.swiper) { return; }\n        swiper.thumbs.init();\n        swiper.thumbs.update(true);\n      },\n      slideChange: function slideChange() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      update: function update() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        thumbsSwiper.setTransition(duration);\n      },\n      beforeDestroy: function beforeDestroy() {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        if (swiper.thumbs.swiperCreated && thumbsSwiper) {\n          thumbsSwiper.destroy();\n        }\n      },\n    },\n  };\n\n  // Swiper Class\n\n  var components = [\n    Device$1,\n    Support$1,\n    Browser$1,\n    Resize,\n    Observer$1,\n    Virtual$1,\n    Keyboard$1,\n    Mousewheel$1,\n    Navigation$1,\n    Pagination$1,\n    Scrollbar$1,\n    Parallax$1,\n    Zoom$1,\n    Lazy$1,\n    Controller$1,\n    A11y,\n    History$1,\n    HashNavigation$1,\n    Autoplay$1,\n    EffectFade,\n    EffectCube,\n    EffectFlip,\n    EffectCoverflow,\n    Thumbs$1\n  ];\n\n  if (typeof Swiper.use === 'undefined') {\n    Swiper.use = Swiper.Class.use;\n    Swiper.installModule = Swiper.Class.installModule;\n  }\n\n  Swiper.use(components);\n\n  return Swiper;\n\n}));\n"], "sourceRoot": ""}