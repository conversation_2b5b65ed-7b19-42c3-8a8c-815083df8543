{"version": 3, "sources": ["webpack:///./src/views/shop/GoodsCon.vue?e092", "webpack:///src/views/shop/GoodsCon.vue", "webpack:///./src/views/shop/GoodsCon.vue?df87", "webpack:///./src/views/shop/GoodsCon.vue", "webpack:///./src/views/shop/GoodsCon.vue?ea6f"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "ref", "class", "posterImageStatus", "attrs", "storeInfo", "slider_image", "staticClass", "_v", "_s", "price", "store_name", "staticStyle", "promotion_desc", "on", "selecAttrTap", "attrTxt", "attrValue", "domProps", "description", "animated", "CartCount", "_e", "joinCart", "buyCart", "attr", "changeFun", "posterData", "setPosterImageStatus", "staticRenderFns", "name", "NAME", "components", "ProductConSwiper", "ProductWindow", "StorePoster", "data", "shareInfoStatus", "weixin<PERSON>tatus", "mapShow", "mapKey", "image", "title", "code", "coupon", "list", "cartAttr", "productAttr", "productSelect", "isOpen", "productValue", "id", "couponList", "cart_num", "replyCount", "replyC<PERSON>ce", "reply", "priceName", "CartList", "posters", "banner", "swiperRecommend", "pagination", "el", "clickable", "autoplay", "loop", "speed", "observer", "observeParents", "goodList", "system_store", "productInfo", "qqmapsdk", "computed", "watch", "$route", "n", "params", "productCon", "updated", "window", "scroll", "mounted", "methods", "updateTitle", "document", "meta", "sTop", "body", "documentElement", "scrollTop", "action", "value", "isLastOne", "that", "$store", "commit", "applicationType", "pointType", "applicationName", "pointName", "q", "countmoney", "$router", "push", "undefined", "$dialog", "toast", "hasSameProductionInCart", "setTimeout", "uniqueId", "number", "component"], "mappings": "gJAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,IAAI,MAAMC,MAAM,CAACP,EAAIQ,kBAAoB,uBAAyB,gBAAgB,CAACJ,EAAG,qBAAqB,CAACK,MAAM,CAAC,WAAWT,EAAIU,UAAUC,gBAAgBP,EAAG,MAAM,CAACQ,YAAY,WAAW,CAACR,EAAG,MAAM,CAACQ,YAAY,yCAAyC,CAACR,EAAG,MAAM,CAACQ,YAAY,wBAAwB,CAACZ,EAAIa,GAAG,SAAST,EAAG,OAAO,CAACQ,YAAY,OAAO,CAACZ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIU,UAAUK,cAAcX,EAAG,MAAM,CAACQ,YAAY,aAAa,CAACZ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIU,UAAUM,eAAeZ,EAAG,MAAM,CAACQ,YAAY,YAAYK,YAAY,CAAC,YAAY,OAAO,MAAQ,YAAY,CAACjB,EAAIa,GAAGb,EAAIc,GAAGd,EAAIU,UAAUQ,qBAAqBd,EAAG,MAAM,CAACQ,YAAY,yCAAyCO,GAAG,CAAC,MAAQnB,EAAIoB,eAAe,CAAChB,EAAG,MAAM,CAACJ,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAIqB,SAAS,MAAMjB,EAAG,OAAO,CAACQ,YAAY,YAAY,CAACZ,EAAIa,GAAGb,EAAIc,GAAGd,EAAIsB,gBAAgBlB,EAAG,MAAM,CAACQ,YAAY,4BAA4BR,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,MAAM,CAACQ,YAAY,SAAS,CAACZ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACQ,YAAY,SAASW,SAAS,CAAC,UAAYvB,EAAIc,GAAGd,EAAIU,UAAUc,kBAAkBpB,EAAG,MAAM,CAACa,YAAY,CAAC,OAAS,YAAYb,EAAG,MAAM,CAACQ,YAAY,uCAAuC,CAACR,EAAG,cAAc,CAACQ,YAAY,gBAAgBL,OAAuB,IAAjBP,EAAIyB,SAAoB,WAAa,GAAGhB,MAAM,CAAC,GAAK,UAAU,CAACL,EAAG,MAAM,CAACQ,YAAY,2BAA2B,CAAEZ,EAAI0B,UAAY,EAAGtB,EAAG,OAAO,CAACQ,YAAY,oBAAoB,CAACZ,EAAIa,GAAGb,EAAIc,GAAGd,EAAI0B,cAAc1B,EAAI2B,OAAOvB,EAAG,MAAM,CAACJ,EAAIa,GAAG,WAAWT,EAAG,MAAM,CAACQ,YAAY,WAAWK,YAAY,CAAC,MAAQ,SAAS,CAACb,EAAG,MAAM,CAACQ,YAAY,WAAWO,GAAG,CAAC,MAAQnB,EAAI4B,WAAW,CAAC5B,EAAIa,GAAG,WAAWT,EAAG,MAAM,CAACQ,YAAY,UAAUO,GAAG,CAAC,MAAQnB,EAAI6B,UAAU,CAAC7B,EAAIa,GAAG,aAAa,GAAGT,EAAG,gBAAgB,CAACK,MAAM,CAAC,KAAOT,EAAI8B,MAAMX,GAAG,CAAC,UAAYnB,EAAI+B,aAAa3B,EAAG,cAAc,CAACK,MAAM,CAAC,kBAAoBT,EAAIQ,kBAAkB,WAAaR,EAAIgC,YAAYb,GAAG,CAAC,qBAAuBnB,EAAIiC,yBAAyB,IAC19DC,EAAkB,G,8xBCoDtB,iBACA,GACEC,KAAMC,EACNC,WAAY,CACVC,iBAAJ,OACIC,cAAJ,OACIC,YAAJ,QAEEC,KAAM,WACJ,MAAO,CACLC,iBAAiB,EACjBC,cAAc,EACdC,SAAS,EACTC,OAAQ,GACRb,WAAY,CACVc,MAAO,GACPC,MAAO,GACPhC,MAAO,GACPiC,KAAM,IAERxC,mBAAmB,EACnBiB,UAAU,EACVwB,OAAQ,CACNA,QAAQ,EACRC,KAAM,IAERpB,KAAM,CACJqB,UAAU,EACVC,YAAa,GACbC,cAAe,IAEjBC,QAAQ,EACRC,aAAc,GACdC,GAAI,EACJ9C,UAAW,GACX+C,WAAY,GACZpC,QAAS,MACTC,UAAW,GACXoC,SAAU,EACVC,WAAY,GACZC,YAAa,GACbC,MAAO,GACPC,UAAW,EACXpC,UAAW,EACXqC,SAAU,EACVC,SAAS,EACTC,OAAQ,CAAC,GAAI,IACbC,gBAAiB,CACfC,WAAY,CACVC,GAAI,qBACJC,WAAW,GAEbC,UAAU,EACVC,MAAM,EACNC,MAAO,IACPC,UAAU,EACVC,gBAAgB,GAElBC,SAAU,GACVC,aAAc,GACdC,YAAa,GACbC,SAAU,OAGdC,SAAU,EAAZ,GACA,6BAEEC,MAAO,CACLC,OADJ,SACA,GACUC,EAAE/C,OAASC,IACbnC,KAAKuD,GAAK0B,EAAEC,OAAO3B,GACnBvD,KAAKmF,gBAIXC,QA1EF,WA2EIC,OAAOC,OAAO,EAAG,IAEnBC,QAAS,WACPvF,KAAKuD,GAAKvD,KAAKgF,OAAOE,OAAO3B,GAC7BvD,KAAKmF,cAEPK,QAAS,CACPC,YADJ,WAEMC,SAAS5C,MAAQ9C,KAAKS,UAAUM,YAAcf,KAAKgF,OAAOW,KAAK7C,OAEjEd,qBAAsB,WACpB,IAAI4D,EAAOF,SAASG,MAAQH,SAASI,gBACrCF,EAAKG,UAAY,EACjB/F,KAAKO,mBAAqBP,KAAKO,kBAC/BP,KAAK+D,SAAU,GAGjB,WAXJ,gHAYA,iCACA,gDACA,oBACA,mBACA,iBAEA,gDACA,oCACA,sBACA,gDAEA,gDAEA,2CACA,8CAEA,4CACA,EADA,CAEA,sDACA,iBAGA,WACA,uBACA,kCAEA,yBACA,QACA,cACA,YACA,cACA,sBACA,sBACA,WACA,wBACA,yBACA,oBACA,gCACA,8BACA,6BACA,sCACA,mBACA,sBACA,4BACA,qCACA,uCACA,sCAGA,mBACA,oBA9DA,iDAgEIjC,UAAW,SAAf,4BACM,GAAc,cAAVkE,EACFhG,KAAK6B,KAAKqB,SAAW+C,OAC7B,uBACQ,IAAR,2CACQ,GAAIC,EAAW,OACflG,KAAK6B,KAAKuB,cAAgB,EAAlC,GACA,wBADA,CAEUK,SAAUwC,EACpB,mCACA,yCAEA,qBAEQjG,KAAK6B,KAAO,EAApB,GACA,UADA,CAEUsB,YAAa8C,EAAM9C,YACnBC,cAAe,EAAzB,GACA,wBADA,GAEA,qBAMIjC,aAAc,WACZnB,KAAK6B,KAAKqB,UAAW,EACrBlD,KAAKqD,QAAS,GAEhBzB,QAAS,WACP,IAAN,OACM,GAAIuE,EAAKtE,KAAKqB,UAAYiD,EAAK9C,OAAQ,CACrC8C,EAAK9C,QAAS,EACd8C,EAAKtE,KAAKqB,UAAW,EACrB,IAAR,uBACA,OACA,EADA,CAEU0B,YAAauB,EAAK1F,YAE5B,qBACQ0F,EAAKC,OAAOC,OAAO,2BAA4B,CAC7CC,gBAAiBlD,EAAcmD,UAC/BC,gBAAiBpD,EAAcqD,YAEjCN,EAAKC,OAAOC,OAAO,qBAAsB,CAACK,IAC1CP,EAAKC,OAAOC,OAAO,2BAA4BM,GAC/CR,EAAKS,QAAQC,KAAK,CAA1B,oCAEQV,EAAKtE,KAAKqB,UAAW,EACrBiD,EAAK9C,QAAS,GAGlB1B,SAAU,WACR,IAAN,OACM,IAA2B,IAAvBwE,EAAKtE,KAAKqB,WAAqC,IAAhBiD,EAAK9C,OACtC,OAAO,EAAf,UAEM,IAA2B,IAAvB8C,EAAKtE,KAAKqB,SACZ,OAAO,EAAf,iBAGM,IAAN,uBACM,QAAsB4D,IAAlB1D,IAA+C,IAAhB+C,EAAK9C,OACtC,OAAO8C,EAAKY,QAAQC,MAAM,CAAlC,cAGM,IAAN,mBACA,YAAQ,OAAR,2BAYM,GATKC,GACHd,EAAK1E,YAGP0E,EAAK3E,UAAW,EAChB0F,YAAW,WACTf,EAAK3E,UAAW,IACxB,KAEUyF,EACF,OAAR,OAAQ,CAAR,CACUE,SAAU/D,EAAc+D,SACxBC,OAAQhE,EAAcK,WAEhC,kBACU,QAAV,OACU,EAAV,eACY,IAAZ,SACY,SAAZ,kBAGA,mBACU,OAAV,iBACY,IAAZ,MACY,SAAZ,sBAGA,CACQ,IAAR,OACA,EADA,CAEUmB,YAAauB,EAAK1F,YAEpB,OAAR,OAAQ,CAAR,GACA,iBACU,EAAV,UACU,EAAV,iBACU,EAAV,eACY,IAAZ,UACY,SAAZ,WACc,EAAd,uBAIA,mBAEU,OADA,EAAV,UACA,iBAAY,IAAZ,aAKI,aAxLJ,8GAyLA,OACA,eACA,UA3LA,0DA4LA,qCA5LA,yCA4LA,EA5LA,KA4LA,EA5LA,KA6LA,EA7LA,2CA6LA,GA7LA,QA8LA,+BACA,uCA/LA,mDCvIiW,I,wBCQ7V4G,EAAY,eACd,EACAvH,EACAmC,GACA,EACA,KACA,WACA,MAIa,aAAAoF,E,2CCnBf,yBAAyf,EAAG", "file": "h5/js/chunk-292cbe7d.fff76480.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{ref:\"box\",class:[_vm.posterImageStatus ? 'noscroll product-con' : 'product-con']},[_c('product-con-swiper',{attrs:{\"img-urls\":_vm.storeInfo.slider_image}}),_c('div',{staticClass:\"wrapper\"},[_c('div',{staticClass:\"share acea-row row-between row-bottom\"},[_c('div',{staticClass:\"money font-color-red\"},[_vm._v(\" 积分: \"),_c('span',{staticClass:\"num\"},[_vm._v(_vm._s(_vm.storeInfo.price))])])]),_c('div',{staticClass:\"introduce\"},[_vm._v(_vm._s(_vm.storeInfo.store_name))]),_c('div',{staticClass:\"introduce\",staticStyle:{\"font-size\":\"12px\",\"color\":\"#e93323\"}},[_vm._v(_vm._s(_vm.storeInfo.promotion_desc))])]),_c('div',{staticClass:\"attribute acea-row row-between-wrapper\",on:{\"click\":_vm.selecAttrTap}},[_c('div',[_vm._v(\" \"+_vm._s(_vm.attrTxt)+\"： \"),_c('span',{staticClass:\"atterTxt\"},[_vm._v(_vm._s(_vm.attrValue))])]),_c('div',{staticClass:\"iconfont icon-jiantou\"})]),_c('div',{staticClass:\"product-intro\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"产品介绍\")]),_c('div',{staticClass:\"conter\",domProps:{\"innerHTML\":_vm._s(_vm.storeInfo.description)}})]),_c('div',{staticStyle:{\"height\":\"1.2rem\"}}),_c('div',{staticClass:\"footer acea-row row-between-wrapper\"},[_c('router-link',{staticClass:\"item animated\",class:_vm.animated === true ? 'bounceIn' : '',attrs:{\"to\":'/cart'}},[_c('div',{staticClass:\"iconfont icon-gouwuche1\"},[(_vm.CartCount > 0)?_c('span',{staticClass:\"num bg-color-red\"},[_vm._v(_vm._s(_vm.CartCount))]):_vm._e()]),_c('div',[_vm._v(\"购物车\")])]),_c('div',{staticClass:\"acea-row\",staticStyle:{\"width\":\"auto\"}},[_c('div',{staticClass:\"joinCart\",on:{\"click\":_vm.joinCart}},[_vm._v(\"加入购物车\")]),_c('div',{staticClass:\"buyCart\",on:{\"click\":_vm.buyCart}},[_vm._v(\"一键下单\")])])],1),_c('ProductWindow',{attrs:{\"attr\":_vm.attr},on:{\"changeFun\":_vm.changeFun}}),_c('StorePoster',{attrs:{\"posterImageStatus\":_vm.posterImageStatus,\"posterData\":_vm.posterData},on:{\"setPosterImageStatus\":_vm.setPosterImageStatus}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"[posterImageStatus ? 'noscroll product-con' : 'product-con']\" ref=\"box\">\n    <product-con-swiper :img-urls=\"storeInfo.slider_image\"></product-con-swiper>\n    <div class=\"wrapper\">\n      <div class=\"share acea-row row-between row-bottom\">\n        <div class=\"money font-color-red\">\n          积分:\n          <span class=\"num\">{{ storeInfo.price }}</span>\n        </div>\n      </div>\n      <div class=\"introduce\">{{ storeInfo.store_name }}</div>\n      <div class=\"introduce\" style=\"font-size: 12px;color: #e93323;\">{{ storeInfo.promotion_desc }}</div>\n    </div>\n    <div class=\"attribute acea-row row-between-wrapper\" @click=\"selecAttrTap\">\n      <div>\n        {{ attrTxt }}：\n        <span class=\"atterTxt\">{{ attrValue }}</span>\n      </div>\n      <div class=\"iconfont icon-jiantou\"></div>\n    </div>\n    <div class=\"product-intro\">\n      <div class=\"title\">产品介绍</div>\n      <div class=\"conter\" v-html=\"storeInfo.description\"></div>\n    </div>\n    <div style=\"height:1.2rem;\"></div>\n    <div class=\"footer acea-row row-between-wrapper\">\n      <router-link :to=\"'/cart'\" class=\"item animated\" :class=\"animated === true ? 'bounceIn' : ''\">\n        <div class=\"iconfont icon-gouwuche1\">\n          <span class=\"num bg-color-red\" v-if=\"CartCount > 0\">{{ CartCount }}</span>\n        </div>\n        <div>购物车</div>\n      </router-link>\n      <div class=\"acea-row\" style=\"width: auto;\">\n        <div class=\"joinCart\" @click=\"joinCart\">加入购物车</div>\n        <div class=\"buyCart\" @click=\"buyCart\">一键下单</div>\n      </div>\n    </div>\n    <ProductWindow v-on:changeFun=\"changeFun\" :attr=\"attr\" />\n    <StorePoster\n      v-on:setPosterImageStatus=\"setPosterImageStatus\"\n      :posterImageStatus=\"posterImageStatus\"\n      :posterData=\"posterData\"\n    />\n  </div>\n</template>\n<script>\nimport ProductConSwiper from \"@components/ProductConSwiper\";\nimport ProductWindow from \"@components/ProductWindow\";\nimport StorePoster from \"@components/StorePoster\";\nimport { postCartAdd, changeCartNum } from \"@api/store\";\nimport { mapGetters } from \"vuex\";\nimport { convertProduct } from \"@utils/convert\";\n\nlet NAME = \"GoodsCon\";\nexport default {\n  name: NAME,\n  components: {\n    ProductConSwiper,\n    ProductWindow,\n    StorePoster\n  },\n  data: function() {\n    return {\n      shareInfoStatus: false,\n      weixinStatus: false,\n      mapShow: false,\n      mapKey: \"\",\n      posterData: {\n        image: \"\",\n        title: \"\",\n        price: \"\",\n        code: \"\"\n      },\n      posterImageStatus: false,\n      animated: false,\n      coupon: {\n        coupon: false,\n        list: []\n      },\n      attr: {\n        cartAttr: false,\n        productAttr: [],\n        productSelect: {}\n      },\n      isOpen: false, //是否打开属性组件\n      productValue: [],\n      id: 0,\n      storeInfo: {},\n      couponList: [],\n      attrTxt: \"请选择\",\n      attrValue: \"\",\n      cart_num: 1, //购买数量\n      replyCount: \"\",\n      replyChance: \"\",\n      reply: [],\n      priceName: 0,\n      CartCount: 0,\n      CartList: 0,\n      posters: false,\n      banner: [{}, {}],\n      swiperRecommend: {\n        pagination: {\n          el: \".swiper-pagination\",\n          clickable: true\n        },\n        autoplay: false,\n        loop: false,\n        speed: 1000,\n        observer: true,\n        observeParents: true\n      },\n      goodList: [],\n      system_store: {},\n      productInfo: {},\n      qqmapsdk: null\n    };\n  },\n  computed: {\n    ...mapGetters([\"isLogin\"])\n  },\n  watch: {\n    $route(n) {\n      if (n.name === NAME) {\n        this.id = n.params.id;\n        this.productCon();\n      }\n    }\n  },\n  updated() {\n    window.scroll(0, 0);\n  },\n  mounted: function() {\n    this.id = this.$route.params.id;\n    this.productCon();\n  },\n  methods: {\n    updateTitle() {\n      document.title = this.storeInfo.store_name || this.$route.meta.title;\n    },\n    setPosterImageStatus: function() {\n      var sTop = document.body || document.documentElement;\n      sTop.scrollTop = 0;\n      this.posterImageStatus = !this.posterImageStatus;\n      this.posters = false;\n    },\n    //产品详情接口；\n    async productCon() {\n      const list = this.$store.state.product.list;\n      const data = list.filter(x => x.id == this.id)[0];\n      const item = convertProduct(data);\n      this.productInfo = data;\n      this.storeInfo = item;\n\n      this.posterData.image = this.storeInfo.image_base;\n      if (this.storeInfo.store_name.length > 30) {\n        this.posterData.title =\n          this.storeInfo.store_name.substring(0, 30) + \"...\";\n      } else {\n        this.posterData.title = this.storeInfo.store_name;\n      }\n      this.posterData.price = this.storeInfo.price;\n      this.posterData.code = this.storeInfo.code_base;\n\n      const attr_values = item.inventorys.map(x => ({\n        ...x,\n        attr: x.materialSkuPropColor + \"-\" + x.materialSkuPropSize,\n        unit: item.unit\n      }));\n      // 提供默认商品颜色和尺寸\n      attr_values[0].on = true;\n      this.attr.productAttr = [\n        { attr_name: \"颜色尺码\", attr_values: attr_values }\n      ];\n      this.attr.productSelect = {\n        id: item.id,\n        image: item.image,\n        unit: item.unit,\n        price: item.price,\n        pointType: item.pointType,\n        pointName: item.pointName,\n        cart_num: 1, // TODO: 需要读取购物车内已经存在的数量\n        store_name: item.store_name,\n        sku: attr_values[0].materialSkuCode,\n        stock: attr_values[0].stockQty,\n        color: attr_values[0].materialSkuPropColor,\n        size: attr_values[0].materialSkuPropSize,\n        warehouseId: attr_values[0].warehouseId,\n        uniqueId: attr_values[0].id + attr_values[0].materialSkuCode,\n        materialId: attr_values[0].id,\n        overLimit: item.overLimit,\n        showStockQty: item.showStockQty,\n        virtualStockQty: attr_values[0].virtualStockQty,\n        limitQtyPerOrder: attr_values[0].limitQtyPerOrder,\n        limitQtyPerYear: attr_values[0].limitQtyPerYear\n      };\n\n      this.updateTitle();\n      this.getCartCount();\n    },\n    changeFun: function({ action, value }) {\n      if (action == \"changeattr\") {\n        this.attr.cartAttr = value;\n      } else if (action == \"ChangeCartNum\") {\n        const isLastOne = !value && this.attr.productSelect.cart_num - 1 < 1;\n        if (isLastOne) return;\n        this.attr.productSelect = {\n          ...this.attr.productSelect,\n          cart_num: value\n            ? this.attr.productSelect.cart_num + 1\n            : this.attr.productSelect.cart_num - 1\n        };\n      } else if (action == \"ChangeProduct\") {\n        // 选中商品\n        this.attr = {\n          ...this.attr,\n          productAttr: value.productAttr,\n          productSelect: {\n            ...this.attr.productSelect,\n            ...value.productSelect\n          }\n        };\n      }\n    },\n    //打开属性插件；\n    selecAttrTap: function() {\n      this.attr.cartAttr = true;\n      this.isOpen = true;\n    },\n    buyCart: function() {\n      const that = this;\n      if (that.attr.cartAttr && that.isOpen) {\n        that.isOpen = false;\n        that.attr.cartAttr = false;\n        let productSelect = that.attr.productSelect;\n        let q = {\n          ...productSelect,\n          productInfo: that.storeInfo\n        };\n        const countmoney = productSelect.cart_num * productSelect.price\n        that.$store.commit(\"UPDATE_ORDER_APPLICATION\", {\n          applicationType: productSelect.pointType,\n          applicationName: productSelect.pointName,\n        });\n        that.$store.commit(\"SAVE_CART_PREORDER\", [q]);\n        that.$store.commit(\"SAVE_COUNTMONEY_PREORDER\", countmoney);\n        that.$router.push({ path: \"/order/submit/\" + [productSelect.id] });\n      } else {\n        that.attr.cartAttr = true;\n        that.isOpen = true;\n      }\n    },\n    joinCart: function() {\n      let that = this;\n      if (that.attr.cartAttr === true && that.isOpen === false) {\n        return (that.isOpen = true);\n      }\n      if (that.attr.cartAttr === false) {\n        return (that.attr.cartAttr = true);\n      }\n\n      let productSelect = that.attr.productSelect;\n      if (productSelect === undefined && that.isOpen === true) {\n        return that.$dialog.toast({ mes: \"未选择产品\" });\n      }\n      // 加入购物车前先把数量加进去\n      const hasSameProductionInCart = that.CartList.find(\n        item => item.uniqueId === productSelect.uniqueId\n      );\n\n      if (!hasSameProductionInCart) {\n        that.CartCount++;\n      }\n\n      that.animated = true;\n      setTimeout(function() {\n        that.animated = false;\n      }, 500);\n\n      if (hasSameProductionInCart) {\n        changeCartNum({\n          uniqueId: productSelect.uniqueId,\n          number: productSelect.cart_num\n        })\n          .then(function(res) {\n            console.log(res);\n            that.$dialog.toast({\n              mes: \"已添加购物车\",\n              callback: () => {}\n            });\n          })\n          .catch(res => {\n            return that.$dialog.toast({\n              mes: res.msg,\n              callback: () => {}\n            });\n          });\n      } else {\n        let q = {\n          ...productSelect,\n          productInfo: that.storeInfo\n        };\n        postCartAdd(q)\n          .then(function() {\n            that.isOpen = false;\n            that.attr.cartAttr = false;\n            that.$dialog.toast({\n              mes: \"添加购物车成功\",\n              callback: () => {\n                that.getCartCount(true);\n              }\n            });\n          })\n          .catch(res => {\n            that.isOpen = false;\n            return that.$dialog.toast({ mes: res.msg });\n          });\n      }\n    },\n    //获取购物车数量\n    async getCartCount() {\n      let that = this;\n      that.CartList = [];\n      if (that.isLogin) {\n        const [status, res] = await this.$store.dispatch(\"getCartList\");\n        if (!status) return false;\n        that.CartList = res.result.resultList;\n        that.CartCount = res.result.resultList.length;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.geoPage {\n  position: fixed;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  z-index: 10000;\n}\n.product-con .store-info {\n  margin-top: 0.2rem;\n  background-color: #fff;\n}\n.product-con .store-info .title {\n  padding: 0 0.3rem;\n  font-size: 0.28rem;\n  color: #282828;\n  height: 0.8rem;\n  line-height: 0.8rem;\n  border-bottom: 0.01rem solid #f5f5f5;\n}\n.product-con .store-info .info {\n  padding: 0 0.3rem;\n  height: 1.26rem;\n}\n.product-con .store-info .info .picTxt {\n  width: 6.15rem;\n}\n.product-con .store-info .info .picTxt .pictrue {\n  width: 0.76rem;\n  height: 0.76rem;\n}\n.product-con .store-info .info .picTxt .pictrue img {\n  width: 100%;\n  height: 100%;\n  border-radius: 0.06rem;\n}\n.product-con .store-info .info .picTxt .text {\n  width: 5.22rem;\n}\n.product-con .store-info .info .picTxt .text .name {\n  font-size: 0.3rem;\n  color: #282828;\n}\n.product-con .store-info .info .picTxt .text .address {\n  font-size: 0.24rem;\n  color: #666;\n  margin-top: 0.03rem;\n}\n.product-con .store-info .info .picTxt .text .address .iconfont {\n  color: #707070;\n  font-size: 0.18rem;\n  margin-left: 0.1rem;\n}\n.product-con .store-info .info .picTxt .text .address .addressTxt {\n  max-width: 4.8rem;\n  width: auto;\n}\n.product-con .store-info .info .iconfont {\n  font-size: 0.4rem;\n}\n.product-con .superior {\n  background-color: #fff;\n  margin-top: 0.2rem;\n}\n.product-con .superior .title {\n  height: 0.98rem;\n}\n.product-con .superior .title img {\n  width: 0.3rem;\n  height: 0.3rem;\n}\n.product-con .superior .title .titleTxt {\n  margin: 0 0.2rem;\n  font-size: 0.3rem;\n  background-image: linear-gradient(to right, #f57a37 0%, #f21b07 100%);\n  background-image: -webkit-linear-gradient(to right, #f57a37 0%, #f21b07 100%);\n  background-image: -moz-linear-gradient(to right, #f57a37 0%, #f21b07 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n.product-con .superior .slider-banner {\n  width: 6.9rem;\n  margin: 0 auto;\n  padding-bottom: 0.2rem;\n}\n.product-con .superior .slider-banner .list {\n  width: 100%;\n  padding-bottom: 0.2rem;\n}\n.product-con .superior .slider-banner .list .item {\n  width: 2.15rem;\n  margin: 0 0.22rem 0.3rem 0;\n  font-size: 0.26rem;\n}\n.product-con .superior .slider-banner .list .item:nth-of-type(3n) {\n  margin-right: 0;\n}\n.product-con .superior .slider-banner .list .item .pictrue {\n  width: 100%;\n  height: 2.15rem;\n}\n.product-con .superior .slider-banner .list .item .pictrue img {\n  width: 100%;\n  height: 100%;\n  border-radius: 0.06rem;\n}\n.product-con .superior .slider-banner .list .item .name {\n  color: #282828;\n  margin-top: 0.12rem;\n}\n.product-con .superior .slider-banner .swiper-pagination-bullet {\n  background-color: #999;\n}\n.product-con .superior .slider-banner .swiper-pagination-bullet-active {\n  background-color: #e93323;\n}\n\n.mask {\n  -webkit-filter: blur(2px);\n  -moz-filter: blur(2px);\n  -ms-filter: blur(2px);\n  filter: blur(2px);\n}\n.footer .icon-shoucang1 {\n  color: #e93323;\n}\n.product-con .product-intro .conter div {\n  width: 100% !important;\n}\n.generate-posters {\n  width: 100%;\n  height: 1.7rem;\n  background-color: #fff;\n  position: fixed;\n  left: 0;\n  bottom: 0;\n  z-index: 99;\n  transform: translate3d(0, 100%, 0);\n  -webkit-transform: translate3d(0, 100%, 0);\n  -ms-transform: translate3d(0, 100%, 0);\n  -moz-transform: translate3d(0, 100%, 0);\n  -o-transform: translate3d(0, 100%, 0);\n  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\n  -webkit-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\n  -moz-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\n  -o-transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\n}\n.generate-posters.on {\n  transform: translate3d(0, 0, 0);\n  -webkit-transform: translate3d(0, 0, 0);\n  -ms-transform: translate3d(0, 0, 0);\n  -moz-transform: translate3d(0, 0, 0);\n  -o-transform: translate3d(0, 0, 0);\n}\n.generate-posters .item {\n  flex: 50%;\n  -webkit-flex: 50%;\n  -ms-flex: 50%;\n  text-align: center;\n}\n.generate-posters .item .iconfont {\n  font-size: 0.8rem;\n  color: #5eae72;\n}\n.generate-posters .item .iconfont.icon-haibao {\n  color: #5391f1;\n}\n.noscroll {\n  height: 100%;\n  overflow: hidden;\n}\n.joinCart {\n  border-radius: 0.5rem 0 0 0.5rem;\n  background-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);\n  background-image: -webkit-linear-gradient(to right, #fea10f 0%, #fa8013 100%);\n  background-image: -moz-linear-gradient(to right, #fea10f 0%, #fa8013 100%);\n  color: white;\n  padding: 0.15rem 0.5rem 0.15rem 0.5rem;\n  background-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);\n  text-align: center;\n}\n.buyCart {\n  border-radius: 0 0.5rem 0.5rem 0;\n  background-image: linear-gradient(to right, #fa6514 0%, #e93323 100%);\n  background-image: -webkit-linear-gradient(to right, #fa6514 0%, #e93323 100%);\n  background-image: -moz-linear-gradient(to right, #fa6514 0%, #e93323 100%);\n  color: white;\n  padding: 0.15rem 0.5rem 0.15rem 0.5rem;\n  background-image: linear-gradient(to right, #fa6514 0%, #e93323 100%);\n  text-align: center;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GoodsCon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GoodsCon.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./GoodsCon.vue?vue&type=template&id=307cafbc&scoped=true&\"\nimport script from \"./GoodsCon.vue?vue&type=script&lang=js&\"\nexport * from \"./GoodsCon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./GoodsCon.vue?vue&type=style&index=0&id=307cafbc&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"307cafbc\",\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GoodsCon.vue?vue&type=style&index=0&id=307cafbc&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GoodsCon.vue?vue&type=style&index=0&id=307cafbc&scoped=true&lang=css&\""], "sourceRoot": ""}