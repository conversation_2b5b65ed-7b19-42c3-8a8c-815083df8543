!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=11),domSupported:"undefined"!=typeof document}}function i(t,e){"createCanvas"===t&&(Qc=null),$c[t]=e}function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=Gc.call(t);if("[object Array]"===i){if(!E(t)){e=[];for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}else if(Vc[i]){if(!E(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}}else if(!Wc[i]&&!E(t)&&!T(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function r(t,e,i){if(!b(e)||!b(t))return i?n(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!b(s)||!b(o)||_(s)||_(o)||T(s)||T(o)||S(s)||S(o)||E(s)||E(o)?!i&&a in t||(t[a]=n(e[a],!0)):r(o,s,i)}return t}function a(t,e){for(var i=t[0],n=1,a=t.length;a>n;n++)i=r(i,t[n],e);return i}function o(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function s(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function l(){return Qc||(Qc=Kc().getContext("2d")),Qc}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function u(t,e){function i(){}var n=t.prototype;i.prototype=e.prototype,t.prototype=new i;for(var r in n)t.prototype[r]=n[r];t.prototype.constructor=t,t.superClass=e}function c(t,e,i){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,i)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,i){if(t&&e)if(t.forEach&&t.forEach===Yc)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function p(t,e,i){if(t&&e){if(t.map&&t.map===jc)return t.map(e,i);for(var n=[],r=0,a=t.length;a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function g(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===Zc)return t.reduce(e,i,n);for(var r=0,a=t.length;a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function v(t,e,i){if(t&&e){if(t.filter&&t.filter===qc)return t.filter(e,i);for(var n=[],r=0,a=t.length;a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e){var i=Uc.call(arguments,2);return function(){return t.apply(e,i.concat(Uc.call(arguments)))}}function y(t){var e=Uc.call(arguments,1);return function(){return t.apply(this,e.concat(Uc.call(arguments)))}}function _(t){return"[object Array]"===Gc.call(t)}function x(t){return"function"==typeof t}function w(t){return"[object String]"===Gc.call(t)}function b(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function S(t){return!!Wc[Gc.call(t)]}function M(t){return!!Vc[Gc.call(t)]}function T(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function C(t){return t!==t}function I(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function D(t,e){return null!=t?t:e}function k(t,e,i){return null!=t?t:null!=e?e:i}function A(){return Function.call.apply(Uc,arguments)}function P(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function L(t,e){if(!t)throw new Error(e)}function O(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function B(t){t[Jc]=!0}function E(t){return t[Jc]}function z(t){function e(t,e){i?n.set(t,e):n.set(e,t)}var i=_(t);this.data={};var n=this;t instanceof z?t.each(e):t&&f(t,e)}function R(t){return new z(t)}function F(){}function N(t,e){var i=new td(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function H(t){var e=new td(2);return e[0]=t[0],e[1]=t[1],e}function W(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function V(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function G(t){return Math.sqrt(X(t))}function X(t){return t[0]*t[0]+t[1]*t[1]}function Y(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function q(t,e){var i=G(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function U(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function j(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function Z(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function $(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function K(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}function Q(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function J(t,e){return{target:t,topTarget:e&&e.topTarget}}function te(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}function ee(t,e,i,n,r,a){var o=t._$handlers;if("function"==typeof i&&(r=n,n=i,i=null),!n||!e)return t;i=te(t,i),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===n)return t;var l={h:n,one:a,query:i,ctx:r||t,callAtLast:n.zrEventfulCallAtLast},h=o[e].length-1,u=o[e][h];return u&&u.callAtLast?o[e].splice(h,0,l):o[e].push(l),t}function ie(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function ne(t,e,i,n){return i=i||{},n||!Hc.canvasSupported?re(t,e,i):Hc.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):re(t,e,i),i}function re(t,e,i){var n=ie(t);i.zrX=e.clientX-n.left,i.zrY=e.clientY-n.top}function ae(t,e,i){if(e=e||window.event,null!=e.zrX)return e;var n=e.type,r=n&&n.indexOf("touch")>=0;if(r){var a="touchend"!==n?e.targetTouches[0]:e.changedTouches[0];a&&ne(t,a,e,i)}else ne(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&od.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function oe(t,e,i){ad?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function se(t,e,i){ad?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}function le(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function he(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function ue(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:ce}}function ce(){sd(this.event)}function de(){}function fe(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return n?ud:!0}return!1}function pe(){var t=new fd(6);return ge(t),t}function ge(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ve(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function me(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function ye(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function _e(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(i),u=Math.cos(i);return t[0]=n*u+o*h,t[1]=-n*h+o*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t}function xe(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function we(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}function be(t){return t>gd||-gd>t}function Se(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Me(t){return t=Math.round(t),0>t?0:t>255?255:t}function Te(t){return 0>t?0:t>1?1:t}function Ce(t){return Me(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function Ie(t){return Te(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function De(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function ke(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function Ae(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Pe(t,e){kd&&Ae(kd,e),kd=Dd.put(t,kd||e.slice())}function Le(t,e){if(t){e=e||[];var i=Dd.get(t);if(i)return Ae(e,i);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in Id)return Ae(e,Id[n]),Pe(t,e),e;if("#"!==n.charAt(0)){var r=n.indexOf("("),a=n.indexOf(")");if(-1!==r&&a+1===n.length){var o=n.substr(0,r),s=n.substr(r+1,a-(r+1)).split(","),l=1;switch(o){case"rgba":if(4!==s.length)return void ke(e,0,0,0,1);l=Ie(s.pop());case"rgb":return 3!==s.length?void ke(e,0,0,0,1):(ke(e,Ce(s[0]),Ce(s[1]),Ce(s[2]),l),Pe(t,e),e);case"hsla":return 4!==s.length?void ke(e,0,0,0,1):(s[3]=Ie(s[3]),Oe(s,e),Pe(t,e),e);case"hsl":return 3!==s.length?void ke(e,0,0,0,1):(Oe(s,e),Pe(t,e),e);default:return}}ke(e,0,0,0,1)}else{if(4===n.length){var h=parseInt(n.substr(1),16);return h>=0&&4095>=h?(ke(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),Pe(t,e),e):void ke(e,0,0,0,1)}if(7===n.length){var h=parseInt(n.substr(1),16);return h>=0&&16777215>=h?(ke(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),Pe(t,e),e):void ke(e,0,0,0,1)}}}}function Oe(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=Ie(t[1]),r=Ie(t[2]),a=.5>=r?r*(n+1):r+n-r*n,o=2*r-a;return e=e||[],ke(e,Me(255*De(o,a,i+1/3)),Me(255*De(o,a,i)),Me(255*De(o,a,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Be(t,e){var i=Le(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0,i[n]>255?i[n]=255:t[n]<0&&(i[n]=0);return ze(i,4===i.length?"rgba":"rgb")}}function Ee(t){var e=Le(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function ze(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}}function Re(t,e){return t[e]}function Fe(t,e,i){t[e]=i}function Ne(t,e,i){return(e-t)*i+t}function He(t,e,i){return i>.5?e:t}function We(t,e,i,n,r){var a=t.length;if(1===r)for(var o=0;a>o;o++)n[o]=Ne(t[o],e[o],i);else for(var s=a&&t[0].length,o=0;a>o;o++)for(var l=0;s>l;l++)n[o][l]=Ne(t[o][l],e[o][l],i)}function Ve(t,e,i){var n=t.length,r=e.length;if(n!==r){var a=n>r;if(a)t.length=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:Ad.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function Ge(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function Xe(t,e,i,n,r,a,o,s,l){var h=t.length;if(1===l)for(var u=0;h>u;u++)s[u]=Ye(t[u],e[u],i[u],n[u],r,a,o);else for(var c=t[0].length,u=0;h>u;u++)for(var d=0;c>d;d++)s[u][d]=Ye(t[u][d],e[u][d],i[u][d],n[u][d],r,a,o)}function Ye(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function qe(t){if(d(t)){var e=t.length;if(d(t[0])){for(var i=[],n=0;e>n;n++)i.push(Ad.call(t[n]));return i}return Ad.call(t)}return t}function Ue(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function je(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function Ze(t,e,i,n,r,a){var o=t._getter,s=t._setter,l="spline"===e,h=n.length;if(h){var u,c=n[0].value,f=d(c),p=!1,g=!1,v=f?je(n):0;n.sort(function(t,e){return t.time-e.time}),u=n[h-1].time;for(var m=[],y=[],_=n[0].value,x=!0,w=0;h>w;w++){m.push(n[w].time/u);var b=n[w].value;if(f&&Ge(b,_,v)||!f&&b===_||(x=!1),_=b,"string"==typeof b){var S=Le(b);S?(b=S,p=!0):g=!0}y.push(b)}if(a||!x){for(var M=y[h-1],w=0;h-1>w;w++)f?Ve(y[w],M,v):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&Ve(o(t._target,r),M,v);var T,C,I,D,k,A,P=0,L=0;if(p)var O=[0,0,0,0];var B=function(t,e){var i;if(0>e)i=0;else if(L>e){for(T=Math.min(P+1,h-1),i=T;i>=0&&!(m[i]<=e);i--);i=Math.min(i,h-2)}else{for(i=P;h>i&&!(m[i]>e);i++);i=Math.min(i-1,h-2)}P=i,L=e;var n=m[i+1]-m[i];if(0!==n)if(C=(e-m[i])/n,l)if(D=y[i],I=y[0===i?i:i-1],k=y[i>h-2?h-1:i+1],A=y[i>h-3?h-1:i+2],f)Xe(I,D,k,A,C,C*C,C*C*C,o(t,r),v);else{var a;if(p)a=Xe(I,D,k,A,C,C*C,C*C*C,O,1),a=Ue(O);else{if(g)return He(D,k,C);a=Ye(I,D,k,A,C,C*C,C*C*C)}s(t,r,a)}else if(f)We(y[i],y[i+1],C,o(t,r),v);else{var a;if(p)We(y[i],y[i+1],C,O,1),a=Ue(O);else{if(g)return He(y[i],y[i+1],C);a=Ne(y[i],y[i+1],C)}s(t,r,a)}},E=new Se({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:B,ondestroy:i});return e&&"spline"!==e&&(E.easing=e),E}}}function $e(t,e,i,n,r,a,o,s){function l(){u--,u||a&&a()}w(n)?(a=r,r=n,n=0):x(r)?(a=r,r="linear",n=0):x(n)?(a=n,n=0):x(i)?(a=i,i=500):i||(i=500),t.stopAnimation(),Ke(t,"",t,e,i,n,s);var h=t.animators.slice(),u=h.length;u||a&&a();for(var c=0;c<h.length;c++)h[c].done(l).start(r,o)}function Ke(t,e,i,n,r,a,o){var s={},l=0;for(var h in n)n.hasOwnProperty(h)&&(null!=i[h]?b(n[h])&&!d(n[h])?Ke(t,e?e+"."+h:h,i[h],n[h],r,a,o):(o?(s[h]=i[h],Qe(t,e,h,n[h])):s[h]=n[h],l++):null==n[h]||o||Qe(t,e,h,n[h]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function Qe(t,e,i,n){if(e){var r={};r[e]={},r[e][i]=n,t.attr(r)}else t.attr(i,n)}function Je(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}function ti(t){for(var e=0;t>=Gd;)e|=1&t,t>>=1;return t+e}function ei(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;i>r&&n(t[r],t[r-1])<0;)r++;ii(t,e,r)}else for(;i>r&&n(t[r],t[r-1])>=0;)r++;return r-e}function ii(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function ni(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var h=n-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=o}}function ri(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])>0?o=u+1:l=u}return l}function ai(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])<0?l=u:o=u+1}return l}function oi(t,e){function i(t,e){l[c]=t,h[c]=e,c+=1}function n(){for(;c>1;){var t=c-2;if(t>=1&&h[t-1]<=h[t]+h[t+1]||t>=2&&h[t-2]<=h[t]+h[t-1])h[t-1]<h[t+1]&&t--;else if(h[t]>h[t+1])break;a(t)}}function r(){for(;c>1;){var t=c-2;t>0&&h[t-1]<h[t+1]&&t--,a(t)}}function a(i){var n=l[i],r=h[i],a=l[i+1],u=h[i+1];h[i]=r+u,i===c-3&&(l[i+1]=l[i+2],h[i+1]=h[i+2]),c--;var d=ai(t[a],t,n,r,0,e);n+=d,r-=d,0!==r&&(u=ri(t[n+r-1],t,a,u,u-1,e),0!==u&&(u>=r?o(n,r,a,u):s(n,r,a,u)))}function o(i,n,r,a){var o=0;for(o=0;n>o;o++)d[o]=t[i+o];var s=0,l=r,h=i;if(t[h++]=t[l++],0!==--a){if(1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];return void(t[h+a]=d[s])}for(var c,f,p,g=u;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[h++]=t[l++],f++,c=0,0===--a){p=!0;break}}else if(t[h++]=d[s++],c++,f=0,1===--n){p=!0;break}while(g>(c|f));if(p)break;do{if(c=ai(t[l],d,s,n,0,e),0!==c){for(o=0;c>o;o++)t[h+o]=d[s+o];if(h+=c,s+=c,n-=c,1>=n){p=!0;break}}if(t[h++]=t[l++],0===--a){p=!0;break}if(f=ri(d[s],t,l,a,0,e),0!==f){for(o=0;f>o;o++)t[h+o]=t[l+o];if(h+=f,l+=f,a-=f,0===a){p=!0;break}}if(t[h++]=d[s++],1===--n){p=!0;break}g--}while(c>=Xd||f>=Xd);if(p)break;0>g&&(g=0),g+=2}if(u=g,1>u&&(u=1),1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];t[h+a]=d[s]}else{if(0===n)throw new Error;for(o=0;n>o;o++)t[h+o]=d[s+o]}}else for(o=0;n>o;o++)t[h+o]=d[s+o]}function s(i,n,r,a){var o=0;for(o=0;a>o;o++)d[o]=t[r+o];var s=i+n-1,l=a-1,h=r+a-1,c=0,f=0;if(t[h--]=t[s--],0!==--n){if(1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];return void(t[h]=d[l])}for(var p=u;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[h--]=t[s--],g++,v=0,0===--n){m=!0;break}}else if(t[h--]=d[l--],v++,g=0,1===--a){m=!0;break}while(p>(g|v));if(m)break;do{if(g=n-ai(d[l],t,i,n,n-1,e),0!==g){for(h-=g,s-=g,n-=g,f=h+1,c=s+1,o=g-1;o>=0;o--)t[f+o]=t[c+o];if(0===n){m=!0;break}}if(t[h--]=d[l--],1===--a){m=!0;break}if(v=a-ri(t[s],d,0,a,a-1,e),0!==v){for(h-=v,l-=v,a-=v,f=h+1,c=l+1,o=0;v>o;o++)t[f+o]=d[c+o];if(1>=a){m=!0;break}}if(t[h--]=t[s--],0===--n){m=!0;break}p--}while(g>=Xd||v>=Xd);if(m)break;0>p&&(p=0),p+=2}if(u=p,1>u&&(u=1),1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];t[h]=d[l]}else{if(0===a)throw new Error;for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}}else for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}var l,h,u=Xd,c=0,d=[];l=[],h=[],this.mergeRuns=n,this.forceMergeRuns=r,this.pushRun=i}function si(t,e,i,n){i||(i=0),n||(n=t.length);var r=n-i;if(!(2>r)){var a=0;if(Gd>r)return a=ei(t,i,n,e),void ni(t,i,n,i+a,e);var o=new oi(t,e),s=ti(r);do{if(a=ei(t,i,n,e),s>a){var l=r;l>s&&(l=s),ni(t,i,i+l,i+a,e),a=l}o.pushRun(i,a),o.mergeRuns(),r-=a,i+=a}while(0!==r);o.forceMergeRuns()}}function li(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function hi(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i.height+i.y,o=o*i.height+i.y),n=isNaN(n)?0:n,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(n,a,r,o);return s}function ui(t,e,i){var n=i.width,r=i.height,a=Math.min(n,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var h=t.createRadialGradient(o,s,0,o,s,l);return h}function ci(){return!1}function di(t,e,i){var n=Kc(),r=e.getWidth(),a=e.getHeight(),o=n.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=r+"px",o.height=a+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=a*i,n}function fi(t){if("string"==typeof t){var e=af.get(t);return e&&e.image}return t}function pi(t,e,i,n,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!i)return e;var a=af.get(t),o={hostEl:i,cb:n,cbPayload:r};return a?(e=a.image,!vi(e)&&a.pending.push(o)):(e=new Image,e.onload=e.onerror=gi,af.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return t}return e}function gi(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function vi(t){return t&&t.width&&t.height}function mi(t,e){e=e||uf;var i=t+":"+e;if(of[i])return of[i];for(var n=(t+"").split("\n"),r=0,a=0,o=n.length;o>a;a++)r=Math.max(ki(n[a],e).width,r);return sf>lf&&(sf=0,of={}),sf++,of[i]=r,r}function yi(t,e,i,n,r,a,o,s){return o?xi(t,e,i,n,r,a,o,s):_i(t,e,i,n,r,a,s)}function _i(t,e,i,n,r,a,o){var s=Ai(t,e,r,a,o),l=mi(t,e);r&&(l+=r[1]+r[3]);var h=s.outerHeight,u=wi(0,l,i),c=bi(0,h,n),d=new Je(u,c,l,h);return d.lineHeight=s.lineHeight,d}function xi(t,e,i,n,r,a,o,s){var l=Pi(t,{rich:o,truncate:s,font:e,textAlign:i,textPadding:r,textLineHeight:a}),h=l.outerWidth,u=l.outerHeight,c=wi(0,h,i),d=bi(0,u,n);return new Je(c,d,h,u)}function wi(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function bi(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function Si(t,e,i){var n=e.x,r=e.y,a=e.height,o=e.width,s=a/2,l="left",h="top";switch(t){case"left":n-=i,r+=s,l="right",h="middle";break;case"right":n+=i+o,r+=s,h="middle";break;case"top":n+=o/2,r-=i,l="center",h="bottom";break;case"bottom":n+=o/2,r+=a+i,l="center";break;case"inside":n+=o/2,r+=s,l="center",h="middle";break;case"insideLeft":n+=i,r+=s,h="middle";break;case"insideRight":n+=o-i,r+=s,l="right",h="middle";break;case"insideTop":n+=o/2,r+=i,l="center";break;case"insideBottom":n+=o/2,r+=a-i,l="center",h="bottom";break;case"insideTopLeft":n+=i,r+=i;break;case"insideTopRight":n+=o-i,r+=i,l="right";break;case"insideBottomLeft":n+=i,r+=a-i,h="bottom";break;case"insideBottomRight":n+=o-i,r+=a-i,l="right",h="bottom"}return{x:n,y:r,textAlign:l,textVerticalAlign:h}}function Mi(t,e,i,n,r){if(!e)return"";var a=(t+"").split("\n");r=Ti(e,i,n,r);for(var o=0,s=a.length;s>o;o++)a[o]=Ci(a[o],r);return a.join("\n")}function Ti(t,e,i,n){n=o({},n),n.font=e;var i=D(i,"...");n.maxIterations=D(n.maxIterations,2);var r=n.minChar=D(n.minChar,0);n.cnCharWidth=mi("国",e);var a=n.ascCharWidth=mi("a",e);n.placeholder=D(n.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var h=mi(i,e);return h>s&&(i="",h=0),s=t-h,n.ellipsis=i,n.ellipsisWidth=h,n.contentWidth=s,n.containerWidth=t,n}function Ci(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var a=mi(t,n);if(i>=a)return t;for(var o=0;;o++){if(r>=a||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?Ii(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=mi(t,n)}return""===t&&(t=e.placeholder),t}function Ii(t,e,i,n){for(var r=0,a=0,o=t.length;o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}function Di(t){return mi("国",t)}function ki(t,e){return cf.measureText(t,e)}function Ai(t,e,i,n,r){null!=t&&(t+="");var a=D(n,Di(e)),o=t?t.split("\n"):[],s=o.length*a,l=s;if(i&&(l+=i[0]+i[2]),t&&r){var h=r.outerHeight,u=r.outerWidth;if(null!=h&&l>h)t="",o=[];else if(null!=u)for(var c=Ti(u-(i?i[1]+i[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),d=0,f=o.length;f>d;d++)o[d]=Ci(o[d],c)}return{lines:o,height:s,outerHeight:l,lineHeight:a}}function Pi(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=hf.lastIndex=0;null!=(n=hf.exec(t));){var a=n.index;a>r&&Li(i,t.substring(r,a)),Li(i,n[2],n[1]),r=hf.lastIndex}r<t.length&&Li(i,t.substring(r,t.length));var o=i.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,m=0,y=0;y<g.tokens.length;y++){var _=g.tokens[y],x=_.styleName&&e.rich[_.styleName]||{},w=_.textPadding=x.textPadding,b=_.font=x.font||e.font,S=_.textHeight=D(x.textHeight,Di(b));if(w&&(S+=w[0]+w[2]),_.height=S,_.lineHeight=k(x.textLineHeight,e.textLineHeight,S),_.textAlign=x&&x.textAlign||e.textAlign,_.textVerticalAlign=x&&x.textVerticalAlign||"middle",null!=f&&s+_.lineHeight>f)return{lines:[],width:0,height:0};_.textWidth=mi(_.text,b);var M=x.textWidth,T=null==M||"auto"===M;if("string"==typeof M&&"%"===M.charAt(M.length-1))_.percentWidth=M,h.push(_),M=0;else{if(T){M=_.textWidth;var C=x.textBackgroundColor,I=C&&C.image;I&&(I=fi(I),vi(I)&&(M=Math.max(M,I.width*S/I.height)))}var A=w?w[1]+w[3]:0;M+=A;var P=null!=d?d-m:null;null!=P&&M>P&&(!T||A>P?(_.text="",_.textWidth=M=0):(_.text=Mi(_.text,P-A,b,c.ellipsis,{minChar:c.minChar}),_.textWidth=mi(_.text,b),M=_.textWidth+A))}m+=_.width=M,x&&(v=Math.max(v,_.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}i.outerWidth=i.width=D(e.textWidth,l),i.outerHeight=i.height=D(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(var p=0;p<h.length;p++){var _=h[p],L=_.percentWidth;_.width=parseInt(L,10)/100*l}return i}function Li(t,e,i){for(var n=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:i,text:s,isLineHolder:!s&&!n};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function Oi(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&O(e)||t.textFont||t.font}function Bi(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;0>l&&(o+=l,l=-l),0>h&&(s+=h,h=-h),"number"==typeof u?i=n=r=a=u:u instanceof Array?1===u.length?i=n=r=a=u[0]:2===u.length?(i=r=u[0],n=a=u[1]):3===u.length?(i=u[0],n=a=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],a=u[3]):i=n=r=a=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),n+r>h&&(c=n+r,n*=h/c,r*=h/c),i+a>h&&(c=i+a,i*=h/c,a*=h/c),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.arc(o+l-n,s+n,n,-Math.PI/2,0),t.lineTo(o+l,s+h-r),0!==r&&t.arc(o+l-r,s+h-r,r,0,Math.PI/2),t.lineTo(o+a,s+h),0!==a&&t.arc(o+a,s+h-a,a,Math.PI/2,Math.PI),t.lineTo(o,s+i),0!==i&&t.arc(o+i,s+i,i,Math.PI,1.5*Math.PI)}function Ei(t){return zi(t),f(t.rich,zi),t}function zi(t){if(t){t.font=Oi(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||ff[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||pf[i]?i:"top";var n=t.textPadding;n&&(t.textPadding=P(t.textPadding))}}function Ri(t,e,i,n,r,a){n.rich?Ni(t,e,i,n,r,a):Fi(t,e,i,n,r,a)}function Fi(t,e,i,n,r,a){var o,s=Gi(n),l=!1,h=e.__attrCachedBy===jd.PLAIN_TEXT;a!==Zd?(a&&(o=a.style,l=!s&&h&&o),e.__attrCachedBy=s?jd.NONE:jd.PLAIN_TEXT):h&&(e.__attrCachedBy=jd.NONE);var u=n.font||df;l&&u===(o.font||df)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var d=n.textPadding,f=n.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=Ai(i,c,d,f,n.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=qi(g,n,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;Wi(e,n,r,_,x);var S=bi(x,g,b),M=_,T=S;if(s||d){var C=mi(i,c),I=C;d&&(I+=d[1]+d[3]);var D=wi(_,I,w);s&&Xi(t,e,n,D,S,I,g),d&&(M=Ki(_,w,d),T+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=n.opacity||1;for(var k=0;k<gf.length;k++){var A=gf[k],P=A[0],L=A[1],O=n[P];l&&O===o[P]||(e[L]=Ud(e,L,O||A[2]))}T+=m/2;var B=n.textStrokeWidth,E=l?o.textStrokeWidth:null,z=!l||B!==E,R=!l||z||n.textStroke!==o.textStroke,F=ji(n.textStroke,B),N=Zi(n.textFill);if(F&&(z&&(e.lineWidth=B),R&&(e.strokeStyle=F)),N&&(l&&n.textFill===o.textFill||(e.fillStyle=N)),1===v.length)F&&e.strokeText(v[0],M,T),N&&e.fillText(v[0],M,T);else for(var k=0;k<v.length;k++)F&&e.strokeText(v[k],M,T),N&&e.fillText(v[k],M,T),T+=m}function Ni(t,e,i,n,r,a){a!==Zd&&(e.__attrCachedBy=jd.NONE);var o=t.__textCotentBlock;(!o||t.__dirtyText)&&(o=t.__textCotentBlock=Pi(i,n)),Hi(t,e,o,n,r)}function Hi(t,e,i,n,r){var a=i.width,o=i.outerWidth,s=i.outerHeight,l=n.textPadding,h=qi(s,n,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;Wi(e,n,r,u,c);var p=wi(u,o,d),g=bi(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+a;Gi(n)&&Xi(t,e,n,p,g,o,s);for(var _=0;_<i.lines.length;_++){for(var x,w=i.lines[_],b=w.tokens,S=b.length,M=w.lineHeight,T=w.width,C=0,I=v,D=y,k=S-1;S>C&&(x=b[C],!x.textAlign||"left"===x.textAlign);)Vi(t,e,x,n,M,m,I,"left"),T-=x.width,I+=x.width,C++;for(;k>=0&&(x=b[k],"right"===x.textAlign);)Vi(t,e,x,n,M,m,D,"right"),T-=x.width,D-=x.width,k--;for(I+=(a-(I-v)-(y-D)-T)/2;k>=C;)x=b[C],Vi(t,e,x,n,M,m,I+x.width/2,"center"),I+=x.width,C++;m+=M}}function Wi(t,e,i,n,r){if(i&&e.textRotation){var a=e.textOrigin;"center"===a?(n=i.width/2+i.x,r=i.height/2+i.y):a&&(n=a[0]+i.x,r=a[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function Vi(t,e,i,n,r,a,o,s){var l=n.rich[i.styleName]||{};l.text=i.text;var h=i.textVerticalAlign,u=a+r/2;"top"===h?u=a+i.height/2:"bottom"===h&&(u=a+r-i.height/2),!i.isLineHolder&&Gi(l)&&Xi(t,e,l,"right"===s?o-i.width:"center"===s?o-i.width/2:o,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(o=Ki(o,s,c),u-=i.height/2-c[2]-i.textHeight/2),Ui(e,"shadowBlur",k(l.textShadowBlur,n.textShadowBlur,0)),Ui(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),Ui(e,"shadowOffsetX",k(l.textShadowOffsetX,n.textShadowOffsetX,0)),Ui(e,"shadowOffsetY",k(l.textShadowOffsetY,n.textShadowOffsetY,0)),Ui(e,"textAlign",s),Ui(e,"textBaseline","middle"),Ui(e,"font",i.font||df);var d=ji(l.textStroke||n.textStroke,p),f=Zi(l.textFill||n.textFill),p=D(l.textStrokeWidth,n.textStrokeWidth);d&&(Ui(e,"lineWidth",p),Ui(e,"strokeStyle",d),e.strokeText(i.text,o,u)),f&&(Ui(e,"fillStyle",f),e.fillText(i.text,o,u))}function Gi(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function Xi(t,e,i,n,r,a,o){var s=i.textBackgroundColor,l=i.textBorderWidth,h=i.textBorderColor,u=w(s);if(Ui(e,"shadowBlur",i.textBoxShadowBlur||0),Ui(e,"shadowColor",i.textBoxShadowColor||"transparent"),Ui(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),Ui(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=i.textBorderRadius;c?Bi(e,{x:n,y:r,width:a,height:o,r:c}):e.rect(n,r,a,o),e.closePath()}if(u)if(Ui(e,"fillStyle",s),null!=i.fillOpacity){var d=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(b(s)){var f=s.image;f=pi(f,null,t,Yi,s),f&&vi(f)&&e.drawImage(f,n,r,a,o)}if(l&&h)if(Ui(e,"lineWidth",l),Ui(e,"strokeStyle",h),null!=i.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function Yi(t,e){e.image=t}function qi(t,e,i){var n=e.x||0,r=e.y||0,a=e.textAlign,o=e.textVerticalAlign;if(i){var s=e.textPosition;if(s instanceof Array)n=i.x+$i(s[0],i.width),r=i.y+$i(s[1],i.height);else{var l=Si(s,i,e.textDistance);n=l.x,r=l.y,a=a||l.textAlign,o=o||l.textVerticalAlign}var h=e.textOffset;h&&(n+=h[0],r+=h[1])}return{baseX:n,baseY:r,textAlign:a,textVerticalAlign:o}}function Ui(t,e,i){return t[e]=Ud(t,e,i),t[e]}function ji(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Zi(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function $i(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Ki(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function Qi(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Ji(t){t=t||{},Fd.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Kd(t.style,this),this._rect=null,this.__clipPaths=[]}function tn(t){Ji.call(this,t)}function en(t){return parseInt(t,10)}function nn(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function rn(t,e,i){return bf.copy(t.getBoundingRect()),t.transform&&bf.applyTransform(t.transform),Sf.width=e,Sf.height=i,!bf.intersect(Sf)}function an(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0}function on(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}function sn(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}function ln(t){return"mousewheel"===t&&Hc.browser.firefox?"DOMMouseScroll":t}function hn(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function un(t){var e=t.pointerType;return"pen"===e||"touch"===e}function cn(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}f(Df,function(e){t._handlers[e]=m(Pf[e],t)}),f(Af,function(e){t._handlers[e]=m(Pf[e],t)}),f(If,function(i){t._handlers[i]=e(Pf[i],t)})}function dn(t){function e(e,i){f(e,function(e){oe(t,ln(e),i._handlers[e])
},i)}rd.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._handlers={},cn(this),Hc.pointerEventsSupported?e(Af,this):(Hc.touchEventsSupported&&e(Df,this),e(If,this))}function fn(t,e){var i=new Ef(Fc(),t,e);return i}function pn(t){return t instanceof Array?t:null==t?[]:[t]}function gn(t,e,i){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var n=0,r=i.length;r>n;n++){var a=i[n];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}function vn(t){return!Rf(t)||Ff(t)||t instanceof Date?t:t.value}function mn(t){return Rf(t)&&!(t instanceof Array)}function yn(t,e){e=(e||[]).slice();var i=p(t||[],function(t){return{exist:t}});return zf(e,function(t,n){if(Rf(t)){for(var r=0;r<i.length;r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i.length;r++){var a=i[r].exist;if(!(i[r].option||null!=a.id&&null!=t.id||null==t.name||wn(t)||wn(a)||a.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),zf(e,function(t){if(Rf(t)){for(var e=0;e<i.length;e++){var n=i[e].exist;if(!i[e].option&&!wn(n)&&null==t.id){i[e].option=t;break}}e>=i.length&&i.push({option:t})}}),i}function _n(t){var e=R();zf(t,function(t){var i=t.exist;i&&e.set(i.id,t)}),zf(t,function(t){var i=t.option;L(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),zf(t,function(t,i){var n=t.exist,r=t.option,a=t.keyInfo;if(Rf(r)){if(a.name=null!=r.name?r.name+"":n?n.name:Nf+i,n)a.id=n.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\x00"+a.name+"\x00"+o++;while(e.get(a.id))}e.set(a.id,t)}})}function xn(t){var e=t.name;return!(!e||!e.indexOf(Nf))}function wn(t){return Rf(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function bn(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?_(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?_(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function Sn(){var t="__\x00ec_inner_"+Wf++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function Mn(t,e,i){if(w(e)){var n={};n[e+"Index"]=0,e=n}var r=i&&i.defaultMainType;!r||Tn(e,r+"Index")||Tn(e,r+"Id")||Tn(e,r+"Name")||(e[r+"Index"]=0);var a={};return zf(e,function(n,r){var n=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=n);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=o[1],l=(o[2]||"").toLowerCase();if(!(!s||!l||null==n||"index"===l&&"none"===n||i&&i.includeMainTypes&&h(i.includeMainTypes,s)<0)){var u={mainType:s};("index"!==l||"all"!==n)&&(u[l]=n);var c=t.queryComponents(u);a[s+"Models"]=c,a[s+"Model"]=c[0]}}),a}function Tn(t,e){return t&&t.hasOwnProperty(e)}function Cn(t,e,i){t.setAttribute?t.setAttribute(e,i):t[e]=i}function In(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function Dn(t){return"auto"===t?Hc.domSupported?"html":"richText":t||"html"}function kn(t){var e={main:"",sub:""};return t&&(t=t.split(Vf),e.main=t[0]||"",e.sub=t[1]||""),e}function An(t){L(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function Pn(t){t.$constructor=t,t.extend=function(t){var e=this,i=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o(i.prototype,t),i.extend=this.extend,i.superCall=On,i.superApply=Bn,u(i,this),i.superClass=e,i}}function Ln(t){var e=["__\x00is_clz",Xf++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function On(t,e){var i=A(arguments,2);return this.superClass.prototype[e].apply(t,i)}function Bn(t,e,i){return this.superClass.prototype[e].apply(t,i)}function En(t,e){function i(t){var e=n[t.main];return e&&e[Gf]||(e=n[t.main]={},e[Gf]=!0),e}e=e||{};var n={};if(t.registerClass=function(t,e){if(e)if(An(e),e=kn(e),e.sub){if(e.sub!==Gf){var r=i(e);r[e.sub]=t}}else n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[Gf]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=kn(t);var e=[],i=n[t.main];return i&&i[Gf]?f(i,function(t,i){i!==Gf&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=kn(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(n,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=kn(t);var e=n[t.main];return e&&e[Gf]},t.parseClassType=kn,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var i=r.call(this,e);return t.registerClass(i,e.type)})}return t}function zn(t){return t>-Qf&&Qf>t}function Rn(t){return t>Qf||-Qf>t}function Fn(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function Nn(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function Hn(t,e,i,n,r,a){var o=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*o*l,c=s*l-9*o*h,d=l*l-3*s*h,f=0;if(zn(u)&&zn(c))if(zn(s))a[0]=0;else{var p=-l/s;p>=0&&1>=p&&(a[f++]=p)}else{var g=c*c-4*u*d;if(zn(g)){var v=c/u,p=-s/o+v,m=-v/2;p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m)}else if(g>0){var y=Kf(g),_=u*s+1.5*o*(-c+y),x=u*s+1.5*o*(-c-y);_=0>_?-$f(-_,ep):$f(_,ep),x=0>x?-$f(-x,ep):$f(x,ep);var p=(-s-(_+x))/(3*o);p>=0&&1>=p&&(a[f++]=p)}else{var w=(2*u*s-3*o*c)/(2*Kf(u*u*u)),b=Math.acos(w)/3,S=Kf(u),M=Math.cos(b),p=(-s-2*S*M)/(3*o),m=(-s+S*(M+tp*Math.sin(b)))/(3*o),T=(-s+S*(M-tp*Math.sin(b)))/(3*o);p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m),T>=0&&1>=T&&(a[f++]=T)}}return f}function Wn(t,e,i,n,r){var a=6*i-12*e+6*t,o=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(zn(o)){if(Rn(a)){var h=-s/a;h>=0&&1>=h&&(r[l++]=h)}}else{var u=a*a-4*o*s;if(zn(u))r[0]=-a/(2*o);else if(u>0){var c=Kf(u),h=(-a+c)/(2*o),d=(-a-c)/(2*o);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Vn(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,h=(s-o)*r+o,u=(l-s)*r+s,c=(u-h)*r+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=n}function Gn(t,e,i,n,r,a,o,s,l,h,u){var c,d,f,p,g,v=.005,m=1/0;ip[0]=l,ip[1]=h;for(var y=0;1>y;y+=.05)np[0]=Fn(t,i,r,o,y),np[1]=Fn(e,n,a,s,y),p=id(ip,np),m>p&&(c=y,m=p);m=1/0;for(var _=0;32>_&&!(Jf>v);_++)d=c-v,f=c+v,np[0]=Fn(t,i,r,o,d),np[1]=Fn(e,n,a,s,d),p=id(np,ip),d>=0&&m>p?(c=d,m=p):(rp[0]=Fn(t,i,r,o,f),rp[1]=Fn(e,n,a,s,f),g=id(rp,ip),1>=f&&m>g?(c=f,m=g):v*=.5);return u&&(u[0]=Fn(t,i,r,o,c),u[1]=Fn(e,n,a,s,c)),Kf(m)}function Xn(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function Yn(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function qn(t,e,i,n,r){var a=t-2*e+i,o=2*(e-t),s=t-n,l=0;if(zn(a)){if(Rn(o)){var h=-s/o;h>=0&&1>=h&&(r[l++]=h)}}else{var u=o*o-4*a*s;if(zn(u)){var h=-o/(2*a);h>=0&&1>=h&&(r[l++]=h)}else if(u>0){var c=Kf(u),h=(-o+c)/(2*a),d=(-o-c)/(2*a);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Un(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function jn(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function Zn(t,e,i,n,r,a,o,s,l){var h,u=.005,c=1/0;ip[0]=o,ip[1]=s;for(var d=0;1>d;d+=.05){np[0]=Xn(t,i,r,d),np[1]=Xn(e,n,a,d);var f=id(ip,np);c>f&&(h=d,c=f)}c=1/0;for(var p=0;32>p&&!(Jf>u);p++){var g=h-u,v=h+u;np[0]=Xn(t,i,r,g),np[1]=Xn(e,n,a,g);var f=id(np,ip);if(g>=0&&c>f)h=g,c=f;else{rp[0]=Xn(t,i,r,v),rp[1]=Xn(e,n,a,v);var m=id(rp,ip);1>=v&&c>m?(h=v,c=m):u*=.5}}return l&&(l[0]=Xn(t,i,r,h),l[1]=Xn(e,n,a,h)),Kf(c)}function $n(t,e,i,n,r,a){r[0]=ap(t,i),r[1]=ap(e,n),a[0]=op(t,i),a[1]=op(e,n)}function Kn(t,e,i,n,r,a,o,s,l,h){var u,c=Wn,d=Fn,f=c(t,i,r,o,fp);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;f>u;u++){var p=d(t,i,r,o,fp[u]);l[0]=ap(p,l[0]),h[0]=op(p,h[0])}for(f=c(e,n,a,s,pp),u=0;f>u;u++){var g=d(e,n,a,s,pp[u]);l[1]=ap(g,l[1]),h[1]=op(g,h[1])}l[0]=ap(t,l[0]),h[0]=op(t,h[0]),l[0]=ap(o,l[0]),h[0]=op(o,h[0]),l[1]=ap(e,l[1]),h[1]=op(e,h[1]),l[1]=ap(s,l[1]),h[1]=op(s,h[1])}function Qn(t,e,i,n,r,a,o,s){var l=Un,h=Xn,u=op(ap(l(t,i,r),1),0),c=op(ap(l(e,n,a),1),0),d=h(t,i,r,u),f=h(e,n,a,c);o[0]=ap(t,r,d),o[1]=ap(e,a,f),s[0]=op(t,r,d),s[1]=op(e,a,f)}function Jn(t,e,i,n,r,a,o,s,l){var h=$,u=K,c=Math.abs(r-a);if(1e-4>c%hp&&c>1e-4)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(up[0]=lp(r)*i+t,up[1]=sp(r)*n+e,cp[0]=lp(a)*i+t,cp[1]=sp(a)*n+e,h(s,up,cp),u(l,up,cp),r%=hp,0>r&&(r+=hp),a%=hp,0>a&&(a+=hp),r>a&&!o?a+=hp:a>r&&o&&(r+=hp),o){var d=a;a=r,r=d}for(var f=0;a>f;f+=Math.PI/2)f>r&&(dp[0]=lp(f)*i+t,dp[1]=sp(f)*n+e,h(s,dp,s),u(l,dp,l))}function tr(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,h=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),h=(t*n-i*e)/(t-i);var u=l*a-o+h,c=u*u/(l*l+1);return s/2*s/2>=c}function er(t,e,i,n,r,a,o,s,l,h,u){if(0===l)return!1;var c=l;if(u>e+c&&u>n+c&&u>a+c&&u>s+c||e-c>u&&n-c>u&&a-c>u&&s-c>u||h>t+c&&h>i+c&&h>r+c&&h>o+c||t-c>h&&i-c>h&&r-c>h&&o-c>h)return!1;var d=Gn(t,e,i,n,r,a,o,s,h,u,null);return c/2>=d}function ir(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;if(l>e+h&&l>n+h&&l>a+h||e-h>l&&n-h>l&&a-h>l||s>t+h&&s>i+h&&s>r+h||t-h>s&&i-h>s&&r-h>s)return!1;var u=Zn(t,e,i,n,r,a,s,l,null);return h/2>=u}function nr(t){return t%=Dp,0>t&&(t+=Dp),t}function rr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>i||i>u+h)return!1;if(Math.abs(n-r)%kp<1e-4)return!0;if(a){var c=n;n=nr(r),r=nr(c)}else n=nr(n),r=nr(r);n>r&&(r+=kp);var d=Math.atan2(l,s);return 0>d&&(d+=kp),d>=n&&r>=d||d+kp>=n&&r>=d+kp}function ar(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l===r?1/0:l>r?o:0}function or(t,e){return Math.abs(t-e)<Lp}function sr(){var t=Bp[0];Bp[0]=Bp[1],Bp[1]=t}function lr(t,e,i,n,r,a,o,s,l,h){if(h>e&&h>n&&h>a&&h>s||e>h&&n>h&&a>h&&s>h)return 0;var u=Hn(e,n,a,s,h,Op);if(0===u)return 0;for(var c,d,f=0,p=-1,g=0;u>g;g++){var v=Op[g],m=0===v||1===v?.5:1,y=Fn(t,i,r,o,v);l>y||(0>p&&(p=Wn(e,n,a,s,Bp),Bp[1]<Bp[0]&&p>1&&sr(),c=Fn(e,n,a,s,Bp[0]),p>1&&(d=Fn(e,n,a,s,Bp[1]))),f+=2===p?v<Bp[0]?e>c?m:-m:v<Bp[1]?c>d?m:-m:d>s?m:-m:v<Bp[0]?e>c?m:-m:c>s?m:-m)}return f}function hr(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=qn(e,n,a,s,Op);if(0===l)return 0;var h=Un(e,n,a);if(h>=0&&1>=h){for(var u=0,c=Xn(e,n,a,h),d=0;l>d;d++){var f=0===Op[d]||1===Op[d]?.5:1,p=Xn(t,i,r,Op[d]);o>p||(u+=Op[d]<h?e>c?f:-f:c>a?f:-f)}return u}var f=0===Op[0]||1===Op[0]?.5:1,p=Xn(t,i,r,Op[0]);return o>p?0:e>a?f:-f}function ur(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);Op[0]=-l,Op[1]=l;var h=Math.abs(n-r);if(1e-4>h)return 0;if(1e-4>h%Pp){n=0,r=Pp;var u=a?1:-1;return o>=Op[0]+t&&o<=Op[1]+t?u:0}if(a){var l=n;n=nr(r),r=nr(l)}else n=nr(n),r=nr(r);n>r&&(r+=Pp);for(var c=0,d=0;2>d;d++){var f=Op[d];if(f+t>o){var p=Math.atan2(s,f),u=a?1:-1;0>p&&(p=Pp+p),(p>=n&&r>=p||p+Pp>=n&&r>=p+Pp)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function cr(t,e,i,n,r){for(var a=0,o=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===Ap.M&&u>1&&(i||(a+=ar(o,s,l,h,n,r))),1===u&&(o=t[u],s=t[u+1],l=o,h=s),c){case Ap.M:l=t[u++],h=t[u++],o=l,s=h;break;case Ap.L:if(i){if(tr(o,s,t[u],t[u+1],e,n,r))return!0}else a+=ar(o,s,t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ap.C:if(i){if(er(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=lr(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ap.Q:if(i){if(ir(o,s,t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=hr(o,s,t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ap.A:var d=t[u++],f=t[u++],p=t[u++],g=t[u++],v=t[u++],m=t[u++];u+=1;var y=1-t[u++],_=Math.cos(v)*p+d,x=Math.sin(v)*g+f;u>1?a+=ar(o,s,_,x,n,r):(l=_,h=x);var w=(n-d)*g/p+d;if(i){if(rr(d,f,g,v,v+m,y,e,w,r))return!0}else a+=ur(d,f,g,v,v+m,y,w,r);o=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case Ap.R:l=o=t[u++],h=s=t[u++];var b=t[u++],S=t[u++],_=l+b,x=h+S;if(i){if(tr(l,h,_,h,e,n,r)||tr(_,h,_,x,e,n,r)||tr(_,x,l,x,e,n,r)||tr(l,x,l,h,e,n,r))return!0}else a+=ar(_,h,_,x,n,r),a+=ar(l,x,l,h,n,r);break;case Ap.Z:if(i){if(tr(o,s,l,h,e,n,r))return!0}else a+=ar(o,s,l,h,n,r);o=l,s=h}}return i||or(s,h)||(a+=ar(o,s,l,h,n,r)||0),0!==a}function dr(t,e,i){return cr(t,0,!1,e,i)}function fr(t,e,i,n){return cr(t,e,!0,i,n)}function pr(t){Ji.call(this,t),this.path=null}function gr(t,e,i,n,r,a,o,s,l,h,u){var c=l*(qp/180),d=Yp(c)*(t-i)/2+Xp(c)*(e-n)/2,f=-1*Xp(c)*(t-i)/2+Yp(c)*(e-n)/2,p=d*d/(o*o)+f*f/(s*s);p>1&&(o*=Gp(p),s*=Gp(p));var g=(r===a?-1:1)*Gp((o*o*s*s-o*o*f*f-s*s*d*d)/(o*o*f*f+s*s*d*d))||0,v=g*o*f/s,m=g*-s*d/o,y=(t+i)/2+Yp(c)*v-Xp(c)*m,_=(e+n)/2+Xp(c)*v+Yp(c)*m,x=Zp([1,0],[(d-v)/o,(f-m)/s]),w=[(d-v)/o,(f-m)/s],b=[(-1*d-v)/o,(-1*f-m)/s],S=Zp(w,b);jp(w,b)<=-1&&(S=qp),jp(w,b)>=1&&(S=0),0===a&&S>0&&(S-=2*qp),1===a&&0>S&&(S+=2*qp),u.addData(h,y,_,o,s,x,S,c,a)}function vr(t){if(!t)return new Ip;for(var e,i=0,n=0,r=i,a=n,o=new Ip,s=Ip.CMD,l=t.match($p),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(Kp)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,_,x,w,b,S,M=i,T=n;switch(d){case"l":i+=f[v++],n+=f[v++],u=s.L,o.addData(u,i,n);break;case"L":i=f[v++],n=f[v++],u=s.L,o.addData(u,i,n);break;case"m":i+=f[v++],n+=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="l";break;case"M":i=f[v++],n=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="L";break;case"h":i+=f[v++],u=s.L,o.addData(u,i,n);break;case"H":i=f[v++],u=s.L,o.addData(u,i,n);break;case"v":n+=f[v++],u=s.L,o.addData(u,i,n);break;case"V":n=f[v++],u=s.L,o.addData(u,i,n);break;case"C":u=s.C,o.addData(u,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":u=s.C,o.addData(u,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":m=i,y=n;var C=o.len(),I=o.data;e===s.C&&(m+=i-I[C-4],y+=n-I[C-3]),u=s.C,M=f[v++],T=f[v++],i=f[v++],n=f[v++],o.addData(u,m,y,M,T,i,n);break;case"s":m=i,y=n;var C=o.len(),I=o.data;e===s.C&&(m+=i-I[C-4],y+=n-I[C-3]),u=s.C,M=i+f[v++],T=n+f[v++],i+=f[v++],n+=f[v++],o.addData(u,m,y,M,T,i,n);break;case"Q":M=f[v++],T=f[v++],i=f[v++],n=f[v++],u=s.Q,o.addData(u,M,T,i,n);break;case"q":M=f[v++]+i,T=f[v++]+n,i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,M,T,i,n);break;case"T":m=i,y=n;var C=o.len(),I=o.data;e===s.Q&&(m+=i-I[C-4],y+=n-I[C-3]),i=f[v++],n=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"t":m=i,y=n;var C=o.len(),I=o.data;e===s.Q&&(m+=i-I[C-4],y+=n-I[C-3]),i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"A":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,T=n,i=f[v++],n=f[v++],u=s.A,gr(M,T,i,n,b,S,_,x,w,u,o);break;case"a":_=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],M=i,T=n,i+=f[v++],n+=f[v++],u=s.A,gr(M,T,i,n,b,S,_,x,w,u,o)}}("z"===d||"Z"===d)&&(u=s.Z,o.addData(u),i=r,n=a),e=u}return o.toStatic(),o}function mr(t,e){var i=vr(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(i.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){Vp(i,t),this.dirty(!0)},e}function yr(t,e){return new pr(mr(t,e))}function _r(t,e){return pr.extend(mr(t,e))}function xr(t,e){for(var i=[],n=t.length,r=0;n>r;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),i.push(a.path)}var o=new pr(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},o}function wr(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function br(t,e,i){var n=e.points,r=e.smooth;if(n&&n.length>=2){if(r&&"spline"!==r){var a=ag(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;(i?o:o-1)>s;s++){var l=a[2*s],h=a[2*s+1],u=n[(s+1)%o];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(n=rg(n,i)),t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;c>s;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}function Sr(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x1,a=e.x2,o=e.y1,s=e.y2;lg(2*r)===lg(2*a)?t.x1=t.x2=Tr(r,n,!0):(t.x1=r,t.x2=a),lg(2*o)===lg(2*s)?t.y1=t.y2=Tr(o,n,!0):(t.y1=o,t.y2=s)}}function Mr(t,e,i){var n=i&&i.lineWidth;if(e&&n){var r=e.x,a=e.y,o=e.width,s=e.height;t.x=Tr(r,n,!0),t.y=Tr(a,n,!0),t.width=Math.max(Tr(r+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Tr(a+s,n,!1)-t.y,0===s?0:1)}}function Tr(t,e,i){var n=lg(2*t);return(n+lg(e))%2===0?n/2:(n+(i?1:-1))/2}function Cr(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?Nn:Fn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?Nn:Fn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?Yn:Xn)(t.x1,t.cpx1,t.x2,e),(i?Yn:Xn)(t.y1,t.cpy1,t.y2,e)]}function Ir(t){Ji.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function Dr(t){return pr.extend(t)}function kr(t,e){return _r(t,e)}function Ar(t,e,i,n){var r=yr(t,e);return i&&("center"===n&&(i=Lr(i,r.getBoundingRect())),Or(r,i)),r}function Pr(t,e,i){var n=new tn({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===i){var r={width:t.width,height:t.height};n.setStyle(Lr(e,r))}}});return n}function Lr(t,e){var i,n=e.width/e.height,r=t.height*n;r<=t.width?i=t.height:(r=t.width,i=r/n);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-i/2,width:r,height:i}}function Or(t,e){if(t.applyTransform){var i=t.getBoundingRect(),n=i.calculateTransform(e);t.applyTransform(n)}}function Br(t){var e=t.shape,i=t.style.lineWidth;return wg(2*e.x1)===wg(2*e.x2)&&(e.x1=e.x2=zr(e.x1,i,!0)),wg(2*e.y1)===wg(2*e.y2)&&(e.y1=e.y2=zr(e.y1,i,!0)),t}function Er(t){var e=t.shape,i=t.style.lineWidth,n=e.x,r=e.y,a=e.width,o=e.height;return e.x=zr(e.x,i,!0),e.y=zr(e.y,i,!0),e.width=Math.max(zr(n+a,i,!1)-e.x,0===a?0:1),e.height=Math.max(zr(r+o,i,!1)-e.y,0===o?0:1),t}function zr(t,e,i){var n=wg(2*t);return(n+wg(e))%2===0?n/2:(n+(i?1:-1))/2}function Rr(t){return null!=t&&"none"!==t}function Fr(t){if("string"!=typeof t)return t;var e=Ig.get(t);return e||(e=Be(t,-.1),1e4>Dg&&(Ig.set(t,e),Dg++)),e}function Nr(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var i=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var n=t.style;for(var r in e)null!=e[r]&&(i[r]=n[r]);i.fill=n.fill,i.stroke=n.stroke}}function Hr(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var i=t.useHoverLayer;t.__highlighted=i?"layer":"plain";var n=t.__zr;if(n||!i){var r=t,a=t.style;i&&(r=n.addHover(t),a=r.style),oa(a),i||Nr(r),a.extendFrom(e),Wr(a,e,"fill"),Wr(a,e,"stroke"),aa(a),i||(t.dirty(!1),t.z2+=Tg)}}}function Wr(t,e,i){!Rr(e[i])&&Rr(t[i])&&(t[i]=Fr(t[i]))}function Vr(t){var e=t.__highlighted;if(e)if(t.__highlighted=!1,"layer"===e)t.__zr&&t.__zr.removeHover(t);else if(e){var i=t.style,n=t.__cachedNormalStl;n&&(oa(i),t.setStyle(n),aa(i));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===Tg&&(t.z2=r)}}function Gr(t,e){t.isGroup?t.traverse(function(t){!t.isGroup&&e(t)}):e(t)}function Xr(t,e){e=t.__hoverStl=e!==!1&&(e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Vr(t),Hr(t))}function Yr(t){return t&&t.__isEmphasisEntered}function qr(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&Gr(this,Hr)}function Ur(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasisEntered&&Gr(this,Vr)}function jr(){this.__isEmphasisEntered=!0,Gr(this,Hr)}function Zr(){this.__isEmphasisEntered=!1,Gr(this,Vr)}function $r(t,e,i){t.isGroup?t.traverse(function(t){!t.isGroup&&Xr(t,t.hoverStyle||e)}):Xr(t,t.hoverStyle||e),Kr(t,i)}function Kr(t,e){var i=e===!1;if(t.__hoverSilentOnTouch=null!=e&&e.hoverSilentOnTouch,!i||t.__hoverStyleTrigger){var n=i?"off":"on";t[n]("mouseover",qr)[n]("mouseout",Ur),t[n]("emphasis",jr)[n]("normal",Zr),t.__hoverStyleTrigger=!i}}function Qr(t,e,i,n,r,a,o){r=r||Mg;var s,l=r.labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=i.getShallow("show"),d=n.getShallow("show");(c||d)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u)),null==s&&(s=x(r.defaultText)?r.defaultText(h,r):r.defaultText));var f=c?s:null,p=d?D(l?l.getFormattedLabel(h,"emphasis",null,u):null,s):null;(null!=f||null!=p)&&(Jr(t,i,a,r),Jr(e,n,o,r,!0)),t.text=f,e.text=p}function Jr(t,e,i,n,r){return ea(t,e,n,r),i&&o(t,i),t}function ta(t,e,i){var n,r={isRectText:!0};i===!1?n=!0:r.autoColor=i,ea(t,e,r,n)}function ea(t,e,i,n){if(i=i||Mg,i.isRectText){var r=e.getShallow("position")||(n?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=D(e.getShallow("distance"),n?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=ia(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);na(o[u]={},c,l,i,n)}}return t.rich=o,na(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function ia(t){for(var e;t&&t!==t.ecModel;){var i=(t.option||Mg).rich;if(i){e=e||{};for(var n in i)i.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function na(t,e,i,n,r,a){i=!r&&i||Mg,t.textFill=ra(e.getShallow("color"),n)||i.color,t.textStroke=ra(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=D(e.getShallow("textBorderWidth"),i.textBorderWidth),t.insideRawTextPosition=t.textPosition,r||(a&&(t.insideRollbackOpt=n,aa(t)),null==t.textFill&&(t.textFill=n.autoColor)),t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&n.disableBox||(t.textBackgroundColor=ra(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=ra(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function ra(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function aa(t){var e=t.insideRollbackOpt;if(e&&null==t.textFill){var i,n=e.useInsideStyle,r=t.insideRawTextPosition,a=e.autoColor;n!==!1&&(n===!0||e.isRectText&&r&&"string"==typeof r&&r.indexOf("inside")>=0)?(i={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=a,null==t.textStrokeWidth&&(t.textStrokeWidth=2))):null!=a&&(i={textFill:null},t.textFill=a),i&&(t.insideRollback=i)}}function oa(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function sa(t,e){var i=e||e.getModel("textStyle");return O([t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" "))}function la(t,e,i,n,r,a){"function"==typeof r&&(a=r,r=null);var o=n&&n.isAnimationEnabled();if(o){var s=t?"Update":"",l=n.getShallow("animationDuration"+s),h=n.getShallow("animationEasing"+s),u=n.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(i,l,u||0,h,a,!!a):(e.stopAnimation(),e.attr(i),a&&a())}else e.stopAnimation(),e.attr(i),a&&a()}function ha(t,e,i,n,r){la(!0,t,e,i,n,r)}function ua(t,e,i,n,r){la(!1,t,e,i,n,r)}function ca(t,e){for(var i=ge([]);t&&t!==e;)me(i,t.getLocalTransform(),i),t=t.parent;return i}function da(t,e,i){return e&&!d(e)&&(e=vd.getLocalTransform(e)),i&&(e=we([],e)),Z([],t,e)}function fa(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return a=da(a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function pa(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:H(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),ha(t,n,i,t.dataIndex)}}})}}function ga(t,e){return p(t,function(t){var i=t[0];i=bg(i,e.x),i=Sg(i,e.x+e.width);var n=t[1];return n=bg(n,e.y),n=Sg(n,e.y+e.height),[i,n]})}function va(t,e){var i=bg(t.x,e.x),n=Sg(t.x+t.width,e.x+e.width),r=bg(t.y,e.y),a=Sg(t.y+t.height,e.y+e.height);return n>=i&&a>=r?{x:i,y:r,width:n-i,height:a-r}:void 0}function ma(t,e,i){e=o({rectHover:!0},e);var n=e.style={strokeNoScale:!0};return i=i||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(n.image=t.slice(8),s(n,i),new tn(e)):Ar(t.replace("path://",""),e,i,"center"):void 0}function ya(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function _a(t,e,i){for(var n=0;n<e.length&&(!e[n]||(t=t&&"object"==typeof t?t[e[n]]:null,null!=t));n++);return null==t&&i&&(t=i.get(e)),t}function xa(t,e){var i=Eg(t).getParent;return i?i.call(t,e):t.parentModel}function wa(t){return[t||"",zg++,Math.random().toFixed(5)].join("_")}function ba(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=kn(t),e[t.main]=i},t.determineSubType=function(i,n){var r=n.type;if(!r){var a=kn(i).main;t.hasSubTypes(i)&&e[a]&&(r=e[a](n))}return r},t}function Sa(t,e){function i(t){var i={},a=[];return f(t,function(o){var s=n(i,o),l=s.originalDeps=e(o),u=r(l,t);s.entryCount=u.length,0===s.entryCount&&a.push(o),f(u,function(t){h(s.predecessor,t)<0&&s.predecessor.push(t);var e=n(i,t);h(e.successor,t)<0&&e.successor.push(o)})}),{graph:i,noEntryList:a}}function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var i=[];return f(t,function(t){h(e,t)>=0&&i.push(t)}),i}t.topologicalTravel=function(t,e,n,r){function a(t){l[t].entryCount--,0===l[t].entryCount&&h.push(t)}function o(t){u[t]=!0,a(t)}if(t.length){var s=i(e),l=s.graph,h=s.noEntryList,u={};for(f(t,function(t){u[t]=!0});h.length;){var c=h.pop(),d=l[c],p=!!u[c];p&&(n.call(r,c,d.originalDeps.slice()),delete u[c]),f(d.successor,p?o:a)}f(u,function(){throw new Error("Circle dependency may exists")})}}}function Ma(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function Ta(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]}function Ca(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?Ma(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function Ia(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function Da(t){var e=t.toString(),i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function ka(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function Aa(t){var e=2*Math.PI;return(t%e+e)%e}function Pa(t){return t>-Rg&&Rg>t}function La(t){if(t instanceof Date)return t;if("string"==typeof t){var e=Fg.exec(t);if(!e)return new Date(0/0);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function Oa(t){return Math.pow(10,Ba(t))}function Ba(t){return Math.floor(Math.log(t)/Math.LN10)}function Ea(t,e){var i,n=Ba(t),r=Math.pow(10,n),a=t/r;return i=e?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,t=i*r,n>=-20?+t.toFixed(0>n?-n:0):t}function za(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function Ra(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Fa(t){return null==t?"":(t+"").replace(Hg,function(t,e){return Wg[e]})}function Na(t,e,i){_(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],a=0;a<r.length;a++){var o=Vg[a];t=t.replace(Gg(o),Gg(o,0))}for(var s=0;n>s;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(Gg(Vg[l],s),i?Fa(h):h)}return t}function Ha(t,e){t=w(t)?{color:t,extraCssText:e}:t||{};var i=t.color,n=t.type,e=t.extraCssText,r=t.renderMode||"html",a=t.markerId||"X";return i?"html"===r?"subItem"===n?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Fa(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Fa(i)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+a+"|}  ",style:{color:i}}:""}function Wa(t,e){return t+="","0000".substr(0,e-t.length)+t}function Va(t,e,i){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=La(e),r=i?"UTC":"",a=n["get"+r+"FullYear"](),o=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),h=n["get"+r+"Minutes"](),u=n["get"+r+"Seconds"](),c=n["get"+r+"Milliseconds"]();return t=t.replace("MM",Wa(o,2)).replace("M",o).replace("yyyy",a).replace("yy",a%100).replace("dd",Wa(s,2)).replace("d",s).replace("hh",Wa(l,2)).replace("h",l).replace("mm",Wa(h,2)).replace("m",h).replace("ss",Wa(u,2)).replace("s",u).replace("SSS",Wa(c,3))}function Ga(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+i,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+i,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=u+i:o=c+i)})}function Xa(t,e,i){i=Ng(i||0);var n=e.width,r=e.height,a=Ca(t.left,n),o=Ca(t.top,r),s=Ca(t.right,n),l=Ca(t.bottom,r),h=Ca(t.width,n),u=Ca(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(h)&&(h=n-s-d-a),isNaN(u)&&(u=r-l-c-o),null!=f&&(isNaN(h)&&isNaN(u)&&(f>n/r?h=.8*n:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(a)&&(a=n-s-h-d),isNaN(o)&&(o=r-l-u-c),t.left||t.right){case"center":a=n/2-h/2-i[3];break;case"right":a=n-h-d}switch(t.top||t.bottom){case"middle":case"center":o=r/2-u/2-i[0];break;case"bottom":o=r-u-c}a=a||0,o=o||0,isNaN(h)&&(h=n-d-a-(s||0)),isNaN(u)&&(u=r-c-o-(l||0));var p=new Je(a+i[3],o+i[0],h,u);return p.margin=i,p}function Ya(t,e,i){function n(i,n){var o={},l=0,h={},u=0,c=2;if(Yg(i,function(e){h[e]=t[e]}),Yg(i,function(t){r(e,t)&&(o[t]=h[t]=e[t]),a(o,t)&&l++,a(h,t)&&u++}),s[n])return a(e,i[1])?h[i[2]]=null:a(e,i[2])&&(h[i[1]]=null),h;if(u!==c&&l){if(l>=c)return o;for(var d=0;d<i.length;d++){var f=i[d];if(!r(o,f)&&r(t,f)){o[f]=t[f];break}}return o}return h}function r(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function o(t,e,i){Yg(t,function(t){e[t]=i[t]})}!b(i)&&(i={});var s=i.ignoreSize;!_(s)&&(s=[s,s]);var l=n(Ug[0],0),h=n(Ug[1],1);o(Ug[0],t,l),o(Ug[1],t,h)}function qa(t){return Ua({},t)}function Ua(t,e){return e&&t&&Yg(qg,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t}function ja(t){var e=[];
return f(Kg.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return kn(t).main}),"dataset"!==t&&h(e,"dataset")<=0&&e.unshift("dataset"),e}function Za(t,e){for(var i=t.length,n=0;i>n;n++)if(t[n].length>e)return t[n];return t[i-1]}function $a(t){var e=t.get("coordinateSystem"),i={coordSysName:e,coordSysDims:[],axisMap:R(),categoryAxisMap:R()},n=iv[e];return n?(n(t,i,i.axisMap,i.categoryAxisMap),i):void 0}function Ka(t){return"category"===t.get("type")}function Qa(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===ov?{}:[]),this.sourceFormat=t.sourceFormat||sv,this.seriesLayoutBy=t.seriesLayoutBy||hv,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&R(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function Ja(t){var e=t.option.source,i=sv;if(M(e))i=lv;else if(_(e)){0===e.length&&(i=rv);for(var n=0,r=e.length;r>n;n++){var a=e[n];if(null!=a){if(_(a)){i=rv;break}if(b(a)){i=av;break}}}}else if(b(e)){for(var o in e)if(e.hasOwnProperty(o)&&d(e[o])){i=ov;break}}else if(null!=e)throw new Error("Invalid data");cv(t).sourceFormat=i}function to(t){return cv(t).source}function eo(t){cv(t).datasetMap=R()}function io(t){var e=t.option,i=e.data,n=M(i)?lv:nv,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=lo(t);if(l){var h=l.option;i=h.source,n=cv(l).sourceFormat,r=!0,a=a||h.seriesLayoutBy,null==o&&(o=h.sourceHeader),s=s||h.dimensions}var u=no(i,n,a,o,s),c=e.encode;!c&&l&&(c=so(t,l,i,n,a,u)),cv(t).source=new Qa({data:i,fromDataset:r,seriesLayoutBy:a,sourceFormat:n,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:c})}function no(t,e,i,n,r){if(!t)return{dimensionsDefine:ro(r)};var a,o,s;if(e===rv)"auto"===n||null==n?ao(function(t){null!=t&&"-"!==t&&(w(t)?null==o&&(o=1):o=0)},i,t,10):o=n?1:0,r||1!==o||(r=[],ao(function(t,e){r[e]=null!=t?t:""},i,t)),a=r?r.length:i===uv?t.length:t[0]?t[0].length:null;else if(e===av)r||(r=oo(t),s=!0);else if(e===ov)r||(r=[],s=!0,f(t,function(t,e){r.push(e)}));else if(e===nv){var l=vn(t[0]);a=_(l)&&l.length||1}var h;return s&&f(r,function(t,e){"name"===(b(t)?t.name:t)&&(h=e)}),{startIndex:o,dimensionsDefine:ro(r),dimensionsDetectCount:a,potentialNameDimIndex:h}}function ro(t){if(t){var e=R();return p(t,function(t){if(t=o({},b(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function ao(t,e,i,n){if(null==n&&(n=1/0),e===uv)for(var r=0;r<i.length&&n>r;r++)t(i[r]?i[r][0]:null,r);else for(var a=i[0]||[],r=0;r<a.length&&n>r;r++)t(a[r],r)}function oo(t){for(var e,i=0;i<t.length&&!(e=t[i++]););if(e){var n=[];return f(e,function(t,e){n.push(e)}),n}}function so(t,e,i,n,r,a){var o=$a(t),s={},l=[],h=[],u=t.subType,c=R(["pie","map","funnel"]),d=R(["line","bar","pictorialBar","scatter","effectScatter","candlestick","boxplot"]);if(o&&null!=d.get(u)){var p=t.ecModel,g=cv(p).datasetMap,v=e.uid+"_"+r,m=g.get(v)||g.set(v,{categoryWayDim:1,valueWayDim:0});f(o.coordSysDims,function(t){if(null==o.firstCategoryDimIndex){var e=m.valueWayDim++;s[t]=e,h.push(e)}else if(o.categoryAxisMap.get(t))s[t]=0,l.push(0);else{var e=m.categoryWayDim++;s[t]=e,h.push(e)}})}else if(null!=c.get(u)){for(var y,_=0;5>_&&null==y;_++)uo(i,n,r,a.dimensionsDefine,a.startIndex,_)||(y=_);if(null!=y){s.value=y;var x=a.potentialNameDimIndex||Math.max(y-1,0);h.push(x),l.push(x)}}return l.length&&(s.itemName=l),h.length&&(s.seriesName=h),s}function lo(t){var e=t.option,i=e.data;return i?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function ho(t,e){return uo(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function uo(t,e,i,n,r,a){function o(t){return null!=t&&isFinite(t)&&""!==t?!1:w(t)&&"-"!==t?!0:void 0}var s,l=5;if(M(t))return!1;var h;if(n&&(h=n[a],h=b(h)?h.name:h),e===rv)if(i===uv){for(var u=t[a],c=0;c<(u||[]).length&&l>c;c++)if(null!=(s=o(u[r+c])))return s}else for(var c=0;c<t.length&&l>c;c++){var d=t[r+c];if(d&&null!=(s=o(d[a])))return s}else if(e===av){if(!h)return;for(var c=0;c<t.length&&l>c;c++){var f=t[c];if(f&&null!=(s=o(f[h])))return s}}else if(e===ov){if(!h)return;var u=t[h];if(!u||M(u))return!1;for(var c=0;c<u.length&&l>c;c++)if(null!=(s=o(u[c])))return s}else if(e===nv)for(var c=0;c<t.length&&l>c;c++){var f=t[c],p=vn(f);if(!_(p))return!1;if(null!=(s=o(p[a])))return s}return!1}function co(t,e){if(e){var i=e.seiresIndex,n=e.seriesId,r=e.seriesName;return null!=i&&t.componentIndex!==i||null!=n&&t.id!==n||null!=r&&t.name!==r}}function fo(t,e){var i=t.color&&!t.colorLayer;f(e,function(e,a){"colorLayer"===a&&i||Kg.hasClass(a)||("object"==typeof e?t[a]=t[a]?r(t[a],e,!1):n(e):null==t[a]&&(t[a]=e))})}function po(t){t=t,this.option={},this.option[dv]=1,this._componentsMap=R({series:[]}),this._seriesIndices,this._seriesIndicesMap,fo(t,this._theme.option),r(t,Jg,!1),this.mergeOption(t)}function go(t,e){_(e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t.get(e)||[]).slice()}),i}function vo(t,e,i){var n=e.type?e.type:i?i.subType:Kg.determineSubType(t,e);return n}function mo(t,e){t._seriesIndicesMap=R(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function yo(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function _o(t){f(pv,function(e){this[e]=m(t[e],t)},this)}function xo(){this._coordinateSystems=[]}function wo(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function bo(t,e,i){var n,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;vv(l,function(t){t&&t.option&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=s),vv([r].concat(a).concat(p(o,function(t){return t.option})),function(t){vv(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function So(t,e,i){var n={width:e,height:i,aspectratio:e/i},r=!0;return f(t,function(t,e){var i=e.match(xv);if(i&&i[1]&&i[2]){var a=i[1],o=i[2].toLowerCase();Mo(n[o],t,a)||(r=!1)}}),r}function Mo(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function To(t,e){return t.join(",")===e.join(",")}function Co(t,e){e=e||{},vv(e,function(e,i){if(null!=e){var n=t[i];if(Kg.hasClass(i)){e=pn(e),n=pn(n);var r=yn(n,e);t[i]=yv(r,function(t){return t.option&&t.exist?_v(t.exist,t.option,!0):t.exist||t.option})}else t[i]=_v(n,e,!0)}})}function Io(t){var e=t&&t.itemStyle;if(e)for(var i=0,n=Sv.length;n>i;i++){var a=Sv[i],o=e.normal,s=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?r(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),s&&s[a]&&(t[a]=t[a]||{},t[a].emphasis?r(t[a].emphasis,s[a]):t[a].emphasis=s[a],s[a]=null)}}function Do(t,e,i){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var n=t[e].normal,r=t[e].emphasis;n&&(i?(t[e].normal=t[e].emphasis=null,s(t[e],n)):t[e]=n),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function ko(t){Do(t,"itemStyle"),Do(t,"lineStyle"),Do(t,"areaStyle"),Do(t,"label"),Do(t,"labelLine"),Do(t,"upperLabel"),Do(t,"edgeLabel")}function Ao(t,e){var i=bv(t)&&t[e],n=bv(i)&&i.textStyle;if(n)for(var r=0,a=Hf.length;a>r;r++){var e=Hf[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function Po(t){t&&(ko(t),Ao(t,"label"),t.emphasis&&Ao(t.emphasis,"label"))}function Lo(t){if(bv(t)){Io(t),ko(t),Ao(t,"label"),Ao(t,"upperLabel"),Ao(t,"edgeLabel"),t.emphasis&&(Ao(t.emphasis,"label"),Ao(t.emphasis,"upperLabel"),Ao(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Io(e),Po(e));var i=t.markLine;i&&(Io(i),Po(i));var n=t.markArea;n&&Po(n);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!M(a))for(var o=0;o<a.length;o++)Po(a[o]);f(t.categories,function(t){ko(t)})}if(r&&!M(r))for(var o=0;o<r.length;o++)Po(r[o]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,o=0;o<s.length;o++)Po(s[o]);var i=t.markLine;if(i&&i.data)for(var l=i.data,o=0;o<l.length;o++)_(l[o])?(Po(l[o][0]),Po(l[o][1])):Po(l[o]);"gauge"===t.type?(Ao(t,"axisLabel"),Ao(t,"title"),Ao(t,"detail")):"treemap"===t.type?(Do(t.breadcrumb,"itemStyle"),f(t.levels,function(t){ko(t)})):"tree"===t.type&&ko(t.leaves)}}function Oo(t){return _(t)?t:t?[t]:[]}function Bo(t){return(_(t)?t[0]:t)||{}}function Eo(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&(i=i&&i[e[n]],null!=i);n++);return i}function zo(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function Ro(t){f(Tv,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function Fo(t){f(t,function(e,i){var n=[],r=[0/0,0/0],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,h){var u=o.get(e.stackedDimension,h);if(isNaN(u))return r;var c,d;s?d=o.getRawIndex(h):c=o.get(e.stackedByDimension,h);for(var f=0/0,p=i-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(u>=0&&v>0||0>=u&&0>v){u+=v,f=v;break}}}return n[0]=u,n[1]=f,n});o.hostModel.setData(l),e.data=l})}function No(t,e){Qa.isInstance(t)||(t=Qa.seriesDataToSource(t)),this._source=t;var i=this._data=t.data,n=t.sourceFormat;n===lv&&(this._offset=0,this._dimSize=e,this._data=i);var r=Av[n===rv?n+"_"+t.seriesLayoutBy:n];o(this,r)}function Ho(){return this._data.length}function Wo(t){return this._data[t]}function Vo(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Go(t,e,i){return null!=i?t[i]:t}function Xo(t,e,i,n){return Yo(t[n],this._dimensionInfos[e])}function Yo(t,e){var i=e&&e.type;if("ordinal"===i){var n=e&&e.ordinalMeta;return n?n.parseAndCollect(t):t}return"time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+La(t)),null==t||""===t?0/0:+t}function qo(t,e,i){if(t){var n=t.getRawDataItem(e);if(null!=n){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(i);return s&&(r=s.name,a=s.index),Pv[o](n,e,a,r)}}}function Uo(t){return new jo(t)}function jo(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function Zo(t,e,i,n,r,a){zv.reset(i,n,r,a),t._callingProgress=e,t._callingProgress({start:i,end:n,count:n-i,next:zv.next},t.context)}function $o(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var i,n;!e&&t._reset&&(i=t._reset(t.context),i&&i.progress&&(n=i.forceFirstProgress,i=i.progress),_(i)&&!i.length&&(i=null)),t._progress=i,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),n}function Ko(t){var e=t.name;xn(t)||(t.name=Qo(t)||e)}function Qo(t){var e=t.getRawData(),i=e.mapDimension("seriesName",!0),n=[];return f(i,function(t){var i=e.getDimensionInfo(t);i.displayName&&n.push(i.displayName)}),n.join(" ")}function Jo(t){return t.model.getRawData().count()}function ts(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),es}function es(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function is(t,e){f(t.CHANGABLE_METHODS,function(i){t.wrapMethod(i,y(ns,e))})}function ns(t){var e=rs(t);e&&e.setOutputEnd(this.count())}function rs(t){var e=(t.ecModel||{}).scheduler,i=e&&e.getPipeline(t.uid);if(i){var n=i.currentTask;if(n){var r=n.agentStubMap;r&&(n=r.get(t.uid))}return n}}function as(){this.group=new Vd,this.uid=wa("viewChart"),this.renderTask=Uo({plan:ls,reset:hs}),this.renderTask.context={view:this}}function os(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var i=0;i<t.childCount();i++)os(t.childAt(i),e)}function ss(t,e,i){var n=bn(t,e);null!=n?f(pn(n),function(e){os(t.getItemGraphicEl(e),i)}):t.eachItemGraphicEl(function(t){os(t,i)})}function ls(t){return Gv(t.model)}function hs(t){var e=t.model,i=t.ecModel,n=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&Vv(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,i,n,r),Yv[l]}function us(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,d=l||i;l=null,a=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(n,t):a>=0?n():c=setTimeout(n,-a),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function cs(t,e,i,n){var r=t[e];if(r){var a=r[qv]||r,o=r[jv],s=r[Uv];if(s!==i||o!==n){if(null==i||!n)return t[e]=a;r=t[e]=us(a,i,"debounce"===n),r[qv]=a,r[jv]=n,r[Uv]=i}return r}}function ds(t,e,i,n){this.ecInstance=t,this.api=e,this.unfinished;var i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice();this._allHandlers=i.concat(n),this._stageTaskMap=R()}function fs(t,e,i,n,r){function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var o;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,h=s.overallTask;if(h){var u,c=h.agentStubMap;c.each(function(t){a(r,t)&&(t.dirty(),u=!0)}),u&&h.dirty(),em(h,n);var d=t.getPerformArgs(h,r.block);c.each(function(t){t.perform(d)}),o|=h.perform(d)}else l&&l.each(function(s){a(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&i.isSeriesFiltered(s.context.model),em(s,n),o|=s.perform(l)})}}),t.unfinished|=o}function ps(t,e,i,n,r){function a(i){var a=i.uid,s=o.get(a)||o.set(a,Uo({plan:xs,reset:ws,count:Ss}));s.context={model:i,ecModel:n,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},Ms(t,i,s)}var o=i.seriesTaskMap||(i.seriesTaskMap=R()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?n.eachRawSeries(a):s?n.eachRawSeriesByType(s,a):l&&l(n,r).each(a);var h=t._pipelineMap;o.each(function(t,e){h.get(e)||(t.dispose(),o.removeKey(e))})}function gs(t,e,i,n,r){function a(e){var i=e.uid,n=s.get(i);n||(n=s.set(i,Uo({reset:ms,onDirty:_s})),o.dirty()),n.context={model:e,overallProgress:u,modifyOutputEnd:c},n.agent=o,n.__block=u,Ms(t,e,n)}var o=i.overallTask=i.overallTask||Uo({reset:vs});o.context={ecModel:n,api:r,overallReset:e.overallReset,scheduler:t};var s=o.agentStubMap=o.agentStubMap||R(),l=e.seriesType,h=e.getTargetSeries,u=!0,c=e.modifyOutputEnd;l?n.eachRawSeriesByType(l,a):h?h(n,r).each(a):(u=!1,f(n.getSeries(),a));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),s.removeKey(e))})}function vs(t){t.overallReset(t.ecModel,t.api,t.payload)}function ms(t){return t.overallProgress&&ys}function ys(){this.agent.dirty(),this.getDownstream().dirty()}function _s(){this.agent&&this.agent.dirty()}function xs(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function ws(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=pn(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return bs(e)}):im}function bs(t){return function(e,i){var n=i.data,r=i.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(n,a);else r&&r.progress&&r.progress(e,n)}}function Ss(t){return t.data.count()}function Ms(t,e,i){var n=e.uid,r=t._pipelineMap.get(n);!r.head&&(r.head=i),r.tail&&r.tail.pipe(i),r.tail=i,i.__idxInPipeline=r.count++,i.__pipeline=r}function Ts(t){nm=null;try{t(rm,am)}catch(e){}return nm}function Cs(t,e){for(var i in e.prototype)t[i]=F}function Is(t){if(w(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function Ds(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function ks(t,e){for(var i=t.firstChild;i;){if(1===i.nodeType){var n=i.getAttribute("offset");n=n.indexOf("%")>0?parseInt(n,10)/100:n?parseFloat(n):0;var r=i.getAttribute("stop-color")||"#000000";e.addColorStop(n,r)}i=i.nextSibling}}function As(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function Ps(t){for(var e=O(t).split(fm),i=[],n=0;n<e.length;n+=2){var r=parseFloat(e[n]),a=parseFloat(e[n+1]);i.push([r,a])}return i}function Ls(t,e,i,n){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(Bs(t,e),o(r,Es(t)),!n))for(var s in vm)if(vm.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[vm[s]]=l)}var h=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new Kd;var c=e.style;null!=r.fill&&c.set(h,Os(r.fill,i)),null!=r.stroke&&c.set(u,Os(r.stroke,i)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=O(r.lineDash).split(fm)),c[u]&&"none"!==c[u]&&(e[u]=!0),e.__inheritedStyle=r}function Os(t,e){var i=e&&t&&t.match(mm);if(i){var n=O(i[1]),r=e[n];return r}return t}function Bs(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var n=null,r=[];i.replace(ym,function(t,e,i){r.push(e,i)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(n=n||pe(),s){case"translate":o=O(o).split(fm),ye(n,n,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=O(o).split(fm),xe(n,n,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=O(o).split(fm),_e(n,n,parseFloat(o[0]));break;case"skew":o=O(o).split(fm),console.warn("Skew transform is not supported yet");break;case"matrix":var o=O(o).split(fm);n[0]=parseFloat(o[0]),n[1]=parseFloat(o[1]),n[2]=parseFloat(o[2]),n[3]=parseFloat(o[3]),n[4]=parseFloat(o[4]),n[5]=parseFloat(o[5])}}e.setLocalTransform(n)}}function Es(t){var e=t.getAttribute("style"),i={};if(!e)return i;var n={};_m.lastIndex=0;for(var r;null!=(r=_m.exec(e));)n[r[1]]=r[2];for(var a in vm)vm.hasOwnProperty(a)&&null!=n[a]&&(i[vm[a]]=n[a]);return i}function zs(t,e,i){var n=e/t.width,r=i/t.height,a=Math.min(n,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+i/2];return{scale:o,position:s}}function Rs(t){return function(e,i,n){e=e&&e.toLowerCase(),rd.prototype[t].call(this,e,i,n)}}function Fs(){rd.call(this)}function Ns(t,e,i){function r(t,e){return t.__prio-e.__prio}i=i||{},"string"==typeof e&&(e=Km[e]),this.id,this.group,this._dom=t;var a="canvas",o=this._zr=fn(t,{renderer:i.renderer||a,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=us(m(o.flush,o),17);var e=n(e);e&&Iv(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new xo;var s=this._api=nl(this);si($m,r),si(Um,r),this._scheduler=new ds(this,s,Um,$m),rd.call(this,this._ecEventProcessor=new rl),this._messageCenter=new Fs,this._initEvents(),this.resize=m(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),Us(o,this),B(this)}function Hs(t,e,i){var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=Mn(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}function Ws(t){var e=t._model,i=t._scheduler;i.restorePipelines(e),i.prepareStageTasks(),js(t,"component",e,i),js(t,"chart",e,i),i.plan()}function Vs(t,e,i,n,r){function a(n){n&&n.__alive&&n[e]&&n[e](n.__model,o,t._api,i)}var o=t._model;if(!n)return void Mm(t._componentsViews.concat(t._chartsViews),a);var s={};s[n+"Id"]=i[n+"Id"],s[n+"Index"]=i[n+"Index"],s[n+"Name"]=i[n+"Name"];var l={mainType:n,query:s};r&&(l.subType=r);var h=i.excludeSeriesId;null!=h&&(h=R(pn(h))),o&&o.eachComponent(l,function(e){h&&null!=h.get(e.id)||a(t["series"===n?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function Gs(t,e){var i=t._chartsMap,n=t._scheduler;e.eachSeries(function(t){n.updateStreamModes(t,i[t.__viewId])})}function Xs(t,e){var i=t.type,n=t.escapeConnect,r=Ym[i],a=r.actionInfo,l=(a.update||"update").split(":"),h=l.pop();l=null!=l[0]&&Im(l[0]),this[Nm]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=p(t.batch,function(e){return e=s(o({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===i||"downplay"===i;Mm(u,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=a.event||d.type,f.push(d),g?Vs(this,h,t,"series"):l&&Vs(this,h,t,l.main,l.sub)},this),"none"===h||g||l||(this[Hm]?(Ws(this),Gm.update.call(this,t),this[Hm]=!1):Gm[h].call(this,t)),d=c?{type:a.event||i,escapeConnect:n,batch:f}:f[0],this[Nm]=!1,!e&&this._messageCenter.trigger(d.type,d)}function Ys(t){for(var e=this._pendingActions;e.length;){var i=e.shift();Xs.call(this,i,t)}}function qs(t){!t&&this.trigger("updated")}function Us(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[Hm]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function js(t,e,i,n){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var u=Im(t.type),c=a?Nv.getClass(u.main,u.sub):as.getClass(u.sub);r=new c,r.init(i,h),s[e]=r,o.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!a&&n.prepareView(r,t,i,h)}for(var a="component"===e,o=a?t._componentsViews:t._chartsViews,s=a?t._componentsMap:t._chartsMap,l=t._zr,h=t._api,u=0;u<o.length;u++)o[u].__alive=!1;a?i.eachComponent(function(t,e){"series"!==t&&r(e)}):i.eachSeries(r);for(var u=0;u<o.length;){var c=o[u];c.__alive?u++:(!a&&c.renderTask.dispose(),l.remove(c.group),c.dispose(i,h),o.splice(u,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function Zs(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function $s(t,e,i,n){Ks(t,e,i,n),Mm(t._chartsViews,function(t){t.__alive=!1}),Qs(t,e,i,n),Mm(t._chartsViews,function(t){t.__alive||t.remove(e,i)})}function Ks(t,e,i,n,r){Mm(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,i,n),il(r,t)})}function Qs(t,e,i,n,r){var a,o=t._scheduler;e.eachSeries(function(e){var i=t._chartsMap[e.__viewId];i.__alive=!0;var s=i.renderTask;o.updatePayload(s,n),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),i.group.silent=!!e.get("silent"),il(e,i),el(e,i)}),o.unfinished|=a,tl(t._zr,e),Kv(t._zr.dom,e)}function Js(t,e){Mm(Zm,function(i){i(t,e)})}function tl(t,e){var i=t.storage,n=0;i.traverse(function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!Hc.node&&i.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function el(t,e){var i=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}function il(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function nl(t){var e=t._coordSysMgr;return o(new _o(t),{getCoordinateSystems:m(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var i=e.__ecComponentInfo;if(null!=i)return t._model.getComponent(i.mainType,i.index);e=e.parent}}})}function rl(){this.eventInfo}function al(t){function e(t,e){for(var i=0;i<t.length;i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";Mm(qm,function(o,s){t._messageCenter.on(s,function(o){if(ty[t.group]&&t[a]!==i){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];Mm(Jm,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),Mm(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}function ol(t,e,i){var n=ul(t);if(n)return n;var r=new Ns(t,e,i);return r.id="ec_"+ey++,Jm[r.id]=r,Cn(t,ny,r.id),al(r),r}function sl(t){if(_(t)){var e=t;t=null,Mm(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+iy++,Mm(e,function(e){e.group=t})}return ty[t]=!0,t}function ll(t){ty[t]=!1}function hl(t){"string"==typeof t?t=Jm[t]:t instanceof Ns||(t=ul(t)),t instanceof Ns&&!t.isDisposed()&&t.dispose()}function ul(t){return Jm[In(t,ny)]}function cl(t){return Jm[t]}function dl(t,e){Km[t]=e}function fl(t){jm.push(t)}function pl(t,e){wl(Um,t,e,Pm)}function gl(t){Zm.push(t)}function vl(t,e,i){"function"==typeof e&&(i=e,e="");var n=Cm(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,Sm(Wm.test(n)&&Wm.test(e)),Ym[n]||(Ym[n]={action:i,actionInfo:t}),qm[e]=n}function ml(t,e){xo.register(t,e)}function yl(t){var e=xo.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function _l(t,e){wl($m,t,e,Om,"layout")}function xl(t,e){wl($m,t,e,Em,"visual")}function wl(t,e,i,n,r){(Tm(e)||Cm(e))&&(i=e,e=n);var a=ds.wrapStageHandler(i,r);return a.__prio=e,a.__raw=i,t.push(a),a}function bl(t,e){Qm[t]=e}function Sl(t){return Kg.extend(t)}function Ml(t){return Nv.extend(t)}function Tl(t){return Fv.extend(t)}function Cl(t){return as.extend(t)}function Il(t){i("createCanvas",t)}function Dl(t,e,i){wm.registerMap(t,e,i)}function kl(t){var e=wm.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function Al(t,e,i){i=i||{};var n,r,a,o,s=i.byIndex,l=i.stackedCoordDimension,h=!(!t||!t.get("stack"));if(f(e,function(t,i){w(t)&&(e[i]=t={name:t}),h&&!t.isExtraCoord&&(s||n||!t.ordinalMeta||(n=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||n||(s=!0),r){a="__\x00ecstackresult",o="__\x00ecstackedover",n&&(n.createInvertedIndices=!0);var u=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===u&&d++}),e.push({name:a,coordDim:u,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:o,coordDim:o,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:n&&n.name,isStackedByIndex:s,stackedOverDimension:o,stackResultDimension:a}}function Pl(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Ll(t,e){return Pl(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Ol(t){return t.get("stack")||oy+t.seriesIndex}function Bl(t){return t.dim+t.index}function El(t,e){var i=[];return e.eachSeriesByType(t,function(t){Hl(t)&&!Wl(t)&&i.push(t)}),i}function zl(t){var e=[];return f(t,function(t){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=r.getExtent(),o="category"===r.type?r.getBandWidth():Math.abs(a[1]-a[0])/i.count(),s=Ca(t.get("barWidth"),o),l=Ca(t.get("barMaxWidth"),o),h=t.get("barGap"),u=t.get("barCategoryGap");e.push({bandWidth:o,barWidth:s,barMaxWidth:l,barGap:h,barCategoryGap:u,axisKey:Bl(r),stackId:Ol(t)})}),Rl(e)}function Rl(t){var e={};f(t,function(t){var i=t.axisKey,n=t.bandWidth,r=e[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=r.stacks;e[i]=r;var o=t.stackId;a[o]||r.autoWidthCount++,a[o]=a[o]||{width:0,maxWidth:0};var s=t.barWidth;s&&!a[o].width&&(a[o].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(a[o].maxWidth=l);var h=t.barGap;null!=h&&(r.gap=h);var u=t.barCategoryGap;null!=u&&(r.categoryGap=u)});var i={};return f(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=Ca(t.categoryGap,r),o=Ca(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-a)/(l+(l-1)*o);h=Math.max(h,0),f(n,function(t){var e=t.maxWidth;e&&h>e&&(e=Math.min(e,s),t.width&&(e=Math.min(e,t.width)),s-=e,t.width=e,l--)}),h=(s-a)/(l+(l-1)*o),h=Math.max(h,0);var u,c=0;f(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+o)}),u&&(c-=u.width*o);var d=-c/2;f(n,function(t,n){i[e][n]=i[e][n]||{offset:d,width:t.width},d+=t.width*(1+o)})}),i}function Fl(t,e,i){if(t&&e){var n=t[Bl(e)];return null!=n&&null!=i&&(n=n[Ol(i)]),n}}function Nl(t,e){var i=El(t,e),n=zl(i),r={};f(i,function(t){var e=t.getData(),i=t.coordinateSystem,a=i.getBaseAxis(),o=Ol(t),s=n[Bl(a)][o],l=s.offset,h=s.width,u=i.getOtherAxis(a),c=t.get("barMinHeight")||0;r[o]=r[o]||[],e.setLayout({offset:l,size:h});for(var d=e.mapDimension(u.dim),f=e.mapDimension(a.dim),p=Pl(e,d),g=u.isHorizontal(),v=Vl(a,u,p),m=0,y=e.count();y>m;m++){var _=e.get(d,m),x=e.get(f,m);if(!isNaN(_)){var w=_>=0?"p":"n",b=v;p&&(r[o][x]||(r[o][x]={p:v,n:v}),b=r[o][x][w]);var S,M,T,C;if(g){var I=i.dataToPoint([_,x]);S=b,M=I[1]+l,T=I[0]-v,C=h,Math.abs(T)<c&&(T=(0>T?-1:1)*c),p&&(r[o][x][w]+=T)}else{var I=i.dataToPoint([x,_]);S=I[0]+l,M=b,T=h,C=I[1]-v,Math.abs(C)<c&&(C=(0>=C?-1:1)*c),p&&(r[o][x][w]+=C)}e.setItemLayout(m,{x:S,y:M,width:T,height:C})}}},this)}function Hl(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Wl(t){return t.pipelineContext&&t.pipelineContext.large}function Vl(t,e){var i,n,r=e.getGlobalExtent();r[0]>r[1]?(i=r[1],n=r[0]):(i=r[0],n=r[1]);var a=e.toGlobalCoord(e.dataToCoord(0));return i>a&&(a=i),a>n&&(a=n),a}function Gl(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function Xl(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function Yl(t){return t._map||(t._map=R(t.categories))}function ql(t){return b(t)&&null!=t.value?t.value:t+""}function Ul(t,e,i,n){var r={},a=t[1]-t[0],o=r.interval=Ea(a/e,!0);null!=i&&i>o&&(o=r.interval=i),null!=n&&o>n&&(o=r.interval=n);var s=r.intervalPrecision=jl(o),l=r.niceTickExtent=[fy(Math.ceil(t[0]/o)*o,s),fy(Math.floor(t[1]/o)*o,s)];return $l(l,t),r}function jl(t){return Da(t)+2}function Zl(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function $l(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Zl(t,0,e),Zl(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function Kl(t,e,i,n){var r=[];if(!t)return r;var a=1e4;e[0]<i[0]&&r.push(e[0]);for(var o=i[0];o<=i[1]&&(r.push(o),o=fy(o+t,n),o!==r[r.length-1]);)if(r.length>a)return[];return e[1]>(r.length?r[r.length-1]:i[1])&&r.push(e[1]),r}function Ql(t,e){return ky(t,Dy(e))}function Jl(t,e){var i,n,r,a=t.type,o=e.getMin(),s=e.getMax(),l=null!=o,h=null!=s,u=t.getExtent();"ordinal"===a?i=e.getCategories().length:(n=e.get("boundaryGap"),_(n)||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(n=[0,0]),n[0]=Ca(n[0],1),n[1]=Ca(n[1],1),r=u[1]-u[0]||Math.abs(u[0])),null==o&&(o="ordinal"===a?i?0:0/0:u[0]-n[0]*r),null==s&&(s="ordinal"===a?i?i-1:0/0:u[1]+n[1]*r),"dataMin"===o?o=u[0]:"function"==typeof o&&(o=o({min:u[0],max:u[1]})),"dataMax"===s?s=u[1]:"function"==typeof s&&(s=s({min:u[0],max:u[1]})),(null==o||!isFinite(o))&&(o=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(C(o)||C(s)||"ordinal"===a&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(o>0&&s>0&&!l&&(o=0),0>o&&0>s&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===a){var d,p=El("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=zl(p),v=th(o,s,e,g);o=v.min,s=v.max}}return[o,s]}function th(t,e,i,n){var r=i.axis.getExtent(),a=r[1]-r[0],o=Fl(n,i.axis);if(void 0===o)return{min:t,max:e};var s=1/0;f(o,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(o,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=1-(s+l)/a,d=u/c-u;return e+=d*(l/h),t-=d*(s/h),{min:t,max:e}}function eh(t,e){var i=Jl(t,e),n=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:a,fixMin:n,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function ih(t,e){if(e=e||t.get("type"))switch(e){case"category":return new dy(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new gy;default:return(Gl.getClass(e)||gy).create(t)}}function nh(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)}function rh(t){var e=t.getLabelModel().get("formatter"),i="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(i){return i=t.scale.getLabel(i),e.replace("{value}",null!=i?i:"")}}(e):"function"==typeof e?function(n,r){return null!=i&&(r=n-i),e(ah(t,n),r)
}:function(e){return t.scale.getLabel(e)}}function ah(t,e){return"category"===t.type?t.scale.getLabel(e):e}function oh(t){var e=t.model,i=t.scale;if(e.get("axisLabel.show")&&!i.isBlank()){var n,r,a="category"===t.type,o=i.getExtent();a?r=i.count():(n=i.getTicks(),r=n.length);var s,l=t.getLabelModel(),h=rh(t),u=1;r>40&&(u=Math.ceil(r/40));for(var c=0;r>c;c+=u){var d=n?n[c]:o[0]+c,f=h(d),p=l.getTextRect(f),g=sh(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function sh(t,e){var i=e*Math.PI/180,n=t.plain(),r=n.width,a=n.height,o=r*Math.cos(i)+a*Math.sin(i),s=r*Math.sin(i)+a*Math.cos(i),l=new Je(n.x,n.y,o,s);return l}function lh(t){var e=t.get("interval");return null==e?"auto":e}function hh(t){return"category"===t.type&&0===lh(t.getLabelModel())}function uh(t){return this._axes[t]}function ch(t){Ey.call(this,t)}function dh(t){return"category"===t.type?ph(t):mh(t)}function fh(t,e){return"category"===t.type?vh(t,e):{ticks:t.scale.getTicks()}}function ph(t){var e=t.getLabelModel(),i=gh(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:i.labelCategoryInterval}:i}function gh(t,e){var i=yh(t,"labels"),n=lh(e),r=_h(i,n);if(r)return r;var a,o;return x(n)?a=Th(t,n):(o="auto"===n?wh(t):n,a=Mh(t,o)),xh(i,n,{labels:a,labelCategoryInterval:o})}function vh(t,e){var i=yh(t,"ticks"),n=lh(e),r=_h(i,n);if(r)return r;var a,o;if((!e.get("show")||t.scale.isBlank())&&(a=[]),x(n))a=Th(t,n,!0);else if("auto"===n){var s=gh(t,t.getLabelModel());o=s.labelCategoryInterval,a=p(s.labels,function(t){return t.tickValue})}else o=n,a=Mh(t,o,!0);return xh(i,n,{ticks:a,tickCategoryInterval:o})}function mh(t){var e=t.scale.getTicks(),i=rh(t);return{labels:p(e,function(e,n){return{formattedLabel:i(e,n),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function yh(t,e){return zy(t)[e]||(zy(t)[e]=[])}function _h(t,e){for(var i=0;i<t.length;i++)if(t[i].key===e)return t[i].value}function xh(t,e,i){return t.push({key:e,value:i}),i}function wh(t){var e=zy(t).autoInterval;return null!=e?e:zy(t).autoInterval=t.calculateCategoryInterval()}function bh(t){var e=Sh(t),i=rh(t),n=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,a=r.getExtent(),o=r.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],h=t.dataToCoord(l+1)-t.dataToCoord(l),u=Math.abs(h*Math.cos(n)),c=Math.abs(h*Math.sin(n)),d=0,f=0;l<=a[1];l+=s){var p=0,g=0,v=yi(i(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/u,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(m,y))),x=zy(t.model),w=x.lastAutoInterval,b=x.lastTickCount;return null!=w&&null!=b&&Math.abs(w-_)<=1&&Math.abs(b-o)<=1&&w>_?_=w:(x.lastTickCount=o,x.lastAutoInterval=_),_}function Sh(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function Mh(t,e,i){function n(t){l.push(i?t:{formattedLabel:r(t),rawLabel:a.getLabel(t),tickValue:t})}var r=rh(t),a=t.scale,o=a.getExtent(),s=t.getLabelModel(),l=[],h=Math.max((e||0)+1,1),u=o[0],c=a.count();0!==u&&h>1&&c/h>2&&(u=Math.round(Math.ceil(u/h)*h));var d=hh(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&u!==o[0]&&n(o[0]);for(var g=u;g<=o[1];g+=h)n(g);return p&&g!==o[1]&&n(o[1]),l}function Th(t,e,i){var n=t.scale,r=rh(t),a=[];return f(n.getTicks(),function(t){var o=n.getLabel(t);e(t,o)&&a.push(i?t:{formattedLabel:r(t),rawLabel:o,tickValue:t})}),a}function Ch(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}function Ih(t,e,i,n,r){function a(t,e){return u?t>e:e>t}var o=e.length;if(t.onBand&&!n&&o){var s,l=t.getExtent();if(1===o)e[0].coord=l[0],s=e[1]={coord:l[0]};else{var h=e[1].coord-e[0].coord;f(e,function(t){t.coord-=h/2;var e=e||0;e%2>0&&(t.coord-=h/(2*(e+1)))}),s={coord:e[o-1].coord+h},e.push(s)}var u=l[0]>l[1];a(e[0].coord,l[0])&&(r?e[0].coord=l[0]:e.shift()),r&&a(l[0],e[0].coord)&&e.unshift({coord:l[0]}),a(l[1],s.coord)&&(r?s.coord=l[1]:e.pop()),r&&a(s.coord,l[1])&&e.push({coord:l[1]})}}function Dh(t,e){return e.type||(e.data?"category":"value")}function kh(t,e){return t.getCoordSysModel()===e}function Ah(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}function Ph(t,e,i,n){function r(t){return t.dim+"_"+t.index}i.getAxesOnZeroOf=function(){return a?[a]:[]};var a,o=t[e],s=i.model,l=s.get("axisLine.onZero"),h=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=h)Lh(o[h])&&(a=o[h]);else for(var u in o)if(o.hasOwnProperty(u)&&Lh(o[u])&&!n[r(o[u])]){a=o[u];break}a&&(n[r(a)]=!0)}}function Lh(t){return t&&"category"!==t.type&&"time"!==t.type&&nh(t)}function Oh(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function Bh(t){return p(jy,function(e){var i=t.getReferringComponents(e)[0];return i})}function Eh(t){return"cartesian2d"===t.get("coordinateSystem")}function zh(t){return t}function Rh(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||zh,this._newKeyGetter=n||zh,this.context=r}function Fh(t,e,i,n,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[n](t[a],a),s=e[o];null==s?(i.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}function Nh(t){var e={},i=e.encode={},n=R(),r=[],a=[];f(t.dimensions,function(e){var o=t.getDimensionInfo(e),s=o.coordDim;if(s){var l=i[s];i.hasOwnProperty(s)||(l=i[s]=[]),l[o.coordDimIndex]=e,o.isExtraCoord||(n.set(s,1),Wh(o.type)&&(r[0]=e)),o.defaultTooltip&&a.push(e)}Zy.each(function(t,e){var n=i[e];i.hasOwnProperty(e)||(n=i[e]=[]);var r=o.otherDims[e];null!=r&&r!==!1&&(n[r]=o.name)})});var o=[],s={};n.each(function(t,e){var n=i[e];s[e]=n[0],o=o.concat(n)}),e.dataDimsOnCoord=o,e.encodeFirstDimNotExtra=s;var l=i.label;l&&l.length&&(r=l.slice());var h=i.tooltip;return h&&h.length?a=h.slice():a.length||(a=r.slice()),i.defaultedLabel=r,i.defaultedTooltip=a,e}function Hh(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function Wh(t){return!("ordinal"===t||"time"===t)}function Vh(t){return t._rawCount>65535?e_:n_}function Gh(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function Xh(t,e){f(r_.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,f(a_,function(i){t[i]=n(e[i])}),t._calculationInfo=o(e._calculationInfo)}function Yh(t,e,i,n,r){var a=t_[e.type],o=n-1,s=e.name,l=t[s][o];if(l&&l.length<i){for(var h=new a(Math.min(r-o*i,i)),u=0;u<l.length;u++)h[u]=l[u];t[s][o]=h}for(var c=n*i;r>c;c+=i)t[s].push(new a(Math.min(r-c,i)))}function qh(t){var e=t._invertedIndicesMap;f(e,function(i,n){var r=t._dimensionInfos[n],a=r.ordinalMeta;if(a){i=e[n]=new i_(a.categories.length);for(var o=0;o<i.length;o++)i[o]=Qy;for(var o=0;o<t._count;o++)i[t.get(n,o)]=o}})}function Uh(t,e,i){var n;if(null!=e){var r=t._chunkSize,a=Math.floor(i/r),o=i%r,s=t.dimensions[e],l=t._storage[s][a];if(l){n=l[o];var h=t._dimensionInfos[s].ordinalMeta;h&&h.categories.length&&(n=h.categories[n])}}return n}function jh(t){return t}function Zh(t){return t<this._count&&t>=0?this._indices[t]:-1}function $h(t,e){var i=t._idList[e];return null==i&&(i=Uh(t,t._idDimIdx,e)),null==i&&(i=Jy+e),i}function Kh(t){return _(t)||(t=[t]),t}function Qh(t,e){var i=t.dimensions,n=new o_(p(i,t.getDimensionInfo,t),t.hostModel);Xh(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i.length;o++){var s=i[o];a[s]&&(h(e,s)>=0?(r[s]=Jh(a[s]),n._rawExtent[s]=tu(),n._extent[s]=null):r[s]=a[s])}return n}function Jh(t){for(var e=new Array(t.length),i=0;i<t.length;i++)e[i]=Gh(t[i]);return e}function tu(){return[1/0,-1/0]}function eu(t,e,i){function r(t,e,i){null!=Zy.get(e)?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,u.set(e,!0))}Qa.isInstance(e)||(e=Qa.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var a=(i.dimsDef||[]).slice(),l=R(i.encodeDef),h=R(),u=R(),c=[],d=iu(e,t,a,i.dimCount),p=0;d>p;p++){var g=a[p]=o({},b(a[p])?a[p]:{name:a[p]}),v=g.name,m=c[p]={otherDims:{}};null!=v&&null==h.get(v)&&(m.name=m.displayName=v,h.set(v,p)),null!=g.type&&(m.type=g.type),null!=g.displayName&&(m.displayName=g.displayName)}l.each(function(t,e){if(t=pn(t).slice(),1===t.length&&t[0]<0)return void l.set(e,!1);var i=l.set(e,[]);f(t,function(t,n){w(t)&&(t=h.get(t)),null!=t&&d>t&&(i[n]=t,r(c[t],e,n))})});var y=0;f(t,function(t){var e,t,i,a;if(w(t))e=t,t={};else{e=t.name;var o=t.ordinalMeta;t.ordinalMeta=null,t=n(t),t.ordinalMeta=o,i=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var h=l.get(e);if(h!==!1){var h=pn(h);if(!h.length)for(var u=0;u<(i&&i.length||1);u++){for(;y<c.length&&null!=c[y].coordDim;)y++;y<c.length&&h.push(y++)}f(h,function(n,o){var l=c[n];if(r(s(l,t),e,o),null==l.name&&i){var h=i[o];!b(h)&&(h={name:h}),l.name=l.displayName=h.name,l.defaultTooltip=h.defaultTooltip}a&&s(l.otherDims,a)})}});var _=i.generateCoord,x=i.generateCoordCount,S=null!=x;x=_?x||1:0;for(var M=_||"value",T=0;d>T;T++){var m=c[T]=c[T]||{},C=m.coordDim;null==C&&(m.coordDim=nu(M,u,S),m.coordDimIndex=0,(!_||0>=x)&&(m.isExtraCoord=!0),x--),null==m.name&&(m.name=nu(m.coordDim,h)),null==m.type&&ho(e,T,m.name)&&(m.type="ordinal")}return c}function iu(t,e,i,n){var r=Math.max(t.dimensionsDetectCount||1,e.length,i.length,n||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function nu(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}function ru(t,e,i){i=i||{},Qa.isInstance(t)||(t=Qa.seriesDataToSource(t));var n,r=e.get("coordinateSystem"),a=xo.get(r),o=$a(e);o&&(n=p(o.coordSysDims,function(t){var e={name:t},i=o.axisMap.get(t);if(i){var n=i.get("type");e.type=Hh(n)}return e})),n||(n=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"]);var s,l,h=h_(t,{coordDimensions:n,generateCoord:i.generateCoord});o&&f(h,function(t,e){var i=t.coordDim,n=o.categoryAxisMap.get(i);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=Al(e,h),c=new o_(h,e);c.setCalculationInfo(u);var d=null!=s&&au(t)?function(t,e,i,n){return n===s?i:this.defaultDimValueGetter(t,e,i,n)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function au(t){if(t.sourceFormat===nv){var e=ou(t.data||[]);return null!=e&&!_(vn(e))}}function ou(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function su(t,e){var i=t.mapDimension("defaultedLabel",!0),n=i.length;if(1===n)return qo(t,e,i[0]);if(n){for(var r=[],a=0;a<i.length;a++){var o=qo(t,e,i[a]);r.push(o)}return r.join(" ")}}function lu(t,e,i,n,r,a){var o=i.getModel("label"),s=i.getModel("emphasis.label");Qr(t,e,o,s,{labelFetcher:r,labelDataIndex:a,defaultText:su(r.getData(),a),isRectText:!0,autoColor:n}),hu(t),hu(e)}function hu(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function uu(t,e,i){i.style.text=null,ha(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function cu(t,e,i){i.style.text=null,ha(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}function du(t,e,i,n,r,a,o,l){var h=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),c=n.getModel("itemStyle"),d=n.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(s({fill:h,opacity:u},c.getBarItemStyle()));var f=n.getShallow("cursor");f&&t.attr("cursor",f);var p=o?r.height>0?"bottom":"top":r.width>0?"left":"right";l||lu(t.style,d,n,h,a,i,p),$r(t,d)}function fu(t,e){var i=t.get(f_)||0;return Math.min(i,Math.abs(e.width),Math.abs(e.height))}function pu(t,e,i){var n=t.getData(),r=[],a=n.getLayout("valueAxisHorizontal")?1:0;r[1-a]=n.getLayout("valueAxisStart");var o=new v_({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:r,__valueIdx:a});e.add(o),gu(o,t,n)}function gu(t,e,i){var n=i.getVisual("borderColor")||i.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}function vu(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function mu(t,e,i,n,r,a,o){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?Pr(t.slice(8),new Je(e,i,n,r),o?"center":"cover"):0===t.indexOf("path://")?Ar(t.slice(7),{},new Je(e,i,n,r),o?"center":"cover"):new M_({shape:{symbolType:t,x:e,y:i,width:n,height:r}}),l.__isEmptyBrush=s,l.setColor=vu,l.setColor(a),l}function yu(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e}function _u(t,e,i,n){var r,a,o=Aa(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return Pa(o-T_/2)?(a=l?"bottom":"top",r="center"):Pa(o-1.5*T_)?(a=l?"top":"bottom",r="center"):(a="middle",r=1.5*T_>o&&o>T_/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function xu(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function wu(t,e,i){if(!hh(t.axis)){var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],i=i||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],h=i[0],u=i[1],c=i[i.length-1],d=i[i.length-2];n===!1?(bu(a),bu(h)):Su(a,o)&&(n?(bu(o),bu(u)):(bu(a),bu(h))),r===!1?(bu(s),bu(c)):Su(l,s)&&(r?(bu(l),bu(d)):(bu(s),bu(c)))}}function bu(t){t&&(t.ignore=!0)}function Su(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=ge([]);return _e(r,r,-t.rotation),i.applyTransform(me([],r,t.getLocalTransform())),n.applyTransform(me([],r,e.getLocalTransform())),i.intersect(n)}}function Mu(t){return"middle"===t||"center"===t}function Tu(t,e,i){var n=e.axis;if(e.get("axisTick.show")&&!n.scale.isBlank()){for(var r=e.getModel("axisTick"),a=r.getModel("lineStyle"),o=r.get("length"),l=n.getTicksCoords(),h=[],u=[],c=t._transform,d=[],f=0;f<l.length;f++){var p=l[f].coord;h[0]=p,h[1]=0,u[0]=p,u[1]=i.tickDirection*o,c&&(Z(h,h,c),Z(u,u,c));var g=new dg(Br({anid:"tick_"+l[f].tickValue,shape:{x1:h[0],y1:h[1],x2:u[0],y2:u[1]},style:s(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(g),d.push(g)}return d}}function Cu(t,e,i){var n=e.axis,r=I(i.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var a=e.getModel("axisLabel"),o=a.get("margin"),s=n.getViewLabels(),l=(I(i.labelRotate,a.get("rotate"))||0)*T_/180,h=D_(i.rotation,l,i.labelDirection),u=e.getCategories(!0),c=[],d=xu(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=a;u&&u[l]&&u[l].textStyle&&(v=new ya(u[l].textStyle,a,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=n.dataToCoord(l),_=[y,i.labelOffset+i.labelDirection*o],x=new Qp({anid:"label_"+l,position:_,rotation:h.rotation,silent:d,z2:10});Jr(x.style,v,{text:f,textAlign:v.getShallow("align",!0)||h.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,textFill:"function"==typeof m?m("category"===n.type?g:"value"===n.type?l+"":l,s):m}),p&&(x.eventData=yu(e),x.eventData.targetType="axisLabel",x.eventData.value=g),t._dumbGroup.add(x),x.updateTransform(),c.push(x),t.group.add(x),x.decomposeTransform()}),c}}function Iu(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return Du(i,t,e),i.seriesInvolved&&Au(i,t),i}function Du(t,e,i){var n=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),a=r.get("link",!0)||[],o=[];k_(i.getCoordinateSystems(),function(i){function s(n,s,l){var u=l.model.getModel("axisPointer",r),d=u.get("show");if(d&&("auto"!==d||n||zu(u))){null==s&&(s=u.get("triggerTooltip")),u=n?ku(l,c,r,e,n,s):u;var f=u.get("snap"),p=Ru(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:i,axisPointerModel:u,triggerTooltip:s,involveSeries:g,snap:f,useHandle:zu(u),seriesModels:[]};h[p]=v,t.seriesInvolved|=g;var m=Pu(a,l);if(null!=m){var y=o[m]||(o[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=a[m].mapper,v.linkGroup=y}}}if(i.axisPointerEnabled){var l=Ru(i.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=i;var u=i.model,c=u.getModel("tooltip",n);if(k_(i.getAxes(),A_(s,!1,null)),i.getTooltipAxes&&n&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=i.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&k_(p.baseAxes,A_(s,f?"cross":!0,d)),f&&k_(p.otherAxes,A_(s,"cross",!1))}}})}function ku(t,e,i,r,a,o){var l=e.getModel("axisPointer"),h={};k_(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=n(l.get(t))}),h.snap="category"!==t.type&&!!o,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===a){var c=l.get("label.show");if(u.show=null!=c?c:!0,!o){var d=h.lineStyle=l.get("crossStyle");d&&s(u,d.textStyle)}}return t.model.getModel("axisPointer",new ya(h,i,r))}function Au(t,e){e.eachSeries(function(e){var i=e.coordinateSystem,n=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);i&&"none"!==n&&n!==!1&&"item"!==n&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&k_(t.coordSysAxesInfo[Ru(i.model)],function(t){var n=t.axis;i.getAxis(n.dim)===n&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function Pu(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(Lu(a[n+"AxisId"],i.id)||Lu(a[n+"AxisIndex"],i.componentIndex)||Lu(a[n+"AxisName"],i.name))return r}}function Lu(t,e){return"all"===t||_(t)&&h(t,e)>=0||t===e}function Ou(t){var e=Bu(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,a=i.get("status"),o=i.get("value");null!=o&&(o=n.parse(o));var s=zu(i);null==a&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function Bu(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Ru(t)]}function Eu(t){var e=Bu(t);return e&&e.axisPointerModel}function zu(t){return!!t.get("handle.show")}function Ru(t){return t.type+"||"+t.id}function Fu(t,e,i,n,r,a){var o=P_.getAxisPointerClass(t.axisPointerClass);if(o){var s=Eu(e);s?(t._axisPointer||(t._axisPointer=new o)).render(e,s,n,a):Nu(t,n)}}function Nu(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}function Hu(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,a={},o=r.getAxesOnZeroOf()[0],s=r.position,l=o?"onZero":s,h=r.dim,u=n.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===h?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}a.position=["y"===h?p[d[l]]:c[0],"x"===h?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===h?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),I(i.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-m:m,a.z2=1,a}function Wu(t,e,i){var n,r={},a="toggleSelected"===t;return i.eachComponent("legend",function(i){a&&null!=n?i[n?"select":"unSelect"](e.name):(i[t](e.name),n=i.isSelected(e.name));var o=i.getData();f(o,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);r[e]=r.hasOwnProperty(e)?r[e]&&n:n}})}),{name:e.name,selected:r}}function Vu(t,e){var i=Ng(e.get("padding")),n=e.getItemStyle(["color","opacity"]);n.fill=e.get("backgroundColor");var t=new ug({shape:{x:t.x-i[3],y:t.y-i[0],width:t.width+i[1]+i[3],height:t.height+i[0]+i[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1});return t}function Gu(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function Xu(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:n})}function Yu(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:n})}function qu(t,e,i){var n=t.getOrient(),r=[1,1];r[n.index]=0,Ya(e,i,{type:"box",ignoreSize:r})}function Uu(t,e,i,n,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void i.showPointer(t,e);var s=ju(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!n&&t.snap&&a.containData(h)&&null!=h&&(e=h),i.showPointer(t,e,l,r),i.showTooltip(t,s,h)}}function ju(t,e){var i=e.axis,n=i.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return j_(e.seriesModels,function(e){var l,h,u=e.getData().mapDimension(n,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(u,t,i);h=c.dataIndices,l=c.nestestValue}else{if(h=e.getData().indicesOfNearest(u[0],t,"category"===i.type?.5:null),!h.length)return;l=e.getData().get(u[0],h[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);o>=f&&((o>f||d>=0&&0>s)&&(o=f,s=d,r=l,a.length=0),j_(h,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function Zu(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function $u(t,e,i,n){var r=i.payloadBatch,a=e.axis,o=a.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=Ru(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Ku(t,e,i){var n=i.axesInfo=[];j_(e,function(e,i){var r=e.axisPointerModel.option,a=t[i];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&n.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Qu(t,e,i,n){if(ic(e)||!t.list.length)return void n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function Ju(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",a=$_(n)[r]||{},o=$_(n)[r]={};j_(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&j_(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var s=[],l=[];f(a,function(t,e){!o[e]&&l.push(t)}),f(o,function(t,e){!a[e]&&s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function tc(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function ec(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function ic(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function nc(t,e,i){if(!Hc.node){var n=e.getZr();Q_(n).records||(Q_(n).records={}),rc(n,e);var r=Q_(n).records[t]||(Q_(n).records[t]={});r.handler=i}}function rc(t,e){function i(i,n){t.on(i,function(i){var r=lc(e);J_(Q_(t).records,function(t){t&&n(t,i,r.dispatchAction)}),ac(r.pendings,e)})}Q_(t).initialized||(Q_(t).initialized=!0,i("click",y(sc,"click")),i("mousemove",y(sc,"mousemove")),i("globalout",oc))}function ac(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function oc(t,e,i){t.handler("leave",null,i)}function sc(t,e,i,n){e.handler(t,i,n)}function lc(t){var e={showTip:[],hideTip:[]},i=function(n){var r=e[n.type];r?r.push(n):(n.dispatchAction=i,t.dispatchAction(n))};return{dispatchAction:i,pendings:e}}function hc(t,e){if(!Hc.node){var i=e.getZr(),n=(Q_(i).records||{})[t];n&&(Q_(i).records[t]=null)}}function uc(){}function cc(t,e,i,n){dc(ex(i).lastProp,n)||(ex(i).lastProp=n,e?ha(i,n,t):(i.stopAnimation(),i.attr(n)))}function dc(t,e){if(b(t)&&b(e)){var i=!0;return f(e,function(e,n){i=i&&dc(t[n],e)}),!!i}return t===e}function fc(t,e){t[e.get("label.show")?"show":"hide"]()}function pc(t){return{position:t.position.slice(),rotation:t.rotation||0}}function gc(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function vc(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle(),e.fill=null):"shadow"===i&&(e=n.getAreaStyle(),e.stroke=null),e}function mc(t,e,i,n,r){var a=i.get("value"),o=_c(a,e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),s=i.getModel("label"),l=Ng(s.get("padding")||0),h=s.getFont(),u=yi(o,h),c=r.position,d=u.width+l[1]+l[3],f=u.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),yc(c,d,f,n);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:o,textFont:h,textFill:s.getTextColor(),textPosition:"inside",fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function yc(t,e,i,n){var r=n.getWidth(),a=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,a)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function _c(t,e,i,n,r){t=e.scale.parse(t);var a=e.scale.getLabel(t,{precision:r.precision}),o=r.formatter;if(o){var s={value:ah(e,t),seriesData:[]};f(n,function(t){var e=i.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,r=e&&e.getDataParams(n);r&&s.seriesData.push(r)}),w(o)?a=o.replace("{value}",a):x(o)&&(a=o(s))}return a}function xc(t,e,i){var n=pe();return _e(n,n,i.rotation),ye(n,n,i.position),da([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}function wc(t,e,i,n,r,a){var o=C_.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),mc(e,n,r,a,{position:xc(n.axis,t,i),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function bc(t,e,i){return i=i||0,{x1:t[i],y1:t[1-i],x2:e[i],y2:e[1-i]}}function Sc(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}function Mc(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}function Tc(t){return"x"===t.dim?0:1}function Cc(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return p(lx,function(t){return t+"transition:"+i}).join(";")}function Ic(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t.getFont()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),ox(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function Dc(t){var e=[],i=t.get("transitionDuration"),n=t.get("backgroundColor"),r=t.getModel("textStyle"),a=t.get("padding");return i&&e.push(Cc(i)),n&&(Hc.canvasSupported?e.push("background-Color:"+n):(e.push("background-Color:#"+Ee(n)),e.push("filter:alpha(opacity=70)"))),ox(["width","color","radius"],function(i){var n="border-"+i,r=sx(n),a=t.get(r);null!=a&&e.push(n+":"+a+("color"===i?"":"px"))}),e.push(Ic(r)),null!=a&&e.push("padding:"+Ng(a).join("px ")+"px"),e.join(";")+";"}function kc(t,e){if(Hc.wxa)return null;var i=document.createElement("div"),n=this._zr=e.getZr();this.el=i,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r._enterable){var i=n.handler;ae(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function Ac(t){this._zr=t.getZr(),this._show=!1,this._hideTimeout}function Pc(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(ya.isInstance(i)&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new ya(i,e,e.ecModel))}return e}function Lc(t,e){return t.dispatchAction||m(e.dispatchAction,e)}function Oc(t,e,i,n,r,a,o){var s=i.getOuterSize(),l=s.width,h=s.height;return null!=a&&(t+l+a>n?t-=l+a:t+=a),null!=o&&(e+h+o>r?e-=h+o:e+=o),[t,e]}function Bc(t,e,i,n,r){var a=i.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,n)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function Ec(t,e,i){var n=i[0],r=i[1],a=5,o=0,s=0,l=e.width,h=e.height;switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+h/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-n/2,s=e.y+h+a;break;case"left":o=e.x-n-a,s=e.y+h/2-r/2;break;case"right":o=e.x+l+a,s=e.y+h/2-r/2}return[o,s]}function zc(t){return"center"===t||"middle"===t}var Rc=2311,Fc=function(){return Rc++},Nc={};Nc="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var Hc=Nc,Wc={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Vc={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Gc=Object.prototype.toString,Xc=Array.prototype,Yc=Xc.forEach,qc=Xc.filter,Uc=Xc.slice,jc=Xc.map,Zc=Xc.reduce,$c={},Kc=function(){return $c.createCanvas()};$c.createCanvas=function(){return document.createElement("canvas")};var Qc,Jc="__ec_primitive__";z.prototype={constructor:z,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=m(t,e));for(var i in this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var td="undefined"==typeof Float32Array?Array:Float32Array,ed=U,id=j;Q.prototype={constructor:Q,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(J(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(J(e,t),"drag",t.event);var o=this.findHover(i,n,e).target,s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(J(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(J(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(J(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(J(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var nd=Array.prototype.slice,rd=function(t){this._$handlers={},this._$eventProcessor=t
};rd.prototype={constructor:rd,one:function(t,e,i,n){return ee(this,t,e,i,n,!0)},on:function(t,e,i,n){return ee(this,t,e,i,n,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t].length;a>r;r++)i[t][r].h!==e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>3&&(n=nd.call(n,1));for(var a=e.length,o=0;a>o;){var s=e[o];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))o++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,n[1]);break;case 3:s.h.call(s.ctx,n[1],n[2]);break;default:s.h.apply(s.ctx,n)}s.one?(e.splice(o,1),a--):o++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>4&&(n=nd.call(n,1,n.length-1));for(var a=n[n.length-1],o=e.length,s=0;o>s;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(a);break;case 2:l.h.call(a,n[1]);break;case 3:l.h.call(a,n[1],n[2]);break;default:l.h.apply(a,n)}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var ad="undefined"!=typeof window&&!!window.addEventListener,od=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,sd=ad?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},ld=function(){this._track=[]};ld.prototype={constructor:ld,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},a=0,o=n.length;o>a;a++){var s=n[a],l=ne(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in hd)if(hd.hasOwnProperty(e)){var i=hd[e](this._track,t);if(i)return i}}};var hd={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&r.length>1&&n&&n.length>1){var a=le(n)/le(r);!isFinite(a)&&(a=1),e.pinchScale=a;var o=he(n);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},ud="silent";de.prototype.dispose=function(){};var cd=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],dd=function(t,e,i,n){rd.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new de,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,Q.call(this),this.setHandlerProxy(i)};dd.prototype={constructor:dd,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(cd,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,i=t.zrY,n=this._hovered,r=n.target;r&&!r.__zr&&(n=this.findHover(n.x,n.y),r=n.target);var a=this._hovered=this.findHover(e,i),o=a.target,s=this.proxy;s.setCursor&&s.setCursor(o?o.cursor:"default"),r&&o!==r&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),o&&o!==r&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,i=t.toElement||t.relatedTarget;do i=i&&i.parentNode;while(i&&9!==i.nodeType&&!(e=i===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){t=t||{};var n=t.target;if(!n||!n.silent){for(var r="on"+e,a=ue(e,t,i);n&&(n[r]&&(a.cancelBubble=n[r].call(n,a)),n.trigger(e,a),n=n.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},a=n.length-1;a>=0;a--){var o;if(n[a]!==i&&!n[a].ignore&&(o=fe(n[a],t,e))&&(!r.topTarget&&(r.topTarget=n[a]),o!==ud)){r.target=n[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new ld);var i=this._gestureMgr;"start"===e&&i.clear();var n=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),n){var r=n.type;t.gestureEvent=r,this.dispatchToElement({target:n.target},r,n.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){dd.prototype[t]=function(e){var i=this.findHover(e.zrX,e.zrY),n=i.target;if("mousedown"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if("mouseup"===t)this._upEl=n;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||ed(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(i,t,e)}}),c(dd,rd),c(dd,Q);var fd="undefined"==typeof Float32Array?Array:Float32Array,pd=ge,gd=5e-5,vd=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},md=vd.prototype;md.transform=null,md.needLocalTransform=function(){return be(this.rotation)||be(this.position[0])||be(this.position[1])||be(this.scale[0]-1)||be(this.scale[1]-1)};var yd=[];md.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;if(!i&&!e)return void(n&&pd(n));n=n||pe(),i?this.getLocalTransform(n):pd(n),e&&(i?me(n,t.transform,n):ve(n,t.transform)),this.transform=n;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(yd);var a=yd[0]<0?-1:1,o=yd[1]<0?-1:1,s=((yd[0]-a)*r+a)/yd[0]||0,l=((yd[1]-o)*r+o)/yd[1]||0;n[0]*=s,n[1]*=s,n[2]*=l,n[3]*=l}this.invTransform=this.invTransform||pe(),we(this.invTransform,n)},md.getLocalTransform=function(t){return vd.getLocalTransform(this,t)},md.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},md.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var _d=[],xd=pe();md.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=this.position,r=this.scale;be(e-1)&&(e=Math.sqrt(e)),be(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),n[0]=t[4],n[1]=t[5],r[0]=e,r[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},md.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(me(_d,t.invTransform,e),e=_d);var i=this.origin;i&&(i[0]||i[1])&&(xd[4]=i[0],xd[5]=i[1],me(_d,e,xd),_d[4]-=i[0],_d[5]-=i[1],e=_d),this.setLocalTransform(e)}},md.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},md.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&Z(i,i,n),i},md.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&Z(i,i,n),i},vd.getLocalTransform=function(t,e){e=e||[],pd(e);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,a=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),xe(e,e,n),r&&_e(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=a[0],e[5]+=a[1],e};var wd={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-wd.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*wd.bounceIn(2*t):.5*wd.bounceOut(2*t-1)+.5}};Se.prototype={constructor:Se,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var i=(t-this._startTime-this._pausedTime)/this._life;if(!(0>i)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?wd[n]:n,a="function"==typeof r?r(i):i;return this.fire("frame",a),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var bd=function(){this.head=null,this.tail=null,this._len=0},Sd=bd.prototype;Sd.insert=function(t){var e=new Md(t);return this.insertEntry(e),e},Sd.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Sd.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Sd.len=function(){return this._len},Sd.clear=function(){this.head=this.tail=null,this._len=0};var Md=function(t){this.value=t,this.next,this.prev},Td=function(t){this._list=new bd,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},Cd=Td.prototype;Cd.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var a=i.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new Md(e),o.key=t,i.insertEntry(o),n[t]=o}return r},Cd.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value):void 0},Cd.clear=function(){this._list.clear(),this._map={}};var Id={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},Dd=new Td(20),kd=null,Ad=Array.prototype.slice,Pd=function(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||Re,this._setter=n||Fe,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};Pd.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:qe(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;e>i;i++)t[i].call(this)},start:function(t,e){var i,n=this,r=0,a=function(){r--,r||n._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=Ze(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Ld=1;"undefined"!=typeof window&&(Ld=Math.max(window.devicePixelRatio||1,1));var Od=0,Bd=Ld,Ed=function(){};1===Od?Ed=function(){for(var t in arguments)throw new Error(arguments[t])}:Od>1&&(Ed=function(){for(var t in arguments)console.log(arguments[t])});var zd=Ed,Rd=function(){this.animators=[]};Rd.prototype={constructor:Rd,animate:function(t,e){var i,n=!1,r=this,a=this.__zr;if(t){var o=t.split("."),s=r;n="shape"===o[0];for(var l=0,u=o.length;u>l;l++)s&&(s=s[o[l]]);s&&(i=s)}else i=r;if(!i)return void zd('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new Pd(i,e);return d.during(function(){r.dirty(n)}).done(function(){c.splice(h(c,d),1)}),c.push(d),a&&a.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;i>n;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,a){$e(this,t,e,i,n,r,a)},animateFrom:function(t,e,i,n,r,a){$e(this,t,e,i,n,r,a,!0)}};var Fd=function(t){vd.call(this,t),rd.call(this,t),Rd.call(this,t),this.id=t.id||Fc()};Fd.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(b(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(Fd,Rd),c(Fd,vd),c(Fd,rd);var Nd=Z,Hd=Math.min,Wd=Math.max;Je.prototype={constructor:Je,union:function(t){var e=Hd(t.x,this.x),i=Hd(t.y,this.y);this.width=Wd(t.x+t.width,this.x+this.width)-e,this.height=Wd(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this.height,Nd(t,t,r),Nd(e,e,r),Nd(i,i,r),Nd(n,n,r),this.x=Hd(t[0],e[0],i[0],n[0]),this.y=Hd(t[1],e[1],i[1],n[1]);var a=Wd(t[0],e[0],i[0],n[0]),o=Wd(t[1],e[1],i[1],n[1]);this.width=a-this.x,this.height=o-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=pe();return ye(r,r,[-e.x,-e.y]),xe(r,r,[i,n]),ye(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Je||(t=Je.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(o>n||i>s||l>a||r>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new Je(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Je.create=function(t){return new Je(t.x,t.y,t.width,t.height)};var Vd=function(t){t=t||{},Fd.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Vd.prototype={constructor:Vd,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);n>=0&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Vd&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=h(n,t);return 0>r?this:(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof Vd&&t.delChildrenFromStorage(i)),e&&e.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof Vd&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof Vd&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof Vd&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new Je(0,0,0,0),n=t||this._children,r=[],a=0;a<n.length;a++){var o=n[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),l=o.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),e=e||i.clone(),e.union(i)):(e=e||s.clone(),e.union(s))}}return e||i}},u(Vd,Fd);var Gd=32,Xd=7,Yd=function(){this._roots=[],this._displayList=[],this._displayListLen=0};Yd.prototype={constructor:Yd,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;r>n;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Hc.canvasSupported&&si(i,li)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Vd&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof Vd&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,n=t.length;n>e;e++)this.delRoot(t[e]);else{var r=h(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Vd&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:li};var qd={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Ud=function(t,e,i){return qd.hasOwnProperty(e)?i*=t.dpr:i},jd={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Zd=9,$d=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Kd=function(t){this.extendFrom(t,!1)};Kd.prototype={constructor:Kd,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var n=this,r=i&&i.style,a=!r||t.__attrCachedBy!==jd.STYLE_BIND;t.__attrCachedBy=jd.STYLE_BIND;for(var o=0;o<$d.length;o++){var s=$d[o],l=s[0];(a||n[l]!==r[l])&&(t[l]=Ud(t,l,n[l]||s[1]))}if((a||n.fill!==r.fill)&&(t.fillStyle=n.fill),(a||n.stroke!==r.stroke)&&(t.strokeStyle=n.stroke),(a||n.opacity!==r.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(a||n.blend!==r.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var h=n.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||e!==!0&&(e===!1?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n="radial"===e.type?ui:hi,r=n(t,e,i),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var Qd=Kd.prototype,Jd=0;Jd<$d.length;Jd++){var tf=$d[Jd];tf[0]in Qd||(Qd[tf[0]]=tf[1])}Kd.getGradient=Qd.getGradient;var ef=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};ef.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var nf=function(t,e,i){var n;i=i||Bd,"string"==typeof t?n=di(t,e,i):b(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var r=n.style;r&&(n.onselectstart=ci,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i};nf.prototype={constructor:nf,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=di("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,a&&(a.width=t*i,a.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i=this.dom,n=this.ctx,r=i.width,a=i.height,e=e||this.clearColor,o=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,r/l,a/l)),n.clearRect(0,0,r,a),e&&"transparent"!==e){var h;e.colorStops?(h=e.__canvasGradient||Kd.getGradient(n,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=h):e.image&&(h=ef.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=h||e,n.fillRect(0,0,r,a),n.restore()}if(o){var u=this.domBack;n.save(),n.globalAlpha=s,n.drawImage(u,0,0,r,a),n.restore()}}};var rf="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},af=new Td(50),of={},sf=0,lf=5e3,hf=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,uf="12px sans-serif",cf={};cf.measureText=function(t,e){var i=l();return i.font=e||uf,i.measureText(t)};var df=uf,ff={left:1,right:1,center:1},pf={top:1,bottom:1,middle:1},gf=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],vf=new Je,mf=function(){};mf.prototype={constructor:mf,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&Ei(i,!0);var n=i.text;if(null!=n&&(n+=""),Qi(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(vf.copy(e),vf.applyTransform(r),e=vf),Ri(this,t,n,i,e,Zd),t.restore()}}},Ji.prototype={constructor:Ji,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?Fd.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Kd(t,this),this.dirty(!1),this}},u(Ji,Fd),c(Ji,mf),tn.prototype={constructor:tn,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=pi(n,this._image,this,this.onload);if(r&&vi(r)){var a=i.x||0,o=i.y||0,s=i.width,l=i.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(r,u,c,i.sWidth,i.sHeight,a,o,s,l)}else if(i.sx&&i.sy){var u=i.sx,c=i.sy,d=s-u,f=l-c;t.drawImage(r,u,c,d,f,a,o,s,l)}else t.drawImage(r,a,o,s,l);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Je(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect
}},u(tn,Ji);var yf=1e5,_f=314159,xf=.01,wf=.001,bf=new Je(0,0,0,0),Sf=new Je(0,0,0,0),Mf=function(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=o({},i||{}),this.dpr=i.devicePixelRatio||Bd,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,n){var l=t.width,h=t.height;null!=i.width&&(l=i.width),null!=i.height&&(h=i.height),this.dpr=i.devicePixelRatio||1,t.width=l*this.dpr,t.height=h*this.dpr,this._width=l,this._height=h;var u=new nf(t,this,this.dpr);u.__builtin__=!0,u.initContext(),s[_f]=u,u.zlevel=_f,a.push(_f),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=sn(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};Mf.prototype={constructor:Mf,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var n=0;n<i.length;n++){var r=i[n],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===n?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return i.__from=t,t.__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=h(i,e);n>=0&&i.splice(n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){si(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(yf));var n={};i.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,n))):(t.splice(r,1),o.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(yf)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!n){var r=this;rf(function(){r._paintList(t,e,i)})}}},_compositeManually:function(){var t=this.getLayer(_f).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},_doPaintList:function(t,e){for(var i=[],n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n],a=this._layers[r];a.__builtin__&&a!==this._hoverlayer&&(a.__dirty||e)&&i.push(a)}for(var o=!0,s=0;s<i.length;s++){var a=i[s],l=a.ctx,h={};l.save();var u=e?a.__startIndex:a.__drawIndex,c=!e&&a.incremental&&Date.now,d=c&&Date.now(),p=a.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(a.__startIndex===a.__endIndex)a.clear(!1,p);else if(u===a.__startIndex){var g=t[u];g.incremental&&g.notClear&&!e||a.clear(!1,p)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=a.__startIndex);for(var v=u;v<a.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,a,e,h),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}a.__drawIndex=v,a.__drawIndex<a.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return Hc.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,i,n){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&rn(t,this._width,this._height))){var o=t.__clipPaths;(!n.prevElClipPaths||an(o,n.prevElClipPaths))&&(n.prevElClipPaths&&(e.ctx.restore(),n.prevElClipPaths=null,n.prevEl=null),o&&(r.save(),on(o,r),n.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=_f);var i=this._layers[t];return i||(i=new nf("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]&&r(i,this._layerConfig[t],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,a=null,o=-1,s=this._domRoot;if(i[t])return void zd("ZLevel "+t+" has been used already");if(!nn(e))return void zd("Layer of zlevel "+t+" is not valid");if(r>0&&t>n[0]){for(o=0;r-1>o&&!(n[o]<t&&n[o+1]>t);o++);a=i[n[o]]}if(n.splice(o+1,0,t),i[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}for(var r=null,a=0,i=0;i<t.length;i++){var o,n=t[i],s=n.zlevel;n.incremental?(o=this.getLayer(s+wf,this._needsManuallyCompositing),o.incremental=!0,a=1):o=this.getLayer(s+(a>0?xf:0),this._needsManuallyCompositing),o.__builtin__||zd("ZLevel "+s+" has been used by unkown layer "+o.id),o!==r&&(o.__used=!0,o.__startIndex!==i&&(o.__dirty=!0),o.__startIndex=i,o.__drawIndex=o.incremental?-1:i,e(i),r=o),n.__dirty&&(o.__dirty=!0,o.incremental&&o.__drawIndex<0&&(o.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?r(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+xf){var o=this._layers[a];r(o,i[t],!0)}}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(h(i,t),1))},resize:function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(_f).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[_f].dom;var e=new nf("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[n]||en(s[i])||en(o.style[i]))-(en(s[r])||0)-(en(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,h=a.hasStroke()?a.lineWidth:0,u=Math.max(h/2,-s+o),c=Math.max(h/2,s+o),d=Math.max(h/2,-l+o),f=Math.max(h/2,l+o),p=r.width+u+c,g=r.height+d+f;i.width=p*e,i.height=g*e,n.scale(e,e),n.clearRect(0,0,p,g),n.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var m=tn,y=new m({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var Tf=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,rd.call(this)};Tf.prototype={constructor:Tf,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=h(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r.length;for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(rf(t),!e._paused&&e._update())}var e=this;this._running=!0,rf(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var i=new Pd(t,e.loop,e.getter,e.setter);return this.addAnimator(i),i}},c(Tf,rd);var Cf=300,If=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Df=["touchstart","touchend","touchmove"],kf={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},Af=p(If,function(t){var e=t.replace("mouse","pointer");return kf[e]?e:t}),Pf={mousemove:function(t){t=ae(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=ae(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!==this.dom)for(;e&&9!==e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=ae(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,this.handler.processGesture(this,t,"start"),Pf.mousemove.call(this,t),Pf.mousedown.call(this,t),hn(this)},touchmove:function(t){t=ae(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"change"),Pf.mousemove.call(this,t),hn(this)},touchend:function(t){t=ae(this.dom,t),t.zrByTouch=!0,this.handler.processGesture(this,t,"end"),Pf.mouseup.call(this,t),+new Date-this._lastTouchMoment<Cf&&Pf.click.call(this,t),hn(this)},pointerdown:function(t){Pf.mousedown.call(this,t)},pointermove:function(t){un(t)||Pf.mousemove.call(this,t)},pointerup:function(t){Pf.mouseup.call(this,t)},pointerout:function(t){un(t)||Pf.mouseout.call(this,t)}};f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){Pf[t]=function(e){e=ae(this.dom,e),this.trigger(t,e)}});var Lf=dn.prototype;Lf.dispose=function(){for(var t=If.concat(Df),e=0;e<t.length;e++){var i=t[e];se(this.dom,ln(i),this._handlers[i])}},Lf.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(dn,rd);var Of=!Hc.canvasSupported,Bf={canvas:Mf},Ef=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new Yd,a=i.renderer;if(Of){if(!Bf.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&Bf[a]||(a="canvas");var o=new Bf[a](e,r,i,t);this.storage=r,this.painter=o;var s=Hc.node||Hc.worker?null:new dn(o.getViewportRoot());this.handler=new dd(r,o,s,o.root),this.animation=new Tf({stage:{update:m(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(n)}};Ef.prototype={constructor:Ef,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null}};var zf=f,Rf=b,Ff=_,Nf="series\x00",Hf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],Wf=0,Vf=".",Gf="___EC__COMPONENT__CONTAINER___",Xf=0,Yf=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,i,n){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(i&&h(i,o)>=0||n&&h(n,o)<0)){var s=e.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}},qf=Yf([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Uf={getLineStyle:function(t){var e=qf(this,t),i=this.getLineDash(e.lineWidth);return i&&(e.lineDash=i),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[i,i]}},jf=Yf([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Zf={getAreaStyle:function(t,e){return jf(this,t,e)}},$f=Math.pow,Kf=Math.sqrt,Qf=1e-8,Jf=1e-4,tp=Kf(3),ep=1/3,ip=N(),np=N(),rp=N(),ap=Math.min,op=Math.max,sp=Math.sin,lp=Math.cos,hp=2*Math.PI,up=N(),cp=N(),dp=N(),fp=[],pp=[],gp={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},vp=[],mp=[],yp=[],_p=[],xp=Math.min,wp=Math.max,bp=Math.cos,Sp=Math.sin,Mp=Math.sqrt,Tp=Math.abs,Cp="undefined"!=typeof Float32Array,Ip=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Ip.prototype={constructor:Ip,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=Tp(1/Bd/t)||0,this._uy=Tp(1/Bd/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(gp.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=Tp(t-this._xi)>this._ux||Tp(e-this._yi)>this._uy||this._len<5;return this.addData(gp.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(gp.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(gp.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(gp.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=bp(r)*i+t,this._yi=Sp(r)*i+e,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(gp.R,t,e,i,n),this},closePath:function(){this.addData(gp.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Cp||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();Cp&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[n++]=a[o];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Mp(u*u+c*c),f=l,p=h,g=o.length;for(u/=d,c/=d,0>a&&(a=r+a),a%=r,f-=a*u,p-=a*c;u>0&&t>=f||0>u&&f>=t||0===u&&(c>0&&e>=p||0>c&&p>=e);)n=this._dashIdx,i=o[n],f+=u*i,p+=c*i,this._dashIdx=(n+1)%g,u>0&&l>f||0>u&&f>l||c>0&&h>p||0>c&&p>h||s[n%2?"moveTo":"lineTo"](u>=0?xp(f,t):wp(f,t),c>=0?xp(p,e):wp(p,e));u=f-t,c=p-e,this._dashOffset=-Mp(u*u+c*c)},_dashedBezierTo:function(t,e,i,n,r,a){var o,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=Fn,y=0,_=this._dashIdx,x=f.length,w=0;for(0>d&&(d=c+d),d%=c,o=0;1>o;o+=.1)s=m(g,t,i,r,o+.1)-m(g,t,i,r,o),l=m(v,e,n,a,o+.1)-m(v,e,n,a,o),y+=Mp(s*s+l*l);for(;x>_&&(w+=f[_],!(w>d));_++);for(o=(w-d)/y;1>=o;)h=m(g,t,i,r,o),u=m(v,e,n,a,o),_%2?p.moveTo(h,u):p.lineTo(h,u),o+=f[_]/y,_=(_+1)%x;_%2!==0&&p.lineTo(r,a),s=r-h,l=a-u,this._dashOffset=-Mp(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Cp&&(this.data=new Float32Array(t)))},getBoundingRect:function(){vp[0]=vp[1]=yp[0]=yp[1]=Number.MAX_VALUE,mp[0]=mp[1]=_p[0]=_p[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,r=0,a=0;a<t.length;){var o=t[a++];switch(1===a&&(e=t[a],i=t[a+1],n=e,r=i),o){case gp.M:n=t[a++],r=t[a++],e=n,i=r,yp[0]=n,yp[1]=r,_p[0]=n,_p[1]=r;break;case gp.L:$n(e,i,t[a],t[a+1],yp,_p),e=t[a++],i=t[a++];break;case gp.C:Kn(e,i,t[a++],t[a++],t[a++],t[a++],t[a],t[a+1],yp,_p),e=t[a++],i=t[a++];break;case gp.Q:Qn(e,i,t[a++],t[a++],t[a],t[a+1],yp,_p),e=t[a++],i=t[a++];break;case gp.A:var s=t[a++],l=t[a++],h=t[a++],u=t[a++],c=t[a++],d=t[a++]+c;a+=1;var f=1-t[a++];1===a&&(n=bp(c)*h+s,r=Sp(c)*u+l),Jn(s,l,h,u,c,d,f,yp,_p),e=bp(d)*h+s,i=Sp(d)*u+l;break;case gp.R:n=e=t[a++],r=i=t[a++];var p=t[a++],g=t[a++];$n(n,r,n+p,r+g,yp,_p);break;case gp.Z:e=n,i=r}$(vp,vp,yp),K(mp,mp,_p)}return 0===a&&(vp[0]=vp[1]=mp[0]=mp[1]=0),new Je(vp[0],vp[1],mp[0]-vp[0],mp[1]-vp[1])},rebuildPath:function(t){for(var e,i,n,r,a,o,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;u>c;){var d=s[c++];switch(1===c&&(n=s[c],r=s[c+1],e=n,i=r),d){case gp.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case gp.L:a=s[c++],o=s[c++],(Tp(a-n)>l||Tp(o-r)>h||c===u-1)&&(t.lineTo(a,o),n=a,r=o);break;case gp.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case gp.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case gp.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],_=s[c++],x=s[c++],w=g>v?g:v,b=g>v?1:g/v,S=g>v?v/g:1,M=Math.abs(g-v)>.001,T=m+y;M?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,m,T,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,m,T,1-x),1===c&&(e=bp(m)*g+f,i=Sp(m)*v+p),n=bp(T)*g+f,r=Sp(T)*v+p;break;case gp.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case gp.Z:t.closePath(),n=e,r=i}}}},Ip.CMD=gp;var Dp=2*Math.PI,kp=2*Math.PI,Ap=Ip.CMD,Pp=2*Math.PI,Lp=1e-4,Op=[-1,-1,-1],Bp=[-1,-1],Ep=ef.prototype.getCanvasPattern,zp=Math.abs,Rp=new Ip(!0);pr.prototype={constructor:pr,type:"path",__dirtyPath:!0,strokeContainThreshold:5,subPixelOptimize:!1,brush:function(t,e){var i=this.style,n=this.path||Rp,r=i.hasStroke(),a=i.hasFill(),o=i.fill,s=i.stroke,l=a&&!!o.colorStops,h=r&&!!s.colorStops,u=a&&!!o.image,c=r&&!!s.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=i.getGradient(t,o,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=Ep.call(o,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=Ep.call(s,t));var f=i.lineDash,p=i.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(n.setScale(v[0],v[1]),this.__dirtyPath||f&&!g&&r?(n.beginPath(t),f&&!g&&(n.setLineDash(f),n.setLineDashOffset(p)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=i.fillOpacity){var m=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,n.fill(t),t.globalAlpha=m}else n.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=i.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,n.stroke(t),t.globalAlpha=m}else n.stroke(t);f&&g&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Ip},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n||(n=this.path=new Ip),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r.height+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),fr(a,o/s,t,e)))return!0}if(r.hasFill())return dr(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Ji.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(b(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&zp(t[0]-1)>1e-10&&zp(t[3]-1)>1e-10?Math.sqrt(zp(t[0]*t[3]-t[2]*t[1])):1}},pr.extend=function(t){var e=function(e){pr.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var i=t.shape;if(i){this.shape=this.shape||{};var n=this.shape;for(var r in i)!n.hasOwnProperty(r)&&i.hasOwnProperty(r)&&(n[r]=i[r])}t.init&&t.init.call(this,e)};u(e,pr);for(var i in t)"style"!==i&&"shape"!==i&&(e.prototype[i]=t[i]);return e},u(pr,Ji);var Fp=Ip.CMD,Np=[[],[],[]],Hp=Math.sqrt,Wp=Math.atan2,Vp=function(t,e){var i,n,r,a,o,s,l=t.data,h=Fp.M,u=Fp.C,c=Fp.L,d=Fp.R,f=Fp.A,p=Fp.Q;for(r=0,a=0;r<l.length;){switch(i=l[r++],a=r,n=0,i){case h:n=1;break;case c:n=1;break;case u:n=3;break;case p:n=2;break;case f:var g=e[4],v=e[5],m=Hp(e[0]*e[0]+e[1]*e[1]),y=Hp(e[2]*e[2]+e[3]*e[3]),_=Wp(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=_,l[r++]+=_,r+=2,a=r;break;case d:s[0]=l[r++],s[1]=l[r++],Z(s,s,e),l[a++]=s[0],l[a++]=s[1],s[0]+=l[r++],s[1]+=l[r++],Z(s,s,e),l[a++]=s[0],l[a++]=s[1]}for(o=0;n>o;o++){var s=Np[o];s[0]=l[r++],s[1]=l[r++],Z(s,s,e),l[a++]=s[0],l[a++]=s[1]}}},Gp=Math.sqrt,Xp=Math.sin,Yp=Math.cos,qp=Math.PI,Up=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},jp=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(Up(t)*Up(e))},Zp=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(jp(t,e))},$p=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Kp=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,Qp=function(t){Ji.call(this,t)};Qp.prototype={constructor:Qp,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&Ei(i,!0),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;return null!=n&&(n+=""),Qi(n,i)?(this.setTransform(t),Ri(this,t,n,i,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=jd.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&Ei(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var i=yi(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,ji(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},u(Qp,Ji);var Jp=pr.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),tg=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],eg=function(t){return Hc.browser.ie&&Hc.browser.version>=11?function(){var e,i=this.__clipPaths,n=this.style;if(i)for(var r=0;r<i.length;r++){var a=i[r],o=a&&a.shape,s=a&&a.type;if(o&&("sector"===s&&o.startAngle===o.endAngle||"rect"===s&&(!o.width||!o.height))){for(var l=0;l<tg.length;l++)tg[l][2]=n[tg[l][0]],n[tg[l][0]]=tg[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<tg.length;l++)n[tg[l][0]]=tg[l][2]}:t},ig=pr.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:eg(pr.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*r+i,u*r+n),t.lineTo(h*a+i,u*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}}),ng=pr.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}}),rg=function(t,e){for(var i=t.length,n=[],r=0,a=1;i>a;a++)r+=U(t[a-1],t[a]);var o=r/2;o=i>o?i:o;for(var a=0;o>a;a++){var s,l,h,u=a/(o-1)*(e?i:i-1),c=Math.floor(u),d=u-c,f=t[c%i];e?(s=t[(c-1+i)%i],l=t[(c+1)%i],h=t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[c>i-2?i-1:c+1],h=t[c>i-3?i-1:c+2]);var p=d*d,g=d*p;n.push([wr(s[0],f[0],l[0],h[0],d,p,g),wr(s[1],f[1],l[1],h[1],d,p,g)])}return n},ag=function(t,e,i,n){var r,a,o,s,l=[],h=[],u=[],c=[];if(n){o=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)$(o,o,t[d]),K(s,s,t[d]);$(o,o,n[0]),K(s,s,n[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(i)r=t[d?d-1:f-1],a=t[(d+1)%f];else{if(0===d||d===f-1){l.push(H(t[d]));continue}r=t[d-1],a=t[d+1]}V(h,a,r),Y(h,h,e);var g=U(p,r),v=U(p,a),m=g+v;0!==m&&(g/=m,v/=m),Y(u,h,-g),Y(c,h,v);var y=W([],p,u),_=W([],p,c);n&&(K(y,y,o),$(y,y,s),K(_,_,o),$(_,_,s)),l.push(y),l.push(_)}return i&&l.push(l.shift()),l},og=pr.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){br(t,e,!0)}}),sg=pr.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){br(t,e,!1)}}),lg=Math.round,hg={},ug=pr.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(Mr(hg,e,this.style),i=hg.x,n=hg.y,r=hg.width,a=hg.height,hg.r=e.r,e=hg):(i=e.x,n=e.y,r=e.width,a=e.height),e.r?Bi(t,e):t.rect(i,n,r,a),t.closePath()}}),cg={},dg=pr.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(Sr(cg,e,this.style),i=cg.x1,n=cg.y1,r=cg.x2,a=cg.y2):(i=e.x1,n=e.y1,r=e.x2,a=e.y2);var o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),fg=[],pg=pr.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,n),null==l||null==h?(1>u&&(jn(i,o,r,u,fg),o=fg[1],r=fg[2],jn(n,s,a,u,fg),s=fg[1],a=fg[2]),t.quadraticCurveTo(o,s,r,a)):(1>u&&(Vn(i,o,l,r,u,fg),o=fg[1],l=fg[2],r=fg[3],Vn(n,s,h,a,u,fg),s=fg[1],h=fg[2],a=fg[3]),t.bezierCurveTo(o,s,l,h,r,a)))},pointAt:function(t){return Cr(this.shape,t,!1)},tangentAt:function(t){var e=Cr(this.shape,t,!0);return q(e,e)}}),gg=pr.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);
t.moveTo(l*r+i,h*r+n),t.arc(i,n,r,a,o,!s)}}),vg=pr.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),pr.prototype.getBoundingRect.call(this)}}),mg=function(t){this.colorStops=t||[]};mg.prototype={constructor:mg,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var yg=function(t,e,i,n,r,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=a||!1,mg.call(this,r)};yg.prototype={constructor:yg},u(yg,mg);var _g=function(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,mg.call(this,n)};_g.prototype={constructor:_g},u(_g,mg),Ir.prototype.incremental=!0,Ir.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},Ir.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},Ir.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},Ir.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Ir.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},Ir.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,e===this._cursor?null:this._displayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===e?null:this._temporaryDisplayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var xg=[];Ir.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Je(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],n=i.getBoundingRect().clone();i.needLocalTransform()&&n.applyTransform(i.getLocalTransform(xg)),t.union(n)}this._rect=t}return this._rect},Ir.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();if(n.contain(i[0],i[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},u(Ir,Ji);var wg=Math.round,bg=Math.max,Sg=Math.min,Mg={},Tg=1,Cg=xr,Ig=R(),Dg=0,kg=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:Tg,extendShape:Dr,extendPath:kr,makePath:Ar,makeImage:Pr,mergePath:Cg,resizePath:Or,subPixelOptimizeLine:Br,subPixelOptimizeRect:Er,subPixelOptimize:zr,setElementHoverStyle:Xr,isInEmphasis:Yr,setHoverStyle:$r,setAsHoverStyleTrigger:Kr,setLabelStyle:Qr,setTextStyle:Jr,setText:ta,getFont:sa,updateProps:ha,initProps:ua,getTransform:ca,applyTransform:da,transformDirection:fa,groupTransition:pa,clipPointsByRect:ga,clipRectByRect:va,createIcon:ma,Group:Vd,Image:tn,Text:Qp,Circle:Jp,Sector:ig,Ring:ng,Polygon:og,Polyline:sg,Rect:ug,Line:dg,BezierCurve:pg,Arc:gg,IncrementalDisplayable:Ir,CompoundPath:vg,LinearGradient:yg,RadialGradient:_g,BoundingRect:Je}),Ag=["textStyle","color"],Pg={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Ag):null)},getFont:function(){return sa({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return yi(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},Lg=Yf([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),Og={getItemStyle:function(t,e){var i=Lg(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},Bg=c,Eg=Sn();ya.prototype={constructor:ya,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:_a(this.option,this.parsePath(t),!e&&xa(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&xa(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i,n=null==t?this.option:_a(this.option,t=this.parsePath(t));return e=e||(i=xa(this,t))&&i.getModel(t),new ya(n,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(n(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Eg(this).getParent=t},isAnimationEnabled:function(){if(!Hc.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},Pn(ya),Ln(ya),Bg(ya,Uf),Bg(ya,Zf),Bg(ya,Pg),Bg(ya,Og);var zg=0,Rg=1e-4,Fg=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,Ng=P,Hg=/([&<>"'])/g,Wg={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Vg=["a","b","c","d","e","f","g"],Gg=function(t,e){return"{"+t+(null==e?"":e)+"}"},Xg=Mi,Yg=f,qg=["left","right","top","bottom","width","height"],Ug=[["width","left","right"],["height","top","bottom"]],jg=Ga,Zg=(y(Ga,"vertical"),y(Ga,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),$g=Sn(),Kg=ya.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){ya.call(this,t,e,i,n),this.uid=wa("ec_cpt_model")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?qa(t):{},a=e.getTheme();r(t,a.get(this.mainType)),r(t,this.getDefaultOption()),i&&Ya(t,n,i)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;e&&Ya(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=$g(this);if(!t.defaultOption){for(var e=[],i=this.constructor;i;){var n=i.prototype.defaultOption;n&&e.push(n),i=i.superClass}for(var a={},o=e.length-1;o>=0;o--)a=r(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});En(Kg,{registerWhenExtend:!0}),ba(Kg),Sa(Kg,ja),c(Kg,Zg);var Qg="";"undefined"!=typeof navigator&&(Qg=navigator.platform||"");var Jg={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:Qg.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},tv=Sn(),ev={clearColorPalette:function(){tv(this).colorIdx=0,tv(this).colorNameMap={}},getColorFromPalette:function(t,e,i){e=e||this;var n=tv(e),r=n.colorIdx||0,a=n.colorNameMap=n.colorNameMap||{};if(a.hasOwnProperty(t))return a[t];var o=pn(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=i&&s?Za(s,i):o;if(l=l||o,l&&l.length){var h=l[r];return t&&(a[t]=h),n.colorIdx=(r+1)%l.length,h}}},iv={cartesian2d:function(t,e,i,n){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],i.set("x",r),i.set("y",a),Ka(r)&&(n.set("x",r),e.firstCategoryDimIndex=0),Ka(a)&&(n.set("y",a),e.firstCategoryDimIndex=1)},singleAxis:function(t,e,i,n){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],i.set("single",r),Ka(r)&&(n.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,i,n){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],i.set("radius",a),i.set("angle",o),Ka(a)&&(n.set("radius",a),e.firstCategoryDimIndex=0),Ka(o)&&(n.set("angle",o),e.firstCategoryDimIndex=1)},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,i,n){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),o=e.coordSysDims=a.dimensions.slice();f(a.parallelAxisIndex,function(t,a){var s=r.getComponent("parallelAxis",t),l=o[a];i.set(l,s),Ka(s)&&null==e.firstCategoryDimIndex&&(n.set(l,s),e.firstCategoryDimIndex=a)})}},nv="original",rv="arrayRows",av="objectRows",ov="keyedColumns",sv="unknown",lv="typedArray",hv="column",uv="row";Qa.seriesDataToSource=function(t){return new Qa({data:t,sourceFormat:M(t)?lv:nv,fromDataset:!1})},Ln(Qa);var cv=Sn(),dv="\x00_ec_inner",fv=ya.extend({init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new ya(i),this._optionManager=n},setOption:function(t,e){L(!(dv in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):po.call(this,n),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,n){var r=pn(t[e]),s=yn(a.get(e),r);_n(s),f(s,function(t){var i=t.option;b(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=vo(e,i,t.exist))});var l=go(a,n);i[e]=[],a.set(e,[]),f(s,function(t,n){var r=t.exist,s=t.option;if(L(b(s)||r,"Empty component definition"),s){var h=Kg.getClass(e,t.keyInfo.subType,!0);if(r&&r instanceof h)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:n},t.keyInfo);r=new h(s,this,this,u),o(r,u),r.init(s,this,this,u),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);a.get(e)[n]=r,i[e][n]=r.option},this),"series"===e&&mo(this,a.get("series"))}var i=this.option,a=this._componentsMap,s=[];eo(this),f(t,function(t,e){null!=t&&(Kg.hasClass(e)?e&&s.push(e):i[e]=null==i[e]?n(t):r(i[e],t,!0))}),Kg.topologicalTravel(s,Kg.getAllClassMainTypes(),e,this),this._seriesIndicesMap=R(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=n(this.option);return f(t,function(e,i){if(Kg.hasClass(i)){for(var e=pn(e),n=e.length-1;n>=0;n--)wn(e[n])&&e.splice(n,1);t[i]=e}}),delete t[dv],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=i)_(i)||(i=[i]),o=v(p(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var s=_(n);o=v(a,function(t){return s&&h(n,t.id)>=0||!s&&t.id===n})}else if(null!=r){var l=_(r);o=v(a,function(t){return l&&h(r,t.name)>=0||!l&&t.name===r})}else o=a.slice();return yo(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return!t||null==t[e]&&null==t[i]&&null==t[n]?null:{mainType:r,index:t[e],id:t[i],name:t[n]}}function i(e){return t.filter?v(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),o=a?this.queryComponents(a):this._componentsMap.get(r);return i(yo(o,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if("function"==typeof t)i=e,e=t,n.each(function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(w(t))f(n.get(t),e,i);else if(b(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){f(this._seriesIndices,function(i){var n=this._componentsMap.get("series")[i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,i){f(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var i=v(this._componentsMap.get("series"),t,e);mo(this,i)},restoreData:function(t){var e=this._componentsMap;mo(this,e.get("series"));var i=[];e.each(function(t,e){i.push(e)}),Kg.topologicalTravel(i,Kg.getAllClassMainTypes(),function(i){f(e.get(i),function(e){("series"!==i||!co(e,t))&&e.restoreData()})})}});c(fv,ev);var pv=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],gv={};xo.prototype={constructor:xo,create:function(t,e){var i=[];f(gv,function(n){var r=n.create(t,e);i=i.concat(r||[])}),this._coordinateSystems=i},update:function(t,e){f(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},xo.register=function(t,e){gv[t]=e},xo.get=function(t){return gv[t]};var vv=f,mv=n,yv=p,_v=r,xv=/^(min|max)?(.+)$/;wo.prototype={constructor:wo,setOption:function(t,e){t&&f(pn(t.series),function(t){t&&t.data&&M(t.data)&&B(t.data)}),t=mv(t,!0);var i=this._optionBackup,n=bo.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(Co(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=yv(e.timelineOptions,mv),this._mediaList=yv(e.mediaList,mv),this._mediaDefault=mv(e.mediaDefault),this._currentMediaIndices=[],mv(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=mv(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),i=this._mediaList,n=this._mediaDefault,r=[],a=[];if(!i.length&&!n)return a;for(var o=0,s=i.length;s>o;o++)So(i[o].query,t,e)&&r.push(o);return!r.length&&n&&(r=[-1]),r.length&&!To(r,this._currentMediaIndices)&&(a=yv(r,function(t){return mv(-1===t?n.option:i[t].option)})),this._currentMediaIndices=r,a}};var wv=f,bv=b,Sv=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],Mv=function(t,e){wv(Oo(t.series),function(t){bv(t)&&Lo(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),wv(i,function(e){wv(Oo(t[e]),function(t){t&&(Ao(t,"axisLabel"),Ao(t.axisPointer,"label"))})}),wv(Oo(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Ao(e,"axisLabel"),Ao(e&&e.axisPointer,"label")}),wv(Oo(t.calendar),function(t){Do(t,"itemStyle"),Ao(t,"dayLabel"),Ao(t,"monthLabel"),Ao(t,"yearLabel")}),wv(Oo(t.radar),function(t){Ao(t,"name")}),wv(Oo(t.geo),function(t){bv(t)&&(Po(t),wv(Oo(t.regions),function(t){Po(t)}))}),wv(Oo(t.timeline),function(t){Po(t),Do(t,"label"),Do(t,"itemStyle"),Do(t,"controlStyle",!0);var e=t.data;_(e)&&f(e,function(t){b(t)&&(Do(t,"label"),Do(t,"itemStyle"))})}),wv(Oo(t.toolbox),function(t){Do(t,"iconStyle"),wv(t.feature,function(t){Do(t,"iconStyle")})}),Ao(Bo(t.axisPointer),"label"),Ao(Bo(t.tooltip).axisPointer,"label")},Tv=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Cv=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Iv=function(t,e){Mv(t,e),t.series=pn(t.series),f(t.series,function(t){if(b(t)){var e=t.type;if(("pie"===e||"gauge"===e)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var i=Eo(t,"pointer.color");null!=i&&zo(t,"itemStyle.normal.color",i)}Ro(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(Cv,function(e){var i=t[e];i&&(_(i)||(i=[i]),f(i,function(t){Ro(t)}))})},Dv=function(t){var e=R();t.eachSeries(function(t){var i=t.get("stack");if(i){var n=e.get(i)||e.set(i,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;n.length&&r.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(a)}}),e.each(Fo)},kv=No.prototype;kv.pure=!1,kv.persistent=!0,kv.getSource=function(){return this._source};var Av={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Vo},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],i=this._data,n=0;n<i.length;n++){var r=i[n];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Ho,getItem:Wo,appendData:Vo},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],i=this._source.dimensionsDefine,n=0;n<i.length;n++){var r=this._data[i[n].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,i){for(var n=e[i]||(e[i]=[]),r=0;r<(t||[]).length;r++)n.push(t[r])})}},original:{count:Ho,getItem:Wo,appendData:Vo},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var i=this._dimSize*t,n=0;n<this._dimSize;n++)e[n]=this._data[i+n];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},Pv={arrayRows:Go,objectRows:function(t,e,i,n){return null!=i?t[n]:t},keyedColumns:Go,original:function(t,e,i){var n=vn(t);return null!=i&&n instanceof Array?n[i]:n},typedArray:Go},Lv={arrayRows:Xo,objectRows:function(t,e){return Yo(t[e],this._dimensionInfos[e])},keyedColumns:Xo,original:function(t,e,i,n){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&mn(t)&&(this.hasItemOption=!0),Yo(r instanceof Array?r[n]:r,this._dimensionInfos[e])},typedArray:function(t,e,i,n){return t[n]}},Ov=/\{@(.+?)\}/g,Bv={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),a=i.getName(t),o=i.getRawDataItem(t),s=i.getItemVisual(t,"color"),l=this.ecModel.getComponent("tooltip"),h=l&&l.get("renderMode"),u=Dn(h),c=this.mainType,d="series"===c;return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:d?this.subType:null,seriesIndex:this.seriesIndex,seriesId:d?this.id:null,seriesName:d?this.name:null,name:a,dataIndex:r,data:o,dataType:e,value:n,color:s,marker:Ha({color:s,renderMode:u}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,i,n,r){e=e||"normal";var a=this.getData(i),o=a.getItemModel(t),s=this.getDataParams(t,i);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=o.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,l(s);if("string"==typeof l){var h=Na(l,s);return h.replace(Ov,function(e,i){var n=i.length;return"["===i.charAt(0)&&"]"===i.charAt(n-1)&&(i=+i.slice(1,n-1)),qo(a,t,i)})}},getRawValue:function(t,e){return qo(this.getData(e),t)},formatTooltip:function(){}},Ev=jo.prototype;Ev.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var i=this._upstream,n=t&&t.skip;if(this._dirty&&i){var r=this.context;r.data=r.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),h=t&&t.modDataCount||0;(o!==l||s!==h)&&(a="reset");var u;(this._dirty||"reset"===a)&&(this._dirty=!1,u=$o(this,n)),this._modBy=l,this._modDataCount=h;var c=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(u||f>d)){var p=this._progress;if(_(p))for(var g=0;g<p.length;g++)Zo(this,p[g],d,f,l,h);else Zo(this,p,d,f,l,h)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var zv=function(){function t(){return i>n?n++:null}function e(){var t=n%o*r+Math.ceil(n/o),e=n>=i?null:a>t?t:n;return n++,e}var i,n,r,a,o,s={reset:function(l,h,u,c){n=l,i=h,r=u,a=c,o=Math.ceil(a/r),s.next=r>1&&a>0?e:t}};return s}();Ev.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Ev.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Ev.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},Ev.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Ev.getUpstream=function(){return this._upstream},Ev.getDownstream=function(){return this._downstream},Ev.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Rv=Sn(),Fv=Kg.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.color",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.dataTask=Uo({count:Jo,reset:ts}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,i),io(this);var n=this.getInitialData(t,i);is(n,this),this.dataTask.context.data=n,Rv(this).dataBeforeProcessed=n,Ko(this)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?qa(t):{},a=this.subType;Kg.hasClass(a)&&(a+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),gn(t,"label",["show"]),this.fillDataTextStyle(t.data),i&&Ya(t,n,i)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&Ya(this.option,t,i),io(this);var n=this.getInitialData(t,e);is(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Rv(this).dataBeforeProcessed=n,Ko(this)},fillDataTextStyle:function(t){if(t&&!M(t))for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&gn(t[i],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=rs(this);if(e){var i=e.context.data;return null==t?i:i.getLinkedData(t)}return Rv(this).data},setData:function(t){var e=rs(this);if(e){var i=e.context;i.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),i.outputData=t,e!==this.dataTask&&(i.data=t)}Rv(this).data=t},getSource:function(){return to(this)},getRawData:function(){return Rv(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,i,n){function r(i){function r(t,i){var r=c.getDimensionInfo(i);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+o.seriesIndex+"at"+u,p=Ha({color:y,type:"subItem",renderMode:n,markerId:f}),g="string"==typeof p?p:p.content,v=(a?g+Fa(r.displayName||"-")+": ":"")+Fa("ordinal"===d?t+"":"time"===d?e?"":Va("yyyy/MM/dd hh:mm:ss",t):za(t));v&&s.push(v),l&&(h[f]=y,++u)}}var a=g(i,function(t,e,i){var n=c.getDimensionInfo(i);return t|=n&&n.tooltip!==!1&&null!=n.displayName},0),s=[];d.length?f(d,function(e){r(qo(c,t,e),e)}):f(i,r);var p=a?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:n,content:v,style:h}}function a(t){return{renderMode:n,content:Fa(za(t)),style:h}}var o=this;n=n||"html";var s="html"===n?"<br/>":"\n",l="richText"===n,h={},u=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=_(v),y=c.getItemVisual(t,"color");b(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var x=p>1||m&&!p?r(v):a(p?qo(c,t,d[0]):m?v[0]:v),w=x.content,S=o.seriesIndex+"at"+u,M=Ha({color:y,type:"item",renderMode:n,markerId:S});h[S]=y,++u;var T=c.getName(t),C=this.name;xn(this)||(C=""),C=C?Fa(C)+(e?": ":s):"";var I="string"==typeof M?M:M.content,D=e?I+C+w:C+I+(T?Fa(T)+": "+w:w);return{html:D,markers:h}},isAnimationEnabled:function(){if(Hc.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,i){var n=this.ecModel,r=ev.getColorFromPalette.call(this,t,e,i);return r||(r=n.getColorFromPalette(t,e,i)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(Fv,Bv),c(Fv,ev);var Nv=function(){this.group=new Vd,this.uid=wa("viewComponent")};Nv.prototype={constructor:Nv,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var Hv=Nv.prototype;Hv.updateView=Hv.updateLayout=Hv.updateVisual=function(){},Pn(Nv),En(Nv,{registerWhenExtend:!0});var Wv=function(){var t=Sn();return function(e){var i=t(e),n=e.pipelineContext,r=i.large,a=i.progressiveRender,o=i.large=n.large,s=i.progressiveRender=n.progressiveRender;return!!(r^o||a^s)&&"reset"}},Vv=Sn(),Gv=Wv();as.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,n){ss(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){ss(t.getData(),n,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var Xv=as.prototype;Xv.updateView=Xv.updateLayout=Xv.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},Pn(as,["dispose"]),En(as,{registerWhenExtend:!0}),as.markUpdateMethod=function(t,e){Vv(t).updateMethod=e};var Yv={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},qv="\x00__throttleOriginMethod",Uv="\x00__throttleRate",jv="\x00__throttleType",Zv={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i=t.getData(),n=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(n)||t.getColorFromPalette(t.name,null,e.getSeriesCount());if(i.setVisual("color",r),!e.isSeriesFiltered(t)){"function"!=typeof r||r instanceof mg||i.each(function(e){i.setItemVisual(e,"color",r(t.getDataParams(e)))});var a=function(t,e){var i=t.getItemModel(e),r=i.get(n,!0);null!=r&&t.setItemVisual(e,"color",r)};return{dataEach:i.hasItemOption?a:null}}}},$v={toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},Kv=function(t,e){function i(t,e){if("string"!=typeof t)return t;var i=t;return f(e,function(t,e){i=i.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),i}function n(t){var e=o.get(t);if(null==e){for(var i=t.split("."),n=$v.aria,r=0;r<i.length;++r)n=n[i[r]];return n}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function a(t){return $v.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show")){if(o.get("description"))return void t.setAttribute("aria-label",o.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,h=o.get("data.maxCount")||10,u=o.get("series.maxCount")||10,c=Math.min(s,u);if(!(1>s)){var d=r();l=d?i(n("general.withTitle"),{title:d}):n("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=i(n(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,o=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=n(o?l+"withName":l+"withoutName"),r=i(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:a(t.subType)});var u=t.getData();window.data=u,r+=u.count()>h?i(n("data.partialData"),{displayCnt:h}):n("data.allData");for(var d=[],f=0;f<u.count();f++)if(h>f){var g=u.getName(f),v=qo(u,f);d.push(i(n(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(n("data.separator.middle"))+n("data.separator.end"),p.push(r)}}),l+=p.join(n("series.multiple.separator.middle"))+n("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},Qv=Math.PI,Jv=function(t,e){e=e||{},s(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var i=new ug({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),n=new gg({shape:{startAngle:-Qv/2,endAngle:-Qv/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),r=new ug({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});
n.animateShape(!0).when(1e3,{endAngle:3*Qv/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*Qv/2}).delay(300).start("circularInOut");var a=new Vd;return a.add(n),a.add(r),a.add(i),a.resize=function(){var e=t.getWidth()/2,a=t.getHeight()/2;n.setShape({cx:e,cy:a});var o=n.shape.r;r.setShape({x:e-o,y:a-o,width:2*o,height:2*o}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},a.resize(),a},tm=ds.prototype;tm.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},tm.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,r=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,a=r?i.step:null,o=n&&n.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},tm.getPipeline=function(t){return this._pipelineMap.get(t)},tm.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),r=n.count(),a=i.progressiveEnabled&&e.incrementalPrepareRender&&r>=i.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=i.context={progressiveRender:a,modDataCount:s,large:o}},tm.restorePipelines=function(t){var e=this,i=e._pipelineMap=R();t.eachSeries(function(t){var n=t.getProgressive(),r=t.uid;i.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:n&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(n||700),count:0}),Ms(e,t,t.dataTask)})},tm.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),i=this.api;f(this._allHandlers,function(n){var r=t.get(n.uid)||t.set(n.uid,[]);n.reset&&ps(this,n,r,e,i),n.overallReset&&gs(this,n,r,e,i)},this)},tm.prepareView=function(t,e,i,n){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=i,a.api=n,r.__block=!t.incrementalPrepareRender,Ms(this,e,r)},tm.performDataProcessorTasks=function(t,e){fs(this,this._dataProcessorHandlers,t,e,{block:!0})},tm.performVisualTasks=function(t,e,i){fs(this,this._visualHandlers,t,e,i)},tm.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},tm.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var em=tm.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},im=bs(0);ds.wrapStageHandler=function(t,e){return x(t)&&(t={overallReset:t,seriesType:Ts(t)}),t.uid=wa("stageHandler"),e&&(t.visualType=e),t};var nm,rm={},am={};Cs(rm,fv),Cs(am,_o),rm.eachSeriesByType=rm.eachRawSeriesByType=function(t){nm=t},rm.eachComponent=function(t){"series"===t.mainType&&t.subType&&(nm=t.subType)};var om=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],sm={color:om,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],om]},lm="#eee",hm=function(){return{axisLine:{lineStyle:{color:lm}},axisTick:{lineStyle:{color:lm}},axisLabel:{textStyle:{color:lm}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:lm}}}},um=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],cm={color:um,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:lm},crossStyle:{color:lm}}},legend:{textStyle:{color:lm}},textStyle:{color:lm},title:{textStyle:{color:lm}},toolbox:{iconStyle:{normal:{borderColor:lm}}},dataZoom:{textStyle:{color:lm}},visualMap:{textStyle:{color:lm}},timeline:{lineStyle:{color:lm},itemStyle:{normal:{color:um[1]}},label:{normal:{textStyle:{color:lm}}},controlStyle:{normal:{color:lm,borderColor:lm}}},timeAxis:hm(),logAxis:hm(),valueAxis:hm(),categoryAxis:hm(),line:{symbol:"circle"},graph:{color:um},gauge:{title:{textStyle:{color:lm}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};cm.categoryAxis.splitLine.show=!1,Kg.extend({type:"dataset",defaultOption:{seriesLayoutBy:hv,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){Ja(this)}}),Nv.extend({type:"dataset"});var dm=pr.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=.5522848,n=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*i,l=o*i;t.moveTo(n-a,r),t.bezierCurveTo(n-a,r-l,n-s,r-o,n,r-o),t.bezierCurveTo(n+s,r-o,n+a,r-l,n+a,r),t.bezierCurveTo(n+a,r+l,n+s,r+o,n,r+o),t.bezierCurveTo(n-s,r+o,n-a,r+l,n-a,r),t.closePath()}}),fm=/[\s,]+/;Ds.prototype.parse=function(t,e){e=e||{};var i=Is(t);if(!i)throw new Error("Illegal svg");var n=new Vd;this._root=n;var r=i.getAttribute("viewBox")||"",a=parseFloat(i.getAttribute("width")||e.width),o=parseFloat(i.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),Ls(i,n,null,!0);for(var s=i.firstChild;s;)this._parseNode(s,n),s=s.nextSibling;var l,h;if(r){var u=O(r).split(fm);u.length>=4&&(l={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])})}if(l&&null!=a&&null!=o&&(h=zs(l,a,o),!e.ignoreViewBox)){var c=n;n=new Vd,n.add(c),c.scale=h.scale.slice(),c.position=h.position.slice()}return e.ignoreRootClip||null==a||null==o||n.setClipPath(new ug({shape:{x:0,y:0,width:a,height:o}})),{root:n,width:a,height:o,viewBoxRect:l,viewBoxTransform:h}},Ds.prototype._parseNode=function(t,e){var i=t.nodeName.toLowerCase();"defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0);var n;if(this._isDefine){var r=gm[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{var r=pm[i];r&&(n=r.call(this,t,e),e.add(n))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},Ds.prototype._parseText=function(t,e){if(1===t.nodeType){var i=t.getAttribute("dx")||0,n=t.getAttribute("dy")||0;this._textX+=parseFloat(i),this._textY+=parseFloat(n)}var r=new Qp({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});As(e,r),Ls(t,r,this._defs);var a=r.style.fontSize;a&&9>a&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=a/9,r.scale[1]*=a/9);var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r};var pm={g:function(t,e){var i=new Vd;return As(e,i),Ls(t,i,this._defs),i},rect:function(t,e){var i=new ug;return As(e,i),Ls(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new Jp;return As(e,i),Ls(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new dg;return As(e,i),Ls(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new dm;return As(e,i),Ls(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i&&(i=Ps(i));var n=new og({shape:{points:i||[]}});return As(e,n),Ls(t,n,this._defs),n},polyline:function(t,e){var i=new pr;As(e,i),Ls(t,i,this._defs);var n=t.getAttribute("points");n&&(n=Ps(n));var r=new sg({shape:{points:n||[]}});return r},image:function(t,e){var i=new tn;return As(e,i),Ls(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,n=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(r),this._textY=parseFloat(n)+parseFloat(a);var o=new Vd;return As(e,o),Ls(t,o,this._defs),o},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=n&&(this._textY=parseFloat(n));var r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new Vd;return As(e,o),Ls(t,o,this._defs),this._textX+=r,this._textY+=a,o},path:function(t,e){var i=t.getAttribute("d")||"",n=yr(i);return As(e,n),Ls(t,n,this._defs),n}},gm={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),n=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new yg(e,i,n,r);return ks(t,a),a},radialgradient:function(){}},vm={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},mm=/url\(\s*#(.*?)\)/,ym=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,_m=/([^\s:;]+)\s*:\s*([^:;]+)/g,xm=R(),wm={registerMap:function(t,e,i){var n;return _(e)?n=e:e.svg?n=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(i=e.specialAreas,e=e.geoJson),n=[{type:"geoJSON",source:e,specialAreas:i}]),f(n,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var i=bm[e];i(t)}),xm.set(t,n)},retrieveMap:function(t){return xm.get(t)}},bm={geoJSON:function(t){var e=t.source;t.geoJSON=w(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=Is(t.source)}},Sm=L,Mm=f,Tm=x,Cm=b,Im=Kg.parseClassType,Dm="4.2.1",km={zrender:"4.0.6"},Am=1,Pm=1e3,Lm=5e3,Om=1e3,Bm=2e3,Em=3e3,zm=4e3,Rm=5e3,Fm={PROCESSOR:{FILTER:Pm,STATISTIC:Lm},VISUAL:{LAYOUT:Om,GLOBAL:Bm,CHART:Em,COMPONENT:zm,BRUSH:Rm}},Nm="__flagInMainProcess",Hm="__optionUpdated",Wm=/^[a-zA-Z0-9_]+$/;Fs.prototype.on=Rs("on"),Fs.prototype.off=Rs("off"),Fs.prototype.one=Rs("one"),c(Fs,rd);var Vm=Ns.prototype;Vm._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[Hm]){var e=this[Hm].silent;this[Nm]=!0,Ws(this),Gm.update.call(this),this[Nm]=!1,this[Hm]=!1,Ys.call(this,e),qs.call(this,e)}else if(t.unfinished){var i=Am,n=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(n),t.performDataProcessorTasks(n),Gs(this,n),t.performVisualTasks(n),Qs(this,this._model,r,"remain"),i-=+new Date-a}while(i>0&&t.unfinished);t.unfinished||this._zr.flush()}}},Vm.getDom=function(){return this._dom},Vm.getZr=function(){return this._zr},Vm.setOption=function(t,e,i){var n;if(Cm(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[Nm]=!0,!this._model||e){var r=new wo(this._api),a=this._theme,o=this._model=new fv(null,null,a,r);o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,jm),i?(this[Hm]={silent:n},this[Nm]=!1):(Ws(this),Gm.update.call(this),this._zr.flush(),this[Hm]=!1,this[Nm]=!1,Ys.call(this,n),qs.call(this,n))},Vm.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},Vm.getModel=function(){return this._model},Vm.getOption=function(){return this._model&&this._model.getOption()},Vm.getWidth=function(){return this._zr.getWidth()},Vm.getHeight=function(){return this._zr.getHeight()},Vm.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},Vm.getRenderedCanvas=function(t){if(Hc.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},Vm.getSvgDataUrl=function(){if(Hc.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.pathToDataUrl()}},Vm.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;Mm(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return Mm(n,function(t){t.group.ignore=!1}),a},Vm.getConnectedDataURL=function(t){if(Hc.canvasSupported){var e=this.group,i=Math.min,r=Math.max,a=1/0;if(ty[e]){var o=a,s=a,l=-a,h=-a,u=[],c=t&&t.pixelRatio||1;f(Jm,function(a){if(a.group===e){var c=a.getRenderedCanvas(n(t)),d=a.getDom().getBoundingClientRect();o=i(d.left,o),s=i(d.top,s),l=r(d.right,l),h=r(d.bottom,h),u.push({dom:c,left:d.left,top:d.top})}}),o*=c,s*=c,l*=c,h*=c;var d=l-o,p=h-s,g=Kc();g.width=d,g.height=p;var v=fn(g);return Mm(u,function(t){var e=new tn({style:{x:t.left*c-o,y:t.top*c-s,image:t.dom}});v.add(e)}),v.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},Vm.convertToPixel=y(Hs,"convertToPixel"),Vm.convertFromPixel=y(Hs,"convertFromPixel"),Vm.containPixel=function(t,e){var i,n=this._model;return t=Mn(n,t),f(t,function(t,n){n.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(i|=a.containPoint(e,t))}},this)},this),!!i},Vm.getVisual=function(t,e){var i=this._model;t=Mn(i,t,{defaultMainType:"series"});var n=t.seriesModel,r=n.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},Vm.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},Vm.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Gm={prepareAndUpdate:function(t){Ws(this),Gm.update.call(this,t)},update:function(t){var e=this._model,i=this._api,n=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,i),a.performDataProcessorTasks(e,t),Gs(this,e),r.update(e,i),Zs(e),a.performVisualTasks(e,t),$s(this,e,i,t);var o=e.get("backgroundColor")||"transparent";if(Hc.canvasSupported)n.setBackgroundColor(o);else{var s=Le(o);o=ze(s,"rgb"),0===s[3]&&(o="transparent")}Js(e,i)}},updateTransform:function(t){var e=this._model,i=this,n=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=i.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,n,t);l&&l.update&&r.push(s)}else r.push(s)});var a=R();e.eachSeries(function(r){var o=i._chartsMap[r.__viewId];if(o.updateTransform){var s=o.updateTransform(r,e,n,t);s&&s.update&&a.set(r.uid,1)}else a.set(r.uid,1)}),Zs(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:a}),Qs(i,e,n,t,a),Js(e,this._api)}},updateView:function(t){var e=this._model;e&&(as.markUpdateMethod(t,"updateView"),Zs(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),$s(this,this._model,this._api,t),Js(e,this._api))},updateVisual:function(t){Gm.update.call(this,t)},updateLayout:function(t){Gm.update.call(this,t)}};Vm.resize=function(t){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var i=e.resetOption("media"),n=t&&t.silent;this[Nm]=!0,i&&Ws(this),Gm.update.call(this),this[Nm]=!1,Ys.call(this,n),qs.call(this,n)}},Vm.showLoading=function(t,e){if(Cm(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Qm[t]){var i=Qm[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},Vm.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},Vm.makeActionFromEvent=function(t){var e=o({},t);return e.type=qm[t.type],e},Vm.dispatchAction=function(t,e){if(Cm(e)||(e={silent:!!e}),Ym[t.type]&&this._model){if(this[Nm])return void this._pendingActions.push(t);Xs.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&Hc.browser.weChat&&this._throttledZrFlush(),Ys.call(this,e.silent),qs.call(this,e.silent)}},Vm.appendData=function(t){var e=t.seriesIndex,i=this.getModel(),n=i.getSeriesByIndex(e);n.appendData(t),this._scheduler.unfinished=!0},Vm.on=Rs("on"),Vm.off=Rs("off"),Vm.one=Rs("one");var Xm=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];Vm._initEvents=function(){Mm(Xm,function(t){var e=function(e){var i,n=this.getModel(),r=e.target,a="globalout"===t;if(a)i={};else if(r&&null!=r.dataIndex){var s=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(i=o({},r.eventData));if(i){var l=i.componentType,h=i.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",h=i.seriesIndex);var u=l&&null!=h&&n.getComponent(l,h),c=u&&this["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];i.event=e,i.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:i,model:u,view:c},this.trigger(t,i)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),Mm(qm,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},Vm.isDisposed=function(){return this._disposed},Vm.clear=function(){this.setOption({series:[]},!0)},Vm.dispose=function(){if(!this._disposed){this._disposed=!0,Cn(this.getDom(),ny,"");var t=this._api,e=this._model;Mm(this._componentsViews,function(i){i.dispose(e,t)}),Mm(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete Jm[this.id]}},c(Ns,rd),rl.prototype={constructor:rl,normalizeQuery:function(t){var e={},i={},n={};if(w(t)){var r=Im(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],o={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<a.length;l++){var h=a[l],u=r.lastIndexOf(h);if(u>0&&u===r.length-h.length){var c=r.slice(0,u);"data"!==c&&(e.mainType=c,e[h.toLowerCase()]=t,s=!0)}}o.hasOwnProperty(r)&&(i[r]=t,s=!0),s||(n[r]=t)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},filter:function(t,e){function i(t,e,i,n){return null==t[i]||e[n||i]===t[i]}var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var l=e.cptQuery,h=e.dataQuery;return i(l,o,"mainType")&&i(l,o,"subType")&&i(l,o,"index","componentIndex")&&i(l,o,"name")&&i(l,o,"id")&&i(h,a,"name")&&i(h,a,"dataIndex")&&i(h,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a))},afterTrigger:function(){this.eventInfo=null}};var Ym={},qm={},Um=[],jm=[],Zm=[],$m=[],Km={},Qm={},Jm={},ty={},ey=new Date-0,iy=new Date-0,ny="_echarts_instance_",ry=ll;xl(Bm,Zv),fl(Iv),pl(Lm,Dv),bl("default",Jv),vl({type:"highlight",event:"highlight",update:"highlight"},F),vl({type:"downplay",event:"downplay",update:"downplay"},F),dl("light",sm),dl("dark",cm);var ay={},oy="__ec_stack_",sy=.5,ly="undefined"!=typeof Float32Array?Float32Array:Array,hy={seriesType:"bar",plan:Wv(),reset:function(t){function e(t,e){for(var i,c=new ly(2*t.count),d=[],f=[],p=0;null!=(i=t.next());)f[h]=e.get(o,i),f[1-h]=e.get(s,i),d=n.dataToPoint(f,null,d),c[p++]=d[0],c[p++]=d[1];e.setLayout({largePoints:c,barWidth:u,valueAxisStart:Vl(r,a,!1),valueAxisHorizontal:l})}if(Hl(t)&&Wl(t)){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=n.getOtherAxis(r),o=i.mapDimension(a.dim),s=i.mapDimension(r.dim),l=a.isHorizontal(),h=l?0:1,u=Fl(zl([t]),r,t).width;return u>sy||(u=sy),{progress:e}}}};Gl.prototype.parse=function(t){return t},Gl.prototype.getSetting=function(t){return this._setting[t]},Gl.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Gl.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Gl.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Gl.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Gl.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Gl.prototype.getExtent=function(){return this._extent.slice()},Gl.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},Gl.prototype.isBlank=function(){return this._isBlank},Gl.prototype.setBlank=function(t){this._isBlank=t},Gl.prototype.getLabel=null,Pn(Gl),En(Gl,{registerWhenExtend:!0}),Xl.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&p(i,ql);return new Xl({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})};var uy=Xl.prototype;uy.getOrdinal=function(t){return Yl(this).get(t)},uy.parseAndCollect=function(t){var e,i=this._needCollect;if("string"!=typeof t&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=Yl(this);return e=n.get(t),null==e&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=0/0),e};var cy=Gl.prototype,dy=Gl.extend({type:"ordinal",init:function(t,e){(!t||_(t))&&(t=new Xl({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),cy.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return cy.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(cy.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:F,niceExtent:F});dy.create=function(){return new dy};var fy=Ia,py=Ia,gy=Gl.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),gy.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=jl(t)},getTicks:function(){return Kl(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=Da(t)||0:"auto"===i&&(i=this._intervalPrecision),t=py(t,i,!0),za(t)},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){0>r&&(r=-r,n.reverse());var a=Ul(n,t,e,i);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax?e[0]-=i/2:(e[1]+=i/2,e[0]-=i/2)}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=py(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=py(Math.ceil(e[1]/r)*r))}});gy.create=function(){return new gy};var vy=gy.prototype,my=Math.ceil,yy=Math.floor,_y=1e3,xy=60*_y,wy=60*xy,by=24*wy,Sy=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][1]<e?i=r+1:n=r}return i},My=gy.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return Va(e[0],i,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=by,e[1]+=by),e[1]===-1/0&&1/0===e[0]){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-by}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=Ia(yy(e[0]/n)*n)),t.fixMax||(e[1]=Ia(my(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],a=r/t;null!=e&&e>a&&(a=e),null!=i&&a>i&&(a=i);var o=Ty.length,s=Sy(Ty,a,0,o),l=Ty[Math.min(s,o-1)],h=l[1];if("year"===l[0]){var u=r/h,c=Ea(u/t,!0);h*=c}var d=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,f=[Math.round(my((n[0]-d)/h)*h+d),Math.round(yy((n[1]-d)/h)*h+d)];$l(f,n),this._stepLvl=l,this._interval=h,this._niceExtent=f},parse:function(t){return+La(t)}});f(["contain","normalize"],function(t){My.prototype[t]=function(e){return vy[t].call(this,this.parse(e))}});var Ty=[["hh:mm:ss",_y],["hh:mm:ss",5*_y],["hh:mm:ss",10*_y],["hh:mm:ss",15*_y],["hh:mm:ss",30*_y],["hh:mm\nMM-dd",xy],["hh:mm\nMM-dd",5*xy],["hh:mm\nMM-dd",10*xy],["hh:mm\nMM-dd",15*xy],["hh:mm\nMM-dd",30*xy],["hh:mm\nMM-dd",wy],["hh:mm\nMM-dd",2*wy],["hh:mm\nMM-dd",6*wy],["hh:mm\nMM-dd",12*wy],["MM-dd\nyyyy",by],["MM-dd\nyyyy",2*by],["MM-dd\nyyyy",3*by],["MM-dd\nyyyy",4*by],["MM-dd\nyyyy",5*by],["MM-dd\nyyyy",6*by],["week",7*by],["MM-dd\nyyyy",10*by],["week",14*by],["week",21*by],["month",31*by],["week",42*by],["month",62*by],["week",70*by],["quarter",95*by],["month",31*by*4],["month",31*by*5],["half-year",380*by/2],["month",31*by*8],["month",31*by*10],["year",380*by]];My.create=function(t){return new My({useUTC:t.ecModel.get("useUTC")})};var Cy=Gl.prototype,Iy=gy.prototype,Dy=Da,ky=Ia,Ay=Math.floor,Py=Math.ceil,Ly=Math.pow,Oy=Math.log,By=Gl.extend({type:"log",base:10,$constructor:function(){Gl.apply(this,arguments),this._originalScale=new gy},getTicks:function(){var t=this._originalScale,e=this._extent,i=t.getExtent();return p(Iy.getTicks.call(this),function(n){var r=Ia(Ly(this.base,n));return r=n===e[0]&&t.__fixMin?Ql(r,i[0]):r,r=n===e[1]&&t.__fixMax?Ql(r,i[1]):r},this)},getLabel:Iy.getLabel,scale:function(t){return t=Cy.scale.call(this,t),Ly(this.base,t)},setExtent:function(t,e){var i=this.base;t=Oy(t)/Oy(i),e=Oy(e)/Oy(i),Iy.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=Cy.getExtent.call(this);e[0]=Ly(t,e[0]),e[1]=Ly(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=Ql(e[0],n[0])),i.__fixMax&&(e[1]=Ql(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Oy(t[0])/Oy(e),t[1]=Oy(t[1])/Oy(e),Cy.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=Oa(i),r=t/i*n;for(.5>=r&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var a=[Ia(Py(e[0]/n)*n),Ia(Ay(e[1]/n)*n)];this._interval=n,this._niceExtent=a}},niceExtent:function(t){Iy.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){By.prototype[t]=function(e){return e=Oy(e)/Oy(this.base),Cy[t].call(this,e)}}),By.create=function(){return new By};var Ey=function(t){this._axes={},this._dimList=[],this.name=t||""};Ey.prototype={constructor:Ey,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,uh,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},ch.prototype={constructor:ch,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,i){var n=this.getAxis("x"),r=this.getAxis("y");return i=i||[],i[0]=n.toGlobalCoord(n.dataToCoord(t[0])),i[1]=r.toGlobalCoord(r.dataToCoord(t[1])),i},clampData:function(t,e){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,r=i.getExtent(),a=n.getExtent(),o=i.parse(t[0]),s=n.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return e=e||[],e[0]=i.coordToData(i.toLocalCoord(t[0])),e[1]=n.coordToData(n.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},u(ch,Ey);var zy=Sn(),Ry=[0,1],Fy=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};Fy.prototype={constructor:Fy,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return ka(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&(i=i.slice(),Ch(i,n.count())),Ta(t,Ry,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&(i=i.slice(),Ch(i,n.count()));var r=Ta(t,i,Ry,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=fh(this,e),n=i.ticks,r=p(n,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),a=e.get("alignWithLabel");return Ih(this,r,i.tickCategoryInterval,a,t.clamp),r},getViewLabels:function(){return dh(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return bh(this)}};var Ny=function(t,e,i,n,r){Fy.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"};Ny.prototype={constructor:Ny,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},u(Ny,Fy);var Hy={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},Wy={};Wy.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Hy),Wy.valueAxis=r({boundaryGap:[0,0],splitNumber:5},Hy),Wy.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},Wy.valueAxis),Wy.logAxis=s({scale:!0,logBase:10},Wy.valueAxis);var Vy=["value","category","time","log"],Gy=function(t,e,i,n){f(Vy,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,n){var a=this.layoutMode,s=a?qa(e):{},l=n.getTheme();r(e,l.get(o+"Axis")),r(e,this.getDefaultOption()),e.type=i(t,e),a&&Ya(e,s,a)},optionUpdated:function(){var t=this.option;
"category"===t.type&&(this.__ordinalMeta=Xl.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:a([{},Wy[o+"Axis"],n],!0)})}),Kg.registerSubTypeDefaulter(t+"Axis",y(i,t))},Xy={getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!C(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:F,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},Yy=Kg.extend({type:"cartesian2dAxis",axis:null,init:function(){Yy.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){Yy.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){Yy.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(Yy.prototype,Xy);var qy={offset:0};Gy("x",Yy,Dh,qy),Gy("y",Yy,Dh,qy),Kg.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Uy=Ah.prototype;Uy.type="grid",Uy.axisPointerEnabled=!0,Uy.getRect=function(){return this._rect},Uy.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),f(i.x,function(t){eh(t.scale,t.model)}),f(i.y,function(t){eh(t.scale,t.model)});var n={};f(i.x,function(t){Ph(i,"y",t,n)}),f(i.y,function(t){Ph(i,"x",t,n)}),this.resize(this.model,e)},Uy.resize=function(t,e,i){function n(){f(a,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),Oh(t,e?r.x:r.y)})}var r=Xa(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var a=this._axesList;n(),!i&&t.get("containLabel")&&(f(a,function(t){if(!t.model.get("axisLabel.inside")){var e=oh(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),n())},Uy.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},Uy.getAxes=function(){return this._axesList.slice()},Uy.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}b(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},Uy.getCartesians=function(){return this._coordsList.slice()},Uy.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},Uy.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},Uy._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r.coordinateSystem,h(l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},Uy.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},Uy._initCartesian=function(t,e){function i(i){return function(o,s){if(kh(o,t,e)){var l=o.get("position");"x"===i?"top"!==l&&"bottom"!==l&&(l="bottom",n[l]&&(l="top"===l?"bottom":"top")):"left"!==l&&"right"!==l&&(l="left",n[l]&&(l="left"===l?"right":"left")),n[l]=!0;var h=new Ny(i,ih(o),[0,0],o.get("type"),l),u="category"===h.type;h.onBand=u&&o.get("boundaryGap"),h.inverse=o.get("inverse"),o.axis=h,h.model=o,h.grid=this,h.index=s,this._axesList.push(h),r[i][s]=h,a[i]++}}}var n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};return e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),a.x&&a.y?(this._axesMap=r,void f(r.x,function(e,i){f(r.y,function(n,r){var a="x"+i+"y"+r,o=new ch(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(n)},this)},this)):(this._axesMap={},void(this._axesList=[]))},Uy._updateScale=function(t,e){function i(t,e){f(t.mapDimension(e.dim,!0),function(i){e.scale.unionExtentFromData(t,Ll(t,i))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(n){if(Eh(n)){var r=Bh(n,t),a=r[0],o=r[1];if(!kh(a,e,t)||!kh(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=n.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(i(l,h,n),i(l,u,n))}},this)},Uy.getTooltipAxes=function(t){var e=[],i=[];return f(this.getCartesians(),function(n){var r=null!=t&&"auto"!==t?n.getAxis(t):n.getBaseAxis(),a=n.getOtherAxis(r);h(e,r)<0&&e.push(r),h(i,a)<0&&i.push(a)}),{baseAxes:e,otherAxes:i}};var jy=["xAxis","yAxis"];Ah.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,r){var a=new Ah(n,t,e);a.name="grid_"+r,a.resize(n,e,!0),n.coordinateSystem=a,i.push(a)}),t.eachSeries(function(e){if(Eh(e)){var i=Bh(e,t),n=i[0],r=i[1],a=n.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(n.componentIndex,r.componentIndex)}}),i},Ah.dimensions=Ah.prototype.dimensions=ch.prototype.dimensions,xo.register("cartesian2d",Ah),Rh.prototype={constructor:Rh,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,i=this._new,n={},r={},a=[],o=[];for(Fh(e,n,a,"_oldKeyGetter",this),Fh(i,r,o,"_newKeyGetter",this),t=0;t<e.length;t++){var s=a[t],l=r[s];if(null!=l){var h=l.length;h?(1===h&&(r[s]=null),l=l.unshift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<o.length;t++){var s=o[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var u=0,h=l.length;h>u;u++)this._add&&this._add(l[u]);else this._add&&this._add(l)}}}};var Zy=R(["tooltip","label","itemName","itemId","seriesName"]),$y=b,Ky="undefined",Qy=-1,Jy="e\x00\x00",t_={"float":typeof Float64Array===Ky?Array:Float64Array,"int":typeof Int32Array===Ky?Array:Int32Array,ordinal:Array,number:Array,time:Array},e_=typeof Uint32Array===Ky?Array:Uint32Array,i_=typeof Int32Array===Ky?Array:Int32Array,n_=typeof Uint16Array===Ky?Array:Uint16Array,r_=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],a_=["_extent","_approximateExtent","_rawExtent"],o_=function(t,e){t=t||["x","y"];for(var i={},n=[],r={},a=0;a<t.length;a++){var o=t[a];w(o)&&(o={name:o});var s=o.name;o.type=o.type||"float",o.coordDim||(o.coordDim=s,o.coordDimIndex=0),o.otherDims=o.otherDims||{},n.push(s),i[s]=o,o.index=a,o.createInvertedIndices&&(r[s]=[])}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=Nh(this),this._invertedIndicesMap=r,this._calculationInfo={}},s_=o_.prototype;s_.type="list",s_.hasItemOption=!0,s_.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},s_.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},s_.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},s_.mapDimension=function(t,e){var i=this._dimensionsSummary;if(null==e)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return e===!0?(n||[]).slice():n&&n[e]},s_.initData=function(t,e,i){var n=Qa.isInstance(t)||d(t);n&&(t=new No(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},i||(this.hasItemOption=!1),this.defaultDimValueGetter=Lv[this._rawData.getSource().sourceFormat],this._dimValueGetter=i=i||this.defaultDimValueGetter,this._dimValueGetterArrayRows=Lv.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},s_.getProvider=function(){return this._rawData},s_.appendData=function(t){var e=this._rawData,i=this.count();e.appendData(t);var n=e.count();e.persistent||(n+=i),this._initDataFromProvider(i,n)},s_.appendValues=function(t,e){for(var i=this._chunkSize,n=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;a>u;u++){var c=r[u];o[c]||(o[c]=tu()),n[c]||(n[c]=[]),Yh(n,this._dimensionInfos[c],i,h,l),this._chunkCount=n[c].length}for(var d=new Array(a),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/i),v=f%i,m=0;a>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);n[c][g][v]=y;var _=o[c];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},qh(this)},s_._initDataFromProvider=function(t,e){if(!(t>=e)){for(var i,n=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=o[p];c[g]||(c[g]=tu());var v=l[g];0===v.otherDims.itemName&&(i=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),Yh(a,v,n,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var _=Math.floor(y/n),x=y%n,w=0;s>w;w++){var g=o[w],b=a[g][_],S=this._dimValueGetter(m,g,y,w);b[x]=S;var M=c[g];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var T=h[y];if(m&&null==T)if(null!=m.name)h[y]=T=m.name;else if(null!=i){var C=o[i],I=a[C][_];if(I){T=I[x];var D=l[C].ordinalMeta;D&&D.categories.length&&(T=D.categories[T])}}var k=null==m?null:m.id;null==k&&null!=T&&(d[T]=d[T]||0,k=T,d[T]>0&&(k+="__ec__"+d[T]),d[T]++),null!=k&&(u[y]=k)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},qh(this)}},s_.count=function(){return this._count},s_.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var r=0;n>r;r++)t[r]=e[r]}else t=new i(e.buffer,0,n)}else for(var i=Vh(this),t=new i(this.count()),r=0;r<t.length;r++)t[r]=r;return t},s_.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var i=this._storage;if(!i[t])return 0/0;e=this.getRawIndex(e);var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[t][n],o=a[r];return o},s_.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var i=this._storage[t];if(!i)return 0/0;var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[n];return a[r]},s_._getFast=function(t,e){var i=Math.floor(e/this._chunkSize),n=e%this._chunkSize,r=this._storage[t][i];return r[n]},s_.getValues=function(t,e){var i=[];_(t)||(e=t,t=this.dimensions);for(var n=0,r=t.length;r>n;n++)i.push(this.get(t[n],e));return i},s_.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,i=this._dimensionInfos,n=0,r=e.length;r>n;n++)if("ordinal"!==i[e[n]].type&&isNaN(this.get(e[n],t)))return!1;return!0},s_.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],i=tu();if(!e)return i;var n,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();n=i;for(var o=n[0],s=n[1],l=0;r>l;l++){var h=this._getFast(t,this.getRawIndex(l));o>h&&(o=h),h>s&&(s=h)}return n=[o,s],this._extent[t]=n,n},s_.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},s_.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},s_.getCalculationInfo=function(t){return this._calculationInfo[t]},s_.setCalculationInfo=function(t,e){$y(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},s_.getSum=function(t){var e=this._storage[t],i=0;if(e)for(var n=0,r=this.count();r>n;n++){var a=this.get(t,n);isNaN(a)||(i+=a)}return i},s_.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var i=[].concat(e).sort(function(t,e){return t-e}),n=this.count();return 0===n?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},s_.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i[e];return null==n||isNaN(n)?Qy:n},s_.indexOfName=function(t){for(var e=0,i=this.count();i>e;e++)if(this.getName(e)===t)return e;return-1},s_.indexOfRawIndex=function(t){if(!this._indices)return t;if(t>=this._rawCount||0>t)return-1;var e=this._indices,i=e[t];if(null!=i&&i<this._count&&i===t)return t;for(var n=0,r=this._count-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},s_.indicesOfNearest=function(t,e,i){var n=this._storage,r=n[t],a=[];if(!r)return a;null==i&&(i=1/0);for(var o=Number.MAX_VALUE,s=-1,l=0,h=this.count();h>l;l++){var u=e-this.get(t,l),c=Math.abs(u);i>=u&&o>=c&&((o>c||u>=0&&0>s)&&(o=c,s=u,a.length=0),a.push(l))}return a},s_.getRawIndex=jh,s_.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],i=0;i<this.dimensions.length;i++){var n=this.dimensions[i];e.push(this.get(n,t))}return e},s_.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||Uh(this,this._nameDimIdx,e)||""},s_.getId=function(t){return $h(this,this.getRawIndex(t))},s_.each=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Kh(t),this.getDimension,this);for(var r=t.length,a=0;a<this.count();a++)switch(r){case 0:e.call(i,a);break;case 1:e.call(i,this.get(t[0],a),a);break;case 2:e.call(i,this.get(t[0],a),this.get(t[1],a),a);break;default:for(var o=0,s=[];r>o;o++)s[o]=this.get(t[o],a);s[o]=a,e.apply(i,s)}}},s_.filterSelf=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(Kh(t),this.getDimension,this);for(var r=this.count(),a=Vh(this),o=new a(r),s=[],l=t.length,h=0,u=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(i,c);else if(1===l){var g=this._getFast(u,f);d=e.call(i,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(u,f);s[v]=c,d=e.apply(i,s)}d&&(o[h++]=f)}return r>h&&(this._indices=o),this._count=h,this._extent={},this.getRawIndex=this._indices?Zh:jh,this}},s_.selectRange=function(t){if(this._count){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);var n=e.length;if(n){var r=this.count(),a=Vh(this),o=new a(r),s=0,l=e[0],h=t[l][0],u=t[l][1],c=!1;if(!this._indices){var d=0;if(1===n){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=h&&u>=y||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===n){for(var f=this._storage[l],_=this._storage[e[1]],x=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=_[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],S=b[m];(y>=h&&u>=y||isNaN(y))&&(S>=x&&w>=S||isNaN(S))&&(o[s++]=d),d++}c=!0}}if(!c)if(1===n)for(var m=0;r>m;m++){var M=this.getRawIndex(m),y=this._getFast(l,M);(y>=h&&u>=y||isNaN(y))&&(o[s++]=M)}else for(var m=0;r>m;m++){for(var T=!0,M=this.getRawIndex(m),p=0;n>p;p++){var C=e[p],y=this._getFast(i,M);(y<t[C][0]||y>t[C][1])&&(T=!1)}T&&(o[s++]=this.getRawIndex(m))}return r>s&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?Zh:jh,this}}},s_.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i),r},s_.map=function(t,e,i,n){i=i||n||this,t=p(Kh(t),this.getDimension,this);var r=Qh(this,t);r._indices=this._indices,r.getRawIndex=r._indices?Zh:jh;for(var a=r._storage,o=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;h>d;d++){for(var f=0;l>f;f++)u[f]=this.get(t[f],d);u[l]=d;var g=e&&e.apply(i,u);if(null!=g){"object"!=typeof g&&(o[0]=g,g=o);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,_=0;_<g.length;_++){var x=t[_],w=g[_],b=c[x],S=a[x];S&&(S[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},s_.downSample=function(t,e,i,n){for(var r=Qh(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(Vh(this))(h),f=0,p=0;h>p;p+=s){s>h-p&&(s=h-p,o.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/u),y=v%u;o[g]=l[m][y]}var _=i(o),x=this.getRawIndex(Math.min(p+n(o,_)||0,h-1)),w=Math.floor(x/u),b=x%u;l[w][b]=_,_<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=Zh,r},s_.getItemModel=function(t){var e=this.hostModel;return new ya(this.getRawDataItem(t),e,e&&e.ecModel)},s_.diff=function(t){var e=this;return new Rh(t?t.getIndices():[],this.getIndices(),function(e){return $h(t,e)},function(t){return $h(e,t)})},s_.getVisual=function(t){var e=this._visual;return e&&e[t]},s_.setVisual=function(t,e){if($y(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},s_.setLayout=function(t,e){if($y(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},s_.getLayout=function(t){return this._layout[t]},s_.getItemLayout=function(t){return this._itemLayouts[t]},s_.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?o(this._itemLayouts[t]||{},e):e},s_.clearItemLayouts=function(){this._itemLayouts.length=0},s_.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},s_.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=n,$y(e))for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a],r[a]=!0);else n[e]=i,r[e]=!0},s_.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var l_=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};s_.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(l_,e)),this._graphicEls[t]=e},s_.getItemGraphicEl=function(t){return this._graphicEls[t]},s_.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},s_.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new o_(e,this.hostModel)}if(t._storage=this._storage,Xh(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?Zh:jh,t},s_.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(A(arguments)))})},s_.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],s_.CHANGABLE_METHODS=["filterSelf","selectRange"];var h_=function(t,e){return e=e||{},eu(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})},u_=Fv.extend({type:"series.__base_bar__",getInitialData:function(){return ru(this.getSource(),this)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(e.clampData(t)),n=this.getData(),r=n.getLayout("offset"),a=n.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});u_.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t}});var c_=Yf([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),d_={getBarItemStyle:function(t){var e=c_(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},f_=["itemStyle","barBorderWidth"];o(ya.prototype,d_),Cl({type:"bar",render:function(t,e,i){this._updateDrawMode(t);var n=t.get("coordinateSystem");return("cartesian2d"===n||"polar"===n)&&(this._isLargeDraw?this._renderLarge(t,e,i):this._renderNormal(t,e,i)),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,i=this.group,n=t.getData(),r=this._data,a=t.coordinateSystem,o=a.getBaseAxis();"cartesian2d"===a.type?e=o.isHorizontal():"polar"===a.type&&(e="angle"===o.dim);var s=t.isAnimationEnabled()?t:null;n.diff(r).add(function(r){if(n.hasValue(r)){var o=n.getItemModel(r),l=g_[a.type](n,r,o),h=p_[a.type](n,r,o,l,e,s);n.setItemGraphicEl(r,h),i.add(h),du(h,n,r,o,l,t,e,"polar"===a.type)}}).update(function(o,l){var h=r.getItemGraphicEl(l);if(!n.hasValue(o))return void i.remove(h);var u=n.getItemModel(o),c=g_[a.type](n,o,u);h?ha(h,{shape:c},s,o):h=p_[a.type](n,o,u,c,e,s,!0),n.setItemGraphicEl(o,h),i.add(h),du(h,n,o,u,c,t,e,"polar"===a.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===a.type?e&&uu(t,s,e):e&&cu(t,s,e)}).execute(),this._data=n},_renderLarge:function(t){this._clear(),pu(t,this.group)},_incrementalRenderLarge:function(t,e){pu(e,this.group,!0)},dispose:F,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,i=this._data;t&&t.get("animation")&&i&&!this._isLargeDraw?i.eachItemGraphicEl(function(e){"sector"===e.type?cu(e.dataIndex,t,e):uu(e.dataIndex,t,e)}):e.removeAll(),this._data=null}});var p_={cartesian2d:function(t,e,i,n,r,a,s){var l=new ug({shape:o({},n)});if(a){var h=l.shape,u=r?"height":"width",c={};h[u]=0,c[u]=n[u],kg[s?"updateProps":"initProps"](l,{shape:c},a,e)}return l},polar:function(t,e,i,n,r,a,o){var l=n.startAngle<n.endAngle,h=new ig({shape:s({clockwise:l},n)});if(a){var u=h.shape,c=r?"r":"endAngle",d={};u[c]=r?0:n.startAngle,d[c]=n[c],kg[o?"updateProps":"initProps"](h,{shape:d},a,e)}return h}},g_={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=fu(i,n),a=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+a*r/2,y:n.y+o*r/2,width:n.width-a*r,height:n.height-o*r}},polar:function(t,e){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}},v_=pr.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var i=e.points,n=this.__startPoint,r=this.__valueIdx,a=0;a<i.length;a+=2)n[this.__valueIdx]=i[a+r],t.moveTo(n[0],n[1]),t.lineTo(i[a],i[a+1])}}),m_=Dr({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),y_=Dr({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),__=Dr({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=n-a+o+s,h=Math.asin(s/o),u=Math.cos(h)*o,c=Math.sin(h),d=Math.cos(h),f=.6*o,p=.7*o;t.moveTo(i-u,l+s),t.arc(i,l,o,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(i+u-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-u+c*f,l+s+d*f,i-u,l+s),t.closePath()}}),x_=Dr({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),w_={line:dg,rect:ug,roundRect:ug,square:ug,circle:Jp,diamond:y_,pin:__,arrow:x_,triangle:m_},b_={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},S_={};f(w_,function(t,e){S_[e]=new t});var M_=Dr({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,i){var n=e.symbolType,r=S_[n];"none"!==e.symbolType&&(r||(n="rect",r=S_[n]),b_[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i))}}),T_=Math.PI,C_=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Vd;var i=new Vd({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};C_.prototype={constructor:C_,hasBuilder:function(t){return!!I_[t]},add:function(t){I_[t].call(this)},getGroup:function(){return this.group}};var I_={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis.getExtent(),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(Z(r,r,n),Z(a,a,n));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new dg(Br({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),u=e.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof h||"number"==typeof h)&&(h=[h,h]);var c=h[0],d=h[1];f([{rotate:t.rotation+Math.PI/2,offset:u[0],r:0},{rotate:t.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,i){if("none"!==l[i]&&null!=l[i]){var n=mu(l[i],-c/2,-d/2,c,d,s.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];n.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(n)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=Tu(this,t,e),n=Cu(this,t,e);wu(t,n,i)},axisName:function(){var t=this.opt,e=this.axisModel,i=I(t.axisName,e.get("name"));if(i){var n,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,Mu(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*T_/180);var f;Mu(r)?n=D_(t.rotation,null!=d?d:t.rotation,a):(n=_u(t,r,d||0,h),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(n.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=I(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?Xg(i,m,p,v,{minChar:2,placeholder:g.placeholder}):i,_=e.get("tooltip",!0),x=e.mainType,w={componentType:x,name:i,$vars:["name"]};w[x+"Index"]=e.componentIndex;var b=new Qp({anid:"name",__fullText:i,__truncatedText:y,position:c,rotation:n.rotation,silent:xu(e),z2:1,tooltip:_&&_.show?o({content:i,formatter:function(){return i},formatterParams:w},_):null});Jr(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:n.textAlign,textVerticalAlign:n.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=yu(e),b.eventData.targetType="axisName",b.eventData.name=i),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},D_=C_.innerTextLayout=function(t,e,i){var n,r,a=Aa(e-t);return Pa(a)?(r=i>0?"top":"bottom",n="center"):Pa(a-T_)?(r=i>0?"bottom":"top",n="center"):(r="middle",n=a>0&&T_>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,textVerticalAlign:r}},k_=f,A_=y,P_=Ml({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&Ou(t),P_.superApply(this,"render",arguments),Fu(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n){Fu(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),P_.superApply(this,"remove",arguments)},dispose:function(t,e){Nu(this,e),P_.superApply(this,"dispose",arguments)}}),L_=[];P_.registerAxisPointerClass=function(t,e){L_[t]=e},P_.getAxisPointerClass=function(t){return t&&L_[t]};var O_=["axisLine","axisTickLabel","axisName"],B_=["splitArea","splitLine"],E_=P_.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Vd,this.group.add(this._axisGroup),t.get("show")){var a=t.getCoordSysModel(),o=Hu(a,t),s=new C_(t,o);f(O_,s.add,s),this._axisGroup.add(s.getGroup()),f(B_,function(e){t.get(e+".show")&&this["_"+e](t,a)},this),pa(r,this._axisGroup,t),E_.superCall(this,"render",t,e,i,n)}},remove:function(){this._splitAreaColors=null},_splitLine:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),r=n.getModel("lineStyle"),a=r.get("color");a=_(a)?a:[a];for(var o=e.coordinateSystem.getRect(),l=i.isHorizontal(),h=0,u=i.getTicksCoords({tickModel:n}),c=[],d=[],f=r.getLineStyle(),p=0;p<u.length;p++){var g=i.toGlobalCoord(u[p].coord);l?(c[0]=g,c[1]=o.y,d[0]=g,d[1]=o.y+o.height):(c[0]=o.x,c[1]=g,d[0]=o.x+o.width,d[1]=g);var v=h++%a.length,m=u[p].tickValue;this._axisGroup.add(new dg(Br({anid:null!=m?"line_"+u[p].tickValue:null,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:a[v]},f),silent:!0})))}}},_splitArea:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitArea"),r=n.getModel("areaStyle"),a=r.get("color"),o=e.coordinateSystem.getRect(),l=i.getTicksCoords({tickModel:n,clamp:!0});if(l.length){var h=a.length,u=this._splitAreaColors,c=R(),d=0;if(u)for(var f=0;f<l.length;f++){var p=u.get(l[f].tickValue);if(null!=p){d=(p+(h-1)*f)%h;break}}var g=i.toGlobalCoord(l[0].coord),v=r.getAreaStyle();a=_(a)?a:[a];for(var f=1;f<l.length;f++){var m,y,x,w,b=i.toGlobalCoord(l[f].coord);i.isHorizontal()?(m=g,y=o.y,x=b-m,w=o.height,g=m+x):(m=o.x,y=g,x=o.width,w=b-y,g=y+w);var S=l[f-1].tickValue;null!=S&&c.set(S,d),this._axisGroup.add(new ug({anid:null!=S?"area_"+S:null,shape:{x:m,y:y,width:x,height:w},style:s({fill:a[d]},v),silent:!0})),d=(d+1)%h}this._splitAreaColors=c}}}});E_.extend({type:"xAxis"}),E_.extend({type:"yAxis"}),Ml({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new ug({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))
}}),fl(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),_l(y(Nl,"bar")),_l(hy),xl({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}});var z_=Sl({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{}},mergeOption:function(t){z_.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],i=[];t.eachRawSeries(function(n){var r=n.name;i.push(r);var a;if(n.legendDataProvider){var o=n.legendDataProvider(),s=o.mapArray(o.getName);t.isSeriesFiltered(n)||(i=i.concat(s)),s.length?e=e.concat(s):a=!0}else a=!0;a&&xn(n)&&e.push(n.name)}),this._availableNames=i;var n=this.get("data")||e,r=p(n,function(t){return("string"==typeof t||"number"==typeof t)&&(t={name:t}),new ya(t,this,this.ecModel)},this);this._data=r},getData:function(){return this._data},select:function(t){var e=this.option.selected,i=this.get("selectedMode");if("single"===i){var n=this._data;f(n,function(t){e[t.get("name")]=!1})}e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&h(this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});vl("legendToggleSelect","legendselectchanged",y(Wu,"toggleSelected")),vl("legendSelect","legendselected",y(Wu,"select")),vl("legendUnSelect","legendunselected",y(Wu,"unSelect"));var R_=y,F_=f,N_=Vd,H_=Ml({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new N_),this._backgroundEl,this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},render:function(t,e,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left"),this.renderInner(r,t,e,i);var a=t.getBoxLayoutParams(),o={width:i.getWidth(),height:i.getHeight()},l=t.get("padding"),h=Xa(a,o,l),u=this.layoutInner(t,r,h,n),c=Xa(s({width:u.width,height:u.height},a),o,l);this.group.attr("position",[c.x-u.x,c.y-u.y]),this.group.add(this._backgroundEl=Vu(u,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(t,e,i,n){var r=this.getContentGroup(),a=R(),o=e.get("selectedMode"),s=[];i.eachRawSeries(function(t){!t.get("legendHoverLink")&&s.push(t.id)}),F_(e.getData(),function(l,h){var u=l.get("name");if(!this.newlineDisabled&&(""===u||"\n"===u))return void r.add(new N_({newline:!0}));var c=i.getSeriesByName(u)[0];if(!a.get(u))if(c){var d=c.getData(),f=d.getVisual("color");"function"==typeof f&&(f=f(c.getDataParams(0)));var p=d.getVisual("legendSymbol")||"roundRect",g=d.getVisual("symbol"),v=this._createItem(u,h,l,e,p,g,t,f,o);v.on("click",R_(Gu,u,n)).on("mouseover",R_(Xu,c.name,null,n,s)).on("mouseout",R_(Yu,c.name,null,n,s)),a.set(u,!0)}else i.eachRawSeries(function(i){if(!a.get(u)&&i.legendDataProvider){var r=i.legendDataProvider(),c=r.indexOfName(u);if(0>c)return;var d=r.getItemVisual(c,"color"),f="roundRect",p=this._createItem(u,h,l,e,f,null,t,d,o);p.on("click",R_(Gu,u,n)).on("mouseover",R_(Xu,null,u,n,s)).on("mouseout",R_(Yu,null,u,n,s)),a.set(u,!0)}},this)},this)},_createItem:function(t,e,i,n,r,a,s,l,h){var u=n.get("itemWidth"),c=n.get("itemHeight"),d=n.get("inactiveColor"),f=n.get("symbolKeepAspect"),p=n.isSelected(t),g=new N_,v=i.getModel("textStyle"),m=i.get("icon"),y=i.getModel("tooltip"),_=y.parentModel;if(r=m||r,g.add(mu(r,0,0,u,c,p?l:d,null==f?!0:f)),!m&&a&&(a!==r||"none"===a)){var x=.8*c;"none"===a&&(a="circle"),g.add(mu(a,(u-x)/2,(c-x)/2,x,x,p?l:d,null==f?!0:f))}var w="left"===s?u+5:-5,b=s,S=n.get("formatter"),M=t;"string"==typeof S&&S?M=S.replace("{name}",null!=t?t:""):"function"==typeof S&&(M=S(t)),g.add(new Qp({style:Jr({},v,{text:M,x:w,y:c/2,textFill:p?v.getTextColor():d,textAlign:b,textVerticalAlign:"middle"})}));var T=new ug({shape:g.getBoundingRect(),invisible:!0,tooltip:y.get("show")?o({content:t,formatter:_.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:t,$vars:["name"]}},y.option):null});return g.add(T),g.eachChild(function(t){t.silent=!0}),T.silent=!h,this.getContentGroup().add(g),$r(g),g.__legendDataIndex=e,g},layoutInner:function(t,e,i){var n=this.getContentGroup();jg(t.get("orient"),n,t.get("itemGap"),i.width,i.height);var r=n.getBoundingRect();return n.attr("position",[-r.x,-r.y]),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}}),W_=function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var i=0;i<e.length;i++)if(!e[i].isSelected(t.name))return!1;return!0})};pl(W_),Kg.registerSubTypeDefaulter("legend",function(){return"plain"});var V_=z_.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,i,n){var r=qa(t);V_.superCall(this,"init",t,e,i,n),qu(this,t,r)},mergeOption:function(t,e){V_.superCall(this,"mergeOption",t,e),qu(this,this.option,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}}),G_=Vd,X_=["width","height"],Y_=["x","y"],q_=H_.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){q_.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new G_),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new G_),this._showController},resetInner:function(){q_.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,i,n){function r(t,i){var r=t+"DataIndex",l=ma(e.get("pageIcons",!0)[e.getOrient().name][i],{onclick:m(a._pageGo,a,r,e,n)},{x:-s[0]/2,y:-s[1]/2,width:s[0],height:s[1]});l.name=t,o.add(l)}var a=this;q_.superCall(this,"renderInner",t,e,i,n);var o=this._controllerGroup,s=e.get("pageIconSize",!0);_(s)||(s=[s,s]),r("pagePrev",0);var l=e.getModel("pageTextStyle");o.add(new Qp({name:"pageText",style:{textFill:l.getTextColor(),font:l.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),r("pageNext",1)},layoutInner:function(t,e,i,n){var r=this.getContentGroup(),a=this._containerGroup,o=this._controllerGroup,s=t.getOrient().index,l=X_[s],h=X_[1-s],u=Y_[1-s];jg(t.get("orient"),r,t.get("itemGap"),s?i.width:null,s?null:i.height),jg("horizontal",o,t.get("pageButtonItemGap",!0));var c=r.getBoundingRect(),d=o.getBoundingRect(),f=this._showController=c[l]>i[l],p=[-c.x,-c.y];n||(p[s]=r.position[s]);var g=[0,0],v=[-d.x,-d.y],m=D(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(f){var y=t.get("pageButtonPosition",!0);"end"===y?v[s]+=i[l]-d[l]:g[s]+=d[l]+m}v[1-s]+=c[h]/2-d[h]/2,r.attr("position",p),a.attr("position",g),o.attr("position",v);var _=this.group.getBoundingRect(),_={x:0,y:0};if(_[l]=f?i[l]:c[l],_[h]=Math.max(c[h],d[h]),_[u]=Math.min(0,d[u]+v[1-s]),a.__rectSize=i[l],f){var x={x:0,y:0};x[l]=Math.max(i[l]-d[l]-m,0),x[h]=_[h],a.setClipPath(new ug({shape:x})),a.__rectSize=x[l]}else o.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var w=this._getPageInfo(t);return null!=w.pageIndex&&ha(r,{position:w.contentPosition},f?t:!1),this._updatePageInfoView(t,w),_},_pageGo:function(t,e,i){var n=this._getPageInfo(e)[t];null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:e.id})},_updatePageInfoView:function(t,e){var i=this._controllerGroup;f(["pagePrev","pageNext"],function(n){var r=null!=e[n+"DataIndex"],a=i.childOfName(n);a&&(a.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),a.cursor=r?"pointer":"default")});var n=i.childOfName("pageText"),r=t.get("pageFormatter"),a=e.pageIndex,o=null!=a?a+1:0,s=e.pageCount;n&&r&&n.setStyle("text",w(r)?r.replace("{current}",o).replace("{total}",s):r({current:o,total:s}))},_getPageInfo:function(t){function e(t){if(t){var e=t.getBoundingRect(),i=e[l]+t.position[o];return{s:i,e:i+e[s],i:t.__legendDataIndex}}}function i(t,e){return t.e>=e&&t.s<=e+a}var n=t.get("scrollDataIndex",!0),r=this.getContentGroup(),a=this._containerGroup.__rectSize,o=t.getOrient().index,s=X_[o],l=Y_[o],h=this._findTargetItemIndex(n),u=r.children(),c=u[h],d=u.length,f=d?1:0,p={contentPosition:r.position.slice(),pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return p;var g=e(c);p.contentPosition[o]=-g.s;for(var v=h+1,m=g,y=g,_=null;d>=v;++v)_=e(u[v]),(!_&&y.e>m.s+a||_&&!i(_,m.s))&&(m=y.i>m.i?y:_,m&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=m.i),++p.pageCount)),y=_;for(var v=h-1,m=g,y=g,_=null;v>=-1;--v)_=e(u[v]),_&&i(y,_.s)||!(m.i<y.i)||(y=m,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=m.i),++p.pageCount,++p.pageIndex),m=_;return p},_findTargetItemIndex:function(t){var e,i=this.getContentGroup();return this._showController?i.eachChild(function(i,n){i.__legendDataIndex===t&&(e=n)}):e=0,e}});vl("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;null!=i&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(i)})});var U_=function(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var a=i.getData(),o=bn(a,t);if(null==o||0>o||_(o))return{point:[]};var s=a.getItemGraphicEl(o),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(a.getValues(p(l.dimensions,function(t){return a.mapDimension(t)}),o,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),n=[h.x+h.width/2,h.y+h.height/2]}return{point:n,el:s}},j_=f,Z_=y,$_=Sn(),K_=function(t,e,i){var n=t.currTrigger,r=[t.x,t.y],a=t,o=t.dispatchAction||m(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){ic(r)&&(r=U_({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},e).point);var l=ic(r),h=a.axesInfo,u=s.axesInfo,c="leave"===n||ic(r),d={},f={},p={list:[],map:{}},g={showPointer:Z_(Zu,f),showTooltip:Z_($u,p)};j_(s.coordSysMap,function(t,e){var i=l||t.containPoint(r);j_(s.coordSysAxesInfo[e],function(t){var e=t.axis,n=tc(h,t);if(!c&&i&&(!h||n)){var a=n&&n.value;null!=a||l||(a=e.pointToData(r)),null!=a&&Uu(t,a,g,!1,d)}})});var v={};return j_(u,function(t,e){var i=t.linkGroup;i&&!f[e]&&j_(i.axesInfo,function(e,n){var r=f[n];if(e!==t&&r){var a=r.value;i.mapper&&(a=t.axis.scale.parse(i.mapper(a,ec(e),ec(t)))),v[t.key]=a}})}),j_(v,function(t,e){Uu(u[e],t,g,!0,d)}),Ku(f,u,d),Qu(p,r,t,o),Ju(u,o,i),d}},Q_=(Sl({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),Sn()),J_=f,tx=Ml({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";nc("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){hc(e.getZr(),"axisPointer"),tx.superApply(this._model,"remove",arguments)},dispose:function(t,e){hc("axisPointer",e),tx.superApply(this._model,"dispose",arguments)}}),ex=Sn(),ix=n,nx=m;uc.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==a){this._lastValue=r,this._lastStatus=a;var o=this._group,s=this._handle;if(!a||"hide"===a)return o&&o.hide(),void(s&&s.hide());o&&o.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(o){var c=y(cc,e,u);this.updatePointerEl(o,l,c,e),this.updateLabelEl(o,l,c,e)}else o=this._group=new Vd,this.createPointerEl(o,l,t,e),this.createLabelEl(o,l,t,e),i.getZr().add(o);gc(o,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===i||null==i){var o=this.animationThreshold;if(r&&n.getBandWidth()>o)return!0;if(a){var s=Bu(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>o}return!1}return i===!0},makeElOption:function(){},createPointerEl:function(t,e){var i=e.pointer;if(i){var n=ex(t).pointerEl=new kg[i.type](ix(e.pointer));t.add(n)}},createLabelEl:function(t,e,i,n){if(e.label){var r=ex(t).labelEl=new ug(ix(e.label));t.add(r),fc(r,n)}},updatePointerEl:function(t,e,i){var n=ex(t).pointerEl;n&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=ex(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),fc(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,r=e.getModel("handle"),a=e.get("status");if(!r.get("show")||!a||"hide"===a)return n&&i.remove(n),void(this._handle=null);var o;this._handle||(o=!0,n=this._handle=ma(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){sd(t.event)},onmousedown:nx(this._onHandleDragMove,this,0,0),drift:nx(this._onHandleDragMove,this),ondragend:nx(this._onHandleDragEnd,this)}),i.add(n)),gc(n,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];n.setStyle(r.getItemStyle(null,s));var l=r.get("size");_(l)||(l=[l,l]),n.attr("scale",[l[0]/2,l[1]/2]),cs(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)}},_moveHandleToValue:function(t,e){cc(this._axisPointerModel,!e&&this._moveAnimation,this._handle,pc(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(pc(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(pc(n)),ex(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}},uc.prototype.constructor=uc,Pn(uc);var rx=uc.extend({makeElOption:function(t,e,i,n,r){var a=i.axis,o=a.grid,s=n.get("type"),l=Mc(o,a).getOtherAxis(a).getGlobalExtent(),h=a.toGlobalCoord(a.dataToCoord(e,!0));if(s&&"none"!==s){var u=vc(n),c=ax[s](a,h,l,u);c.style=u,t.graphicKey=c.type,t.pointer=c}var d=Hu(o.model,i);wc(e,t,d,i,n,r)},getHandleTransform:function(t,e,i){var n=Hu(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:xc(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i){var n=i.axis,r=n.grid,a=n.getGlobalExtent(!0),o=Mc(r,n).getOtherAxis(n).getGlobalExtent(),s="x"===n.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(a[1],l[s]),l[s]=Math.max(a[0],l[s]);var h=(o[1]+o[0])/2,u=[h,h];u[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:c[s]}}}),ax={line:function(t,e,i,n){var r=bc([e,i[0]],[e,i[1]],Tc(t));return Br({shape:r,style:n}),{type:"Line",shape:r}},shadow:function(t,e,i){var n=Math.max(1,t.getBandWidth()),r=i[1]-i[0];return{type:"Rect",shape:Sc([e-n/2,i[0]],[n,r],Tc(t))}}};P_.registerAxisPointerClass("CartesianAxisPointer",rx),fl(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!_(e)&&(t.axisPointer.link=[e])}}),pl(Fm.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Iu(t,e)}),vl({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},K_),Sl({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var ox=f,sx=Ra,lx=["","-webkit-","-moz-","-o-"],hx="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";kc.prototype={constructor:kc,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i.position&&"absolute"!==e.position&&(i.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=hx+Dc(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i,n=this._zr;n&&n.painter&&(i=n.painter.getViewportRootOffset())&&(t+=i.offsetLeft,e+=i.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(m(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(t+=parseInt(i.paddingLeft,10)+parseInt(i.paddingRight,10)+parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),e+=parseInt(i.paddingTop,10)+parseInt(i.paddingBottom,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:t,height:e}}},Ac.prototype={constructor:Ac,_enterable:!0,update:function(){},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,i){this.el&&this._zr.remove(this.el);for(var n={},r=t,a="{marker",o="|}",s=r.indexOf(a);s>=0;){var l=r.indexOf(o),h=r.substr(s+a.length,l-s-a.length);n["marker"+h]=h.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[h],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[h]},r=r.substr(l+1),s=r.indexOf("{marker")}this.el=new Qp({style:{rich:n,text:t,textLineHeight:20,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding")},z:i.get("z")}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){this.el&&this.el.attr("position",[t,e])},hide:function(){this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(m(this.hide,this),t)):this.hide())},isShow:function(){return this._show},getOuterSize:function(){return this.getSize()}};var ux=m,cx=f,dx=Ca,fx=new ug({shape:{x:-1,y:-1,width:2,height:2}});Ml({type:"tooltip",init:function(t,e){if(!Hc.node){var i=t.getComponent("tooltip"),n=i.get("renderMode");this._renderMode=Dn(n);var r;"html"===this._renderMode?(r=new kc(e.getDom(),e),this._newLine="<br/>"):(r=new Ac(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,i){if(!Hc.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");nc("itemTooltip",this._api,ux(function(t,i,n){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(i,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!Hc.node){var r=Lc(n,i);this._ticket="";var a=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var o=fx;o.position=[n.x,n.y],o.update(),o.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:o},r)}else if(a)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,event:{},dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=U_(n,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:n.position,target:s.el,event:{}},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target,event:{}},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(Lc(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,a=n.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=Pc([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:n.position}),!0}}},_tryShow:function(t,e){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=m(e,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var i=this._ecModel,n=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],l=Pc([e.tooltipOption,n]),h=this._renderMode,u=this._newLine,c={};cx(t,function(t){cx(t.dataByAxis,function(t){var e=i.getComponent(t.axisDim+"Axis",t.axisIndex),n=t.value,a=[];if(e&&null!=n){var l=_c(n,e.axis,i,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(o){var u=i.getSeriesByIndex(o.seriesIndex),d=o.dataIndexInside,f=u&&u.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=ah(e.axis,n),f.axisValueLabel=l,f){s.push(f);var p,g=u.formatTooltip(d,!0,null,h);if(b(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;a.push(p)}});var d=l;o.push("html"!==h?a.join(u):(d?Fa(d)+u:"")+a.join(u))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(l,o,s,Math.random(),a[0],a[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,a=n.getSeriesByIndex(r),o=e.dataModel||a,s=e.dataIndex,l=e.dataType,h=o.getData(),u=Pc([h.getItemModel(s),o,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d,f,p=o.getDataParams(s,l),g=o.formatTooltip(s,!1,l,this._renderMode);b(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+o.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),i({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){var r=n;n={content:r,formatter:r}}var a=new ya(n,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var h=this._tooltipContent,u=t.get("formatter");o=o||t.get("position");var c=e;if(u&&"string"==typeof u)c=Na(u,i,!0);else if("function"==typeof u){var d=ux(function(e,n){e===this._ticket&&(h.setContent(n,l,t),this._updatePosition(t,o,r,a,h,i,s))},this);this._ticket=n,c=u(i,n,d)}h.setContent(c,l,t),h.show(t),this._updatePosition(t,o,r,a,h,i,s)}},_updatePosition:function(t,e,i,n,r,a,o){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=o&&o.getBoundingRect().clone();if(o&&d.applyTransform(o.transform),"function"==typeof e&&(e=e([i,n],a,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),_(e))i=dx(e[0],s),n=dx(e[1],l);else if(b(e)){e.width=h[0],e.height=h[1];var f=Xa(e,{width:s,height:l});i=f.x,n=f.y,u=null,c=null}else if("string"==typeof e&&o){var p=Ec(e,d,h);i=p[0],n=p[1]}else{var p=Oc(i,n,r,s,l,u?null:20,c?null:20);i=p[0],n=p[1]}if(u&&(i-=zc(u)?h[0]/2:"right"===u?h[0]:0),c&&(n-=zc(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var p=Bc(i,n,r,s,l);i=p[0],n=p[1]}r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,i=!!e&&e.length===t.length;return i&&cx(e,function(e,n){var r=e.dataByAxis||{},a=t[n]||{},o=a.dataByAxis||[];i&=r.length===o.length,i&&cx(r,function(t,e){var n=o[e]||{},r=t.seriesDataIndices||[],a=n.seriesDataIndices||[];i&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&r.length===a.length,i&&cx(r,function(t,e){var n=a[e];i&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=t,!!i},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){Hc.node||(this._tooltipContent.hide(),hc("itemTooltip",e))}}),vl({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),vl({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){}),t.version=Dm,t.dependencies=km,t.PRIORITY=Fm,t.init=ol,t.connect=sl,t.disConnect=ll,t.disconnect=ry,t.dispose=hl,t.getInstanceByDom=ul,t.getInstanceById=cl,t.registerTheme=dl,t.registerPreprocessor=fl,t.registerProcessor=pl,t.registerPostUpdate=gl,t.registerAction=vl,t.registerCoordinateSystem=ml,t.getCoordinateSystemDimensions=yl,t.registerLayout=_l,t.registerVisual=xl,t.registerLoading=bl,t.extendComponentModel=Sl,t.extendComponentView=Ml,t.extendSeriesModel=Tl,t.extendChartView=Cl,t.setCanvasCreator=Il,t.registerMap=Dl,t.getMap=kl,t.dataTool=ay});