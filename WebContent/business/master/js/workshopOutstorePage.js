var years = [];
$(document).ready(function () {
	var y = new Date().getFullYear();
	years[2] = y-- + '年';
	years[1] = y-- + '年';
	years[0] = y + '年';
	
    initQueryCtrl();
    initChart();
    initGrid();
	$('.ui-layout').each(function(){
		UI.Layout.init(this, {
			minWidth: 850,
			minHeight: 300
		});
	});
	$(window).resize();
    //初始化回车查询功能
    $('.query-panel input[type=text]').keydown(function(e){
		if(e.keyCode==13){
			$(this).parents('.query-panel:first').find('.btn-query').click();
		}
	});
	LoadMask.show();
    setTimeout(function () {
    	$.getJSON(common.ctx + 'reportview/report/data.do', {workshopId: workshopId,packageName: 'workshop', viewName: 'OutstoreGrid'}, function(result){
			LoadMask.hide();
            if (result.code == 'success') {
                gridStore.setResult(result.data);
                //默认选中汇总行
                $('.ui-grid-summary-row:first').each(function(){
                	selectRow(grid.dataElMap[this.id].id, true);
                });
            } else {
    			common.alertMes("加载出库扫码数据失败。" + result.errorMsg, "error");
            }
    	});
    }, 10);
});
function initQueryCtrl() {
}
var myChart;
function initChart(){
    // 基于准备好的dom，初始化echarts实例
    myChart = echarts.init(document.getElementById('chart'));

    // 指定图表的配置项和数据
    var options = {
        color: ['#4cabce', '#006699', '#003366'],        
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: years
        },
        xAxis: {
            data: ["1 月","2 月","3 月","4 月","5 月","6 月","7 月","8 月","9 月","10 月","11 月","12 月"]
        },
        yAxis: {},
        series: []
    };

    window.updateChart = function (data) {
      options.series = data.map(function(item) {
        item.type = 'bar'
        item.barGap = '10%'
        item.barCategoryGap = '30%'
        return item
      })
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(options);
    }
    var data = [];
    for(var i = 0; i < years.length; i++){
    	data.push({name: years[i], data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]});
    }
    updateChart(data);
}

//初始化表格
var grid = null, gridStore = null;
function initGrid() {
    gridStore = UI.Store.init({
		data: [],
		beforeload: function(){
			LoadMask.show();
		},
		afterload: function(){
			LoadMask.hide();
		}
	});
	grid = UI.Grid.init('#grid', {
		headers: [{
			title: 'SKU', 
			property: 'sku',
			width: 200, 
			endLock: true
		}, {
			title: '产品名称', 
			property: 'product_name',
//			align: 'right',
			width: 280
		}, {
			title: '扫码升数(L)', 
			property: 'sell_through',
//			align: 'right',
			width: 100,
			summary: true,
			formatter: function(value, record, index, rowId){
				if(!value){
					return '<b>0</b>';
				}
				return '<b>' + common.formatNumber(value, 1) + '</b>';
			}
		}],
		fit: true,
		autoLoad: true,
		cellPaddingY: 2, //单元格Y轴方向间距
		yOverflow: 'scroll',
		store: gridStore,
		summary: true,
		summaryTitle: '合计',
		dataRowEvents: {
			'onclick': function(rowId){
				selectRow(rowId);
			}
		}
	});
//    common.addFitAbilityForUIGrid(grid);
//    $(window).resize();
}

var currentRowId = null, xitems = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
function selectRow(rowId, summaryRow){
	if(currentRowId == rowId){
		return;
	}
	$('.item-highlight').removeClass('item-highlight');
	grid.addRowClass(rowId, 'item-highlight');
	currentRowId = rowId;
	var params = {
			workshopId: workshopId,
			packageName: 'workshop', 
			viewName: 'OutstoreChart',
			xfieldName: 'month',
			xitems: xitems,
			zfieldName: 'year',
			zitems: years,
			yfieldName: 'sell_through'
		};
	if(!summaryRow){
		var record = grid.getRecordById(rowId);
		if(record.sku){
			params.sku = record.sku;
		}
	}
	LoadMask.show();
    setTimeout(function () {
    	$.post(common.ctx + 'reportview/report/chartdata.do', $.param(params, true), function(result){
			LoadMask.hide();
            if (result.code == 'success') {
            	updateChart(result.data);
            } else {
    			common.alertMes("加载出库扫码数据失败。" + result.errorMsg, "error");
            }
    	}, 'json');
    }, 10);
}

//根据参数查询
function query() {
	gridStore.load(buildParams());
}

//构建当前查询条件
function buildParams(){
//	if ($("#queryPanel").hasClass("query-adv")) {
		return {
			queryField: null,
			queryType: 1,
			start: 0
		};
//	} else {
//		return {
//			queryField: $('#gKeyWord').val(),
//			queryType: 2,
//			start: 0
//		};
//	}
}
