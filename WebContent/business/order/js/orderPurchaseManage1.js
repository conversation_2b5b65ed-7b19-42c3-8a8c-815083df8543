
var rpcClient = common.rpcClient;

var orderTableDataStore;
var pageFlag = null;
var selectStatus = {};
var confirmOrderIds = null;

var order_grid = null;
$(document).ready(function(){
	renderOrderTable();
	initOrderSource();
    $("#pageFlag").val("");
    initQueryCtrl();
    initState();
    
    $('#queryPanel .control-text').keydown(function(e){
		if(e.keyCode==13){
			queryOrdersByParam();
		}
	});
});

function initQueryCtrl(){
	/*Ctrls.rpcClient = common.rpcClient;
	var partnerCtrl = Ctrls.PartnerSuggest.init('#partnerId', '#partnerId_c', {
		emptyText: '全部'
	});
	*/
	Ctrls.rpcClient = common.rpcClient;
	var partnerId = $('#partnerId').val();
	var workshopCtrl = Ctrls.WorkshopAuto.init($('#workshopName'), {partnerId: partnerId == '' ? null : partnerId, resourceId: 'orderPurchase'});
	if(loginUserType == 1){
		var partnerCtrl = Ctrls.PartnerAutoSelect.init('#partnerId', {
			placeholder: '全部',
			params: {resourceId: 'orderPurchase', spResource: 'false'},
			onchange: function(value){
			workshopCtrl.refresh({partnerId: value});
			}
		});
	}else{
		$('#partnerCondWrapper').hide();
	}
	  
}

function initialize(menuId){
	common.caculateWidth("taskNewBigbox");
}


function renderOrderTable(){
	BUI.use([ 'bui/grid', 'bui/data' ], function(_grid, _data) {
		var Grid = _grid;
		var Store = _data.Store;
		var columns = [
		 {
            title: '序号', dataIndex: 'orderNo', width: '65px',sortable: false, renderer: function (value, obj, index) {

                return index + 1;
            }
         },
         {
			title : '订单编号',
			dataIndex : 'orderNo',
			width : '150px'
		},
         {
			title : '合伙人',
			dataIndex : 'orgName',
			width : '40%'
		},
		 {
			title : '门店名称',
			dataIndex : 'workshopName',
			width : '60%'
		},  {
			title : '订单状态',
			dataIndex : 'status',
			width : '120px',
			renderer : function (value,obj) {
	        	if(value == '0'){
	        		return '待确认';
	        	}else if(value == '1'){
	        		return '待出库';
	        	}else if(value == '5'){
	        		return '待收货';
	        	}else if(value == '7'){
	        		return '已收货';
	        	}else if(value == '6'){
	        		return '已取消';
	        	}else if(value == '9'){
	        		return '已转为采购订单';
	        	}
	        }
		}, {
			title : '创建人',
			dataIndex : 'creator',
			width : '100px',
			renderer : function (value,obj) {
				return obj.creatorDisplayName;
	        }
		}, {
			title : '创建时间',
			dataIndex : 'createTime',
			width : '90px',
			renderer : function (value,obj) {
				if(value){
					return formatTimeMillis(value);
				}
	            return "";
	        }
		}, /*{
			title : '订单金额(元)',
			dataIndex : 'totalOrderPrice',
			elCls: 'text-right',
			width : '110px',
			renderer : function (value,obj) {
				return format(value);
	        }
		},*/ {
			title : '操作',
			dataIndex : 'id',
			sortable: false,
			width : '200px',
			renderer : function (value,obj,index) {
				var returnStr = '<a href="javascript:void(0);" onclick="orderDetial(' + obj.id + ')">查看</a>';
				if(obj.status == '1'){
					returnStr = returnStr + '&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="printOutstock(' + obj.id + ')">打印出库单</a>';
					returnStr = returnStr + '&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" onclick="generateSellInPartnerOrder(' + obj.id + ')">转为采购订单</a>';
				}
	            return returnStr;
	        }
		} ];
		var partnerId = ""+$('#partnerId').val();
		var status = ""+$('#stateId').val();
		orderTableDataStore = new Store({
			url: '/orderServ/queryCGOrders.do',
			autoLoad:true,
			root: "resultLst",
            totalProperty: "totalRecord",
			remoteSort: true,
			sortField: 'createTime',
			sortDirection: 'DESC',
			params: {partnerId: partnerId,
					 status: status,
/*workshopName: $('#workshopName').val(),
					 dateFrom: $('#dateFrom').val(),
					 dateTo: $('#dateTo').val(),
					 orderType: $('#pageFlag').val(),
					 queryField: $('#gKeyWord').val(),*/
					 start: 0},
		    proxy : {
				method : 'post'
			},
			pageSize:10
		});
		{
			order_grid = new Grid.Grid({
				render : '#orderLists',
				columns : columns,
				store : orderTableDataStore,
				bbar : {
					pagingBar : true
				},
				plugins: [BUI.Grid.Plugins.CheckSelection,Grid.Plugins.AutoFit]
			});
		}
		order_grid.render();
		common.initPageSizeBar(order_grid);
	});
}
function format(input) {
    var n = parseFloat(input).toFixed(2);
    var re = /(\d{1,3})(?=(\d{3})+(?:\.))/g;
    return n.replace(re, "$1,");
}
function printOutstock(id){
	 window.open(common.ctx + "order/downloadOutstockPDF.do?orderId=" + id);
}

/*
	根据参数查询订单
*/
function queryOrdersByParam(){
/*var partnerId = ""+$('#partnerId').val();
var status = ""+$('#stateId').val();
	var params = {
		partnerId: partnerId,
		status: status,
		workshopName: $('#workshopName').val(),
		dateFrom: $('#dateFrom').val(),
		dateTo: $('#dateTo').val(),
		orderType: $('#pageFlag').val(),
		queryField: $('#gKeyWord').val(),
		start: 0
	};
	orderTableDataStore.load(params);*/
	var partnerId = ""+$('#partnerId').val();
	var status = ""+$('#stateId').val();
	if($('#queryPanel').hasClass('query-adv')){
		orderTableDataStore.load({
			partnerId: partnerId,
			orderStatus: status,
			workshopName: $('#workshopName').val(),
			dateFrom: $('#dateFrom').val(),
			dateTo: $('#dateTo').val(),
			orderType: $('#pageFlag').val(),
			queryField: null,
			queryType: 1,
			start: 0
		});
	}else{
		orderTableDataStore.load({
			partnerId: partnerId,
			orderStatus: status,
			workshopName: null,
			dateFrom: null,
			dateTo: null,
			orderType: null,
			queryField: $('#gKeyWord').val(),
			queryType: 2,
			start: 0
		});
	}
}

function formatTimeMillis(timeMillis){
	var date = new Date(timeMillis);
	var monthOfDay = formatCurDay(date.getMonth()+1);
	var dateOfDay = formatCurDay(date.getDate());
	var dateFormat = date.getFullYear()+"-"+monthOfDay+"-"+dateOfDay;
	return dateFormat;
}
function formatCurDay(formatPara){
	if(formatPara < 10){
		return "0"+formatPara;
	}else{
		return formatPara;
	}
}

function confirmBatch(){
	var ids = [], workshops = {}, multiWorkshop = false, workshop = null;
	for(var k in selectStatus){
		var item = selectStatus[k];
		if(item.checked){
			if(!workshops[item.workShopId]){
				if(ids.length > 0){
					multiWorkshop = true;
				}else{
					workshop = item.workshopName;
				}
				workshops[item.workShopId] = {id: item.workShopId, name: item.workshopName};
			}
			ids.push(k);
		}
	}
	if(multiWorkshop){
		var i = 0, msg = "您批量确认的订单包含";
		for(var k in workshops){
			if(i == 0){
				mes += workshops[k].name;
			}else{
				mes += (", " + workshops[k].name);
			}
			i++;
		}
		common.alertMes(msg + i + "个门店。批量确认只能选择一个门店的订单。请去掉其中" + (i - 1) + '个门店的订单。','error');
		return;
	}
	showDetailDialog(true, workshop, ids);
}

var detailDialog = null, detailNumbEditable = false, detailGrid = null, 
	detailGridCellEditing = null, detailGridStore = null, detailViewGrid = null;

function openDialog(items){
	//处理数据
	for(var i = 0; i < items.length; i++){
		items[i].defAmount = items[i].amount;
	}
	if(detailDialog == null){
		//初始化详情对话框
		BUI.use(['bui/overlay'],function(Overlay){
			//初始化对话框
			detailDialog = new Overlay.Dialog({
			    title:'',
			    width:750,
			    //配置DOM容器的编号
			    contentId:'detailDialog',
			    buttons: [
			              {
			            	  text:'确认',
			            	  elCls : 'button button-primary btn-ok',
			            	  handler : function(){
			            	  	if(detailGridCellEditing.get('curEditor') && !detailGridCellEditing.get('curEditor').accept()){
			            	  		return;
			            	  	}
		            	  		confirmOrders(confirmOrderIds, detailGridStore.get('data'), this);
			            	  }
			              },{
			            	  text:'取消',
			            	  elCls : 'button btn-cancel',
			            	  handler : function(){
			            	  	this.close();
			            	  }
			              },{
			            	  text:'关闭',
			            	  elCls : 'button btn-close',
			            	  handler : function(){
			            	  	this.close();
			            	  }
			              }]
			});
		});
	}
	detailDialog.show();
	
	//意向订单的查看
	$('#orderDetailGrid').hide();
	$('#orderDetailViewGrid').show();
	if(detailViewGrid == null){
		BUI.use(['bui/grid','bui/data'],function(Grid,Data){
			//初始化订单明细表
			detailViewGrid = new Grid.Grid({
				render : '#orderDetailViewGrid',
				columns : [{
					title : '序号',
					dataIndex :'',
					sortable: false,
					width:50,
					renderer : function(value,obj,index){
						return index + 1;
		        	}
				},{
					title : '商品编号',
					dataIndex :'sku',
					sortable: false,
					width:'20%'
				},
				{
					title : '商品名称',
					dataIndex :'productName',
					sortable: false,
					width:'45%'
				},{
					title : '数量',
					dataIndex :'amount',
					sortable: false,
					width:'10%'
				},{
					title : '单位',
					dataIndex :'units',
					sortable: false,
					summary:true,
					width:'10%'
				},{
					title : '总价(元)',
					dataIndex :'price',
					sortable: false,
					elCls: 'text-right',
					width:'15%',	
					summary:true/*,
					renderer : function(value,obj,index){
						if(value){
							return '￥' + (obj.amount * value);
						}
						return '';
		        	}*/
				}],
				items : items,
				height: 300
			});
			detailViewGrid.render();
			$('#orderDetailViewGrid .bui-grid-hd-empty').css('width', '17px');
		});
	}else{
		detailViewGrid.setItems(items);
	}
	if(detailNumbEditable){
		detailDialog.set('title', '确认订单');
		$('.btn-ok').show();
		$('.btn-cancel').show();
		$('.btn-close').hide();
	}else{
		detailDialog.set('title', '查看订单');
		$('.btn-ok').hide();
		$('.btn-cancel').hide();
		$('.btn-close').show();
	}
}
function confirmOrders(orderIds, orderLines, dialog){
	LoadMask.show();
	setTimeout(function(){
		rpcClient.call("orderService.confirmPotentialOrders",
				[orderIds, orderLines],
				function (result) {
					if (result.code == "success") {
						common.alertMes('订单确认成功','success');
						orderTableDataStore.load();
					} else {
						common.alertMes(result.codeMsg,'error');
					}
					LoadMask.hide();
					dialog.close();
				},function(error){
					common.ajaxTimeout(error);
				});
	}, 0);
}
function showDetailDialog(isConfirmOp, workshop, ids){
	detailNumbEditable = isConfirmOp;
	LoadMask.show();
	setTimeout(function(){
		rpcClient.call("orderService.queryOrderLinesByOrderId",
				[ids],
				function (result) {
					if (result.code == "success") {
						confirmOrderIds = ids;
						confirmOrderLines = 
						openDialog(result.resultlst);
					} else {
						common.alertMes(result.codeMsg,'error');
					}
					LoadMask.hide();
				},function(error){
					common.ajaxTimeout(error);
				});
	}, 0);
	$('#workshop').html(workshop);
}



//订单来源
var orderSourceCtrl = null;
function initOrderSource() {

    BUI.use('bui/select', function (Select) {
        var items = [
            { text: '全部', value: null },
            { text: '意向订单', value: 'P' },
            { text: '实际订单', value: 'A' },
            { text: 'SP下单', value: 'PA' }
        ],
            orderSourceCtrl = new Select.Select({
                render: '#orderSourceCONT',
                valueField: '#pageFlag',
                items: items,


            });
          //  alert(""+$("#pageFlag").val());
        orderSourceCtrl.render();
        orderSourceCtrl.on('change', function (e) {
           //queryOrdersByParam();
           
        });
    });

}

//跳转到SP下订单界面
function gotoSPOrderPage()
{
	window.location.href = "/business/order/createSpOrder1.jsp";
}



//合伙人
/*function initQueryCtrl(){
	Ctrls.rpcClient = common.rpcClient;
	var partnerId = $('#partnerId').val();
	var workshopCtrl = Ctrls.WorkshopAuto.init($('#workshopName'), {partnerId: partnerId == '-999' ? null : partnerId});
	if(loginUserType == 1){
		$('#partner_cond_area').show();
		var partnerCtrl = Ctrls.Partner.init('#partnerId', '#partnerIdC', {
		    param: null,
			events: {
				change: function(ev){
					if(ev.value != workshopCtrl.partnerId){
						workshopCtrl.refresh({partnerId: ev.value == '-999' ? null : ev.value});
						//queryOrdersByParam();
					}
				}
			}
		});
	}
}*/

//获取状态栏
function initState() {
    BUI.use('bui/select', function (Select) {
        var items = [
        	{ text: '全部', value: '-1' },
        	{ text: '待确认', value: '0' },
        	{ text: '待出库', value: '1' },
        	/*{ text: '待下单', value: '2' },
        	{ text: '已下单', value: '3' },*/
            /*{ text: '待发货', value: '4' },*/
        	{ text: '待收货', value: '5' },
        	{ text: '已收货', value: '7' },
            { text: '已取消', value: '6' },
            { text: '已转为采购订单', value: '9' }
        ],
            state = new Select.Select({
                render: '#stateIdC',
                valueField: '#stateId',
                items: items,


            });
        state.render();
        state.on('change', function (e) {
            //refre
          //queryOrdersByParam();
           
        });
    });
}

// 查看订单详情
var updateDialog = null;
var isCloseFresh = false;
function orderDetial(id) {
	LoadMask.show();
    $('#orderDetail_grid').empty();
    rpcClient.call('orderService.getOrdersAndOrderLinesByOrderID', [""+id],
        function (result) {
            if (result.code == "success") {
                var order = result.orderVo;
                var orderNoTemp = order.orderNo;
            	$('#orderdetail #orderNo').text(orderNoTemp);
    			$('#orderdetail #partnerName').text(order.orgName);
				$('#orderdetail #workshopName').text(order.orderWorkshopName);
				var status = order.status;
				
				if(status == '0'){
					order.statusMeaning =  '待确认';
	        	}else if(status == '1'){
	        		order.statusMeaning =  '待出库';
	        	}else if(status == '5'){
	        		order.statusMeaning =  '待收货';
	        	}else if(status == '7'){
	        		order.statusMeaning =  '已收货';
	        	}else if(status == '6'){
	        		order.statusMeaning =  '已取消';
	        	}else if(status == '9'){
	        		order.statusMeaning =  '已转为采购订单';
	        	}
    			$('#orderdetail #orderStatus').text(order.statusMeaning);
    			$('#orderdetail #createTime').text(formatTimeMillis(order.createTime));
    			$('#orderdetail #creator').text(order.creatorDisplayName);
    			$('#orderdetail #address').text(order.address);
    			$('#orderdetail #receiveUserName').text(order.receiveUserName);
    			$('#orderdetail #receivePhoneNo').text(order.receivePhoneNo);
    			$('#orderdetail #stockOutNo').text(order.stockOutNo);
    			$('#orderdetail #stockInNo').text(order.stockInNo);
    			$('#orderdetail #remark').text(order.remark);
    			/*$('#orderdetail #totalPriceID').text("￥"+order.totalOrderPrice);*/
    			var order_type = order.orderType;
    			if(order_type=='P')//意向订单
    			{
    				$('#orderdetail #confirmBtn').show();
    			}else//其他订单
    			{
    				$('#orderdetail #confirmBtn').hide();
    			}
                var order_status = order.status;
                dialogDetail(result.lstOrderLinevo,order_status,order.id);
                if (updateDialog == null) {
                	var dialogWidth = $(window).width() * 0.8;
                    var dialogHeight = $(window).height() * 0.8;
                    if(dialogWidth < 768){
                    	dialogWidth = 768;
                    }
                    if(dialogHeight < 570){
                    	dialogHeight = 570;
                    }
                    BUI.use('bui/overlay', function (Overlay) {
                        updateDialog = new Overlay.Dialog({
                            title: "查看订单详情",
                            width: dialogWidth,
                            height: dialogHeight,
                            //配置DOM容器的编号
                            contentId: 'updateContent',
                            buttons: [

                                {
                                    text: '关闭',
                                    elCls: 'button btn-close',
                                    handler: function () {
                                        this.close();
                                        if(order_type!='P' && isCloseFresh=='true')//当订单状态发生变化的时候，需要刷新
                                        {
                                          queryOrdersByParam();
                                        }
                                    }
                                }]
                        });
                    });
                }
                updateDialog.show();
            } else {
                common.alertMes('拉取数据失败', 'error');
            }
            LoadMask.hide();
       })


}

//显示订单详情
var gridDetail = null;
var orderIdForOper = null;
var orderLineData = null;
function dialogDetail(result,orderStatus,orderId) {
    orderLineData = result;
    var orderstatus = orderStatus;
    if(orderStatus != 0){
    	$('#cancelBtn').hide();
    }
    var orderid = orderId;
    orderIdForOper = orderId;
    BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
    var columns = [{
						title : '序号',
						dataIndex :'',
						sortable: false,
						width:'50px',
						renderer : function(value,obj,index){
							return index + 1;
			        	}
					},{
						title : '商品编号',
						dataIndex :'sku',
						sortable: false,
						width:'20%'
					},{
						title : '商品名称',
						dataIndex :'productName',
						sortable: false,
						width:'35%',
						renderer: function(value,item,index){
							var text = value;
				        	if(item.productPhoto){
				        		text = '<div class="photo-icon"><img src="' + common.ctx + 'downloadAttachmentFile.do?sourceType=5&attId=' + 
				        				item.productPhoto + '"/></div><span>' + value + '</span>';
				        	}
				        	return text;

						}
					},{
						title : '数量',
						dataIndex :'amount',
						sortable: false,
						width:'100px',
						renderer : function(value,obj,index){
							if(orderstatus=='0')
							{
								return '<input id="'+obj.id+'" onfocus="this.select();"  min="1" max="10000" style="width:60px;height:25px;font-size:18px;padding-right:15px;" ' + 
								'value="'+value+'" class="text-right quantity-input" onchange="setProductBuyCount(\''+obj+'\',\''+obj.id+'\')"></input>';
								             
							}
	          				return value;
			        	}
					},/*{
						title : '单价(元)',
						dataIndex :'price',
						sortable: false,
						elCls: 'text-right',
						width:'15%',
						renderer : function (value,obj) {
							return format(value);
				        }
					},{
						title : '总价(元)',
						dataIndex :'totalPrice',
						sortable: false,
						elCls: 'text-right',
						width:'15%',
						summary:true,
						renderer : function (value,obj) {
							return format(value);
				        }
					},*/{
						title : '操作',
						dataIndex :'status',
						sortable: false,
						width:'50px',
						renderer : function(value,obj,index){
							var returnStr = '';
							if(orderstatus=='0')
							{
								if(value=='1')
								{
									returnStr =  '  <a href="javascript:void(0);" onclick="orderConfirmOrCancle(false,' + orderid + ','+obj.id+',false)">取消</a>';
								}else if(value='0')
								{
									returnStr =  '<a href="javascript:void(0);" onclick="orderConfirmOrCancle(true,' + orderid + ','+obj.id+',false)">确认</a>'
								}
								             
							}/*else // if(orderstatus=='1')
							{
								returnStr =  '<a href="javascript:void(0);" onclick="orderConfirmOrCancle(false,' + orderid + ','+obj.id+',false)">取消</a>';
							}*/
	          				return returnStr;
			        	}
					}];
        var store = new Data.Store({
            data: orderLineData,
            autoLoad: true,
            pageSize: 10
        });
        var editing = new Grid.Plugins.CellEditing();
        var summary = new Grid.Plugins.Summary({
        	pageSummaryTitle:'订单总额'
        })
        gridDetail = new Grid.Grid({
            render: '#orderDetail_grid',
            /*width: '738',*/
            /*height: '230',*/
            columns: columns,
            //idField: 'orderDetail_grid',
            store: store,
 			plugins: [BUI.Grid.Plugins.CheckSelection, editing,summary], // 插件形式引入多选表格
            bbar: {
                pagingBar: true
            }
        });
        gridDetail.render();
        common.initPageSizeBar(gridDetail);
        $(".bui-grid-summary-row").addClass("text-right");
    });

}
function setProductBuyCount(obj,value){
	for(var i = 0; i < orderLineData.length; i++){
		var tempProduct  = orderLineData[i];
		if(tempProduct.id == value){
			var countInput = $("#"+value+"").val();
			if(!common.checkInt(countInput)){
				common.alertMes("购买数量只能是大于0的整数！", 'error');
				$("#"+value+"").val("");
				return;
			}
			tempProduct.amount = $("#"+value+"").val();
			break;
		}
	}
}
//批量确认或取消
function operBatchOrderLine(isConfirmOp)
{
   orderConfirmOrCancle(isConfirmOp,orderIdForOper,null,true)
}


//确认或取消
function orderConfirmOrCancle(isConfirmOp, orderId,orderLineId,isOperMoreObj)
{
	//获取选中的订单行信息,,针对批量
	var selectionsOrderLine = [];
	if(isOperMoreObj) //多个
	{
		var items = gridDetail.getItemsByStatus("selected");
		if(items.length == 0){
			common.alertMes("请先选择要操作的产品", 'error');
			return;
		}
		for(var i = 0; i < items.length; i++)
		{
			selectionsOrderLine.push(items[i]);
		}
	}else //单个
	{
		var orderLine = {};
		orderLine.id = orderLineId;
		orderLine.amount = $("#"+orderLineId+"").val();
		selectionsOrderLine.push(orderLine);
	}
	
    
    var operType = "-1";
	if(isConfirmOp)
	{
		operType = "1";
	}else
	{
	    operType = "0";
	}
	
	rpcClient.call('orderService.modifyOrderAndOrderLineStatusByOrderId', [""+orderId,selectionsOrderLine,operType],
        function (result) {
            if (result.code == "success") {
                common.alertMes("操作成功！", "info");
                var gotoOrderMainPage = result.isgotoOrderMainPage;
                isCloseFresh = result.isgotoOrderMainPage
                if(gotoOrderMainPage == "true")
                {
                	queryOrdersByParam();
                	updateDialog.close();
                }else
                {
                	 orderDetial(""+orderId);
                }
               
            } else {
                common.alertMes('操作失败', 'error');
            }
     });
}
function generatePartnerOrder(){
	var orderSelectedArray = order_grid.getSelection( ) ;
	if(orderSelectedArray.length == 0){
		common.alertMes("请先选择要合并的订单", 'error');
		return;
	}
	var orderIdArray = [];
	for(var i = 0 ; i < orderSelectedArray.length ; i ++){
		var tempOrder = orderSelectedArray[i];
		if(tempOrder.status != '1'){
			common.alertMes("需合并的订单状态只能为待出库！", 'error');
			return;
		}
		orderIdArray.push(tempOrder.id);
	}
	LoadMask.show();
	rpcClient.call('partnerOrderService.generatePartnerOrderByIds', [orderIdArray],
	        function (result) {
	            if (result.code == "success") {
	                common.alertMes("合并成功！", "info");
	                var gotoOrderMainPage = result.isgotoOrderMainPage;
	                isCloseFresh = result.isgotoOrderMainPage
	                queryOrdersByParam();
	               
	            } else {
	            	common.alertMes(result.errorMsg, 'error');	                
	            }
	            LoadMask.hide();
	     });
}
function generateSellInPartnerOrder(orderId){
	var orderIdArray = [];
	orderIdArray.push(orderId);
	LoadMask.show();
	rpcClient.call('partnerOrderService.generatePartnerOrderByIds', [orderIdArray],
	        function (result) {
	            if (result.code == "success") {
	                common.alertMes("转化成功！", "info");
	                var gotoOrderMainPage = result.isgotoOrderMainPage;
	                isCloseFresh = result.isgotoOrderMainPage
	                queryOrdersByParam();
	               
	            } else {
	            	common.alertMes('转化失败', 'error');
	            }
	            LoadMask.hide();
	     });
}