; 'use strict';

var rpc = new $.JsonRpcClient({
	ajaxUrl : '/wxPublicRpc.do'
});

$(function() {
	material_application_detail_widget.init();
	basicHover();
	//判断是否显示
    if(hideBack){
        $('#closeDetailBtn').hide();
    }
});

let expressFadeInTimeout = null;
let expressFadeOutTimeout = null;
let tableLength = 0;

var material_application_detail_widget = (function($) {
	'use strict';

	var req = {
			getApplicationHeadInfo : function(applicationId, callback) {
				rpc.call("ApplicationService.getApplicationHeadInfo", [ applicationId ], function(result) {
					if (result.success) {
						callback(result.application);
					} else {
						common.alertMes(result.errorMsg, 'error');
					}
				}, function(error) {
					common.ajaxTimeout(error);
				});
			},
			getApplicationLineInfo : function(applicationId, callback) {
				rpc.call("ApplicationService.getApplicationLineInfo", [ applicationId ], function(result) {
					if (result.success) {
						callback(result.applicationLines);
					} else {
						common.alertMes(result.errorMsg, 'error');
					}
				}, function(error) {
					common.ajaxTimeout(error);
				});
			},
			getApplicationStatusHistory : function(applicationId, callback) {
				rpc.call("ApplicationService.getApplicationStatusHistory", [ applicationId ], function(result) {
					if (result.success) {
						callback(result.historyList);
					} else {
						common.alertMes(result.errorMsg, 'error');
					}
				}, function(error) {
					common.ajaxTimeout(error);
				});
			},
			receive : function (applicationId, lastUpdateTime, callback, errFunc) {
					rpc.call("ApplicationService.receive", [ applicationId, lastUpdateTime ], function(result) {
						if (result.success) {
							common.alertMes(result.successMsg, 'info');
							$.isFunction(callback) && callback(result.data);
						} else {
							common.alertMes(result.errorMsg,'error');
							$.isFunction(errFunc) && errFunc(result.data);
						}
					}, function(error) {
						common.ajaxTimeout(error);
					});
			},
			getApplicationLogistics : function(applicationId, callback) {
				rpc.call("ApplicationService.getApplicationLogistics", [ applicationId ], function(result) {
					if (result.success) {
						callback(result.logisticsList);
					} else {
						common.alertMes(result.errorMsg, 'error');
					}
				}, function(error) {
					common.ajaxTimeout(error);
				});
			},
			kuaidiQuery : function(expressNumber, contactNumber, callback){
				rpc.call("commonService.kuaidiQuery", [expressNumber, contactNumber ], function(result) {
					if (result.code == 'success') {
						callback(result.resultLst);
					} else {
						common.alertMes(result.errorMsg, 'error');
					}
				}, function(error) {
					common.ajaxTimeout(error);
				});
			},
		};

	var loader = {
		initGrid: function() {
			BUI.use(['bui/grid','bui/data'],function(Grid,Data){

				var columns = [{
					title: '序号',
					width: 50,
					renderer: function(value, obj, index) {
						return index + 1;
					},
					sortable: false
				},
				{
					title: '物料',
					width: '100%',
					dataIndex: 'materialName',
					elStyle : {'min-width':'200px'},
					renderer: function(v, item) {
						var imgDomStr = material_common.getMaterialImagesDom(item.materialId,item.materialImages, item.materialName, item.materialDesc);
						var des = '<div class="grid-cell-block"><div class="grid-cell-line">' + v + '</div></div>';
						var tag = '<div style="color:#666;">'+item.materialTypeName+'</div>';
						var skuCode = item.materialSkuCode;
						var sku = '<small style="color:#999;">编码：'+ item.materialCode + '， 颜色：' +skuCode.split('-')[1] + '， 尺寸： ' + skuCode.split('-')[2] + '</small>';
						return imgDomStr + des + tag + sku;
					}
				},
				{
					title: '编码',
					dataIndex: 'materialCode',
					visible:false,
					width: '15%',
					renderer: function(v, item) {
						var skuCode = item.materialSkuCode;
						if (skuCode) {
							return item.materialCode + '（' +skuCode.split('-')[1] + ', ' + skuCode.split('-')[2] + '）';
						}
						return item.materialCode;
					}
				},
				{
					title: '颜色和尺寸',
					dataIndex: 'materialSkuCode',
					visible:false,
					width: '15%',
					renderer: function(v, item) {
						if (v) {
							return v.split('-')[1] + ', ' + v.split('-')[2];
						}
						return '';
					}
				},
				{
					title: '类别',
					dataIndex: 'materialTypeName',
					visible:false,
					width: '10%'
				},
				{
					title: '描述',
					dataIndex: 'materialDesc',
					visible:false,
					width: 150
				},
				{
					title: pointTitle + '数',
					dataIndex: 'materialPrice',
					width: 100,
					visible: !!pointMode,
					renderer: function(v, item) {
						return '<span style="color:#ed5c5f;font-weight: 600;">' + v + '</span>';
					}
				},
				{
					title: '数量',
					dataIndex: 'applicationQty',
					width: 100,
					summary: true,
					sortable: false,
					renderer: function(v, item) {
						if (!item.materialId){
							// 汇总统计的场合，直接返回数字
							return '<span class="application-qty">' + v + '</span>';
						}
						if (!v) {
							v = 0;
						}
						return '×<span class="application-qty">' + v + '</span>' + item.materialUnit;
					}
				},
				{
					title: '总'+ pointTitle +'数',
					dataIndex: 'materialPriceTotal',
					summary: true,
					width: 100,
					visible: !!pointMode,
					renderer: function(v, item) {
						return '<span style="color:#ed5c5f;font-weight: 600;">' + common.formatNumber(v,2) + '</span>';
					}
				}
				];
				var data =[],
				store = new Data.Store({
					data: data,
					autoLoad: true,
					pageSize: 100
				}),
				grid = new Grid.Grid({
					render:'#applicationDetailGrid',
					columns : columns,
					store: store,
					bbar:{
						pagingBar:true
					},
					plugins : [Grid.Plugins.Summary] 
				}).render();
				store.on('beforeprocessload',function(e){
					// reload selection data, restore from cache
					var arr = e.data.rows;
					$.each(arr, function(i, item) {
						item.materialPriceTotal =  parseFloat(item.materialPrice)* parseInt(item.applicationQty);
					});

				});
				common.initGrid(grid, null, true);

				// 异步加载表格数据
				req.getApplicationLineInfo(applicationId, function(data) {
					store.setResult(data);
				});
			});
			return this;
		},
		initHistoryGrid: function() {
			BUI.use(['bui/grid','bui/data'],function(Grid,Data){

				var columns = [ {
					title : '序号',
					width : '5%',
					renderer : function(value, obj, index) {
						return index + 1;
					},
					sortable : false
				}, {
					title : '步骤',
					width : '20%',
					dataIndex : 'step'
				}, {
					title : '处理意见',
					dataIndex : 'coments',
					width : '40%'
				}, {
					title : '处理人',
					dataIndex : 'creationPersonName',
					width : '15%'
				}, {
					title : '处理时间',
					dataIndex : 'creationTime',
					width : '20%',
					renderer : function(v) {
						return v ? common.formatDate(new Date(v),
								'yyyy-MM-dd hh:mm') : '';
					}
				} ];
				var data =[],
				store = new Data.Store({
					data: data,
					autoLoad: true,
					pageSize: 10
				}),
				grid = new Grid.Grid({
					render:'#applicationHistoryGrid',
					columns : columns,
					store: store,
					bbar:{
						pagingBar:true
					}
				}).render();
				common.initGrid(grid, null, true);

				// 异步加载表格数据
				req.getApplicationStatusHistory(applicationId, function(data) {
					store.setResult(data);
				});
			});
			return this;
		},
		initLogisticsGrid: function(applicationCode) {
			BUI.use(['bui/grid','bui/data'],function(Grid,Data){
				var columns = [ {
					title : '序号',
					width : '5%',
					renderer : function(value, obj, index) {
						return index + 1;
					},
					sortable : false
				}, {
					title : '物流详情',
					width : '45%',
					dataIndex : 'expressContext'
				}, {
					title : '快递公司',
					dataIndex : 'expressCompany',
					width : '15%'
				}, {
					title : '快递单号',
					dataIndex : 'expressNumber',
					width : '15%',
					renderer: function(value, obj, index) {
//						if(index == tableLength - 1){//渲染最后一行的时候添加事件
//							addHover();
//						}
						return '<span class="express-number-follow-span" tableIndex="' + index + '">' + value + '</span>';
					}
				}, {
					title : '跟踪时间',
					dataIndex : 'trackingTime',
					width : '20%',
					renderer : function(v) {
						return v ? common.formatDate(new Date(v),
								'yyyy-MM-dd hh:mm') : '';
					}
				} ];
				var data =[],
				store = new Data.Store({
					data: data,
					autoLoad: true,
					pageSize: 10
				}),
				grid = new Grid.Grid({
					render:'#applicationLogisticsGrid',
					columns : columns,
					store: store,
					bbar:{
						pagingBar:true
					}
				}).render();
				common.initGrid(grid, null, true);

				// 异步加载表格数据
				req.getApplicationLogistics(applicationCode, function(data) {
					tableLength = data.length
					store.setResult(data);
					addHover();
				});
			});
			return this;
		},
		initDetailPanel :function() {
			req.getApplicationHeadInfo(applicationId, function(data) {
//				$('.header-panel #applicationName').html(data.applicationName);
				$('.application-info-panel #applicationCode').html(data.applicationCode);
				var editBtn = $('.header-btns #editBtn'), receiveBtn = $('.header-btns #receiveBtn');
				var app_status_dom='';
				switch(data.applicationStatus) {
				case 'DRAFT' :
					app_status_dom = '<span  class="badge">暂存</span >';
                    editBtn.click(function () {
                        material_application_detail_widget.edit(data.businessTypeCode);
                    });
					editBtn.show();
					break;
				case 'READY' :
					app_status_dom = '<span  class="badge badge-info">订单待确认</span>';
					break;
				case 'PENDING_4_URGENT' :
					app_status_dom = '<span  class="badge badge-info">加急审批中</span>';
					break;
				case 'POINT_SETTLED' :
					app_status_dom = '<span  class="badge badge-info">订单已结算</span>';
					break;
				case 'PENDING_ON_L1' :
					app_status_dom = '<span  class="badge badge-inverse">订单待确认</span>';
					break;
				case 'PENDING_ON_L2' :
					app_status_dom = '<span  class="badge badge-inverse">订单待审批</span>';
					break;
				case 'APPROVED' :
					app_status_dom = '<span  class="badge badge-success">待发货</span>';
					break;
				case 'REJECTED' :
					app_status_dom = '<span  class="badge badge-error">已驳回</span>';
                    editBtn.click(function () {
                        material_application_detail_widget.edit(data.businessTypeCode);
                    });
                    editBtn.show();
					break;
				case 'PENDING_ON_OUTBOUND' :
					app_status_dom = '<span  class="badge">待出库</span>';
					break;
				case 'OUTBOUND' :
					app_status_dom = '<span  class="badge">已发货</span>';
					break;
				case 'SHIPPING' :
					app_status_dom = '<span  class="badge">运送中</span>';
					receiveBtn.show();
					receiveBtn.click(function() {
						common.confirmMes("你确定要确认收货吗?", function() {
							material_application_detail_widget.receive(applicationId, data.lastUpdateTime);
						}, 'question');
					});
					loader.initLogisticsGrid(data.applicationCode);
					break;
				case 'RECEIVED' :
					app_status_dom = '<span  class="badge">已收货</span>';
					loader.initLogisticsGrid(data.applicationCode);
					break;
				case 'RETURNED' :
					app_status_dom = '<span  class="badge">已退回</span>';
					break;
				};

				$('.application-info-panel #applicationStatus').html(app_status_dom);
				$('.application-info-panel #applicationPersonName').html(data.applicationPersonName);
				$('.application-info-panel #applicationTime').html(data.applicationTime ? common.formatDate(new Date(data.applicationTime),'yyyy-MM-dd hh:mm') : '');
				var adress_dom;
				if (data.shipToWorkshop) {
					adress_dom = data.addressDetail + ' <span class="label label-Inverse">门店:'+data.workshopName+'</span>';
				} else {
					adress_dom = data.addressRegionName + ' ' + data.addressDetail;
				}
				$('.application-info-panel #addressDetail').html(adress_dom);
				$('.application-info-panel #contacts').html(data.contacts);
				$('.application-info-panel #contactNumber').html(data.contactNumber);
				$('.application-info-panel #applicationOrgName').html(data.applicationOrgName);
				$('.application-info-panel #comments').html(data.comments);
				$('.application-info-panel #recipientDate').html(common.formatDate(new Date(data.recipientDate),'yyyy-MM-dd'));
			});
			return this;
		},
		initTabs: function() {
			BUI.use(['bui/tab','bui/mask'],function(Tab){
				var tab = new Tab.TabPanel({
					render : '#tabs',
					elCls : 'nav-tabs',
					panelContainer : '#tab_content',//如果内部有容器，那么会跟标签项一一对应，如果没有会自动生成
					autoRender: true,
					children:[
						{title:'订单明细',value:'1'},
						{title:'订单动态',value:'2'},
						{title:'物流信息',value:'3'},
					]
				});
				tab.setSelected(tab.getItemAt(0));
				tab.on('selectedchange', function(ev){
					var item = ev.item,_value = item.get('value'),_text = item.get('text');
					$(window).trigger('resize');
				});
			});
			return this;
		}
	};
	
	//绑定悬浮框事件
	function addHover(){
		setTimeout(function(){
			$(".express-number-follow-span").hover(function(){
                let dom = $(this)
				expressFadeInTimeout = setTimeout(function() {
					let expressNumber = dom.text()
					let contactNumber = $('.application-info-panel #contactNumber').html()
					req.kuaidiQuery(expressNumber, contactNumber,function(data){
						$("#hover-message").empty()
						if(data){
							for(let i = data.length - 1; i >= 0; i--){
								let v = data[i]
								let style = i == 0  ?'current-item-color':''
								$("#hover-message").prepend('<div class="express-item ' +  style  + '"><div class="express-item-content">' + 
										v.context.replace(/【/g,'[').replace(/】/g,'] ') + '</div>' + '<div class="express-item-time">' + v.time + '</div></div>');
							}
						}
						
						let offIndex = dom.attr('tableIndex')
						let offTop = -170 + parseInt(offIndex) * 30 + 'px'
						$("#hover-message").css("top", offTop)
						$("#hover-message").fadeIn(500,function(){})
					});
				}, 400)
			},function(){
				expressFadeOutTimeout = setTimeout(function(){
					clearExpressInfo()
				}, 500)
			})
		}, 200)
	};

	return {
		init: function() {
			loader.initDetailPanel().initTabs().initGrid().initHistoryGrid();
		},
		edit: function (bizType) {
            var toUrl = "/material/jsp/applicationCreate.jsp?applicationId="+applicationId+"&pointMode=" + !!pointMode +"&pointType=" +  pointType;
            if(bizType){
                toUrl += "&bizType="+bizType;
            }
			window.location.href = toUrl;
		},
		receive :  function (applicationId, lastUpdateTime) {
			req.receive(applicationId, lastUpdateTime, function(data){
				window.location.reload();
			},function(){
				window.location.reload();
			});
		}
	};
}($));

function clearExpressInfo(){
	clearTimeout(expressFadeInTimeout)
	$("#hover-message").fadeOut(100,function(){})
	$("#hover-message").empty()
}

function basicHover(){
	$('#hover-message').hover(function(){
		clearTimeout(expressFadeOutTimeout)
	}, function(){
		$("#hover-message").hide()
		$("#hover-message").empty()
	})
}